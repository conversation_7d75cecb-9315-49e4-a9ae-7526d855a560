# Payment Reconciliation Testing Guide

## Prerequisites

1. **Keystone API** must be running with Stripe integration
2. **Station Admin API** must be accessible
3. **Next.js admin app** must be running
4. **Valid Stripe keys** configured in environment

## Testing Steps

### 1. Start the Development Server

```bash
cd next-admin
npm run dev
```

The app will be available at `http://localhost:3000`

### 2. Navigate to Payment Reconciliation

1. Go to the Stripe Reconciliation Dashboard
2. Click on the **"Payment Reconciliation"** tab
3. You should see the date range selector and "Run Reconciliation" button

### 3. Test with Recent Data

1. **Set date range** to last 7-30 days (or a range where you know there are Stripe payments)
2. **Click "Run Reconciliation"**
3. **Observe the results:**
   - Summary cards should show counts
   - Any unmatched payments will be listed below
   - Each unmatched payment shows: ID, amount, date, method, description

### 4. Expected Behavior

**If all payments match:**
- Green success message: "All Payments Matched!"
- Summary shows equal counts

**If there are unmatched payments:**
- Red alert cards showing unmatched Stripe payments
- Each card includes:
  - Stripe charge ID
  - Amount and currency
  - Payment date and method
  - Description
  - Link to Stripe receipt

### 5. Verify Data Accuracy

**Check Stripe Dashboard:**
1. Go to Stripe Dashboard → Payments
2. Filter by the same date range
3. Compare counts with the reconciliation results

**Check Station Admin:**
1. Look at the payments table in Station Admin
2. Verify that unmatched payments are indeed missing

## Common Test Scenarios

### Scenario 1: New Installation
- **Expected**: Many unmatched payments (if webhook integration is new)
- **Action**: Review and potentially backfill missing payments

### Scenario 2: Webhook Issues
- **Expected**: Recent payments missing from Station Admin
- **Action**: Check webhook configuration and logs

### Scenario 3: Manual Payments
- **Expected**: Station Admin payments without Stripe records
- **Action**: These won't show as "unmatched" since we're looking for Stripe→SA direction

## Troubleshooting

### No Data Showing
1. Check Stripe API keys in environment
2. Verify Keystone GraphQL endpoint is accessible
3. Check browser console for errors
4. Verify date range has actual payments

### Authentication Errors
1. Ensure you're logged into the admin app
2. Check Station Admin API authentication
3. Verify API endpoints are accessible

### GraphQL Errors
1. Check Keystone server logs
2. Verify Stripe service initialization
3. Test GraphQL queries in Keystone admin UI

## API Endpoints Used

- **GraphQL**: `stripePaymentReconciliation` query
- **REST**: `/api/station-admin-payments?startDate=X&endDate=Y`
- **Station Admin**: `/payments?start=X&end=Y&status=succeeded`

## Data Flow

```
1. User selects date range
2. Frontend calls GraphQL for Stripe payments
3. Frontend calls REST API for Station Admin payments  
4. Frontend compares payment_id fields
5. Frontend displays unmatched Stripe payments
```

## Next Steps

After identifying unmatched payments, you can:

1. **Investigate webhook issues** if recent payments are missing
2. **Backfill missing payments** using Station Admin tools
3. **Review payment processing** for any systematic issues
4. **Set up monitoring** to catch future discrepancies quickly
