# 🎯 Simple E2E Testing Setup for StationAdmin

## **Quick Start Guide**

### **1. Prerequisites**
- ✅ ngrok tunnel running: `https://pet-leopard-fully.ngrok-free.app`
- ✅ StationAdmin running on `localhost:3000`
- ✅ CallCenter account access

### **2. Admin Credentials Setup**
Add your admin credentials to `.env`:
```bash
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=your_password
```

### **3. How Tests Work**
1. **Automatic Authentication** - Tests use your admin credentials for Google OAuth
2. **Navigate to Donors Page** - Goes to `/#/donors` where "Create New Donation" button is located
3. **Form Access** - Clicks button to access donation form
4. **Test Mode** - Forces staging API and test Stripe keys for safety

**✅ ADVANTAGE**: Fully automated - no manual login required!

### **3. Run Tests**
```bash
# Run the simplified E2E tests
npm run test:e2e

# Or run specific tests
npm run test:e2e:donations
```

## **What the Tests Do**

### **Test 1: Admin Authentication & Donation**
- ✅ Clears all cookies/storage for fresh session
- ✅ Performs Google OAuth with admin credentials
- ✅ Navigates to `/#/donors` page
- ✅ Finds and clicks "Create New Donation" button
- ✅ Forces test mode (staging API + test Stripe keys)
- ✅ Fills complete donation form ($50)
- ✅ Uses test credit card `****************`
- ✅ Submits donation to staging database
- ✅ Takes screenshots at each step

### **Test 2: Legacy Authentication Check**
- ✅ Checks existing session authentication
- ✅ Attempts sign out and re-authentication
- ✅ Provides fallback guidance for manual setup

### **Test 3: One-Time & Monthly Tests**
- ✅ Simplified tests that run after authentication
- ✅ Skip gracefully if authentication fails
- ✅ Focus on form filling and submission

## **Test Mode Verification**

The tests automatically ensure:
- 🧪 **Staging API**: `https://api.staging.kpfa.org`
- 🧪 **Test Stripe Keys**: `pk_test_rnm58ULbGeHkmqKf3ghNkt1O`
- 🧪 **Safe Testing**: No production data affected

## **Expected Output**

### **If Not Authenticated (or wrong user type):**
```
🔍 Checking StationAdmin access via ngrok
📄 Page title: StationAdmin
🔐 Authentication status: { hasForm: false, hasLoginRedirect: false }
❌ Cannot access CreateDonation form
🔍 Available buttons: [ '', '' ]
🔍 Available links: [ 'Dashboard', 'Campaigns', 'KPFA Site', 'Sign Out', '', 'Home', 'KPFA' ]
⏭️ Skipping test - authentication required
💡 Please login manually with CallCenter account, then run tests
```

### **If Authenticated (CallCenter user):**
```
🔍 Checking StationAdmin access via ngrok
📄 Page title: StationAdmin
🎯 Found "Create New Donation" button - clicking...
🔐 Authentication status: { hasForm: true, hasLoginRedirect: false, clickedCreateButton: true }
✅ Successfully accessed CreateDonation form!
✅ Found and clicked "Create New Donation" button
🧪 Test mode status: { value: true, route: 'api.staging.' }
✅ Form accessible - proceeding with donation test
✅ Form filled with test data
💳 Submitting donation...
💰 One-time donation test completed
```

## **Verifying Results**

After successful tests, check staging database for:
- **New donation records** with amounts $50 and $25
- **New donor records** with names "Test Donor" and "Monthly Subscriber"
- **Stripe test payments** in your Stripe test dashboard

## **Troubleshooting**

### **"Authentication required" errors**
- Login manually at the ngrok URL
- Verify you can access `/donors/create-donation`
- Keep browser session active

### **"Form elements not found" errors**
- Check screenshots in `test-results/` directory
- Verify ngrok tunnel is pointing to correct port
- Ensure StationAdmin is running on localhost:3000

### **"Test mode unknown" errors**
- This is expected - test mode is forced via Redux dispatch
- Check console logs for "Test mode forced" messages

## **Files Created**

Tests create screenshots in `test-results/`:
- `homepage-check.png` - StationAdmin homepage
- `donation-form-ready.png` - CreateDonation form
- `before-submit.png` - Form filled with test data
- `after-submit.png` - Result after submission
- `monthly-before-submit.png` - Monthly form
- `monthly-after-submit.png` - Monthly result

## **Next Steps**

1. **Run the tests** after manual authentication
2. **Check staging database** for new donations
3. **Verify Stripe test dashboard** for payments
4. **Iterate on form selectors** if needed

**The tests are now simple, reliable, and focused on core Stripe payment testing!** 🎉
