#KPFA Admin
Based on CoreUI Free React Admin Template v2

## Table of Contents

- [Dev Notes](#dev-setup)
- [Installation](#installation)
- [Usage](#basic-usage)
- [Testing](#testing)
- [What's included](#whats-included)

## Dev Notes

In order for queries to the [API] to run, you'll need a JWT token. This can be obtained by logging into the app. Tokens expire regularly, so you will need to log in again to renew authentication.

Current supported Node version:
16.11.1

## Installation

```bash
# clone the repo
$ git clone https://github.com/coreui/coreui-free-react-admin-template.git my-project

# go into app's directory
$ cd my-project

# install app's dependencies
$ npm install
```

## Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app)

see also:
[User Guide](CRA.md)

### Basic usage

```bash
# dev server  with hot reload at http://localhost:3000
$ npm start
```

Navigate to [http://localhost:3000](http://localhost:3000). The app will automatically reload if you change any of the source files.

### Build

Run `build` to build the project. The build artifacts will be stored in the `build/` directory.

```bash
# build for production with minification
$ npm run build
```

## Testing

The project uses Jest and React Testing Library for testing. Tests are located in `__tests__` directories next to the components they test.

### Test Setup

The testing environment includes:
- Jest as the test runner
- React Testing Library for component testing
- Redux Mock Store for testing components with Redux dependencies

### Running Tests

```bash
# Run all tests
$ npm test

# Run tests for a specific file or pattern
$ npm test -- --testPathPattern=ComponentName

# Run tests in watch mode (default)
$ npm test

# Run tests with coverage
$ npm test -- --coverage
```

### Test Structure

Tests are organized following these conventions:
1. Test files are placed in `__tests__` directories next to the components they test
2. Test files are named `ComponentName.test.js`
3. Each test file includes:
   - Component rendering tests
   - Interaction tests
   - State management tests (if applicable)

### Example Test

Here's an example of how we test a form component:

```javascript
describe('ComponentName', () => {
  // Setup tests
  beforeEach(() => {
    // Setup code
  });

  it('renders without crashing', async () => {
    // Basic render test
  });

  it('handles user interactions', async () => {
    // Interaction tests
  });

  it('manages state correctly', async () => {
    // State management tests
  });
});
```

### Best Practices

1. Use `act()` when testing components that cause state updates
2. Mock external dependencies and Redux store
3. Test user interactions using `fireEvent` or `userEvent`
4. Write assertions that match how users interact with your app
5. Keep tests focused and follow the Arrange-Act-Assert pattern

### Testing Roadmap

The following items represent our next steps for improving test coverage and quality:

#### High Priority
- [ ] Add tests for critical form validation logic
- [ ] Add tests for API integration points
- [ ] Add tests for Redux actions and reducers
- [ ] Add end-to-end tests for critical user flows (e.g., donation process)

#### Medium Priority
- [ ] Migrate from Enzyme to React Testing Library for all tests
- [ ] Add snapshot tests for UI components
- [ ] Implement test coverage reporting and minimum coverage requirements
- [ ] Add performance tests for complex components

#### Nice to Have
- [ ] Add visual regression testing
- [ ] Implement automated accessibility testing
- [ ] Add load testing for forms and data-heavy components
- [ ] Create testing documentation with more detailed examples

Current test coverage focuses on the ContactForm component. Next components to test should be:
1. DonorSearch component
2. Payment processing components
3. Campaign management components
4. User authentication flows

## What's included

Within the download you'll find the following directories and files, logically grouping common assets and providing both compiled and minified variations. You'll see something like this:

```
CoreUI-React#v2.0.0
├── public/          #static files
│   ├── assets/      #assets
│   └── index.html   #html temlpate
│
├── src/             #project root
│   ├── containers/  #container source
│   ├── scss/        #user scss/css source
│   ├── views/       #views source
│   ├── App.js
│   ├── App.test.js
│   ├── index.js
│   ├── _nav.js      #sidebar config
│   └── routes.js    #routes config
│
└── package.json
```

## Documentation

The documentation for the CoreUI Admin Template is hosted at our website [CoreUI for React](https://coreui.io/react/)
