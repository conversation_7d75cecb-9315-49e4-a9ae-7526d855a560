{"name": "kpfa_admin", "version": "0.0.1", "description": "KPFA administration application", "author": "KPFA", "homepage": "./", "copyright": "Copyright 2019-2020", "license": "MIT", "private": true, "repository": {"type": "git", "url": "https://git.newday.host/krm/KPFA_Admin_Frontend.git"}, "dependencies": {"@coreui/coreui": "^4.3.0", "@coreui/react": "^4.11.0", "@coreui/utils": "^2.0.2", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.2.3", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@tsconfig/create-react-app": "^2.0.1", "@types/jest": "^29.5.8", "@types/node": "^20.10.5", "@types/react": "18.2.45", "@types/react-csv": "^1.1.10", "@types/react-dom": "18.2.18", "@types/react-redux": "^7.1.33", "@types/react-router-dom": "^5.3.3", "chart.js": "^4.4.0", "classname": "^0.0.0", "classnames": "^2.5.1", "core-js": "^3.36.0", "currency.js": "^2.0.4", "debounce-promise": "^3.1.2", "formik": "^2.4.6", "graphql": "^16.9.0", "graphql-tag": "^2.12.6", "iso-3166-1-alpha-2": "^1.0.2", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.11.4", "lodash": "^4.17.21", "logrocket": "^8.1.0", "moment": "^2.30.1", "node-sass": "^9.0.0", "postcss-normalize": "^10.0.1", "prop-types": "^15.8.1", "react": "18.2.0", "react-app-polyfill": "^3.0.0", "react-audio-player": "^0.17.0", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.1.0", "react-csv": "^2.2.2", "react-dom": "18.2.0", "react-loadable": "^5.5.0", "react-papaparse": "^4.4.0", "react-redux": "^9.1.1", "react-router-dom": "^6.21.1", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-test-renderer": "18.2.0", "react-text-mask": "^5.4.3", "typescript": "^5.3.2", "yup": "^0.32.11"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@playwright/test": "^1.54.1", "dotenv": "^17.2.0", "redux-mock-store": "^1.5.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:cov": "react-scripts test --coverage", "test:debug": "react-scripts --inspect-brk test --runInBand", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:donations": "playwright test tests/e2e/create-donation.test.ts", "test:e2e:validation": "playwright test tests/e2e/create-donation-validation.test.ts", "test:e2e:discovery": "playwright test tests/e2e/form-discovery.test.ts --headed", "test:e2e:credit-cards": "playwright test --grep 'credit card'", "test:e2e:one-time": "playwright test --grep 'one-time'", "test:e2e:monthly": "playwright test --grep 'monthly'", "test:all": "npm run test && npm run test:e2e", "eject": "react-scripts eject"}, "overrides": {"typescript": "^5.3.2"}, "bugs": {"url": ""}, "eslintConfig": {"extends": "react-app"}, "browserslist": [">0.2%", "not dead", "not ie <= 9", "not op_mini all"], "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}", "!**/*index.js", "!src/serviceWorker.js", "!src/polyfill.js"]}}