<!DOCTYPE html>
<html>
<head>
  <title>Auth Token Bridge</title>
  <meta charset="utf-8">
</head>
<body>
  <script>
    (function() {
      // This file is used to bridge authentication between StationAdmin and local development
      // It securely sends the JWT token from admin.kpfa.org to localhost via postMessage
      
      try {
        // Get the JWT token from localStorage
        const token = localStorage.getItem('jwt');
        
        // Check if there's a parent window (the iframe caller)
        if (window.parent && window.parent !== window) {
          // Generate an allow list of local development origins
          const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:3001', 
            'http://localhost:3002',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
            'http://127.0.0.1:3002',
            'https://pet-leopard-fully.ngrok-free.app'
          ];
          
          // Send the token to the parent window on all allowed origins
          // The receiving end will check the origin for security
          allowedOrigins.forEach(origin => {
            try {
              window.parent.postMessage({
                type: 'auth_token',
                token: token
              }, origin);
            } catch (err) {
              console.log(`Failed to send to ${origin}`);
            }
          });
        }
      } catch (error) {
        console.error('Error in token bridge:', error);
      }
    })();
  </script>
  
  <!-- Hidden content -->
  <div style="display: none;">
    Authentication bridge activated.
  </div>
</body>
</html> 