import { CNavItem, CNavTitle } from "@coreui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

let _nav = {
  admin: [
    {
      name: "Dashboard",
      to: "/dashboard",
      icon: <FontAwesomeIcon icon="home" className="nav-icon" />,
      component: CNavItem,
    },
    {
      component: CNavTitle,
      name: "<PERSON><PERSON>",
    },
    {
      component: CNavItem,
      name: "<PERSON><PERSON>",
      to: "/donors",
      icon: <FontAwesomeIcon icon="users" className="nav-icon" />,
    },
    {
      component: CNavItem,
      name: "Campaigns",
      to: "/campaigns",
      icon: <FontAwesomeIcon icon="phone" className="nav-icon" />,
    },
    {
      component: CNavItem,
      name: "Premiums",
      to: "/premiums",
      icon: <FontAwesomeIcon icon="gift" className="nav-icon" />,
    },
    {
      component: CNavTitle,
      name: "Reports",
    },
    {
      component: CNavItem,
      name: "Campaigns",
      to: "/reports/campaignreport",
      icon: <FontAwesomeIcon icon="chart-pie" className="nav-icon" />,
    },
    {
      component: CNavItem,
      name: "Billing",
      to: "/reports/billingreport",
      icon: <FontAwesomeIcon icon="print" className="nav-icon" />,
    },
    {
      component: CNavItem,
      name: "Shipping",
      to: "/reports/shipping",
      icon: <FontAwesomeIcon icon="envelope" className="nav-icon" />,
    },
    {
      component: CNavItem,
      name: "Tax Statements",
      to: "/reports/taxstatements",
      icon: <FontAwesomeIcon icon="mail-bulk" className="nav-icon" />,
    },
    {
      component: CNavItem,
      name: "Streaming",
      to: "/reports/streaming",
      icon: <FontAwesomeIcon icon="headphones" className="nav-icon" />,
    },
    {
      component: CNavItem,
      name: "Revenue",
      to: "/reports/revenue",
      icon: <FontAwesomeIcon icon="clipboard-list" className="nav-icon" />,
    },
    {
      component: CNavTitle,
      name: "",
    },
    {
      component: CNavItem,
      name: "KPFA Site",
      href: "https://kpfa.org",
      icon: <FontAwesomeIcon icon="broadcast-tower" className="nav-icon" />,
      attributes: { target: "_blank", rel: "noopener" },
    },
  ],
  staff: [
    {
      name: "Dashboard",
      to: "/dashboard",
      icon: <FontAwesomeIcon icon="home" className="nav-icon" />,
      component: CNavItem,
    },
    {
      component: CNavTitle,
      name: "Reports",
    },
    {
      component: CNavItem,
      name: "Campaigns",
      to: "/reports/campaignreport",
      icon: <FontAwesomeIcon icon="chart-pie" className="nav-icon" />,
    },
    {
      component: CNavItem,
      name: "KPFA Site",
      href: "https://kpfa.org",
      icon: <FontAwesomeIcon icon="broadcast-tower" className="nav-icon" />,
      attributes: { target: "_blank", rel: "noopener" },
    },
  ],
  callCenter: [
    {
      component: CNavItem,
      name: "Create Donation",
      to: "/donors",
      icon: <FontAwesomeIcon icon="users" className="nav-icon" />,
    },
  ],
};

export default _nav;
