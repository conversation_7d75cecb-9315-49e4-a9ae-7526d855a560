// Need to use the React-specific entry point to import createApi
import {
  BaseQueryFn,
  create<PERSON><PERSON>,
  FetchArgs,
  fetchBaseQuery,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query/react";
import type { Donation, Show, StreamStamp } from "../types";
import type { RootState } from "../store";

type ApiResponse<T> = {
  records: T[];
};

type CampaignDonationsResponse = ApiResponse<Donation>;
type ShowResponse = ApiResponse<Show>;

const rawBaseQuery = (baseUrl: string) =>
  fetchBaseQuery({
    baseUrl,
    prepareHeaders: (headers, { getState }: any) => {
      const token = getState().jwt.value;

      // If we have a token set in state, let's assume that we should be passing it.
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }

      //headers.set('Content-Type', "application/json")

      return headers;
    },
  });

const dynamicBaseQuery: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const testMode = (api.getState() as RootState).testMode;

  // gracefully handle scenarios where data to generate the URL is missing
  if (!testMode) {
    return {
      error: {
        status: 400,
        statusText: "Bad Request",
        data: "No test mode data received",
      },
    };
  }

  const baseUrl = testMode.value 
    ? `https://${testMode.route}kpfa.org`
    : `https://api.kpfa.org`;

  return rawBaseQuery(baseUrl)(args, api, extraOptions);
};

// Define a service using a base URL and expected endpoints
export const kpfa = createApi({
  reducerPath: "kpfa",
  baseQuery: dynamicBaseQuery,
  refetchOnMountOrArgChange: true,
  tagTypes: ["Campaign", "Premium", "Category", "Vendor"],
  endpoints: (builder) => ({
    getCampaigns: builder.query<any, void>({
      query: () => "campaigns",
      providesTags: ["Campaign"],
    }),
    getCampaign: builder.query({
      query: (id) => ({
        url: `campaigns/${id}`,
      }),
    }),
    editCampaign: builder.mutation({
      query: ({ body }) => ({
        url: `campaigns`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Campaign"],
    }),
    deleteCampaign: builder.mutation({
      query: ({ id }) => ({
        url: "campaigns",
        method: "DELETE",
        body: { id: id },
      }),
      invalidatesTags: ["Campaign"],
    }),
    addCampaign: builder.mutation({
      query: ({ body }) => ({
        url: "campaigns",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Campaign"],
    }),
    getPremiums: builder.query({
      query: () => "premiums",
      providesTags: ["Premium"],
    }),
    getPremium: builder.query({
      query: (id) => ({
        url: `premiums/${id}`,
      }),
    }),
    editPremium: builder.mutation({
      query: ({ body }) => ({
        url: "premiums",
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Premium"],
    }),
    addPremium: builder.mutation({
      query: ({ body }) => ({
        url: "premiums",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Premium"],
    }),
    addPremiumImage: builder.mutation({
      query: ({ body }) => ({
        url: "premiums/uploads",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Premium"],
    }),
    deletePremium: builder.mutation({
      query: (id) => ({
        url: `premiums/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Premium"],
    }),
    getPremiumCategories: builder.query({
      query: () => "premiums/categories",
      providesTags: ["Category"],
    }),
    addCategory: builder.mutation({
      query: ({ body }) => ({
        url: "premiums/categories",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Category"],
    }),
    editCategory: builder.mutation({
      query: ({ body }) => ({
        url: "premiums/categories",
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Category"],
    }),
    deleteCategory: builder.mutation({
      query: (id) => ({
        url: `premiums/categories/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Category"],
    }),
    getVendors: builder.query({
      query: () => "premiums/vendors",
      providesTags: ["Vendor"],
    }),
    addVendor: builder.mutation({
      query: ({ body }) => ({
        url: "premiums/vendors",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Vendor"],
    }),
    editVendor: builder.mutation({
      query: ({ body }) => ({
        url: "premiums/vendors",
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Vendor"],
    }),
    deleteVendor: builder.mutation({
      query: (id) => ({
        url: `premiums/vendors/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Vendor"],
    }),
    getDonation: builder.query({
      query: (id) => ({
        url: `donations/${id}`,
      }),
    }),
    getDonations: builder.query<
      ApiResponse<Donation>,
      { start: string; end: string }
    >({
      query: (body) => ({
        url: `donations?start=${body.start}&end=${body.end}`,
      }),
    }),
    getCampaignDonations: builder.query<CampaignDonationsResponse, number>({
      query: (campaignID) => ({
        url: `donations?campaign_id=${campaignID}`,
      }),
    }),
    addPayment: builder.mutation({
      query: ({ body }) => ({
        url: "payments",
        method: "POST",
        body,
      }),
    }),
    editPayment: builder.mutation({
      query: ({ body }) => ({
        url: "payments",
        method: "PUT",
        body,
      }),
    }),
    deletePayment: builder.mutation({
      query: ({ id }) => ({
        url: "payments",
        method: "DELETE",
        body: { id: id },
      }),
    }),
    createDonation: builder.mutation({
      query: ({ body }) => ({
        url: "donations",
        method: "POST",
        body,
      }),
    }),
    getStreamers: builder.query<StreamStamp[], {} | null>({
      query: () => "stats/streams",
    }),
    getAllShows: builder.query<ShowResponse, void>({
      query: () => ({
        url: "shows",
      }),
    }),
  }),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetCampaignsQuery,
  useGetCampaignQuery,
  useEditCampaignMutation,
  useAddCampaignMutation,
  useDeleteCampaignMutation,
  useGetPremiumsQuery,
  useAddPaymentMutation,
  useDeletePaymentMutation,
  useEditPaymentMutation,
  useCreateDonationMutation,
  useGetDonationQuery,
  useGetDonationsQuery,
  useGetPremiumCategoriesQuery,
  useAddCategoryMutation,
  useEditCategoryMutation,
  useDeleteCategoryMutation,
  useGetVendorsQuery,
  useEditVendorMutation,
  useAddVendorMutation,
  useDeleteVendorMutation,
  useAddPremiumMutation,
  useEditPremiumMutation,
  useDeletePremiumMutation,
  useLazyGetPremiumQuery,
  useLazyGetCampaignDonationsQuery,
  useGetStreamersQuery,
  useAddPremiumImageMutation,
  useLazyGetDonationsQuery,
  useGetAllShowsQuery,
} = kpfa;
