import React, { Suspense } from "react";
import { useRoutes } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, CSpinner } from "@coreui/react";
import routes, { callCenterRoutes, staffRoutes } from "../routes";
import { useAppSelector } from "../hooks";

const AppContent = () => {
  // Load routes based on the route configurations
  let elements = useRoutes(routes);
  let callCenterElements = useRoutes(callCenterRoutes);
  let staffElements = useRoutes(staffRoutes);

  // Get the access level from the Redux state
  const accessLevel = useAppSelector((state) => state.user.accessLevel);

  // Function to determine which routes to render based on access level
  const renderRoutes = () => {
    switch (accessLevel) {
      case "CallCenter":
        return callCenterElements;
      case "Staff":
        return staffElements;
      case "Admin":
        return elements;
      default:
        return null;
    }
  };

  return (
    <CContainer fluid>
      <Suspense fallback={<CSpinner color="primary" />}>
        {renderRoutes()}
      </Suspense>
    </CContainer>
  );
};

export default React.memo(AppContent);
