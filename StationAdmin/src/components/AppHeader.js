import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@coreui/react";
import { AppBreadcrumb } from "./index";
import logo from "../assets/img/brand/logo.svg";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { toggleSidebar } from "../slices/sidebarSlice";
import { useAppDispatch } from "../hooks";

const AppHeader = () => {
  const dispatch = useAppDispatch();

  return (
    <CHeader position="fixed" className="mb-4">
      <CContainer fluid>
        <CHeaderBrand className="me-auto" to="/">
          <img src={logo} height="48" alt="Logo" />
        </CHeaderBrand>
        <CHeaderToggler
          className="d-md-none text-dark"
          onClick={() => dispatch(toggleSidebar())}
        >
          <FontAwesomeIcon icon="bars" />
        </CHeaderToggler>
      </CContainer>
      <CHeaderDivider />
      <CContainer fluid>
        <AppBreadcrumb />
      </CContainer>
    </CHeader>
  );
};

export default AppHeader;
