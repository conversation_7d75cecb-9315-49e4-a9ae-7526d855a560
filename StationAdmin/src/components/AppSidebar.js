import React from "react";

import {
  CNavItem,
  CNavLink,
  CSidebar,
  CSidebarNav,
  CSidebarToggler,
} from "@coreui/react";

import { AppSidebarNav } from "./AppSidebarNav";

// sidebar nav config
import navigation from "../_nav";
import { toggleSidebar } from "../slices/sidebarSlice";
import { toggleTestMode } from "../slices/testModeSlice.ts";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useAppDispatch, useAppSelector } from "../hooks";

const AppSidebar = ({ signOut }) => {
  const dispatch = useAppDispatch();
  const visible = useAppSelector((state) => state.sidebar.visible);
  const testMode = useAppSelector((state) => state.testMode.value);
  const accessLevel = useAppSelector((state) => state.user.accessLevel);

  const handleNextAdminClick = (e) => {
    e.preventDefault();
    const token = localStorage.getItem('jwt');
    if (!token) {
      alert('Please log in first to access the Next Admin app');
      return;
    }

    const currentHostname = window.location.hostname;
    const isDevelopment =
      currentHostname === 'localhost' || currentHostname.includes('ngrok');

    let nextAppUrl;
    if (isDevelopment) {
      nextAppUrl = 'http://localhost:3001';
      console.log(
        'Using development URL for Next.js app:',
        nextAppUrl,
        'since current hostname is',
        currentHostname,
      );
    } else {
      nextAppUrl = 'https://next.kpfa.org';
      console.log(
        'Using production URL for Next.js app:',
        nextAppUrl,
        'since current hostname is',
        currentHostname,
      );
    }

    const targetUrl = `${nextAppUrl}/api/auth/token?token=${encodeURIComponent(
      token,
    )}&redirect=/duplicate-donors`;
    console.log('Opening URL:', targetUrl);

    // Open in new tab
    window.open(targetUrl, '_blank');
  };

  return (
    <CSidebar
      position="fixed"
      unfoldable
      visible={visible}
      onVisibleChange={(event) => {
        if (visible === true && event === false) {
          dispatch({
            type: "sidebar/toggleSidebar",
            payload: { visible: false },
          });
        }
      }}
    >
      <CSidebarNav>
        <AppSidebarNav
          items={
            accessLevel === "Admin"
              ? navigation.admin
              : accessLevel === "CallCenter"
              ? navigation.callCenter
              : navigation.staff
          }
        />
        {accessLevel === "Admin" ? (
          <CNavItem>
            <CNavLink
              onClick={() => dispatch(toggleTestMode())}
              role="button"
              className={testMode ? "text-danger" : ""}
            >
              <FontAwesomeIcon
                icon="flask"
                className={`fa-w-20 nav-icon ${testMode ? "text-danger" : ""}`}
              />
              Test Mode
            </CNavLink>
          </CNavItem>
        ) : null}
        {accessLevel === "Admin" ? (
          <CNavItem>
            <CNavLink
              onClick={handleNextAdminClick}
              role="button"
              title="Duplicates"
            >
              <FontAwesomeIcon icon="wrench" className="fa-w-20 nav-icon" />
              Duplicates
            </CNavLink>
          </CNavItem>
        ) : null}
        <CNavItem className="cursor-pointer">
          <CNavLink onClick={(e) => signOut(e)} role="button">
            <FontAwesomeIcon icon="sign-out-alt" className="fa-w-20 nav-icon" />
            Sign Out
          </CNavLink>
        </CNavItem>
      </CSidebarNav>
      <CSidebarToggler
        className="d-block d-md-none"
        onClick={() => dispatch(toggleSidebar())}
      />
    </CSidebar>
  );
};

export default React.memo(AppSidebar);
