import { CPagination, CPaginationItem } from "@coreui/react";

const ResultsPagination = ({ currentPage, handlePagination, totalPages }) => {
  if (totalPages > 1) {
    return (
      <div className="d-flex justify-content-center mt-5">
        <CPagination>
          {currentPage === 1 ? (
            <CPaginationItem disabled aria-label="Previous">
              «
            </CPaginationItem>
          ) : (
            <CPaginationItem
              aria-label="Previous"
              role="button"
              onClick={(e) => handlePagination(e)}
              data-increment="-1"
            >
              «
            </CPaginationItem>
          )}

          {currentPage - 2 > 0 ? (
            <CPaginationItem
              role="button"
              onClick={(e) => handlePagination(e)}
              data-increment="-2"
            >
              {currentPage - 2}
            </CPaginationItem>
          ) : null}

          {currentPage - 1 > 0 ? (
            <CPaginationItem
              role="button"
              onClick={(e) => handlePagination(e)}
              data-increment="-1"
            >
              {currentPage - 1}
            </CPaginationItem>
          ) : null}

          <CPaginationItem active>{currentPage}</CPaginationItem>

          {currentPage + 1 <= totalPages ? (
            <CPaginationItem
              role="button"
              onClick={(e) => handlePagination(e)}
              data-increment="1"
            >
              {currentPage + 1}
            </CPaginationItem>
          ) : null}

          {currentPage + 2 <= totalPages ? (
            <CPaginationItem
              role="button"
              onClick={(e) => handlePagination(e)}
              data-increment="2"
            >
              {currentPage + 2}
            </CPaginationItem>
          ) : null}

          {currentPage + 1 <= totalPages ? (
            <CPaginationItem
              role="button"
              onClick={(e) => handlePagination(e)}
              data-increment="1"
            >
              »
            </CPaginationItem>
          ) : (
            <CPaginationItem disabled>»</CPaginationItem>
          )}
        </CPagination>
      </div>
    );
  } else {
    return null;
  }
};

export default ResultsPagination;
