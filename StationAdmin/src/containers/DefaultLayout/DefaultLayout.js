import {
  AppContent,
  AppSidebar,
  AppFooter,
  AppHeader,
} from "../../components/index";

const DefaultLayout = ({ handleInvalidToken }) => {
  const signOut = (e) => {
    e.preventDefault();
    handleInvalidToken();
  };

  return (
    <div>
      <AppSidebar signOut={(e) => signOut(e)} />
      <div className="wrapper d-flex flex-column min-vh-100">
        <AppHeader />
        <div className="body flex-grow-1 px-3">
          <AppContent />
        </div>
        <AppFooter />
      </div>
    </div>
  );
};

export default DefaultLayout;
