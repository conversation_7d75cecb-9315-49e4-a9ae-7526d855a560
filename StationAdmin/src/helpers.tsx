import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { sortBy } from "lodash";
import { Payment, Premium } from "./types";

//expects an object of premiums
export function listPremiums(premiumsArr: Premium[]) {
  let premiums = [];

  if (premiumsArr) {
    for (const premium of premiumsArr) {
      if (premium.id) {
        premiums.push(premium.id);
      } else {
        premiums.push(premium);
      }
    }
    let outStr;
    if (premiums.length === 1) {
      outStr = premiums[0];
    } else if (premiums.length >= 2) {
      //joins all with commas
      outStr = premiums.join(", ");
    }
    return outStr;
  } else {
    return premiumsArr;
  }
}

//gross total of payment amounts from a pledge
export function grossTotal(payments: Payment[]) {
  let total = 0;
  for (const payment of payments) {
    total += payment.amount;
  }
  return total;
}

export function sortResultsArray(
  array: any[],
  sortColumn: string,
  sortOrderAscending: boolean
) {
  const sortedResults = sortBy(array, [sortColumn]);
  if (sortOrderAscending === false) {
    sortedResults.reverse();
  }
  return sortedResults;
}

export function showSortArrow(
  id: string,
  sortColumn: string,
  sortOrderAscending: boolean
) {
  if (sortColumn === id) {
    if (!sortOrderAscending) {
      return <FontAwesomeIcon icon="arrow-down" className="ms-2" />;
    }
    if (sortOrderAscending) {
      return <FontAwesomeIcon icon="arrow-up" className="ms-2" />;
    }
  }
  return null;
}

export function moneyFormat(amount: number = 0) {
  return amount.toLocaleString("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
}

export function toTitleCase(str: string | undefined): string {
  if (typeof str === "string") {
    return str.replace(
      /\w\S*/g,
      (txt) => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()
    );
  } else return "";
}

/**
 * Shortens a timestamp from 'YYYY-MM-DD HH:MM:SS' to 'M/D/YY HH:MM' in the Pacific Time Zone.
 * @param {string} timestamp - The original timestamp.
 * @return {string} - The shortened timestamp.
 */

export const shortenTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp);

  const options: Intl.DateTimeFormatOptions = {
    year: "2-digit",
    month: "numeric",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
    timeZone: "America/Los_Angeles",
  };

  return new Intl.DateTimeFormat("en-US", options).format(date);
};
