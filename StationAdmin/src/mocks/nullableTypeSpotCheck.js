const donations = require("./donations.json");
const property = "status";
const parentProperty = "shipments";
/***********/
let isNullable = false;
let propertyType = {};

//will need to restructure next const and for loop depending what you want to check
const itemsWithParentProperty = donations.records.filter(
  (item) => item[parentProperty].length > 0
);

for (const item of itemsWithParentProperty) {
  for (const parent of item[parentProperty]) {
    if (parent[property] !== null) {
      propertyType[typeof parent[property]] = true;
    }

    if (parent[property] === null) {
      console.log(payment);
      isNullable = true;
    }
  }
}

if (isNullable === false) {
  console.log("This property is not nullable");
} else {
  console.log("This property is nullable");
}

console.log(`${property} is a: ${JSON.stringify(propertyType)}`);
