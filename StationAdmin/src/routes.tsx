import React from "react";
import { Navigate } from "react-router-dom";

const Dashboard = React.lazy(() => import("./views/Dashboard"));
const ManageDonors = React.lazy(() => import("./views/Donors/Manage"));
const CampaignReport = React.lazy(
  () => import("./views/Reports/CampaignReport")
);
const BillingReport = React.lazy(() => import("./views/Reports/BillingReport"));
const ShippingReport = React.lazy(() => import("./views/Reports/Shipping"));
const TaxStatements = React.lazy(() => import("./views/Reports/TaxStatements"));
const StreamingReport = React.lazy(
  () => import("./views/Reports/Streaming/StreamingReport")
);
const Revenue = React.lazy(() => import("./views/Reports//Revenue/Revenue"));
const PremiumManagement = React.lazy(() => import("./views/Premiums"));
const Vendors = React.lazy(() => import("./views/Vendors/ManageVendors"));
const ManageCampaigns = React.lazy(
  () => import("./views/Campaigns/ManageCampaigns")
);
const ManageCategories = React.lazy(
  () => import("./views/PremiumCategories/ManageCategories")
);

export const callCenterRoutes = [
  { path: "*", name: "Call Center Dashboard", element: <ManageDonors /> },
];

export const staffRoutes = [
  { path: "/dashboard", name: "Dashboard", element: <Dashboard /> },
  {
    path: "/reports/campaignreport",
    name: "Campaign Report",
    element: <CampaignReport />,
  },
  {
    path: "/",
    element: <Navigate to="/dashboard" />,
  },
];

const routes = [
  { path: "/dashboard", name: "Dashboard", element: <Dashboard /> },
  {
    path: "/donors",
    name: "Manage Donors",
    element: <ManageDonors />,
  },
  {
    path: "/campaigns",
    name: "Manage Campaigns",
    element: <ManageCampaigns />,
  },
  {
    path: "/reports/campaignreport",
    name: "Campaign Report",
    element: <CampaignReport />,
  },
  {
    path: "/reports/billingreport",
    name: "Billing Report",
    element: <BillingReport />,
  },
  {
    path: "/reports/shipping",
    name: "Shipping Report",
    element: <ShippingReport />,
  },
  {
    path: "/reports/taxstatements",
    name: "Tax Statements",
    element: <TaxStatements />,
  },
  {
    path: "/reports/streaming",
    name: "Streaming Report",
    element: <StreamingReport />,
  },
  {
    path: "reports/revenue",
    name: "Revenue",
    element: <Revenue />,
  },
  {
    path: "/premiums",
    name: "Premiums",
    element: <PremiumManagement />,
  },
  {
    path: "/vendors",
    name: "Vendors",
    element: <Vendors />,
  },
  {
    path: "/categories",
    name: "Manage Premium Categories",
    element: <ManageCategories />,
  },
  {
    path: "/",
    element: <Navigate to="/dashboard" />,
  },
];

export default routes;
