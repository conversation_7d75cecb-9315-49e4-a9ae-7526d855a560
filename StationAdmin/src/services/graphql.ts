import { createApi, BaseQueryFn } from "@reduxjs/toolkit/query/react";
import {
  GetDuplicateGroupsResponse,
  GetDuplicateDonorResponse,
  CreateDuplicateGroupVariables,
  UpdateDuplicateGroupVariables,
  UpdateDuplicateDonorVariables,
  CreateDuplicateDonorVariables,
  DuplicateDonor,
  DuplicateGroup,
} from "../types/graphql";
import type { RootState } from '../store';

// Determine if we're in development mode
const isDevMode = process.env.NODE_ENV === 'development';

// API URL configuration
const STAGING_API_URL = 'https://keystone.staging.kpfa.org/api/graphql';
const LOCAL_API_URL = 'http://localhost:3000/api/graphql';
const PRODUCTION_API_URL = 'https://keystone.kpfa.org/api/graphql';

interface GraphQLResponse<T = unknown> {
  data?: T;
  errors?: Array<{ message: string }>;
}

type CustomBaseQueryFn = BaseQueryFn<
  { body: string },
  unknown,
  { status: number; message: string; data: any }
>;

const graphqlBaseQuery = ({ baseUrl }: { baseUrl: string }): CustomBaseQueryFn =>
  async ({ body }, { getState }) => {
    try {
      const state = getState() as RootState;
      const token = state.jwt.value;
      const isTestMode = state.testMode.value;
      
      // Use the appropriate API URL based on environment and test mode
      const effectiveBaseUrl = isTestMode ? STAGING_API_URL : (isDevMode ? LOCAL_API_URL : PRODUCTION_API_URL);
      
      console.log(`Using GraphQL API: ${effectiveBaseUrl} (${isTestMode ? 'Staging' : (isDevMode ? 'Local' : 'Production')} Environment)`);

      const headers: HeadersInit = {
        "Content-Type": "application/json",
      };

      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }

      const result = await fetch(effectiveBaseUrl, {
        method: "POST",
        headers,
        body,
      });

      const json = await result.json() as GraphQLResponse;

      if (result.ok) {
        return { data: json.data };
      } else {
        return {
          error: {
            status: result.status,
            message: json.errors ? json.errors[0]?.message : "Unknown error",
            data: json.errors,
          },
        };
      }
    } catch (err) {
      return {
        error: {
          status: 500,
          message: err instanceof Error ? err.message : "Network error",
          data: null,
        },
      };
    }
  };

export const graphqlApi = createApi({
  baseQuery: graphqlBaseQuery({
    baseUrl: STAGING_API_URL, // This is just a placeholder since we determine the actual URL at request time
  }),
  tagTypes: ["DuplicateGroups", "DuplicateDonors"],
  endpoints: (builder) => ({
    getDuplicateGroups: builder.query<GetDuplicateGroupsResponse, void>({
      query: () => ({
        body: JSON.stringify({
          query: `
            query GetDuplicateGroups {
              duplicateGroups {
                id
                name
                primaryDonor {
                  id
                  kpfaId
                }
                duplicateDonors {
                  id
                  kpfaId
                }
              }
            }
          `,
        }),
      }),
      providesTags: ["DuplicateGroups"],
    }),
    createDuplicateGroup: builder.mutation<DuplicateGroup, CreateDuplicateGroupVariables>({
      query: (newGroup: CreateDuplicateGroupVariables) => ({
        body: JSON.stringify({
          query: `
            mutation CreateDuplicateGroup($name: String!) {
              createDuplicateGroup(data: { name: $name }) {
                id
                name
                primaryDonor {
                  id
                }
                duplicateDonors {
                  id
                }
              }
            }
          `,
          variables: newGroup,
        }),
      }),
      invalidatesTags: ["DuplicateDonors", "DuplicateGroups"],
    }),
    updateDuplicateGroup: builder.mutation<DuplicateGroup, UpdateDuplicateGroupVariables>({
      query: ({ id, primaryDonorId }: UpdateDuplicateGroupVariables) => ({
        body: JSON.stringify({
          query: `
            mutation UpdateDuplicateGroup($id: ID!, $primaryDonorId: ID) {
              updateDuplicateGroup(
                where: { id: $id },
                data: {
                  primaryDonor: { connect: { id: $primaryDonorId } },
                }
              ) {
                id
                name
                primaryDonor {
                  id
                }
              }
            }
          `,
          variables: { id, primaryDonorId },
        }),
      }),
      invalidatesTags: ["DuplicateDonors", "DuplicateGroups"],
    }),
    getDuplicateDonor: builder.query<GetDuplicateDonorResponse, { kpfaId: number }>({
      query: ({ kpfaId }: { kpfaId: number }) => ({
        body: JSON.stringify({
          query: `
            query GetDuplicateDonor($kpfaId: Int!) {
              duplicateDonors(where: { kpfaId: { equals: $kpfaId } }) {
                id
                kpfaId
                group {
                  id
                  name
                  primaryDonor {
                    kpfaId
                  }
                }
                primaryGroup {
                  id
                }
              }
            }
          `,
          variables: { kpfaId },
        }),
      }),
      providesTags: ["DuplicateDonors"],
    }),
    updateDuplicateDonor: builder.mutation<DuplicateDonor, UpdateDuplicateDonorVariables>({
      query: ({ id, group, primaryGroupId }: UpdateDuplicateDonorVariables) => {
        const variables: any = { id, group };
        const variableDeclarations = '$id: ID!, $group: ID';
        
        let mutationString;
        
        if (primaryGroupId) {
          variables.primaryGroupId = primaryGroupId;
          mutationString = `
            mutation UpdateDuplicateDonor(${variableDeclarations}, $primaryGroupId: ID) {
              updateDuplicateDonor(
                where: { id: $id },
                data: {
                  group: { connect: { id: $group } },
                  primaryGroup: { connect: { id: $primaryGroupId } }
                }
              ) {
                id
                kpfaId
                group {
                  id
                }
                primaryGroup {
                  id
                }
              }
            }
          `;
        } else {
          mutationString = `
            mutation UpdateDuplicateDonor(${variableDeclarations}) {
              updateDuplicateDonor(
                where: { id: $id },
                data: {
                  group: { connect: { id: $group } }
                }
              ) {
                id
                kpfaId
                group {
                  id
                }
                primaryGroup {
                  id
                }
              }
            }
          `;
        }

        return {
          body: JSON.stringify({
            query: mutationString,
            variables,
          }),
        };
      },
      invalidatesTags: ["DuplicateDonors", "DuplicateGroups"],
    }),
    createDuplicateDonor: builder.mutation<DuplicateDonor, CreateDuplicateDonorVariables>({
      query: ({ kpfaId, group }: CreateDuplicateDonorVariables) => ({
        body: JSON.stringify({
          query: `
            mutation CreateDuplicateDonor($kpfaId: Int!, $group: ID) {
              createDuplicateDonor(data: { kpfaId: $kpfaId, group: { connect: { id: $group } } }) {
                id
                kpfaId
                group {
                  id
                }
                primaryGroup {
                  id
                }
              }
            }
          `,
          variables: { kpfaId, group },
        }),
      }),
      invalidatesTags: ["DuplicateDonors", "DuplicateGroups"],
    }),
  }),
});

export const {
  useGetDuplicateGroupsQuery,
  useCreateDuplicateGroupMutation,
  useGetDuplicateDonorQuery,
  useUpdateDuplicateDonorMutation,
  useCreateDuplicateDonorMutation,
  useUpdateDuplicateGroupMutation,
} = graphqlApi;