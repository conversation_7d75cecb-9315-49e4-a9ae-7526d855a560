import { createSlice } from "@reduxjs/toolkit";

interface TestModeState {
  value: boolean;
  route: string;
}

const initialState = {
  value:
    (window.location.href.indexOf("staging") !== -1 ||
      window.location.href.indexOf("localhost") !== -1) &&
    window.location.href.indexOf("livemode") === -1
      ? true
      : false,
  route: "api.staging.",
} as TestModeState;

export const testModeSlice = createSlice({
  name: "testMode",
  initialState,
  reducers: {
    toggleTestMode: (state) => {
      state.value = !state.value;
    },
  },
});

export const { toggleTestMode } = testModeSlice.actions;
