import React from 'react';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';

// Create simple mock reducers for testing
const mockJwtReducer = (state = { token: 'mock-token', isAuthenticated: true }, action) => state;
const mockUserReducer = (state = { id: 1, username: 'testuser', role: 'admin' }, action) => state;
const mockSidebarReducer = (state = { show: false }, action) => state;
const mockCampaignOptionsReducer = (state = [{ value: 1, label: 'Test Campaign' }], action) => state;
const mockTestModeReducer = (state = { value: false, route: 'staging.' }, action) => state;

// Create a mock store for testing
export function createMockStore(initialState = {}) {
  const defaultState = {
    jwt: { token: 'mock-token', isAuthenticated: true },
    user: { id: 1, username: 'testuser', role: 'admin' },
    sidebar: { show: false },
    campaignOptions: [{ value: 1, label: 'Test Campaign' }],
    testMode: { value: false, route: 'staging.' },
    ...initialState,
  };

  return configureStore({
    reducer: {
      jwt: mockJwtReducer,
      user: mockUserReducer,
      sidebar: mockSidebarReducer,
      campaignOptions: mockCampaignOptionsReducer,
      testMode: mockTestModeReducer,
    },
    preloadedState: defaultState,
  });
}

// Custom render function that includes providers
export function renderWithProviders(
  ui,
  {
    preloadedState = {},
    store = createMockStore(preloadedState),
    initialEntries = ['/'],
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return (
      <Provider store={store}>
        <MemoryRouter initialEntries={initialEntries}>
          <Routes>
            <Route path="*" element={children} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
}

// Simple wrapper for components that only need Redux
export function renderWithRedux(
  ui,
  {
    preloadedState = {},
    store = createMockStore(preloadedState),
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return <Provider store={store}>{children}</Provider>;
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
}

// Simple wrapper for components that only need Router
export function renderWithRouter(
  ui,
  {
    initialEntries = ['/'],
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return (
      <MemoryRouter initialEntries={initialEntries}>
        <Routes>
          <Route path="*" element={children} />
        </Routes>
      </MemoryRouter>
    );
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock data for common test scenarios
export const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  role: 'admin',
};

export const mockJwt = {
  token: 'mock-jwt-token',
  isAuthenticated: true,
};

export const mockCampaignOptions = [
  { value: 1, label: 'Test Campaign 1' },
  { value: 2, label: 'Test Campaign 2' },
];

// Default mock state
export const defaultMockState = {
  user: mockUser,
  jwt: mockJwt,
  campaignOptions: mockCampaignOptions,
  sidebar: { show: false },
  testMode: { enabled: false },
};
