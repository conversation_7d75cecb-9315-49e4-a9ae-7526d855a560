export interface Donation {
  id: number;
  account_id: number | null;
  donor_id: number;
  transaction_id: number;
  timestamp: string;
  firstname: string;
  lastname: string;
  address1: string;
  address2: string | null;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  shipping_firstname: string | null;
  shipping_lastname: string | null;
  shipping_address1: string | null;
  shipping_address2: string | null;
  shipping_city: string | null;
  shipping_state: string | null;
  shipping_country: string | null;
  shipping_postal_code: string | null;
  phone: string;
  email: string;
  type: DonationType;
  amount: number;
  status: DonationStatus;
  major_donation: boolean;
  installment: DonationInstallment;
  comments: string | null;
  add_me: boolean;
  read_onair: boolean;
  donation_match: boolean;
  show_name: string;
  source: DonationSource;
  campaign_id: number | null;
  campaign: string | null;
  updated: string;
  recording_url: string | null;
  recording_size: string | null;
  recording_length: string | null;
  premiums_ID: number[];
  premiums: Premium[];
  payments: Payment[];
  shipments: Shipment[];
  subscriptions: [];
}

type DonationType =
  | "Pledge"
  | "In-Kind"
  | "Vehicle"
  | "Bequest"
  | "Corporate Match"
  | "QCD"
  | "Donor Advised Fund"
  | "Major"
  | "Minor";

type DonationStatus = "Unpaid" | "Paid" | "Canceled" | "Refunded";

type DonationInstallment = "One-Time" | "Monthly";

type DonationSource =
  | "StationAdmin"
  | "CallCenter"
  | "PhoneRoom"
  | "WebSite"
  | "Testing"
  | "PaySol"
  | "Allegiance";

export interface Premium {
  id: number;
  name: string;
  price: number;
  cog: number;
  fmv: number;
  qty: number;
  download_url: string | null;
  img_url: string;
  category: string;
  shipment_status?: "Shipped" | "Returned" | "New" | "Canceled" | "On Hold";
}

export interface Payment {
  id: number;
  customer_id: number | null;
  payment_id: number;
  processor:
    | "Stripe"
    | "PayPal"
    | "Venmo"
    | "DipJar"
    | "P-check"
    | "K-check"
    | "cash"
    | "ACH";
  method:
    | "card"
    | "check"
    | "cash"
    | "stock"
    | "inkind"
    | "bank_account"
    | "epayment";
  card_type: "credit" | "debit" | "prepaid" | "unknown" | null;
  amount: number;
  amount_refunded: number;
  status: string;
  transaction_status: string;
  last4: number | boolean;
  exp_month: boolean | number;
  exp_year: null | number;
  date_created: string;
  date_updated: string;
  date_deposit: string;
}

export interface Shipment {
  id: number;
  premium_id: number;
  status: "Shipped" | "returned" | "New" | "Canceled" | "On Hold";
}

export interface StreamStamp {
  timestamp: string;
  stream: string;
  streamers: number;
  show: string;
  episode: string;
  post_id: number;
}

export interface Show {
  days: string;
  name: string;
  startsMoment: string;
  endsMoment: string;
  type: string;
  weeks_that_it_airs: [];
}

//type returned by KPFA API
export interface Campaign {
  id: number;
  type: string;
  name: string;
  active: boolean;
  start: string;
  end: string;
  duration: string;
  goal: number;
  gift_title: string | null;
  gift_link: string | null;
  notes: string | null;
  count: number;
  pledged: number;
  pledged_percent: number;
  paid: number;
  sustainer_total: number;
  sustainer_count: number;
  paid_percent: number;
  pledge_daily_average: number;
  pledge_donation_average: number;
}

export interface Show {
  id: number;
  name: string;
  hosts: string;
  description: string;
  starts: string;
  ends: string;
  duration: number;
  days: string;
  weeks: string[];
  dad_id: number;
  type: string;
  img_url: string;
  active: boolean;
}
