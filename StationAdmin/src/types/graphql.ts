export interface DuplicateDonor {
  id: string;
  kpfaId: number;
  group: { id: string } | null;
  primaryGroup: { id: string } | null;
}

export interface DuplicateGroup {
  id: string;
  name: string;
  primaryDonor: DuplicateDonor | null;
  duplicateDonors: DuplicateDonor[];
}

export interface GetDuplicateGroupsResponse {
  duplicateGroups: DuplicateGroup[];
}

export interface GetDuplicateDonorResponse {
  duplicateDonors: DuplicateDonor[];
}

export interface CreateDuplicateGroupVariables {
  name: string;
}

export interface UpdateDuplicateGroupVariables {
  id: string;
  primaryDonorId: string | null;
}

export interface UpdateDuplicateDonorVariables {
  id: string;
  group: string;
  primaryGroupId?: string;
}

export interface CreateDuplicateDonorVariables {
  kpfaId: number;
  group: string;
}
