import React, { forwardRef, useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import InputFlexible from "../Form/InputFlexible";
import campaignSchema from "./CampaignSchema";
import { CRow, CCol, CButton, CButtonGroup } from "@coreui/react";
import Checkbox from "../Form/Checkbox";
import SelectField from "../Form/SelectField";
import {
  useAddCampaignMutation,
  useEditCampaignMutation,
} from "../../api/kpfa.ts";
import Alert from "../../components/Alert";

const blankInitialValues = {
  type: "",
  name: "",
  active: false,
  start: "",
  end: "",
  goal: "",
  gift_title: "",
  gift_link: "",
};

const CampaignForm = forwardRef(
  (
    {
      selectedCampaign,
      setSelectedCampaign,
      toggleEdit,
      alert,
      setAlert,
      visible,
    },
    ref
  ) => {
    const [initialValues, setInitialValues] = useState(blankInitialValues);
    const [editCampaign] = useEditCampaignMutation();
    const [addCampaign] = useAddCampaignMutation();

    useEffect(() => {
      selectedCampaign
        ? setInitialValues({
            type: selectedCampaign.type,
            name: selectedCampaign.name,
            active: selectedCampaign.active ? true : false,
            start: selectedCampaign.start.slice(0, 10),
            end: selectedCampaign.end.slice(0, 10),
            goal: selectedCampaign.goal,
            gift_title:
              selectedCampaign.gift_title === null
                ? ""
                : selectedCampaign.gift_title,
            gift_link:
              selectedCampaign.gift_link === null
                ? ""
                : selectedCampaign.gift_link,
          })
        : setInitialValues(blankInitialValues);

      console.log(selectedCampaign);
    }, [selectedCampaign]);

    return (
      <div ref={ref}>
        {visible ? (
          <div className="bg-white rounded shadow">
            <Formik
              enableReinitialize={true}
              initialValues={initialValues}
              validationSchema={campaignSchema}
              onSubmit={(values, { setSubmitting }) => {
                setAlert({ display: false });

                let body = values;

                if (body.type === "mailer" || body.type === "email") {
                  body.end = body.start;
                } else {
                  body.end += " 23:59:59";
                }

                if (selectedCampaign) {
                  body.id = selectedCampaign.id;
                }

                const mutation = selectedCampaign ? editCampaign : addCampaign;

                mutation({ body })
                  .unwrap()
                  .then((fulfilled) => {
                    setAlert({
                      type: "success",
                      display: true,
                      message: `Successfully ${
                        selectedCampaign ? "updated" : "created"
                      } campaign: ${fulfilled.name}`,
                    });
                    body.id = fulfilled.id;
                    body.end = body.end.slice(0, 10);
                    setSelectedCampaign(body);
                  })
                  .catch((rejected) => {
                    setAlert({
                      type: "fail",
                      display: true,
                      message: rejected?.data?.message,
                      error: rejected?.error,
                    });
                  });

                setSubmitting(false);
              }}
            >
              {({ values }) => (
                <Form className="col-12 mt-2 mb-4 p-3">
                  <h3>
                    {selectedCampaign ? `Edit Campaign` : "Create New Campaign"}
                  </h3>
                  <CRow className="pb-2 pt-4">
                    <CCol>
                      <Field
                        name="active"
                        label="Active"
                        component={Checkbox}
                      />
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol xs={12} sm className="mb-4">
                      <Field
                        name="name"
                        label="Name"
                        component={InputFlexible}
                      />
                    </CCol>
                    <CCol xs={12} sm>
                      <Field
                        name="type"
                        label="Type"
                        component={SelectField}
                        labelPlacement="top"
                        options={[
                          { value: "marathon", label: "Marathon" },
                          { value: "mailer", label: "Mailer" },
                          { value: "general", label: "General" },
                          { value: "email", label: "Email" },
                        ]}
                      />
                    </CCol>
                    <CCol className="mb-4" xs={12} sm>
                      <Field
                        type="number"
                        name="goal"
                        label="Goal"
                        component={InputFlexible}
                      />
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol className="mb-4">
                      <Field
                        type="date"
                        name="start"
                        label="Start"
                        component={InputFlexible}
                      />
                    </CCol>
                    <CCol className="mb-4">
                      {values.type === "mailer" ||
                      values.type === "email" ? null : (
                        <Field
                          type="date"
                          name="end"
                          label="End"
                          component={InputFlexible}
                        />
                      )}
                    </CCol>
                  </CRow>

                  <CRow>
                    <CCol className="mb-4">
                      <Field
                        name="gift_title"
                        label="Gift Title"
                        component={InputFlexible}
                      />
                    </CCol>
                    <CCol className="mb-4">
                      <Field
                        type="url"
                        name="gift_link"
                        label="Gift Link"
                        component={InputFlexible}
                      />
                    </CCol>
                  </CRow>

                  <CRow className="pb-4">
                    <CCol>
                      <CButtonGroup>
                        <CButton type="submit">
                          {selectedCampaign ? "Edit" : "Create"}
                        </CButton>
                        <CButton onClick={toggleEdit} variant="outline">
                          Cancel
                        </CButton>
                      </CButtonGroup>
                      <Alert alert={alert} />
                    </CCol>
                  </CRow>
                </Form>
              )}
            </Formik>
          </div>
        ) : null}
      </div>
    );
  }
);

export default CampaignForm;
