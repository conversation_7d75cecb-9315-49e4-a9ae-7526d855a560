import * as Yup from "yup";
import { errorStrings } from "../Form/validationStrings";

const campaignSchema = Yup.object().shape({
  name: Yup.string().required(errorStrings.required),
  type: Yup.string().required(errorStrings.required),
  goal: Yup.number()
    .positive(errorStrings.positiveNum)
    .required(errorStrings.required),
  start: Yup.string().required(errorStrings.required),
  end: Yup.string().when("type", {
    is: (val) => val !== "mailer" && val !== "email",
    then: Yup.string().required(errorStrings.required),
  }),
  gift_title: Yup.string().nullable(),
  gift_link: Yup.string().url(errorStrings.url),
});

export default campaignSchema;
