import {
  CButton,
  CButtonGroup,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";
import { useState } from "react";
import { useDeleteCampaignMutation } from "../../api/kpfa.ts";
import Alert from "../../components/Alert";

export default function DeleteModal({ toggle, visible, selectedCampaign }) {
  const [
    deleteCampaign,
    { isLoading, isSuccess },
  ] = useDeleteCampaignMutation();
  const [alert, setAlert] = useState({ display: false });

  const handleDelete = () => {
    deleteCampaign({ id: selectedCampaign.id })
      .unwrap()
      .then((fulfilled) => {
        setAlert({
          type: "success",
          display: true,
          message: fulfilled.message,
        });
      })
      .catch((rejected) => {
        setAlert({
          type: "fail",
          display: true,
          message: rejected?.data?.message,
          error: rejected?.error,
        });
      });
  };

  return (
    <CModal
      visible={visible}
      onClose={toggle}
      backdrop="static"
      keyboard={false}
    >
      <CModalHeader onClose={toggle}>
        <h4>Confirm Campaign Deletion</h4>
      </CModalHeader>
      <CModalBody className="py-4">
        {isSuccess
          ? null
          : `Are you sure you want to delete ${selectedCampaign.name}?`}
        <Alert alert={alert} />
      </CModalBody>
      <CModalFooter>
        <CButtonGroup>
          <CButton
            onClick={toggle}
            disabled={isLoading}
            color={isSuccess ? "primary" : "danger"}
            variant="outline"
            size="sm"
          >
            {isSuccess ? "Back" : "Cancel"}
          </CButton>
          {isSuccess ? null : (
            <CButton
              onClick={handleDelete}
              disabled={isLoading}
              className="text-white"
              size="sm"
              color="danger"
            >
              Delete
            </CButton>
          )}
        </CButtonGroup>
      </CModalFooter>
    </CModal>
  );
}
