import React from "react";
import {
  CBadge,
  CButton,
  CButtonGroup,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from "@coreui/react";
import currency from "currency.js";

export default function DisplayResults({ campaigns, campaignAction }) {
  const formatDollars = (amount) => {
    return currency(amount, { precision: 0 }).format();
  };

  return (
    <div className="mx-auto p-3 bg-white rounded shadow my-3">
      <h3 className="mb-3">Campaigns</h3>
      <CTable responsive>
        <CTableHead>
          <CTableRow>
            <CTableHeaderCell scope="col">Name</CTableHeaderCell>
            <CTableHeaderCell scope="col">Type</CTableHeaderCell>
            <CTableHeaderCell scope="col">Start</CTableHeaderCell>
            <CTableHeaderCell scope="col">End</CTableHeaderCell>
            <CTableHeaderCell scope="col">Goal</CTableHeaderCell>
            <CTableHeaderCell scope="col">Pledged</CTableHeaderCell>
            <CTableHeaderCell scope="col">Paid</CTableHeaderCell>
            <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {campaigns.map((campaign) => (
            <CTableRow key={campaign.id}>
              <CTableDataCell scope="row" className="align-middle">
                {campaign.name}{" "}
                {campaign.active ? (
                  <CBadge color="success" className="ms-2">
                    Active
                  </CBadge>
                ) : null}
              </CTableDataCell>
              <CTableDataCell className="text-capitalize align-middle">
                {campaign.type}
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                {campaign.start.slice(0, 10)}
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                {campaign.end.slice(0, 10)}
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                {formatDollars(campaign.goal)}
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                <div className="d-flex justify-content-between">
                  {formatDollars(campaign.pledged)}
                  <CBadge color="light" className="text-dark">
                    {currency(campaign.pledged_percent).dollars()}%
                  </CBadge>
                </div>
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                <div className="d-flex justify-content-between">
                  {formatDollars(campaign.paid)}
                  <CBadge color="light" className="text-dark">
                    {currency(campaign.paid_percent).dollars()}%
                  </CBadge>
                </div>
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                <CButtonGroup>
                  <CButton
                    onClick={(e) => campaignAction(e, "edit")}
                    variant="outline"
                    size="sm"
                    data-id={campaign.id}
                  >
                    Edit
                  </CButton>
                  <CButton
                    onClick={(e) => campaignAction(e, "delete")}
                    data-id={campaign.id}
                    variant="outline"
                    size="sm"
                  >
                    Delete
                  </CButton>
                </CButtonGroup>
              </CTableDataCell>
            </CTableRow>
          ))}
        </CTableBody>
      </CTable>
    </div>
  );
}
