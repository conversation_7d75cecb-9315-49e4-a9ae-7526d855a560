import { useGetCampaignsQuery } from "../../api/kpfa.ts";
import React, { useRef, useState } from "react";
import DisplayResults from "./DisplayResults";
import CampaignForm from "./CampaignForm";
import { CButton } from "@coreui/react";
import DeleteModal from "./DeleteModal";

export default function ManageCampaigns() {
  const [showEdit, setShowEdit] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState();
  const [alert, setAlert] = useState({
    type: "",
    display: false,
    message: "",
  });

  const {
    data: campaignData,
    isLoading: campaignDataIsLoading,
  } = useGetCampaignsQuery();

  const formRef = useRef();

  const campaignAction = (e, action) => {
    setSelectedCampaign(
      campaignData.filter(
        (campaign) => campaign.id === parseInt(e.target.dataset.id)
      )[0]
    );

    if (action === "edit") {
      setShowEdit(true);
      setAlert({ display: false });
      formRef.current.scrollIntoView({ behavior: "smooth" });
    }

    if (action === "delete") {
      setShowEdit(false);
      setShowDelete(true);
    }
  };

  const handleNewCampaignClick = () => {
    setShowEdit(true);
    setAlert({ display: false });

    if (selectedCampaign) {
      setSelectedCampaign(null);
    }
  };

  return (
    <>
      {!showEdit || (showEdit && selectedCampaign) ? (
        <CButton className="mb-3" onClick={handleNewCampaignClick}>
          Create New Campaign
        </CButton>
      ) : null}

      <CampaignForm
        ref={formRef}
        visible={showEdit}
        selectedCampaign={selectedCampaign}
        setSelectedCampaign={setSelectedCampaign}
        toggleEdit={() => setShowEdit(false)}
        alert={alert}
        setAlert={setAlert}
      />

      {!campaignDataIsLoading && campaignData ? (
        <DisplayResults
          campaigns={campaignData}
          campaignAction={campaignAction}
        />
      ) : (
        <div className="d-flex justify-content-center pt-3">
          <div className="spinner-border text-info">
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      )}
      {showDelete ? (
        <DeleteModal
          toggle={() => setShowDelete(false)}
          visible={showDelete}
          selectedCampaign={selectedCampaign}
        />
      ) : null}
    </>
  );
}
