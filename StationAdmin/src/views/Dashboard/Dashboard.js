import { useEffect, useState, useRef, useCallback } from "react";
import moment from "moment";
import { isEqual, cloneDeep, sortBy } from "lodash";
import currency from "currency.js";
import { CCollapse, CButton, CRow } from "@coreui/react";

import DonationStats from "./components/DonationStats";
import DonationTimeline from "./components/DonationTimeline";
import StreamersAndCallers from "./components/StreamersAndCallers";
import "./styles/dashboard.css";
import useAsyncReference from "../Hooks/useAsyncReference";
import { useGetCampaignsQuery } from "../../api/kpfa.ts";
import { useAppSelector } from "../../hooks";

const defaultCampaignSelectOption = { value: "", label: "All Donations" };
const dateMin = moment("1990-01-01");

const Dashboard = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [statsExpanded, setStatsExpanded] = useState(true);
  const [showsForSelectedDate, setShowsForSelectedDate] = useState([]);
  const [selectedDate, setSelectedDate] = useState(
    moment().format("YYYY-MM-DD"),
  );
  const [today] = useState(moment().format("YYYY-MM-DD"));
  const [selectedDonationGrouping, setSelectedDonationGrouping] =
    useState("show");
  const [donationsForSelectedDate, setDonationsForSelectedDate] =
    useAsyncReference([]);
  const [
    filteredDonationsForSelectedDate,
    setFilteredDonationsForSelectedDate,
  ] = useState([]);
  const [selectedCampaign, setSelectedCampaign] = useAsyncReference(
    defaultCampaignSelectOption,
  );
  const [campaigns, setCampaigns] = useState([defaultCampaignSelectOption]);
  const [filteredDonationsTotal, setFilteredDonationsTotal] = useState(0);
  const [filterDonationsLength, setFilterDonationsLength] = useState(0);
  const [currentStats, setCurrentStats] = useState({});
  const [currentMarathonStats, setCurrentMarathonStats] = useState({});
  const [numberOfCallers, setNumberOfCallers] = useState(0);
  const [filterMatch, setFilterMatch] = useAsyncReference(false);
  const [showAbandonedDonations, setShowAbandonedDonations] =
    useAsyncReference(false);
  const [PrevCampaignData, setPrevCampaignData] = useState(null);
  const [selectActiveCampaign, setSelectActiveCampaign] = useState(true);
  const [isRunning, setIsRunning] = useState(true);
  const [filteredMonthlyDonationsTotal, setFilteredMonthlyDonationsTotal] =
    useState(0);
  const [
    filteredMonthlyDonationsQuantity,
    setFilteredMonthlyDonationsQuantity,
  ] = useState(0);

  const selectedDateRef = useRef(selectedDate);
  const testMode = useAppSelector((state) => state.testMode);

  const { data: campaignData, isSuccess: campaignDataIsSuccess } =
    useGetCampaignsQuery();

  useEffect(() => {
    if (campaignData?.length) {
      let tempPrevCampaignData;
      let smallestDiffFrToday;
      campaignData.forEach((campaign) => {
        const diff = moment(today).diff(moment(campaign.end));
        if (
          (diff > 0 && smallestDiffFrToday > diff) ||
          (diff > 0 && !smallestDiffFrToday)
        ) {
          smallestDiffFrToday = diff;
          tempPrevCampaignData = campaign;
        }
      });
      setPrevCampaignData(tempPrevCampaignData);
    }
  }, [campaignDataIsSuccess, campaignData, today]);

  function useInterval(callback, delay) {
    const savedCallback = useRef();

    // Remember the latest callback.
    useEffect(() => {
      savedCallback.current = callback;
    }, [callback]);

    // Set up the interval.
    useEffect(() => {
      function tick() {
        savedCallback.current();
      }
      if (delay !== null) {
        let id = setInterval(tick, delay);
        return () => clearInterval(id);
      }
    }, [delay]);
  }

  const fetchDailyShows = async () => {
    try {
      const showsResponse = await fetch(
        `https://api.kpfa.org/programs/?d=${selectedDateRef.current}`,
      );
      const showsJSON = await showsResponse.json();
      const localShowsForSelectedDate = await showsJSON.records;
      if (!isEqual(localShowsForSelectedDate, showsForSelectedDate)) {
        setShowsForSelectedDate(localShowsForSelectedDate);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const filterAbandonedDonations = useCallback(
    (records) => {
      if (showAbandonedDonations.current) {
        return records.filter((donation) => {
          if (donation.source === "WebSite") {
            if (donation.payments.length > 0) {
              return false;
            } else {
              return true;
            }
          } else {
            return false;
          }
        });
      } else {
        return records.filter((donation) => {
          if (donation.source === "WebSite") {
            if (donation.payments.length > 0) {
              return true;
            } else {
              return false;
            }
          } else {
            return true;
          }
        });
      }
    },
    [showAbandonedDonations],
  );

  const filterDonationsForSelectedDay = useCallback(() => {
    const donations = cloneDeep(donationsForSelectedDate.current);
    const filteredDonationsByCampaign = !isEqual(selectedCampaign.current, {
      value: "",
      label: "All Donations",
    })
      ? donations.filter(
          (donation) => donation.campaign_id === selectedCampaign.current.value,
        )
      : donations;
    const filteredDonationsByMatch = filterMatch.current
      ? filteredDonationsByCampaign.filter(
          (donation) => donation.donation_match,
        )
      : filteredDonationsByCampaign;
    const filteredDonationsForSelectedDate = filteredDonationsByMatch;
    const filteredDonationsTotal = filteredDonationsForSelectedDate.reduce(
      (total, donation) => {
        if (donation.installment === "Monthly") {
          return currency(total).add(currency(donation.amount).multiply(12))
            .value;
        } else {
          return currency(total).add(currency(donation.amount)).value;
        }
      },
      0,
    );

    const filteredMonthlyDonations = filteredDonationsForSelectedDate.filter(
      (donation) => donation.installment === "Monthly",
    );
    const filteredMonthlyDonationsTotal = filteredMonthlyDonations.reduce(
      (total, donation) => total + donation.amount,
      0,
    );
    const filterDonationsLength = filteredDonationsForSelectedDate.length;
    setFilteredDonationsForSelectedDate(filteredDonationsForSelectedDate);
    setFilteredDonationsTotal(filteredDonationsTotal);
    setFilterDonationsLength(filterDonationsLength);
    setFilteredMonthlyDonationsTotal(filteredMonthlyDonationsTotal);
    setFilteredMonthlyDonationsQuantity(filteredMonthlyDonations.length);
  }, [donationsForSelectedDate, filterMatch, selectedCampaign]);

  const fetchDailyDonations = useCallback(() => {
    function updateCampaignSelectOptions(donationCampaignIDs) {
      const clonedCampaignData = cloneDeep(campaignData);
      const campaignDataFiltered = clonedCampaignData.filter((campaign) =>
        donationCampaignIDs.includes(campaign.id),
      );

      const sortedCampaignData = sortBy(
        campaignDataFiltered,
        (campaign) => new Date(campaign.start),
      ).reverse();

      const campaigns = [defaultCampaignSelectOption];
      let selectedCampaign = defaultCampaignSelectOption;
      sortedCampaignData.forEach((campaign) => {
        const label = `${campaign.name} (${campaign.type}) - From ${moment(
          campaign.start,
        ).format("MM/DD/YYYY")} To ${moment(campaign.end).format(
          "MM/DD/YYYY",
        )}`;
        const value = campaign.id;
        if (
          moment(today).isSame(moment(selectedDateRef.current)) &&
          moment(selectedDateRef).isBetween(campaign.start, campaign.end)
        ) {
          selectedCampaign = { value, label };
        }

        campaigns.push({ value, label });
      });
      if (selectActiveCampaign) {
        setCampaigns(campaigns);
        setSelectActiveCampaign(false);
        setSelectedCampaign(selectedCampaign);
        filterDonationsForSelectedDay();
      } else {
        setCampaigns(campaigns);
      }
    }

    setIsLoading(true);
    const date = selectedDateRef.current;
    const headers = {
      Authorization: `Bearer ${localStorage.getItem("jwt")}`,
      "Content-Type": "application/json",
      Accept: "application/json",
    };
    // debugger;
    fetch(
      `https://${
        testMode.value ? "api.staging." : "api."
      }kpfa.org/donations/?start=${date}&end=${date}`,
      {
        headers,
        method: "GET",
      },
    )
      .then((response) => response.json())
      .then((response) => {
        const campaignIDsForDonations = [
          ...new Set(
            response.records
              .map((donation) => donation.campaign_id)
              .filter((campaignID) => campaignID !== 0),
          ),
        ];
        // if setShowAbandonedDonations is true, show only the abandoned donations. Otherwise, show only non-abandoned donations
        setDonationsForSelectedDate(filterAbandonedDonations(response.records));
        setIsLoading(false);
        updateCampaignSelectOptions(campaignIDsForDonations);
        filterDonationsForSelectedDay();
      })
      .catch((error) => {
        setIsLoading(false);
        console.log(error);
      });
  }, [
    setIsLoading,
    selectedDateRef,
    setDonationsForSelectedDate,
    filterDonationsForSelectedDay,
    campaignData,
    selectActiveCampaign,
    setSelectedCampaign,
    today,
    testMode,
    filterAbandonedDonations,
  ]);

  const fetchCurrentMarathonStats = async () => {
    let date = selectedDateRef.current;

    try {
      const currentMarathonStatsResponse = await fetch(
        `https://${
          testMode.value ? "api.staging." : "api."
        }kpfa.org/stats/?d=${date}`,
      );
      const localCurrentStats = await currentMarathonStatsResponse.json();
      const { calls: numberOfCallers } = localCurrentStats;
      const currentMarathonStats = localCurrentStats.campaigns
        ? localCurrentStats.campaigns[0]
        : "";

      if (!isEqual(localCurrentStats, currentStats)) {
        setCurrentStats(localCurrentStats);
        setCurrentMarathonStats(currentMarathonStats);
        setNumberOfCallers(numberOfCallers);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updateSelectedDate = (event) => {
    const localSelectedDate = event.target.value;
    if (localSelectedDate) {
      if (localSelectedDate === today) {
        setIsRunning(true);
      } else {
        setIsRunning(false);
      }
      selectedDateRef.current = localSelectedDate;
      setSelectedDate(localSelectedDate);
      setSelectActiveCampaign(true);
      setSelectedCampaign(defaultCampaignSelectOption);

      if (
        moment(localSelectedDate).isAfter(dateMin) &&
        !moment(localSelectedDate).isAfter(moment())
      ) {
        fetchDailyShows();
        fetchDailyDonations();
        fetchCurrentMarathonStats();
      } else {
        setDonationsForSelectedDate([]);
      }
    } else {
      selectedDateRef.current = localSelectedDate;
      setSelectedDate(localSelectedDate);
      setDonationsForSelectedDate([]);
    }
  };

  const updateFilterMatch = () => {
    setFilterMatch(!filterMatch.current);
    filterDonationsForSelectedDay();
  };

  const updateFilterAbandoned = () => {
    setShowAbandonedDonations(!showAbandonedDonations.current);
    fetchDailyDonations();
  };

  const updateSelectedCampaign = (selection) => {
    setSelectedCampaign(selection);
    filterDonationsForSelectedDay();
  };

  const updateSelectedDonationGrouping = (event) => {
    setSelectedDonationGrouping(event.target.id);
  };

  const toggleStatsExpand = () => {
    setStatsExpanded(!statsExpanded);
  };

  useInterval(
    () => {
      fetchDailyDonations();
      fetchCurrentMarathonStats();
    },
    isRunning ? 15000 : null,
  );

  useEffect(() => {
    fetchCurrentMarathonStats();
    fetchDailyShows();
    //eslint-disable-next-line
  }, []);

  useEffect(() => {
    if (campaignData?.length) {
      fetchDailyDonations();
    }
    //eslint-disable-next-line
  }, [campaignData]);

  return (
    <div className="animated fadeIn">
      <div className="card">
        <div className="card-body">
          <div className="row">
            <div className="col-sm-12">
              <StreamersAndCallers numberOfCallers={numberOfCallers} />
            </div>
          </div>
          <CCollapse visible={statsExpanded}>
            <DonationStats
              selectedDate={selectedDate}
              today={today}
              currentMarathonStats={currentMarathonStats}
              filteredDonationsTotal={filteredDonationsTotal}
              filterDonationsLength={filterDonationsLength}
              PrevCampaignData={PrevCampaignData}
              isLoading={isLoading}
              filteredMonthlyDonationsTotal={filteredMonthlyDonationsTotal}
              filteredMonthlyDonationsQuantity={
                filteredMonthlyDonationsQuantity
              }
            />
          </CCollapse>
          <CRow>
            <CButton
              className="mx-auto w-auto my-4"
              color="primary"
              size="sm"
              outline="true"
              onClick={toggleStatsExpand}
            >
              {statsExpanded ? "▲ Collapse Stats ▲" : "▼ Expand Stats ▼"}
            </CButton>
          </CRow>
          <DonationTimeline
            updateSelectedDate={updateSelectedDate}
            selectedDate={selectedDate}
            selectedDonationGrouping={selectedDonationGrouping}
            updateSelectedDonationGrouping={updateSelectedDonationGrouping}
            showsForSelectedDate={showsForSelectedDate}
            filteredDonationsForSelectedDate={filteredDonationsForSelectedDate}
            campaigns={campaigns}
            selectedCampaign={selectedCampaign.current}
            updateSelectedCampaign={updateSelectedCampaign}
            filterMatch={filterMatch}
            updateFilterMatch={updateFilterMatch}
            isLoading={isLoading}
            updateFilterAbandoned={updateFilterAbandoned}
            showAbandonedDonations={showAbandonedDonations}
          />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
