import ReactSelect from "react-select";
import moment from "moment";
import { useAppSelector } from "../../../hooks";

const DateAndFilterControls = ({
  updateSelectedDate,
  selectedDate,
  campaigns,
  updateSelectedCampaign,
  selectedCampaign,
  filterMatch,
  updateFilterMatch,
  selectedDonationGrouping,
  updateSelectedDonationGrouping,
  isLoading,
  showAbandonedDonations,
  updateFilterAbandoned,
}) => {
  const accessLevel = useAppSelector((state) => state.user.accessLevel);

  return (
    <div className="row mb-5">
      <div className="col-sm-12 col-md-3 form-group">
        <label htmlFor="date">Donations for the Selected Date</label>
        <input
          className="form-control me-2"
          id="date-picker"
          type="date"
          name="date"
          onChange={updateSelectedDate}
          value={selectedDate}
          min="1990-01-01"
          max={moment().format("YYYY-MM-DD")}
        />
      </div>
      <div className="col-sm-12 col-md-3 form-group">
        <label htmlFor="date">Filter Donations by Campaign</label>
        {campaigns.length > 0 ? (
          <ReactSelect
            options={campaigns}
            onChange={updateSelectedCampaign}
            value={selectedCampaign}
          />
        ) : null}
      </div>
      <div className="col-sm-12 col-md-2 form-group pt-4">
        <div className="form-check mt-2">
          <input
            className="form-check-input"
            type="checkbox"
            value={filterMatch}
            id="filter-match"
            onChange={updateFilterMatch}
          />
          <label className="form-check-label" htmlFor="filter-match">
            Show Only Matches
          </label>
        </div>
      </div>
      {accessLevel === "Admin" ? (
        <div className="col-sm-12 col-md-2 form-group pt-4">
          <div className="form-check mt-2">
            <input
              className="form-check-input"
              type="checkbox"
              value={showAbandonedDonations}
              id="abandonedDonations"
              onChange={updateFilterAbandoned}
            />
            <label className="form-check-label" htmlFor="abandonedDonations">
              Show Abandoned
            </label>
          </div>
        </div>
      ) : null}
      <div className="col-sm-12 col-md-1 form-group pt-4">
        {isLoading ? (
          <div className="animated fadeIn d-flex flex-row align-items-center justify-content-start">
            <div className="spinner-border text-info" role="status">
              <span className="sr-only">Loading...</span>
            </div>
          </div>
        ) : null}
      </div>
      <div
        className={`col-sm-12 col-md-3 form-group d-flex flex-column align-items-start ${
          accessLevel !== "Admin" ?? "mt-4"
        }`}
      >
        <span className="me-2" id="group-donations">
          Group Donations by
        </span>
        <div
          className="btn-group me-2"
          role="group"
          aria-label="Group donations by"
        >
          <button
            type="button"
            id="show"
            className={
              selectedDonationGrouping === "show"
                ? "btn btn-primary"
                : "btn btn-secondary"
            }
            onClick={updateSelectedDonationGrouping}
          >
            Show
          </button>
          <button
            type="button"
            id="hour"
            className={
              selectedDonationGrouping === "hour"
                ? "btn btn-primary"
                : "btn btn-secondary"
            }
            onClick={updateSelectedDonationGrouping}
          >
            Hour
          </button>
        </div>
      </div>
    </div>
  );
};

export default DateAndFilterControls;
