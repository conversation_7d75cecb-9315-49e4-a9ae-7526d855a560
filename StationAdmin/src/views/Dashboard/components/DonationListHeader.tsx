import currency from "currency.js";
import { Donation, Show } from "../../../types";
import { formatCurrency } from "../../../utils/currencyUtils";

type Props = {
  donationGroup: Donation[];
  show: Show | null;
  index: number;
};

const DonationListHeader = ({ donationGroup, show, index }: Props) => {
  const total = donationGroup.reduce((total, currentDonation) => {
    if (currentDonation.installment === "Monthly") {
      return currency(total).add(currency(currentDonation.amount).multiply(12))
        .value;
    } else {
      return currency(total).add(currency(currentDonation.amount)).value;
    }
  }, 0);
  let donationGroupingColumnHeader = "";

  const formatTime = (date: string) => {
    if (!date) {
      console.error("Date is undefined or empty.");
      return "Invalid Date";
    }

    // If the input is just a time string
    if (date.match(/^\d{2}:\d{2}:\d{2}$/)) {
      const [hour, ,] = date.split(":");
      const formattedHour =
        parseInt(hour) > 12 ? parseInt(hour) - 12 : parseInt(hour);
      const period = parseInt(hour) >= 12 ? "PM" : "AM";
      return `${formattedHour}${period}`;
    }

    const parsedDate = new Date(date);

    if (isNaN(parsedDate.getTime())) {
      console.error(`Invalid date format: ${date}`);
      return "Invalid Date";
    }

    const formattedTime = parsedDate.toLocaleString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });

    return formattedTime;
  };

  function adjustTime(dateString: string) {
    const date = new Date(dateString);
    date.setMinutes(date.getMinutes());
    return date.toISOString();
  }

  if (!show) {
    const indexString = (23 - index).toString().padStart(2, "0");
    const currentHour = formatTime(`${indexString}:00:00`);
    donationGroupingColumnHeader = currentHour;
  } else if (show) {
    const adjustedStartString = adjustTime(show.startsMoment); // No offset for display
    const adjustedEndString = adjustTime(show.endsMoment);

    const adjustedStartTime = formatTime(adjustedStartString);
    const adjustedEndTime = formatTime(adjustedEndString);

    const showName = `${show.name} (${adjustedStartTime} - ${adjustedEndTime})`;
    donationGroupingColumnHeader = showName;
  }

  const sustainingDonations = donationGroup.filter(
    (donation) => donation.installment === "Monthly"
  );

  return (
    <>
      <div className="row bg-dark text-white pt-2 pb-2 font-weight-bold">
        <div className="col-sm-4">
          <h5>{donationGroupingColumnHeader}</h5>
        </div>
        <div className="col-sm-2">
          <h5>
            <strong>Total: {formatCurrency(total)}</strong>
          </h5>
        </div>
        <div className="col-sm-2 text-right">
          <h5>Donors: {donationGroup.length}</h5>
        </div>
        <div className="col-sm-2 text-right">
          <h5>
            Sustaining:{" "}
            {formatCurrency(
              sustainingDonations.reduce(
                (accumulator, currentDonation) =>
                  accumulator + currentDonation.amount,
                0
              )
            )}
          </h5>
        </div>
        <div className="col-sm-2 text-right">
          <h5>Sustainers: {sustainingDonations.length}</h5>
        </div>
      </div>
      <div className="row bg-light pt-1 pb-1 font-weight-bold mb-2 d-none d-md-flex">
        <div className="col-sm-1">Match</div>
        <div className="col-sm-2">Name</div>
        <div className="col-sm-1">Location</div>
        <div className="col-sm-1">Source</div>
        <div className="col-sm-1">Donation</div>
        <div className="col-sm-2">Premium</div>
        <div className="col-sm-3">Comment</div>
        <div className="col-sm-1">Time</div>
      </div>
    </>
  );
};

export default DonationListHeader;
