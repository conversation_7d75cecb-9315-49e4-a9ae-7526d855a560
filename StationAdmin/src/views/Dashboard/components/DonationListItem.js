import moment from "moment";
import { startCase, toLower } from "lodash";
import { Link } from "react-router-dom";
import { CBadge } from "@coreui/react";
import { useAppSelector } from "../../../hooks";
import { formatCurrency } from "../../../utils/currencyUtils";

const DonationListItem = ({
  donation: {
    donation_match,
    firstname,
    lastname,
    city,
    state,
    source,
    amount,
    installment,
    premiums,
    comments,
    timestamp,
    donor_id,
    read_onair,
  },
  index,
}) => {
  const accessLevel = useAppSelector((state) => state.user.accessLevel);
  const time = moment(timestamp).format("h:mm a");
  const premiumsList = function () {
    if (premiums.length > 0) {
      const list = premiums.reduce((list, currentPremium) => {
        return list + `${currentPremium.name} \n`;
      }, "");
      return list;
    } else {
      return "";
    }
  };
  return (
    <>
      <div
        className={index % 2 === 0 ? "row pt-2 pb-3" : "row bg-light pt-2 pb-3"}
      >
        <div className="col-md-1">
          {" "}
          <span className="d-inline-block d-md-none font-weight-bold me-2 mb-2">
            Match:
          </span>{" "}
          {donation_match ? "Y" : "N"}
        </div>
        <div className="col-md-2">
          {" "}
          <span className="d-inline-block d-md-none font-weight-bold me-2 mb-2">
            Name:
          </span>{" "}
          <strong>
            {accessLevel === "Admin" ? (
              <Link
                to={{
                  pathname: "/donors",
                }}
                state={{ linkedDonorID: donor_id }}
              >
                {startCase(toLower(`${firstname} ${lastname}`))}
              </Link>
            ) : read_onair === true ? (
              startCase(toLower(firstname))
            ) : (
              "Anonymous"
            )}
          </strong>
        </div>
        <div className="col-md-1">
          <span className="d-inline-block d-md-none font-weight-bold me-2 mb-2">
            Location:
          </span>{" "}
          {startCase(toLower(city))}, {state}
        </div>
        <div className="col-md-1">
          <span className="d-inline-block d-md-none font-weight-bold me-2 mb-2">
            Source:
          </span>{" "}
          {source}
        </div>
        <div className="col-md-1 d-flex flex-column">
          <span className="d-inline-block d-md-none font-weight-bold me-2">
            Amount:
          </span>{" "}
          <span>{formatCurrency(amount)}</span>{" "}
          <CBadge
            color={installment === "Monthly" ? "info" : "dark"}
            className="mt-2"
          >
            {" "}
            {`${installment}`}
          </CBadge>
        </div>
        <div className="col-md-2">
          <span className="d-inline-block d-md-none font-weight-bold me-2 mb-2">
            Premiums:
          </span>{" "}
          {premiumsList()}
        </div>
        <div className="col-md-3">
          <span className="d-inline-block d-md-none font-weight-bold me-2">
            Comment:
          </span>{" "}
          {comments}
        </div>
        <div className="col-md-1">
          <span className="d-inline-block d-md-none font-weight-bold me-2 mb-2 mt-2">
            Time:
          </span>{" "}
          {time}
        </div>
      </div>
    </>
  );
};

export default DonationListItem;
