import React from "react";

import MarathonStats from "./MarathonStats";
import DonationsSelectedDay from "./DonationsSelectedDay";
import DonationTotalsForDay from "./DonationTotalsForDay";
import PreviousMarathonStats from "./PreviousMarathonStats";
import DonationsBySustainers from "./DonationsBySustainers";

const DonationStats = ({
  selectedDate,
  today,
  currentMarathonStats,
  filteredDonationsTotal,
  filterDonationsLength,
  PrevCampaignData,
  isLoading,
  filteredMonthlyDonationsTotal,
  filteredMonthlyDonationsQuantity,
}) => {
  if (selectedDate === today) {
    if (
      currentMarathonStats.hasOwnProperty("id") &&
      currentMarathonStats.id !== 0
    ) {
      return (
        <div className="row">
          <div className="col-md-5">
            <DonationsSelectedDay currentMarathonStats={currentMarathonStats} />
          </div>
          <div className="col-md-2">
            <DonationsBySustainers
              filteredMonthlyDonationsTotal={filteredMonthlyDonationsTotal}
              filteredMonthlyDonationsQuantity={
                filteredMonthlyDonationsQuantity
              }
              sustainerCount={currentMarathonStats.sustainer_count}
              sustainerTotal={currentMarathonStats.sustainer_total}
            />
          </div>
          <div className="col-md-5">
            <MarathonStats currentMarathonStats={currentMarathonStats} />
          </div>
        </div>
      );
    }
    return (
      <div className="row">
        <div className="col-sm-6">
          <DonationTotalsForDay
            filteredDonationsTotal={filteredDonationsTotal}
            filterDonationsLength={filterDonationsLength}
            isLoading={isLoading}
            textLeft
            filteredMonthlyDonationsTotal={filteredMonthlyDonationsTotal}
            filteredMonthlyDonationsQuantity={filteredMonthlyDonationsQuantity}
          />
        </div>
        <div className="col-sm-6">
          <PreviousMarathonStats PrevCampaignData={PrevCampaignData} />
        </div>
      </div>
    );
  } else {
    return (
      <div className="row">
        <div className="col-sm-12">
          <DonationTotalsForDay
            filteredDonationsTotal={filteredDonationsTotal}
            filterDonationsLength={filterDonationsLength}
            isLoading={isLoading}
            filteredMonthlyDonationsTotal={filteredMonthlyDonationsTotal}
            filteredMonthlyDonationsQuantity={filteredMonthlyDonationsQuantity}
          />
        </div>
      </div>
    );
  }
};

export default DonationStats;
