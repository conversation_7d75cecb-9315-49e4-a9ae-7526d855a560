import { cloneDeep } from "lodash";
import moment from "moment";

import DonationTimelineGroup from "./DonationTimelineGroup";
import DateAndFilterControls from "./DateAndFilterControls";

const DonationTimeline = ({
  isLoading,
  selectedDate,
  updateSelectedDate,
  selectedDonationGrouping,
  updateSelectedDonationGrouping,
  showsForSelectedDate,
  filteredDonationsForSelectedDate,
  campaigns,
  selectedCampaign,
  updateSelectedCampaign,
  filterMatch,
  updateFilterMatch,
  showAbandonedDonations,
  updateFilterAbandoned,
}) => {
  if (filteredDonationsForSelectedDate.length > 0) {
    const donationData = cloneDeep(filteredDonationsForSelectedDate);
    //   const donationData = clonedDonationData.sort((a, b) => {
    //     return moment(a.timestamp).valueOf() - moment(b.timestamp).valueOf();
    //   });
    let donationsArray = [];
    if (selectedDonationGrouping === "hour") {
      const hours = [];

      // 24 items array to represent 24 hours
      for (let i = 0; i < 24; i++) {
        hours.push([]);
      }
      donationData.forEach((donation) => {
        const donationHour = Number(moment(donation.timestamp).format("HH"));

        hours[donationHour].push(donation);
      });
      donationsArray = [...hours];
    } else if (selectedDonationGrouping === "show") {
      const showsCloned = cloneDeep(showsForSelectedDate);
      showsCloned.forEach((show) => {
        const currentDate = new Date(selectedDate);
        const days = [
          "Sunday",
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
        ];
        const currentDay = days[currentDate.getUTCDay()];
        const dayData = show.days?.find((day) => day.day === currentDay);
        if (dayData) {
          const startTimeFormatted = `${dayData.start.slice(
            0,
            2,
          )}:${dayData.start.slice(2, 4)}:00`;

          let startHour = parseInt(dayData.start.slice(0, 2));
          let startMinute = parseInt(dayData.start.slice(2, 4));
          let totalMinutes = startMinute + parseInt(dayData.length);

          let endHour = startHour + Math.floor(totalMinutes / 60);
          let endMinute = totalMinutes % 60;

          if (endHour >= 24) {
            endHour -= 24;
          }

          const endTimeFormatted = `${String(endHour).padStart(
            2,
            "0",
          )}:${String(endMinute).padStart(2, "0")}:00`;

          show.startsMoment = moment(
            `${selectedDate} ${startTimeFormatted}`,
            "YYYY-MM-DD HH:mm:ss",
          );
          show.endsMoment = moment(
            `${selectedDate} ${endTimeFormatted}`,
            "YYYY-MM-DD HH:mm:ss",
          );
          show.donations = [];
        }
      });
      const showsSortedByStart = showsCloned.sort((a, b) => {
        return (
          (a.startsMoment ? a.startsMoment.valueOf() : 0) -
          (b.startsMoment ? b.startsMoment.valueOf() : 0)
        );
      });

      donationData.forEach((donation) => {
        const { timestamp } = donation;
        const timestampMoment = moment(timestamp);

        for (const show of showsSortedByStart) {
          let { startsMoment, endsMoment, donations } = show;

          // Calculate the donation window start and end times

          const donationWindowStart = startsMoment?.clone().add(11, "minutes");
          const donationWindowEnd = endsMoment?.clone().add(11, "minutes");

          // Check if the donation time falls within the donation window
          if (
            timestampMoment.isBetween(
              donationWindowStart,
              donationWindowEnd,
              undefined,
              "[)",
            )
          ) {
            donations.push(donation);
            break;
          }
        }
      });

      donationsArray = [...showsSortedByStart];
    }

    donationsArray.reverse();

    var donationsTable = donationsArray.map((donationsArrayItem, index) => {
      let donationGroup = [];

      if (selectedDonationGrouping === "hour" && donationsArrayItem) {
        donationGroup = [...donationsArrayItem];
      } else if (
        selectedDonationGrouping === "show" &&
        donationsArrayItem &&
        donationsArrayItem.donations
      ) {
        donationGroup = [...donationsArrayItem.donations];
      }

      if (donationGroup.length > 0) {
        return (
          <DonationTimelineGroup
            key={index + "hour"}
            donationGroup={donationGroup}
            show={
              selectedDonationGrouping === "show" ? donationsArrayItem : null
            }
            index={index}
          />
        );
      }
      return null;
    });
  }

  return (
    <>
      <DateAndFilterControls
        updateSelectedDate={updateSelectedDate}
        selectedDate={selectedDate}
        campaigns={campaigns}
        updateSelectedCampaign={updateSelectedCampaign}
        selectedCampaign={selectedCampaign}
        filterMatch={filterMatch}
        updateFilterMatch={updateFilterMatch}
        selectedDonationGrouping={selectedDonationGrouping}
        updateSelectedDonationGrouping={updateSelectedDonationGrouping}
        isLoading={isLoading}
        showAbandonedDonations={showAbandonedDonations}
        updateFilterAbandoned={updateFilterAbandoned}
      />
      {donationsTable ? (
        <div className="animated fadeIn">{donationsTable}</div>
      ) : null}
      {!isLoading && !donationsTable ? (
        <h5 className="animated fadeIn mt-5 mb-5 text-center">
          No donations found for the selected date.
        </h5>
      ) : null}
      {moment(selectedDate).isAfter(moment()) ? (
        <div className="alert alert-warning" role="alert">
          Invalid Date Selected.
        </div>
      ) : null}
    </>
  );
};

export default DonationTimeline;
