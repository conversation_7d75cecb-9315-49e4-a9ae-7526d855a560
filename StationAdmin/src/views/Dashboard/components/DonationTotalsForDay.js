import { useEffect } from "react";
import { useGetActiveSubscribersQuery } from "../../../api/stripeWorker";
import { useAppSelector } from "../../../hooks";
import { formatCurrency } from "../../../utils/currencyUtils";

const DonationTotalsForDay = ({
  filteredDonationsTotal,
  filterDonationsLength,
  isLoading,
  textLeft,
  filteredMonthlyDonationsQuantity,
  filteredMonthlyDonationsTotal,
}) => {
  const accessLevel = useAppSelector((state) => state.user.accessLevel);
  // Using the skip option to conditionally fetch data based on access level
  const { data: subscribersData, refetch } = useGetActiveSubscribersQuery(
    undefined,
    {
      skip: accessLevel !== "Admin",
    },
  );

  useEffect(() => {
    if (accessLevel === "Admin") {
      refetch();
    }
  }, [filteredMonthlyDonationsQuantity, refetch, accessLevel]);

  if (!isLoading || filteredDonationsTotal) {
    return (
      <div
        className="card"
        style={textLeft ? { height: "calc(100% - 1.5em)" } : {}}
      >
        <div
          className={
            textLeft
              ? "card-body d-flex flex-column justify-content-center"
              : "card-body d-flex flex-column align-items-center justify-content-center"
          }
        >
          {!textLeft ? (
            <>
              <h3 className="mb-3">
                Total Donations for Day:{" "}
                <strong>{formatCurrency(filteredDonationsTotal)}</strong>{" "}
              </h3>
              <h4>Number of Donations : {filterDonationsLength} </h4>
              {filteredMonthlyDonationsQuantity > 0 ? (
                <h4>
                  {filteredMonthlyDonationsQuantity} Monthly Donation
                  {filteredMonthlyDonationsQuantity > 1 ? "s" : null}, totalling{" "}
                  {formatCurrency(filteredMonthlyDonationsTotal)}
                </h4>
              ) : null}
            </>
          ) : (
            <>
              <h4>
                Donations Total for Day:{" "}
                <strong>{formatCurrency(filteredDonationsTotal)}</strong>{" "}
              </h4>
              <h5>Number of Donations : {filterDonationsLength} </h5>
              {filteredMonthlyDonationsQuantity > 0 ? (
                <h5>
                  {filteredMonthlyDonationsQuantity} Monthly Donation
                  {filteredMonthlyDonationsQuantity > 1 ? "s" : null}, totalling{" "}
                  {formatCurrency(filteredMonthlyDonationsTotal)}
                </h5>
              ) : null}
            </>
          )}
          {subscribersData && accessLevel === "Admin" ? (
            <>
              <h5 class="mt-3">
                Total Sustainers:{" "}
                {subscribersData.totalActiveSubscribers.toLocaleString()}
              </h5>
              <h5>
                Total Monthly Sustaining Amount:{" "}
                {formatCurrency(subscribersData.totalMonthlyAmount)}
              </h5>
            </>
          ) : null}
        </div>
      </div>
    );
  }
  return (
    <div
      className="card d-flex align-items-center justify-content-center"
      style={{ height: "calc(100% - 1.5em)" }}
    >
      <div className="card-text d-flex justify-content-center pt-3 mb-3">
        <div className="spinner-border text-info" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    </div>
  );
};

export default DonationTotalsForDay;
