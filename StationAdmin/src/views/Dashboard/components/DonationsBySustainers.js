import { useEffect } from "react";
import { useGetActiveSubscribersQuery } from "../../../api/stripeWorker";
import { useAppSelector } from "../../../hooks";
import { formatCurrency } from "../../../utils/currencyUtils";

const DonationsBySustainers = ({
  filteredMonthlyDonationsTotal,
  filteredMonthlyDonationsQuantity,
  sustainerCount,
  sustainerTotal,
}) => {
  const accessLevel = useAppSelector((state) => state.user.accessLevel);
  // Using the skip option to conditionally fetch data based on access level
  const { data: subscribersData, refetch } = useGetActiveSubscribersQuery(
    undefined,
    {
      skip: accessLevel !== "Admin",
    },
  );

  useEffect(() => {
    if (accessLevel === "Admin") {
      refetch();
    }
  }, [filteredMonthlyDonationsQuantity, refetch, accessLevel]);

  return (
    <>
      <div className="card stats">
        <div className="card-body overflow-auto">
          <h4 className="card-title">Sustainers</h4>

          {!isNaN(filteredMonthlyDonationsTotal) ? (
            <div className="card-text">
              <strong>
                Today: {formatCurrency(filteredMonthlyDonationsTotal)}
              </strong>
              <h6>Count: {filteredMonthlyDonationsQuantity}</h6>
              <strong>Drive: {formatCurrency(sustainerTotal)}</strong>
              <h6>Count: {sustainerCount}</h6>
            </div>
          ) : (
            <div className="card-text d-flex justify-content-center pt-3em">
              <div className="spinner-border" role="status">
                <span className="sr-only">Loading...</span>
              </div>
            </div>
          )}
          {subscribersData && accessLevel === "Admin" ? (
            <>
              <strong>
                Total: {formatCurrency(subscribersData.totalMonthlyAmount)}
              </strong>
              <h6>
                Count: {subscribersData.totalActiveSubscribers.toLocaleString()}
              </h6>
            </>
          ) : null}
        </div>
      </div>
    </>
  );
};

export default DonationsBySustainers;
