import { CircularProgressbar } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";
import { formatCurrency } from "../../../utils/currencyUtils";

const DonationsSelectedDay = ({
  currentMarathonStats: { day_goal, day_total, day_togo, day_percent },
}) => {
  return (
    <>
      <div className="card stats">
        <div className="card-body">
          <h4 className="card-title">Today</h4>

          {!isNaN(day_goal) ? (
            <div className="card-text row">
              <div className="col-sm-7">
                <h5 className="mt-4">
                  <strong>Total: {formatCurrency(day_total)}</strong>
                </h5>
                <h5>Goal: {formatCurrency(day_goal)}</h5>
                <h5>Need: {formatCurrency(day_togo)}</h5>
              </div>
              <div className="col-sm-5" style={{ maxWidth: "10em" }}>
                <CircularProgressbar
                  value={day_percent}
                  text={`${parseInt(day_percent)}%`}
                />
              </div>
            </div>
          ) : (
            <div className="card-text d-flex justify-content-center pt-3em">
              <div className="spinner-border" role="status">
                <span className="sr-only">Loading...</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default DonationsSelectedDay;
