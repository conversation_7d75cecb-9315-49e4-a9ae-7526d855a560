import { CircularProgressbar } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";
import { formatCurrency } from "../../../utils/currencyUtils";

const MarathonStats = ({
  currentMarathonStats: { goal, pledged, percent, togo },
}) => {
  return (
    <>
      <div className="card stats">
        <div className="card-body">
          <h4 className="card-title">Drive</h4>
          <div className="card-text">
            {!isNaN(goal) ? (
              <div className="card-text row">
                <div className="col-sm-7">
                  <h5 className="mt-4">
                    <strong>Total: {formatCurrency(pledged)}</strong>
                  </h5>
                  <h5>Goal: {formatCurrency(goal)}</h5>
                  <h5>Need: {formatCurrency(togo)}</h5>
                </div>
                <div className="col-sm-5" style={{ maxWidth: "10em" }}>
                  <CircularProgressbar
                    value={percent}
                    //   style={{ width: "50%", margin: "0 auto", maxHeight: "2em" }}
                    text={`${parseInt(percent)}%`}
                  />
                </div>
              </div>
            ) : (
              <div className="card-text d-flex justify-content-center pt-3em">
                <div className="spinner-border" role="status">
                  <span className="sr-only">Loading...</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default MarathonStats;
