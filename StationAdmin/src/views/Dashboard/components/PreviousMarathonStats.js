import currency from "currency.js";
import { CircularProgressbar } from "react-circular-progressbar";

const PreviousMarathonStats = ({ PrevCampaignData }) => {
  if (PrevCampaignData) {
    return (
      <div className="card pb-4" style={{ height: "calc(100% - 1.5em)" }}>
        <div className="card-body d-flex flex-row justify-content-start align-items-center">
          <div className="row w-100">
            <div className="col-sm-6">
              <h4 className="mb-3">{PrevCampaignData.name} Campaign</h4>
              <h5>
                Total Pledged:{" "}
                <strong>
                  {currency(PrevCampaignData.pledged, {
                    precision: 0,
                  }).format()}
                </strong>{" "}
              </h5>
              <h5>
                Total Paid:{" "}
                <strong>
                  {currency(PrevCampaignData.paid, { precision: 0 }).format()}
                </strong>{" "}
              </h5>
              <h5>
                Goal:{" "}
                <strong>
                  {currency(PrevCampaignData.goal, { precision: 0 }).format()}
                </strong>{" "}
              </h5>
            </div>

            <div className="col-sm-6">
              <div style={{ maxWidth: "140px", margin: "0 auto" }}>
                <CircularProgressbar
                  value={PrevCampaignData.pledged_percent}
                  text={`${Math.floor(PrevCampaignData.pledged_percent)}%`}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div
      className="card d-flex align-items-center justify-content-center"
      style={{ height: "calc(100% - 1.5em)" }}
    >
      <div className="card-text d-flex justify-content-center pt-3 mb-3">
        <div className="spinner-border text-info" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    </div>
  );
};

export default PreviousMarathonStats;
