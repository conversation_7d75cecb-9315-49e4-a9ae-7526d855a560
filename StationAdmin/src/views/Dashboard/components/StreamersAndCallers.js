import { useGetTotalListenersQuery } from "../../../api/icecast";

const StreamersAndCallers = (props) => {
  // Destructure the number of callers from props
  const numberOfCallers = props.numberOfCallers
    ? props.numberOfCallers.callers
    : 0;

  // Use RTK Query hook to get the total stream listeners, with polling every 60 seconds
  const { data: totalListeners = 0 } = useGetTotalListenersQuery(undefined, {
    pollingInterval: 60000, // Polling every 1 minute (60,000 ms)
  });

  return (
    <div className="alert alert-info d-flex justify-content-between">
      <h4>Callers: {numberOfCallers}</h4>
      <h4>Streamers: {totalListeners}</h4>
    </div>
  );
};

export default StreamersAndCallers;
