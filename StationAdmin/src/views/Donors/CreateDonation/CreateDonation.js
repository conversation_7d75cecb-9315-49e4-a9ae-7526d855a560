import { useState, useEffect } from "react";
import { cloneDeep, sortBy } from "lodash";
import moment from "moment";

import DonationType from "./components/DonationType";
import AmountSelector from "./components/AmountSelector";
import ContactForm from "./components/ContactForm";
import PremiumDropdown from "./components/PremiumDropdown";
import SubmitCart from "./components/SubmitCart";
import Warnings from "./components/Warnings";
import {
  useGetCampaignsQuery,
  useGetPremiumsQuery,
} from "../../../api/kpfa.ts";

const CreateDonation = (props) => {
  const minAmount = "1";
  const maxAmount = "999999";
  const [amount, setAmount] = useState("");
  const [amountTouched, setAmountTouched] = useState(false);
  const [premiumsTotal, setPremiumsTotal] = useState(0);
  const [donationType, setDonationType] = useState("One-Time");
  const [formData, setFormData] = useState({});
  const [canSubmit, setCanSubmit] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [premiums, setPremiums] = useState([]);
  const [premiumDropdown, setPremiumDropdown] = useState([]);
  const [selectedPremiums, setSelectedPremiums] = useState([]);
  const [selectedPremiumsData, setSelectedPremiumsData] = useState([]);
  const [campaignIDs, setCampaignIDs] = useState([]);
  const [campaignLabels, setCampaignLabels] = useState([]);

  const { data: premiumData } = useGetPremiumsQuery();
  const { data: campaignData } = useGetCampaignsQuery();

  useEffect(() => {
    if (premiumData?.records) {
      setPremiums(premiumData.records);
    }
  }, [premiumData]);

  useEffect(() => {
    if (campaignData?.length) {
      const responseCopy = cloneDeep(campaignData);
      const sortedResponse = sortBy(
        responseCopy,
        (campaign) => new Date(campaign.start),
      ).reverse();
      const campaignIDs = sortedResponse.map((campaign) => campaign.id);
      const campaignLabels = sortedResponse.map(
        (campaign) =>
          `${campaign.name} (${campaign.type}) - From ${moment(
            campaign.start,
          ).format("MM/DD/YYYY")} To ${moment(campaign.end).format(
            "MM/DD/YYYY",
          )}`,
      );
      campaignIDs.unshift("");
      campaignLabels.unshift("No campaign selected");
      setCampaignIDs(campaignIDs);
      setCampaignLabels(campaignLabels);
    }
  }, [campaignData]);

  useEffect(() => {
    if (premiums.length > 0) {
      const clonedPremiums = cloneDeep(premiums);
      const dropdownItems = [];
      clonedPremiums.forEach((item) => {
        const { active, id, name, variants } = item;
        if (name && !variants && active) {
          dropdownItems.push({
            value: id,
            label: `${name}`,
          });
          return;
        }
        if (name && variants && active) {
          variants.variations.forEach((variant) => {
            dropdownItems.push({
              value: variant.id,
              label: `${name} (${variant.name})`,
            });
            return;
          });
          return;
        }
      });
      setPremiumDropdown(dropdownItems);
    }
  }, [premiums]);

  useEffect(() => {
    if (selectedPremiumsData.length > 0) {
      const clonedSelectedPremiumsData = cloneDeep(selectedPremiumsData);
      const mutatedPremiumsTotal = clonedSelectedPremiumsData.reduce(
        (total, item) => {
          const price = donationType === "One-Time" ? item.price : item.cog;
          return total + price;
        },
        0,
      );
      setPremiumsTotal(mutatedPremiumsTotal);
    } else {
      setPremiumsTotal(0);
    }
  }, [donationType, selectedPremiumsData]);

  const handleAddPremium = (selection) => {
    const clonedSelectedPremiums = cloneDeep(selectedPremiums);
    const clonedSelectedPremiumsData = cloneDeep(selectedPremiumsData);
    const allPremiums = cloneDeep(premiums);
    if (selection.value) {
      clonedSelectedPremiums.push(Number(selection.value));
      const addedPremiumData = allPremiums.filter((item) => {
        const selectionID = Number(selection.value);
        const itemID = Number(item.id);
        if (item.variants) {
          const itemWithVariations = item.variants.variations.filter(
            (variantItem) => {
              const variantItemID = Number(variantItem.id);
              return variantItemID === selectionID;
            },
          );
          if (itemWithVariations.length > 0) {
            return true;
          }
        }
        return itemID === selectionID;
      });
      clonedSelectedPremiumsData.push(addedPremiumData[0]);
      setSelectedPremiums(clonedSelectedPremiums);
      setSelectedPremiumsData(clonedSelectedPremiumsData);
    }
  };

  const handleRemovePremium = (event) => {
    let index = event.target.dataset.index;
    let clonedSelectedPremiums = cloneDeep(selectedPremiums);
    let clonedSelectedPremiumsData = cloneDeep(selectedPremiumsData);
    clonedSelectedPremiums.splice(index, 1);
    clonedSelectedPremiumsData.splice(index, 1);
    setSelectedPremiums(clonedSelectedPremiums);
    setSelectedPremiumsData(clonedSelectedPremiumsData);
  };

  const amountChange = (event) => {
    const eValue = event.target.value;
    if (!isNaN(Number(eValue))) {
      const amount = (() => {
        if (eValue[eValue.length - 1] === ".") {
          return Number(eValue) + ".";
        } else if (Math.floor(Number(eValue)) < Number(eValue)) {
          if (
            String(Number(eValue)).length - Number(eValue).toFixed(0).length ===
            2
          ) {
            return eValue;
          }
          return eValue.match(/^-?\d+(?:\.\d{0,2})?/)[0];
        } else if (
          Number(Number(eValue).toFixed(2)) === Number(eValue) &&
          String(Math.floor(Number(eValue))).length !== eValue.length
        ) {
          return eValue;
        }
        if (Number(eValue) < 1) {
          return 1;
        } else if (Number(eValue) > 999999) {
          return 999999;
        } else {
          return String(Number(eValue));
        }
      })();
      setAmount(amount);
    }
  };

  const amountBlur = (event) => {
    const eValue = event.target.value;
    if (eValue[eValue.length - 1] === ".") {
      setAmount(String(Number(eValue)));
      setAmountTouched(true);
      return;
    } else if (Math.floor(Number(eValue)) < Number(eValue)) {
      if (eValue.length - String(Math.floor(Number(eValue))).length === 2) {
        setAmount(eValue + "0");
        setAmountTouched(true);
        return;
      }
      return;
    } else if (Math.floor(Number(eValue)) === Number(eValue)) {
      setAmount(String(Number(eValue)));
      setAmountTouched(true);
      return;
    }
    setAmountTouched(true);
    return;
  };

  const donationTypeChange = (event) => {
    setDonationType(event.target.value);
  };

  const updateStateFormData = (values) => {
    setFormData(values);
    setCanSubmit(true);
    setShowModal(true);
  };

  const hideModal = () => {
    setShowModal(false);
  };

  const bindFormikMethods = (setFieldValue) => {
    //eslint-disable-next-line
    setFieldValue = setFieldValue;
  };

  const { selectedDonor } = props;

  return (
    <div className="animated fadeIn">
      <div className="card mt-4">
        <div className="card-body">
          <div className="row">
            <div className="col-12 mb-4 d-flex justify-content-between">
              <h2>
                <span className="font-weight-bold">New Donation</span>{" "}
                {props.selectedDonor
                  ? `• ${selectedDonor.firstname} ${selectedDonor.lastname}`
                  : ""}
              </h2>
            </div>
          </div>
          <div className="row">
            <div className="col-md-6">
              <AmountSelector
                amountChange={amountChange}
                amountBlur={amountBlur}
                amount={amount}
                donationType={donationType}
              />
              <DonationType
                donationTypeChange={donationTypeChange}
                donationType={donationType}
              />
              <ContactForm
                updateStateFormData={updateStateFormData}
                amount={amount}
                installment={donationType}
                minAmount={minAmount}
                maxAmount={maxAmount}
                campaignIDs={campaignIDs}
                campaignLabels={campaignLabels}
                bindFormikMethods={bindFormikMethods}
                selectedDonor={selectedDonor}
              />
              <SubmitCart
                toggleCreateDonation={props.toggleCreateDonation}
                formData={formData}
                amount={amount}
                donationType={donationType}
                premiums={premiums}
                selectedPremiums={selectedPremiums}
                selectedPremiumsData={selectedPremiumsData}
                canSubmit={canSubmit}
                showModal={showModal}
                hideModal={hideModal}
                premiumsTotal={premiumsTotal}
                minAmount={minAmount}
                maxAmount={maxAmount}
                selectedDonor={selectedDonor}
                setSelectedDonor={props.setSelectedDonor}
                campaignData={campaignData}
              />
            </div>
            <div className="col-md-6">
              <PremiumDropdown
                donationType={donationType}
                premiums={premiums}
                premiumDropdown={premiumDropdown}
                selectedPremiums={selectedPremiums}
                selectedPremiumsData={selectedPremiumsData}
                handleRemovePremium={handleRemovePremium}
                handleAddPremium={handleAddPremium}
                premiumsTotal={premiumsTotal}
              />
              <Warnings
                amount={amount}
                minAmount={minAmount}
                maxAmount={maxAmount}
                amountTouched={amountTouched}
                premiumsTotal={premiumsTotal}
                donationType={donationType}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateDonation;
