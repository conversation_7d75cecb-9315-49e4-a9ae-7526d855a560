import React from 'react';
import { render, screen, fireEvent, act, within } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import ContactForm from '../components/ContactForm';

// Create a mock store
const mockStore = configureStore([]);

describe('ContactForm', () => {
  let store;
  const mockUpdateStateFormData = jest.fn();
  const mockBindFormikMethods = jest.fn();

  beforeEach(() => {
    store = mockStore({
      campaignOptions: {
        value: [],
        activeCampaignID: null
      },
      user: {
        accessLevel: 'Admin'
      }
    });
  });

  const defaultProps = {
    installment: 'One-Time',
    amount: '100',
    updateStateFormData: mockUpdateStateFormData,
    bindFormikMethods: mockBindFormikMethods,
    selectedDonor: null
  };

  it('renders without crashing', async () => {
    await act(async () => {
      render(
        <Provider store={store}>
          <ContactForm {...defaultProps} />
        </Provider>
      );
    });
  });

  it('clears form when selectedDonor changes to null', async () => {
    let rerender;
    await act(async () => {
      const result = render(
        <Provider store={store}>
          <ContactForm {...defaultProps} />
        </Provider>
      );
      rerender = result.rerender;
    });

    // First render with a selected donor
    const donor = {
      id: 1,
      firstname: 'John',
      lastname: 'Doe',
      address1: '123 Main St',
      address2: '',
      city: 'Berkeley',
      state: 'CA',
      postal_code: '94710',
      country: 'US',
      phone: '************',
      email: '<EMAIL>'
    };

    await act(async () => {
      rerender(
        <Provider store={store}>
          <ContactForm {...defaultProps} selectedDonor={donor} />
        </Provider>
      );
    });

    // Verify donor information is populated
    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('123 Main St')).toBeInTheDocument();

    // Rerender with no selected donor
    await act(async () => {
      rerender(
        <Provider store={store}>
          <ContactForm {...defaultProps} selectedDonor={null} />
        </Provider>
      );
    });

    // Verify form fields are cleared
    expect(screen.queryByDisplayValue('John')).not.toBeInTheDocument();
    expect(screen.queryByDisplayValue('Doe')).not.toBeInTheDocument();
    expect(screen.queryByDisplayValue('123 Main St')).not.toBeInTheDocument();
  });

  it('updates form when selectedDonor changes', async () => {
    let rerender;
    await act(async () => {
      const result = render(
        <Provider store={store}>
          <ContactForm {...defaultProps} />
        </Provider>
      );
      rerender = result.rerender;
    });

    const donor1 = {
      id: 1,
      firstname: 'John',
      lastname: 'Doe',
      address1: '123 Main St',
      address2: '',
      city: 'Berkeley',
      state: 'CA',
      postal_code: '94710',
      country: 'US',
      phone: '************',
      email: '<EMAIL>'
    };

    const donor2 = {
      id: 2,
      firstname: 'Jane',
      lastname: 'Smith',
      address1: '456 Oak Ave',
      address2: '',
      city: 'Oakland',
      state: 'CA',
      postal_code: '94612',
      country: 'US',
      phone: '************',
      email: '<EMAIL>'
    };

    // First render with donor1
    await act(async () => {
      rerender(
        <Provider store={store}>
          <ContactForm {...defaultProps} selectedDonor={donor1} />
        </Provider>
      );
    });

    // Verify donor1 information is populated
    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('123 Main St')).toBeInTheDocument();

    // Rerender with donor2
    await act(async () => {
      rerender(
        <Provider store={store}>
          <ContactForm {...defaultProps} selectedDonor={donor2} />
        </Provider>
      );
    });

    // Verify donor2 information is populated and donor1 information is gone
    expect(screen.getByDisplayValue('Jane')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Smith')).toBeInTheDocument();
    expect(screen.getByDisplayValue('456 Oak Ave')).toBeInTheDocument();
    expect(screen.queryByDisplayValue('John')).not.toBeInTheDocument();
    expect(screen.queryByDisplayValue('Doe')).not.toBeInTheDocument();
    expect(screen.queryByDisplayValue('123 Main St')).not.toBeInTheDocument();
  });

  it('displays donor address as static text when selectedDonor is present', async () => {
    const donor = {
      id: 1,
      firstname: 'John',
      lastname: 'Doe',
      address1: '123 Main St',
      address2: 'Apt 4B',
      city: 'Berkeley',
      state: 'CA',
      postal_code: '94710',
      country: 'US',
      phone: '************',
      email: '<EMAIL>'
    };

    await act(async () => {
      render(
        <Provider store={store}>
          <ContactForm {...defaultProps} selectedDonor={donor} />
        </Provider>
      );
    });

    // Verify donor information is displayed as static text
    expect(screen.getByText(/John Doe/)).toBeInTheDocument();
    expect(screen.getByText(/123 Main St/)).toBeInTheDocument();
    expect(screen.getByText(/Apt 4B/)).toBeInTheDocument();
    expect(screen.getByText(/Berkeley, CA 94710/)).toBeInTheDocument();
    expect(screen.getByText(/US/)).toBeInTheDocument();
    expect(screen.getByText(/Phone: ************/)).toBeInTheDocument();
    expect(screen.getByText(/Email: <EMAIL>/)).toBeInTheDocument();

    // Verify billing address form is hidden
    const billingForm = screen.getByTestId('billing-form');
    expect(billingForm).toHaveClass('d-none');
  });

  it('allows entering shipping address when selectedDonor is present', async () => {
    const donor = {
      id: 1,
      firstname: 'John',
      lastname: 'Doe',
      address1: '123 Main St',
      city: 'Berkeley',
      state: 'CA',
      postal_code: '94710',
      country: 'US'
    };

    await act(async () => {
      render(
        <Provider store={store}>
          <ContactForm {...defaultProps} selectedDonor={donor} />
        </Provider>
      );
    });

    // Check the shipping address checkbox
    const shippingCheckbox = screen.getByLabelText(/Shipping Address is different from billing address/i);
    await act(async () => {
      fireEvent.click(shippingCheckbox);
    });

    // Verify shipping address fields are visible
    const shippingForm = screen.getByTestId('shipping-form');
    expect(shippingForm).not.toHaveClass('d-none');

    // Fill in shipping address
    await act(async () => {
      fireEvent.change(screen.getByRole('textbox', { name: /^Shipping First Name$/i }), { target: { value: 'Jane' } });
      fireEvent.change(screen.getByRole('textbox', { name: /^Shipping Last Name$/i }), { target: { value: 'Smith' } });
      fireEvent.change(screen.getByRole('textbox', { name: /^Shipping Address$/i }), { target: { value: '456 Oak Ave' } });
      fireEvent.change(screen.getByRole('textbox', { name: /^Shipping City$/i }), { target: { value: 'Oakland' } });
      
      // Handle state field as a textbox (based on the actual field type)
      const stateField = screen.getByRole('textbox', { name: /^Shipping State/i });
      fireEvent.change(stateField, { target: { value: 'CA' } });

      fireEvent.change(screen.getByRole('textbox', { name: /^Shipping Zip/i }), { target: { value: '94612' } });
      fireEvent.change(screen.getByRole('combobox', { name: /^Shipping Country$/i }), { target: { value: 'US' } });
    });

    // Verify the values were entered correctly
    expect(screen.getByDisplayValue('Jane')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Smith')).toBeInTheDocument();
    expect(screen.getByDisplayValue('456 Oak Ave')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Oakland')).toBeInTheDocument();
    expect(screen.getByDisplayValue('CA')).toBeInTheDocument();
    expect(screen.getByDisplayValue('94612')).toBeInTheDocument();
    expect(screen.getByDisplayValue('US')).toBeInTheDocument();
  });

  it('allows entering new donor information when no selectedDonor is present', async () => {
    await act(async () => {
      render(
        <Provider store={store}>
          <ContactForm {...defaultProps} />
        </Provider>
      );
    });

    // Verify billing form is visible
    const billingForm = screen.getByTestId('billing-form');
    expect(billingForm).not.toHaveClass('d-none');

    // Enter new donor information
    await act(async () => {
      fireEvent.change(screen.getByLabelText(/^First Name$/i), { target: { value: 'Jane' } });
      fireEvent.change(screen.getByLabelText(/^Last Name$/i), { target: { value: 'Smith' } });
      fireEvent.change(screen.getByLabelText(/^Address$/i), { target: { value: '456 Oak Ave' } });
      fireEvent.change(screen.getByLabelText(/^City$/i), { target: { value: 'Oakland' } });
      fireEvent.change(screen.getByLabelText(/^State$/i), { target: { value: 'CA' } });
      fireEvent.change(screen.getByLabelText(/^Zip$/i), { target: { value: '94612' } });
      fireEvent.change(screen.getByLabelText(/^Country$/i), { target: { value: 'US' } });
    });

    // Verify the values were entered correctly
    expect(screen.getByDisplayValue('Jane')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Smith')).toBeInTheDocument();
    expect(screen.getByDisplayValue('456 Oak Ave')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Oakland')).toBeInTheDocument();
    expect(screen.getByDisplayValue('CA')).toBeInTheDocument();
    expect(screen.getByDisplayValue('94612')).toBeInTheDocument();
    expect(screen.getByDisplayValue('US')).toBeInTheDocument();
  });
}); 
