import {
  CBadge,
  CCol,
  CFormInput,
  CFormLabel,
  CInputGroup,
  CInputGroupText,
  CRow,
} from "@coreui/react";
import React from "react";

import "../styles/amount-selector.css";

const AmountSelector = (props) => {
  return (
    <div className="mb-3">
      <h3 className="mb-3  ">Donation Amount &amp; Type</h3>
      <CRow>
        <CCol>
          <CFormLabel htmlFor="amount">Amount</CFormLabel>
          <CInputGroup>
            <CInputGroupText>$</CInputGroupText>
            <CFormInput
              type="text"
              className="form-control form-control-lg"
              id="amount"
              placeholder="Enter donation amount"
              aria-label="amount input"
              onChange={props.amountChange}
              onBlur={props.amountBlur}
              // onFocus={this.props.amountChange}
              value={props.amount}
            />
          </CInputGroup>
        </CCol>
        {props.donationType === "Monthly" ? (
          <CCol className="d-flex align-items-end">
            <CBadge color="primary" className="p-3">
              /month
            </CBadge>
          </CCol>
        ) : null}
      </CRow>
    </div>
  );
};

export default AmountSelector;
