import React from "react";
import { Field } from "formik";

import { months, ccYears } from "./Form/constants";
import Input from "./Form/Input";
import SelectField from "../../../Form/SelectField";

const CardPaymentForm = ({ setFieldValue }) => {
  return (
    <div data-private>
      <Field
        name="cardNumber"
        id="cardNumber"
        component={Input}
        mask={[
          /\d/,
          /\d/,
          /\d/,
          /\d/,
          " ",
          /\d/,
          /\d/,
          /\d/,
          /\d/,
          " ",
          /\d/,
          /\d/,
          /\d/,
          /\d/,
          " ",
          /\d/,
          /\d/,
          /\d/,
          /\d/
        ]}
        label="Card Number"
        placeholder="1234 5678 9012 3456"
        onChange={e => {
          setFieldValue("cardNumber", e.target.value.replace(/[^0-9.]+/g, ""));

          // handleChange(e);
        }}
      />
      <Field
        name="securityCode"
        component={Input}
        label="Security Code"
        placeholder="1234"
        mask={[/\d/, /\d/, /\d/, /\d/]}
        onChange={e => {
          setFieldValue(
            "securityCode",
            e.target.value.replace(/[^0-9.]+/g, "")
          );

          // handleChange(e);
        }}
      />
      <Field
        name="expirationMonth"
        component={SelectField}
        label="Expiration Month"
        options={months}
      />
      <Field
        name="expirationYear"
        component={SelectField}
        label="Expiration Year"
        options={ccYears}
      />
    </div>
  );
};

export default CardPaymentForm;
