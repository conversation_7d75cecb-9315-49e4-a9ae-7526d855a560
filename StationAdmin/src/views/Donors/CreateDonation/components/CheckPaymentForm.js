import { Field } from "formik";

import Input from "./Form/Input";
import {
  CHECK_NUMBER_LABEL,
  CHECK_DEPOSIT_DATE_LABEL,
  CHECK_STATUS_LABEL,
  CHECK_PROCESSOR_LABEL,
  CHECK_PROCESSOR_OPTIONS,
  CHECK_STATUS_OPTIONS,
} from "./Form/constants";
import SelectField from "../../../Form/SelectField";

const CheckPaymentForm = () => {
  return (
    <div data-private>
      <Field name="checkNumber" label={CHECK_NUMBER_LABEL} component={Input} />
      <Field
        name="checkDepositDate"
        label={CHECK_DEPOSIT_DATE_LABEL}
        component={Input}
        type="date"
      />
      <Field
        name="checkStatus"
        label={CHECK_STATUS_LABEL}
        component={SelectField}
        options={CHECK_STATUS_OPTIONS}
      />
      <Field
        name="checkProcessor"
        label={CHECK_PROCESSOR_LABEL}
        component={SelectField}
        options={CHECK_PROCESSOR_OPTIONS}
      />
    </div>
  );
};

export default CheckPaymentForm;
