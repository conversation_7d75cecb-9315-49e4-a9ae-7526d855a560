import { Formik, Form, Field } from "formik";
import Input from "./Form/Input";
import initialValues from "./Form/initialValues";
import Checkbox from "./Form/Checkbox";
import Textarea from "./Form/Textarea";
import RadioButton from "./Form/RadioButton";
import RadioButtonGroup from "./Form/RadioButtonGroup";
import {
  stateOptions,
  countryOptions,
  donationType,
} from "../../../Form/constants";
import supportFormSchema from "./Form/validationSchema";
import CheckPaymentForm from "./CheckPaymentForm";
import CardPaymentForm from "./CardPaymentForm";
import PhoneMask from "../../../Form/PhoneMask";
import SelectField from "../../../Form/SelectField";
import { useAppSelector } from "../../../../hooks";
import { useEffect, useRef, useState } from "react";
import { resetCardValues, resetCheckValues } from "./ContactFormUtils";
import CashPaymentForm from "./CashPaymentForm";

const ContactForm = (props) => {
  const campaignOptions = useAppSelector(
    (state) => state.campaignOptions.value,
  );
  const activeCampaignID = useAppSelector(
    (state) => state.campaignOptions.activeCampaignID,
  );
  const accessLevel = useAppSelector((state) => state.user.accessLevel);
  const [formSubmitAttempted, setFormSubmitAttempted] = useState(false);

  const { bindFormikMethods } = props;

  const ePaymentProcessorOptions = [
    { value: "PayPal", label: "PayPal" },
    { value: "DipJar", label: "DipJar" },
    { value: "Venmo", label: "Venmo" },
    { value: "ACH", label: "ACH" },
  ];

  // Create a clean copy of initialValues for each form instance
  const getInitialValues = () => {
    const values = { ...initialValues };
    
    if (props.selectedDonor) {
      values.id = props.selectedDonor.id;
      values.firstName = props.selectedDonor.firstname;
      values.lastName = props.selectedDonor.lastname;
      values.address1 = props.selectedDonor.address1;
      values.address2 = props.selectedDonor.address2;
      values.city = props.selectedDonor.city;
      values.state = props.selectedDonor.state;
      values.zip = props.selectedDonor.postal_code;
      values.country = props.selectedDonor.country;
      
      // Normalize E.164 formatted phone numbers
      if (props.selectedDonor.phone) {
        // If phone starts with "+1" or has 11 digits starting with 1, convert to 10 digits
        const phoneStr = props.selectedDonor.phone.toString();
        const phoneDigits = phoneStr.replace(/\D/g, '');
        
        // Handle E.164 format
        if (phoneDigits.length === 11 && phoneDigits.startsWith('1')) {
          values.phone = phoneDigits.substring(1); // Remove the '1' prefix
        } else {
          values.phone = props.selectedDonor.phone;
        }
      } else {
        values.phone = props.selectedDonor.phone;
      }
      
      values.email = props.selectedDonor.email;
    }
    
    // Ensure shipping fields are always properly initialized when checkbox is unchecked
    if (!values.showShippingAddress) {
      values.firstNameShipping = "";
      values.lastNameShipping = "";
      values.address1Shipping = "";
      values.address2Shipping = "";
      values.cityShipping = "";
      values.stateShipping = "";
      values.zipShipping = "";
      values.countryShipping = "";
    }

    if (activeCampaignID) {
      values.campaign = activeCampaignID;
    }

    return values;
  };

  // Ref for Formik instance
  const formikRef = useRef();

  // Trigger validation and update donationType when amount changes
  useEffect(() => {
    if (formikRef.current) {
      const { values, setFieldValue } = formikRef.current;

      if (!values.campaign) {
        // Check if no campaign is selected
        if (props.amount >= 1000 && values.donationType !== "Major") {
          setFieldValue("donationType", "Major");
        } else if (props.amount < 1000 && values.donationType !== "Minor") {
          setFieldValue("donationType", "Minor");
        }
      }

      // Revalidate the form after updating donationType
      formikRef.current.validateForm();
    }
  }, [props.amount]);

  // Reset shipping fields when showShippingAddress is toggled off
  useEffect(() => {
    if (formikRef.current) {
      const { values, setFieldValue } = formikRef.current;
      if (values && !values.showShippingAddress) {
        setFieldValue("firstNameShipping", "");
        setFieldValue("lastNameShipping", "");
        setFieldValue("address1Shipping", "");
        setFieldValue("address2Shipping", "");
        setFieldValue("cityShipping", "");
        setFieldValue("stateShipping", "");
        setFieldValue("zipShipping", "");
        setFieldValue("countryShipping", "");
        
        // Revalidate the form after clearing shipping fields
        formikRef.current.validateForm();
      }
    }
  }, [formikRef.current?.values?.showShippingAddress]);
  
  // Normalize phone number format before submission
  useEffect(() => {
    if (formikRef.current && props.selectedDonor) {
      const { values, setFieldValue } = formikRef.current;
      if (values && values.phone) {
        // If this is a US phone number
        if (values.country === "US") {
          const phoneStr = values.phone.toString();
          const phoneDigits = phoneStr.replace(/\D/g, '');
          
          // Handle E.164 format or number with country code
          if (phoneDigits.length === 11 && phoneDigits.startsWith('1')) {
            // Remove the '1' prefix to get a standard 10-digit US number
            setFieldValue("phone", phoneDigits.substring(1));
          }
        }
      }
    }
  }, [props.selectedDonor]);

  return (
    <>
      <Formik
        enableReinitialize={true}
        initialValues={getInitialValues()}
        validationSchema={supportFormSchema(props.installment, props.amount)}
        validateOnMount={true}
        onSubmit={(values, { setSubmitting }) => {
          // If shipping address is not shown, remove shipping fields from validation
          if (!values.showShippingAddress) {
            // Clear shipping fields before submitting
            values.firstNameShipping = "";
            values.lastNameShipping = "";
            values.address1Shipping = "";
            values.address2Shipping = "";
            values.cityShipping = "";
            values.stateShipping = "";
            values.zipShipping = "";
            values.countryShipping = "";
          }
          
          // Normalize US phone number with country code before submission
          if (values.phone && values.country === "US") {
            const phoneDigits = values.phone.toString().replace(/\D/g, '');
            if (phoneDigits.length === 11 && phoneDigits.startsWith('1')) {
              values.phone = phoneDigits.substring(1);
            }
          }
          
          props.updateStateFormData(values);
          setSubmitting(false);
        }}
        innerRef={formikRef}
      >
        {({
          values,
          handleChange,
          setFieldValue,
          resetForm,
          errors,
          isSubmitting,
          handleSubmit,
        }) => {
          bindFormikMethods(setFieldValue, resetForm);
          return (
            <Form className="needs-validation" onSubmit={(e) => {
              setFormSubmitAttempted(true);
              handleSubmit(e);
            }}>
              <Field type="hidden" name="amount" value={props.amount} />

              <h3 className="mb-3">Donation Details</h3>
              <Field
                name="campaign"
                label="Assign to Campaign"
                component={SelectField}
                options={campaignOptions}
              />
              {props.installment === "One-Time" && !values.campaign ? (
                <Field
                  name="donationType"
                  label="Donation Type"
                  component={SelectField}
                  options={donationType}
                  onChange={(e) => {
                    if (e === "In-Kind") {
                      setFieldValue("paymentType", "in-kind");
                    } else {
                      setFieldValue("paymentType", "card");
                    }
                    handleChange(e);
                  }}
                />
              ) : null}
              <h3 className="mb-3 mt-4">
                {values.showShippingAddress === false
                  ? "Billing & Shipping Address"
                  : "Billing Address"}
              </h3>
              <div className="mb-3">
                <Field
                  name="showShippingAddress"
                  label="Shipping Address is different from billing address"
                  component={Checkbox}
                  onChange={(e) => {
                    // Pass event to Formik's handleChange first
                    handleChange(e);
                    
                    // Then handle shipping fields
                    if (!e.target.checked) {
                      // Clear shipping fields when checkbox is unchecked
                      setFieldValue("firstNameShipping", "");
                      setFieldValue("lastNameShipping", "");
                      setFieldValue("address1Shipping", "");
                      setFieldValue("address2Shipping", "");
                      setFieldValue("cityShipping", "");
                      setFieldValue("stateShipping", "");
                      setFieldValue("zipShipping", "");
                      setFieldValue("countryShipping", "");
                      
                      // Force validation to run after clearing fields
                      setTimeout(() => {
                        formikRef.current.validateForm();
                      }, 0);
                    }
                  }}
                />
              </div>
              {props.selectedDonor ? (
                <>
                  <strong>
                    {props.selectedDonor.firstname}{" "}
                    {props.selectedDonor.lastname}
                  </strong>
                  <p>
                    {props.selectedDonor.address1} <br />
                    {props.selectedDonor.address2
                      ? props.selectedDonor.address2
                      : null}
                    {props.selectedDonor.address2 ? <br /> : null}
                    {props.selectedDonor.city}, {props.selectedDonor.state}{" "}
                    {props.selectedDonor.postal_code} <br />
                    {props.selectedDonor.country}
                  </p>
                  {props.selectedDonor.phone ? (
                    <p>
                      Phone: {props.selectedDonor.phone}
                      <br />
                      {props.selectedDonor.email
                        ? "Email: " + props.selectedDonor.email
                        : null}
                    </p>
                  ) : null}
                </>
              ) : null}
              <div className={props.selectedDonor ? "d-none" : null} data-testid="billing-form">
                <Field
                  name="firstName"
                  label="First Name"
                  component={Input}
                  placeholder="Jane"
                />
                <Field
                  name="lastName"
                  label="Last Name"
                  component={Input}
                  placeholder="Smith"
                />
                <Field
                  name="address1"
                  label="Address"
                  component={Input}
                  placeholder="123 Main St."
                />
                <Field
                  name="address2"
                  label="Address (continued)"
                  component={Input}
                  placeholder="Apartment 161"
                />
                <Field
                  name="city"
                  label="City"
                  component={Input}
                  placeholder="Berkeley"
                />
                {values.country === "US" ? (
                  <Field
                    name="state"
                    label="State"
                    component={SelectField}
                    options={stateOptions}
                  />
                ) : (
                  <Field
                    name="state"
                    label="State or Provence"
                    component={Input}
                    placeholder="Ontario"
                  />
                )}
                {values.country === "US" ? (
                  <Field
                    name="zip"
                    label="Zip"
                    component={Input}
                    placeholder="94710"
                    mask={(value) => {
                      const filteredValue = value.replace(/[^0-9.]+/g, "");
                      if (parseInt(filteredValue).toString().length <= 5) {
                        return [/\d/, /\d/, /\d/, /\d/, /\d/];
                      }
                      return [
                        /\d/,
                        /\d/,
                        /\d/,
                        /\d/,
                        /\d/,
                        "-",
                        /\d/,
                        /\d/,
                        /\d/,
                        /\d/,
                      ];
                    }}
                    guide={false}
                    onChange={(e) => {
                      setFieldValue(
                        "zip",
                        e.target.value.replace(/[^0-9.]+/g, ""),
                      );

                      // handleChange(e);
                    }}
                    value={values.zip}
                    // type="number"
                  />
                ) : (
                  <Field
                    name="zip"
                    label="Zip or Postal Code"
                    component={Input}
                    placeholder="94710"
                    // type="number"
                  />
                )}
                <Field
                  name="country"
                  label="Country"
                  component={SelectField}
                  options={countryOptions}
                  onChange={(e) => {
                    if (e.value !== "US") {
                      if (values.country === "US") {
                        setFieldValue("state", "");
                        setFieldValue("zip", "");
                      }
                    }
                    if (e.value === "US") {
                      if (values.country !== "US") {
                        setFieldValue("state", "CA");
                        setFieldValue("zip", "");
                      }
                    }

                    handleChange(e);
                  }}
                />
                <PhoneMask
                  country={values.country}
                  setFieldValue={setFieldValue}
                  component={Input}
                  value={values.phone}
                />
                <Field
                  type="email"
                  name="email"
                  label="Email"
                  component={Input}
                  placeholder="<EMAIL>"
                />
              </div>
              {values.showShippingAddress && (
                <div data-testid="shipping-form">
                  <h3 className="mb-3">Shipping Address</h3>
                  <Field
                    name="firstNameShipping"
                    label="Shipping First Name"
                    component={Input}
                    placeholder="Jane"
                  />
                  <Field
                    name="lastNameShipping"
                    label="Shipping Last Name"
                    component={Input}
                    placeholder="Smith"
                  />
                  <Field
                    name="address1Shipping"
                    label="Shipping Address"
                    component={Input}
                    placeholder="123 Main St."
                  />
                  <Field
                    name="address2Shipping"
                    label="Shipping Address (continued)"
                    component={Input}
                    placeholder="Apartment 161"
                  />
                  <Field
                    name="cityShipping"
                    label="Shipping City"
                    component={Input}
                    placeholder="Berkeley"
                  />
                  {values.countryShipping === "US" ? (
                    <Field
                      name="stateShipping"
                      label="Shipping State"
                      component={SelectField}
                      options={stateOptions}
                    />
                  ) : (
                    <Field
                      name="stateShipping"
                      label="Shipping State or Provence"
                      component={Input}
                      placeholder="Ontario"
                    />
                  )}
                  {values.countryShipping === "US" ? (
                    <Field
                      name="zipShipping"
                      label="Shipping Zip"
                      component={Input}
                      placeholder="94710"
                      mask={(value) => {
                        const filteredValue = value.replace(/[^0-9.]+/g, "");
                        if (parseInt(filteredValue).toString().length <= 5) {
                          return [/\d/, /\d/, /\d/, /\d/, /\d/];
                        }
                        return [
                          /\d/,
                          /\d/,
                          /\d/,
                          /\d/,
                          /\d/,
                          "-",
                          /\d/,
                          /\d/,
                          /\d/,
                          /\d/,
                        ];
                      }}
                      guide={false}
                      onChange={(e) => {
                        setFieldValue(
                          "zipShipping",
                          e.target.value.replace(/[^0-9.]+/g, ""),
                        );
                      }}
                      value={values.zipShipping}
                    />
                  ) : (
                    <Field
                      name="zipShipping"
                      label="Shipping Zip or Postal Code"
                      component={Input}
                      placeholder="94710"
                    />
                  )}
                  <Field
                    name="countryShipping"
                    label="Shipping Country"
                    component={SelectField}
                    options={countryOptions}
                    onChange={(e) => {
                      if (e.value !== "US") {
                        if (values.countryShipping === "US") {
                          setFieldValue("stateShipping", "");
                          setFieldValue("zipShipping", "");
                        }
                      }
                      if (e.value === "US") {
                        if (values.countryShipping !== "US") {
                          setFieldValue("stateShipping", "CA");
                          setFieldValue("zipShipping", "");
                        }
                      }
                      handleChange(e);
                    }}
                  />
                </div>
              )}
              <h3 className="mb-3">Payment Information</h3>
              <RadioButtonGroup
                id="paymentType"
                // label="One of these please"
                value={values.paymentType}
                error={errors.paymentType}
                // touched={touched.radioGroup}
              >
                {values.donationType !== "In-Kind" ? (
                  <>
                    <Field
                      component={RadioButton}
                      name="paymentType"
                      id="card"
                      label="Credit Card"
                      onChange={(e) => {
                        resetCheckValues(setFieldValue, initialValues);
                        handleChange(e);
                      }}
                    />
                    {props.installment === "One-Time" ? (
                      <>
                        {accessLevel !== "CallCenter" ? (
                          <Field
                            component={RadioButton}
                            name="paymentType"
                            id="check"
                            label="Check"
                            onChange={(e) => {
                              resetCardValues(setFieldValue, initialValues);
                              handleChange(e);
                            }}
                          />
                        ) : null}
                        <Field
                          component={RadioButton}
                          name="paymentType"
                          id="bill-me-later"
                          label="Bill-me-later"
                          onChange={(e) => {
                            resetCardValues(setFieldValue, initialValues);
                            resetCheckValues(setFieldValue, initialValues);
                            handleChange(e);
                          }}
                        />
                        {accessLevel !== "CallCenter" ? (
                          <>
                            <Field
                              component={RadioButton}
                              name="paymentType"
                              id="cash"
                              label="Cash"
                              onChange={(e) => {
                                resetCardValues(setFieldValue, initialValues);
                                resetCheckValues(setFieldValue, initialValues);
                                handleChange(e);
                              }}
                            />
                            <Field
                              component={RadioButton}
                              name="paymentType"
                              id="epayment"
                              label="ePayment"
                              onChange={(e) => {
                                resetCardValues(setFieldValue, initialValues);
                                resetCheckValues(setFieldValue, initialValues);
                                handleChange(e);
                              }}
                            />
                            {values.paymentType === "epayment" && (
                              <>
                                <Field
                                  name="ePaymentId"
                                  label="Payment ID"
                                  component={Input}
                                  placeholder="Enter Payment ID"
                                />
                                <Field
                                  name="ePaymentProcessor"
                                  label="Processor"
                                  component={SelectField}
                                  options={ePaymentProcessorOptions}
                                />
                                <Field
                                  name="ePaymentDate"
                                  label="Payment Date"
                                  component={Input}
                                  type="date"
                                />
                              </>
                            )}
                          </>
                        ) : null}
                      </>
                    ) : null}{" "}
                  </>
                ) : null}

                {values.donationType === "In-Kind" ? (
                  <Field
                    component={RadioButton}
                    name="paymentType"
                    id="in-kind"
                    label="In-Kind"
                    onChange={(e) => {
                      resetCardValues(setFieldValue, initialValues);
                      setFieldValue("checkNumber", initialValues.checkNumber);
                      setFieldValue(
                        "checkDepositDate",
                        initialValues.checkDepositDate,
                      );
                      setFieldValue("checkStatus", initialValues.checkStatus);
                      setFieldValue(
                        "checkProcessor",
                        initialValues.checkProcessor,
                      );

                      handleChange(e);
                    }}
                  />
                ) : null}
              </RadioButtonGroup>
              {values.paymentType === "check" ? <CheckPaymentForm /> : null}
              {values.paymentType === "cash" ? <CashPaymentForm /> : null}
              {values.paymentType === "card" ? (
                <CardPaymentForm setFieldValue={setFieldValue} />
              ) : null}
              <h3 className="mb-3  ">Comments</h3>
              <Field
                component={Checkbox}
                label="Can we mention your first name on the air?"
                name="readOnair"
              />
              <Field
                component={Checkbox}
                label="Comments for on-air hosts?"
                name="hasComment"
              />
              {values.hasComment === true ? (
                <Field
                  component={Textarea}
                  name="comment"
                  label="(500 character limit)"
                />
              ) : null}
              {(Number(props.amount) >= 200 &&
                props.installment === "One-Time") ||
              (Number(props.amount) >= 20 &&
                props.installment === "Monthly") ? (
                <Field
                  component={Checkbox}
                  label="Can we use this donation as a match?"
                  name="donationMatch"
                  checked={values.donationMatch}
                />
              ) : null}
              {props.amount &&
              Number(props.amount) <= Number(props.maxAmount) ? (
                <>
                  {formSubmitAttempted && Object.keys(errors).length > 0 && (
                    <div className="alert alert-danger mt-3">
                      <strong>Please fix the following errors:</strong>
                      <ul className="mb-0 mt-2">
                        {Object.entries(errors).map(([field, error]) => {
                          // Convert camelCase field names to readable format
                          const readableField = field
                            .replace(/([A-Z])/g, ' $1')
                            .replace(/^./, str => str.toUpperCase());
                          
                          return (
                            <li key={field}>
                              <strong>{readableField}:</strong> {error}
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  )}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="btn btn-primary btn-lg btn-block mb-5 mt-5"
                  >
                    Go to next step &#8594;
                  </button>
                </>
              ) : (
                <button
                  type="submit"
                  disabled
                  className="btn btn-primary btn-lg btn-block mb-5 mt-5"
                >
                  Go to next step &#8594;
                </button>
              )}
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default ContactForm;
