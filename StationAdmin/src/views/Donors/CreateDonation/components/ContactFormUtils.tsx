import { FormikHelpers } from "formik";

interface InitialValues {
  cardNumber: string;
  securityCode: string;
  expirationYear: number;
  expirationMonth: string;
  checkNumber: string;
  checkDepositDate: string;
  checkProcessor: string;
  checkStatus: string;
  cashDepositDate: string;
}

export const resetCardValues = (
  setFieldValue: FormikHelpers<InitialValues>["setFieldValue"],
  initialValues: InitialValues,
) => {
  setFieldValue("cardNumber", initialValues.cardNumber);
  setFieldValue("securityCode", initialValues.securityCode);
  setFieldValue("expirationMonth", initialValues.expirationMonth);
  setFieldValue("expirationYear", initialValues.expirationYear);
};

//
export const resetCheckValues = (
  setFieldValue: FormikHelpers<InitialValues>["setFieldValue"],
  initialValues: InitialValues,
) => {
  setFieldValue("checkNumber", initialValues.checkNumber);
  setFieldValue("checkDepositDate", initialValues.checkDepositDate);
  setFieldValue("checkStatus", initialValues.checkStatus);
  setFieldValue("checkProcessor", initialValues.checkProcessor);
};
