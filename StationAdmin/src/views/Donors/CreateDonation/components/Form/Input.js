import React from "react";
import Mask from "react-text-mask";

const Input = ({
  field, // { name, value, onChange, onBlur }
  form: { touched, errors, submitCount }, // also values, setXXXX, handleXXXX, dirty, isValid, status, etc.
  ...props
}) => {
  const inputClassNme = () => {
    if (submitCount > 0) {
      if (errors[field.name]) {
        return "form-control is-invalid";
      }
      if (!errors[field.name]) {
        return "form-control is-valid";
      }
    }
    return "form-control";
  };

  const inputId = `${field.name}-input`;

  const inputType = () => {
    if (props.mask) {
      return (
        <Mask
          // {...field}
          // {...props}
          className={inputClassNme()}
          value={props.value || field.value}
          type={props.type || "text"}
          placeholder={props.placeholder}
          id={inputId}
          name={field.name}
          onChange={props.onChange || field.onChange}
          guide={props.hasOwnProperty("guide") ? props.guide : true}
          mask={props.mask}
          aria-label={props.label}
        />
      );
    }
    return (
      <input
        // {...field}
        // {...props}
        className={inputClassNme()}
        value={props.value || field.value}
        type={props.type || "text"}
        placeholder={props.placeholder}
        id={inputId}
        name={field.name}
        onChange={props.onChange || field.onChange}
        aria-label={props.label}
        // required={props.required || false}
      />
    );
  };

  return (
    <div className="form-group row mb-3">
      {/* {console.log("errors", errors, "field", field)} */}
      <label className="col-form-label col-sm-3" htmlFor={inputId}>
        {props.label}
      </label>
      <div className="col-sm-9">
        {inputType()}
        <div className="invalid-feedback">{errors[field.name] || null}</div>
      </div>
    </div>
  );
};

export default Input;
