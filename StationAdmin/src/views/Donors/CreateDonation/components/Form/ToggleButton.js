import React from "react";
import currency from "currency.js";

const ToggleButton = (props) => {
  const buttonClassNames = () => {
    if (
      (props.stateAmount === props.value && props.otherFocus === false) ||
      (props.donationType && props.donationType === props.value)
    ) {
      return "btn btn-primary";
    }
    if (
      props.stateAmount !== props.value ||
      props.donationType !== props.value ||
      props.otherFocus === true
    ) {
      return "btn btn-secondary";
    }
  };

  return (
    <button
      className={buttonClassNames()}
      value={props.value}
      onClick={props.onChange}
    >
      {!isNaN(props.label) ? currency(props.value).format() : props.label}
    </button>
  );
};

export default ToggleButton;
