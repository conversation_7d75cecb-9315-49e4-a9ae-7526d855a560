//todo: consolidate this into the main Form/contstants file

export const months = [
  { value: "01", label: "January" },
  { value: "02", label: "February" },
  { value: "03", label: "March" },
  { value: "04", label: "April" },
  { value: "05", label: "May" },
  { value: "06", label: "June" },
  { value: "07", label: "July" },
  { value: "08", label: "August" },
  { value: "09", label: "September" },
  { value: "10", label: "October" },
  { value: "11", label: "November" },
  { value: "12", label: "December" },
];

const currentYear = new Date().getFullYear();
const ccYearList = Array(currentYear - (currentYear - 10) + 1)
  .fill()
  .map((_, idx) => currentYear + idx);
let ccYearObjects = [];

for (const year of ccYearList) {
  let yearObj = { label: year, value: year };
  ccYearObjects.push(yearObj);
}

export const ccYears = ccYearObjects;

export const orderStatus = ["Unpaid", "Paid", "Canceled", "Refunded"];

export const paymentMethod = [
  "card",
  "check",
  "cash",
  "stock",
  "in_kind",
  "bank_account",
];

export const CHECK_STATUS_OPTIONS = [
  { value: "Succeeded", label: "Succeeded" },
  { value: "Pending", label: "Pending" },
  { value: "Failed", label: "Failed" },
];

/*  CheckPaymentForm  */

export const CHECK_FIELD_NAMES = [
  "checkAmount",
  "checkNumber",
  "checkDepositDate",
  "checkStatus",
  "checkProcessor",
];

export const CHECK_NUMBER_LABEL = "Check Number";
export const CHECK_DEPOSIT_DATE_LABEL = "Date Deposited";
export const CHECK_STATUS_LABEL = "Status";
export const CHECK_PROCESSOR_LABEL = "Processor";
export const CHECK_PROCESSOR_OPTIONS = [
  { value: "P-check", label: "P-check" },
  { value: "K-check", label: "K-check" },
];
