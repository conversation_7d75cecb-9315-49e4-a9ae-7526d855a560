import { months, ccYears } from "./constants";
import {
  CHECK_STATUS_OPTIONS,
  CHECK_PROCESSOR_OPTIONS,
} from "../Form/constants";

const dateObj = new Date();
const monthName = dateObj.toLocaleString("default", { month: "long" });
const today =
  dateObj.getFullYear() +
  "-" +
  ("0" + (dateObj.getMonth() + 1)).slice(-2) +
  "-" +
  ("0" + dateObj.getDate()).slice(-2);

let initialValues = {
  campaign: "",
  donationType: "",
  showShippingAddress: false,
  firstName: "",
  lastName: "",
  address1: "",
  address2: "",
  city: "",
  state: "CA",
  zip: "",
  country: "US",
  phone: "",
  email: "",
  firstNameShipping: "",
  lastNameShipping: "",
  address1Shipping: "",
  address2Shipping: "",
  cityShipping: "",
  stateShipping: "",
  zipShipping: "",
  countryShipping: "",
  paymentType: "card",
  cardNumber: "",
  securityCode: "",
  expirationMonth: months.find((month) => month.label === monthName).value,
  expirationYear: ccYears[0].value,
  hasComment: false,
  comment: "",
  readOnair: false,
  donationMatch: false,
  checkNumber: "",
  checkDepositDate: today,
  checkStatus: CHECK_STATUS_OPTIONS[0].value,
  checkProcessor: CHECK_PROCESSOR_OPTIONS[0].value,
  ePaymentId: "",
  ePaymentProcessor: "",
  cashDepositDate: today,
};

export default initialValues;
