import * as Yup from "yup";
import { errorStrings } from "./validationStrings";

const createValidationForType = (type, fields) => {
  return fields.reduce((acc, field) => {
    acc[field] = Yup.string().when("paymentType", {
      is: type,
      then: Yup.string()
        .min(type === "check" ? 1 : 1, errorStrings.min)
        .max(type === "check" ? 20 : 36, errorStrings.max)
        .required(errorStrings.required),
    });
    return acc;
  }, {});
};

const supportFormSchema = (installment, amount) => {
  const baseSchema = {
    donationType: Yup.string().when("campaign", {
      is: (val) => isNaN(val) && installment === "One-Time",
      then: Yup.string()
        .required(errorStrings.required)
        .when([], {
          is: () => true, // Always check these conditions if there's no campaign
          then: Yup.string()
            .when("donationType", {
              is: "Minor",
              then: Yup.string().test(
                "amount-less-than-1000",
                `Donation type "Minor" is only allowed for amounts under $1000.`,
                () => amount < 1000,
              ),
            })
            .when("donationType", {
              is: "Major",
              then: Yup.string().test(
                "amount-greater-than-or-equal-to-1000",
                `Donation type "Major" is only allowed for amounts of $1000 or more.`,
                () => amount >= 1000,
              ),
            }),
        }),
    }),
    showShippingAddress: Yup.boolean(),
    firstName: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    lastName: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    address1: Yup.string()
      .min(5, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    address2: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .nullable(),
    city: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    state: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    zip: Yup.string().when("country", {
      is: "US",
      then: Yup.string()
        .min(5, errorStrings.min)
        .max(10, errorStrings.max)
        .required(errorStrings.required),
      otherwise: Yup.string()
        .min(5, errorStrings.min)
        .max(20, errorStrings.max)
        .required(errorStrings.required),
    }),
    country: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    phone: Yup.string().when("country", {
      is: "US",
      then: Yup.string()
        .transform((value) => {
          // If the value exists, strip any non-numeric characters
          if (value) {
            return value.replace(/\D/g, '');
          }
          return value;
        })
        .test(
          'valid-us-phone',
          'US phone number must have 10-11 digits',
          (value) => {
            if (!value) return true; // Allow empty values (nullable)
            
            // Valid if exactly 10 digits or 11 digits starting with 1 (country code)
            return value.length === 10 || 
                  (value.length === 11 && value.startsWith('1'));
          }
        )
        .nullable(),
      otherwise: Yup.string()
        .min(10, errorStrings.min)
        .max(20, errorStrings.max)
        .nullable(),
    }),
    email: Yup.string().email(errorStrings.email).nullable(),
    cardNumber: Yup.string().when("paymentType", {
      is: "card",
      then: Yup.string()
        .min(15, errorStrings.min)
        .max(19, errorStrings.max)
        .required(errorStrings.required),
    }),
    securityCode: Yup.string().when("paymentType", {
      is: "card",
      then: Yup.string()
        .min(3, errorStrings.min)
        .max(4, errorStrings.max)
        .required(errorStrings.required),
    }),
    expirationMonth: Yup.string().when("paymentType", {
      is: "card",
      then: Yup.string().required(errorStrings.required),
    }),
    expirationYear: Yup.string().when("paymentType", {
      is: "card",
      then: Yup.string().required(errorStrings.required),
    }),
    ...createValidationForType("check", [
      "checkNumber",
      "checkDepositDate",
      "checkStatus",
      "checkProcessor",
    ]),
    ...createValidationForType("epayment", [
      "ePaymentId",
      "ePaymentDate",
      "ePaymentProcessor",
    ]),
    comment: Yup.string()
      .min(8, errorStrings.min)
      .max(500, errorStrings.commentMax),
  };

  // Only add shipping validation when showShippingAddress is true
  const shippingSchema = {
    firstNameShipping: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    lastNameShipping: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    address1Shipping: Yup.string()
      .min(5, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    address2Shipping: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .nullable(),
    cityShipping: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    stateShipping: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
    zipShipping: Yup.string().when("countryShipping", {
      is: "US",
      then: Yup.string()
        .min(5, errorStrings.min)
        .max(10, errorStrings.max)
        .required(errorStrings.required),
      otherwise: Yup.string()
        .min(5, errorStrings.min)
        .max(15, errorStrings.max)
        .required(errorStrings.required),
    }),
    countryShipping: Yup.string()
      .min(2, errorStrings.min)
      .max(50, errorStrings.max)
      .required(errorStrings.required),
  };

  return Yup.object().shape({
    ...baseSchema,
    // Conditionally include shipping validation based on showShippingAddress
    firstNameShipping: Yup.mixed().when('showShippingAddress', {
      is: true,
      then: shippingSchema.firstNameShipping,
      otherwise: Yup.string().notRequired()
    }),
    lastNameShipping: Yup.mixed().when('showShippingAddress', {
      is: true,
      then: shippingSchema.lastNameShipping,
      otherwise: Yup.string().notRequired()
    }),
    address1Shipping: Yup.mixed().when('showShippingAddress', {
      is: true,
      then: shippingSchema.address1Shipping,
      otherwise: Yup.string().notRequired()
    }),
    address2Shipping: Yup.mixed().when('showShippingAddress', {
      is: true,
      then: shippingSchema.address2Shipping,
      otherwise: Yup.string().notRequired()
    }),
    cityShipping: Yup.mixed().when('showShippingAddress', {
      is: true,
      then: shippingSchema.cityShipping,
      otherwise: Yup.string().notRequired()
    }),
    stateShipping: Yup.mixed().when('showShippingAddress', {
      is: true,
      then: shippingSchema.stateShipping,
      otherwise: Yup.string().notRequired()
    }),
    zipShipping: Yup.mixed().when('showShippingAddress', {
      is: true,
      then: shippingSchema.zipShipping,
      otherwise: Yup.string().notRequired()
    }),
    countryShipping: Yup.mixed().when('showShippingAddress', {
      is: true,
      then: shippingSchema.countryShipping,
      otherwise: Yup.string().notRequired()
    }),
  });
};

export default supportFormSchema;
