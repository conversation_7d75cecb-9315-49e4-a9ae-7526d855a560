import { CBadge } from "@coreui/react";
import React from "react";
import ReactSelect from "react-select";
import SelectedPremiums from "./SelectedPremiums";

const PremiumDropdown = (props) => {
  return (
    <React.Fragment>
      <h3 className="mb-5 d-flex justify-content-between align-items-between">
        <span>Premiums</span>
        {props.selectedPremiums.length > 0 ? (
          <CBadge color="secondary" shape="rounded-pill">
            {props.selectedPremiums.length}
          </CBadge>
        ) : null}
      </h3>
      <SelectedPremiums
        amount={props.amount}
        premiums={props.premiums}
        selectedPremiums={props.selectedPremiums}
        selectedPremiumsData={props.selectedPremiumsData}
        handleRemovePremium={props.handleRemovePremium}
        donationType={props.donationType}
        premiumsTotal={props.premiumsTotal}
      />
      {props.premiumDropdown.length > 0 ? (
        <ReactSelect
          className="mt-3"
          options={props.premiumDropdown}
          onChange={props.handleAddPremium}
          value={null}
          placeholder="Select an item from the list to add"
        />
      ) : null}
    </React.Fragment>
  );
};

export default PremiumDropdown;
