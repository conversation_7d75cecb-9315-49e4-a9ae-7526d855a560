import React from "react";
import { cloneDeep } from "lodash";
import currency from "currency.js";

const SelectedPremiums = (props) => {
  // console.log("props", props);

  if (props.selectedPremiums.length > 0) {
    const selectedPremiums = cloneDeep(props.selectedPremiums);
    const allPremiums = cloneDeep(props.premiums);
    console.log("selectedPremiums", selectedPremiums);

    const premiumsList = selectedPremiums.map((item, index) => {
      let variation = "";
      // let variationID = "";
      const selectedPremium = allPremiums.find((premium) => {
        if (!premium.variants) {
          return Number(premium.id) === item;
        } else if (premium.variants) {
          // console.log(
          //   "premium.variants.variations",
          //   premium.variants.variations,
          //   item
          // );
          const parentPremium = premium.variants.variations.find((variant) => {
            // console.log(variant);
            variation = variant.name;
            // variationID = variant.id;
            return Number(variant.id) === item;
          });
          // console.log("parentPremium", parentPremium);
          return parentPremium ? true : false;
        }
        return false;
      });

      return (
        <li
          className="list-group-item d-flex justify-content-between align-items-center selected-premiums"
          key={`item${index}`}
        >
          {selectedPremium ? (
            <img
              style={{ width: "70px", marginRight: "15px" }}
              src={selectedPremium.img_url}
              alt="premium thumbnail"
            />
          ) : null}
          <span className="premium-name">
            {selectedPremium
              ? `${selectedPremium.name} ${
                  selectedPremium.variants ? `(${variation})` : ""
                }`
              : `Cannot find premium ID ${item} in premium list returned by server.`}
          </span>

          <strong className="ms-1">
            $
            {props.donationType === "One-Time"
              ? `${selectedPremium.price}`
              : `${selectedPremium.cog}/month`}
          </strong>

          {!props.summary ? (
            <div
              className="btn btn-danger ms-2"
              data-index={index}
              onClick={props.handleRemovePremium}
            >
              remove
            </div>
          ) : null}
        </li>
      );
    });
    return (
      <ul
        className="list-group mx-auto"
        style={props.summary ? { width: "80%" } : {}}
      >
        {premiumsList}
        {!props.summary ? (
          <li className="list-group-item list-group-item-primary d-flex justify-content-between align-items-center">
            <strong>Selected Premiums Total:</strong>
            <strong>
              $
              {props.donationType === "One-Time"
                ? props.premiumsTotal
                : `${props.premiumsTotal}/month`}
            </strong>
          </li>
        ) : null}
        {props.summary ? (
          <li className="list-group-item list-group-item-primary d-flex justify-content-center align-items-center">
            <h5>
              <strong>{props.donationType}</strong> Donation of{" "}
              <strong>{currency(props.amount).format()}</strong>
            </h5>
          </li>
        ) : null}
      </ul>
    );
  }
  return (
    <ul className="list-group">
      <li
        href="#"
        className="list-group-item list-group-item-action list-group-item-info"
      >
        No Premiums Selected
      </li>
    </ul>
  );
};

export default SelectedPremiums;
