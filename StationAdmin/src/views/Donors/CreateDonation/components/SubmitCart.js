import { useAppSelector } from "../../../../hooks";
import SubmitModal from "./SubmitModal";

const SubmitCart = ({
  formData,
  amount,
  minAmount,
  maxAmount,
  donationType,
  canSubmit,
  showModal,
  hideModal,
  premiums,
  selectedPremiums,
  selectedPremiumsData,
  premiumsTotal,
  selectedDonor,
  setSelectedDonor,
  toggleCreateDonation,
  campaignData,
}) => {
  const userEmail = useAppSelector((state) => state.user.email);

  if (canSubmit) {
    let source;
    if (userEmail === "<EMAIL>") {
      source = "CallCenter";
    } else if (userEmail === "<EMAIL>") {
      source = "PhoneRoom";
    } else {
      source = "StationAdmin";
    }

    //adjust data before adding to PremiumsForm
    const expYear = formData.expirationYear
      ? formData.expirationYear.toString().slice(2, 4)
      : "";

    const premiumsForm = {
      firstname: formData.firstName,
      lastname: formData.lastName,
      address1: formData.address1,
      address2: formData.address2,
      city: formData.city,
      state: formData.state,
      country: formData.country,
      postal_code: formData.zip,
      phone: Number(formData.phone) || "",
      email: formData.email || "",
      shipping_firstname: formData.showShippingAddress
        ? formData.firstNameShipping
        : formData.firstName,
      shipping_lastname: formData.showShippingAddress
        ? formData.lastNameShipping
        : formData.lastName,
      shipping_address1: formData.showShippingAddress
        ? formData.address1Shipping
        : formData.address1,
      shipping_address2: formData.showShippingAddress
        ? formData.address2Shipping || ""
        : formData.address2,
      shipping_city: formData.showShippingAddress
        ? formData.cityShipping
        : formData.city,
      shipping_state: formData.showShippingAddress
        ? formData.stateShipping
        : formData.state,
      shipping_country: formData.showShippingAddress
        ? formData.countryShipping
        : formData.country,
      shipping_postal_code: formData.showShippingAddress
        ? formData.zipShipping
        : formData.zip,
      method: formData.paymentType,
      type: formData.donationType,
      //   cardtype: document.querySelector("#card-type").textContent,
      cardtype: "",
      cardnumber:
        formData.paymentType === "card" ? Number(formData.cardNumber) : "",
      exp_month:
        formData.paymentType === "card" ? Number(formData.expirationMonth) : "",
      exp_year: formData.paymentType === "card" ? Number(expYear) : "",
      cc_securitycode:
        formData.paymentType === "card" ? formData.securityCode : "",
      payment_id:
        formData.paymentType === "check"
          ? formData.checkNumber
          : formData.paymentType === "epayment"
            ? formData.ePaymentId
            : "",
      date_deposited:
        formData.paymentType === "check"
          ? formData.checkDepositDate
          : formData.paymentType === "cash"
            ? formData.cashDepositDate
            : formData.paymentType === "epayment"
              ? formData.ePaymentDate
              : "",
      status:
        formData.paymentType === "epayment"
          ? "succeeded"
          : formData.paymentType === "check"
            ? formData.checkStatus
            : "",
      processor:
        formData.paymentType === "check"
          ? formData.checkProcessor
          : formData.paymentType === "epayment"
            ? formData.ePaymentProcessor
            : "",
      amount: Number(amount),
      installment: donationType,
      comments: formData.comment || "",
      add_me: "", // Missing in form
      read_onair: formData.readOnair,
      donotshare: "", // Missing in form
      browser: navigator.userAgent,
      show_name: "", // Missing in form
      source,
      donation_match: formData.donationMatch,
      premiums: selectedPremiums,
      premiums_cart: selectedPremiumsData,
      campaign_id: Number(formData.campaign),
    };

    return (
      <div>
        <SubmitModal
          amount={amount}
          donationType={donationType}
          premiumsForm={premiumsForm}
          showModal={showModal}
          hideModal={hideModal}
          formData={formData}
          premiums={premiums}
          selectedPremiums={selectedPremiums}
          selectedPremiumsData={selectedPremiumsData}
          premiumsTotal={premiumsTotal}
          minAmount={minAmount}
          maxAmount={maxAmount}
          selectedDonor={selectedDonor}
          setSelectedDonor={setSelectedDonor}
          toggleCreateDonation={toggleCreateDonation}
          campaignData={campaignData}
        />
      </div>
    );
  }
  return null;
};

export default SubmitCart;
