//todo: finish refactoring into funcational component
import { useEffect, useState } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CBadge,
} from "@coreui/react";
import currency from "currency.js";

import Summary from "./Summary";
import SelectedPremiums from "./SelectedPremiums";
import Warnings from "./Warnings";
import checkmark from "../images/checkmark.svg";
import "../styles/submit-modal.css";
import {
  useCreateDonationMutation,
  useGetDonationQuery,
} from "../../../../api/kpfa.ts";
import { useAppSelector } from "../../../../hooks";

export default function SubmitModal({
  hideModal,
  showModal,
  className,
  amount,
  donationType,
  formData,
  premiums,
  selectedPremiums,
  selectedPremiumsData,
  premiumsTotal,
  minAmount,
  maxAmount,
  amountTouched,
  premiumsForm,
  selectedDonor,
  setSelectedDonor,
  toggleCreateDonation,
  campaignData,
}) {
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [showFailureModal, setShowFailureModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [dataUpdateCompleted, setDataUpdateCompleted] = useState(false);
  const [skip, setSkip] = useState(true);
  const [newDonationId, setNewDonationId] = useState("");

  const testMode = useAppSelector((state) => state.testMode);
  const [
    createDonation,
    { data, error, isSuccess, isError, reset: resetMutation },
  ] = useCreateDonationMutation();

  const { data: donationData } = useGetDonationQuery(newDonationId, { skip });

  useEffect(() => {
    if (isError) {
      //next one should show status
      //setResponse(error?.data.status);
      //next one should show error message
      //setResponseContent(error?.data.message);
      setShowProcessingModal(false);
      setShowFailureModal(true);
    }
  }, [isError]);

  useEffect(() => {
    if (isSuccess) {
      if (data.status === "error") {
        setShowProcessingModal(false);
        setShowFailureModal(true);
      } else {
        setShowProcessingModal(false);
        setShowSuccessModal(true);

        if (selectedDonor) {
          setSkip(false);
          setNewDonationId(data.id);
        }
      }
    }
  }, [isSuccess, data, selectedDonor, toggleCreateDonation]);

  useEffect(() => {
    if (dataUpdateCompleted === false && donationData) {
      let mutatedSelectedDonor = selectedDonor;
      mutatedSelectedDonor.donations.unshift(donationData.records[0]);

      setSelectedDonor(mutatedSelectedDonor);
      setDataUpdateCompleted(true);
    }
  }, [setSelectedDonor, selectedDonor, dataUpdateCompleted, donationData]);

  const resetModals = () => {
    setShowProcessingModal(false);
    setShowFailureModal(false);
    setShowSuccessModal(false);
    setSubmitted(false);

    hideModal();
  };

  const resetForm = () => {
    resetMutation();
    resetModals();
    setSelectedDonor("");
    toggleCreateDonation(false);
  };

  const confirmDonation = () => {
    setShowProcessingModal(true);
    hideModal();
  };

  const confirmationModal = () => {
    if (showModal === true) {
      return (
        <CModal
          visible={showModal}
          onClose={resetModals}
          className={className}
          backdrop="static"
          keyboard={false}
          size="lg"
        >
          <CModalHeader>Please review and confirm the donation</CModalHeader>
          <CModalBody>
            <Summary
              amount={amount}
              donationType={donationType}
              formData={formData}
            />
            <SelectedPremiums
              amount={amount}
              donationType={donationType}
              formData={formData}
              premiums={premiums}
              selectedPremiums={selectedPremiums}
              selectedPremiumsData={selectedPremiumsData}
              premiumsTotal={premiumsTotal}
              summary
            />
            <Warnings
              amount={amount}
              minAmount={minAmount}
              maxAmount={maxAmount}
              amountTouched={amountTouched}
              premiumsTotal={premiumsTotal}
              donationType={donationType}
            />
          </CModalBody>
          <CModalFooter>
            <CButton color="secondary" onClick={resetModals}>
              Make a Change
            </CButton>
            <CButton color="primary" onClick={confirmDonation}>
              Confirm <strong>{donationType}</strong> Donation of{" "}
              <strong>{currency(amount).format()}</strong>
            </CButton>
          </CModalFooter>
        </CModal>
      );
    }
  };

  const processingModal = () => {
    if (showProcessingModal === true) {
      if (submitted === false) {
        let body = premiumsForm;
        if (premiumsForm.installment === "Monthly" || formData.campaign) {
          body.type = "Pledge";
        }

        // if an existing donor account is selected, we can take advantage of that to bypass the searchDonor() function on the backend
        // when the donor ID is passed in this request, the backend will use it to assign the new donation to said donor account 🎉

        if (selectedDonor) {
          body.donor_id = selectedDonor.id;
        }

        createDonation({ body });
        setSubmitted(true);
      }
      return (
        <CModal
          visible={showProcessingModal}
          className={className}
          backdrop="static"
          keyboard={false}
        >
          <CModalHeader>
            <CBadge color="warning" className="me-2">
              Please Wait
            </CBadge>{" "}
            Processing Donation
          </CModalHeader>
          <CModalBody>
            <div className="d-flex justify-content-center mt-2 mb-2">
              <h3 className=" ">
                <div
                  className="spinner-border text-primary"
                  style={{
                    width: "2rem",
                    height: "2rem",
                    marginRight: "0.5rem",
                  }}
                  role="status"
                >
                  <span className="sr-only">Processing Donation...</span>
                </div>
                Processing Donation...
              </h3>
            </div>
            <p>
              Please do not close window or navigate away from this page until
              your donation is processed and the confirmation message is
              displayed.
            </p>
          </CModalBody>
        </CModal>
      );
    }
    return null;
  };

  const failureModal = () => {
    if (showFailureModal === true) {
      return (
        <CModal
          visible={showFailureModal}
          onClose={() => resetModals}
          className={className}
          backdrop={true}
          keyboard={true}
        >
          <CModalHeader>
            <CBadge color="danger" className="me-2">
              Error
            </CBadge>{" "}
            Donation Failed
          </CModalHeader>
          {/* <CModalHeader toggle={toggle}>Modal title</CModalHeader> */}
          <CModalBody>
            <p>The donation did not process successfully.</p>
            {error ? (
              <>
                <p>
                  <strong>{error?.data.message}</strong>
                </p>
                <p>
                  Please make any required changes to the donation form and try
                  again.
                </p>
              </>
            ) : null}
            {data ? (
              <p>{data?.message}</p>
            ) : error ? (
              <p>error</p>
            ) : (
              <p>
                Error reaching KPFA server. Please check your internet
                connection and try again.
              </p>
            )}
          </CModalBody>
          <CModalFooter>
            <CButton color="primary" onClick={resetModals}>
              Return to donation form
            </CButton>
          </CModalFooter>
        </CModal>
      );
    }
    return null;
  };

  const successModal = () => {
    if (showSuccessModal === true && data) {
      return (
        <CModal
          visible={showSuccessModal}
          onClose={resetForm}
          backdrop={true}
          keyboard={true}
        >
          <CModalHeader>
            <CBadge color="success" className="me-2">
              Success
            </CBadge>{" "}
            Thank You for Supporting KPFA!
          </CModalHeader>
          <CModalBody>
            <div className="checkmark-container">
              <img src={checkmark} className="bounceIn" alt="checkmark" />
              <p>
                <strong>
                  Please read the following script for the caller:{" "}
                </strong>
                Your donation to KPFA was processed successfully. Please allow 6
                to 8 weeks for any gift items to be shipped to you.
              </p>
              <p></p>
              <strong>Donation {data.id} was created!</strong>
              <a
                href={`https://${
                  testMode.value ? "testing." : ""
                }api.kpfa.org/receipt/${data.transaction_id}`}
              >
                View Receipt
              </a>
            </div>
          </CModalBody>
          <CModalFooter>
            <CButton color="primary" onClick={resetForm}>
              Close
            </CButton>
          </CModalFooter>
        </CModal>
      );
    }
    return null;
  };

  return (
    <>
      {confirmationModal()}
      {processingModal()}
      {failureModal()}
      {successModal()}
    </>
  );
}
