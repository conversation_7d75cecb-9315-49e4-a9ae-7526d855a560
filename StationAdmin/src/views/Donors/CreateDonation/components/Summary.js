import React from "react";
import useFormatPhone from "../../../Hooks/useFormatPhone";

export default function Summary({ amount, donationType, formData }) {
  const phoneNumber = useFormatPhone(formData.phone, formData.country);

  return (
    <div className="mt-3 mx-auto" id="summary" style={{ width: "80%" }}>
      {
        <div id="summary-text">
          <h1 className="text-center text-primary">${amount}</h1>
          <h5 className="text-center">{donationType}</h5>
        </div>
      }

      <div className="row mt-3 mb-2">
        <div
          className={
            formData.showShippingAddress === true
              ? "col-sm-5 mx-auto"
              : "col-sm-12 mx-auto"
          }
        >
          <h5>
            {formData.showShippingAddress === true
              ? "Billing Address"
              : "Billing & Shipping Address"}
          </h5>
          <p>
            <strong>
              {formData.firstName} {formData.lastName}
            </strong>
          </p>
          <p>
            {formData.address1} <br />
            {formData.address2 ? formData.address2 : null}
            {formData.address2 ? <br /> : null}
            {formData.city}, {formData.state} {formData.zip} <br />
            {formData.country}
          </p>
          {formData.phone ? (
            <p>
              Phone: {phoneNumber}
              <br />
              {formData.email ? "Email: " + formData.email : null}
            </p>
          ) : null}
          {formData.paymentType === "card" && formData.cardNumber !== "" ? (
            <div data-private>
              <p>
                <span>
                  <strong>Card Number: </strong>
                  {formData.cardNumber.replace(/\d{4}(?=.)/g, "$& ")}
                </span>
                <br />
                <span>
                  <strong>Expiration: </strong>
                  {formData.expirationMonth}
                  {", "}
                  {formData.expirationYear}
                </span>
                <br />
                <span>
                  <strong>Security Code: </strong>
                  {formData.securityCode}
                </span>
              </p>
            </div>
          ) : null}
        </div>
        {formData.showShippingAddress === true ? (
          <div className="col-sm-5 mx-auto">
            <h5>Shipping Address</h5>
            <p>
              <strong>
                {formData.firstNameShipping} {formData.lastNameShipping}
              </strong>
            </p>
            <p>
              {formData.address1Shipping} <br />
              {formData.address2Shipping ? formData.address2Shipping : null}
              {formData.address2Shipping ? <br /> : null}
              {formData.cityShipping}, {formData.stateShipping}{" "}
              {formData.zipShipping} <br />
              {formData.countryShipping}
            </p>
          </div>
        ) : null}
      </div>
    </div>
  );
}
