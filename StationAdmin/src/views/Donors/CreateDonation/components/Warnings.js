import React from "react";
import currency from "currency.js";

const Warnings = ({
  amount,
  minAmount,
  maxAmount,
  amountTouched,
  premiumsTotal,
  donationType,
}) => {
  return (
    <div className="mt-4">
      {amountTouched && Number(amount) > Number(maxAmount) ? (
        <div className="alert alert-danger" role="alert">
          Maximum donation amount is {currency(maxAmount).format()}
        </div>
      ) : null}
      {Number(amount) < premiumsTotal ? (
        <div className="alert alert-warning" role="alert">
          Warning: The premium total is{" "}
          <strong>
            {currency(
              currency(premiumsTotal).subtract(currency(amount)).value
            ).format()}
          </strong>{" "}
          more than the current donation amount.
        </div>
      ) : null}

      {donationType === "Monthly" &&
      premiumsTotal > 0 &&
      currency(amount).subtract(currency(premiumsTotal)).value >=
        Number(minAmount) * 2 ? (
        <div className="alert alert-danger" role="alert">
          The <strong>MONTHLY</strong> pledge amount significantly exceeds the{" "}
          <strong>MONTHLY</strong> donation requirements for the premium(s)
          selected. Please verify this is correct.
        </div>
      ) : null}
    </div>
  );
};

export default Warnings;
