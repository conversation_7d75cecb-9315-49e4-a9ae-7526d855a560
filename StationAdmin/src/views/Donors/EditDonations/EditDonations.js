import { useCallback, useEffect, useState } from "react";
import { cloneDeep, sortBy } from "lodash";
import moment from "moment";

import ResultsPagination from "../../../components/ResultsPagination";
import DisplayResults from "./components/DisplayResults";
import Modals from "./components/Modals";
import "./edit-donations.css";
import { modalActions, modalFlows } from "./strings";
import { CCard, CCardBody } from "@coreui/react";
import { useGetCampaignsQuery } from "../../../api/kpfa.ts";
import { useAppDispatch } from "../../../hooks";

const EditDonations = ({
  selectedDonor,
  refreshCurrentDonor,
  setSelectedDonor,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [results, setResults] = useState([]);
  const [pledgeToEdit, setPledgeToEdit] = useState({});
  const [selectedPremiums, setSelectedPremiums] = useState([]);
  const [formValues, setFormValues] = useState({});
  const [formData, setFormData] = useState({});
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [modalAction, setModalAction] = useState(null);
  const [modalFlow, setModalFlow] = useState(null);
  const resultsPerPage = 10;

  const dispatch = useAppDispatch();

  const getLatestPledges = useCallback(() => {
    setIsLoading(true);
    if (selectedDonor) {
      const results = selectedDonor.donations;
      const totalPages = Math.ceil(results.length / resultsPerPage);
      const updateCurrentPage = currentPage <= totalPages ? currentPage : 1;

      setResults(results);
      setCurrentPage(updateCurrentPage);
      setTotalPages(totalPages);
      setIsLoading(false);
    }
    if (!selectedDonor) {
      setResults([]);
      setTotalPages(1);
      setCurrentPage(1);
      setIsLoading(false);
    }
  }, [selectedDonor, currentPage]);

  const { data: campaignData } = useGetCampaignsQuery();

  useEffect(() => {
    const responseCopy = cloneDeep(campaignData);
    const sortedResponse = sortBy(
      responseCopy,
      (campaign) => new Date(campaign.start),
    ).reverse();
    const campaignIDs = sortedResponse.map((campaign) => campaign.id);
    const campaignLabels = sortedResponse.map(
      (campaign) =>
        `${campaign.name} (${campaign.type}) - From ${moment(
          campaign.start,
        ).format(
          "MM/DD/YYYY",
        )} To ${moment(campaign.end).format("MM/DD/YYYY")}`,
    );
    campaignIDs.unshift("");
    campaignLabels.unshift("No campaign selected");

    dispatch({
      type: "campaignOptions/setCampaignOptions",
      payload: {
        campaignLabels: campaignLabels,
        campaignIDs: campaignIDs,
        activeCampaignID: sortedResponse.filter(
          (campaign) => campaign.active === true,
        )[0]?.id,
      },
    });
  }, [campaignData, dispatch]);

  useEffect(() => {
    getLatestPledges();
  }, [getLatestPledges, selectedDonor]);

  const handlePagination = (e) => {
    const paginationChange = parseInt(e.target.dataset.increment);
    setCurrentPage(currentPage + paginationChange);
  };

  const handleEditModal = (event) => {
    const transactionId = event.currentTarget.id;
    const clonedResults = cloneDeep(results);
    let pledge;
    let selectedPremiums = [];
    clonedResults.forEach((item) => {
      if (Number(item.id) === Number(transactionId)) {
        pledge = { ...item };
      }
    });
    if (pledge.premiums !== null) {
      for (const premium of pledge.premiums) {
        selectedPremiums.push(premium.id);
      }
    }

    setPledgeToEdit(pledge);
    setSelectedPremiums(selectedPremiums);
    setModalAction(modalActions.edit);
    setModalFlow(modalFlows.edit);
  };

  const UpdatePledgeToEdit = () => {
    const PledgeToEditId = pledgeToEdit.id;
    const results = selectedDonor.donations;
    let pledge;
    let selectedPremiums;
    results.forEach((item) => {
      if (Number(item.id) === Number(PledgeToEditId)) {
        pledge = { ...item };
      }
    });
    if (pledge.premiums_ID !== null) {
      selectedPremiums = cloneDeep(pledge.premiums);
    }

    setPledgeToEdit(pledge);
    setSelectedPremiums(selectedPremiums);
  };

  const closeModal = () => {
    refreshCurrentDonor();
    setModalAction(null);
    setModalFlow(null);
    setSelectedPremiums([]);
    setFormValues({});
    setFormData({});
    setPledgeToEdit({});
    setFormSubmitted(false);
  };

  const backToEditModal = () => {
    setModalFlow(modalFlows.edit);
    setFormSubmitted(false);
  };

  const handleAddPremium = (selection) => {
    const selectedPremiumsArray = [...selectedPremiums];
    if (selection.value) {
      selectedPremiumsArray.push(parseInt(selection.value));
      setSelectedPremiums(selectedPremiumsArray);
    }
  };

  const handleRemovePremium = (event) => {
    const index = event.target.dataset.index;
    const selectedPremiumsArray = [...selectedPremiums];
    selectedPremiumsArray.splice(index, 1);
    setSelectedPremiums(selectedPremiumsArray);
  };

  const handleCancelModal = (event) => {
    const transactionId = event.target.dataset.id;
    const resultsArray = [...results];
    let pledge;
    resultsArray.forEach((item) => {
      if (Number(item.id) === Number(transactionId)) {
        pledge = { ...item };
      }
    });

    const formData = {
      donation_id: pledge.id,
      amount: pledge.amount,
      installment: pledge.installment,
      type: pledge.type,
      comments: pledge.comment,
      status: "canceled",
      campaign_id: pledge.campaign_id,
      transaction_id: pledge.transaction_id,
      source: pledge.source,
      subscription_id: pledge.subscriptions[0].id,
    };

    setPledgeToEdit(pledge);
    setFormData(formData);
    setModalAction(modalActions.cancel);
    setModalFlow(modalFlows.confirm);
  };

  const handleDeleteModal = (event) => {
    const transactionId = event.target.dataset.id;
    const resultsArray = [...results];
    let pledge;
    let selectedPremiumsTemp = [];
    resultsArray.forEach((item) => {
      if (Number(item.id) === Number(transactionId)) {
        pledge = { ...item };
      }
    });
    if (pledge.premiums !== null) {
      selectedPremiumsTemp = cloneDeep(pledge.premiums);
    }

    const selectedPremiums = selectedPremiumsTemp.map((item) =>
      item.toString(),
    );

    const formData = {
      donation_id: pledge.id,
      shipping_firstname: pledge.shipping_firstname,
      shipping_lastname: pledge.shipping_lastname,
      shipping_address1: pledge.shipping_address1,
      shipping_address2: pledge.shipping_address2,
      shipping_city: pledge.shipping_city,
      shipping_state: pledge.shipping_state,
      shipping_country: pledge.shipping_country,
      shipping_postal_code: pledge.shipping_postal_code,
      amount: pledge.amount,
      installment: pledge.installment,
      type: pledge.type,
      comments: pledge.comment,
      read_onair: pledge.read_onair,
      donation_match: pledge.donation_match,
      premiums: pledge.premiums_ID,
      status: "canceled",
      campaign_id: pledge.campaign_id,
      transaction_id: pledge.transaction_id,
      source: pledge.source,
    };

    setPledgeToEdit(pledge);
    setSelectedPremiums(selectedPremiums);
    setFormData(formData);
    setModalAction(modalActions.delete);
    setModalFlow(modalFlows.confirm);
  };

  /**
   * HACK: The current API has a design issue where shipping fields are set to NULL when empty strings are provided,
   * but the database schema requires these fields to be NOT NULL. When the shipping address checkbox is unchecked,
   * we use a single space character " " for required shipping fields to prevent the backend from converting them to NULL.
   * 
   * API Refactoring Recommendations:
   * 1. Update API to handle empty strings properly without converting to NULL
   * 2. Create a separate endpoint for updating shipping address status (enable/disable)
   * 3. Modify database schema to allow NULL values for shipping fields, or
   * 4. Implement proper validation that returns clear error messages
   * 
   * Issue reference: Data truncated for column 'firstname' at row 1 (Issue #127)
   */
  const handlePledgeUpdateSubmit = (formValues) => {
    const formData = {
      donation_id: pledgeToEdit.id,
      shipping_firstname: formValues.showShippingAddress
        ? formValues.firstNameShipping
        : " ",
      shipping_lastname: formValues.showShippingAddress
        ? formValues.lastNameShipping
        : " ",
      shipping_address1: formValues.showShippingAddress
        ? formValues.address1Shipping
        : " ",
      shipping_address2: formValues.showShippingAddress
        ? formValues.address2Shipping || ""
        : "",
      shipping_city: formValues.showShippingAddress
        ? formValues.cityShipping
        : " ",
      shipping_state: formValues.showShippingAddress
        ? formValues.stateShipping
        : " ",
      shipping_country: formValues.showShippingAddress
        ? formValues.countryShipping
        : " ",
      shipping_postal_code: formValues.showShippingAddress
        ? formValues.zipShipping
        : " ",
      amount: formValues.amount,
      installment: formValues.pledgeType,
      type:
        formValues.pledgeType === "Monthly" || formValues.campaignID
          ? "Pledge"
          : formValues.donationType,
      comments: formValues.comment || null,
      read_onair: formValues.readOnair,
      donation_match: formValues.donationMatch,
      premiums: formValues.overridePremiums
        ? formValues.premiums.split(",")
        : [...selectedPremiums],
      // status: formValues.orderStatus.toLowerCase(),
      campaign_id: formValues.campaignID,
      transaction_id: pledgeToEdit.transaction_id,
      source: pledgeToEdit.source,
    };

    setFormData(formData);
    setFormValues(formValues);
    setModalFlow(modalFlows.confirm);
  };

  const confirmDonation = () => {
    setFormSubmitted(false);
    setModalFlow(modalFlows.processing);
  };

  const displayFailureModal = () => {
    setFormSubmitted(true);
    setModalFlow(modalFlows.failure);
  };

  const displaySuccessModal = () => {
    setFormSubmitted(true);
    setModalFlow(modalFlows.success);
  };

  const displayCardHeader = () => {
    return (
      <>
        <div className="card-header px-3 pb-1">
          <h6 className="font-weight-bold">
            {selectedDonor.firstname +
              " " +
              selectedDonor.lastname +
              "'s Donations"}
          </h6>
        </div>
        <div className="row bg-info text-white p-1 mx-0 font-weight-bold">
          <div className="col-sm-1">Amount</div>
          <div className="col-sm-1">Timestamp</div>
          <div className="col-sm-1">Pledge</div>
          <div className="col-sm-3">Status</div>
          <div className="col-sm-1">Payment</div>
          <div className="col-sm-2">Premiums</div>
          <div className="col-sm-3">Actions</div>
        </div>
      </>
    );
  };

  return (
    <div className="animated fadeIn mt-4">
      <CCard>
        {selectedDonor ? displayCardHeader() : ""}
        <CCardBody>
          <DisplayResults
            results={results}
            currentPage={currentPage}
            totalPages={totalPages}
            resultsPerPage={resultsPerPage}
            handleEditModal={handleEditModal}
            handleCancelModal={handleCancelModal}
            handleDeleteModal={handleDeleteModal}
            isLoading={isLoading}
            selectedDonor={selectedDonor}
          />

          <ResultsPagination
            results={results}
            currentPage={currentPage}
            totalPages={totalPages}
            handlePagination={handlePagination}
            isLoading={isLoading}
          />
        </CCardBody>
      </CCard>
      <Modals
        UpdatePledgeToEdit={UpdatePledgeToEdit}
        modalAction={modalAction}
        modalFlow={modalFlow}
        closeModal={closeModal}
        backToEditModal={backToEditModal}
        displayFailureModal={displayFailureModal}
        displaySuccessModal={displaySuccessModal}
        pledgeToEdit={pledgeToEdit}
        handleAddPremium={handleAddPremium}
        handleRemovePremium={handleRemovePremium}
        selectedPremiums={selectedPremiums}
        handlePledgeUpdateSubmit={handlePledgeUpdateSubmit}
        formData={formData}
        formValues={formValues}
        confirmDonation={confirmDonation}
        formSubmitted={formSubmitted}
        selectedDonor={selectedDonor}
        setSelectedDonor={setSelectedDonor}
        refreshCurrentDonor={refreshCurrentDonor}
      />
    </div>
  );
};

export default EditDonations;
