import AddPaymentOptions from "./AddPaymentOptions";
import CheckPaymentForm from "./CheckPaymentForm";
import CardPaymentForm from "./CardPaymentForm";
import CashPaymentForm from "./CashPaymentForm";
import EpaymentForm from "./EpaymentForm";
import StripePaymentForm from "./StripePaymentForm";

const AddPayment = ({
  UpdatePledgeToEdit,
  pledge,
  values,
  toggleActionButtons,
  selectedPayment,
  updateSelectedPayment,
  selectedPaymentMethod,
  updateSelectedPaymentMethod,
  selectedPaymentType,
  updateSelectedPaymentType,
  showCheckForm,
  toggleCheckForm,
  showCardForm,
  toggleCardForm,
  showCashForm,
  toggleCashForm,
  showStripeForm,
  toggleStripeForm,
  showEpaymentForm,
  toggleEpaymentForm,
  populateForm,
  updatePopulateForm,
  selectedDonor,
  setSelectedDonor,
  refreshCurrentDonor,
}) => {
  const checkPaymentNumberExists = (checkNumber) => {
    return pledge.payments.some(
      (payment) => payment.payment_id === checkNumber
    );
  };

  return (
    <div className="card mb-5" style={{ width: "100%" }}>
      <div className="card-body">
        {(() => {
          if (
            !showCheckForm &&
            !showCardForm &&
            !showCashForm &&
            !showStripeForm &&
            !showEpaymentForm
          ) {
            return (
              <>
                <h5 className="card-title mb-3">Add Payment</h5>
                <AddPaymentOptions
                  toggleCheckForm={toggleCheckForm}
                  toggleCardForm={toggleCardForm}
                  toggleCashForm={toggleCashForm}
                  toggleActionButtons={toggleActionButtons}
                  updateSelectedPaymentType={updateSelectedPaymentType}
                />
              </>
            );
          } else if (showCheckForm) {
            return (
              <CheckPaymentForm
                pledge={pledge}
                values={values}
                toggleCheckForm={toggleCheckForm}
                toggleActionButtons={toggleActionButtons}
                selectedPayment={selectedPayment}
                updateSelectedPayment={updateSelectedPayment}
                selectedPaymentMethod={selectedPaymentMethod}
                updateSelectedPaymentMethod={updateSelectedPaymentMethod}
                selectedPaymentType={selectedPaymentType}
                updateSelectedPaymentType={updateSelectedPaymentType}
                populateForm={populateForm}
                updatePopulateForm={updatePopulateForm}
                showCheckForm={showCheckForm}
                showCardForm={showCardForm}
                showCashForm={showCashForm}
                UpdatePledgeToEdit={UpdatePledgeToEdit}
                selectedDonor={selectedDonor}
                setSelectedDonor={setSelectedDonor}
                refreshCurrentDonor={refreshCurrentDonor}
                checkPaymentNumberExists={checkPaymentNumberExists}
              />
            );
          } else if (showCardForm) {
            return (
              <CardPaymentForm
                UpdatePledgeToEdit={UpdatePledgeToEdit}
                pledge={pledge}
                values={values}
                toggleCardForm={toggleCardForm}
                toggleActionButtons={toggleActionButtons}
                selectedPayment={selectedPayment}
                updateSelectedPayment={updateSelectedPayment}
                selectedPaymentMethod={selectedPaymentMethod}
                updateSelectedPaymentMethod={updateSelectedPaymentMethod}
                selectedPaymentType={selectedPaymentType}
                updateSelectedPaymentType={updateSelectedPaymentType}
                populateForm={populateForm}
                updatePopulateForm={updatePopulateForm}
                showCardForm={showCardForm}
                showCheckForm={showCheckForm}
                showCashForm={showCashForm}
                selectedDonor={selectedDonor}
                setSelectedDonor={setSelectedDonor}
                refreshCurrentDonor={refreshCurrentDonor}
              />
            );
          } else if (showCashForm) {
            return (
              <CashPaymentForm
                UpdatePledgeToEdit={UpdatePledgeToEdit}
                pledge={pledge}
                values={values}
                toggleCashForm={toggleCashForm}
                toggleActionButtons={toggleActionButtons}
                selectedPayment={selectedPayment}
                updateSelectedPayment={updateSelectedPayment}
                selectedPaymentMethod={selectedPaymentMethod}
                updateSelectedPaymentMethod={updateSelectedPaymentMethod}
                selectedPaymentType={selectedPaymentType}
                updateSelectedPaymentType={updateSelectedPaymentType}
                populateForm={populateForm}
                updatePopulateForm={updatePopulateForm}
                showCardForm={showCardForm}
                showCheckForm={showCheckForm}
                showCashForm={showCashForm}
                refreshCurrentDonor={refreshCurrentDonor}
                selectedDonor={selectedDonor}
                setSelectedDonor={setSelectedDonor}
              />
            );
          } else if (showEpaymentForm) {
            return (
              <EpaymentForm
                pledge={pledge}
                toggleEpaymentForm={toggleEpaymentForm}
                toggleActionButtons={toggleActionButtons}
                selectedPayment={selectedPayment}
                updateSelectedPayment={updateSelectedPayment}
                selectedPaymentMethod={selectedPaymentMethod}
                updateSelectedPaymentMethod={updateSelectedPaymentMethod}
                selectedPaymentType={selectedPaymentType}
                updateSelectedPaymentType={updateSelectedPaymentType}
                showEpaymentForm={showEpaymentForm}
                selectedDonor={selectedDonor}
                setSelectedDonor={setSelectedDonor}
                UpdatePledgeToEdit={UpdatePledgeToEdit}
              />
            );
          } else if (showStripeForm) {
            return (
              <StripePaymentForm
                pledge={pledge}
                values={values}
                toggleStripeForm={toggleStripeForm}
                toggleActionButtons={toggleActionButtons}
                selectedPayment={selectedPayment}
                updateSelectedPayment={updateSelectedPayment}
                selectedPaymentMethod={selectedPaymentMethod}
                updateSelectedPaymentMethod={updateSelectedPaymentMethod}
                selectedPaymentType={selectedPaymentType}
                updateSelectedPaymentType={updateSelectedPaymentType}
                populateForm={populateForm}
                updatePopulateForm={updatePopulateForm}
                showCardForm={showCardForm}
                showCheckForm={showCheckForm}
                showStripeForm={showStripeForm}
              />
            );
          }
          return null;
        })()}
      </div>
    </div>
  );
};

export default AddPayment;
