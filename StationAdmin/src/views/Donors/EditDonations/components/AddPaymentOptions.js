import { CButton, CButtonGroup } from "@coreui/react";
import React from "react";

const AddPaymentOptions = ({
  toggleActionButtons,
  toggleCheckForm,
  toggleCardForm,
  toggleCashForm,
  updateSelectedPaymentType,
}) => {
  return (
    <div className="row d-flex justify-content-center mb-3">
      <CButtonGroup role="group">
        <CButton
          variant="outline"
          onClick={() => {
            toggleCheckForm(true);
            toggleActionButtons(false);
            updateSelectedPaymentType("check");
          }}
        >
          Check Payment
        </CButton>
        <CButton
          variant="outline"
          onClick={() => {
            toggleCardForm(true);
            toggleActionButtons(false);
            updateSelectedPaymentType("card");
          }}
        >
          Card Payment
        </CButton>
        <CButton
          variant="outline"
          onClick={() => {
            toggleCashForm(true);
            toggleActionButtons(false);
            updateSelectedPaymentType("cash");
          }}
        >
          Cash Payment
        </CButton>
      </CButtonGroup>
    </div>
  );
};

export default AddPaymentOptions;
