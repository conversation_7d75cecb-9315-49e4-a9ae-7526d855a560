import React, { useState } from "react";

const CampaignSearchQuery = ({
  handleSearch,
  campaignLabels,
  campaignSearchStrings
}) => {
  const [selectedCampaign, setSelectedCampaign] = useState("");
  return (
    <div className="form-group">
      <label htmlFor="campaign-search">Campaign</label>
      <select
        id="campaign-search"
        className="form-control"
        onChange={e => {
          const eventValue = e.target.value;
          setSelectedCampaign(eventValue);
          if (eventValue) {
            handleSearch(eventValue, eventValue);
          } else {
            setSelectedCampaign("");
            handleSearch("", "");
          }
        }}
        value={selectedCampaign}
      >
        {campaignLabels.length > 0 ? (
          campaignLabels.map((campaign, index) => (
            <option key={campaign} value={campaignSearchStrings[index]}>
              {campaign}
            </option>
          ))
        ) : (
          <option>No Campaigns Available</option>
        )}
      </select>
    </div>
  );
};

export default CampaignSearchQuery;
