import React, { useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import moment from "moment";

import {
  CARD_FORM_HEADING_ADD,
  CARD_FIELD_NAMES,
  CARD_AMOUNT_LABEL,
  CARD_NUMBER_LABEL,
  CARD_SECURITY_CODE_LABEL,
  CARD_EXPIRATION_MONTH_LABEL,
  CARD_EXPIRATION_YEAR_LABEL,
  ADD_CARD_CANCEL_BTN_LABEL,
  ADD_CARD_CONFIRM_BTN_LABEL,
  CARD_VALIDATION_AMOUNT_FAILED,
  CARD_VALIDATION_NUMBER_FAILED,
  CARD_VALIDATION_SECURITY_CODE_FAILED,
  CARD_VALIDATION_EXPIRATION_MONTH_FAILED,
  CARD_VALIDATION_EXPIRATION_YEAR_FAILED,
} from "../strings";
import InputFlexible from "../../../Form/InputFlexible";
import DropdownFlexible from "../../../Form/DropdownFlexible";
import { months, ccYears } from "../../../Form/constants";
import Overlay from "./Overlay";
import { CButton, CButtonGroup, CCol, CRow } from "@coreui/react";
import { useAddPaymentMutation } from "../../../../api/kpfa.ts";

const CardPaymentForm = ({
  UpdatePledgeToEdit,
  pledge,
  toggleCardForm,
  showCardForm,
  toggleActionButtons,
  selectedPaymentMethod,
  updateSelectedPayment,
  updateSelectedPaymentMethod,
  selectedPaymentType,
  updateSelectedPaymentType,
  selectedDonor,
  setSelectedDonor,
  selectedPayment,
}) => {
  const [showSpinner, toggleSpinner] = useState(false);
  const [showSuccess, toggleSuccess] = useState(false);
  const [showFailure, toggleFailure] = useState(false);
  const [errorMessage, setErrorMessage] = useState();
  const [dataUpdateCompleted, setDataUpdateCompleted] = useState(false);

  const [
    addPayment,
    { data: paymentResponse, error, isLoading, isSuccess, isError },
  ] = useAddPaymentMutation();

  useEffect(() => {
    if (isLoading) {
      toggleSpinner(true);
    } else {
      toggleSpinner(false);
    }
  }, [isLoading]);

  useEffect(() => {
    if (isSuccess) {
      toggleSuccess(true);
    } else {
      toggleSuccess(false);
    }
    if (dataUpdateCompleted === false && isSuccess) {
      let mutatedSelectedDonor = selectedDonor;
      let pledgeIndex = mutatedSelectedDonor.donations.findIndex(
        (obj) => obj.id === pledge.id
      );
      let mutatedPayments =
        mutatedSelectedDonor.donations[pledgeIndex].payments;

      //add new payment object to pledgeToEdit & selectedDonor
      mutatedPayments.push(paymentResponse);
      mutatedSelectedDonor.donations[pledgeIndex].payments = mutatedPayments;
      setSelectedDonor(mutatedSelectedDonor);
      UpdatePledgeToEdit();
      setDataUpdateCompleted(true);
    }
  }, [
    isSuccess,
    UpdatePledgeToEdit,
    setSelectedDonor,
    selectedPayment,
    selectedDonor,
    pledge,
    dataUpdateCompleted,
    paymentResponse,
  ]);

  useEffect(() => {
    if (isError) {
      toggleFailure(true);
    } else {
      toggleFailure(false);
    }
  }, [isError]);

  useEffect(() => {
    if (error) {
      setErrorMessage(error?.data.message);
    }
  }, [error]);

  const initialValues = {
    cardAmount: "",
    cardNumber: "",
    cardSecurityCode: "",
    cardExpirationMonth: months[Number(moment().format("MM")) - 1],
    cardExpirationYear: moment().format("YYYY"),
  };

  return (
    <div data-private>
      <h5 className="card-title mb-3">{CARD_FORM_HEADING_ADD}</h5>
      <Formik
        initialValues={initialValues}
        // validationSchema={supportFormSchema}
        onSubmit={(values, { setSubmitting }) => {
          let body = {
            method: "card",
            amount: values.cardAmount,
            cardnumber: values.cardNumber,
            cc_securitycode: values.cardSecurityCode,
            exp_month:
              values.cardExpirationMonth[0] + values.cardExpirationMonth[1],
            exp_year: values.cardExpirationYear,
            donor_id: selectedDonor.id,
            transaction_id: pledge.transaction_id,
            donation_id: pledge.id,
            firstname: selectedDonor.firstname,
            lastname: selectedDonor.lastname,
            address1: selectedDonor.address1,
            address2: selectedDonor.address2,
            city: selectedDonor.city,
            state: selectedDonor.state,
            country: selectedDonor.country,
            postal_code: selectedDonor.postal_code,
            phone: selectedDonor.phone,
            email: selectedDonor.email,
            source: pledge.source,
            installment: pledge.installment,
            status: "succeeded",
          };

          addPayment({ body });

          setSubmitting(false);
        }}
      >
        {({ handleSubmit, setFieldValue, setFieldTouched }) => {
          const resetForm = () => {
            // reset field values
            setFieldValue("cardAmount", "");
            setFieldValue("cardNumber", "");
            setFieldValue("cardSecurityCode", "");
            setFieldValue("cardExpirationMonth", "");
            setFieldValue("cardExpirationYear", "");
            CARD_FIELD_NAMES.forEach((field) => {
              setFieldTouched(field, false);
            });
            // toggleCheckForm(false);
            toggleCardForm(false);
            toggleActionButtons(true);
            updateSelectedPayment(null);
            updateSelectedPaymentMethod(null);
            updateSelectedPaymentType(null);
          };

          return (
            <Form className="needs-validation" onSubmit={handleSubmit}>
              <div style={{ position: "relative" }}>
                <CRow>
                  <CCol md={3}>
                    <Field
                      name="cardAmount"
                      label={CARD_AMOUNT_LABEL}
                      component={InputFlexible}
                      validate={(value) => {
                        if (
                          Number(value) &&
                          Number(value) >= 0.5 &&
                          Number(value) < 999999
                        ) {
                          return;
                        }
                        return CARD_VALIDATION_AMOUNT_FAILED;
                      }}
                    />
                  </CCol>
                  <CCol>
                    <Field
                      name="cardNumber"
                      label={CARD_NUMBER_LABEL}
                      component={InputFlexible}
                      validate={(value) => {
                        if (
                          Number(value) &&
                          String(Number(value)).length > 14 &&
                          String(Number(value)).length < 20
                        ) {
                          return;
                        }
                        return CARD_VALIDATION_NUMBER_FAILED;
                      }}
                    />
                  </CCol>
                  <CCol md={3}>
                    <Field
                      name="cardSecurityCode"
                      label={CARD_SECURITY_CODE_LABEL}
                      component={InputFlexible}
                      validate={(value) => {
                        if (
                          Number(value) &&
                          value.length < 5 &&
                          (String(Number(value)).length === 3 ||
                            String(Number(value)).length === 4)
                        ) {
                          return;
                        } else if (
                          Number(value) &&
                          value.length < 5 &&
                          value[0] === "0" &&
                          (String(Number(value)).length === 1 ||
                            String(Number(value)).length === 2 ||
                            String(Number(value)).length === 3)
                        ) {
                          return;
                        }
                        return CARD_VALIDATION_SECURITY_CODE_FAILED;
                      }}
                    />
                  </CCol>
                </CRow>
                <CRow className="mt-3">
                  <CCol md={4}>
                    <Field
                      name="cardExpirationMonth"
                      label={CARD_EXPIRATION_MONTH_LABEL}
                      component={DropdownFlexible}
                      options={months}
                      validate={(value) => {
                        if (months.includes(value)) {
                          return;
                        }
                        return CARD_VALIDATION_EXPIRATION_MONTH_FAILED;
                      }}
                    />
                  </CCol>
                  <CCol md={4}>
                    <Field
                      name="cardExpirationYear"
                      label={CARD_EXPIRATION_YEAR_LABEL}
                      component={DropdownFlexible}
                      options={ccYears}
                      validate={(value) => {
                        if (ccYears.includes(value)) {
                          return;
                        }
                        return CARD_VALIDATION_EXPIRATION_YEAR_FAILED;
                      }}
                    />
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CButtonGroup className="mt-4">
                      <CButton variant="outline" onClick={resetForm}>
                        {ADD_CARD_CANCEL_BTN_LABEL}
                      </CButton>
                      <CButton type="submit">
                        {ADD_CARD_CONFIRM_BTN_LABEL}
                      </CButton>
                    </CButtonGroup>
                  </CCol>
                </CRow>
                <Overlay
                  errorMessage={errorMessage}
                  showSpinner={showSpinner}
                  showSuccess={showSuccess}
                  showFailure={showFailure}
                  toggleSuccess={toggleSuccess}
                  toggleFailure={toggleFailure}
                  resetForm={resetForm}
                  selectedPaymentMethod={selectedPaymentMethod}
                  selectedPaymentType={selectedPaymentType}
                  toggleForm={toggleCardForm}
                  showForm={showCardForm}
                />
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default CardPaymentForm;
