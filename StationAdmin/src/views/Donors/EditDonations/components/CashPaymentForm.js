import React, { useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import moment from "moment";
import { startCase, toLower } from "lodash";

import {
  CASH_FORM_HEADING_ADD,
  CASH_FORM_HEADING_EDIT,
  CASH_FORM_HEADING_DELETE,
  CASH_FIELD_NAMES,
  CASH_AMOUNT_LABEL,
  CASH_DEPOSIT_DATE_LABEL,
  ADD_CASH_CANCEL_BTN_LABEL,
  ADD_CASH_CONFIRM_BTN_LABEL,
  EDIT_CASH_CONFIRM_BTN_LABEL,
  DELETE_CASH_CONFIRM_BTN_LABEL,
  CASH_VALIDATION_AMOUNT_FAILED,
  CASH_VALIDATION_DATE_FAILED,
} from "../strings";
import InputFlexible from "../../../Form/InputFlexible";
import Overlay from "./Overlay";
import { CButton, CButtonGroup, CCol, CRow } from "@coreui/react";
import {
  useAddPaymentMutation,
  useDeletePaymentMutation,
  useEditPaymentMutation,
} from "../../../../api/kpfa.ts";

const CashPaymentForm = ({
  pledge,
  toggleCashForm,
  toggleActionButtons,
  selectedPayment,
  updateSelectedPayment,
  selectedPaymentMethod,
  updateSelectedPaymentMethod,
  selectedPaymentType,
  updateSelectedPaymentType,
  showCashForm,
  UpdatePledgeToEdit,
  selectedDonor,
  setSelectedDonor,
  refreshCurrentDonor,
}) => {
  const [showSpinner, toggleSpinner] = useState(false);
  const [showSuccess, toggleSuccess] = useState(false);
  const [showFailure, toggleFailure] = useState(false);
  const [errorMessage, setErrorMessage] = useState();
  const [dataUpdateCompleted, setDataUpdateCompleted] = useState(false);

  const [
    addPayment,
    { data: paymentResponse, error, isLoading, isSuccess, isError },
  ] = useAddPaymentMutation();

  const [
    deletePayment,
    {
      error: deleteError,
      isLoading: deleteIsLoading,
      isSuccess: deleteIsSuccess,
      isError: deleteIsError,
    },
  ] = useDeletePaymentMutation();

  const [
    editPayment,
    {
      data: editResponse,
      error: editError,
      isLoading: editIsLoading,
      isSuccess: editIsSuccess,
      isError: editIsError,
    },
  ] = useEditPaymentMutation();

  useEffect(() => {
    if (isLoading || deleteIsLoading || editIsLoading) {
      toggleSpinner(true);
    } else {
      toggleSpinner(false);
    }
  }, [isLoading, deleteIsLoading, editIsLoading]);

  useEffect(() => {
    if (isSuccess || deleteIsSuccess || editIsSuccess) {
      toggleSuccess(true);
    } else {
      toggleSuccess(false);
    }
    if (dataUpdateCompleted === false) {
      if (isSuccess || deleteIsSuccess || editIsSuccess) {
        let mutatedSelectedDonor = selectedDonor;
        let pledgeIndex = mutatedSelectedDonor.donations.findIndex(
          (obj) => obj.id === pledge.id
        );
        let mutatedPayments =
          mutatedSelectedDonor.donations[pledgeIndex].payments;

        if (deleteIsSuccess) {
          //remove deleted payment from pledgeToEdit & selectedDonor
          mutatedPayments = mutatedPayments.filter(
            (payment) => payment.id !== selectedPayment.id
          );
        } else if (isSuccess) {
          //add new payment object to pledgeToEdit & selectedDonor
          mutatedPayments.push(paymentResponse);
        } else if (editIsSuccess) {
          //todo: refactor this to use the API's response once that is wired up
          async function refreshDonor() {
            await refreshCurrentDonor();
            UpdatePledgeToEdit();
          }
          refreshDonor();
          console.log(editResponse);
        }
        mutatedSelectedDonor.donations[pledgeIndex].payments = mutatedPayments;
        setSelectedDonor(mutatedSelectedDonor);
        UpdatePledgeToEdit();
        setDataUpdateCompleted(true);
      }
    }
  }, [
    isSuccess,
    deleteIsSuccess,
    editIsSuccess,
    UpdatePledgeToEdit,
    setSelectedDonor,
    selectedPayment,
    selectedDonor,
    pledge,
    dataUpdateCompleted,
    paymentResponse,
    editResponse,
    refreshCurrentDonor,
  ]);

  useEffect(() => {
    if (isError || deleteIsError || editIsError) {
      toggleFailure(true);
    } else {
      toggleFailure(false);
    }
  }, [isError, deleteIsError, editIsError]);

  useEffect(() => {
    if (error) {
      setErrorMessage(error?.data.message);
    } else if (deleteError) {
      setErrorMessage(deleteError?.data.message);
    } else if (editError) {
      setErrorMessage(editError?.data.message);
    }
  }, [error, deleteError, editError]);

  const getInitialValues = () => {
    if (selectedPayment && selectedPaymentMethod) {
      return {
        cashAmount: selectedPayment.amount,
        cashNumber: selectedPayment.payment_id,
        cashDepositDate: moment(selectedPayment.date_deposited).format(
          "YYYY-MM-DD"
        ),
        cashStatus: startCase(toLower(selectedPayment.status)),
        cashProcessor: selectedPayment.processor,
      };
    }
    return {
      cashAmount: "",
      cashDepositDate: "",
    };
  };

  return (
    <>
      <h5 className="card-title mb-3">
        {(() => {
          if (selectedPaymentMethod === "DELETE") {
            return CASH_FORM_HEADING_DELETE;
          } else if (selectedPaymentMethod === "PUT") {
            return CASH_FORM_HEADING_EDIT;
          }
          return CASH_FORM_HEADING_ADD;
        })()}
      </h5>
      <Formik
        initialValues={getInitialValues()}
        onSubmit={(values, { setSubmitting }) => {
          let body = {
            donation_id: pledge.id,
            amount: Number(values.cashAmount),
            processor: values.cashProcessor,
            date_deposited: values.cashDepositDate,
            method: "cash",
            source: pledge.source,
            status: "succeeded",
          };

          if (!selectedPaymentMethod) {
            addPayment({ body });
          } else if (selectedPaymentMethod === "DELETE") {
            let id = selectedPayment.id;
            deletePayment({ id });
          } else if (selectedPaymentMethod === "PUT") {
            body.id = selectedPayment.id;
            body.payment_id = selectedPayment.payment_id;
            editPayment({ body });
          }
          setSubmitting(false);
        }}
      >
        {({ handleSubmit, setFieldValue, setFieldTouched }) => {
          const resetForm = () => {
            // reset field values
            CASH_FIELD_NAMES.forEach((field) => {
              setFieldValue(field, "");
              setFieldTouched(field, false);
            });

            toggleCashForm(false);
            toggleActionButtons(true);
            updateSelectedPayment(null);
            updateSelectedPaymentMethod(null);
            updateSelectedPaymentType(null);
          };

          return (
            <Form className="needs-validation" onSubmit={handleSubmit}>
              <div style={{ position: "relative" }}>
                <CRow>
                  <CCol>
                    <Field
                      name="cashAmount"
                      label={CASH_AMOUNT_LABEL}
                      component={InputFlexible}
                      disabled={
                        selectedPaymentMethod === "DELETE" ? true : false
                      }
                      validate={(value) => {
                        if (Number(value) >= 0.01) {
                          return undefined;
                        }
                        return CASH_VALIDATION_AMOUNT_FAILED;
                      }}
                    />
                  </CCol>
                  <CCol>
                    <Field
                      name="cashDepositDate"
                      label={CASH_DEPOSIT_DATE_LABEL}
                      component={InputFlexible}
                      disabled={
                        selectedPaymentMethod === "DELETE" ? true : false
                      }
                      type="date"
                      validate={(value) => {
                        const regEx = /^\d{4}-\d{2}-\d{2}$/;
                        if (value.match(regEx)) {
                          return undefined;
                        }
                        return CASH_VALIDATION_DATE_FAILED;
                      }}
                    />
                  </CCol>
                </CRow>
                <div className="row">
                  <div className="col-sm-12 text-right mt-3">
                    <CButtonGroup>
                      <CButton variant="outline" onClick={resetForm}>
                        {ADD_CASH_CANCEL_BTN_LABEL}
                      </CButton>
                      <CButton type="submit">
                        {(() => {
                          if (selectedPaymentMethod === "DELETE") {
                            return DELETE_CASH_CONFIRM_BTN_LABEL;
                          } else if (selectedPaymentMethod === "PUT") {
                            return EDIT_CASH_CONFIRM_BTN_LABEL;
                          }
                          return ADD_CASH_CONFIRM_BTN_LABEL;
                        })()}
                      </CButton>
                    </CButtonGroup>
                  </div>
                </div>
                <Overlay
                  errorMessage={errorMessage}
                  showSpinner={showSpinner}
                  showSuccess={showSuccess}
                  showFailure={showFailure}
                  toggleSuccess={toggleSuccess}
                  toggleFailure={toggleFailure}
                  resetForm={resetForm}
                  selectedPaymentMethod={selectedPaymentMethod}
                  selectedPaymentType={selectedPaymentType}
                  toggleForm={toggleCashForm}
                  showForm={showCashForm}
                />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default CashPaymentForm;
