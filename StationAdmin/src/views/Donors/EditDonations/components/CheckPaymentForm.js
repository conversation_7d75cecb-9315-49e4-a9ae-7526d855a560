import { useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import moment from "moment";
import { startCase, toLower } from "lodash";
import { CButton, CButtonGroup, CCol, CRow } from "@coreui/react";
import {
  CHECK_FORM_HEADING_ADD,
  CHECK_FORM_HEADING_EDIT,
  CHECK_FORM_HEADING_DELETE,
  CHECK_FIELD_NAMES,
  CHECK_AMOUNT_LABEL,
  CHECK_NUMBER_LABEL,
  CHECK_DEPOSIT_DATE_LABEL,
  CHECK_STATUS_LABEL,
  CHECK_STATUS_OPTIONS,
  CHECK_PROCESSOR_LABEL,
  CHECK_PROCESSOR_OPTIONS,
  ADD_CHECK_CANCEL_BTN_LABEL,
  ADD_CHECK_CONFIRM_BTN_LABEL,
  EDIT_CHECK_CONFIRM_BTN_LABEL,
  DELETE_CHECK_CONFIRM_BTN_LABEL,
  CHEC<PERSON>_VALIDATION_AMOUNT_FAILED,
  CHECK_VALIDATION_NUMBER_FAILED,
  CHECK_VALIDATION_DATE_FAILED,
  CHECK_VALIDATION_PROCESSOR_FAILED,
} from "../strings";
import InputFlexible from "../../../Form/InputFlexible";
import DropdownFlexible from "../../../Form/DropdownFlexible";
import Overlay from "./Overlay";
import {
  useAddPaymentMutation,
  useDeletePaymentMutation,
  useEditPaymentMutation,
} from "../../../../api/kpfa.ts";
import SelectField from "../../../Form/SelectField";

const CheckPaymentForm = ({
  pledge,
  toggleCheckForm,
  toggleActionButtons,
  selectedPayment,
  updateSelectedPayment,
  selectedPaymentMethod,
  updateSelectedPaymentMethod,
  selectedPaymentType,
  updateSelectedPaymentType,
  showCheckForm,
  selectedDonor,
  setSelectedDonor,
  UpdatePledgeToEdit,
  refreshCurrentDonor,
  checkPaymentNumberExists, // New prop
}) => {
  const [showSpinner, toggleSpinner] = useState(false);
  const [showSuccess, toggleSuccess] = useState(false);
  const [showFailure, toggleFailure] = useState(false);
  const [errorMessage, setErrorMessage] = useState(false);
  const [dataUpdateCompleted, setDataUpdateCompleted] = useState(false);

  const [
    addPayment,
    { data: paymentResponse, error, isLoading, isSuccess, isError },
  ] = useAddPaymentMutation();

  const [
    deletePayment,
    {
      error: deleteError,
      isLoading: deleteIsLoading,
      isSuccess: deleteIsSuccess,
      isError: deleteIsError,
    },
  ] = useDeletePaymentMutation();

  const [
    editPayment,
    {
      data: editResponse,
      error: editError,
      isLoading: editIsLoading,
      isSuccess: editIsSuccess,
      isError: editIsError,
    },
  ] = useEditPaymentMutation();

  useEffect(() => {
    if (isLoading || deleteIsLoading || editIsLoading) {
      toggleSpinner(true);
    } else {
      toggleSpinner(false);
    }
  }, [isLoading, deleteIsLoading, editIsLoading]);

  useEffect(() => {
    if (isSuccess || deleteIsSuccess || editIsSuccess) {
      toggleSuccess(true);
    } else {
      toggleSuccess(false);
    }
    if (dataUpdateCompleted === false) {
      if (isSuccess || deleteIsSuccess || editIsSuccess) {
        let mutatedSelectedDonor = selectedDonor;
        let pledgeIndex = mutatedSelectedDonor.donations.findIndex(
          (obj) => obj.id === pledge.id,
        );
        let mutatedPayments =
          mutatedSelectedDonor.donations[pledgeIndex].payments;

        if (deleteIsSuccess) {
          //remove deleted payment from pledgeToEdit & selectedDonor
          mutatedPayments = mutatedPayments.filter(
            (payment) => payment.id !== selectedPayment.id,
          );
        } else if (isSuccess) {
          //add new payment object to pledgeToEdit & selectedDonor
          mutatedPayments.push(paymentResponse);
        } else if (editIsSuccess) {
          //todo: refactor this to use the API's response once that is wired up
          async function refreshDonor() {
            await refreshCurrentDonor();
            UpdatePledgeToEdit();
          }
          refreshDonor();
          console.log(editResponse);
        }
        mutatedSelectedDonor.donations[pledgeIndex].payments = mutatedPayments;
        setSelectedDonor(mutatedSelectedDonor);
        UpdatePledgeToEdit();
        setDataUpdateCompleted(true);
      }
    }
  }, [
    isSuccess,
    deleteIsSuccess,
    editIsSuccess,
    UpdatePledgeToEdit,
    setSelectedDonor,
    selectedPayment,
    selectedDonor,
    pledge,
    dataUpdateCompleted,
    paymentResponse,
    editResponse,
    refreshCurrentDonor,
  ]);

  useEffect(() => {
    if (isError || deleteIsError || editIsError) {
      toggleFailure(true);
    } else {
      toggleFailure(false);
    }
  }, [isError, deleteIsError, editIsError]);

  useEffect(() => {
    if (error) {
      setErrorMessage(error?.data.message);
    } else if (deleteError) {
      setErrorMessage(deleteError?.data.message);
    } else if (editError) {
      setErrorMessage(editError?.data.message);
    }
  }, [error, deleteError, editError]);

  const getInitialValues = () => {
    if (selectedPayment && selectedPaymentMethod) {
      return {
        checkAmount: selectedPayment.amount,
        checkNumber: selectedPayment.payment_id,
        checkDepositDate: moment(selectedPayment.date_deposited).format(
          "YYYY-MM-DD",
        ),
        checkStatus: startCase(toLower(selectedPayment.transaction_status)),
        checkProcessor: selectedPayment.processor,
      };
    }
    return {
      checkAmount: "",
      checkNumber: "",
      checkDepositDate: "",
      checkStatus: CHECK_STATUS_OPTIONS[0],
      checkProcessor: CHECK_PROCESSOR_OPTIONS[0],
    };
  };

  return (
    <div data-private>
      <h5 className="card-title mb-3">
        {(() => {
          if (selectedPaymentMethod === "DELETE") {
            return CHECK_FORM_HEADING_DELETE;
          } else if (selectedPaymentMethod === "PUT") {
            return CHECK_FORM_HEADING_EDIT;
          }
          return CHECK_FORM_HEADING_ADD;
        })()}
      </h5>
      <Formik
        initialValues={getInitialValues()}
        // validationSchema={supportFormSchema}
        onSubmit={async (values, { setSubmitting }) => {
          setSubmitting(true);

          if (!["DELETE", "PUT"].includes(selectedPaymentMethod)) {
            const exists = checkPaymentNumberExists(values.checkNumber);
            if (exists) {
              setErrorMessage(
                "This check payment number already exists for this donation.",
              );
              toggleFailure(true);
              setSubmitting(false);
              return;
            }
          }

          let body = {
            donation_id: pledge.id,
            payment_id: values.checkNumber,
            amount: Number(values.checkAmount),
            processor: values.checkProcessor,
            date_deposited: values.checkDepositDate,
            status: values.checkStatus.value
              ? values.checkStatus.value
              : toLower(values.checkStatus),
            method: "check",
          };
          if (!selectedPaymentMethod) {
            body.source = pledge.source;
            addPayment({ body });
          } else if (selectedPaymentMethod === "DELETE") {
            let id = selectedPayment.id;
            deletePayment({ id });
          } else if (selectedPaymentMethod === "PUT") {
            body.id = selectedPayment.id;
            editPayment({ body });
          }

          setSubmitting(false);
        }}
      >
        {({ handleSubmit, setFieldValue, setFieldTouched, isSubmitting }) => {
          const resetForm = () => {
            // reset field values
            setFieldValue("checkAmount", "");
            setFieldValue("checkNumber", "");
            setFieldValue("checkDepositDate", "");
            setFieldValue("checkStatus", CHECK_STATUS_OPTIONS[0]);
            setFieldValue("checkProcessor", CHECK_PROCESSOR_OPTIONS[0]);

            CHECK_FIELD_NAMES.forEach((field) => {
              setFieldTouched(field, false);
            });

            toggleCheckForm(false);
            toggleActionButtons(true);
            updateSelectedPayment(null);
            updateSelectedPaymentMethod(null);
            updateSelectedPaymentType(null);
          };

          return (
            <Form className="needs-validation" onSubmit={handleSubmit}>
              <div style={{ position: "relative" }}>
                <CRow>
                  <CCol>
                    <Field
                      name="checkAmount"
                      label={CHECK_AMOUNT_LABEL}
                      component={InputFlexible}
                      disabled={
                        selectedPaymentMethod === "DELETE" ? true : false
                      }
                      validate={(value) => {
                        if (Number(value) >= 0.01) {
                          return undefined;
                        }
                        return CHECK_VALIDATION_AMOUNT_FAILED;
                      }}
                    />
                  </CCol>
                  <CCol>
                    <Field
                      name="checkNumber"
                      label={CHECK_NUMBER_LABEL}
                      component={InputFlexible}
                      disabled={
                        selectedPaymentMethod === "DELETE" ? true : false
                      }
                      validate={(value) => {
                        if (Number(value) || Number(value) === 0) {
                          return undefined;
                        }
                        return CHECK_VALIDATION_NUMBER_FAILED;
                      }}
                    />
                  </CCol>
                  <CCol>
                    <Field
                      name="checkDepositDate"
                      label={CHECK_DEPOSIT_DATE_LABEL}
                      component={InputFlexible}
                      disabled={
                        selectedPaymentMethod === "DELETE" ? true : false
                      }
                      type="date"
                      validate={(value) => {
                        const regEx = /^\d{4}-\d{2}-\d{2}$/;
                        if (value.match(regEx)) {
                          return undefined;
                        }
                        return CHECK_VALIDATION_DATE_FAILED;
                      }}
                    />
                  </CCol>
                </CRow>
                <CRow className="my-3">
                  <CCol>
                    <Field
                      name="checkStatus"
                      label={CHECK_STATUS_LABEL}
                      component={SelectField}
                      disabled={
                        selectedPaymentMethod === "DELETE" ? true : false
                      }
                      options={CHECK_STATUS_OPTIONS}
                      labelPlacement="top"
                      defaultValue={
                        selectedPayment
                          ? CHECK_STATUS_OPTIONS.find(
                              (option) =>
                                option.value ===
                                selectedPayment.transaction_status,
                            )
                          : CHECK_STATUS_OPTIONS.find(
                              (option) => option.value === "succeeded",
                            )
                      }
                    />
                  </CCol>
                  <CCol>
                    <Field
                      name="checkProcessor"
                      label={CHECK_PROCESSOR_LABEL}
                      component={DropdownFlexible}
                      disabled={
                        selectedPaymentMethod === "DELETE" ? true : false
                      }
                      options={CHECK_PROCESSOR_OPTIONS}
                      validate={(value) => {
                        if (CHECK_PROCESSOR_OPTIONS.includes(value)) {
                          return undefined;
                        }
                        return CHECK_VALIDATION_PROCESSOR_FAILED;
                      }}
                    />
                  </CCol>
                </CRow>
                <CRow>
                  <CCol>
                    <CButtonGroup className="me-auto mt-3">
                      <CButton variant="outline" onClick={resetForm}>
                        {ADD_CHECK_CANCEL_BTN_LABEL}
                      </CButton>
                      <CButton type="submit" disabled={isSubmitting}>
                        {(() => {
                          if (selectedPaymentMethod === "DELETE") {
                            return DELETE_CHECK_CONFIRM_BTN_LABEL;
                          } else if (selectedPaymentMethod === "PUT") {
                            return EDIT_CHECK_CONFIRM_BTN_LABEL;
                          }
                          return ADD_CHECK_CONFIRM_BTN_LABEL;
                        })()}
                      </CButton>
                    </CButtonGroup>
                  </CCol>
                </CRow>
                <Overlay
                  showSpinner={showSpinner}
                  showSuccess={showSuccess}
                  showFailure={showFailure}
                  toggleSuccess={toggleSuccess}
                  toggleFailure={toggleFailure}
                  resetForm={resetForm}
                  selectedPaymentMethod={selectedPaymentMethod}
                  selectedPaymentType={selectedPaymentType}
                  toggleForm={toggleCheckForm}
                  showForm={showCheckForm}
                  errorMessage={errorMessage}
                />
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default CheckPaymentForm;
