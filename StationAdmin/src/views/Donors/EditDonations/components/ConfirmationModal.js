import React from "react";
import {
  <PERSON>utton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CAlert,
  CButtonGroup,
} from "@coreui/react";

import DisplayFormData from "./DisplayFormData";
import { modalActions, modalFlowStr } from "../strings";

const ConfirmationModal = ({
  formData,
  backToEditModal,
  confirmDonation,
  modalAction,
  closeModal,
  selectedDonor,
}) => {
  return (
    <CModal
      visible={true}
      onClose={modalAction === modalActions.edit ? backToEditModal : closeModal}
      backdrop="static"
      keyboard={false}
    >
      <CModalHeader
        onClose={
          modalAction === modalActions.edit ? backToEditModal : closeModal
        }
      >
        {modalFlowStr[modalAction].confirmationHeader}
      </CModalHeader>
      <CModalBody>
        {modalAction === modalActions.delete ? (
          <CAlert color="danger">{modalFlowStr.delete.confirmationBody}</CAlert>
        ) : null}
        <DisplayFormData donor={selectedDonor} formData={formData} />
      </CModalBody>
      <CModalFooter>
        <CButtonGroup className="me-auto">
          <CButton
            variant="outline"
            onClick={
              modalAction === modalActions.edit ? backToEditModal : closeModal
            }
          >
            {modalFlowStr[modalAction].confirmationBtnCancel}
          </CButton>
          <CButton
            color={modalAction === modalActions.edit ? "primary" : "danger"}
            onClick={confirmDonation}
          >
            {modalFlowStr[modalAction].confirmationBtnConfirm}
          </CButton>
        </CButtonGroup>
      </CModalFooter>
    </CModal>
  );
};

export default ConfirmationModal;
