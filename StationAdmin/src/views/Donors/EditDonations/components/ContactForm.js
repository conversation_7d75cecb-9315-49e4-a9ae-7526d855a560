import React, { useState } from "react";
import {
  CTabContent,
  CTabPane,
  CNav,
  CNavItem,
  CNavLink,
  CModalBody,
} from "@coreui/react";
import DonationFormPane from "./DonationFormPane";
import PaymentsPane from "./PaymentsPane";
import ShipmentsPane from "./ShipmentsPane";

const ContactForm = ({
  pledgeToEdit,
  selectedDonor,
  setSelectedDonor,
  premiums,
  selectedPremiums,
  handlePledgeUpdateSubmit,
  formValues,
  closeModal,
  UpdatePledgeToEdit,
  refreshCurrentDonor,
  premiumSelectOptions,
  handleAddPremium,
  handleRemovePremium,
}) => {
  const [activeKey, setActiveKey] = useState(1);

  return (
    <>
      <CNav variant="tabs" role="tablist" className="mt-3">
        <CNavItem>
          <CNavLink
            role="button"
            active={activeKey === 1}
            onClick={() => {
              setActiveKey(1);
            }}
          >
            Donation
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            role="button"
            active={activeKey === 2}
            onClick={() => {
              setActiveKey(2);
            }}
          >
            Payments
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            role="button"
            active={activeKey === 3}
            onClick={() => {
              setActiveKey(3);
            }}
          >
            Shipments
          </CNavLink>
        </CNavItem>
      </CNav>

      <CModalBody className="p-3">
        <CTabContent className="py-3">
          <CTabPane
            role="tabpabel"
            visible={activeKey === 1}
            aria-labelledby="donation-tab"
          >
            <DonationFormPane
              pledge={pledgeToEdit}
              selectedPremiums={selectedPremiums}
              handlePledgeUpdateSubmit={handlePledgeUpdateSubmit}
              formValues={formValues}
              closeModal={closeModal}
            />
          </CTabPane>
          <CTabPane
            role="tabpabel"
            visible={activeKey === 2}
            aria-labelledby="payments-tab"
          >
            <PaymentsPane
              UpdatePledgeToEdit={UpdatePledgeToEdit}
              values={pledgeToEdit}
              pledge={pledgeToEdit}
              selectedDonor={selectedDonor}
              setSelectedDonor={setSelectedDonor}
              refreshCurrentDonor={refreshCurrentDonor}
            />
          </CTabPane>
          <CTabPane
            role="tabpabel"
            visible={activeKey === 3}
            aria-labelledby="shipments-tab"
          >
            <ShipmentsPane
              pledge={pledgeToEdit}
              selectedDonor={selectedDonor}
              selectedPremiums={selectedPremiums}
              premiums={premiums}
              handlePledgeUpdateSubmit={handlePledgeUpdateSubmit}
              formValues={formValues}
              closeModal={closeModal}
              premiumSelectOptions={premiumSelectOptions}
              handleAddPremium={handleAddPremium}
              handleRemovePremium={handleRemovePremium}
            />
          </CTabPane>
        </CTabContent>
      </CModalBody>
    </>
  );
};

export default ContactForm;
