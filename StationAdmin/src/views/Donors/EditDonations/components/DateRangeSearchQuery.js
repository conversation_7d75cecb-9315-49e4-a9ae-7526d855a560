import React, { useState } from "react";
import moment from "moment";

const DateRangeSearchQuery = ({ handleSearch }) => {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  return (
    <>
      <div className="row">
        <div className="col-md-6">
          <div className="form-group">
            <label htmlFor="date-range-search-start">Date Range Start</label>
            <input
              type="date"
              className="form-control"
              id="date-range-search-start"
              value={startDate}
              onChange={e => {
                const start = e.target.value;
                const end = endDate ? endDate : null;
                if (!end || (end && moment(start).isBefore(moment(end)))) {
                  setStartDate(e.target.value);
                  if (start && end) {
                    const searchString = `?start=${start}&end=${end}`;
                    const searchText = `${start} ${end}`;
                    handleSearch(searchText, searchString);
                  }
                } else {
                  setStartDate("");
                  handleSearch("", "");
                }
              }}
            />
          </div>
        </div>
        <div className="col-md-6">
          <div className="form-group">
            <label htmlFor="date-range-search-end">Date Range End</label>
            <input
              type="date"
              className="form-control"
              id="date-range-search-end"
              value={endDate}
              onChange={e => {
                const end = e.target.value;
                const start = startDate ? startDate : null;
                if (!start || (start && moment(end).isAfter(moment(start)))) {
                  setEndDate(e.target.value);
                  if (start && end) {
                    const searchString = `?start=${start}&end=${end}`;
                    const searchText = `${start} ${end}`;
                    handleSearch(searchText, searchString);
                  }
                } else {
                  setEndDate("");
                  handleSearch("", "");
                }
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default DateRangeSearchQuery;
