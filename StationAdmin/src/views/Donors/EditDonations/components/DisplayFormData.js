import React from "react";
import { listPremiums } from "../../../../helpers";

const DisplayFormData = (props) => {
  return (
    <ul className="ps-0" style={{ listStyle: "none" }}>
      <h2 className="text-danger font-weight-bold">
        ${props.formData.amount} {props.formData.installment}
      </h2>
      {props.formData.shipping_firstname ? (
        <li>
          <strong>Shipping First Name:</strong>{" "}
          {props.formData.shipping_firstname}
        </li>
      ) : null}
      {props.formData.shipping_lastname ? (
        <li>
          <strong>Shipping Last Name:</strong>{" "}
          {props.formData.shipping_lastname}
        </li>
      ) : null}
      {props.formData.shipping_address1 ? (
        <li>
          <strong>Shipping Address1:</strong> {props.formData.shipping_address1}
        </li>
      ) : null}
      {props.formData.shipping_address2 ? (
        <li>
          <strong>Shipping Address2:</strong> {props.formData.shipping_address2}
        </li>
      ) : null}
      {props.formData.shipping_city ? (
        <li>
          <strong>Shipping City:</strong> {props.formData.shipping_city}
        </li>
      ) : null}
      {props.formData.shipping_state ? (
        <li>
          <strong>Shipping State:</strong> {props.formData.shipping_state}
        </li>
      ) : null}
      {props.formData.shipping_country ? (
        <li>
          <strong>Shipping Country:</strong> {props.formData.shipping_country}
        </li>
      ) : null}
      {props.formData.shipping_postal_code ? (
        <li>
          <strong>Shipping Zip/Postal Code:</strong>{" "}
          {props.formData.shipping_postal_code}
        </li>
      ) : null}
      <li>
        <strong>Donation Type:</strong> {props.formData.type}
      </li>
      {props.formData.comments ? (
        <li>
          <strong>Comments:</strong> {props.formData.comments}
        </li>
      ) : null}
      {props.formData.read_onair ? (
        <li>
          <strong>Read On-air:</strong> {props.formData.read_onair.toString()}
        </li>
      ) : null}
      {props.formData.donation_match ? (
        <li>
          <strong>Donation Match:</strong>{" "}
          {props.formData.donation_match.toString()}
        </li>
      ) : null}
      {props.formData.premiums ? (
        <li>
          <strong>Premiums:</strong> {listPremiums(props.formData.premiums)}
        </li>
      ) : null}
      {/* <li>
        <strong>Status:</strong> {props.formData.status}
      </li> */}
      {props.formData.campaign_id ? (
        <li>
          <strong>Campaign ID:</strong> {props.formData.campaign_id}
        </li>
      ) : null}
      <li>
        <strong>Donation ID:</strong> {props.formData.donation_id}
      </li>
      <li>
        <strong>Transaction ID:</strong>{" "}
        {props.formData.transaction_id.toString(",")}
      </li>
      <li>
        <strong>Source:</strong> {props.formData.source}
      </li>
    </ul>
  );
};

export default DisplayFormData;
