import { CBadge, CButton, CButtonGroup } from "@coreui/react";
import { listPremiums, shortenTimestamp } from "../../../../helpers";
import { useAppSelector } from "../../../../hooks";
import { getPledgePaymentSummary } from "./DisplayResultsUtils";
import Email from "./Email";

const DisplayResults = (props) => {
  const testMode = useAppSelector((state) => state.testMode);
  const accessLevel = useAppSelector((state) => state.user.accessLevel);

  if (props.isLoading) {
    return (
      <div className="row mt-5 pt-5 mb-5 pb-5 text-center">
        <div className="col-sm-12">
          <div
            className="spinner-border text-info"
            style={{ width: "3rem", height: "3rem" }}
            role="status"
          >
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      </div>
    );
  }
  if (props.results.length > 0) {
    const results = [...props.results];
    const startIndex = (props.currentPage - 1) * props.resultsPerPage;
    const resultsSlice = results.slice(
      startIndex,
      startIndex + props.resultsPerPage,
    );

    const tableRows = resultsSlice.map((pledge, index) => {
      const getBadgeClassName = () => {
        if (pledge.status && pledge.status.toLowerCase() === "paid") {
          return "success";
        }
        if (
          pledge.status &&
          (pledge.status.toLowerCase() === "unpaid" ||
            pledge.status.toLowerCase() === "partially paid")
        ) {
          return "warning";
        }
        if (pledge.status && pledge.status.toLowerCase() === "canceled") {
          return "danger";
        }
        if (
          pledge.status &&
          (pledge.status.toLowerCase() === "refunded" ||
            pledge.status.toLowerCase() === "partially refunded")
        ) {
          return "info";
        }
      };

      return (
        <div className="row mt-3 mb-3" key={"row" + index}>
          <div className="col-sm-1">
            <strong>{"$" + pledge.amount}</strong>
          </div>
          <div className="col-sm-1">
            <p>{shortenTimestamp(pledge.timestamp)}</p>
          </div>
          <div className="col-sm-1">
            <p>{pledge.id}</p>
          </div>
          <div className="col-sm-3">
            <h5>
              <CBadge color={getBadgeClassName()}>
                {pledge.status === "Unpaid" &&
                pledge.source === "WebSite" &&
                !pledge.payments.length
                  ? "Abandoned"
                  : pledge.status
                    ? pledge.status.charAt(0).toUpperCase() +
                      pledge.status.slice(1)
                    : "Unknown"}
              </CBadge>
              <CBadge
                className="mx-1"
                color={
                  pledge.installment === "Monthly" ? "primary" : "secondary"
                }
              >
                {pledge.installment}
              </CBadge>
              {pledge.subscriptions.length ? (
                <CBadge
                  color={pledge.subscriptions[0].active ? "info" : "secondary"}
                >
                  {pledge.subscriptions[0].active ? "Active" : "Inactive"}
                </CBadge>
              ) : null}
            </h5>
          </div>
          <div className="text-capitalize col-sm-1">
            {getPledgePaymentSummary(pledge.payments)}
          </div>
          <div className="col-sm-2">
            {pledge.premiums !== null ? (
              <p className="premiums-list">{listPremiums(pledge.premiums)}</p>
            ) : null}
          </div>
          <div className="col-sm-3">
            <CButtonGroup size="sm">
              {accessLevel !== "CallCenter" ? (
                <CButton
                  color="primary"
                  id={pledge.id}
                  onClick={props.handleEditModal}
                >
                  Edit
                </CButton>
              ) : null}
              {/* {pledge.status && pledge.status.toLowerCase() === "unpaid" ? (
                <a
                  className="btn btn-primary btn-sm me-1 mt-1"
                  href={`https://api.kpfa.org/donation/pay/${pledge.transaction_id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Pay
                </a>
              ) : null} */}
              <CButton
                color="primary"
                className="border-secondary"
                variant="outline"
                href={`https://${
                  testMode.value ? "api.staging." : "api."
                }kpfa.org/receipts/${pledge.transaction_id}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                View Receipt
              </CButton>
              {props.selectedDonor?.email ? (
                <Email
                  emailAddress={props.selectedDonor.email}
                  pledge={pledge}
                />
              ) : null}
              {accessLevel !== "CallCenter" && pledge.payments.length === 0 ? (
                <CButton
                  color="danger"
                  className="border-secondary text-white"
                  data-id={pledge.id}
                  onClick={props.handleDeleteModal}
                >
                  Delete
                </CButton>
              ) : null}
              {accessLevel !== "CallCenter" &&
              pledge.subscriptions[0]?.active === true ? (
                <CButton
                  color="danger"
                  className="border-secondary text-white"
                  data-id={pledge.id}
                  onClick={props.handleCancelModal}
                >
                  Cancel
                </CButton>
              ) : null}
            </CButtonGroup>
          </div>
        </div>
      );
    });
    return tableRows;
  }
  return (
    <div className="row">
      <div className="col-sm-12 text-center">
        <h4>No Results to Display</h4>
      </div>
    </div>
  );
};

export default DisplayResults;
