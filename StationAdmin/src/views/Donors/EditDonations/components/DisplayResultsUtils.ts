import { Payment } from "../../../../types";

export function getPledgePaymentSummary(payments: Payment[]): string {
  if (!payments || payments.length === 0) {
    return "";
  }

  const firstPayment = payments[0];
  const method = firstPayment?.method || "";
  const processor = method === "check" ? firstPayment.processor : method;
  const paymentId = method === "check" ? `#${firstPayment.payment_id}` : "";
  const additionalPayments =
    payments.length > 1 ? `+ ${payments.length - 1}` : "";

  return `${processor} ${paymentId} ${additionalPayments}`.trim();
}
