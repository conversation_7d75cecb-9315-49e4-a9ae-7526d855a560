import { useState } from "react";
import { Formik, Form, Field } from "formik";
import { CButton, CButtonGroup } from "@coreui/react";
import ReactAudioPlayer from "react-audio-player";

import Input from "../../../Form/Input";
import Checkbox from "../../../Form/Checkbox";
import Textarea from "../../../Form/Textarea";
import { pledgeTypes, donationType } from "../../../Form/constants";
import supportFormSchema from "../../../Form/validationSchema";
import SelectField from "../../../Form/SelectField";
import { useAppSelector } from "../../../../hooks";

const DonationFormPane = (props) => {
  const campaignOptions = useAppSelector(
    (state) => state.campaignOptions.value,
  );

  const {
    pledge,
    selectedPremiums,
    handlePledgeUpdateSubmit,
    formValues,
    closeModal,
  } = props;

  const [amount, setAmount] = useState(Number(pledge.amount));
  const [installment, setInstallment] = useState(pledge.installment);

  const tempFormValues = formValues;
  const initialPledgeValues = {
    campaignID: pledge.campaign_id || "",
    amount: Number(pledge.amount),
    pledgeType: pledge.installment,
    donationType: pledge.type,
    showShippingAddress: pledge.shipping_address1 ? true : false,
    firstNameShipping: pledge.shipping_address1
      ? pledge.shipping_firstname
      : "",
    lastNameShipping: pledge.shipping_address1 ? pledge.shipping_lastname : "",
    address1Shipping: pledge.shipping_address1 ? pledge.shipping_address1 : "",
    address2Shipping: pledge.shipping_address2 ? pledge.shipping_address2 : "",
    cityShipping: pledge.shipping_address1 ? pledge.shipping_city : "",
    stateShipping: pledge.shipping_address1 ? pledge.shipping_state : "",
    zipShipping: pledge.shipping_address1 ? pledge.shipping_postal_code : "",
    countryShipping: pledge.shipping_address1 ? pledge.shipping_country : "",
    comment: pledge.comments || "",
    readOnair: pledge.read_onair,
    donationMatch: pledge.donation_match,
    overridePremiums: false,
    premiums: selectedPremiums,
  };

  function getInitialValues() {
    if (
      Object.entries(tempFormValues).length !== 0 &&
      tempFormValues.constructor === Object
    ) {
      return tempFormValues;
    }
    return initialPledgeValues;
  }

  return (
    <>
      <Formik
        initialValues={getInitialValues()}
        validationSchema={supportFormSchema(installment, amount)}
        onSubmit={(values, { setSubmitting }) => {
          handlePledgeUpdateSubmit(values);

          setSubmitting(false);
        }}
      >
        {({ values, handleSubmit, setFieldValue }) => {
          return (
            <Form className="needs-validation" onSubmit={handleSubmit}>
              <Field
                name="campaignID"
                label="Assign to Campaign"
                component={SelectField}
                options={campaignOptions}
                onChange={(event) => {
                  if (!event.value && values.pledgeType === "One-Time") {
                    if (values.amount < 1000) {
                      setFieldValue("donationType", "Minor");
                    } else {
                      setFieldValue("donationType", "Major");
                    }
                  }
                }}
              />
              <Field
                name="amount"
                label="Amount"
                component={Input}
                onChange={(event) => {
                  const eValue = event.target.value;
                  if (!isNaN(Number(eValue))) {
                    const amount = (() => {
                      if (eValue[eValue.length - 1] === ".") {
                        return Number(eValue) + ".";
                      } else if (Math.floor(Number(eValue)) < Number(eValue)) {
                        if (
                          String(Number(eValue)).length -
                            Number(eValue).toFixed(0).length ===
                          2
                        ) {
                          return eValue;
                        }
                        return eValue.match(/^-?\d+(?:\.\d{0,2})?/)[0];
                      } else if (
                        Number(Number(eValue).toFixed(2)) === Number(eValue) &&
                        String(Math.floor(Number(eValue))).length !==
                          eValue.length
                      ) {
                        return eValue;
                      }
                      return String(Number(eValue));
                    })();
                    setFieldValue("amount", amount);
                    setAmount(amount);
                  }
                }}
                onBlur={(event) => {
                  const eValue = event.target.value;
                  if (eValue[eValue.length - 1] === ".") {
                    setFieldValue("amount", String(Number(eValue)));
                    return;
                  } else if (Math.floor(Number(eValue)) < Number(eValue)) {
                    if (
                      eValue.length -
                        String(Math.floor(Number(eValue))).length ===
                      2
                    ) {
                      setFieldValue("amount", eValue + "0");
                      return;
                    }
                    return;
                  } else if (Math.floor(Number(eValue)) === Number(eValue)) {
                    setFieldValue("amount", String(Number(eValue)));
                    return;
                  }
                  return;
                }}
              />
              <Field
                name="pledgeType"
                label="Installment"
                component={SelectField}
                options={pledgeTypes}
                onChange={(event) => {
                  setInstallment(event);
                  console.log(values.campaignID);
                  console.log(event);
                  console.log(amount);
                  if (event === "One-Time" && !values.campaignID.length) {
                    if (values.amount < 1000) {
                      setFieldValue("donationType", "Minor");
                    } else {
                      setFieldValue("donationType", "Major");
                    }
                  }
                }}
              />
              {values.campaignID || values.pledgeType === "Monthly" ? null : (
                <Field
                  name="donationType"
                  label="Donation Type"
                  component={SelectField}
                  options={donationType}
                />
              )}
              {pledge.recording_url ? (
                <div className="row form-group">
                  <label className="col-form-label col-sm-3">Recording</label>
                  <div className="col-sm-9">
                    <ReactAudioPlayer
                      src={pledge.recording_url}
                      controls
                      className="w-100"
                    />
                  </div>
                </div>
              ) : null}
              <Field
                component={Textarea}
                name="comment"
                label="Comments (500 character limit)"
              />
              <Field
                component={Checkbox}
                label="Can we mention your first name on the air?"
                name="readOnair"
                checked={values.readOnair}
                className="my-3"
              />
              <Field
                component={Checkbox}
                label="Can we use this donation as a match?"
                name="donationMatch"
                checked={values.donationMatch}
                className="mb-3"
              />
              <CButtonGroup>
                <CButton variant="outline" onClick={closeModal}>
                  Cancel
                </CButton>
                <CButton color="primary" type="submit">
                  Submit Donation Changes
                </CButton>
              </CButtonGroup>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default DonationFormPane;
