import React, { useEffect, useState } from "react";
import { CModal, CModalHeader, CBadge } from "@coreui/react";
import { cloneDeep } from "lodash";

import ContactForm from "./ContactForm";
import { useGetPremiumsQuery } from "../../../../api/kpfa.ts";

const EditModal = (props) => {
  const [premiumDropdown, setPremiumDropdown] = useState([]);
  const [premiums, setPremiums] = useState([]);
  const { data: premiumData } = useGetPremiumsQuery();

  useEffect(() => {
    if (premiumData?.records) {
      const clonedPremiums = cloneDeep(premiumData.records);
      const dropdownItems = [];
      clonedPremiums.forEach((item) => {
        const { id, name, price, variants } = item;
        if (name && !variants) {
          dropdownItems.push({
            value: id.toString(),
            label: `${name} - $${price}`,
          });
          return;
        }
        if (name && variants) {
          variants.variations.forEach((variant) => {
            dropdownItems.push({
              value: variant.id.toString(),
              label: `${name} (${variant.name}) - $${price}`,
            });
            return;
          });
          return;
        }
      });
      setPremiums(premiumData.records);
      setPremiumDropdown(dropdownItems);
    }
  }, [premiumData]);

  return (
    <CModal
      visible={true}
      backdrop={true}
      keyboard={true}
      size="lg"
      onClose={props.closeModal}
    >
      <CModalHeader>
        Edit Donation: {props.pledgeToEdit.id} ({props.pledgeToEdit.timestamp}){" "}
        <CBadge className="mx-2" color="secondary">
          {props.pledgeToEdit.source}
        </CBadge>
      </CModalHeader>
      <ContactForm
        pledgeToEdit={props.pledgeToEdit}
        selectedPremiums={props.selectedPremiums}
        handlePledgeUpdateSubmit={props.handlePledgeUpdateSubmit}
        formValues={props.formValues}
        premiums={premiums}
        handleRemovePremium={props.handleRemovePremium}
        premiumSelectOptions={premiumDropdown}
        handleAddPremium={props.handleAddPremium}
        selectedDonor={props.selectedDonor}
        setSelectedDonor={props.setSelectedDonor}
        refreshCurrentDonor={props.refreshCurrentDonor}
        UpdatePledgeToEdit={props.UpdatePledgeToEdit}
        closeModal={props.closeModal}
      />
    </CModal>
  );
};

export default EditModal;
