import { CButton } from "@coreui/react";
import React, { useState } from "react";

import EmailModal from "./EmailModal";

const Email = ({ emailAddress, pledge }) => {
  const [showEmailConfirmationModal, toggleEmailConfirmationModal] = useState(
    false
  );
  const [emailPayload, updateEmailPayload] = useState(null);

  return (
    <>
      <CButton
        color="primary"
        className="border-secondary"
        variant="outline"
        // href={`https://api.kpfa.org/email/${pledge.transaction_id}`}
        // target="_blank"
        // rel="noopener noreferrer"
        data-transaction={pledge.transaction_id}
        onClick={(e) => {
          toggleEmailConfirmationModal(true);
          updateEmailPayload({
            type: "thankyou",
            transaction_ids: [e.currentTarget.dataset.transaction],
          });
        }}
      >
        Email Receipt
      </CButton>
      <EmailModal
        emailAddress={emailAddress}
        pledge={pledge}
        showEmailConfirmationModal={showEmailConfirmationModal}
        toggleEmailConfirmationModal={toggleEmailConfirmationModal}
        emailPayload={emailPayload}
      />
    </>
  );
};

export default Email;
