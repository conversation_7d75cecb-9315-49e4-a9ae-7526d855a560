import React, { useState } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
} from "@coreui/react";

import checkmark from "../images/checkmark.svg";
import { useAppSelector } from "../../../../hooks";

const EmailModal = ({
  emailAddress,
  pledge,
  showEmailConfirmationModal,
  toggleEmailConfirmationModal,
  emailPayload,
}) => {
  const [showEmailSendingModal, toggleEmailSendingModal] = useState(false);
  const [showEmailSuccessModal, toggleEmailSuccessModal] = useState(false);
  const [showEmailFailureModal, toggleEmailFailureModal] = useState(false);
  const [emailResponse, SetEmailResponse] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const testMode = useAppSelector((state) => state.testMode);

  const closeModals = () => {
    toggleEmailSendingModal(false);
    toggleEmailSuccessModal(false);
    toggleEmailFailureModal(false);
    toggleEmailConfirmationModal(false);
  };

  const confirmSendEmail = () => {
    toggleEmailConfirmationModal(false);
    toggleEmailSendingModal(true);

    const url = `https://${
      testMode.value ? "api.staging." : "api."
    }kpfa.org/email`;

    const headers = {
      Authorization: `Bearer ${localStorage.getItem("jwt")}`,
      "Content-Type": "application/json",
      Accept: "application/json",
    };
    if (submitting === false) {
      setSubmitting(true);

      fetch(url, {
        method: "POST",
        body: JSON.stringify(emailPayload),
        headers,
      })
        .then((response) => response.json())
        .then((response) => {
          console.log(response);
          if (response.status === "error") {
            SetEmailResponse(response);
            setSubmitting(false);
            toggleEmailSendingModal(false);
            toggleEmailFailureModal(true);
          }
          if (response.status === "success") {
            SetEmailResponse(response);
            setSubmitting(false);
            toggleEmailSendingModal(false);
            toggleEmailSuccessModal(true);
          }
        })
        .catch((response) => {
          console.log(response);
          SetEmailResponse(response);
          toggleEmailSendingModal(false);
          toggleEmailFailureModal(true);
        });
    }
  };

  if (showEmailConfirmationModal) {
    return (
      <CModal
        visible={true}
        onClose={closeModals}
        backdrop="static"
        keyboard={false}
      >
        <CModalHeader>Email Confirmation</CModalHeader>
        <CModalBody>
          Send Thank You Email to <strong>{emailAddress}</strong>?
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={closeModals}>
            Cancel
          </CButton>
          <CButton color="primary" onClick={confirmSendEmail}>
            Send Email
          </CButton>
        </CModalFooter>
      </CModal>
    );
  } else if (showEmailSendingModal) {
    return (
      <CModal visible={true} backdrop="static" keyboard={false}>
        <CModalHeader>
          <span className="badge badge-warning">Please Wait</span>
        </CModalHeader>
        <CModalBody>
          <div className="d-flex justify-content-center mt-2 mb-2">
            <h3>
              <div
                className="spinner-border text-primary"
                style={{
                  width: "2rem",
                  height: "2rem",
                  marginRight: "0.5rem",
                }}
                role="status"
              >
                <span className="sr-only">Sending Email...</span>
              </div>
              Sending Email...
            </h3>
          </div>
          <p>
            Please do not close this window or navigate away from this page
            until your request has been processed and the confirmation message
            is displayed.
          </p>
        </CModalBody>
      </CModal>
    );
  } else if (showEmailSuccessModal) {
    return (
      <CModal
        visible={true}
        onClose={closeModals}
        backdrop={true}
        keyboard={true}
      >
        <CModalHeader>
          <span className="badge badge-success">Success</span>
        </CModalHeader>
        <CModalBody>
          <div className="checkmark-container">
            <img src={checkmark} className="bounceIn" alt="checkmark" />
            <p>
              <strong>{emailResponse.message}</strong>
            </p>
          </div>
        </CModalBody>
        <CModalFooter>
          <CButton color="primary" onClick={closeModals}>
            Return to Edit Donations
          </CButton>
        </CModalFooter>
      </CModal>
    );
  } else if (showEmailFailureModal) {
    return (
      <CModal
        visible={true}
        onClose={closeModals}
        backdrop={true}
        keyboard={true}
      >
        <CModalHeader>
          <span className="badge badge-danger">Error</span> Email Failed
        </CModalHeader>
        <CModalBody>
          <p>The Email was not sent successfully.</p>
          {emailResponse.message ? (
            <React.Fragment>
              <p>
                <strong>{emailResponse.message}</strong>
              </p>
            </React.Fragment>
          ) : null}
          {!emailResponse.message ? (
            <p>
              Error reaching KPFA server. Please check your internet connection
              and try again.
            </p>
          ) : null}
        </CModalBody>
        <CModalFooter>
          <CButton color="primary" onClick={closeModals}>
            Return to Edit Donations
          </CButton>
        </CModalFooter>
      </CModal>
    );
  }
  return null;
};

export default EmailModal;
