import { useState } from "react";
import { Formik, Form, Field } from "formik";
import moment from "moment";
import { CButton, CButtonGroup, CCol, CRow } from "@coreui/react";
import InputFlexible from "../../../Form/InputFlexible";
import {
  useAddPaymentMutation,
  useDeletePaymentMutation,
  useEditPaymentMutation,
} from "../../../../api/kpfa.ts";
import SelectField from "../../../Form/SelectField";

// Constants for epayment form
const EPAYMENT_FORM_HEADING_ADD = "Add Epayment";
const EPAYMENT_FORM_HEADING_EDIT = "Edit Epayment";
const EPAYMENT_FORM_HEADING_DELETE = "Delete Epayment";
const EPAYMENT_FIELD_NAMES = {
  amount: "amount",
  payment_id: "payment_id",
  date_deposited: "date_deposited",
  status: "status",
  processor: "processor",
};
const EPAYMENT_AMOUNT_LABEL = "Amount";
const EPAYMENT_ID_LABEL = "Payment ID";
const EPAYMENT_DEPOSIT_DATE_LABEL = "Deposit Date";
const EPAYMENT_STATUS_LABEL = "Status";
const EPAYMENT_STATUS_OPTIONS = [
  { value: "succeeded", label: "Succeeded" },
  { value: "pending", label: "Pending" },
  { value: "failed", label: "Failed" },
];
const EPAYMENT_PROCESSOR_LABEL = "Processor";
const EPAYMENT_PROCESSOR_OPTIONS = [
  { value: "Venmo", label: "Venmo" },
  { value: "PayPal", label: "PayPal" },
  { value: "DipJar", label: "DipJar" },
  { value: "Staff", label: "Staff" },
];
const ADD_EPAYMENT_CANCEL_BTN_LABEL = "Cancel";
const ADD_EPAYMENT_CONFIRM_BTN_LABEL = "Add Epayment";
const EDIT_EPAYMENT_CONFIRM_BTN_LABEL = "Update Epayment";
const DELETE_EPAYMENT_CONFIRM_BTN_LABEL = "Delete Epayment";

const EpaymentForm = ({
  pledge,
  toggleEpaymentForm,
  toggleActionButtons,
  selectedPayment,
  updateSelectedPayment,
  selectedPaymentMethod,
  updateSelectedPaymentMethod,
  selectedPaymentType,
  updateSelectedPaymentType,
  showEpaymentForm,
  selectedDonor,
  setSelectedDonor,
  UpdatePledgeToEdit,
}) => {
  const [addPayment] = useAddPaymentMutation();
  const [editPayment] = useEditPaymentMutation();
  const [deletePayment] = useDeletePaymentMutation();

  const [isLoading, setIsLoading] = useState(false);

  const initialValues = {
    amount: selectedPayment?.amount || "",
    payment_id: selectedPayment?.payment_id || "",
    date_deposited: selectedPayment?.date_deposited
      ? moment(selectedPayment.date_deposited).format("YYYY-MM-DD")
      : moment().format("YYYY-MM-DD"),
    status: selectedPayment?.status || "succeeded",
    processor: selectedPayment?.processor || "Venmo",
  };

  const validate = (values) => {
    const errors = {};
    if (!values.amount || values.amount <= 0) {
      errors.amount = "Amount is required and must be greater than 0";
    }
    if (!values.payment_id) {
      errors.payment_id = "Payment ID is required";
    }
    if (!values.date_deposited) {
      errors.date_deposited = "Deposit date is required";
    }
    if (!values.processor) {
      errors.processor = "Processor is required";
    }
    return errors;
  };

  const onSubmit = async (values, { setSubmitting }) => {
    setIsLoading(true);
    try {
      const paymentData = {
        donation_id: pledge.id,
        amount: parseFloat(values.amount),
        payment_id: values.payment_id,
        method: "epayment",
        processor: values.processor,
        date_deposited: values.date_deposited,
        status: values.status,
      };

      let result;
      if (selectedPaymentMethod === "PUT") {
        // Edit existing payment
        result = await editPayment({
          id: selectedPayment.id,
          ...paymentData,
        });
      } else if (selectedPaymentMethod === "DELETE") {
        // Delete payment
        result = await deletePayment({ id: selectedPayment.id });
      } else {
        // Add new payment
        result = await addPayment(paymentData);
      }

      if (result.error) {
        console.error("Payment operation failed:", result.error);
      } else {
        // Update frontend state immediately for delete
        if (selectedPaymentMethod === "DELETE" && setSelectedDonor && selectedDonor) {
          let mutatedSelectedDonor = { ...selectedDonor };
          let pledgeIndex = mutatedSelectedDonor.donations.findIndex(
            (obj) => obj.id === pledge.id,
          );

          if (pledgeIndex !== -1) {
            let mutatedPayments = mutatedSelectedDonor.donations[pledgeIndex].payments.filter(
              (payment) => payment.id !== selectedPayment.id,
            );
            mutatedSelectedDonor.donations[pledgeIndex].payments = mutatedPayments;
            setSelectedDonor(mutatedSelectedDonor);
            UpdatePledgeToEdit();
          }
        }

        // Close the form
        toggleEpaymentForm(false);
        updateSelectedPayment(null);
        updateSelectedPaymentMethod(null);
        updateSelectedPaymentType(null);
      }
    } catch (error) {
      console.error("Payment operation error:", error);
    } finally {
      setIsLoading(false);
      setSubmitting(false);
      toggleActionButtons(true);
    }
  };

  const handleCancel = () => {
    toggleEpaymentForm(false);
    updateSelectedPayment(null);
    updateSelectedPaymentMethod(null);
    updateSelectedPaymentType(null);
    toggleActionButtons(true);
  };

  const getFormHeading = () => {
    switch (selectedPaymentMethod) {
      case "PUT":
        return EPAYMENT_FORM_HEADING_EDIT;
      case "DELETE":
        return EPAYMENT_FORM_HEADING_DELETE;
      default:
        return EPAYMENT_FORM_HEADING_ADD;
    }
  };

  const getSubmitButtonLabel = () => {
    switch (selectedPaymentMethod) {
      case "PUT":
        return EDIT_EPAYMENT_CONFIRM_BTN_LABEL;
      case "DELETE":
        return DELETE_EPAYMENT_CONFIRM_BTN_LABEL;
      default:
        return ADD_EPAYMENT_CONFIRM_BTN_LABEL;
    }
  };

  const isDeleteMode = selectedPaymentMethod === "DELETE";



  if (!showEpaymentForm) {
    console.log("EpaymentForm: returning null because showEpaymentForm is false");
    return null;
  }

  console.log("EpaymentForm: about to render form");

  return (
    <div data-private>
      <h5 className="card-title mb-3">{getFormHeading()}</h5>
          <Formik
            initialValues={initialValues}
            validate={validate}
            onSubmit={onSubmit}
            enableReinitialize
          >
            {({ isSubmitting, errors, touched }) => (
              <Form>
                <CRow className="mb-3">
                  <CCol md={6}>
                    <Field
                      name={EPAYMENT_FIELD_NAMES.amount}
                      component={InputFlexible}
                      type="number"
                      step="0.01"
                      label={EPAYMENT_AMOUNT_LABEL}
                      disabled={isDeleteMode}
                      required
                    />
                  </CCol>
                  <CCol md={6}>
                    <Field
                      name={EPAYMENT_FIELD_NAMES.payment_id}
                      component={InputFlexible}
                      type="text"
                      label={EPAYMENT_ID_LABEL}
                      disabled={isDeleteMode}
                      required
                    />
                  </CCol>
                </CRow>

                <CRow className="mb-3">
                  <CCol md={6}>
                    <Field
                      name={EPAYMENT_FIELD_NAMES.date_deposited}
                      component={InputFlexible}
                      type="date"
                      label={EPAYMENT_DEPOSIT_DATE_LABEL}
                      disabled={isDeleteMode}
                      required
                    />
                  </CCol>
                  <CCol md={6}>
                    <Field
                      name={EPAYMENT_FIELD_NAMES.status}
                      component={SelectField}
                      label={EPAYMENT_STATUS_LABEL}
                      options={EPAYMENT_STATUS_OPTIONS}
                      labelPlacement="top"
                      isDisabled={selectedPaymentMethod === "DELETE" ? true : false}
                      required
                    />
                  </CCol>
                </CRow>

                <CRow className="mb-3">
                  <CCol md={6}>
                    <Field
                      name={EPAYMENT_FIELD_NAMES.processor}
                      component={SelectField}
                      label={EPAYMENT_PROCESSOR_LABEL}
                      options={EPAYMENT_PROCESSOR_OPTIONS}
                      labelPlacement="top"
                      isDisabled={selectedPaymentMethod === "DELETE" ? true : false}
                      required
                    />
                  </CCol>
                </CRow>

                {isDeleteMode && (
                  <div className="alert alert-warning">
                    <strong>Warning:</strong> This action cannot be undone. Are you sure you want to delete this epayment?
                  </div>
                )}

                <CButtonGroup className="float-end">
                  <CButton
                    type="button"
                    color="secondary"
                    onClick={handleCancel}
                    disabled={isSubmitting || isLoading}
                  >
                    {ADD_EPAYMENT_CANCEL_BTN_LABEL}
                  </CButton>
                  <CButton
                    type="submit"
                    color={isDeleteMode ? "danger" : "primary"}
                    disabled={isSubmitting || isLoading}
                  >
                    {isLoading ? "Processing..." : getSubmitButtonLabel()}
                  </CButton>
                </CButtonGroup>
              </Form>
            )}
          </Formik>
    </div>
  );
};

export default EpaymentForm;
