import React from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CBadge,
} from "@coreui/react";

import { modalFlowStr, modalActions } from "../strings";

const FailureModal = ({
  backToEditModal,
  responseContent,
  modalAction,
  closeModal,
}) => {
  return (
    <CModal
      visible={true}
      onClose={modalAction === modalActions.edit ? backToEditModal : closeModal}
      backdrop={true}
      keyboard={true}
    >
      <CModalHeader>
        <CBadge color="danger">
          {modalFlowStr[modalAction].FailureHeader}
        </CBadge>
      </CModalHeader>
      <CModalBody>
        <p>{modalFlowStr[modalAction].FailureBody}</p>
        {responseContent ? (
          <>
            <p>
              <strong>{responseContent}</strong>
            </p>
          </>
        ) : null}
        {!responseContent ? (
          <p>
            <PERSON><PERSON>r reaching KPFA server. Please check your internet connection
            and try again.
          </p>
        ) : null}
      </CModalBody>
      <CModalFooter>
        <CButton
          color="primary"
          onClick={
            modalAction === modalActions.edit ? backToEditModal : closeModal
          }
        >
          {modalFlowStr[modalAction].FailureBtnConfirm}
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default FailureModal;
