import React, { useState } from "react";
import EditModal from "./EditModal";
import ConfirmationModal from "./ConfirmationModal";
import ProcessingModal from "./ProcessingModal";
import SuccessModal from "./SuccessModal";
import FailureModal from "./FailureModal";
import { modalFlows } from "../strings";

const Modals = ({
  UpdatePledgeToEdit,
  closeModal,
  pledgeToEdit,
  handleAddPremium,
  handleRemovePremium,
  selectedPremiums,
  handlePledgeUpdateSubmit,
  formValues,
  backToEditModal,
  formData,
  confirmDonation,
  formSubmitted,
  displayFailureModal,
  displaySuccessModal,
  modalAction,
  modalFlow,
  selectedDonor,
  setSelectedDonor,
  refreshCurrentDonor,
}) => {
  const [responseContent, updateResponseContent] = useState(null);
  const [receipt, updateReceipt] = useState(null);

  if (modalFlow === modalFlows.edit) {
    return (
      <EditModal
        UpdatePledgeToEdit={UpdatePledgeToEdit}
        closeModal={closeModal}
        pledgeToEdit={pledgeToEdit}
        handleAddPremium={handleAddPremium}
        handleRemovePremium={handleRemovePremium}
        selectedPremiums={selectedPremiums}
        handlePledgeUpdateSubmit={handlePledgeUpdateSubmit}
        formValues={formValues}
        selectedDonor={selectedDonor}
        setSelectedDonor={setSelectedDonor}
        refreshCurrentDonor={refreshCurrentDonor}
      />
    );
  } else if (modalFlow === modalFlows.confirm) {
    return (
      <ConfirmationModal
        formData={formData}
        backToEditModal={backToEditModal}
        confirmDonation={confirmDonation}
        modalAction={modalAction}
        closeModal={closeModal}
        selectedDonor={selectedDonor}
      />
    );
  } else if (modalFlow === modalFlows.processing) {
    return (
      <ProcessingModal
        formSubmitted={formSubmitted}
        formData={formData}
        displayFailureModal={displayFailureModal}
        updateResponseContent={updateResponseContent}
        updateReceipt={updateReceipt}
        displaySuccessModal={displaySuccessModal}
        modalAction={modalAction}
        selectedDonor={selectedDonor}
        setSelectedDonor={setSelectedDonor}
        refreshCurrentDonor={refreshCurrentDonor}
      />
    );
  } else if (modalFlow === modalFlows.success) {
    return (
      <SuccessModal
        closeModal={closeModal}
        responseContent={responseContent}
        receipt={receipt}
        modalAction={modalAction}
      />
    );
  } else if (modalFlow === modalFlows.failure) {
    return (
      <FailureModal
        backToEditModal={backToEditModal}
        responseContent={responseContent}
        modalAction={modalAction}
        closeModal={closeModal}
      />
    );
  }
  return null;
};

export default Modals;
