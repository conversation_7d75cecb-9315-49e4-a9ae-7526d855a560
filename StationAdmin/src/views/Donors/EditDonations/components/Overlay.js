import PaymentSpinner from "./PaymentSpinner";
import PaymentSuccess from "./PaymentSuccess";
import PaymentFailure from "./PaymentFailure";

const overlay = ({
  showSpinner,
  showSuccess,
  showFailure,
  toggleSuccess,
  toggleFailure,
  toggleForm,
  showForm,
  selectedPaymentMethod,
  selectedPaymentType,
  selectedPaymentProcessor,
  resetForm,
  errorMessage,
}) => {
  if (showSpinner || showSuccess || showFailure) {
    return (
      <div
        style={{
          position: "absolute",
          top: "-10px",
          left: "-10px",
          backgroundColor: "rgba(255, 255, 255, 0.75)",
          width: "calc(100% + 20px)",
          height: "calc(100% + 20px)",
        }}
        className="d-flex flex-column justify-content-center align-items-center"
      >
        <PaymentSpinner
          showSpinner={showSpinner}
          selectedPaymentMethod={selectedPaymentMethod}
          selectedPaymentType={selectedPaymentType}
          selectedPaymentProcessor={selectedPaymentProcessor}
        />
        <PaymentSuccess
          showSuccess={showSuccess}
          toggleSuccess={toggleSuccess}
          selectedPaymentMethod={selectedPaymentMethod}
          selectedPaymentType={selectedPaymentType}
          selectedPaymentProcessor={selectedPaymentProcessor}
          resetForm={resetForm}
          showForm={showForm}
          toggleForm={toggleForm}
        />
        <PaymentFailure
          showFailure={showFailure}
          toggleFailure={toggleFailure}
          selectedPaymentMethod={selectedPaymentMethod}
          selectedPaymentType={selectedPaymentType}
          selectedPaymentProcessor={selectedPaymentProcessor}
          showForm={showForm}
          toggleForm={toggleForm}
          errorMessage={errorMessage}
        />
      </div>
    );
  }
  return null;
};

export default overlay;
