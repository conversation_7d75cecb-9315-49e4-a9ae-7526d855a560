import { CRow } from "@coreui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useEffect } from "react";

import {
  CHECK_SUBMIT_FAILURE_ADD,
  CHECK_SUBMIT_FAILURE_EDIT,
  CHECK_SUBMIT_FAILURE_DELETE,
  CARD_SUBMIT_FAILURE,
  CASH_SUBMIT_FAILURE_DELETE,
  CASH_SUBMIT_FAILURE_EDIT,
  CASH_SUBMIT_FAILURE_ADD,
  STRIPE_SUBMIT_FAILURE,
} from "../strings";

const PaymentFailure = ({
  showFailure,
  toggleFailure,
  selectedPaymentMethod,
  selectedPaymentType,
  selectedPaymentProcessor,
  errorMessage,
}) => {
  useEffect(() => {});
  if (showFailure) {
    return (
      <>
        <div className="alert alert-danger d-flex flex-column" role="alert">
          <FontAwesomeIcon
            role="button"
            icon="times"
            size="lg"
            className="ms-auto"
            aria-label="Close"
            onClick={() => {
              toggleFailure(false);
            }}
          />
          <CRow className="mx-2 mt-3 text-center">
            {(() => {
              if (
                selectedPaymentType === "check" &&
                selectedPaymentMethod === "DELETE"
              ) {
                return CHECK_SUBMIT_FAILURE_DELETE;
              } else if (
                selectedPaymentType === "check" &&
                selectedPaymentMethod === "PUT"
              ) {
                return CHECK_SUBMIT_FAILURE_EDIT;
              } else if (
                selectedPaymentType === "check" &&
                !selectedPaymentMethod
              ) {
                return CHECK_SUBMIT_FAILURE_ADD;
              } else if (
                selectedPaymentType === "cash" &&
                selectedPaymentMethod === "DELETE"
              ) {
                return CASH_SUBMIT_FAILURE_DELETE;
              } else if (
                selectedPaymentType === "cash" &&
                selectedPaymentMethod === "PUT"
              ) {
                return CASH_SUBMIT_FAILURE_EDIT;
              } else if (
                selectedPaymentType === "cash" &&
                !selectedPaymentMethod
              ) {
                return CASH_SUBMIT_FAILURE_ADD;
              } else if (selectedPaymentProcessor === "Stripe") {
                //return STRIPE_SUBMIT_FAILURE;
                return STRIPE_SUBMIT_FAILURE;
              } else if (selectedPaymentType === "card") {
                return CARD_SUBMIT_FAILURE;
              }
              return null;
            })()}
          </CRow>
          <CRow className="mx-2 mb-4 mt-2 text-center">
            {String(errorMessage)}
          </CRow>
        </div>
      </>
    );
  }
  return null;
};

export default PaymentFailure;
