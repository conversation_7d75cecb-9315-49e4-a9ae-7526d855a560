import React from "react";

import {
  CHECK_SUBMIT_ADD,
  CHECK_SUBMIT_EDIT,
  CHECK_SUBMIT_DELETE,
  CARD_SUBMIT_CHARGE,
  CASH_SUBMIT_DELETE,
  CASH_SUBMIT_EDIT,
  CASH_SUBMIT_ADD,
  STRIPE_SUBMIT_REFUND
} from "../strings";

const PaymentSpinner = ({
  showSpinner,
  selectedPaymentMethod,
  selectedPaymentProcessor,
  selectedPaymentType
}) => {
  if (showSpinner) {
    return (
      <>
        <h5 className="text-center">
          {(() => {
            if (
              selectedPaymentType === "check" &&
              selectedPaymentMethod === "DELETE"
            ) {
              return CHECK_SUBMIT_DELETE;
            } else if (
              selectedPaymentType === "check" &&
              selectedPaymentMethod === "PUT"
            ) {
              return CHECK_SUBMIT_EDIT;
            } else if (
              selectedPaymentType === "check" &&
              !selectedPaymentMethod
            ) {
              return CHECK_SUBMIT_ADD;
            } else if (
              selectedPaymentType === "cash" &&
              selectedPaymentMethod === "DELETE"
            ) {
              return CASH_SUBMIT_DELETE;
            } else if (
              selectedPaymentType === "cash" &&
              selectedPaymentMethod === "PUT"
            ) {
              return CASH_SUBMIT_EDIT;
            } else if (
              selectedPaymentType === "cash" &&
              !selectedPaymentMethod
            ) {
              return CASH_SUBMIT_ADD;
            }
            else if ( selectedPaymentProcessor === "Stripe" ) {
              return STRIPE_SUBMIT_REFUND;
            } else if (selectedPaymentType === "card") {
              return CARD_SUBMIT_CHARGE;
            }
            return null;
          })()}
          <br />
          Please Wait...
        </h5>
        <div className="spinner-border" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </>
    );
  }
  return null;
};

export default PaymentSpinner;
