import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";

import {
  CHECK_SUBMIT_SUCCESS_ADD,
  CHECK_SUBMIT_SUCCESS_EDIT,
  CHECK_SUBMIT_SUCCESS_DELETE,
  CARD_SUBMIT_SUCCESS,
  CASH_SUBMIT_SUCCESS_ADD,
  CASH_SUBMIT_SUCCESS_EDIT,
  CASH_SUBMIT_SUCCESS_DELETE,
  STRIPE_REFUND_SUCCESS,
} from "../strings";

const PaymentSuccess = ({
  showSuccess,
  toggleSuccess,
  selectedPaymentMethod,
  selectedPaymentType,
  selectedPaymentProcessor,
  resetForm,
  showForm,
  toggleForm,
}) => {
  if (showSuccess) {
    return (
      <div className="alert alert-success" role="alert">
        {(() => {
          if (
            selectedPaymentType === "check" &&
            selectedPaymentMethod === "DELETE"
          ) {
            return CHECK_SUBMIT_SUCCESS_DELETE;
          } else if (
            selectedPaymentType === "check" &&
            selectedPaymentMethod === "PUT"
          ) {
            return CHECK_SUBMIT_SUCCESS_EDIT;
          } else if (
            selectedPaymentType === "check" &&
            !selectedPaymentMethod
          ) {
            return CHECK_SUBMIT_SUCCESS_ADD;
          } else if (
            selectedPaymentType === "cash" &&
            selectedPaymentMethod === "DELETE"
          ) {
            return CASH_SUBMIT_SUCCESS_DELETE;
          } else if (
            selectedPaymentType === "cash" &&
            selectedPaymentMethod === "PUT"
          ) {
            return CASH_SUBMIT_SUCCESS_EDIT;
          } else if (selectedPaymentProcessor === "Stripe") {
            return STRIPE_REFUND_SUCCESS;
          } else if (selectedPaymentType === "cash" && !selectedPaymentMethod) {
            return CASH_SUBMIT_SUCCESS_ADD;
          } else if (selectedPaymentType === "card") {
            return CARD_SUBMIT_SUCCESS;
          }
          return null;
        })()}
        <FontAwesomeIcon
          role="button"
          icon="times"
          className="mx-2"
          aria-label="Close"
          onClick={() => {
            toggleSuccess(false);
            toggleForm(false);
            resetForm();
          }}
        />
      </div>
    );
  }
  return null;
};

export default PaymentSuccess;
