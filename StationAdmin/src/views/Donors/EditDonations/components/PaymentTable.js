import currency from "currency.js";
import { startCase, toLower } from "lodash";
import { CBadge, CButton, CButtonGroup } from "@coreui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useAppSelector } from "../../../../hooks";

const PaymentTable = ({
  values,
  enableActionButtons,
  toggleActionButtons,
  updateSelectedPayment,
  updateSelectedPaymentMethod,
  updateSelectedPaymentType,
  toggleCheckForm,
  toggleCashForm,
  toggleStripeForm,
  toggleEpaymentForm,
  updatePopulateForm,
}) => {
  const updatePayment = (e) => {
    toggleActionButtons(false);
    const method = e.currentTarget.dataset.method;
    const index = e.currentTarget.dataset.index;
    const paymentType = e.currentTarget.dataset.type;

    updateSelectedPayment(values.payments[index]);
    updateSelectedPaymentMethod(method);
    updatePopulateForm(true);
    updateSelectedPaymentType(paymentType);

    if (paymentType === "cash") {
      toggleCashForm(true);
    } else if (paymentType === "check") {
      toggleCheckForm(true);
    } else if (paymentType === "epayment") {
      toggleEpaymentForm(true);
    } else if (values.payments[index].processor === "Stripe") {
      toggleStripeForm(true);
    }
  };

  const testMode = useAppSelector((state) => state.testMode);

  if (values.payments.length > 0) {
    return (
      <ul className="list-group mb-3">
        <li
          className="list-group-item bg-light d-none d-lg-block"
          key="payment-header"
        >
          <div className="row">
            <div className="col-lg-6 font-weight-bold">
              <div className="row">
                <div className="col-lg-3 font-weight-bold">Method</div>
                <div className="col-lg-2 font-weight-bold">Amount</div>
                <div className="col-lg-3 font-weight-bold">Processor</div>
                <div className="col-lg-4 font-weight-bold">Status</div>
              </div>
            </div>
            <div className="col-lg-6 font-weight-bold">
              <div className="row">
                <div className="col-lg-4 font-weight-bold">Deposited</div>
                <div className="col-lg-4 font-weight-bold">Payment</div>
                <div className="col-lg-4 font-weight-bold">Actions</div>
              </div>
            </div>
          </div>
        </li>
        {values.payments.map((payment, index) => (
          <li className="list-group-item" key={index}>
            <div className="row">
              <div className="col-lg-6">
                <div className="row">
                  <div className="col-md-6 col-lg-3">
                    <span className="mb-3 me-2 font-weight-bold d-inline-block d-lg-none">
                      Method:{" "}
                    </span>
                    {`${
                      payment.card_type
                        ? startCase(toLower(payment.card_type))
                        : ""
                    }
                  ${startCase(toLower(payment.method))}
                  `}
                  </div>
                  <div className="col-md-6 col-lg-2">
                    <span className="mb-3 me-2 font-weight-bold d-inline-block d-lg-none">
                      Amount:{" "}
                    </span>
                    {currency(payment.amount).format()}
                  </div>
                  <div className="col-md-6 col-lg-3">
                    <span className="mb-3 me-2 font-weight-bold d-inline-block d-lg-none">
                      Processor:{" "}
                    </span>
                    {payment.processor}
                  </div>
                  <div className="col-md-6 col-lg-4">
                    <span className="mb-3 me-2 font-weight-bold d-inline-block d-lg-none">
                      Status:{" "}
                    </span>
                    {payment.status.toLowerCase() === "succeeded" ? (
                      <CBadge color="success" size="sm">
                        Succeeded
                      </CBadge>
                    ) : (
                      <CBadge color="warning" size="sm">
                        {payment.status}
                      </CBadge>
                    )}
                  </div>
                </div>
              </div>

              <div className="col-lg-6">
                <div className="row">
                  <div className="col-md-6 col-lg-4">
                    <span className="mb-3 me-2 font-weight-bold d-inline-block d-lg-none">
                      Deposited:{" "}
                    </span>
                    {payment.date_deposited}
                  </div>
                  <div className="col-md-6 col-lg-4">
                    <span className="mb-3 me-2 font-weight-bold d-inline-block d-lg-none">
                      Payment:{" "}
                    </span>
                    {payment.payment_id && payment.card_type ? (
                      <a
                        href={`https://dashboard.stripe.com/${
                          testMode.value ? "test/" : ""
                        }payments/${payment.payment_id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="btn btn-primary btn-sm"
                      >
                        View
                      </a>
                    ) : (
                      <span
                        className="text-truncate d-inline-block"
                        style={{ maxWidth: "150px" }}
                        title={payment.payment_id || "Unknown"}
                      >
                        {payment.payment_id || "Unknown"}
                      </span>
                    )}
                  </div>
                  <div className="col-md-6 col-lg-4">
                    <span className="mb-3 me-2 font-weight-bold d-inline-block d-lg-none">
                      Actions:{" "}
                    </span>
                    {(() => {
                      /* eslint-disable no-fallthrough */
                      switch (payment.method) {
                        case "cash":
                        case "check":
                          return (
                            <CButtonGroup size="sm">
                              <CButton
                                variant="outline"
                                data-method="PUT"
                                data-index={index}
                                data-type={payment.method}
                                className={
                                  payment.method !== "card" &&
                                  enableActionButtons
                                    ? ""
                                    : "disabled"
                                }
                                onClick={updatePayment}
                              >
                                <FontAwesomeIcon icon="pencil-alt" />
                              </CButton>
                              <CButton
                                variant="outline"
                                type="button"
                                data-method="DELETE"
                                data-index={index}
                                data-type={payment.method}
                                className={
                                  enableActionButtons ? "" : "disabled"
                                }
                                onClick={updatePayment}
                              >
                                <FontAwesomeIcon icon="trash" />
                              </CButton>
                            </CButtonGroup>
                          );
                        case "epayment":
                          // Only delete functionality for epayment - edit needs more discussion
                          return (
                            <CButton
                              variant="outline"
                              type="button"
                              data-method="DELETE"
                              data-index={index}
                              data-type={payment.method}
                              className={
                                enableActionButtons ? "" : "disabled"
                              }
                              onClick={updatePayment}
                            >
                              <FontAwesomeIcon icon="trash" />
                            </CButton>
                          );
                        case "card":
                          if (payment.processor === "Stripe")
                            return (
                              <>
                                <button
                                  type="button"
                                  data-method="PATCH"
                                  data-index={index}
                                  data-type={payment.method}
                                  className={
                                    enableActionButtons
                                      ? "btn btn-primary btn-sm"
                                      : "btn btn-primary btn-sm disabled"
                                  }
                                  onClick={updatePayment}
                                >
                                  Refund
                                </button>
                              </>
                            );
                        default:
                          return <span>N/A</span>;
                      }
                      /* eslint-enable no-fallthrough */
                    })()}
                  </div>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    );
  }
  return <div className="alert alert-info">No Payments</div>;
};

export default PaymentTable;
