import React, { useState } from "react";

import PaymentTable from "./PaymentTable";
import AddPayment from "./AddPayment";

const PaymentsPane = ({
  pledge,
  values,
  refreshCurrentDonor,
  selectedDonor,
  setSelectedDonor,
  UpdatePledgeToEdit,
}) => {
  const [enableActionButtons, toggleActionButtons] = useState(true);
  const [selectedPayment, updateSelectedPayment] = useState(null);
  const [selectedPaymentMethod, updateSelectedPaymentMethod] = useState(null);
  const [selectedPaymentType, updateSelectedPaymentType] = useState(null);
  const [showCheckForm, toggleCheckForm] = useState(false);
  const [showCardForm, toggleCardForm] = useState(false);
  const [showStripeForm, toggleStripeForm] = useState(false);
  const [showCashForm, toggleCashForm] = useState(false);
  const [showEpaymentForm, toggleEpaymentForm] = useState(false);
  const [populateForm, updatePopulateForm] = useState(false);

  return (
    <>
      <PaymentTable
        values={values}
        enableActionButtons={enableActionButtons}
        updateSelectedPayment={updateSelectedPayment}
        updateSelectedPaymentMethod={updateSelectedPaymentMethod}
        updateSelectedPaymentType={updateSelectedPaymentType}
        toggleCheckForm={toggleCheckForm}
        toggleCashForm={toggleCashForm}
        toggleStripeForm={toggleStripeForm}
        toggleEpaymentForm={toggleEpaymentForm}
        updatePopulateForm={updatePopulateForm}
        toggleActionButtons={toggleActionButtons}
      />
      <AddPayment
        UpdatePledgeToEdit={UpdatePledgeToEdit}
        pledge={pledge}
        values={values}
        enableActionButtons={enableActionButtons}
        toggleActionButtons={toggleActionButtons}
        selectedPayment={selectedPayment}
        updateSelectedPayment={updateSelectedPayment}
        selectedPaymentMethod={selectedPaymentMethod}
        updateSelectedPaymentMethod={updateSelectedPaymentMethod}
        selectedPaymentType={selectedPaymentType}
        updateSelectedPaymentType={updateSelectedPaymentType}
        showCheckForm={showCheckForm}
        toggleCheckForm={toggleCheckForm}
        showCardForm={showCardForm}
        toggleCardForm={toggleCardForm}
        showCashForm={showCashForm}
        toggleCashForm={toggleCashForm}
        showStripeForm={showStripeForm}
        toggleStripeForm={toggleStripeForm}
        showEpaymentForm={showEpaymentForm}
        toggleEpaymentForm={toggleEpaymentForm}
        populateForm={populateForm}
        updatePopulateForm={updatePopulateForm}
        selectedDonor={selectedDonor}
        setSelectedDonor={setSelectedDonor}
        refreshCurrentDonor={refreshCurrentDonor}
      />
    </>
  );
};

export default PaymentsPane;
