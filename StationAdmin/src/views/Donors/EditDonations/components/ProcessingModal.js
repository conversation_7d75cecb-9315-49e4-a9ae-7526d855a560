import { CModal, CModalHeader, CModalBody, CBadge } from "@coreui/react";
import { useAppSelector } from "../../../../hooks";

import { modalFlowStr, modalActions } from "../strings";

const ProcessingModal = ({
  formSubmitted,
  formData,
  displayFailureModal,
  updateResponseContent,
  updateReceipt,
  displaySuccessModal,
  modalAction,
  refreshCurrentDonor,
  selectedDonor,
  setSelectedDonor,
}) => {
  const testMode = useAppSelector((state) => state.testMode);

  if (formSubmitted === false) {
    let url;
    let baseUrl = `https://${
      testMode.value ? "api.staging." : "api."
    }kpfa.org/`;

    switch (modalAction) {
      case modalActions.edit:
        url = `${baseUrl}donations`;
        break;
      case modalActions.delete:
        url = `${baseUrl}donations/${formData.donation_id}`;
        break;
      case modalActions.cancel:
        url = `${baseUrl}subscriptions/${formData.subscription_id}`;
        break;
      default:
        break;
    }

    const initObject = {
      method: modalAction === modalActions.edit ? "PUT" : "DELETE",
      body:
        modalAction === modalActions.edit
          ? JSON.stringify(formData)
          : JSON.stringify({}),
      headers: {
        Authorization: `Bearer ${localStorage.getItem("jwt")}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    };

    fetch(url, initObject)
      .then((response) => {
        if (!response.ok) {
          displayFailureModal();
          response.json().then((data) => {
            updateResponseContent(data.message);
            updateReceipt(null);
          });
        }
        if (response.ok) {
          response.json().then((data) => {
            displaySuccessModal();
            data.message
              ? updateResponseContent(data.message)
              : updateResponseContent(
                  `Donation ${data.id} updated successfully`
                );
            updateReceipt(
              `https://${
                testMode.value ? "api.staging." : "api."
              }kpfa.org/receipts/${data.transaction_id}`
            );
            refreshCurrentDonor();
            if (modalAction === modalActions.cancel) {
              let mutatedSelectedDonor = selectedDonor;
              mutatedSelectedDonor.donations
                .find(
                  (donation) =>
                    (donation.transaction_id = formData.transaction_id)
                )
                .subscriptions.find(
                  (sub) => sub.id === formData.subscription_id
                ).active = false;
              setSelectedDonor(mutatedSelectedDonor);
            }
          });
        }
      })
      .catch((error) => {
        console.log(error);
        displayFailureModal();
        updateResponseContent(null);
        updateReceipt(null);
      });

    return (
      <CModal visible={true} backdrop="static" keyboard={false}>
        <CModalHeader>
          <CBadge className="warning">
            {modalFlowStr[modalAction].processingHeader}
          </CBadge>
        </CModalHeader>
        <CModalBody>
          <div className="d-flex justify-content-center mt-2 mb-2">
            <h3>
              <div
                className="spinner-border text-primary"
                style={{
                  width: "2rem",
                  height: "2rem",
                  marginRight: "0.5rem",
                }}
                role="status"
              >
                <span className="sr-only">
                  {modalFlowStr[modalAction].processingBody}
                </span>
              </div>
              {modalFlowStr[modalAction].processingBody}
            </h3>
          </div>
          <p>
            Please do not close this window or navigate away from this page
            until your changes have been processed and the confirmation message
            is displayed.
          </p>
        </CModalBody>
      </CModal>
    );
  }
  return null;
};

export default ProcessingModal;
