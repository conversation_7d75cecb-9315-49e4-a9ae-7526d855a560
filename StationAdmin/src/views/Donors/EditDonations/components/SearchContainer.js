import React, { useState } from "react";

import { searchOptions } from "../strings";
import TextSearchQuery from "./TextSearchQuery";
import CampaignSearchQuery from "./CampaignSearchQuery";
import DateRangeSearchQuery from "./DateRangeSearchQuery";

const SearchContainer = ({
  handleSearch,
  searchValue,
  campaignLabels,
  campaignSearchStrings
}) => {
  const [selectedSearch, setSelectedSearch] = useState(searchOptions.name);
  const [searchString, setSearchString] = useState("");

  return (
    <>
      <div className="row">
        <div className="col-sm-6 col-md-4">
          <div className="form-group">
            <label htmlFor="search-type">Search by</label>
            <select
              id="search-type"
              className="form-control"
              onChange={e => {
                setSelectedSearch(e.target.value);
                handleSearch("", "");
              }}
              value={selectedSearch}
            >
              {Object.entries(searchOptions).map(([key, value]) => (
                <option key={key} value={value}>
                  {value}
                </option>
              ))}
            </select>
          </div>
        </div>
        <div className="col-sm-6 col-md-8">
          {(() => {
            if (selectedSearch === searchOptions.name) {
              return (
                <TextSearchQuery
                  handleSearch={handleSearch}
                  searchValue={searchValue}
                  searchString={searchString}
                  setSearchString={setSearchString}
                  searchType={searchOptions.name}
                />
              );
            } else if (selectedSearch === searchOptions.campaign) {
              return (
                <CampaignSearchQuery
                  handleSearch={handleSearch}
                  searchValue={searchValue}
                  searchString={searchString}
                  setSearchString={setSearchString}
                  searchType={searchOptions.campaign}
                  campaignLabels={campaignLabels}
                  campaignSearchStrings={campaignSearchStrings}
                />
              );
            } else if (selectedSearch === searchOptions.dateRange) {
              return (
                <DateRangeSearchQuery
                  handleSearch={handleSearch}
                  searchValue={searchValue}
                  searchString={searchString}
                  setSearchString={setSearchString}
                  searchType={searchOptions.dateRange}
                />
              );
            }
            return null;
          })()}
        </div>
      </div>
    </>
  );
};

export default SearchContainer;
