import React from "react";
//so - should I refactor this to run its own request to the API to the premiums endpoint? based on the donations? I'm not sure
// todo: make sure the endpoint is return the Variants property of Premiums

const SelectedPremiums = (props) => {
  if (props.selectedPremiums && props.premiums?.length) {
    const selectedPremiums = [...props.selectedPremiums];
    const allPremiums = [...props.premiums];
    const premiumsList = selectedPremiums.map((item, index) => {
      let variation = "";
      let variationID = "";
      const selectedPremium = allPremiums.find((premium) => {
        if (!premium.variants) {
          //this condition added as a workaround, may need to refactor
          if (item.id) {
            return premium.id.toString() === item.id.toString();
          } else {
            return premium.id.toString() === item.toString();
          }
        } else if (premium.variants) {
          const parentPremium = premium.variants.variations.find((variant) => {
            variation = variant.name;
            variationID = variant.id;
            //this condition added as a workaround, may need to refactor
            if (item.id) {
              return variant.id.toString() === item.id.toString();
            } else {
              return variant.id.toString() === item.toString();
            }
          });
          return parentPremium ? true : false;
        }
        return false;
      });

      return (
        <li
          className="list-group-item d-flex justify-content-between align-items-center selected-premiums"
          key={`item${index}`}
        >
          <span className="premium-name">
            {selectedPremium
              ? `${selectedPremium.name} ${
                  selectedPremium.variants ? `(${variation})` : ""
                }`
              : `Cannot find premium ID ${item.id} in premium list returned by server.`}
          </span>

          <strong>
            {selectedPremium
              ? selectedPremium.variants
                ? `ID:${variationID}`
                : `ID:${selectedPremium.id}`
              : ""}
          </strong>

          {selectedPremium ? (
            <strong>{`$${selectedPremium.price}`}</strong>
          ) : null}

          <div
            className="btn btn-danger ms-2"
            data-index={index}
            onClick={props.handleRemovePremium}
          >
            remove
          </div>
        </li>
      );
    });
    return (
      <ul className="list-group">
        {premiumsList}
        {/* <li className="list-group-item list-group-item-dark d-flex justify-content-between align-items-center">
          <strong>Selected Premiums Total:</strong>
          <strong>$999</strong>
        </li> */}
      </ul>
    );
  }
  return (
    <ul className="list-group">
      <li
        href="#"
        className="list-group-item list-group-item-action list-group-item-warning"
      >
        No Premiums Selected
      </li>
    </ul>
  );
};

export default SelectedPremiums;
