import React from "react";
import { Formik, Form, Field } from "formik";
import countries from "iso-3166-1-alpha-2";
import ReactSelect from "react-select";

import Input from "../../../Form/Input";
import Checkbox from "../../../Form/Checkbox";
import Dropdown from "../../../Form/Dropdown";
import SelectedPremiums from "./SelectedPremiums";
import { states, statesAbbreviations } from "../../../Form/constants";
import supportFormSchema from "../../../Form/validationSchema";
import DonorAddress from "../../../Shared/DonorAddress";
import { CButton, CButtonGroup } from "@coreui/react";

const ShipmentsPane = (props) => {
  const countryLabels = countries.getCountries();
  const countryCodes = countries.getCodes();

  const {
    premiums,
    selectedPremiums,
    handleRemovePremium,
    premiumSelectOptions,
    handleAddPremium,
    pledge,
    selectedDonor,
    closeModal,
  } = props;

  const tempFormValues = props.formValues;
  const initialPledgeValues = {
    campaignID: pledge.campaign_id || "",
    amount: Number(pledge.amount),
    pledgeType: pledge.installment,
    donationType: pledge.type,
    showShippingAddress: pledge.shipping_address1 ? true : false,
    firstNameShipping: pledge.shipping_address1
      ? pledge.shipping_firstname
      : "",
    lastNameShipping: pledge.shipping_address1 ? pledge.shipping_lastname : "",
    address1Shipping: pledge.shipping_address1 ? pledge.shipping_address1 : "",
    address2Shipping: pledge.shipping_address2 ? pledge.shipping_address2 : "",
    cityShipping: pledge.shipping_address1 ? pledge.shipping_city : "",
    stateShipping: pledge.shipping_address1 ? pledge.shipping_state : "",
    zipShipping: pledge.shipping_address1 ? pledge.shipping_postal_code : "",
    countryShipping: pledge.shipping_address1 ? pledge.shipping_country : "",
    savedShippingValues: null,
    comment: pledge.comments || "",
    readOnair: pledge.read_onair,
    donationMatch: pledge.donation_match,
    overridePremiums: false,
  };

  function getInitialValues() {
    if (
      Object.entries(tempFormValues).length !== 0 &&
      tempFormValues.constructor === Object
    ) {
      return tempFormValues;
    }
    return initialPledgeValues;
  }

  return (
    <>
      <Formik
        initialValues={getInitialValues()}
        validationSchema={supportFormSchema}
        submitForm={props.submitForm}
        onSubmit={(values, { setSubmitting }) => {
          props.handlePledgeUpdateSubmit(values);

          setSubmitting(false);
        }}
      >
        {({ values, handleChange, handleSubmit, setFieldValue }) => {
          return (
            <Form className="needs-validation" onSubmit={handleSubmit}>
              <h3 className="mb-3">
                {values.showShippingAddress === false
                  ? "Billing & Shipping Address"
                  : "Billing Address"}
              </h3>
              <DonorAddress selectedDonor={selectedDonor} />

              <Field
                name="showShippingAddress"
                label="Shipping Address is different from billing address"
                component={Checkbox}
                checked={values.showShippingAddress}
                onChange={(e) => {
                  if (e.target.checked === false) {
                    // Store the current shipping values before clearing them
                    // These values will be stored in the form state but not sent to the server
                    const savedShippingValues = {
                      firstNameShipping: values.firstNameShipping,
                      lastNameShipping: values.lastNameShipping,
                      address1Shipping: values.address1Shipping,
                      address2Shipping: values.address2Shipping,
                      cityShipping: values.cityShipping,
                      stateShipping: values.stateShipping,
                      zipShipping: values.zipShipping,
                      countryShipping: values.countryShipping,
                    };
                    
                    // Store the saved values in hidden fields for later use
                    setFieldValue("savedShippingValues", savedShippingValues);
                    
                    // Clear the visible shipping fields
                    setFieldValue("firstNameShipping", "");
                    setFieldValue("lastNameShipping", "");
                    setFieldValue("address1Shipping", "");
                    setFieldValue("address2Shipping", "");
                    setFieldValue("cityShipping", "");
                    setFieldValue("stateShipping", "");
                    setFieldValue("zipShipping", "");
                    setFieldValue("countryShipping", "");
                  } else if (values.savedShippingValues) {
                    // Restore saved values when checkbox is checked again
                    const saved = values.savedShippingValues;
                    setFieldValue("firstNameShipping", saved.firstNameShipping || "");
                    setFieldValue("lastNameShipping", saved.lastNameShipping || "");
                    setFieldValue("address1Shipping", saved.address1Shipping || "");
                    setFieldValue("address2Shipping", saved.address2Shipping || "");
                    setFieldValue("cityShipping", saved.cityShipping || "");
                    setFieldValue("stateShipping", saved.stateShipping || "");
                    setFieldValue("zipShipping", saved.zipShipping || "");
                    setFieldValue("countryShipping", saved.countryShipping || "");
                  }
                  handleChange(e);
                }}
              />
              {values.showShippingAddress === false ? null : (
                <>
                  <h3 className="mb-3">Shipping Address</h3>
                  <Field
                    name="firstNameShipping"
                    label="First Name"
                    component={Input}
                  />
                  <Field
                    name="lastNameShipping"
                    label="Last Name"
                    component={Input}
                  />

                  <Field
                    name="address1Shipping"
                    label="Address"
                    component={Input}
                  />

                  <Field
                    name="address2Shipping"
                    label="Address (continued)"
                    component={Input}
                  />
                  <div className="mb-3">
                    <Field name="cityShipping" label="City" component={Input} />
                    {values.countryShipping === "US" ? (
                      <Field
                        name="stateShipping"
                        label="State"
                        component={Dropdown}
                        options={states}
                        valuePresets={statesAbbreviations}
                      />
                    ) : (
                      <>
                        <Field
                          name="stateShipping"
                          label="State or Province"
                          component={Input}
                        />
                      </>
                    )}
                  </div>

                  {values.countryShipping === "US" ? (
                    <Field
                      name="zipShipping"
                      label="Zip"
                      component={Input}
                      mask={(value) => {
                        const filteredValue = value.replace(/[^0-9.]+/g, "");
                        if (parseInt(filteredValue).toString().length <= 5) {
                          return [/\d/, /\d/, /\d/, /\d/, /\d/];
                        }
                        return [
                          /\d/,
                          /\d/,
                          /\d/,
                          /\d/,
                          /\d/,
                          "-",
                          /\d/,
                          /\d/,
                          /\d/,
                          /\d/,
                        ];
                      }}
                      guide={false}
                      onChange={(e) => {
                        setFieldValue(
                          "zipShipping",
                          e.target.value.replace(/[^0-9.]+/g, "")
                        );
                      }}
                      value={values.zipShipping}
                    />
                  ) : (
                    <Field
                      name="zipShipping"
                      label="Zip or Postal Code"
                      component={Input}
                    />
                  )}
                  <Field
                    name="countryShipping"
                    label="Country"
                    component={Dropdown}
                    options={countryLabels}
                    valuePresets={countryCodes}
                    // placeholder="US"
                    onChange={(e) => {
                      if (e.target.value !== "US") {
                        if (values.country === "US") {
                          setFieldValue("stateShipping", "");
                          setFieldValue("zipShipping", "");
                        }
                      }
                      if (e.target.value === "US") {
                        if (values.countryShipping !== "US") {
                          setFieldValue("stateShipping", "CA");
                          setFieldValue("zipShipping", "");
                        }
                      }

                      handleChange(e);
                    }}
                  />
                </>
              )}
              <h3 className="mb-3 mt-2">Premiums</h3>
              <Field
                name="overridePremiums"
                label="Manually enter premium IDs"
                component={Checkbox}
                checked={values.overridePremiums}
                onChange={(e) => {
                  if (e.target.checked === true) {
                    setFieldValue("premiums", selectedPremiums.join(","));
                  }
                  handleChange(e);
                }}
              />
              {values.overridePremiums === true ? (
                <>
                  <Field
                    name="premiums"
                    label="Premiums (comma separated numbers only)"
                    component={Input}
                  />
                  <h5>
                    Please note: Manually entered values will be lost when the
                    "Manually enter premium IDs" checkbox is unchecked.
                  </h5>
                </>
              ) : null}

              {values.overridePremiums === false ? (
                <>
                  <SelectedPremiums
                    premiums={premiums}
                    selectedPremiums={selectedPremiums}
                    handleRemovePremium={handleRemovePremium}
                  />
                  <h5 className="mb-3 mt-4">Add Premiums</h5>
                  <ReactSelect
                    options={premiumSelectOptions}
                    onChange={handleAddPremium}
                    value={null}
                    placeholder="Select an item from the list to add"
                  />
                </>
              ) : null}
              <div className="mt-3">
                <CButtonGroup>
                  <CButton variant="outline" onClick={closeModal}>
                    Cancel
                  </CButton>
                  <CButton type="submit">Submit Donation Changes</CButton>
                </CButtonGroup>
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default ShipmentsPane;
