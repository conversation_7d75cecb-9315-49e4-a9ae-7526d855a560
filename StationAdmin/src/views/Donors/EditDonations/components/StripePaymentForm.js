import { useState, useEffect } from "react";
import { Formik, Form, Field } from "formik";

import {
  STRIPE_FORM_HEADING,
  STRIPE_REFUND_REASON_LABEL,
  STRIPE_AMOUNT_LABEL,
  STRIPE_FIELD_NAMES,
  STRIPE_CANCEL_BTN_LABEL,
  STRIPE_REFUND_BTN_LABEL,
  STRIPE_REFUND_OPTIONS,
} from "../strings";
import InputFlexible from "../../../Form/InputFlexible";
import Overlay from "./Overlay";
import stripePaymentSchema from "../../../Form/StripePaymentSchema";
import { CBadge, CCol, CRow } from "@coreui/react";
import SelectField from "../../../Form/SelectField";
import { useAppSelector } from "../../../../hooks";

const StripePaymentForm = ({
  toggleStripeForm,
  toggleActionButtons,
  selectedPayment,
  updateSelectedPayment,
  selectedPaymentMethod,
  updateSelectedPaymentMethod,
  selectedPaymentType,
  updateSelectedPaymentType,
  showStripeForm,
}) => {
  const [showSpinner, toggleSpinner] = useState(false);
  const [showSuccess, toggleSuccess] = useState(false);
  const [showFailure, toggleFailure] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [fullyRefunded, setFullyRefunded] = useState(false);
  const [amountRefunded, setAmountRefunded] = useState(
    selectedPayment.amount_refunded
  );

  const testMode = useAppSelector((state) => state.testMode);

  useEffect(() => {
    if (selectedPayment.amount - selectedPayment.amount_refunded === 0) {
      setFullyRefunded(true);
    }
  }, [selectedPayment.amount, selectedPayment.amount_refunded]);

  const refundStripePayment = (values) => {
    toggleSpinner(true);
    const url = `https://${
      testMode.value ? "api.staging." : "api."
    }kpfa.org/payments/${selectedPayment.id}`;
    const headers = {
      Authorization: `Bearer ${localStorage.getItem("jwt")}`,
      "Content-Type": "application/json",
      Accept: "application/json",
    };

    let body = {
      amount: Number(values.Amount),
      reason: values.Reason,
      action: "refund",
      method: "card",
      status: selectedPayment.status,
    };

    fetch(url, {
      method: "PATCH",
      body: JSON.stringify(body),
      headers,
    })
      .then((response) => {
        if (response.ok) {
          response.json();
          toggleSpinner(false);
          toggleSuccess(true);
          toggleFailure(false);
          setAmountRefunded(
            (selectedPayment.amount_refunded += parseFloat(values.Amount))
          );
        } else {
          response.json().then((response) => {
            toggleSpinner(false);
            toggleSuccess(false);
            toggleFailure(true);
            setErrorMessage(response.message);
          });
        }
      })
      .catch((response) => {
        response.json().then((response) => {
          console.log(`second: ${response}`);
          setErrorMessage(response.message);
          toggleSpinner(false);
          toggleSuccess(false);
          toggleFailure(true);
        });
      });
  };

  const getInitialValues = () => {
    if (selectedPayment && selectedPaymentMethod) {
      if (selectedPayment.amount_refunded) {
        return {
          Amount: parseFloat(
            selectedPayment.amount - selectedPayment.amount_refunded
          ).toFixed(2),
          Reason: "requested_by_customer",
        };
      } else
        return {
          Amount: selectedPayment.amount,
          Reason: "requested_by_customer",
        };
    }
  };

  return (
    <>
      <div className="card-title mb-3 d-flex">
        <h5>
          {fullyRefunded ? (
            <CBadge color="warning">Fully Refunded</CBadge>
          ) : (
            STRIPE_FORM_HEADING
          )}
        </h5>
        <div className="ms-auto">
          <div>
            {amountRefunded
              ? `Amount Already Refunded: $${amountRefunded}`
              : ""}
          </div>
          <div>KPFA ID: {selectedPayment.id}</div>
          <div>Stripe ID: {selectedPayment.payment_id}</div>
        </div>
      </div>
      <Formik
        initialValues={getInitialValues()}
        validationSchema={stripePaymentSchema}
        onSubmit={(values, { setSubmitting }) => {
          refundStripePayment(values);

          setSubmitting(false);
        }}
      >
        {({ handleSubmit, setFieldValue, setFieldTouched }) => {
          const resetForm = () => {
            // reset field values
            STRIPE_FIELD_NAMES.forEach((field) => {
              setFieldValue(field, "");
              setFieldTouched(field, false);
            });

            toggleStripeForm(false);
            toggleActionButtons(true);
            updateSelectedPayment(null);
            updateSelectedPaymentMethod(null);
            updateSelectedPaymentType(null);
          };

          return (
            <Form className="needs-validation" onSubmit={handleSubmit}>
              <div style={{ position: "relative" }}>
                <CRow>
                  <CCol md={4}>
                    <Field
                      disabled={fullyRefunded}
                      name="Amount"
                      label={STRIPE_AMOUNT_LABEL}
                      component={InputFlexible}
                    />
                  </CCol>
                  <CCol md={4}>
                    <Field
                      isDisabled={fullyRefunded}
                      name="Reason"
                      label={STRIPE_REFUND_REASON_LABEL}
                      component={SelectField}
                      options={STRIPE_REFUND_OPTIONS}
                      labelPlacement="top"
                    />
                  </CCol>
                </CRow>
                <div className="row mt-2 mb-2">
                  <div className="col-sm-12 text-right">
                    <button
                      className="btn btn-secondary me-3"
                      type="button"
                      onClick={resetForm}
                    >
                      {STRIPE_CANCEL_BTN_LABEL}
                    </button>
                    <button
                      className="btn btn-primary"
                      type="submit"
                      disabled={fullyRefunded}
                    >
                      {STRIPE_REFUND_BTN_LABEL}
                    </button>
                  </div>
                </div>
                <Overlay
                  showSpinner={showSpinner}
                  showSuccess={showSuccess}
                  showFailure={showFailure}
                  toggleSuccess={toggleSuccess}
                  toggleFailure={toggleFailure}
                  resetForm={resetForm}
                  selectedPaymentMethod={selectedPaymentMethod}
                  selectedPaymentType={selectedPaymentType}
                  selectedPaymentProcessor={selectedPayment.processor}
                  toggleForm={toggleStripeForm}
                  showForm={showStripeForm}
                  errorMessage={errorMessage}
                />
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default StripePaymentForm;
