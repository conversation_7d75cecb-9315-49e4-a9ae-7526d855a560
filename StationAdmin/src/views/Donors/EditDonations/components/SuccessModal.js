import React from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
} from "@coreui/react";

import { modalFlowStr, modalActions } from "../strings";
import checkmark from "../images/checkmark.svg";

const SuccessModal = ({
  closeModal,
  modalAction,
  responseContent,
  receipt,
}) => {
  return (
    <CModal visible={true} onClose={closeModal} backdrop={true} keyboard={true}>
      <CModalHeader onClose={closeModal}>
        <span className="badge badge-success">
          {modalFlowStr[modalAction].SuccessHeader}
        </span>
      </CModalHeader>
      <CModalBody className="text-center">
        <div className="checkmark-container">
          <img src={checkmark} className="bounceIn" alt="checkmark" />
          <p>{modalFlowStr[modalAction].SuccessBody}</p>
          <p>
            <strong>{responseContent}</strong>
          </p>
          {modalAction === modalActions.edit ? (
            <a href={receipt}>View Receipt</a>
          ) : null}
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton className="mx-auto" color="primary" onClick={closeModal}>
          {modalFlowStr[modalAction].SuccessBtnConfirm}
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default SuccessModal;
