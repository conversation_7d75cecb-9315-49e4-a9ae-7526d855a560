import React from "react";

const TextSearchQuery = ({ handleSearch, searchValue, searchType }) => {
  return (
    <div className="form-group">
      <label htmlFor="search">Text Search</label>
      <input
        name="search"
        type="text"
        className="form-control me-2"
        placeholder="Enter Search Query"
        onChange={e => {
          const searchText = e.target.value;
          const searchString = `?s=${searchText}`;
          handleSearch(searchText, searchString);
        }}
        value={searchValue}
      />
    </div>
  );
};

export default TextSearchQuery;
