/*  CheckPaymentForm  */

export const CHECK_FIELD_NAMES = [
  "checkAmount",
  "checkNumber",
  "checkDepositDate",
  "checkStatus",
  "checkProcessor",
];

export const CHECK_FORM_HEADING_ADD = "Add Check Payment";
export const CHECK_FORM_HEADING_EDIT = "Edit Check Payment";
export const CHECK_FORM_HEADING_DELETE = "Delete Check Payment";

export const CHECK_AMOUNT_LABEL = "Amount";
export const CHECK_NUMBER_LABEL = "Check Number";
export const CHECK_DEPOSIT_DATE_LABEL = "Date Deposited";
export const CHECK_STATUS_LABEL = "Status";
export const CHECK_STATUS_OPTIONS = [
  { label: "Succeeded", value: "succeeded" },
  { label: "Pending", value: "pending" },
  { label: "Failed", value: "failed" },
];
export const CHECK_PROCESSOR_LABEL = "Processor";
export const CHECK_PROCESSOR_OPTIONS = ["P-check", "K-check"];

export const ADD_CHECK_CANCEL_BTN_LABEL = "Cancel";
export const ADD_CHECK_CONFIRM_BTN_LABEL = "Add Check Payment";
export const EDIT_CHECK_CONFIRM_BTN_LABEL = "Edit Check Payment";
export const DELETE_CHECK_CONFIRM_BTN_LABEL = "Delete Check Payment";

export const CHECK_VALIDATION_AMOUNT_FAILED = "Invalid Amount";
export const CHECK_VALIDATION_NUMBER_FAILED = "Invalid Check Number";
export const CHECK_VALIDATION_DATE_FAILED = "Invalid Date";
export const CHECK_VALIDATION_STATUS_FAILED = "Invalid Status Selection";
export const CHECK_VALIDATION_PROCESSOR_FAILED = "Invalid Processor Selection";

/*  PaymentSpinner  */

export const CHECK_SUBMIT_ADD = "Adding Check Payment";
export const CHECK_SUBMIT_EDIT = "Editing Check Payment";
export const CHECK_SUBMIT_DELETE = "Deleting Check Payment";
export const CARD_SUBMIT_CHARGE = "Charging Card";
export const CASH_SUBMIT_ADD = "Adding Cash Payment";
export const CASH_SUBMIT_EDIT = "Editing Cash Payment";
export const CASH_SUBMIT_DELETE = "Deleting Cash Payment";

/*  PaymentSuccess  */

export const CHECK_SUBMIT_SUCCESS_ADD = "Check Payment Added Successfully.";
export const CHECK_SUBMIT_SUCCESS_EDIT = "Check Payment Edited Successfully.";
export const CHECK_SUBMIT_SUCCESS_DELETE =
  "Check Payment Deleted Successfully.";
export const CARD_SUBMIT_SUCCESS = "Card Charged Successfully.";
export const CASH_SUBMIT_SUCCESS_ADD = "Cash Payment Added Successfully.";
export const CASH_SUBMIT_SUCCESS_EDIT = "Cash Payment Edited Successfully.";
export const CASH_SUBMIT_SUCCESS_DELETE = "Cash Payment Deleted Successfully.";

/*  PaymentFailed  */

export const CHECK_SUBMIT_FAILURE_ADD = "Adding Check Payment Failed.";
export const CHECK_SUBMIT_FAILURE_EDIT = "Editing Check Payment Failed.";
export const CHECK_SUBMIT_FAILURE_DELETE = "Deleting Check Payment Failed.";
export const CARD_SUBMIT_FAILURE = "Card Charge Failed.";
export const CASH_SUBMIT_FAILURE_ADD = "Adding Cash Payment Failed.";
export const CASH_SUBMIT_FAILURE_EDIT = "Editing Cash Payment Failed.";
export const CASH_SUBMIT_FAILURE_DELETE = "Deleting Cash Payment Failed.";

/*  CardPaymentForm  */

export const CARD_FIELD_NAMES = [
  "cardAmount",
  "cardNumber",
  "cardSecurityCode",
  "cardExpirationMonth",
  "cardExpirationYear",
];

export const CARD_FORM_HEADING_ADD = "Add Card Payment";
export const CARD_FORM_HEADING_EDIT = "Edit Card Payment";
export const CARD_FORM_HEADING_DELETE = "Delete Card Payment";

export const CARD_AMOUNT_LABEL = "Amount";
export const CARD_NUMBER_LABEL = "Card Number";
export const CARD_SECURITY_CODE_LABEL = "Card Security Code";
export const CARD_EXPIRATION_MONTH_LABEL = "Expiration Month";
export const CARD_EXPIRATION_YEAR_LABEL = "Expiration Month";

export const ADD_CARD_CANCEL_BTN_LABEL = "Cancel";
export const ADD_CARD_CONFIRM_BTN_LABEL = "Charge Card";

export const CARD_VALIDATION_AMOUNT_FAILED = "Invalid Amount";
export const CARD_VALIDATION_NUMBER_FAILED = "Invalid Card Number";
export const CARD_VALIDATION_SECURITY_CODE_FAILED =
  "Invalid Card Security Code";
export const CARD_VALIDATION_EXPIRATION_MONTH_FAILED =
  "Invalid Expiration Month";
export const CARD_VALIDATION_EXPIRATION_YEAR_FAILED = "Invalid Expiration Year";

/*   CashPaymentForm   */

export const CASH_FIELD_NAMES = ["cashAmount", "cashDepositDate"];

export const CASH_FORM_HEADING_ADD = "Add Cash Payment";
export const CASH_FORM_HEADING_EDIT = "Edit Cash Payment";
export const CASH_FORM_HEADING_DELETE = "Delete Cash Payment";

export const CASH_AMOUNT_LABEL = "Amount";
export const CASH_DEPOSIT_DATE_LABEL = "Date Deposited";

export const ADD_CASH_CANCEL_BTN_LABEL = "Cancel";
export const ADD_CASH_CONFIRM_BTN_LABEL = "Add Cash Payment";
export const EDIT_CASH_CONFIRM_BTN_LABEL = "Edit Cash Payment";
export const DELETE_CASH_CONFIRM_BTN_LABEL = "Delete Cash Payment";

export const CASH_VALIDATION_AMOUNT_FAILED = "Invalid Amount";
export const CASH_VALIDATION_DATE_FAILED = "Invalid Date";

/*   StripePaymentForm   */
export const STRIPE_FIELD_NAMES = ["stripeAmount", "stripeReason"];
export const STRIPE_FORM_HEADING = "Refund Stripe Payment";
export const STRIPE_REFUND_REASON_LABEL = "Reason for Refund";

export const STRIPE_AMOUNT_LABEL = "Amount";

export const STRIPE_CANCEL_BTN_LABEL = "Cancel";
export const STRIPE_REFUND_BTN_LABEL = "Refund Stripe Payment";

export const STRIPE_REFUND_OPTIONS = [
  { value: "duplicate", label: "Duplicate" },
  { value: "fraudulent", label: "Fraudulent" },
  { value: "requested_by_customer", label: "Requested by Customer" },
];

export const STRIPE_VALIDATION_AMOUNT_FAILED = "Invalid Amount";
export const STRIPE_SUBMIT_FAILURE = "Stripe Refund Failed: ";
export const STRIPE_SUBMIT_REFUND = "Refunding Stripe Payment";
export const STRIPE_REFUND_SUCCESS = "Stripe Payment Refunded Successfully";

/*   SearchContainer   */

export const searchOptions = {
  name: "Name / ID / Email",
  campaign: "Campaign",
  dateRange: "Date Range",
};

/*   ContactForm   */

export const paymentStatusOptions = {
  all: "Show All",
  paid: "Paid",
  unpaid: "Unpaid",
  partiallyRefunded: "Partially Refunded",
  partiallyPaid: "Partially Paid",
  refunded: "Refunded",
};

/*   Modal Flow   */

export const modalActions = {
  delete: "delete",
  edit: "edit",
  cancel: "cancel",
};

export const modalFlows = {
  edit: "edit",
  confirm: "confirm",
  processing: "processing",
  success: "success",
  failure: "failure",
};

export const modalFlowStr = {
  delete: {
    confirmationHeader: "Please confirm deleting this donation",
    confirmationBody: "Warning: Deletions are permanent.",
    confirmationBtnCancel: "Cancel",
    confirmationBtnConfirm: "Confirm Deleting Donation",
    processingHeader: "Please Wait",
    processingBody: "Deleting Donation...",
    SuccessHeader: "Success",
    SuccessBody: "",
    SuccessBtnConfirm: "Return to Manage Donors",
    FailureHeader: "Error",
    FailureBody:
      "There was an error when deleting the selected donation. Please try again. \nIf the error persists, please contact your administrator.",
    FailureBtnConfirm: "OK",
  },
  cancel: {
    confirmationHeader: "Please confirm cancelling this subscription",
    confirmationBody: "Warning: Cancelations are permanent.",
    confirmationBtnCancel: "Back",
    confirmationBtnConfirm: "Confirm Cancelling Subscription",
    processingHeader: "Please Wait",
    processingBody: "Cancelling Subscription...",
    SuccessHeader: "Success",
    SuccessBody: "",
    SuccessBtnConfirm: "Return to Manage Donors",
    FailureHeader: "Error",
    FailureBody:
      "There was an error when cancelling the selected subscription. Please try again. \nIf the error persists, please contact your administrator.",
    FailureBtnConfirm: "OK",
  },
  edit: {
    confirmationHeader: "Please review and confirm your changes",
    confirmationBody: "",
    confirmationBtnCancel: "Go Back to Edit Form",
    confirmationBtnConfirm: "Confirm Changes to Donation",
    processingHeader: "Please Wait",
    processingBody: "Processing Donation Changes...",
    SuccessHeader: "Success",
    SuccessBody:
      "Your changes to the donation have been successfully processed.",
    SuccessBtnConfirm: "Return to Edit Donations",
    FailureHeader: "Error",
    FailureBody:
      "There was an error when updating the selected donation. Please try again. \nIf the error persists, please contact your administrator.",
    FailureBtnConfirm: "Return to Donation Edit Form",
  },
};
