import { useState, useEffect } from "react";

import DonorInformation from "./components/DonorInformation";
import EditDonations from "../EditDonations/EditDonations";
import CreateDonation from "../CreateDonation/CreateDonation";
import useToggle from "../../Hooks/useToggle";
import DonorSearch from "./components/DonorSearch";
import { useLocation } from "react-router-dom";
import { CButton, CCard } from "@coreui/react";
import { useAppSelector } from "../../../hooks";

const Manage = () => {
  const [selectedDonor, setSelectedDonor] = useState(null);
  const [showCreateDonation, toggleCreateDonation] = useToggle();
  const [showEditDonor, toggleEditDonor] = useToggle();
  const [hideInfo, setHideInfo] = useState(false);
  const location = useLocation();

  const testMode = useAppSelector((state) => state.testMode);
  const accessLevel = useAppSelector((state) => state.user.accessLevel);

  const showOnlyCreateDonation = () => {
    toggleCreateDonation();
    if (showEditDonor) {
      toggleEditDonor();
    }
  };

  const showOnlyEditDonor = () => {
    setHideInfo(false);
    toggleEditDonor();
    if (showCreateDonation) {
      toggleCreateDonation();
    }
  };

  //try using a callback from react router, this is doing something weird.

  const refreshCurrentDonor = (kpfaId) => {
    //todo: rename, and possibly refactor with the higher level component I believe is doing the same fetch.
    return new Promise((resolve) => {
      const headers = {
        Authorization: `Bearer ${localStorage.getItem("jwt")}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };
      let url;
      let id;

      if (kpfaId) {
        id = kpfaId;
      } else {
        id = location.state?.linkedDonorID
          ? location.state.linkedDonorID
          : selectedDonor.id;
      }

      url = `https://${
        testMode.value ? "api.staging." : "api."
      }kpfa.org/accounts/donors/${id}`;

      fetch(url, {
        headers,
        method: "GET",
      })
        .then((response) => {
          if (response.ok) {
            return response.json();
          } else {
            throw new Error("donation fetch failed!");
          }
        })
        .then((json) => {
          setSelectedDonor(json.records[0]);
          resolve();
        })
        .catch((error) => {
          console.log(error);
        });
    });
  };

  const newDonor = () => {
    setSelectedDonor(null);
    toggleCreateDonation(true);
  };

  useEffect(() => {
    if (location.state?.linkedDonorID) {
      refreshCurrentDonor();
    }
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    setHideInfo(false);
  }, [selectedDonor, setHideInfo]);

  return (
    <div className="animated fadeIn">
      <DonorSearch setSelectedDonor={setSelectedDonor} />
      <div className="my-4">
        <CButton onClick={showOnlyCreateDonation} className="me-2">
          {showCreateDonation ? "Cancel Donation ▲" : "Create New Donation ▼"}
        </CButton>
        {selectedDonor ? (
          <>
            <CButton onClick={showOnlyEditDonor} className="mx-2">
              <span className="cui-pencil" />{" "}
              {showEditDonor ? "Cancel Donor Edits" : "Edit Donor"}
            </CButton>
            <CButton onClick={newDonor} className="mx-2">
              New Donor
            </CButton>
          </>
        ) : null}
        {accessLevel === "CallCenter" ? (
          <a href="https://secure.kpfa.org/changes">
            <CButton className="mx-2">Changes/Corrections</CButton>
          </a>
        ) : null}
      </div>
      {showCreateDonation ? (
        <CreateDonation
          selectedDonor={selectedDonor}
          setSelectedDonor={setSelectedDonor}
          setHideInfo={setHideInfo}
          toggleCreateDonation={toggleCreateDonation}
        />
      ) : (
        ""
      )}
      {!hideInfo ? (
        <>
          <CCard className="mt-4 container-fluid p-0">
            <DonorInformation
              selectedDonor={selectedDonor}
              setSelectedDonor={setSelectedDonor}
              showEditDonor={showEditDonor}
              toggleEditDonor={toggleEditDonor}
              refreshCurrentDonor={refreshCurrentDonor}
            />
          </CCard>
          <EditDonations
            selectedDonor={selectedDonor}
            setSelectedDonor={setSelectedDonor}
            refreshCurrentDonor={refreshCurrentDonor}
          />{" "}
        </>
      ) : null}
    </div>
  );
};

export default Manage;
