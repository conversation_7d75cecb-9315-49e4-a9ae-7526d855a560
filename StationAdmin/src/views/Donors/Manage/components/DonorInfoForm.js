import { useState } from "react";
import { Formik, Form, Field } from "formik";
import InputFlexible from "../../../Form/InputFlexible";
import donorInfoSchema from "../../../Form/DonorInfoValidationSchema";
import Textarea from "../../../Form/Textarea";
import { stateOptions, countryOptions } from "../../../Form/constants";
import {
  CAlert,
  CRow,
  CCol,
  CButton,
  CButtonGroup,
  CFormLabel,
} from "@coreui/react";
import SelectField from "../../../Form/SelectField";
import PhoneMask from "../../../Form/PhoneMask";
import { useAppSelector } from "../../../../hooks";
import DuplicateGroupForm from "./DuplicateGroupForm";

const DonorInfoForm = (props) => {
  const [successAlert, setSuccessAlert] = useState({
    display: false,
    status: "",
    message: "",
    color: "",
  });
  const [failAlert, setFailAlert] = useState({ display: false, message: "" });

  const testMode = useAppSelector((state) => state.testMode);
  const accessLevel = useAppSelector((state) => state.user.accessLevel);

  function toggleFailAlert(error) {
    setFailAlert({ display: true, message: error });
  }

  function toggleSuccessAlert(status, message) {
    let color = "";

    if (status === "success") {
      color = "success";
    } else {
      color = "danger";
    }

    setSuccessAlert({ display: true, message: message, color: color });
  }

  function hideAlerts() {
    setFailAlert({ display: false, message: "" });
    setSuccessAlert({ display: false, message: "" });
  }

  return (
    <>
      <Formik
        enableReinitialize={true}
        initialValues={{
          type: props.selectedDonor.type
            ? props.selectedDonor.type
            : "Individual",
          firstname: props.selectedDonor.firstname
            ? props.selectedDonor.firstname
            : "",
          lastname: props.selectedDonor.lastname
            ? props.selectedDonor.lastname
            : "",
          phone: props.selectedDonor.phone ? (() => {
            // Handle phone numbers with country code prefixes
            const phoneStr = props.selectedDonor.phone.toString();
            const phoneDigits = phoneStr.replace(/\D/g, '');

            // Handle US numbers with country code 1 (11 digits starting with 1)
            // This fixes the issue where "+1" or "1" prefixed numbers get truncated
            if (phoneDigits.length === 11 && phoneDigits.startsWith('1')) {
              return phoneDigits.substring(1); // Remove the '1' prefix for US numbers
            }

            // For 10-digit numbers, return as-is
            if (phoneDigits.length === 10) {
              return phoneDigits;
            }

            // For other lengths, return the cleaned digits (international numbers, etc.)
            return phoneDigits;
          })() : "",
          email: props.selectedDonor.email ? props.selectedDonor.email : "",
          address1: props.selectedDonor.address1
            ? props.selectedDonor.address1
            : "",
          address2: props.selectedDonor.address2
            ? props.selectedDonor.address2
            : "",
          city: props.selectedDonor.city ? props.selectedDonor.city : "",
          state: props.selectedDonor.state ? props.selectedDonor.state : "",
          country: props.selectedDonor.country
            ? props.selectedDonor.country
            : "",
          postal_code: props.selectedDonor.postal_code
            ? props.selectedDonor.postal_code
            : "",
          notes: props.selectedDonor.notes ? props.selectedDonor.notes : "",
          deceased: props.selectedDonor.deceased
            ? props.selectedDonor.deceased
            : false,
          partner_firstname: props.selectedDonor.partner_firstname
            ? props.selectedDonor.partner_firstname
            : "",
          partner_lastname: props.selectedDonor.partner_lastname
            ? props.selectedDonor.partner_lastname
            : "",
          donotsolicit: props.selectedDonor.donotsolicit
            ? props.selectedDonor.donotsolicit
            : false,
          allegiance_id: props.selectedDonor.allegiance_id
            ? props.selectedDonor.allegiance_id
            : "",
        }}
        validationSchema={donorInfoSchema}
        onSubmit={(values, { setSubmitting }) => {
          failAlert.display = successAlert.display = false;

          let url = `https://${
            testMode.value ? "api.staging." : "api."
          }kpfa.org/accounts/donors/${props.selectedDonor.id}`;

          const headers = {
            Authorization: `Bearer ${localStorage.getItem("jwt")}`,
            "Content-Type": "application/json",
            Accept: "application/json",
          };
          fetch(url, {
            method: "PATCH",
            body: JSON.stringify(values),
            headers,
          })
            .then((response) => {
              if (response.ok) {
                toggleSuccessAlert(
                  "success",
                  `Donor ${props.selectedDonor.id} updated`,
                );
                props.setSelectedDonor({ ...props.selectedDonor, ...values });
              } else {
                throw new Error(
                  `Update donor failed! HTTP Response code: ${response.status}`,
                );
              }
            })
            .catch((error) => {
              console.log(error);
              toggleFailAlert("Error updating donor");
            });

          setSubmitting(false);
        }}
      >
        {({ values, setFieldValue }) => (
          <Form className="col-12 mt-2 mb-4 p-3">
            <CRow className="pb-4">
              <div className="col-md-3 form-group">
                <Field
                  name="firstname"
                  label="First Name"
                  component={InputFlexible}
                />
              </div>
              <div className="col-md-3 form-group">
                <Field
                  name="lastname"
                  label="Last Name"
                  component={InputFlexible}
                />
              </div>
              <div className="col-md-2 form-group">
                <Field
                  name="type"
                  label="Type"
                  component={SelectField}
                  labelPlacement="top"
                  options={[
                    { value: "Individual", label: "Individual" },
                    { value: "Couple", label: "Couple" },
                    { value: "Foundation", label: "Foundation" },
                    { value: "Corporate", label: "Corporate" },
                    { value: "Goverment", label: "Goverment" },
                    { value: "Trust", label: "Trust" },
                    { value: "Charity", label: "Charity" },
                    { value: "Fund", label: "Fund" },
                    { value: "Test", label: "Test" },
                    { value: "Fraudulent", label: "Fraudulent" },
                  ]}
                />
              </div>
              <div className="col-md-2">
                <Field
                  name="deceased"
                  label="Vital Status"
                  component={SelectField}
                  labelPlacement="top"
                  options={[
                    { label: "Alive", value: false },
                    { label: "Deceased", value: true },
                  ]}
                ></Field>
              </div>
              <div className="col-md-2">
                <Field
                  name="donotsolicit"
                  label="Solicit"
                  component={SelectField}
                  labelPlacement="top"
                  options={[
                    { label: "Not Okay", value: true },
                    { label: "Okay", value: false },
                  ]}
                />
              </div>
            </CRow>
            <CRow className="pb-4">
              <CCol>
                <Field
                  name="partner_firstname"
                  label="Partner First Name"
                  component={InputFlexible}
                />
              </CCol>
              <CCol>
                <Field
                  name="partner_lastname"
                  label="Partner Last Name"
                  component={InputFlexible}
                />
              </CCol>
              <CCol>
                <CFormLabel>Phone</CFormLabel>
                <PhoneMask
                  country={values.country}
                  setFieldValue={setFieldValue}
                  component={InputFlexible}
                />
              </CCol>
              <CCol>
                <Field
                  type="email"
                  name="email"
                  label="Email"
                  component={InputFlexible}
                />
              </CCol>
            </CRow>
            <CRow className="pb-4">
              <CCol>
                <Field
                  name="address1"
                  label="Address 1"
                  component={InputFlexible}
                />
              </CCol>
              <CCol>
                <Field
                  name="address2"
                  label="Address 2"
                  component={InputFlexible}
                />
              </CCol>
            </CRow>

            <CRow className="pb-4">
              <CCol>
                <Field name="city" label="City" component={InputFlexible} />
              </CCol>
              <CCol>
                {values.country === "US" ? (
                  <Field
                    className="mt-auto"
                    name="state"
                    label="State"
                    component={SelectField}
                    options={stateOptions}
                    labelPlacement="top"
                  />
                ) : (
                  <Field
                    name="state"
                    label="State or Province"
                    component={InputFlexible}
                    placeholder="Ontario"
                    labelPlacement="top"
                  />
                )}
              </CCol>
              <CCol>
                <Field
                  name="country"
                  label="Country"
                  component={SelectField}
                  labelPlacement="top"
                  options={countryOptions}
                  onChange={(value) => {
                    if (value === "US") {
                      setFieldValue("postal_code", "");
                      setFieldValue("state", "CA");
                    } else {
                      setFieldValue("state", "");
                      setFieldValue("postal_code", "");
                    }
                  }}
                />
              </CCol>
              <CCol>
                <Field
                  name="postal_code"
                  label="Postal Code"
                  component={InputFlexible}
                />
              </CCol>
            </CRow>

            <CRow className="pb-4">
              <CCol>
                <Field name="notes" label="Notes" component={Textarea}></Field>
              </CCol>
            </CRow>

            {accessLevel !== "CallCenter" ? (
              <CRow className="pb-4">
                <CCol>
                  <Field
                    name="allegiance_id"
                    label="Allegiance ID"
                    component={InputFlexible}
                  />
                </CCol>
              </CRow>
            ) : null}
            <CRow>
              <CCol>
                <CButtonGroup>
                  <CButton type="submit">Update</CButton>
                  <CButton onClick={props.toggleEditDonor} variant="outline">
                    <span className="cui-pencil" /> Cancel Donor Edits
                  </CButton>
                </CButtonGroup>

                {failAlert.display && (
                  <CAlert
                    color="danger"
                    className="mt-4 animated fadeIn fadeOut"
                  >
                    {failAlert.message}
                  </CAlert>
                )}
                {successAlert.display && (
                  <CAlert
                    color={successAlert.color}
                    className="mt-4 animated fadeIn fadeOut alert-dismissible"
                  >
                    {successAlert.message}
                  </CAlert>
                )}
              </CCol>
            </CRow>
          </Form>
        )}
      </Formik>
      <DuplicateGroupForm
        hideAlerts={hideAlerts}
        selectedDonorId={props.selectedDonor.id}
        toggleFailAlert={toggleFailAlert}
        toggleSuccessAlert={toggleSuccessAlert}
      />
    </>
  );
};

export default DonorInfoForm;
