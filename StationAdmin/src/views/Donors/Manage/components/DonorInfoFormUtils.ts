import { DuplicateGroup, DuplicateDonor } from "../../../../types/graphql";

// Function to format duplicate group labels
export const formatDuplicateGroupLabel = (
  groups: DuplicateGroup[],
): { label: string; value: string }[] => {
  return groups.map((group) => ({
    label: group.name,
    value: group.id,
  }));
};

export interface CreateGroupResult {
  success: boolean;
  data?: any;
  error?: { message: string };
}

export const handleCreateGroup = async (
  createDuplicateGroup: Function,
  newGroupName: string,
): Promise<CreateGroupResult> => {
  const newGroup = {
    name: newGroupName,
  };

  try {
    const result = await createDuplicateGroup(newGroup).unwrap();

    if (!result.createDuplicateGroup) {
      throw new Error("Check that your duplicate group name is unique.");
    }

    return { success: true, data: result };
  } catch (error) {
    const typedError = error as { message: string };
    return { success: false, error: typedError };
  }
};

export const handleSelectGroup = async (
  updateDuplicateDonor: Function,
  createDuplicateDonor: Function,
  donor: DuplicateDonor,
  selectedDonorId: number,
  newGroupId: string,
): Promise<CreateGroupResult> => {
  try {
    if (donor) {
      const result = await updateDuplicateDonor({
        id: donor.id,
        group: newGroupId,
      }).unwrap();
      return { success: true, data: result };
    } else {
      const result = await createDuplicateDonor({
        kpfaId: selectedDonorId,
        group: newGroupId,
      }).unwrap();
      return { success: true, data: result };
    }
  } catch (error) {
    const typedError = error as { message: string };
    return { success: false, error: typedError };
  }
};

export const updateDonorPrimaryGroup = async (
  updateDuplicateGroup: Function,
  groupId: string,
  donorId: string | null,
): Promise<CreateGroupResult> => {
  try {
    const result = await updateDuplicateGroup({
      id: groupId,
      primaryDonorId: donorId,
    }).unwrap();
    return { success: true, data: result };
  } catch (error) {
    const typedError = error as { message: string };
    return { success: false, error: typedError };
  }
};
