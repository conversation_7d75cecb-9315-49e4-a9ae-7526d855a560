import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>utton } from "@coreui/react";

import DonorInfoForm from "./DonorInfoForm";
import useMysqlDate from "../../../Hooks/useMysqlDate";
import DonorAddress from "../../../Shared/DonorAddress";
import useFormatPhone from "../../../Hooks/useFormatPhone";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useAppSelector } from "../../../../hooks";
import { useGetDuplicateDonorQuery } from "../../../../services/graphql";

const DonorInformation = ({
  setSelectedDonor,
  selectedDonor,
  showEditDonor,
  toggleEditDonor,
  refreshCurrentDonor,
}) => {
  const [ytdTotal, setYtdTotal] = useState(0);
  const accessLevel = useAppSelector((state) => state.user.accessLevel);
  const { data: duplicateDonorData } = useGetDuplicateDonorQuery({
    kpfaId: selectedDonor?.id,
  });

  useEffect(() => {
    if (selectedDonor?.donations) {
      let total = 0;

      for (const donation of selectedDonor.donations) {
        for (const payment of donation.payments) {
          if (
            payment.transaction_status === "succeeded" &&
            payment.date_created.substring(0, 4) ===
              new Date().getFullYear().toString()
          ) {
            total += payment.amount - payment.amount_refunded;
          }
        }
      }

      setYtdTotal(
        parseFloat(total)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ","),
      );
    }
  }, [selectedDonor]);

  const testMode = useAppSelector((state) => state.testMode.value);

  const StaticDonorInfo = () => {
    const phoneNumber = useFormatPhone(
      selectedDonor.phone,
      selectedDonor.country,
    );
    return (
      <div className="row mb-4">
        <div className="col-6">
          <ul className="list-group list-group-flush">
            <li className="list-group-item border-white py-1">
              <strong>{new Date().getFullYear()} Total: </strong>${ytdTotal}
            </li>
            <li className="list-group-item border-white py-1">
              <strong>Donor Since: </strong>
              {useMysqlDate(selectedDonor.date_created).toDateString()}
            </li>
            <li className="list-group-item border-white py-1">
              <strong>Solicit: </strong>
              {selectedDonor.donotsolicit ? "Do not Solict" : "OK"}
            </li>
            <li className="list-group-item border-white py-1">
              <strong>Donations: </strong>
              {selectedDonor.donations.length}
            </li>
            <li className="list-group-item border-white py-1">
              <strong>Type: </strong>
              {selectedDonor.type}
            </li>
            {selectedDonor.partner_firstname ||
            selectedDonor.partner_lastname ? (
              <li className="list-group-item border-white py-1">
                <strong>Partner: </strong>
                {selectedDonor.partner_firstname}{" "}
                {selectedDonor.partner_lastname}
              </li>
            ) : null}
            {selectedDonor.notes ? (
              <li className="list-group-item border-white py-1">
                <strong>Notes: </strong>
                {selectedDonor.notes}
              </li>
            ) : null}
            {selectedDonor.stripe_cus_id ? (
              <li className="list-group-item border-white py-1">
                <strong>Stripe ID: </strong>
                <a
                  href={`https://dashboard.stripe.com/${
                    testMode ? "test/" : ""
                  }customers/${selectedDonor.stripe_cus_id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {selectedDonor.stripe_cus_id}
                </a>
              </li>
            ) : null}
            {selectedDonor.memsys_id ? (
              <li className="list-group-item border-white py-1">
                <strong>Memsys ID: </strong>
                {selectedDonor.memsys_id}
              </li>
            ) : null}
            {selectedDonor.allegiance_id && accessLevel !== "CallCenter" ? (
              <li className="list-group-item border-white py-1">
                <strong>Allegiance ID: </strong>
                {selectedDonor.allegiance_id}
              </li>
            ) : null}
          </ul>
        </div>
        <div className="col-6">
          <DonorAddress selectedDonor={selectedDonor} />
          {selectedDonor.phone ? (
            <>
              <strong>Phone:</strong> {phoneNumber}
              <br />
            </>
          ) : null}
          {selectedDonor.email ? (
            <>
              <strong>Email: </strong>
              <a href={"mailto:" + selectedDonor.email}>
                {selectedDonor.email}
              </a>
            </>
          ) : null}
        </div>
      </div>
    );
  };

  if (selectedDonor) {
    return (
      <>
        <CAlert
          color="secondary"
          className="rounded-0 text-dark d-flex justify-content-between"
        >
          <span>
            <strong>
              {selectedDonor.firstname} {selectedDonor.lastname}
              {duplicateDonorData?.duplicateDonors.length
                ? duplicateDonorData.duplicateDonors[0]?.primaryGroup
                  ? " (Primary)"
                  : " (Duplicate)"
                : null}
              {duplicateDonorData?.duplicateDonors[0]?.group?.primaryDonor &&
              !duplicateDonorData?.duplicateDonors[0]?.primaryGroup ? (
                <CButton
                  size="sm"
                  color="secondary"
                  shape="rounded-pill"
                  className="ms-2 text-white"
                  onClick={() =>
                    refreshCurrentDonor(
                      duplicateDonorData.duplicateDonors[0].group.primaryDonor
                        .kpfaId,
                    )
                  }
                >
                  View Primary
                </CButton>
              ) : null}
            </strong>
            {selectedDonor.deceased ? " (Deceased)" : ""}
          </span>
          <span>
            <strong className="me-2">KPFA ID: </strong>
            {selectedDonor.id}
          </span>
        </CAlert>
        {showEditDonor ? (
          <DonorInfoForm
            selectedDonor={selectedDonor}
            showEditDonor={showEditDonor}
            setSelectedDonor={setSelectedDonor}
            toggleEditDonor={toggleEditDonor}
          />
        ) : (
          StaticDonorInfo()
        )}
      </>
    );
  }

  return (
    <h4 className="mx-2 my-5 text-center">
      <FontAwesomeIcon icon="info-circle" /> Search for a donor to view their
      information
    </h4>
  );
};

export default DonorInformation;
