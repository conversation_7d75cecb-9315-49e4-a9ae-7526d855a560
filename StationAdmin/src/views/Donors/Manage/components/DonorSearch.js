import debounce from "debounce-promise";
import { useState, useCallback } from "react";
import AsyncSelect from "react-select/async";
import { useAppSelector } from "../../../../hooks";

export default function DonorSearch({ setSelectedDonor }) {
  // This needs to become a controlled component so track state
  const [value, setValue] = useState();
  const [inputValue, setInputValue] = useState("");
  const [selectedOption, setSelectedOption] = useState(null);

  const onInputChange = (inputValue, { action }) => {
    // onBlur => setInputValue to last selected value
    if (action === "input-blur") {
      setInputValue(value ? value.label : "");
    }

    // onInputChange => update inputValue
    else if (action === "input-change") {
      setInputValue(inputValue);
    }
  };

  const onChange = (option) => {
    setValue(option);
    setInputValue(option ? option.label : "");

    if (option) {
      setSelectedOption(option);
      setSelectedDonor(option?.value);
    }
  };

  const testMode = useAppSelector((state) => state.testMode);

  const searchDonors = async (inputValue) => {
    try {
      const headers = {
        Authorization: `Bearer ${localStorage.getItem("jwt")}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };

      let url = testMode.value
        ? `https://${testMode.route}kpfa.org/accounts/donors/?s=${inputValue}`
        : `https://api.kpfa.org/accounts/donors/?s=${inputValue}`;

      let response = await fetch(url, {
        headers,
        method: "GET",
      });

      if (!response.ok) {
        throw new Error("donation fetch failed!");
      }

      let json = await response.json();

      if (json?.records) {
        return json.records.map((donor) => ({
          label: `${donor.firstname} ${donor.lastname}`,
          value: donor,
        }));
      }
    } catch (error) {
      console.log(error);
    }
  };

  const promiseOptions = (inputValue) => {
    return new Promise((resolve) => {
      resolve(searchDonors(inputValue));
    });
  };

  //eslint-disable-next-line
  const debouncedLoadOptions = useCallback(debounce(promiseOptions, 300), [
    testMode,
  ]);

  const customStyles = {
    valueContainer: (provided) => ({
      ...provided,
      paddingTop: 0,
      paddingBottom: 0,
    }),
  };

  return (
    <AsyncSelect
      styles={customStyles}
      autoFocus
      cacheOptions
      loadOptions={debouncedLoadOptions}
      isClearable={true}
      onInputChange={onInputChange}
      onChange={onChange}
      inputValue={inputValue}
      value={selectedOption}
      placeholder="Search by name, email, phone, or ID"
      classNamePrefix="react-select"
    />
  );
}
