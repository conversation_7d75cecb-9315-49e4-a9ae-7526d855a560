import { useEffect, useState, useRef } from "react";
import { Formik, Form, Field } from "formik";
import { <PERSON>utton, CForm<PERSON>abel, CRow, CCol } from "@coreui/react";
import SelectField from "../../../Form/SelectField";
import InputFlexible from "../../../Form/InputFlexible";
import Checkbox from "../../../Form/Checkbox";
import {
  useCreateDuplicateGroupMutation,
  useGetDuplicateGroupsQuery,
  useGetDuplicateDonorQuery,
  useUpdateDuplicateDonorMutation,
  useCreateDuplicateDonorMutation,
  useUpdateDuplicateGroupMutation,
} from "../../../../services/graphql";
import {
  formatDuplicateGroupLabel,
  handleCreateGroup,
  handleSelectGroup,
  updateDonorPrimaryGroup,
  CreateGroupResult,
} from "./DonorInfoFormUtils";

interface DuplicateGroupFormProps {
  hideAlerts: () => void;
  selectedDonorId: number;
  toggleFailAlert: (error: string) => void;
  toggleSuccessAlert: (status: string, message: string) => void;
}

const DuplicateGroupForm: React.FC<DuplicateGroupFormProps> = ({
  hideAlerts,
  selectedDonorId,
  toggleFailAlert,
  toggleSuccessAlert,
}) => {
  const { data: duplicateGroupsData } = useGetDuplicateGroupsQuery();
  const { data: donorData } = useGetDuplicateDonorQuery({
    kpfaId: selectedDonorId,
  });
  const [createDuplicateGroup] = useCreateDuplicateGroupMutation();
  const [updateDuplicateDonor] = useUpdateDuplicateDonorMutation();
  const [createDuplicateDonor] = useCreateDuplicateDonorMutation();
  const [updateDuplicateGroup] = useUpdateDuplicateGroupMutation();
  const [donor, setDonor] = useState<any>(null);
  const [initialGroup, setInitialGroup] = useState("");
  const [otherDups, setOtherDups] = useState<number[]>();
  const [isPrimary, setIsPrimary] = useState<boolean>();
  const prevDonorIdRef = useRef<number>();

  useEffect(() => {
    if (donorData && donorData.duplicateDonors.length > 0) {
      const fetchedDonor = donorData.duplicateDonors[0];
      setDonor(fetchedDonor);
      if (fetchedDonor.group && fetchedDonor.group.id) {
        setInitialGroup(fetchedDonor.group.id);
      }

      if (fetchedDonor?.primaryGroup?.id) {
        setIsPrimary(true);
      } else {
        setIsPrimary(false);
      }

      if (duplicateGroupsData?.duplicateGroups && donor?.group?.id) {
        if (duplicateGroupsData.duplicateGroups.length) {
          const matchingGroup = duplicateGroupsData.duplicateGroups
            .find((group) => group.id === donor.group.id);
          
          if (matchingGroup) {
            setOtherDups(
              matchingGroup.duplicateDonors
                .filter((altDonor) => altDonor.kpfaId !== donor.kpfaId)
                .map((altDonor) => altDonor.kpfaId)
            );
          }
        }
      }
    } else {
      setDonor(null);
      setInitialGroup("");
      setOtherDups([]);
    }
  }, [donorData, duplicateGroupsData, donor]);

  useEffect(() => {
    if (prevDonorIdRef.current !== selectedDonorId) {
      hideAlerts();
      prevDonorIdRef.current = selectedDonorId;
    }
  }, [selectedDonorId, hideAlerts]);

  return (
    <Formik
      enableReinitialize={true}
      initialValues={{
        new_group: "",
        duplicate_group_id: initialGroup,
        is_primary: isPrimary,
      }}
      onSubmit={(values, { setSubmitting }) => {
        handleCreateGroup(createDuplicateGroup, values.new_group)
          .then((result) => {
            if (result && result.success) {
              toggleSuccessAlert(
                "success",
                "Duplicate group created successfully",
              );
            } else {
              toggleFailAlert(
                "Failed to create group: " +
                  (result.error?.message || "Unknown error"),
              );
            }
            setSubmitting(false);
          })
          .catch((error) => {
            toggleFailAlert(
              "Failed to create group: " +
                (error as { message: string }).message,
            );
            setSubmitting(false);
          });
      }}
    >
      {({ setFieldValue }) => (
        <Form className="col-12 mb-4 px-3">
          {donorData?.duplicateDonors[0]?.group?.id && donor?.group?.id ? (
            <CRow className="pb-4">
              <CCol>
                <h6>
                  This donor is marked as a duplicate in the group "
                  {donor.group.name}"
                  {otherDups?.length
                    ? `, along with account${otherDups.length > 1 ? "s" : ""}
                  ${otherDups?.join(", ")}`
                    : null}
                </h6>
              </CCol>
            </CRow>
          ) : null}

          {otherDups?.length ? (
            <CRow className="pb-4">
              <CCol>
                <Field
                  name="isPrimary"
                  label="Primary Account"
                  component={Checkbox}
                  value={isPrimary}
                  onChange={() => {
                    let primaryDonorId = null;
                    if (isPrimary) {
                      //make it not primary!
                      setFieldValue("isPrimary", false);
                      setIsPrimary(false);
                    } else if (donor.group.id) {
                      setFieldValue("isPrimary", true);
                      setIsPrimary(true);
                      primaryDonorId = donor.id;
                    }
                    updateDonorPrimaryGroup(
                      updateDuplicateGroup,
                      donor.group.id,
                      primaryDonorId,
                    )
                      .then((result: CreateGroupResult) => {
                        if (result && result.success) {
                          toggleSuccessAlert(
                            "success",
                            "Donor primary status changed successfully",
                          );
                        } else {
                          toggleFailAlert(
                            "Failed to update donor: " +
                              (result.error?.message || "Unknown error"),
                          );
                        }
                      })
                      .catch((error: { message: string }) => {
                        toggleFailAlert(
                          "Failed to update group: " +
                            (error as { message: string }).message,
                        );
                      });
                  }}
                />
              </CCol>
            </CRow>
          ) : null}
          <CRow className="pb-4">
            <CCol md={6}>
              <Field
                name="duplicate_group_id"
                label="Select Duplicate Group"
                labelPlacement="top"
                component={SelectField}
                options={formatDuplicateGroupLabel(
                  duplicateGroupsData?.duplicateGroups || [],
                )}
                onChange={(selectedOption: string) => {
                  const selectedGroup =
                    duplicateGroupsData?.duplicateGroups.find(
                      (group: { id: string }) => group.id === selectedOption,
                    );
                  if (selectedGroup) {
                    setFieldValue("duplicate_group_id", selectedOption);
                    handleSelectGroup(
                      updateDuplicateDonor,
                      createDuplicateDonor,
                      donor,
                      Number(selectedDonorId),
                      selectedGroup.id,
                    )
                      .then((result) => {
                        if (result && result.success) {
                          toggleSuccessAlert(
                            "success",
                            "Duplicate group updated successfully",
                          );
                        } else {
                          toggleFailAlert(
                            "Failed to update group: " +
                              (result.error?.message || "Unknown error"),
                          );
                        }
                      })
                      .catch((error) => {
                        toggleFailAlert(
                          "Failed to update group: " +
                            (error as { message: string }).message,
                        );
                      });
                  }
                }}
              />
            </CCol>
            <CCol md={6}>
              <CFormLabel>Create Duplicate Group</CFormLabel>
              <div className="d-flex">
                <Field
                  name="new_group"
                  placeholder="New Group Name"
                  component={InputFlexible}
                />
                <CButton type="submit" className="ms-2">
                  Create
                </CButton>
              </div>
            </CCol>
          </CRow>
        </Form>
      )}
    </Formik>
  );
};

export default DuplicateGroupForm;
