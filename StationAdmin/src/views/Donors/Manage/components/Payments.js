import React from "react";
import currency from "currency.js";

const Payments = ({ payment }) => {
  const {
    id,
    processor,
    method,
    amount,
    amount_refunded,
    status,
    date_created,
    date_updated,
    // customer_id,
    // card_type,
    // last4,
    // exp_month,
    // exp_year,
    // last_updated_by,
    // date_deposited
  } = payment;
  return (
    <>
      <td>{id}</td>
      <td>{currency(amount).format()}</td>
      <td>{currency(amount_refunded).format()}</td>
      <td>{status}</td>
      <td>{method}</td>
      <td>{processor}</td>
      <td>{date_created}</td>
      <td>{date_updated}</td>
    </>
  );
};

export default Payments;
