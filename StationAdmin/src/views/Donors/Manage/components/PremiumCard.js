import React from "react";

const PremiumCard = ({ premium }) => {
  const {
    id,
    name,
    price,
    cog,
    fmv,
    qty,
    img_url,
    category,
    status,
    // download_url,
    // donation_id,
  } = premium;
  return (
    <div className="card mb-3">
      <div className="row p-2">
        <div className="col-md-2 text-center">
          <img
            src={img_url}
            style={{
              maxHeight: "100px",
              maxWidth: "calc(100% - 20px)",
              borderRadius: "5px",
            }}
            alt="premium"
          />
        </div>
        <div className="col-md-2 text-center pt-3">
          <h2 className="mb-3">{`$${price}`}</h2>
          <h5>{`$${cog}/mo`}</h5>
        </div>
        <div className="col-md-8 pt-3">
          <h5
            style={{
              textOverflow: "ellipsis",
              overflow: "hidden",
              whiteSpace: "nowrap",
            }}
            className="mb-4"
          >
            {name}
          </h5>
          <div className="row">
            <div className="col-sm-3">
              <h5>{category}</h5>
            </div>
            <div className="col-sm-3">Status: {`${status}`}</div>
            <div className="col-sm-2">ID: {id}</div>
            <div className="col-sm-2">Qty: {qty}</div>
            <div className="col-sm-2">FMV: {`$${fmv}`}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PremiumCard;
