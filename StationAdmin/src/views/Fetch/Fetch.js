import React from "react";

const Fetch = async props => {
  const {
    url,
    methodType,
    authNeeded,
    bodyFunction,
    successCallback,
    failureCallback
  } = props;
  try {
    const headers = new Headers();
    headers.append("Content-Type", "application/json");
    if (authNeeded) {
      const jwt = localStorage.getItem("jwt");
      headers.append("Authorization", `Bearer ${jwt}`);
    }
    const body = JSON.stringify(bodyFunction());
    const method = methodType;

    const response = await fetch(url, { headers, method, body });
    const responseJSON = await response.json();
    if (responseJSON.ok) {
      // update state callback
      successCallback();
    } else {
      throw "Error: Response not ok.";
    }
  } catch (error) {
    console.log(error);
    failureCallback();
  }

  return null;
};

export default Fetch;
