import { <PERSON>ormCheck } from "@coreui/react";
import React from "react";

const Checkbox = ({
  field, // { name, value, onChange, onBlur }
  form: { touched, errors }, // also values, setXXXX, handleXXXX, dirty, isValid, status, etc.
  ...props
}) => {
  return (
    <>
      <CFormCheck
        {...field}
        {...props}
        label={props.label}
        checked={props.value || field.value}
        id={props.id}
        required={props.required || false}
      />
      <div className="invalid-feedback">{errors[field.name] || null}</div>
    </>
  );
};

export default Checkbox;
