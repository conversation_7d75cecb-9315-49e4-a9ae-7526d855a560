import * as Yup from "yup";
import { errorStrings } from "./validationStrings";

const donorInfoSchema = Yup.object().shape({
  firstname: Yup.string()
    .min(2, errorStrings.min)
    .max(50, errorStrings.max)
    .required(errorStrings.required),
  lastname: Yup.string()
    .min(2, errorStrings.min)
    .max(50, errorStrings.max)
    .required(errorStrings.required),
  address1: Yup.string()
    .min(5, errorStrings.min)
    .max(50, errorStrings.max)
    .required(errorStrings.required),
  address2: Yup.string()
    .min(2, errorStrings.min)
    .max(50, errorStrings.max)
    .nullable(),
  city: Yup.string()
    .min(2, errorStrings.min)
    .max(50, errorStrings.max)
    .required(errorStrings.required),
  state: Yup.string().required(errorStrings.required),
  postal_code: Yup.string().when("country", {
    is: "US",
    then: Yup.string()
      .matches(/^[0-9]{5}(?:-[0-9]{4})?$/, "Must be 5 or 9 digits")
      .required(errorStrings.required),
    otherwise: Yup.string()
      .min(5, errorStrings.min)
      .max(20, errorStrings.max)
      .required(errorStrings.required),
  }),
  country: Yup.string().required(errorStrings.required),
  phone: Yup.string()
    .when("country", {
      is: "US",
      then: Yup.string().min(10, errorStrings.min).max(10, errorStrings.max),
      otherwise: Yup.string()
        .min(10, errorStrings.min)
        .max(20, errorStrings.max),
    })
    .nullable(),
  email: Yup.string().email(errorStrings.email).nullable(),
  notes: Yup.string()
    .min(2, errorStrings.min)
    .max(1000, errorStrings.max)
    .nullable(),
});

export default donorInfoSchema;
