import React from "react";

const Dropdown = ({
  field, // { name, value, onChange, onBlur }
  form: { touched, errors, submitCount }, // also values, setXXXX, handleXXXX, dirty, isValid, status, etc.
  ...props
}) => {
  const inputClassNme = () => {
    if (submitCount > 0) {
      if (errors[field.name]) {
        return "form-control is-invalid";
      }
      if (!errors[field.name]) {
        return "form-control is-valid";
      }
    }
    return "form-control";
  };
  const selectOptions = props.options.map((option, index) => {
    return (
      <option
        key={option}
        value={props.valuePresets ? props.valuePresets[index] : option}
      >
        {option}
      </option>
    );
  });
  return (
    <div className="form-group row">
      <label className="col-form-label col-sm-3" htmlFor={props.id}>
        {props.label}
      </label>
      <div className="col-sm-9">
        <select
          className={inputClassNme()}
          {...field}
          options={props.options}
          label={props.label}
          // id={props.id}
          // value={props.placeholder}
          // onChange={this.props.onChange}
        >
          {selectOptions}
        </select>
        <div className="invalid-feedback">{errors[field.name] || null}</div>
      </div>
    </div>
  );
};

export default Dropdown;
