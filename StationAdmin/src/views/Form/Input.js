import React from "react";
import Mask from "react-text-mask";

const Input = ({
  field, // { name, value, onChange, onBlur }
  form: { touched, errors, submitCount }, // also values, setXXXX, handleXXXX, dirty, isValid, status, etc.
  ...props
}) => {
  // console.log(props);
  const inputClassNme = () => {
    if (submitCount > 0) {
      if (errors[field.name]) {
        return "form-control is-invalid";
      }
      if (!errors[field.name]) {
        return "form-control is-valid";
      }
    }
    return "form-control";
  };

  const inputType = () => {
    if (props.mask) {
      return (
        <Mask
          // {...field}
          {...props}
          className={inputClassNme()}
          type={props.type || "text"}
          placeholder={props.placeholder}
          id={props.id}
          name={props.name}
          onChange={props.onChange}
        />
      );
    }
    return (
      <input
        {...field}
        {...props}
        // className="form-control"
        className={inputClassNme()}
        type={props.type || "text"}
        placeholder={props.placeholder}
        // required={props.required || false}
      />
    );
  };

  return (
    <div className="form-group row mb-3">
      {/* {console.log("errors", errors, "field", field)} */}
      <label className="col-form-label col-sm-3">{props.label}</label>
      <div className="col-sm-9">
        {inputType()}
        <div className="invalid-feedback">{errors[field.name] || null}</div>
      </div>
    </div>
  );
};

export default Input;
