import { CFormInput } from "@coreui/react";
import React from "react";
import Mask from "react-text-mask";

const Input = ({
  field, // { name, value, onChange, onBlur }
  form: { touched, errors, submitCount }, // also values, setXXXX, handleXXXX, dirty, isValid, status, etc.
  ...props
}) => {
  const inputClassNme = () => {
    if (touched[field.name]) {
      if (errors[field.name]) {
        return "form-control is-invalid";
      }
      if (!errors[field.name]) {
        return "form-control is-valid";
      }
    }
    return "form-control";
  };

  const inputType = () => {
    if (props.mask) {
      return (
        <Mask
          // {...field}
          {...props}
          className={inputClassNme()}
          type={props.type || "text"}
          placeholder={props.placeholder}
          id={props.id}
          name={props.name}
          onChange={props.onChange}
          value={props.value || field.value}
        />
      );
    }
    return (
      <CFormInput
        {...field}
        {...props}
        className={inputClassNme()}
        type={props.type || "text"}
        placeholder={props.placeholder}
        // required={props.required || false}
      />
    );
  };

  return (
    <>
      {inputType()}
      <div className="invalid-feedback">{errors[field.name] || null}</div>
    </>
  );
};

export default Input;
