import { Field } from "formik";
import React, { useEffect } from "react";

export default function PhoneMask({ country, setFieldValue, component, value }) {
  // Handle phone numbers with country code prefixes when component mounts or value changes
  useEffect(() => {
    if (value && typeof value === 'string') {
      const phoneDigits = value.replace(/[^0-9]/g, "");

      // Handle US numbers with country code 1 (11 digits starting with 1)
      // This fixes the issue where "+1" or "1" prefixed numbers get truncated
      // regardless of the country setting, since many donors may not have country set correctly
      if (phoneDigits.length === 11 && phoneDigits.startsWith("1")) {
        const tenDigitNumber = phoneDigits.substring(1);
        if (tenDigitNumber.length === 10) {
          setFieldValue("phone", tenDigitNumber);
        }
      }
    }
  }, [value, country, setFieldValue]);

  if (country === "US") {
    return (
      <>
        <Field
          id="phone"
          name="phone"
          label="Phone"
          component={component}
          placeholder="(*************"
          mask={[
            "(",
            /[1-9]/,
            /\d/,
            /\d/,
            ")",
            " ",
            /\d/,
            /\d/,
            /\d/,
            "-",
            /\d/,
            /\d/,
            /\d/,
            /\d/,
          ]}
          onChange={(e) => {
            // Clean input to just digits
            const cleanedValue = e.target.value.replace(/[^0-9]/g, "");

            // Handle US numbers with country code 1 (11 digits starting with 1)
            // This fixes the issue where users paste "+1" or "1" prefixed numbers
            if (cleanedValue.length === 11 && cleanedValue.startsWith("1")) {
              // Remove the leading 1 to get a 10-digit number
              setFieldValue("phone", cleanedValue.substring(1));
            } else if (cleanedValue.length <= 10) {
              // For 10 digits or less, use as-is
              setFieldValue("phone", cleanedValue);
            } else {
              // For other lengths > 11, truncate to 10 digits to prevent overflow
              setFieldValue("phone", cleanedValue.substring(0, 10));
            }
          }}
        />
      </>
    );
  } else {
    return (
      <>
        <Field
          id="phone"
          name="phone"
          label="Phone"
          component={component}
          placeholder="15101234567"
          onChange={(e) => {
            const cleanedValue = e.target.value.replace(/[^0-9.]+/g, "");

            // For international fields, also handle US numbers with "+1" prefix
            // This ensures consistency across all phone inputs
            if (cleanedValue.length === 11 && cleanedValue.startsWith("1")) {
              // If it looks like a US number with country code, remove the "1" prefix
              setFieldValue("phone", cleanedValue.substring(1));
            } else {
              setFieldValue("phone", cleanedValue);
            }
          }}
        />
      </>
    );
  }
}
