/**
 *
 * In your Formik form simply use
 *
 * <Field component={SelectField} name="fieldname" options={options}/>
 * where options has the format of
 *
 * const options = [
 *   {value: 1, label: 'text'},
 *   ...
 * ]
 *
 */

import React from "react";
import Select from "react-select";
import { useField } from "formik";
import { CFormLabel } from "@coreui/react";

export default function SelectField(props) {
  // eslint-disable-next-line
  const [field, state, { setValue, setTouched }] = useField(props.field.name);

  const onChange = ({ value }) => {
    if (props.onChange) {
      props.onChange(value);
    }
    setValue(value);
  };

  const customStyles = {
    container: (provided) => ({
      ...provided,
      padding: 0,
      height: "fit-content",
    }),
    control: (provided) => ({
      ...provided,
      minHeight: "fit-content",
      height: "fit-content",
      borderColor:
        props.form.touched[props.field.name] &&
        props.form.errors[props.field.name]
          ? "#e55353"
          : "#b1b7c1",
    }),
    indicatorsContainer: (provided) => ({
      ...provided,
      height: "29px",
    }),
    input: (provided) => ({
      ...provided,
      height: "28px",
    }),
  };

  const inputId = `${props.field.name}-select`;

  return (
    <div
      className={`form-group row mb-${
        props.marginBottom ? props.marginBottom : 3
      }`}
    >
      {props.labelPlacement === "top" ? (
        <CFormLabel htmlFor={inputId}>{props.label}</CFormLabel>
      ) : (
        <div className="col-3">
          <label htmlFor={inputId}>{props.label}</label>
        </div>
      )}
      <div className="col">
        <Select
          {...props}
          inputId={inputId}
          aria-label={props.label}
          className={
            props.form.touched[props.field.name] &&
            props.form.errors[props.field.name]
              ? "is-invalid"
              : null
          }
          styles={customStyles}
          onChange={onChange}
          onBlur={setTouched}
          value={
            props.options
              ? props.options.find((option) => option.value === field.value)
              : ""
          }
        />
        <div className="invalid-feedback">
          {props.form.errors[props.field.name] || null}
        </div>
      </div>
    </div>
  );
}
