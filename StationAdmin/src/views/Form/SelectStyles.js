//not sure the best approach to using styles for this, as we need slightly different ones for fields within Formik & select inputs that stand alone such as filters. maybe react-select is overkill, but it's fairly integrated into this app at this point, and definitely provides value.

export const selectStyles = {
    container: (provided) => ({
      ...provided,
      padding: 0,
      height: "fit-content",
    }),
    control: (provided) => ({
      ...provided,
      minHeight: "fit-content",
      height: "fit-content",
      borderColor: "#b1b7c1",
    }),
    indicatorsContainer: (provided) => ({
      ...provided,
      height: "29px",
    }),
    input: (provided) => ({
      ...provided,
      height: "28px",
    }),
  };
