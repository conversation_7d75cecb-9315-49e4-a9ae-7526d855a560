import { CFormTextarea } from "@coreui/react";

const Textarea = ({
  field, // { name, value, onChange, onBlur } from Formik Field
  form: { errors }, // from Formik Field
  label, // custom label prop
  ...props // any additional props
}) => {
  return (
    <div className="form-group">
      {label && <label htmlFor={field.name}>{label}</label>}
      <CFormTextarea {...field} {...props} rows="3" />
      <div className="d-block invalid-feedback">{errors[field.name]}</div>
    </div>
  );
};

export default Textarea;
