import * as Yup from "yup";
import { errorStrings } from "./validationStrings";

const supportFormSchema = (installment, amount) =>
  Yup.object().shape({
    donationType: Yup.string().when("campaign", {
      is: (val) => isNaN(val) && installment === "One-Time",
      then: Yup.string()
        .required(errorStrings.required)
        .when([], {
          is: () => true, // Always check these conditions if there's no campaign
          then: Yup.string()
            .when("donationType", {
              is: "Minor",
              then: Yup.string().test(
                "amount-less-than-1000",
                `Donation type "Minor" is only allowed for amounts under $1000.`,
                () => amount < 1000,
              ),
            })
            .when("donationType", {
              is: "Major",
              then: Yup.string().test(
                "amount-greater-than-or-equal-to-1000",
                `Donation type "Major" is only allowed for amounts of $1000 or more.`,
                () => amount >= 1000,
              ),
            }),
        }),
    }),
    showShippingAddress: Yup.boolean(),
    firstNameShipping: Yup.string().when("showShippingAddress", {
      is: true,
      then: Yup.string()
        .min(2, errorStrings.min)
        .max(50, errorStrings.max)
        .required(errorStrings.required),
    }),
    lastNameShipping: Yup.string().when("showShippingAddress", {
      is: true,
      then: Yup.string()
        .min(2, errorStrings.min)
        .max(50, errorStrings.max)
        .required(errorStrings.required),
    }),
    address1Shipping: Yup.string().when("showShippingAddress", {
      is: true,
      then: Yup.string()
        .min(5, errorStrings.min)
        .max(50, errorStrings.max)
        .required(errorStrings.required),
    }),
    address2Shipping: Yup.string().when("showShippingAddress", {
      is: true,
      then: Yup.string().min(2, errorStrings.min).max(50, errorStrings.max),
    }),
    stateShipping: Yup.string().when("showShippingAddress", {
      is: true,
      then: Yup.string()
        .min(2, errorStrings.min)
        .max(50, errorStrings.max)
        .required(errorStrings.required),
    }),
    cityShipping: Yup.string().when("showShippingAddress", {
      is: true,
      then: Yup.string()
        .min(2, errorStrings.min)
        .max(50, errorStrings.max)
        .required(errorStrings.required),
    }),
    zipShipping: Yup.string().when(
      ["showShippingAddress", "countryShipping"],
      (showShippingAddress, countryShipping, schema) => {
        if (showShippingAddress && countryShipping === "United States") {
          return schema
            .min(5, errorStrings.min)
            .max(9, errorStrings.max)
            .required(errorStrings.required);
        }
        if (showShippingAddress && countryShipping !== "United States") {
          return schema
            .min(5, errorStrings.min)
            .max(15, errorStrings.max)
            .required(errorStrings.required);
        }
      },
    ),
    countryShipping: Yup.string().when("showShippingAddress", {
      is: true,
      then: Yup.string()
        .min(2, errorStrings.min)
        .max(50, errorStrings.max)
        .required(errorStrings.required),
    }),
    comment: Yup.string().min(2, errorStrings.min).max(1000, errorStrings.max),
  });

export default supportFormSchema;
