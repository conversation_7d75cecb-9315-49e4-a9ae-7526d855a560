import { useEffect, useState } from "react";
import { useAppSelector } from "../../hooks";

export default function useDonors(searchParams, updateIsLoading) {
  const [donations, updateDonations] = useState([]);
  const testMode = useAppSelector((state) => state.testMode);

  useEffect(() => {
    if (
      searchParams.startDate &&
      searchParams.startDate &&
      searchParams.minPaymentAmount > 0
    ) {
      updateIsLoading(true);

      const headers = {
        Authorization: `Bearer ${localStorage.getItem("jwt")}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };

      const url = `https://${
        testMode.value ? "api.staging." : "api."
      }kpfa.org/accounts/donors/?payment_start=${
        searchParams.startDate
      }&payment_end=${searchParams.endDate}&payment_status=${
        searchParams.paymentStatus
      }&payment_amount_gte=${searchParams.minPaymentAmount}`;

      fetch(url, {
        headers,
        method: "GET",
      })
        .then((response) => {
          if (response.ok) {
            return response.json();
          } else {
            throw new Error("donation fetch failed!");
          }
        })
        .then((json) => {
          updateDonations(json.records);
          updateIsLoading(false);
        })
        .catch((error) => {
          console.log(error);
          updateDonations([]);
          updateIsLoading(false);
        });
    } else {
      updateDonations([]);
      updateIsLoading(false);
    }
  }, [searchParams, updateIsLoading, testMode]);

  return [donations, updateDonations];
}
