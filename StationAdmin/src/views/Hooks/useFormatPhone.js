import { parsePhoneNumberFromString } from "libphonenumber-js";

export default function useFormatPhone(number, country) {
  const adjustedNumber = country === "US" ? "+1" + number : "+" + number;
  const phoneNumber = parsePhoneNumberFromString(adjustedNumber);
  if (phoneNumber) {
    if (country === "US") {
      return phoneNumber.formatNational();
    }
    if (country !== "US") {
      return phoneNumber.formatInternational();
    }
  }
  return number;
}
