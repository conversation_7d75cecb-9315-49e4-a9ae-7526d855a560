import { useState } from "react";
import { jwtDecode } from "jwt-decode";
import { CCard, CCardBody, CCol, CContainer, CForm, CRow } from "@coreui/react";
import { GoogleLogin } from "@react-oauth/google";
import logo from "../../../assets/sa-logo.png";
import { useAppDispatch, useAppSelector } from "../../../hooks";

const Login = ({ handleLoginClick }) => {
  //const [username, setUsername] = useState("");
  //const [password, setPassword] = useState("");
  const [invalid, setInvalid] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const dispatch = useAppDispatch();
  const testMode = useAppSelector((state) => state.testMode);

  /*
  const handleLoginCredentialsChange = (e) => {
    switch (e.target.id) {
      case "username":
        setUsername(e.target.value);
        break;
      case "password":
        setPassword(e.target.value);
        break;
      default:
    }
  };
  */

  const handleLogin = (event, credential) => {
    /*
    if (event && (event.key === "Enter" || event.target.id === "login")) {
      event.preventDefault();
    }
    */
    let url;
    url = `https://${testMode.value ? "api.staging." : "api."}kpfa.org/login/`;
    let body;
    if (credential) {
      body = JSON.stringify({ credential });
    } /* else {
      body = JSON.stringify({
        email: username,
        password: password,
      });
    }*/
    fetch(url, {
      method: "POST",
      body,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
    })
      .then((res) => {
        if (res.ok) {
          setInvalid(false);
          return res.json();
        }
        if (!res.ok) {
          res.json().then((data) => {
            console.log(data);
            setErrorMessage(data.message);
            setInvalid(true);
          });
        } else {
          console.log(res);
        }
      })
      .then((res) => {
        if (res && res.message === "Successful login.") {
          const { exp: tokenExpiration } = jwtDecode(res.jwt);
          handleLoginClick(tokenExpiration);
          localStorage.setItem("jwt", res.jwt);
          dispatch({
            type: "jwt/setJwt",
            payload: { token: res.jwt },
          });
          dispatch({
            type: "user/setUser",
            payload: {
              access_level: jwtDecode(res.jwt).data.access_level,
              email: jwtDecode(res.jwt).data.email,
            },
          });
          return;
        }
      });
  };

  return (
    <div className="bg-light min-vh-100 d-flex flex-row align-items-center">
      <CContainer md>
        <CRow className="justify-content-center">
          <CCol md={6}>
            <CCard>
              <CCardBody>
                <CForm className="d-flex flex-column justify-content-center">
                  <img
                    src={logo}
                    alt="Station Admin logo"
                    className="mx-auto mt-2"
                    style={{ height: "100px", width: "100px" }}
                  />
                  <div className="mx-auto my-4">
                    <GoogleLogin
                      onSuccess={(credentialResponse) => {
                        handleLogin(null, credentialResponse.credential);
                        setInvalid(false);
                      }}
                      onError={() => {
                        console.log("Google Login Failed");
                        setErrorMessage("Google Login Failed");
                        setInvalid(true);
                      }}
                    />
                  </div>
                  {/*
                  <p className="text-medium-emphasis my-4">——— or ———</p>
                  <CInputGroup className="my-3">
                    <CInputGroupText>
                      <FontAwesomeIcon icon="user" />
                    </CInputGroupText>
                    <CFormInput
                      id="username"
                      placeholder="Username"
                      autoComplete="username"
                      onChange={(e) => handleLoginCredentialsChange(e)}
                      onKeyPress={handleLogin}
                    />
                  </CInputGroup>
                  <CInputGroup className="mb-4">
                    <CInputGroupText>
                      <FontAwesomeIcon icon="lock" />
                    </CInputGroupText>
                    <CFormInput
                      id="password"
                      type="password"
                      placeholder="Password"
                      autoComplete="current-password"
                      onChange={(e) => handleLoginCredentialsChange(e)}
                      onKeyPress={handleLogin}
                    />
                  </CInputGroup>
                    */}
                  <CRow>
                    {/*
                    <CCol>
                      <button
                        id="login"
                        className="btn btn-primary px-4"
                        onClick={(e) => handleLogin(e)}
                        onKeyPress={handleLogin}
                      >
                        Login
                      </button>
                    </CCol>
                    */}
                    <CCol className="text-right">
                      {invalid ? (
                        <div className="alert alert-danger" role="alert">
                          {errorMessage
                            ? errorMessage
                            : "Invalid Username or Password."}
                        </div>
                      ) : null}
                    </CCol>
                  </CRow>
                </CForm>
              </CCardBody>
            </CCard>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  );
};

export default Login;
