import React, { forwardRef, useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import InputFlexible from "../Form/InputFlexible";
import categorySchema from "./CategorySchema";
import { CRow, CCol, CButton, CButtonGroup } from "@coreui/react";
import {
  useAddCategoryMutation,
  useEditCategoryMutation,
} from "../../api/kpfa.ts";
import Alert from "../../components/Alert";

const blankInitialValues = {
  name: "",
  description: "",
};

const CategoryForm = forwardRef(
  (
    {
      selectedCategory,
      setSelectedCategory,
      toggleEdit,
      alert,
      setAlert,
      visible,
    },
    ref
  ) => {
    const [initialValues, setInitialValues] = useState(blankInitialValues);
    const [editCategory] = useEditCategoryMutation();
    const [addCategory] = useAddCategoryMutation();

    useEffect(() => {
      selectedCategory
        ? setInitialValues({
            name: selectedCategory.name,
            description: selectedCategory.description,
          })
        : setInitialValues(blankInitialValues);
    }, [selectedCategory]);

    return (
      <div ref={ref}>
        {visible ? (
          <div className="bg-white rounded shadow">
            <Formik
              enableReinitialize={true}
              initialValues={initialValues}
              validationSchema={categorySchema}
              onSubmit={(values, { setSubmitting }) => {
                setAlert({ display: false });

                let body = values;

                if (selectedCategory) {
                  body.id = selectedCategory.id;
                }

                const mutation = selectedCategory ? editCategory : addCategory;

                mutation({ body })
                  .unwrap()
                  .then((fulfilled) => {
                    setAlert({
                      type: "success",
                      display: true,
                      message: `Successfully ${
                        selectedCategory ? "updated" : "created"
                      } category: ${fulfilled.name}`,
                    });
                    body.id = fulfilled.id;
                    setSelectedCategory(body);
                  })
                  .catch((rejected) => {
                    setAlert({
                      type: "fail",
                      display: true,
                      message: rejected?.data?.message,
                      error: rejected?.error,
                    });
                  });

                setSubmitting(false);
              }}
            >
              {() => (
                <Form className="col-12 mt-2 mb-4 p-3">
                  <h3>
                    {selectedCategory ? `Edit Category` : "Create New Category"}
                  </h3>
                  <CRow>
                    <CCol xs={12} sm className="mb-4">
                      <Field
                        name="name"
                        label="Name"
                        component={InputFlexible}
                      />
                    </CCol>
                    <CCol xs={12} sm>
                      <Field
                        name="description"
                        label="Description"
                        component={InputFlexible}
                      />
                    </CCol>
                  </CRow>

                  <CRow className="pb-4">
                    <CCol>
                      <CButtonGroup>
                        <CButton type="submit">
                          {selectedCategory ? "Edit" : "Create"}
                        </CButton>
                        <CButton onClick={toggleEdit} variant="outline">
                          Cancel
                        </CButton>
                      </CButtonGroup>
                      <Alert alert={alert} />
                    </CCol>
                  </CRow>
                </Form>
              )}
            </Formik>
          </div>
        ) : null}
      </div>
    );
  }
);

export default CategoryForm;
