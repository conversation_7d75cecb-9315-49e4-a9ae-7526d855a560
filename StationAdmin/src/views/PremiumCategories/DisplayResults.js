import React from "react";
import {
  CButton,
  CButtonGroup,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from "@coreui/react";

export default function DisplayResults({ categories, categoryAction }) {
  return (
    <div className="mx-auto p-3 bg-white rounded shadow my-3">
      <h3 className="mb-3">Premium Categories</h3>
      <CTable responsive>
        <CTableHead className="w-100">
          <CTableRow>
            <CTableHeaderCell scope="col">Name</CTableHeaderCell>
            <CTableHeaderCell scope="col">Description</CTableHeaderCell>
            <CTableHeaderCell scope="col"></CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {categories.map((category) => (
            <CTableRow key={category.id}>
              <CTableDataCell className="align-middle">
                {category.name}
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                {category.description}
              </CTableDataCell>
              <CTableDataCell className="align-middle d-flex justify-content-end">
                <CButtonGroup>
                  <CButton
                    onClick={(e) => categoryAction(e, "edit")}
                    variant="outline"
                    size="sm"
                    data-id={category.id}
                  >
                    Edit
                  </CButton>
                  <CButton
                    onClick={(e) => categoryAction(e, "delete")}
                    data-id={category.id}
                    variant="outline"
                    size="sm"
                  >
                    Delete
                  </CButton>
                </CButtonGroup>
              </CTableDataCell>
            </CTableRow>
          ))}
        </CTableBody>
      </CTable>
    </div>
  );
}
