import { useGetPremiumCategoriesQuery } from "../../api/kpfa.ts";
import React, { useRef, useState } from "react";
import DisplayResults from "./DisplayResults";
import CategoryForm from "./CategoryForm";
import { CButton } from "@coreui/react";
import DeleteModal from "./DeleteModal";
import { Link } from "react-router-dom";

export default function ManageCategories() {
  const [showEdit, setShowEdit] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState();
  const [alert, setAlert] = useState({
    type: "",
    display: false,
    message: "",
  });

  const {
    data: categoriesData,
    isLoading: categoriesDataIsLoading,
  } = useGetPremiumCategoriesQuery();

  const formRef = useRef();

  const categoryAction = (e, action) => {
    setSelectedCategory(
      categoriesData.filter(
        (category) => category.id === parseInt(e.target.dataset.id)
      )[0]
    );

    if (action === "edit") {
      setShowEdit(true);
      setAlert({ display: false });
      formRef.current.scrollIntoView({ behavior: "smooth" });
    }

    if (action === "delete") {
      setShowEdit(false);
      setShowDelete(true);
    }
  };

  const handleNewCategoryClick = () => {
    setShowEdit(true);
    setAlert({ display: false });

    if (selectedCategory) {
      setSelectedCategory(null);
    }
  };

  return (
    <>
      {!showEdit || (showEdit && selectedCategory) ? (
        <CButton className="me-1" onClick={handleNewCategoryClick}>
          Create New Category
        </CButton>
      ) : null}
      <CButton color="dark">
        <Link className="text-white text-decoration-none" to="/premiums">
          Manage Premiums
        </Link>
      </CButton>

      <CategoryForm
        ref={formRef}
        visible={showEdit}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
        toggleEdit={() => setShowEdit(false)}
        alert={alert}
        setAlert={setAlert}
      />

      {!categoriesDataIsLoading && categoriesData ? (
        <DisplayResults
          categories={categoriesData}
          categoryAction={categoryAction}
        />
      ) : (
        <div className="d-flex justify-content-center pt-3">
          <div className="spinner-border text-info">
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      )}
      {showDelete ? (
        <DeleteModal
          toggle={() => setShowDelete(false)}
          visible={showDelete}
          selectedCategory={selectedCategory}
        />
      ) : null}
    </>
  );
}
