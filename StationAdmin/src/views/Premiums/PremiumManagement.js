import React, { useState, useEffect } from "react";
import { CButton } from "@coreui/react";

import PremiumListContainer from "./components/PremiumListContainer";
import PremiumResultsOptions from "./components/PremiumResultsOptions";
import Modal from "./components/Modal";
import Spinner from "./components/Spinner";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  useGetPremiumCategoriesQuery,
  useGetPremiumsQuery,
} from "../../api/kpfa.ts";
import DeleteModal from "./components/DeleteModal";
import { Link } from "react-router-dom";

const PremiumManagement = () => {
  const [premiums, setPremiums] = useState([]);
  const [resultsPerPage, updateResultsPerPage] = useState("10");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, updateTotalPages] = useState(1);
  const [selectedPremium, updateSelectedPremium] = useState({});
  const [showModal, toggleShowModal] = useState(false);
  const [filter, setFilter] = useState({
    name: "",
    category: "",
    active: "",
  });
  const [filteredResults, setFilteredResults] = useState();
  const [filterIsActive, setFilterIsActive] = useState(false);
  const [categoryOptions, setCategoryOptions] = useState();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const {
    data: premiumData,
    isLoading,
    refetch: refetchPremiums,
  } = useGetPremiumsQuery();

  const { data: categories } = useGetPremiumCategoriesQuery();

  useEffect(() => {
    let count = filterIsActive ? filteredResults.length : premiums.length;

    if (count > 0) {
      const total = Math.ceil(count / Number(resultsPerPage));
      updateTotalPages(total);
    }
  }, [premiums, filteredResults, resultsPerPage, filterIsActive]);

  useEffect(() => {
    if (premiumData?.records.length > 0) {
      setPremiums(premiumData.records);
    }
  }, [premiumData]);

  const updateCurrentPage = (e) => {
    const paginationChange = parseInt(e.target.dataset.increment);
    setCurrentPage(currentPage + paginationChange);
  };

  const updateFilter = (key, val) => {
    let newResults = premiums;
    let newFilter = {
      ...filter,
      [key]: val,
    };
    setFilter(newFilter);

    if (
      newFilter.name.length ||
      Number.isInteger(newFilter.category) ||
      typeof newFilter.active === "boolean"
    ) {
      setFilterIsActive(true);

      if (newFilter.name.length) {
        newResults = newResults.filter((premium) =>
          premium.name.toLowerCase().includes(newFilter.name.toLowerCase())
        );
      }

      if (Number.isInteger(newFilter.category)) {
        newResults = newResults.filter(
          (premium) => premium.category_id === newFilter.category
        );
      }

      if (typeof newFilter.active === "boolean") {
        newResults = newResults.filter(
          (premium) => premium.active === newFilter.active
        );
      }
      setFilteredResults(newResults);
      setCurrentPage(1);
    } else {
      setFilterIsActive(false);
    }
  };

  useEffect(() => {
    if (categories) {
      let options = [];
      for (const category of categories) {
        options.push({ value: category.id, label: category.name });
      }
      setCategoryOptions(options);
    }
  }, [categories]);

  return (
    <div className="animated fadeIn">
      <div className="row d-flex justify-content-start">
        <div className="mb-3 me-auto d-block">
          <CButton
            color="primary"
            className="me-1"
            onClick={() => {
              updateSelectedPremium(null);
              toggleShowModal(true);
            }}
          >
            New Premium
          </CButton>
          <CButton
            className="me-1"
            onClick={() => {
              refetchPremiums();
            }}
          >
            <FontAwesomeIcon icon="redo" />
          </CButton>
          <Link to="/categories" className="me-1">
            <CButton color="dark">Categories</CButton>
          </Link>
          <Link to="/vendors" className="me-1">
            <CButton color="dark">Vendors</CButton>
          </Link>
        </div>
      </div>
      <div className="card shadow mb-5">
        <div className="card-body">
          <h2>Premiums</h2>
          <PremiumResultsOptions
            filter={filter}
            updateFilter={updateFilter}
            updateResultsPerPage={updateResultsPerPage}
            updateTotalPages={updateTotalPages}
            setCurrentPage={setCurrentPage}
            currentPage={currentPage}
            premiumsLength={premiums.length}
            updateRefreshPremiums={refetchPremiums}
            categoryOptions={categoryOptions}
          />
          {!isLoading && premiums.length > 0 ? (
            <PremiumListContainer
              premiums={filterIsActive ? filteredResults : premiums}
              resultsPerPage={resultsPerPage}
              currentPage={currentPage}
              updateCurrentPage={updateCurrentPage}
              isLoading={isLoading}
              totalPages={totalPages}
              updateSelectedPremium={updateSelectedPremium}
              toggleShowModal={toggleShowModal}
              setShowDeleteModal={setShowDeleteModal}
            />
          ) : null}
          {isLoading ? <Spinner /> : null}
          <Modal
            selectedPremium={selectedPremium}
            updateSelectedPremium={updateSelectedPremium}
            toggleShowModal={toggleShowModal}
            showModal={showModal}
            categoryOptions={categoryOptions}
          />
          <DeleteModal
            selectedPremium={selectedPremium}
            updateSelectedPremium={updateSelectedPremium}
            setShowDeleteModal={setShowDeleteModal}
            showModal={showDeleteModal}
          />
        </div>
      </div>
    </div>
  );
};

export default PremiumManagement;
