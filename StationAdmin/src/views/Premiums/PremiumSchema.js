import * as Yup from "yup";
import { errorStrings } from "../Form/validationStrings";

const greaterThan = "Must be greater than or equal to 0";

Yup.addMethod(Yup.object, "uniqueProperty", function (propertyName, message) {
  return this.test("unique", message, function (value) {
    if (!value || !value[propertyName]) {
      return true;
    }

    if (
      this.parent
        .filter((v) => v !== value)
        .some((v) => v[propertyName] === value[propertyName])
    ) {
      throw this.createError({
        path: `${this.path}.${propertyName}`,
      });
    }

    return true;
  });
});
const FILE_SIZE = 2000000;
const SUPPORTED_FORMATS = [
  "image/jpg",
  "image/jpeg",
  "image/png",
  "image/webp",
];

const premiumSchema = Yup.object().shape({
  name: Yup.string().required(errorStrings.required),
  price: Yup.number()
    .positive(errorStrings.positiveNum)
    .required(errorStrings.required),
  imgURL: Yup.string().when(["file"], {
    is: (val) => val?.length === 0,
    then: (premiumSchema) => premiumSchema.required(),
  }),
  //Yup.string().url(errorStrings.url).required(errorStrings.required),
  description: Yup.string().required(errorStrings.required),
  category: Yup.number().required(errorStrings.required),
  cog: Yup.number().min(0, greaterThan).required(errorStrings.required),
  fmv: Yup.number().min(0, greaterThan).required(errorStrings.required),
  qty: Yup.number()
    .positive(errorStrings.positiveNum)
    .required(errorStrings.required),
  sortWeight: Yup.number()
    .min(1, "Must be more than 1")
    .max(100, "Must be less than 100")
    .required(errorStrings.required),
  variantType: Yup.string().nullable(),
  variations: Yup.array()
    .of(
      Yup.object()
        .shape({
          name: Yup.string().required(errorStrings.required), // these constraints take precedence
          qty: Yup.number()
            .positive(errorStrings.positiveNum)
            .required(errorStrings.required), // these constraints take precedence
        })
        .uniqueProperty("name", "Duplicate name")
    )
    .when("variantType", {
      is: (val) => val?.length,
      then: (schema) => schema.required(),
    }),
  file: Yup.mixed()
    .nullable()
    .notRequired()
    .test(
      "FILE_SIZE",
      "Uploaded file is too big. Images must be under 2 MB file size.",
      (value) => !value || (value && value.size <= FILE_SIZE)
    )
    .test(
      "FILE_FORMAT",
      "Uploaded file has unsupported format.",
      (value) => !value || (value && SUPPORTED_FORMATS.includes(value.type))
    ),
});

export default premiumSchema;
