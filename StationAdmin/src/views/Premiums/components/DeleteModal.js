import React, { useState } from "react";
import {
  CButtonGroup,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CButton,
} from "@coreui/react";

import Alert from "../../../components/Alert";
import { useDeletePremiumMutation } from "../../../api/kpfa.ts";

const DeleteModal = ({
  showModal,
  selectedPremium,
  setShowDeleteModal,
  updateSelectedPremium,
}) => {
  const [alert, setAlert] = useState({ display: false });
  const [successful, setSuccessful] = useState(false);

  const handleCloseModal = () => {
    setShowDeleteModal(false);
    setAlert({ display: false });
    updateSelectedPremium(null);
    setSuccessful(false);
  };

  const [
    deletePremium,
    { isLoading: deleteIsLoading },
  ] = useDeletePremiumMutation();

  const handleDelete = () => {
    deletePremium(selectedPremium.id)
      .unwrap()
      .then((fulfilled) => {
        setAlert({
          type: "success",
          display: true,
          message: fulfilled.message,
        });
        setSuccessful(true);
      })
      .catch((rejected) => {
        setAlert({
          type: "fail",
          display: true,
          message: rejected?.data?.message,
          error: rejected?.error,
        });
      });
  };

  return (
    <CModal visible={showModal} onClose={handleCloseModal}>
      <CModalHeader>
        <h4>Delete Premium</h4>
      </CModalHeader>
      <CModalBody>
        <h5>Are you sure you want to delete "{selectedPremium?.name}"? </h5>
        This will also delete variations of this premium.
        <br />
        Premiums or variations that are already included in pledge shipments
        cannot be deleted.
        <Alert alert={alert} />
      </CModalBody>
      <CModalFooter>
        {" "}
        <CButtonGroup>
          <CButton
            onClick={handleCloseModal}
            disabled={deleteIsLoading}
            color={successful ? "primary" : "danger"}
            variant="outline"
            size="sm"
          >
            {successful ? "Back" : "Cancel"}
          </CButton>
          {successful ? null : (
            <CButton
              onClick={handleDelete}
              disabled={deleteIsLoading}
              className="text-white"
              size="sm"
              color="danger"
            >
              Delete
            </CButton>
          )}
        </CButtonGroup>
      </CModalFooter>
    </CModal>
  );
};

export default DeleteModal;
