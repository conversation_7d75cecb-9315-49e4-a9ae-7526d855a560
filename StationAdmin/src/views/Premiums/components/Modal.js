import React, { useState } from "react";
import { CModal, CModalHeader, CModalBody } from "@coreui/react";

import PremiumForm from "./PremiumForm";
import Alert from "../../../components/Alert";

const Modal = ({
  showModal,
  selectedPremium,
  toggleShowModal,
  categoryOptions,
  updateSelectedPremium,
}) => {
  const [alert, setAlert] = useState({ display: false });

  const handleCloseModal = () => {
    setAlert({ display: false });
    updateSelectedPremium(null);
    toggleShowModal(false);
  };

  return (
    <>
      <CModal visible={showModal} onClose={handleCloseModal}>
        <CModalHeader>
          {selectedPremium ? "Edit Premium" : "Create New Premium"}
        </CModalHeader>
        <CModalBody>
          <PremiumForm
            handleCloseModal={handleCloseModal}
            selectedPremium={selectedPremium}
            categoryOptions={categoryOptions}
            setAlert={setAlert}
            updateSelectedPremium={updateSelectedPremium}
          />
          <Alert alert={alert} />
        </CModalBody>
      </CModal>
    </>
  );
};

export default Modal;
