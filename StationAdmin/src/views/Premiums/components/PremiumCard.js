import { CBadge } from "@coreui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";

const PremiumCard = ({
  premium,
  updateSelectedPremium,
  toggleShowModal,
  setShowDeleteModal,
}) => {
  const totalQuantity = (qty, variations) => {
    if (variations) {
      let sum = 0;

      for (const variation of variations) {
        sum += variation.qty;
      }

      return "Total Qty: " + sum;
    } else return "Qty: " + qty;
  };

  return (
    <div className="card mb-3">
      <div className="row p-4">
        <div className="col-md-2 text-center">
          <img
            src={premium.img_url}
            style={{
              maxHeight: "100px",
              maxWidth: "calc(100% - 20px)",
              borderRadius: "5px",
            }}
            alt="premium"
          />
        </div>
        <div className="col-md-2 text-center">
          <h2 className="mb-3">{`$${premium.price}`}</h2>
          <h5>{`$${premium.cog}/mo`}</h5>
          {premium.featured ? (
            <span className="badge badge-primary">Featured</span>
          ) : null}
        </div>
        <div className="col-md-6">
          <h5
            style={{
              textOverflow: "ellipsis",
              overflow: "hidden",
              whiteSpace: "nowrap",
            }}
            className="mb-4"
          >
            {premium.name}
          </h5>
          <div className="row">
            <div className="col-sm-2">
              <h5>
                {premium.active ? (
                  <CBadge color="success">Active</CBadge>
                ) : (
                  <CBadge color="light" className="text-dark">
                    Inactive
                  </CBadge>
                )}
              </h5>
            </div>
            <div className="col-sm-3">
              <h5>{premium.category_name}</h5>
            </div>
            <div className="col-sm-2">ID: {premium.id}</div>
            <div className="col-sm-2">
              {totalQuantity(premium.qty, premium.variants?.variations)}
            </div>
            <div className="col-sm-3">
              Vendor: {premium.vendor ? premium.vendor : "N/A"}
            </div>
          </div>
        </div>
        <div className="col-md-2 d-flex flex-d-row align-items-center justify-content-end">
          <button
            className="btn btn-danger me-2"
            onClick={() => {
              updateSelectedPremium(premium);
              setShowDeleteModal(true);
            }}
          >
            <FontAwesomeIcon className="text-white" icon="trash-alt" />
          </button>
          <button
            className="btn btn-primary"
            onClick={() => {
              updateSelectedPremium(premium);
              toggleShowModal(true);
            }}
          >
            Edit
          </button>
        </div>
      </div>
    </div>
  );
};

export default PremiumCard;
