import { useEffect, useState } from "react";
import { Formik, Form, Field, FieldArray, ErrorMessage } from "formik";

import InputFlexible from "../../Form/InputFlexible";
import Textarea from "../../Form/Textarea";
import SelectField from "../../Form/SelectField";
import {
  CButton,
  CCol,
  CModal,
  CModalBody,
  CModalFooter,
  CRow,
} from "@coreui/react";
import {
  useAddPremiumMutation,
  useDeletePremiumMutation,
  useEditPremiumMutation,
  useGetVendorsQuery,
  useLazyGetPremiumQuery,
} from "../../../api/kpfa.ts";
import premiumSchema from "../PremiumSchema";
import { variantNames, variationOptions } from "../constants";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Alert from "../../../components/Alert";
import { useAddPremiumImageMutation } from "../../../api/kpfa";

const PremiumForm = ({
  categoryOptions,
  selectedPremium,
  handleCloseModal,
  setAlert,
  updateSelectedPremium,
}) => {
  const [vendorOptions, setVendorOptions] = useState();
  const { data: vendors } = useGetVendorsQuery();
  const [addPremium] = useAddPremiumMutation();
  const [editPremium] = useEditPremiumMutation();
  const [addPremiumImage] = useAddPremiumImageMutation();
  const [deletePremium] = useDeletePremiumMutation();
  const [visible, setVisible] = useState(false);
  const [selectedVariation, setSelectedVariation] = useState();
  const [variationAlert, setVariationAlert] = useState({ display: false });
  const [imageAlert, setImageAlert] = useState({ display: false });
  const [trigger] = useLazyGetPremiumQuery();

  const lazyFetchPremium = (id) => {
    //using RTK Lazy Query to update form data with variation IDs etc.
    trigger(id)
      //.unwrap()
      .then((result) => {
        updateSelectedPremium(result.data.records[0]);
      })
      .catch((rejected) => {
        console.log(rejected);
      });
  };

  useEffect(() => {
    if (vendors) {
      let options = [{ value: null, label: "None" }];

      for (const vendor of vendors) {
        options.push({ value: vendor.id, label: vendor.company });
      }
      setVendorOptions(options);
    }
  }, [vendors, setVendorOptions]);

  const handleDeleteVariation = (remove, index, variation) => {
    if (variation.id) {
      setSelectedVariation(variation);
      setVisible(true);
    } else {
      remove(index);
    }
  };

  const handleConfirmDeleteVariation = (id) => {
    deletePremium(id)
      .unwrap()
      .then((fulfilled) => {
        setVariationAlert({
          type: "success",
          display: true,
          message: fulfilled.message,
        });
        lazyFetchPremium(selectedPremium.id);
        setVisible(false);
      })
      .catch((rejected) => {
        setVariationAlert({
          type: "fail",
          display: true,
          message: rejected?.data?.message,
          error: rejected?.error,
        });
      });
  };

  const Modal = () => (
    <CModal
      alignment="center"
      backdrop="static"
      visible={visible}
      onClose={() => setVisible(false)}
      className="px-3 shadow-lg"
      size="sm"
    >
      <div className="shadow-lg">
        <CModalBody>
          Are you sure you want to delete Variation "{selectedVariation?.name}"
          with ID {selectedVariation?.id}?
        </CModalBody>
        <CModalFooter className="justify-content-start">
          <CButton
            color="danger"
            onClick={() => handleConfirmDeleteVariation(selectedVariation.id)}
          >
            Delete Variation
          </CButton>
          <CButton color="secondary" onClick={() => setVisible(false)}>
            Cancel
          </CButton>
        </CModalFooter>
      </div>
    </CModal>
  );

  const initialValues =
    selectedPremium && selectedPremium.hasOwnProperty("name")
      ? {
          name: selectedPremium.name,
          status: selectedPremium.active,
          featured: selectedPremium.featured,
          category: selectedPremium.category_id,
          description: selectedPremium.description,
          price: selectedPremium.price,
          cog: selectedPremium.cog,
          fmv: selectedPremium.fmv,
          qty: selectedPremium.qty,
          vendorCode: selectedPremium.vendor_code,
          vendor: selectedPremium.vendor_id ? selectedPremium.vendor_id : null,
          imgURL: selectedPremium.img_url,
          variantType: selectedPremium.variants?.name
            ? selectedPremium.variants.name
            : null,
          variations: selectedPremium.variants?.variations,
          sortWeight: selectedPremium.sort_weight,
          downloadURL: selectedPremium.download_url,
          file: "",
        }
      : {
          name: "",
          status: true,
          featured: false,
          category: "",
          id: "",
          description: "",
          price: "",
          cog: "",
          fmv: "",
          qty: 1,
          vendorCode: "",
          vendor: "",
          imgURL: "",
          variantType: null,
          sortWeight: 50,
          download_url: "",
          file: "",
        };

  const CustomFileUpload = (props) => {
    const { field, form } = props;

    const handleChange = (e) => {
      const file = e.currentTarget.files[0];
      form.setFieldValue(field.name, file);
    };

    return (
      <div>
        <input
          type={"file"}
          onChange={(o) => handleChange(o)}
          className={"form-control"}
        />
      </div>
    );
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={premiumSchema}
      enableReinitialize={true}
      onSubmit={async (values, { setSubmitting }) => {
        setAlert({ display: false });

        let body = {
          name: values.name,
          active: values.status,
          featured: values.featured,
          category_id: values.category,
          description: values.description,
          price: values.price,
          cog: values.cog,
          fmv: values.fmv,
          img_url: values.imgURL,
          vendor_id: values.vendor,
          vendor_code: values.vendorCode,
          qty: values.qty,
          sort_weight: values.sortWeight,
          download_url: values.downloadURL,
        };

        if (values.variantType !== null) {
          body.variants = {
            name: values.variantType,
            variations: values.variations,
          };
        }

        if (selectedPremium) {
          body.id = selectedPremium.id;
        }

        if (values.file) {
          let formdata = new FormData();
          formdata.append("image", values.file);

          await addPremiumImage({ body: formdata })
            .unwrap()
            .then((fulfilled) => {
              body.img_url = fulfilled.img_url;
            })
            .catch((rejected) => {
              setImageAlert({
                type: "fail",
                display: true,
                message: rejected?.error,
                error: rejected?.error,
              });
            });
        }

        const mutation = selectedPremium ? editPremium : addPremium;

        mutation({ body })
          .unwrap()
          .then((fulfilled) => {
            setAlert({
              type: "success",
              display: true,
              message: `Successfully ${
                selectedPremium ? "updated" : "created"
              } premium: ${fulfilled.name}`,
            });
            lazyFetchPremium(fulfilled.id);
          })
          .catch((rejected) => {
            setAlert({
              type: "fail",
              display: true,
              message: rejected?.data?.message,
              error: rejected?.error,
            });
          });

        setSubmitting(false);
      }}
    >
      {({ values, errors }) => {
        return (
          <Form className="needs-validation">
            <Modal />
            <CRow className="mb-3">
              <CCol>
                <Field name="name" label="Name" component={InputFlexible} />
              </CCol>
            </CRow>
            <CRow>
              <CCol md={6}>
                <Field
                  name="status"
                  label="Status"
                  component={SelectField}
                  labelPlacement="top"
                  options={[
                    { value: true, label: "Active" },
                    { value: false, label: "Inactive" },
                  ]}
                />
              </CCol>
              <CCol md={6}>
                <Field
                  name="featured"
                  label="Featured"
                  labelPlacement="top"
                  component={SelectField}
                  options={[
                    { value: true, label: "Yes" },
                    { value: false, label: "No" },
                  ]}
                />
              </CCol>
            </CRow>
            <CRow className="mb-3">
              <CCol md={6}>
                <Field
                  name="category"
                  label="Category"
                  component={SelectField}
                  labelPlacement="top"
                  options={categoryOptions}
                />
              </CCol>
              <CCol md={6}>
                <Field
                  name="vendor"
                  label="Vendor"
                  component={SelectField}
                  labelPlacement="top"
                  options={vendorOptions}
                />
              </CCol>
            </CRow>
            {values.category === 1 ? (
              <CRow className="mb-3">
                <CCol md={12}>
                  <Field
                    name="downloadURL"
                    label="Download URL"
                    component={InputFlexible}
                  />
                </CCol>
              </CRow>
            ) : null}
            <CRow className="mb-3">
              <CCol>
                <Field
                  name="description"
                  label="Description"
                  component={Textarea}
                />
              </CCol>
            </CRow>
            <CRow className="mb-3">
              <CCol md={6}>
                <Field
                  type="number"
                  name="price"
                  label="Price"
                  component={InputFlexible}
                />
              </CCol>
              <CCol md={6}>
                <Field
                  name="cog"
                  label="Cost of Good"
                  type="number"
                  component={InputFlexible}
                />
              </CCol>
            </CRow>
            <CRow className="mb-3">
              <CCol md={6}>
                <Field
                  type="number"
                  name="fmv"
                  label="Fair Market Value"
                  component={InputFlexible}
                />
              </CCol>
              <CCol md={6}>
                <Field
                  name="vendorCode"
                  label="Vendor Code"
                  component={InputFlexible}
                />
              </CCol>
            </CRow>
            <CRow className="mb-3">
              {!values.variations?.length ? (
                <CCol md={6}>
                  <Field
                    name="qty"
                    label="Quantity"
                    component={InputFlexible}
                    type="number"
                  />
                </CCol>
              ) : null}
              <CCol md={6}>
                <Field
                  name="sortWeight"
                  label="Sort Weight"
                  component={InputFlexible}
                  type="number"
                />
              </CCol>
              <CCol md={6} className={!values.variations ? "mt-3" : ""}>
                <Field
                  name="variantType"
                  label="Variant Type"
                  component={SelectField}
                  labelPlacement="top"
                  options={variantNames}
                />
              </CCol>
              <CCol md={6}></CCol>
            </CRow>
            {values.variantType !== null && (
              <FieldArray name="variations">
                {({ remove, push }) => {
                  return (
                    <div className="card my-5 shadow-sm">
                      <div className="card-body">
                        <h5>Variations</h5>
                        {values.variations?.length > 0 ? (
                          values.variations?.map((variation, index) => (
                            <>
                              <CRow className="mt-3" key={index}>
                                <CCol>
                                  <Field
                                    label="Name"
                                    name={`variations.${index}.name`}
                                    component={SelectField}
                                    labelPlacement="top"
                                    options={
                                      variationOptions[values.variantType]
                                    }
                                    marginBottom="0"
                                  />
                                  <ErrorMessage
                                    name={`variations.${index}.name`}
                                    component="div"
                                    className="d-block invalid-feedback"
                                  />
                                </CCol>
                                <CCol md={4}>
                                  <Field
                                    name={`variations.${index}.qty`}
                                    type="number"
                                    label="Quantity"
                                    component={InputFlexible}
                                    min="1"
                                  />
                                  <ErrorMessage
                                    name={`variations.${index}.qty`}
                                    component="div"
                                    className="d-block invalid-feedback"
                                  />
                                </CCol>
                                <CCol md={2} className="d-flex">
                                  <CButton
                                    style={{
                                      height: "39px",
                                      marginTop: "31px",
                                    }}
                                    variant="outline"
                                    color="danger"
                                    onClick={() =>
                                      handleDeleteVariation(
                                        remove,
                                        index,
                                        variation,
                                      )
                                    }
                                  >
                                    <FontAwesomeIcon icon="trash-alt" />
                                  </CButton>
                                </CCol>
                              </CRow>
                              {variation.id ? (
                                <CRow className="mt-2">
                                  <CCol>
                                    <strong>ID:</strong> {variation.id}
                                  </CCol>
                                </CRow>
                              ) : null}
                            </>
                          ))
                        ) : (
                          <div className="text-danger mb-3">
                            Create at least one variation
                          </div>
                        )}
                        <CRow className="mb-2 mt-4">
                          <CCol>
                            <Alert alert={variationAlert} />
                            <CButton
                              color="primary"
                              variant="outline"
                              onClick={() => push({ name: "", qty: "" })}
                            >
                              Add Variation
                            </CButton>
                          </CCol>
                        </CRow>
                      </div>
                    </div>
                  );
                }}
              </FieldArray>
            )}
            <CRow className="mb-3">
              <CCol>
                <Alert alert={imageAlert} />
                <label htmlFor="file" className="form-label">
                  Upload file
                </label>{" "}
                <div className="mb-2 invalid-feedback d-block">
                  {errors.file || null}
                </div>
                <Field id="file" name="file" component={CustomFileUpload} />
              </CCol>
            </CRow>
            <CRow className="mb-3">
              <CCol>
                <Field
                  name="imgURL"
                  label="Image URL"
                  component={InputFlexible}
                />
              </CCol>
            </CRow>
            <CButton type="submit" color="primary">
              {selectedPremium ? "Edit Premium" : "Create Premium"}
            </CButton>{" "}
            <CButton color="secondary" onClick={handleCloseModal}>
              Cancel
            </CButton>
          </Form>
        );
      }}
    </Formik>
  );
};

export default PremiumForm;
