import React from "react";
import ResultsPagination from "../../../components/ResultsPagination";

import PremiumCard from "./PremiumCard";
import ResultsAndPageNumbers from "./ResultsAndPageNumbers";

const PremiumListContainer = ({
  premiums,
  resultsPerPage,
  currentPage,
  updateCurrentPage,
  isLoading,
  totalPages,
  updateSelectedPremium,
  toggleShowModal,
  setShowDeleteModal,
}) => {
  const premiumsSlice = premiums.slice(
    (currentPage - 1) * Number(resultsPerPage),
    (currentPage - 1) * Number(resultsPerPage) + Number(resultsPerPage)
  );
  return (
    <>
      <ResultsAndPageNumbers
        premiumsLength={premiums.length}
        currentPage={currentPage}
        totalPages={totalPages}
      />
      <div className="row animated fadeIn">
        <div className="col-sm-12">
          {premiumsSlice.map((premium) => (
            <PremiumCard
              key={premium.id}
              premium={premium}
              updateSelectedPremium={updateSelectedPremium}
              toggleShowModal={toggleShowModal}
              setShowDeleteModal={setShowDeleteModal}
            />
          ))}
          <ResultsPagination
            totalPages={totalPages}
            currentPage={currentPage}
            handlePagination={updateCurrentPage}
            isLoading={isLoading}
          />
        </div>
      </div>
    </>
  );
};

export default PremiumListContainer;
