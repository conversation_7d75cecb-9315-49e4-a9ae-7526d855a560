import { CFormInput, CFormLabel } from "@coreui/react";
import React, { useEffect, useState } from "react";
import Select from "react-select";
import { selectStyles } from "../../Form/SelectStyles";

const PremiumResultsOptions = ({
  updateResultsPerPage,
  updateTotalPages,
  setCurrentPage,
  currentPage,
  premiumsLength,
  filter,
  updateFilter,
  categoryOptions,
}) => {
  const [categoryFilterOptions, setCategoryFilterOptions] = useState();

  useEffect(() => {
    if (categoryOptions) {
      setCategoryFilterOptions([
        { value: "", label: "All" },
        ...categoryOptions,
      ]);
    }
  }, [categoryOptions]);

  return (
    <div className="row my-5">
      <div className="col-sm-3">
        <CFormLabel htmlFor="nameFilter">Search by name</CFormLabel>
        <CFormInput
          type="search"
          id="nameFilter"
          className="form-control"
          onChange={(e) => {
            updateFilter("name", e.target.value);
          }}
          value={filter.name}
        ></CFormInput>
      </div>
      <div className="col-sm-3">
        <CFormLabel htmlFor="categoryFilter">Category</CFormLabel>
        <Select
          id="categoryFilter"
          onChange={(e) => {
            updateFilter("category", e.value);
          }}
          options={categoryFilterOptions}
          defaultValue={{ value: "", label: "All" }}
          styles={selectStyles}
        />
      </div>
      <div className="col-sm-3">
        <CFormLabel htmlFor="activeFilter">Active</CFormLabel>
        <Select
          id="activeFilter"
          onChange={(e) => {
            updateFilter("active", e.value);
          }}
          options={[
            { value: "", label: "All" },
            { value: true, label: "Yes" },
            { value: false, label: "No" },
          ]}
          defaultValue={{ value: "", label: "All" }}
          styles={selectStyles}
        />
      </div>
      <div className="col-sm-3">
        <CFormLabel htmlFor="resultsPerPage">Results Per Page</CFormLabel>
        <Select
          id="resultsPerPage"
          onChange={(e) => {
            const total = Math.ceil(premiumsLength / Number(e.value));
            updateResultsPerPage(e.value);
            updateTotalPages(total);
            setCurrentPage(currentPage > total ? total : currentPage);
          }}
          styles={selectStyles}
          defaultValue={{ value: 10, label: 10 }}
          options={[
            { value: 10, label: 10 },
            { value: 20, label: 20 },
            { value: 50, label: 50 },
            { value: 100, label: 100 },
          ]}
        />
      </div>
    </div>
  );
};

export default PremiumResultsOptions;
