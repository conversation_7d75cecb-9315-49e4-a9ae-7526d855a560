import React, { Component } from "react";
import moment from "moment";
import { CSVLink } from "react-csv";
import currency from "currency.js";
import { cloneDeep } from "lodash";

import ResultsPagination from "../../../components/ResultsPagination";
import DisplayResults from "./components/DisplayResults";
import Spinner from "./components/Spinner";
import Modals from "./components/Modals";
import { connect } from "react-redux";
import Select from "react-select";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { resultsPerPageOptions } from "../../Form/constants";
import useSortResultsArray from "../../Hooks/useSortResultsArray";
import TableHeader from "./components/TableHeader";

const today = moment().format("YYYY-MM-DD");
const dateMin = "1990-01-01";

class BillingReport extends Component {
  constructor() {
    super();

    this.state = {
      loading: false,
      resultsPerPage: 10,
      currentPage: 1,
      totalPages: 1,
      results: [],
      sortedResults: [],
      filteredResults: [],
      pledgeStatusFilter: "",
      pledgeHasPremiumsFilter: "",
      resultsWithEmails: [],
      resultsCSV: [],
      startDate: "",
      endDate: today,
      total: null,
      showConfirmationModal: false,
      showProcessingModal: false,
      showFailureModal: false,
      showSuccessModal: false,
      sortOrderAscending: true,
      sortColumn: "",
      //forceDescendingDate: true,
    };
  }

  handleDateChange = (event) => {
    this.setState(
      {
        loading: true,
        results: [],
        filteredResults: [],
        resultsWithEmails: [],
        resultsCSV: [],
        totalPages: 1,
        total: null,
        [event.target.name]: event.target.value,
      },
      () => {
        this.getDonations();
      },
    );
  };

  getDonations = () => {
    if (this.state.startDate && this.state.endDate) {
      if (
        moment(this.state.startDate).isAfter(dateMin) &&
        moment(this.state.endDate).isAfter(dateMin) &&
        (moment(this.state.startDate).isBefore(moment(this.state.endDate)) ||
          moment(this.state.startDate).isSame(moment(this.state.endDate)))
      ) {
        const headers = {
          Authorization: `Bearer ${localStorage.getItem("jwt")}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        };

        fetch(
          `https://${
            this.props.testMode.value ? this.props.testMode.route : ""
          }api.kpfa.org/donations/?start=${this.state.startDate}&end=${
            this.state.endDate
          }`,
          {
            headers,
            method: "GET",
          },
        )
          .then((response) => response.json())
          .then((response) => {
            if (response.records) {
              console.log(response.records);
              this.updateFilteredResults(response.records);
            }
            if (!response.records) {
              this.setState({
                results: [],
                filteredResults: [],
                resultsWithEmails: [],
                resultsCSV: [],
                totalPages: 1,
                currentPage: 1,
                loading: false,
                total: null,
              });
            }
          });
      }
      if (moment(this.state.startDate).isAfter(moment(this.state.endDate))) {
        this.setState({
          results: [],
          filteredResults: [],
          resultsWithEmails: [],
          resultsCSV: [],
          totalPages: 1,
          currentPage: 1,
          loading: false,
          total: null,
        });
      }
    }
    if (
      moment(this.state.startDate).isBefore(dateMin) ||
      moment(this.state.endDate).isBefore(dateMin) ||
      !this.state.startDate ||
      !this.state.endDate
    ) {
      this.setState({
        results: [],
        filteredResults: [],
        resultsWithEmails: [],
        resultsCSV: [],
        totalPages: 1,
        currentPage: 1,
        loading: false,
        total: null,
      });
    }
  };

  updateFilteredResults = (results) => {
    const filteredResults = this.filterResults(results);

    const totalPages = Math.ceil(
      filteredResults.length / this.state.resultsPerPage,
    );
    const resultsCSV = cloneDeep(filteredResults).map((pledge) => {
      if (pledge.comments) {
        pledge.comments = pledge.comments.replace(/."/g, '""');
      }

      pledge.numberOfPremiums = pledge.premiums.length;
      if (pledge.premiums.length > 0) {
        pledge.premiums.forEach((premium, index) => {
          pledge[`premium_${index + 1}`] = premium.name.replace(/."/g, '""');
        });
      }
      delete pledge.premiums;
      const paymentsString =
        pledge.payments.length > 0
          ? pledge.payments
              .map((payment) => `$${payment.amount} (${payment.method})`)
              .join("\n")
          : "";
      pledge.payments = paymentsString;

      const shipmentsString =
        pledge.shipments.length > 0
          ? pledge.shipments
              .map(
                (shipment) =>
                  `ID:${shipment.id} Premium:${shipment.premium_id} (${shipment.status})`,
              )
              .join("\n")
          : "";
      pledge.shipments = shipmentsString;

      return pledge;
    });
    const resultsWithEmails = filteredResults.filter((pledge) => {
      if (pledge.email !== null && pledge.email !== "") {
        return true;
      }
      return false;
    });
    const total = filteredResults.reduce((accumulator, pledge) => {
      return currency(accumulator).add(currency(pledge.amount)).value;
    }, 0);

    this.setState({
      results,
      filteredResults,
      resultsWithEmails,
      resultsCSV,
      currentPage: 1,
      totalPages,
      loading: false,
      total,
    });
  };

  filterResults = (results) => {
    let filteredResults = cloneDeep(results).filter(
      (result) => !(result.status === "Unpaid" && result.source === "WebSite"),
    );
    if (this.state.pledgeStatusFilter === "true") {
      filteredResults = filteredResults.filter(
        (result) => result.status === "Paid",
      );
    } else if (this.state.pledgeStatusFilter === "false") {
      filteredResults = filteredResults.filter(
        (result) => result.status === "Unpaid",
      );
    }
    if (this.state.pledgeHasPremiumsFilter === "true") {
      filteredResults = filteredResults.filter(
        (result) => result.premiums.length > 0,
      );
    } else if (this.state.pledgeHasPremiumsFilter === "false") {
      filteredResults = filteredResults.filter(
        (result) => result.premiums.length === 0,
      );
    }
    return filteredResults;
  };

  handleResultsPerPage = (event) => {
    const resultsPerPage = parseInt(event.value);
    const totalPages = Math.ceil(this.state.results.length / resultsPerPage);
    const currentPage =
      this.state.currentPage > totalPages ? totalPages : this.state.currentPage;
    this.setState({ resultsPerPage, totalPages, currentPage });
  };

  handleStatusFilter = (event) => {
    this.setState({ pledgeStatusFilter: event.value }, () => {
      this.updateFilteredResults(this.state.results);
    });
  };

  handlePremiumFilter = (event) => {
    this.setState({ pledgeHasPremiumsFilter: event.value }, () => {
      this.updateFilteredResults(this.state.results);
    });
  };

  handlePagination = (e) => {
    const paginationChange = parseInt(e.target.dataset.increment);
    this.setState({ currentPage: this.state.currentPage + paginationChange });
  };

  handleSendEmail = () => {
    if (this.state.results.length > 0) {
      this.setState({
        showConfirmationModal: true,
        showProcessingModal: false,
        showFailureModal: false,
        showSuccessModal: false,
      });
    }
  };

  closeModal = () => {
    this.setState({
      showConfirmationModal: false,
      showProcessingModal: false,
      showFailureModal: false,
      showSuccessModal: false,
      // formSubmitted: false
    });
  };

  confirmSendEmail = () => {
    this.setState({
      showProcessingModal: true,
      showConfirmationModal: false,
      showFailureModal: false,
      showSuccessModal: false,
    });
  };

  displayFailureModal = () => {
    this.setState({
      showFailureModal: true,
      formSubmitted: true,
      showConfirmationModal: false,
      showProcessingModal: false,
      showSuccessModal: false,
    });
  };

  displaySuccessModal = () => {
    this.setState({
      showSuccessModal: true,
      formSubmitted: true,
      showConfirmationModal: false,
      showProcessingModal: false,
      showFailureModal: false,
    });
  };

  pledgeStatusOptions = [
    { value: "", label: "Show All" },
    { value: "true", label: "Paid" },
    { value: "false", label: "Unpaid" },
  ];

  premiumStatusOptions = [
    { value: "", label: "Show All" },
    { value: "true", label: "With Premiums" },
    { value: "false", label: "No Premiums" },
  ];

  handleChangeSort = (event) => {
    const results = cloneDeep(this.state.results);
    if (this.state.sortColumn === event.currentTarget.id) {
      this.setState(
        { sortOrderAscending: !this.state.sortOrderAscending },
        () => {
          const sortedResults = useSortResultsArray(
            results,
            this.state.sortColumn,
            this.state.sortOrderAscending,
          );
          this.setState({ sortedResults });
        },
      );
    }
    if (this.state.sortColumn !== event.currentTarget.id) {
      this.setState({ sortColumn: event.currentTarget.id }, () => {
        const sortedResults = useSortResultsArray(
          results,
          this.state.sortColumn,
          this.state.sortOrderAscending,
        );
        this.setState({ sortedResults });
      });
    }
  };

  render() {
    return (
      <div className="animated fadeIn">
        <div className="card">
          <div className="card-body">
            <div className="row">
              <div className="col-sm-12 col-md-6 col-lg-4 form-group mb-3">
                <label htmlFor="start">Start date</label>

                <input
                  className="form-control me-2"
                  type="date"
                  name="startDate"
                  min={dateMin}
                  max={today}
                  onChange={this.handleDateChange}
                  value={this.state.startDate}
                />
              </div>
              <div className="col-sm-12 col-md-6 col-lg-4 form-group mb-3">
                <label htmlFor="start">End date</label>

                <input
                  className="form-control me-2"
                  type="date"
                  name="endDate"
                  min={dateMin}
                  max={today}
                  onChange={this.handleDateChange}
                  value={this.state.endDate}
                />
              </div>
              <div className="col-sm-12 col-md-6 col-lg-4 form-group mb-3">
                <label htmlFor="resultsPerPage">Results Per Page</label>
                <Select
                  id="resultsPerPage"
                  onChange={this.handleResultsPerPage}
                  options={resultsPerPageOptions}
                  defaultValue={resultsPerPageOptions[0]}
                />
              </div>
            </div>
            <div className="row mb-3">
              <div className="col-sm-12 col-md-6 col-lg-2 form-group mb-3">
                <label htmlFor="filter-status">Filter by Status</label>
                <Select
                  id="filter-status"
                  onChange={this.handleStatusFilter}
                  options={this.pledgeStatusOptions}
                  defaultValue={this.pledgeStatusOptions[0]}
                />
              </div>
              <div className="col-sm-12 col-md-6 col-lg-2 form-group mb-3">
                <label htmlFor="filter-premiums">Filter by Premiums</label>
                <Select
                  id="filter-premiums"
                  onChange={this.handlePremiumFilter}
                  options={this.premiumStatusOptions}
                  defaultValue={this.premiumStatusOptions[0]}
                />
              </div>
              <div className="col-sm-12 col-md-6 col-lg-4 text-center">
                {this.state.total ? (
                  <div className="alert alert-info mt-3">
                    Total for Selected Results:
                    <strong> {currency(this.state.total).format()}</strong>
                  </div>
                ) : (
                  ""
                )}
              </div>
              <div className="col-sm-12 col-md-6 col-lg-4 text-right">
                <button
                  className="btn btn-primary mt-4 me-2"
                  disabled={
                    this.state.resultsWithEmails.length > 0 ? false : true
                  }
                  onClick={this.handleSendEmail}
                >
                  <FontAwesomeIcon className="me-1" icon="envelope" />
                  Email Invoices
                </button>
                <CSVLink
                  data={this.state.resultsCSV}
                  className={
                    this.state.resultsCSV.length > 0
                      ? "btn btn-primary mt-4"
                      : "btn btn-primary disabled mt-4"
                  }
                  filename={`donation_report_fr_${this.state.startDate}_to_${
                    this.state.endDate
                  }_saved_${moment().format("YYYY-MM-DD_HH-mm")}.csv`}
                >
                  <FontAwesomeIcon icon="download" className="me-1" />
                  Download CSV
                </CSVLink>
              </div>
            </div>
            <div className="row">
              <div className="col-sm-6">
                {this.state.filteredResults.length > 0 ? (
                  <h6>
                    {this.state.filteredResults.length + " Donations Found"}
                  </h6>
                ) : null}
              </div>
              <div className="col-sm-6 text-right">
                {this.state.filteredResults.length > 0 ? (
                  <h6>
                    {"page " +
                      this.state.currentPage +
                      " of " +
                      this.state.totalPages}
                  </h6>
                ) : null}
              </div>
            </div>
            <TableHeader
              handleChangeSort={this.handleChangeSort}
              sortOrderAscending={this.state.sortOrderAscending}
              sortColumn={this.state.sortColumn}
            />
            <Spinner loading={this.state.loading} />
            <DisplayResults
              sortedResults={this.state.sortedResults}
              results={this.state.filteredResults}
              currentPage={this.state.currentPage}
              totalPages={this.state.totalPages}
              resultsPerPage={this.state.resultsPerPage}
              loading={this.state.loading}
              startDate={this.state.startDate}
              endDate={this.state.endDate}
            />
            <ResultsPagination
              results={this.state.filteredResults}
              currentPage={this.state.currentPage}
              totalPages={this.state.totalPages}
              handlePagination={this.handlePagination}
            />
          </div>
        </div>
        <Modals
          results={this.state.filteredResults}
          resultsWithEmails={this.state.resultsWithEmails}
          showConfirmationModal={this.state.showConfirmationModal}
          showProcessingModal={this.state.showProcessingModal}
          showFailureModal={this.state.showFailureModal}
          showSuccessModal={this.state.showSuccessModal}
          closeModal={this.closeModal}
          displayFailureModal={this.displayFailureModal}
          displaySuccessModal={this.displaySuccessModal}
          confirmSendEmail={this.confirmSendEmail}
        />
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return { testMode: state.testMode };
};

export default connect(mapStateToProps)(BillingReport);
