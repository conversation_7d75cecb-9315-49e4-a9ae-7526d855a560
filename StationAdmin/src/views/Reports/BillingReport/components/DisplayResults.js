import React from "react";
import moment from "moment";
import { cloneDeep } from "lodash";
import { CBadge } from "@coreui/react";

const DisplayResults = ({
  sortedResults,
  results: propResults,
  currentPage,
  loading,
  resultsPerPage,
  startDate,
  endDate,
}) => {
  if (propResults.length > 0) {
    const results =
      sortedResults.length > 0
        ? cloneDeep(sortedResults)
        : cloneDeep(propResults);
    const startIndex = (currentPage - 1) * resultsPerPage;
    const resultsSlice = results.slice(startIndex, startIndex + resultsPerPage);

    const tableRows = resultsSlice.map((pledge, index) => {
      const getBadgeClassName = () => {
        if (pledge.status.toLowerCase() === "paid") {
          return "success";
        }
        if (pledge.status.toLowerCase() === "unpaid") {
          return "warning";
        }
        if (pledge.status.toLowerCase() === "canceled") {
          return "danger";
        }
        if (pledge.status.toLowerCase() === "refunded") {
          return "info";
        }
      };

      return (
        <div className="row mt-3 mb-3" key={"row" + index}>
          <div className="col-sm-2">
            <p>{moment(pledge.timestamp).format("MM-DD-YYYY")}</p>
          </div>
          <div className="col-sm-3">
            <span className="">{pledge.firstname + " " + pledge.lastname}</span>
          </div>
          <div className="col-sm-2">
            <span className="">{"$" + pledge.amount}</span>
          </div>
          <div className="col-sm-1">
            <CBadge color={getBadgeClassName()}>
              {pledge.status.charAt(0).toUpperCase() + pledge.status.slice(1)}
            </CBadge>
          </div>
          <div className="col-sm-4">
            {pledge.premiums_ID !== null ? (
              <p className="premiums-list">{pledge.premiums_ID.toString()}</p>
            ) : null}
          </div>
        </div>
      );
    });
    // console.log(tableRows);
    return tableRows;
  }
  if (!propResults.length && loading === false) {
    return (
      <div className="row">
        <div className="col-sm-12 mt-2">
          <h4 className="mt-4 mb-4">No Results to Display</h4>
          {moment(startDate).isAfter(moment(endDate)) ? (
            <div className="alert alert-danger" role="alert">
              Invalid date range selected.
            </div>
          ) : null}
        </div>
      </div>
    );
  }
  return null;
};

export default DisplayResults;
