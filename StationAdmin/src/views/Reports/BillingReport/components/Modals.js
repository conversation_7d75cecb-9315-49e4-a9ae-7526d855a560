import React from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCollapse,
  CCardBody,
  CCard,
} from "@coreui/react";
import { connect } from "react-redux";

class Modals extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      response: null,
      responseContent: null,
      detailedResponse: [],
      receipt: null,
      collapse: false,
    };
  }

  toggle = () => {
    const collapse = !this.state.collapse;
    this.setState({ collapse });
  };

  url = `https://${
    this.props.testMode.value ? this.props.testMode.route : ""
  }api.kpfa.org/email`;

  confirmationModal = () => {
    if (this.props.showConfirmationModal === true) {
      return (
        <CModal
          visible={true}
          onDismiss={this.props.closeModal}
          className={this.props.className}
          backdrop="static"
          keyboard={false}
        >
          <CModalHeader onDismiss={this.props.closeModal}>
            Please review and confirm the emails to sent out
          </CModalHeader>
          <CModalBody>
            <h4>{this.props.resultsWithEmails.length} emails to be sent:</h4>
            <div>
              {this.props.resultsWithEmails.map((pledge, index) => {
                return (
                  <div className="row" key={"n" + index}>
                    <div className="col-sm-6">{`${pledge.firstname} ${pledge.lastname}`}</div>
                    <div className="col-sm-6">{`${pledge.email}`}</div>
                  </div>
                );
              })}
            </div>
          </CModalBody>
          <CModalFooter>
            <CButton color="secondary" onClick={this.props.closeModal}>
              Cancel
            </CButton>
            <CButton color="primary" onClick={this.props.confirmSendEmail}>
              Confirm &amp; Send Emails
            </CButton>
          </CModalFooter>
        </CModal>
      );
    }
    return null;
  };

  processingModal = () => {
    if (this.props.showProcessingModal === true) {
      const transactionIds = this.props.resultsWithEmails.map((pledge) => {
        return pledge.transaction_id;
      });
      const requestBody = { type: "reminder", transaction_ids: transactionIds };

      fetch(this.url, {
        method: "POST", // or 'PUT'
        mode: "cors",
        body: JSON.stringify(requestBody),
        headers: {
          Authorization: `Bearer ${localStorage.getItem("jwt")}`,
          "Content-Type": "application/json",
        },
      })
        .then((response) => response.json())
        .then((response) => {
          // console.log(response);
          if (response.status === "error") {
            this.props.displayFailureModal();
            this.setState({
              response: response.status,
              responseContent: response.message,
              detailedResponse: [],
              receipt: null,
            });
          }
          if (response.status === "success" || response.status === "mixed") {
            this.props.displaySuccessModal();
            this.setState({
              response: response.status,
              responseContent: response.message,
              receipt: response.receipt,
              detailedResponse: response.transactions,
            });
          }
        })
        .catch((response) => {
          // console.log(response);
          this.props.displayFailureModal();
          this.setState({
            response: response,
            responseContent: null,
            detailedResponse: [],
            receipt: null,
          });
        });

      // setTimeout(() => {
      //   this.props.displaySuccessModal();
      //   this.setState({
      //     response: true,
      //     responseContent: "97 emails were sent.",
      //     detailedResponse: [
      //       { transaction_id: "12345", status: "error" },
      //       { transaction_id: "67890", status: "success" }
      //     ]
      //   });
      // }, 3000);

      return (
        <CModal
          visible={true}
          className={this.props.className}
          backdrop="static"
          keyboard={false}
        >
          <CModalHeader>
            <span className="badge badge-warning">Please Wait</span>
          </CModalHeader>
          <CModalBody>
            <div className="d-flex justify-content-center mt-2 mb-2">
              <h3>
                <div
                  className="spinner-border text-primary"
                  style={{
                    width: "2rem",
                    height: "2rem",
                    marginRight: "0.5rem",
                  }}
                  role="status"
                >
                  <span className="sr-only">Processing Changes...</span>
                </div>
                Processing Changes...
              </h3>
            </div>
            <p>
              Please do not close this window or navigate away from this page
              until your changes have been processed and the confirmation
              message is displayed.
            </p>
          </CModalBody>
        </CModal>
      );
    }
    return null;
  };

  failureModal = () => {
    if (this.props.showFailureModal === true) {
      return (
        <CModal
          visible={true}
          onDismiss={this.closeModal}
          className={this.props.className}
          backdrop={true}
          keyboard={true}
        >
          <CModalHeader onDismiss={this.resetModals}>
            <span className="badge badge-danger">Error</span> Email Sending
            Failed
          </CModalHeader>
          <CModalBody>
            <p>Emails were not sent to the selected addresses</p>
            {this.state.responseContent ? (
              <React.Fragment>
                <p>
                  <strong>{this.state.responseContent}</strong>
                </p>
              </React.Fragment>
            ) : null}
            {!this.state.responseContent ? (
              <p>
                Error reaching KPFA server. Please check your internet
                connection and try again.
              </p>
            ) : null}
          </CModalBody>
          <CModalFooter>
            <CButton color="primary" onClick={this.props.closeModal}>
              Return to Billing Report
            </CButton>
          </CModalFooter>
        </CModal>
      );
    }
    return null;
  };

  successModal = () => {
    if (this.props.showSuccessModal === true) {
      return (
        <CModal
          visible={true}
          onDismiss={this.props.closeModal}
          className={this.props.className}
          backdrop={true}
          keyboard={true}
        >
          <CModalHeader onDismiss={this.props.closeModal}>
            <span className="badge badge-success">Emails Sent</span>
          </CModalHeader>
          <CModalBody>
            <div>
              <p>
                <strong>{this.state.responseContent}</strong>
              </p>

              <div>
                <CButton
                  color="primary"
                  onClick={this.toggle}
                  style={{ marginBottom: "1rem" }}
                >
                  Show Detailed Results
                </CButton>
                <CCollapse visible={this.state.collapse}>
                  <CCard>
                    <CCardBody>
                      <div>
                        {/* {this.state.detailedResponse.map(email => {retun email.})} */}
                        <div className="row">
                          <div className="col-sm-6">
                            {JSON.stringify(this.state.detailedResponse, 4)}
                          </div>
                        </div>
                      </div>
                    </CCardBody>
                  </CCard>
                </CCollapse>
              </div>
            </div>
          </CModalBody>
          <CModalFooter>
            <CButton color="primary" onClick={this.props.closeModal}>
              Return to Billing Report
            </CButton>
          </CModalFooter>
        </CModal>
      );
    }
    return null;
  };

  render() {
    return (
      <>
        {this.confirmationModal()}
        {this.processingModal()}
        {this.failureModal()}
        {this.successModal()}
      </>
    );
  }
}

const mapStateToProps = (state) => {
  return { testMode: state.testMode };
};

export default connect(mapStateToProps)(Modals);
