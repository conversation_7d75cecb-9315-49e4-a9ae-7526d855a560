import React from "react";
import useShowSortArrow from "../../../Hooks/useShowSortArrow";

export default function TableHeader({
  handleChangeSort,
  sortOrderAscending,
  sortColumn,
}) {
  return (
    <div className="row bg-dark text-white pt-1 pb-1 font-weight-bold mt-1 mb-2">
      <div
        type="button"
        className="col-sm-2"
        id="timestamp"
        onClick={handleChangeSort}
      >
        Date
        {useShowSortArrow("date", sortColumn, sortOrderAscending)}
      </div>
      <div
        className="col-sm-3"
        type="button"
        id="lastname"
        onClick={handleChangeSort}
      >
        Name
        {useShowSortArrow("lastname", sortColumn, sortOrderAscending)}
      </div>
      <div
        className="col-sm-2"
        type="button"
        id="amount"
        onClick={handleChangeSort}
      >
        Amount
        {useShowSortArrow("amount", sortColumn, sortOrderAscending)}
      </div>
      <div
        className="col-sm-1"
        type="button"
        id="status"
        onClick={handleChangeSort}
      >
        Status
        {useShowSortArrow("status", sortColumn, sortOrderAscending)}
      </div>
      <div
        className="col-sm-4"
        type="button"
        id="premiums"
        onClick={handleChangeSort}
      >
        Premiums
        {useShowSortArrow("premiums", sortColumn, sortOrderAscending)}
      </div>
    </div>
  );
}
