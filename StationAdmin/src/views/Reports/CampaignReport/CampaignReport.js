import { useState, useEffect } from "react";
import ReactSelect from "react-select";
import moment from "moment";

import CampaignInfo from "./components/CampaignInfo";
import CampaignDonutChart from "./components/CampaignDonutChart";
import LineChart from "./components/LineChart";
import AllCampaignsInfo from "./components/AllCampaignsInfo";
import Spinner from "./components/Spinner";
import { useGetCampaignQuery, useGetCampaignsQuery } from "../../../api/kpfa";
import CampaignDonations from "./components/CampaignDonations";

const CampaignReport = () => {
  const [campaignOptions, updateCampaignOptions] = useState([
    { value: "", label: "All Campaigns" },
  ]);
  const [selectedCampaign, setSelectedCampaign] = useState({
    value: "",
    label: "All Campaigns",
    campaignObj: {},
  });
  const [skip, setSkip] = useState(true);
  const [activeCampaign, setActiveCampaign] = useState();
  const [campaignDonationsVisible, setCampaignDonationsVisible] = useState(
    false
  );

  const { data: campaigns, isLoading } = useGetCampaignsQuery();

  useEffect(() => {
    if (selectedCampaign?.campaignObj.active) {
      setSkip(false);
    } else {
      setSkip(true);
    }
  }, [setSkip, selectedCampaign]);

  useEffect(() => {
    if (campaigns?.length > 0) {
      const campaignArray = campaigns.map((campaign) => ({
        value: campaign.id,
        label: `${campaign.name} (${moment(campaign.start).format(
          "MM/DD/YYYY"
        )} - ${moment(campaign.end).format("MM/DD/YYYY")})`,
        campaignObj: campaign,
      }));
      campaignArray.unshift({
        value: "",
        label: "All Campaigns",
        campaignObj: {},
      });
      updateCampaignOptions(campaignArray);

      let activeCampaigns = campaigns.filter(
        (campaign) => campaign.active === true
      );

      if (activeCampaigns?.length > 0) {
        let activeCampaign = activeCampaigns[0];

        let activeCampaignData = {
          value: activeCampaign.id,
          label: `${activeCampaign.name} (${moment(activeCampaign.start).format(
            "MM/DD/YYYY"
          )} - ${moment(activeCampaign.end).format("MM/DD/YYYY")})`,
          campaignObj: activeCampaign,
        };
        setActiveCampaign(activeCampaign.id);
        setSelectedCampaign(activeCampaignData);
      } else {
        let smallestDiffFrToday;
        let prevCampaignData = null;
        campaigns.forEach((campaign) => {
          const diff = moment().diff(moment(campaign.end));
          if (
            (diff > 0 && smallestDiffFrToday > diff) ||
            (diff > 0 && !smallestDiffFrToday)
          ) {
            smallestDiffFrToday = diff;
            prevCampaignData = {
              value: campaign.id,
              label: `${campaign.name} (${moment(campaign.start).format(
                "MM/DD/YYYY"
              )} - ${moment(campaign.end).format("MM/DD/YYYY")})`,
              campaignObj: campaign,
            };
          }
        });
        setSelectedCampaign(prevCampaignData);
      }
    }
  }, [campaigns, updateCampaignOptions]);

  const { data: activeCampaignData } = useGetCampaignQuery(activeCampaign, {
    pollingInterval: 10000,
    skip,
  });

  return (
    <div className="animated fadeIn">
      <div className="card">
        <div className="card-body">
          {isLoading ? (
            <Spinner />
          ) : (
            <>
              <div className="row mb-5 mt-4">
                <div className="col-sm-3"></div>
                <div className="col-sm-6 d-flex flex-column justify-content-center">
                  {campaigns?.length > 0 ? (
                    <>
                      <h5>Select a Campaign to View Information</h5>
                      <ReactSelect
                        options={campaignOptions}
                        value={selectedCampaign}
                        onChange={(selection) => {
                          setCampaignDonationsVisible(false);
                          setSelectedCampaign(selection);
                        }}
                      />
                    </>
                  ) : null}
                </div>
                <div className="col-sm-3"></div>
              </div>
              <div className="row">
                <div
                  className={
                    selectedCampaign.value !== "" ? "col-sm-7" : "col-sm-5"
                  }
                >
                  <div className="card">
                    <div className="card-body">
                      {selectedCampaign.value !== "" ? (
                        <CampaignInfo
                          selectedCampaign={
                            selectedCampaign.campaignObj.active &&
                            activeCampaignData?.length
                              ? activeCampaignData[0]
                              : selectedCampaign.campaignObj
                          }
                        />
                      ) : campaigns ? (
                        <AllCampaignsInfo campaigns={campaigns} />
                      ) : null}
                    </div>
                  </div>
                </div>
                <div
                  className={
                    selectedCampaign.value !== "" ? "col-sm-5" : "col-sm-7"
                  }
                >
                  <div
                    className="card d-flex align-items-center justify-content-center"
                    style={{ height: "calc(100% - 1.5em)" }}
                  >
                    <div className="card-body w-100">
                      {selectedCampaign.value !== "" ? (
                        <CampaignDonutChart
                          selectedCampaign={
                            selectedCampaign.campaignObj.active &&
                            activeCampaignData?.length
                              ? activeCampaignData[0]
                              : selectedCampaign.campaignObj
                          }
                        />
                      ) : campaigns ? (
                        <LineChart campaigns={campaigns} />
                      ) : null}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          <CampaignDonations
            selectedCampaignID={selectedCampaign.value}
            selectedCampaignName={selectedCampaign.label}
            visible={campaignDonationsVisible}
            setVisible={setCampaignDonationsVisible}
          />
        </div>
      </div>
    </div>
  );
};

export default CampaignReport;
