import {
  <PERSON>utton,
  CRow,
  CTable,
  CTable<PERSON><PERSON>er<PERSON>ell,
  CTableRow,
  CTableBody,
  CTableHead,
  CTableDataCell,
  CCard,
  CBadge,
} from "@coreui/react";
import { useEffect, useState } from "react";
import { useLazyGetCampaignDonationsQuery } from "../../../../api/kpfa";
import {
  grossTotal,
  showSortArrow,
  sortResultsArray,
} from "../../../../helpers";
import { Donation } from "../../../../types";
import { CSVLink } from "react-csv";
import Spinner from "./Spinner";
import { Link } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { cloneDeep } from "lodash";
import ResultsPagination from "../../../../components/ResultsPagination";

type Props = {
  selectedCampaignID: number;
  selectedCampaignName: string;
  visible: boolean;
  setVisible: (visibility: boolean) => void;
};

interface MutatedDonation extends Donation {
  grossTotal?: number;
  firstShipmentStatus?: string;
}

export default function CampaignDonations({
  selectedCampaignID,
  selectedCampaignName,
  visible,
  setVisible,
}: Props) {
  const [trigger] = useLazyGetCampaignDonationsQuery();
  const [donations, setDonations] = useState<Donation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [sortOrderAscending, setSortOrderAscending] = useState(true);
  const [sortColumn, setSortColumn] = useState<string>("");
  const [mutatedDonations, setMutatedDonations] = useState<MutatedDonation[]>(
    [],
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState<number>();
  const [startIndex, setStartIndex] = useState(0);

  /*
   * todo: for now, hide the donations table whenever a new campaign is selected
   * in the future, we should probably keep the table visible and just update the data
   * but this is a good start until pagination is implemented
   *
   * todo: error handling
   *
   * * */

  const resultsPerPage = 50;

  useEffect(() => {
    //when a new campaign is selected and the table is automatically hidden, reset props to defaults to not display stale data
    if (!visible) {
      setSortColumn("");
      setSortOrderAscending(true);
      setDonations([]);
      setMutatedDonations([]);
      setStartIndex(0);
      setCurrentPage(1);
    }
  }, [visible]);

  const handleResultsPerPage = (totalLength: number) => {
    const totalPages = Math.ceil(totalLength / resultsPerPage);
    const newCurrentPage = currentPage > totalPages ? totalPages : currentPage;
    setTotalPages(totalPages);
    setCurrentPage(newCurrentPage);
  };

  const handlePagination = (e: React.MouseEvent<HTMLElement>) => {
    const increment = e.currentTarget.getAttribute("data-increment");
    if (increment !== null) {
      const paginationChange = parseInt(increment);
      setCurrentPage(currentPage + paginationChange);
      setStartIndex((currentPage + paginationChange - 1) * resultsPerPage);
    }
  };

  const showDonations = async () => {
    setIsLoading(true);
    setCurrentPage(1);
    await trigger(selectedCampaignID)
      .then((result: any) => {
        setDonations(
          result.data.records.filter(
            (record: Donation) =>
              !(record.source === "WebSite" && record.payments.length === 0),
          ),
        );
        setVisible(true);
        handleResultsPerPage(result.data.records.length);
      })
      .catch((rejected) => {
        console.log(rejected);
      });
    setIsLoading(false);
  };

  const formatTime = (time: string) => {
    const date = new Date(time);
    return date.toLocaleString("en-US", {
      hour: "numeric",
      minute: "numeric",
      month: "numeric",
      day: "numeric",
      year: "numeric",
      hour12: true,
    });
  };

  const csvData = cloneDeep(donations);
  const rowData = csvData.map((donation) => {
    const row = {
      fullname: `${donation.firstname} ${donation.lastname}`.replace(
        /."/g,
        '""',
      ),
      location: `${donation.city}, ${donation.state} ${donation.postal_code}`,
      source: donation.source,
      pledged: `$${donation.amount}`,
      paid: `$${grossTotal(donation.payments)}`,
      installment: donation.installment,
      premiums: donation.premiums_ID.join(", "),
      shipment_status: donation.shipments
        .map((shipment) => {
          return shipment.status;
        })
        .join(", "),
      comments: donation.comments,
      time: formatTime(donation.timestamp),
    };

    return row;
  });

  const csvHeaders = [
    { label: "Full Name", key: "fullname" },
    { label: "Location", key: "location" },
    { label: "Source", key: "source" },
    { label: "Pledged", key: "pledged" },
    { label: "Paid", key: "paid" },
    { label: "Installment", key: "installment" },
    { label: "Premiums", key: "premiums" },
    { label: "Shipment Status", key: "shipment_status" },
    { label: "Comments", key: "comments" },
    { label: "Time", key: "time" },
  ];

  const shipmentBadges = (shipmentStatuses: string[]) => {
    const colorSwitch = (status: string) => {
      switch (status) {
        case "Shipped":
          return "success";
        case "On Hold":
          return "warning";
        case "returned":
          return "dark";
        case "New":
          return "info";
      }
    };
    return shipmentStatuses.map((status, index) => {
      return (
        <CBadge key={index} className="me-1" color={colorSwitch(status)}>
          {status}
        </CBadge>
      );
    });
  };

  const handleChangeSort = (e: React.MouseEvent<HTMLElement>): void => {
    if (mutatedDonations.length === 0) {
      let results: MutatedDonation[] = cloneDeep(donations);
      for (const index of results.keys()) {
        results[index].grossTotal = grossTotal(results[index].payments);
        results[index].firstShipmentStatus =
          results[index].shipments[0]?.status;
      }
      setMutatedDonations(results);
    }

    if (sortColumn === e.currentTarget.id) {
      setSortOrderAscending(!sortOrderAscending);
    }

    if (sortColumn !== e.currentTarget.id) {
      setSortColumn(e.currentTarget.id);
    }
  };

  useEffect(() => {
    if (sortColumn != null) {
      const sortedResults = sortResultsArray(
        mutatedDonations,
        sortColumn,
        sortOrderAscending,
      );
      setDonations(sortedResults);
    }

    //todo: note - there is some performance issue with this. Not sure if it's a rendering issue or an issue with the sortResultsArray function, but if it feels like there is a lag, it's probably from within this hook.
    // It would be worth using the React devtools profiler / flamegraph to see if there is a way to optimize this.
    // If it is a rendering issue my first attempt to improve would be adding front-end pagination.
    //
    // update: adding front end pagination noticeably improved performance for campaigns with thousands of donations.
  }, [sortColumn, sortOrderAscending, mutatedDonations]);

  return (
    <>
      <CRow className="d-flex mt-5">
        <CButton className="ms-3 mb-3 w-auto" onClick={showDonations}>
          Show Donations
        </CButton>
        {isLoading && <Spinner />}
        {visible && donations?.length ? (
          <CSVLink
            data={rowData}
            headers={csvHeaders}
            className="ms-2 btn-primary btn w-auto h-100"
            filename={`Donations: ${selectedCampaignName}.csv`}
          >
            <FontAwesomeIcon icon="file-csv" className="me-2" />
            Download CSV
          </CSVLink>
        ) : null}
      </CRow>
      {visible && donations?.length ? (
        <CCard>
          <h2 className="m-2 my-3">Campaign Donations</h2>

          <CTable responsive striped>
            <CTableHead>
              <CTableRow>
                <CTableHeaderCell
                  scope="col"
                  id="lastname"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Name
                  {showSortArrow("lastname", sortColumn, sortOrderAscending)}
                </CTableHeaderCell>
                <CTableHeaderCell
                  scope="col"
                  id="city"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Location
                  {showSortArrow("city", sortColumn, sortOrderAscending)}
                </CTableHeaderCell>
                <CTableHeaderCell
                  scope="col"
                  id="source"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Source
                  {showSortArrow("source", sortColumn, sortOrderAscending)}
                </CTableHeaderCell>
                <CTableHeaderCell
                  scope="col"
                  id="amount"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Pledged
                  {showSortArrow("amount", sortColumn, sortOrderAscending)}
                </CTableHeaderCell>
                <CTableHeaderCell
                  scope="col"
                  id="grossTotal"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Paid
                  {showSortArrow("grossTotal", sortColumn, sortOrderAscending)}
                </CTableHeaderCell>

                <CTableHeaderCell
                  scope="col"
                  id="installment"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Installment
                  {showSortArrow("installment", sortColumn, sortOrderAscending)}
                </CTableHeaderCell>
                <CTableHeaderCell
                  scope="col"
                  id="premiums_ID"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Premiums
                  {showSortArrow("premiums_ID", sortColumn, sortOrderAscending)}
                </CTableHeaderCell>
                <CTableHeaderCell
                  scope="col"
                  id="firstShipmentStatus"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Shipment Status
                  {showSortArrow(
                    "firstShipmentStatus",
                    sortColumn,
                    sortOrderAscending,
                  )}
                </CTableHeaderCell>
                <CTableHeaderCell
                  scope="col"
                  id="comments"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Comment
                  {showSortArrow("comments", sortColumn, sortOrderAscending)}
                </CTableHeaderCell>
                <CTableHeaderCell
                  scope="col"
                  id="timestamp"
                  onClick={handleChangeSort}
                  role="button"
                >
                  Time
                  {showSortArrow("timestamp", sortColumn, sortOrderAscending)}
                </CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {donations
                .slice(startIndex, startIndex + resultsPerPage)
                .map((donation) => (
                  <CTableRow key={donation.id}>
                    <CTableDataCell>
                      {
                        <Link
                          to={{
                            pathname: "/donors",
                          }}
                          state={{ linkedDonorID: donation.donor_id }}
                        >
                          {donation.firstname} {donation.lastname}
                        </Link>
                      }
                    </CTableDataCell>
                    <CTableDataCell>
                      {donation.city}, {donation.state}
                    </CTableDataCell>
                    <CTableDataCell>{donation.source}</CTableDataCell>
                    <CTableDataCell>${donation.amount}</CTableDataCell>
                    <CTableDataCell>
                      ${grossTotal(donation.payments)}
                    </CTableDataCell>
                    <CTableDataCell>{donation.installment}</CTableDataCell>
                    <CTableDataCell>
                      {donation.premiums_ID.join(", ")}
                    </CTableDataCell>
                    <CTableDataCell>
                      {shipmentBadges(
                        donation.shipments.map((shipment) => {
                          return shipment.status;
                        }),
                      )}
                    </CTableDataCell>
                    <CTableDataCell>{donation.comments}</CTableDataCell>
                    <CTableDataCell>
                      {formatTime(donation.timestamp)}
                    </CTableDataCell>
                  </CTableRow>
                ))}
            </CTableBody>
          </CTable>
          <ResultsPagination
            currentPage={currentPage}
            totalPages={totalPages}
            handlePagination={handlePagination}
          />
        </CCard>
      ) : null}
      {visible && !donations?.length && !isLoading ? (
        <h3 className="my-3">No donations found</h3>
      ) : null}
    </>
  );
}
