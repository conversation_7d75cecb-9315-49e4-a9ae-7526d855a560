import { Doughnut } from "react-chartjs-2";
import currency from "currency.js";

const CampaignDonutChart = ({ selectedCampaign }) => {
  if (
    selectedCampaign &&
    !Array.isArray(selectedCampaign) &&
    selectedCampaign.hasOwnProperty("name")
  ) {
    const { paid, pledged, goal } = selectedCampaign;

    const paymentShortfall = currency(pledged).subtract(currency(paid)).value;
    const pledgeShortfall =
      currency(goal).subtract(currency(pledged)).value > 0
        ? currency(goal).subtract(currency(pledged)).value
        : 0;
    const paidFormatted = currency(paid).value;

    const data = {
      datasets: [
        {
          data: [paidFormatted, paymentShortfall, pledgeShortfall],
          backgroundColor: ["#4dbd74", "#63c2de", "#ffc107"],
        },
      ],
      labels: [
        "Cleared Payments",
        "Unpaid Pledges",
        "Pledge Shortfall From Goal",
      ],
    };

    return (
      <div>
        <Doughnut data={data} />
      </div>
    );
  }
  return null;
};

export default CampaignDonutChart;
