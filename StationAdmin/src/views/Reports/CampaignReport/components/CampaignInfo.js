import React from "react";
import { startCase } from "lodash";
import currency from "currency.js";
import { CBadge, CProgress, CTable } from "@coreui/react";

const formatMoney = (value) => {
  return currency(value).format();
};

const CampaignInfo = ({ selectedCampaign }) => {
  if (
    selectedCampaign &&
    !Array.isArray(selectedCampaign) &&
    selectedCampaign.hasOwnProperty("name")
  ) {
    const {
      active,
      count,
      end,
      goal,
      id,
      name,
      paid,
      paid_percent,
      pledged,
      pledged_percent,
      start,
      type,
      duration,
      pledge_daily_average,
      pledge_donation_average,
      gift_link,
      gift_title,
      notes,
    } = selectedCampaign;

    const columns = [
      {
        key: "name",
        label: "",
        _props: { scope: "col" },
      },
      {
        key: "value",
        label: "",
        _props: { scope: "col" },
      },
    ];

    const itemCellProps = {
      value: { scope: "row", className: "fw-normal", align: "middle" },
      name: { className: "fw-bolder" },
    };

    const items = [
      {
        name: "Dates",
        value: `${start} - ${end}`,
        _cellProps: itemCellProps,
      },
      {
        name: "Duration",
        value: `${duration} days`,
        _cellProps: itemCellProps,
      },
      {
        name: "ID",
        value: id,
        _cellProps: itemCellProps,
      },
      {
        name: "Type",
        value: startCase(type),
        _cellProps: itemCellProps,
      },
      {
        name: "Target",
        value: formatMoney(goal),
        _cellProps: itemCellProps,
      },
      {
        name: "Pledged",
        value: formatMoney(pledged),
        _cellProps: itemCellProps,
      },
      {
        name: "Pledged (% of target)",
        value: (
          <CProgress value={Math.floor(pledged_percent)} color="success">
            {pledged_percent}%
          </CProgress>
        ),
        _cellProps: itemCellProps,
      },
      {
        name: "Paid",
        value: formatMoney(paid),
        _cellProps: itemCellProps,
      },
      {
        name: "Fullfillment (paid % of pledges)",
        value: (
          <CProgress value={Math.floor(paid_percent)} color="success">
            {paid_percent}%
          </CProgress>
        ),
        _cellProps: itemCellProps,
      },
      {
        name: "Average Day (pledge)",
        value: formatMoney(pledge_daily_average),
        _cellProps: itemCellProps,
      },
      {
        name: "Average Donation",
        value: formatMoney(pledge_donation_average),
        _cellProps: itemCellProps,
      },
      {
        name: "Donations",
        value: count,
        _cellProps: itemCellProps,
      },
      {
        name: "Gift",
        value: <a href={gift_link}>{gift_title}</a>,
        _cellProps: itemCellProps,
      },
    ];

    return (
      <>
        <h2 className="d-flex w-100 justify-content-between">
          {name}
          <CBadge
            color={active ? "success" : "info"}
            className="my-auto"
            size="sm"
          >
            {active ? "Active" : "Inactive"}
          </CBadge>
        </h2>

        <CTable columns={columns} items={items} className="my-4" responsive />

        {notes ? (
          <p className="mx-2">
            <strong>Notes: </strong>
            {notes}
          </p>
        ) : null}
      </>
    );
  }
  return null;
};

export default CampaignInfo;
