// todo:  if needed, this could be made tree-shakeable to optimize
// https://react-chartjs-2.netlify.app/docs/migration-to-v4/#tree-shaking
import "chart.js/auto";
import { Line } from "react-chartjs-2";

const LineChart = ({ campaigns }) => {
  const data = {
    labels: campaigns.map((campaign) => campaign.name),

    datasets: [
      {
        label: "Campaign Target",
        data: campaigns.map((campaign) => campaign.goal),
      },
      {
        label: "Amount Paid",
        data: campaigns.map((campaign) => campaign.paid),
        backgroundColor: "#b8ffd1",
      },
      {
        label: "Amount Pledged",
        data: campaigns.map((campaign) => campaign.pledged),
        backgroundColor: "#a3e7ff",
      },
    ],
  };

  return (
    <div>
      <Line data={data} />
    </div>
  );
};

export default LineChart;
