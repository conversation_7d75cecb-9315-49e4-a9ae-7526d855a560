//todo: important! 2021 doesn't close progress bar. 2020 turns up with $0 totals all across the board
import { CCol, CProgress, CRow } from "@coreui/react";
import { useEffect, useState } from "react";
import { CSVLink } from "react-csv";
import Select, { MultiValue } from "react-select";
import {
  useGetAllShowsQuery,
  useGetCampaignsQuery,
  useGetDonationsQuery,
  useLazyGetCampaignDonationsQuery,
} from "../../../api/kpfa";
import { Campaign, Donation, Show } from "../../../types";
import YearSelector from "./YearSelector";
import { daysAbbreviations, weekdays } from "./constants";
import {
  calculateShowDates,
  createCampaignObject,
  createShowObject,
  filterShowsForDonations,
  formatTime,
  generateUniqueShows,
  processCampaignWeeks,
  processYearlyData,
} from "./revenueUtils";
import ShowTable from "./ShowTable";
import {
  CampaignOption,
  ReportCampaignData,
  ReportShowData,
  ReportShowDonationData,
} from "./types";

export default function Revenue() {
  const [selectedCampaigns, setSelectedCampaigns] = useState<CampaignOption[]>(
    []
  );
  const [campaignOptions, setCampaignOptions] = useState<CampaignOption[]>([]);
  const [showsData, setShowsData] = useState<Show[]>([]);
  const [uniqueShows, setUniqueShows] = useState<ReportShowData[]>([]);
  const [csvData, setCsvData] = useState<string[][]>([]);
  const [reportCampaignData, setReportCampaignData] =
    useState<ReportCampaignData[]>();
  const { data: campaigns } = useGetCampaignsQuery();
  const [getCampaignDonations] = useLazyGetCampaignDonationsQuery();
  const [progress, setProgress] = useState(30);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear()); // Default to current year
  const [selectedShows, setSelectedShows] = useState<CampaignOption[]>([]);
  const [showOptions, setShowOptions] = useState<CampaignOption[]>([]);

  const {
    data: currentYearDonationsData,
    isLoading: dateSummaryIsLoading,
    isFetching: yearSummaryIsLoading,
  } = useGetDonationsQuery({
    // Calculate the start and end dates for the selected year
    start: new Date(selectedYear, 0, 1).toISOString().split("T")[0],
    end: new Date(selectedYear, 11, 31).toISOString().split("T")[0],
  });

  const handleYearChange = (year: number) => {
    setProgress(20); // Initialize progress to indicate loading start
    setSelectedYear(year);
    campaignSelectionHandler([]);
    setProgress(101);
  };

  useEffect(() => {
    const today = new Date(); // Current date

    if (campaigns?.length > 0) {
      const filteredCampaigns = campaigns.filter((campaign: Campaign) => {
        const campaignStartDate = new Date(campaign.start);
        return campaignStartDate <= today; // Only include campaigns that have started
      });

      const campaignArray = filteredCampaigns.map((campaign: Campaign) => ({
        value: campaign.id,
        label: `${campaign.name} (${new Date(
          campaign.start
        ).toLocaleDateString()} - ${new Date(
          campaign.end
        ).toLocaleDateString()})`,
      }));

      setCampaignOptions(campaignArray);
    }
  }, [campaigns]);

  useEffect(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to midnight for date-only comparison

    if (campaigns?.length > 0) {
      const filteredCampaigns = campaigns.filter((campaign: Campaign) => {
        const campaignStartDate = new Date(campaign.start);
        campaignStartDate.setHours(0, 0, 0, 0); // Reset time to midnight for accurate comparison
        return campaignStartDate <= today; // Include campaigns that have started on or before today
      });

      const campaignArray = filteredCampaigns.map((campaign: Campaign) => {
        const campaignStartDate = new Date(campaign.start);
        campaignStartDate.setHours(0, 0, 0, 0); // Reset time to midnight for consistent labeling

        return {
          value: campaign.id,
          label: `${
            campaign.name
          } (${campaignStartDate.toLocaleDateString()} - ${new Date(
            campaign.end
          ).toLocaleDateString()})`,
        };
      });

      setCampaignOptions(campaignArray);
    }
  }, [campaigns]);

  // Fetch all shows
  const { data: shows } = useGetAllShowsQuery();

  useEffect(() => {
    if (shows?.records) {
      setShowsData(shows.records.filter((show) => show.active)); // Existing line to store active shows

      // Adjusted to ensure an array is always passed to setShowOptions
      const options =
        shows.records
          .filter((show) => show.active)
          .map((show) => ({
            value: show.id, // Assuming your Show type has an id field
            label: show.name, // Assuming your Show type has a name field
          })) || []; // Fallback to an empty array if undefined

      setShowOptions(options);
    } else {
      // Explicitly set to an empty array if shows.records is undefined
      setShowOptions([]);
    }
  }, [shows]);

  useEffect(() => {
    // Initialize filteredDonations as an empty array
    let filteredDonations: Donation[] = [];

    // Check if there are selected shows and currentYearDonationsData.records is defined
    if (selectedShows.length > 0 && currentYearDonationsData?.records) {
      filteredDonations = currentYearDonationsData.records.filter((donation) =>
        selectedShows.some(
          (show: CampaignOption) => show.label === donation.show_name
        )
      );
    } else if (currentYearDonationsData?.records) {
      // If no shows are selected but records are defined, use all donations
      filteredDonations = currentYearDonationsData.records;
    } // If neither condition is true, filteredDonations remains an empty array

    if (!selectedCampaigns.length && filteredDonations.length && showsData) {
      setProgress(40);
      processYearlyData(
        filteredDonations, // Use the conditionally filtered donations here
        showsData,
        setUniqueShows,
        setProgress
      );
    }
  }, [currentYearDonationsData, selectedCampaigns, showsData, selectedShows]); // Dependency array is correct

  const campaignSelectionHandler = async (
    selectedOptions: CampaignOption[]
  ) => {
    setSelectedCampaigns(selectedOptions as CampaignOption[]);

    if (selectedOptions.length > 0) {
      setProgress(10);
      const tempReportCampaignData: ReportCampaignData[] = [];

      for (const campaign of selectedOptions) {
        // Find the corresponding campaign data using the campaignOption value
        const campaignDetails = campaigns?.find(
          (c: Campaign) => c.id === campaign.value
        );

        const aggregatedResults: Donation[] = [];

        const result = await getCampaignDonations(campaign.value as number);

        if (result.data?.records) {
          aggregatedResults.push(...result.data.records);
        }

        // Extract all unique show names from the donations
        const showNamesFromDonations: string[] = [
          ...new Set(aggregatedResults.map((donation) => donation.show_name)),
        ];

        // Prepare the objects based on the fetched data
        const filteredShowsData = filterShowsForDonations(
          showsData,
          showNamesFromDonations,
          weekdays
        );

        const tempShowsWithDonations: ReportShowDonationData[] = [];

        for (const show of filteredShowsData) {
          const formattedTime = formatTime(show.starts);
          const showDonations = aggregatedResults.filter(
            (donation) => donation.show_name === show.name
          );
          const showDates = calculateShowDates(
            campaignDetails.start,
            campaignDetails.end,
            show.days,
            showDonations,
            setProgress
          );

          const showObject = createShowObject(
            show,
            showDonations,
            showDates,
            formattedTime
          );
          tempShowsWithDonations.push(showObject);
        }

        setProgress(50);

        const weeks = processCampaignWeeks(campaignDetails);
        const campaignObject = createCampaignObject(
          campaign,
          tempShowsWithDonations,
          weeks,
          campaignDetails
        );

        tempReportCampaignData.push(campaignObject);
      }
      setProgress(70);

      const tempUniqueShows = generateUniqueShows(tempReportCampaignData);

      setProgress(100);

      setUniqueShows(tempUniqueShows);
      setReportCampaignData(tempReportCampaignData);
      setTimeout(() => {
        setProgress(101);
      }, 1500);
    } else {
      setReportCampaignData([]);
    }
  };

  // Intermediary handler function to update state with selected options
  const handleShowSelectionChange = (newValue: MultiValue<CampaignOption>) => {
    setSelectedShows([...newValue]);
  };

  const handleExportCSV = async () => {
    //this is currently very incomplete
    // Initialize the rows for "Show Title", "Start Time", and "Show Days"
    const showTitlesRow = ["Show"];
    const startTimesRow = ["Start Time"];
    const showDaysRow = ["Show Days"];
    const weeksRows: string[][] = [];

    // Iterate through the filtered shows
    reportCampaignData?.forEach((campaign) => {
      campaign.shows.forEach((show) => {
        const days = show.days.split(",").map((day) => daysAbbreviations[day]);

        // Push the values to the rows, expanding them as needed
        days.forEach((day, index) => {
          if (index === 0) {
            showTitlesRow.push(show.name);
            startTimesRow.push(show.starts);
          } else {
            showTitlesRow.push("");
            startTimesRow.push("");
          }
          showDaysRow.push(day);
        });
      });
    });

    reportCampaignData?.forEach((campaign) => {
      // Create a row with the formatted date and append it to weeksRows
      campaign.weeks.forEach((startDate) => {
        weeksRows.push([startDate]);
      });

      weeksRows.push([`${campaign.name} Totals by Day of Week`]);
    });

    // Update the CSV data
    setCsvData([showTitlesRow, startTimesRow, showDaysRow, ...weeksRows]);
  };

  return (
    <>
      <CRow>
        <CCol>
          <h1>Revenue Report</h1>
        </CCol>
        <CCol className="d-flex align-items-center">
          {progress > 0 &&
          progress < 101 &&
          (selectedCampaigns.length ||
            dateSummaryIsLoading ||
            yearSummaryIsLoading ||
            uniqueShows[0]?.total) ? (
            <CProgress
              value={progress}
              variant="striped"
              animated
              color="success"
              className="mx-auto w-50"
              progressBarClassName="text-white overflow-visible px-2"
            >
              Crunching Numbers...
            </CProgress>
          ) : null}
        </CCol>
      </CRow>
      <div className="row mt-5">
        <div className="d-flex flex-column justify-content-start w-100">
          {campaignOptions.length > 0 ? (
            <>
              <div className="row">
                <div className="col-sm-4">
                  <h5>Select Campaign</h5>
                  <Select
                    isMulti
                    options={campaignOptions}
                    onChange={(selectedOptions) =>
                      campaignSelectionHandler(
                        selectedOptions as CampaignOption[]
                      )
                    }
                    value={selectedCampaigns}
                  />
                </div>
                <div className="col-sm-4">
                  <h5>Select Year</h5>
                  <YearSelector onChange={handleYearChange} />
                </div>
                <div className="col-sm-4">
                  <h5>Select Show</h5>
                  <Select
                    isMulti
                    name="shows"
                    options={showOptions}
                    className="basic-multi-select"
                    classNamePrefix="select"
                    onChange={handleShowSelectionChange} // Update selectedShows state on change
                    placeholder="Select Shows..."
                  />
                </div>
              </div>
              <CSVLink
                data={csvData} // CSV data
                filename={"shows.csv"}
                className="d-none btn btn-primary mt-4 me-auto"
                onClick={handleExportCSV}
              >
                Export CSV
              </CSVLink>
              {reportCampaignData || uniqueShows ? (
                reportCampaignData?.length ? (
                  <ShowTable
                    data={reportCampaignData}
                    view={"campaign"}
                    uniqueShows={uniqueShows}
                    selectedYear={selectedYear}
                    selectedShows={selectedShows}
                  />
                ) : (
                  <ShowTable
                    view="summary"
                    uniqueShows={uniqueShows}
                    selectedYear={selectedYear}
                    selectedShows={selectedShows}
                  />
                )
              ) : null}
            </>
          ) : (
            "Loading Campaign Data..."
          )}
        </div>
      </div>
    </>
  );
}
