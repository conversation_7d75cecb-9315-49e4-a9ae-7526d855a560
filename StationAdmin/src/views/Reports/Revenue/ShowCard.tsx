import {
  CCard,
  CCardBody,
  CTable,
  CTableHead,
  CTableHeaderCell,
  CTableBody,
  CTableRow,
  CListGroup,
  CListGroupItem,
  CTableDataCell,
} from "@coreui/react";
import { moneyFormat } from "../../../helpers";
import { ReportShowData, ReportCampaignData } from "./types";

interface ShowCardProps {
  show: ReportShowData;
  reportCampaignData?: ReportCampaignData[];
}

const ShowCard: React.FC<ShowCardProps> = ({ show, reportCampaignData }) => {
  const dayOfWeek: { [key: string]: number } = {
    Monday: 0,
    Tuesday: 1,
    Wednesday: 2,
    Thursday: 3,
    Friday: 4,
    Saturday: 5,
    Sunday: 6,
  };

  return (
    <CCard className="shadow">
      <CCardBody>
        <h5>{show.name}</h5>
        <div className="text-muted">
          <h6>{show.starts}</h6>
        </div>
        <CListGroup className="my-4">
          <CListGroupItem>
            Total Donation Amount: {moneyFormat(show.total)}
          </CListGroupItem>
          <CListGroupItem>
            Average Per Episode: {moneyFormat(show.average)}
          </CListGroupItem>
        </CListGroup>
        {reportCampaignData &&
          reportCampaignData.map((campaign: ReportCampaignData) => (
            <div key={campaign.id} className="mt-5">
              <h6 className="mb-3">
                <strong>{campaign.name}</strong>
              </h6>
              <CTable responsive striped hover>
                <CTableHead>
                  <CTableRow>
                    <CTableHeaderCell scope="col" className="text-muted">
                      Week Starting
                    </CTableHeaderCell>
                    {show.days.split(",").map((day, dayIndex) => (
                      <CTableHeaderCell
                        scope="col"
                        key={dayIndex}
                        className="text-muted"
                      >
                        {day.trim()}
                      </CTableHeaderCell>
                    ))}
                  </CTableRow>
                </CTableHead>
                <CTableBody>
                  {campaign.weeks.map((week, weekIndex) => (
                    <CTableRow key={weekIndex}>
                      <CTableHeaderCell scope="row" className="text-muted">
                        {week}
                      </CTableHeaderCell>

                      {show.days.split(",").map((day) => {
                        // Convert week start date string to Date object
                        const [month, dayOfMonth, year] = week
                          .split("/")
                          .map(Number);
                        // Adjust the year to include the full year format
                        const fullYear = year < 100 ? year + 2000 : year;
                        const startDate = new Date(
                          fullYear,
                          month - 1,
                          dayOfMonth
                        );

                        // Adjust the start date to the first day of the week (Monday)
                        // Assuming the startDate is already a Monday
                        const startDayOfWeek = startDate.getDay();
                        const correctStartDay =
                          startDayOfWeek === 0 ? 6 : startDayOfWeek - 1; // Convert Sunday index from 0 to 6, shift other days by 1

                        // Use a type assertion to tell TypeScript that day.trim() is definitely a key of dayOfWeek
                        const targetDayIndex =
                          dayOfWeek[day.trim() as keyof typeof dayOfWeek];

                        // Calculate the difference in days between the start day of the week (Monday) and the target day
                        let dateDifference = targetDayIndex - correctStartDay;
                        // If the target day is before the start of the week (Monday), adjust the difference by adding 7
                        if (dateDifference < 0) {
                          dateDifference += 7;
                        }

                        // Apply the difference to the start date to get the full date for the target day
                        startDate.setDate(startDate.getDate() + dateDifference);

                        // Format the date to match the keys in showDates
                        const fullDate = `${
                          startDate.getMonth() + 1
                        }/${startDate.getDate()}/${
                          startDate.getFullYear() % 100
                        }`;

                        // Ensure startDate is compared without the time component
                        let comparisonDate = new Date(
                          startDate.getFullYear(),
                          startDate.getMonth(),
                          startDate.getDate()
                        );

                        // Check if the date is within the campaign period
                        const campaignStart = new Date(campaign.start);
                        const campaignEnd = new Date(campaign.end);
                        const isDateInCampaign =
                          comparisonDate >= campaignStart &&
                          comparisonDate <= campaignEnd;

                        const donationSum = isDateInCampaign
                          ? campaign.shows.find(
                              (unique) => unique.id === show.id
                            )?.dates[fullDate] || 0
                          : null;

                        return (
                          <CTableDataCell key={`${day}-${week}`}>
                            {" "}
                            {donationSum !== null
                              ? moneyFormat(donationSum)
                              : "—"}
                          </CTableDataCell>
                        );
                      })}
                    </CTableRow>
                  ))}
                </CTableBody>
              </CTable>
            </div>
          ))}
      </CCardBody>
    </CCard>
  );
};

export default ShowCard;
