import { CCol, CRow } from "@coreui/react";
import ShowCard from "./ShowCard";
import { CampaignOption, ReportCampaignData, ReportShowData } from "./types";

type ViewType = "campaign" | "summary";

interface ShowTableProps {
  data?: ReportCampaignData[];
  view: ViewType;
  uniqueShows: ReportShowData[];
  selectedYear: number;
  selectedShows: CampaignOption[];
}

const ShowTable = ({
  data,
  view,
  uniqueShows,
  selectedYear,
  selectedShows,
}: ShowTableProps) => {
  // Filter uniqueShows if selectedShows is provided and not empty
  const showsToDisplay =
    selectedShows?.length > 0
      ? uniqueShows.filter((show) =>
          selectedShows.some((selectedShow) => selectedShow.value === show.id)
        )
      : uniqueShows;

  return (
    <div className="mt-5 px-0 w-100">
      <CRow>
        {view === "summary" ? <h3>{selectedYear} Summary</h3> : null}
        {showsToDisplay.map(
          (
            show // Use showsToDisplay here
          ) => (
            <CCol xs="12" lg="6" xxl="4" key={show.id} className="mb-3">
              {data ? (
                <ShowCard show={show} reportCampaignData={data} />
              ) : (
                <ShowCard show={show} />
              )}
            </CCol>
          )
        )}
      </CRow>
    </div>
  );
};

export default ShowTable;
