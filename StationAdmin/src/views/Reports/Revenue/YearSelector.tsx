import React, { useEffect, useState } from "react";
import Select from "react-select";

// Define the type for the select option
interface YearOption {
  value: number;
  label: string;
}

interface YearSelectorProps {
  onChange: (year: number) => void; // Callback function to handle year change
}

const YearSelector: React.FC<YearSelectorProps> = ({ onChange }) => {
  const [selectedYear, setSelectedYear] = useState<number>(
    new Date().getFullYear()
  );
  const [yearOptions, setYearOptions] = useState<YearOption[]>([]);

  useEffect(() => {
    const currentYear = new Date().getFullYear();
    const options: YearOption[] = [];
    for (let year = 2018; year <= currentYear; year++) {
      options.push({ value: year, label: `${year}` });
    }
    setYearOptions(options);
  }, []);

  const handleYearChange = (selectedOption: YearOption | null) => {
    if (selectedOption) {
      setSelectedYear(selectedOption.value);
      onChange(selectedOption.value); // Call the onChange prop with the new year
    }
  };

  return (
    <Select
      options={yearOptions}
      onChange={handleYearChange}
      value={yearOptions.find((option) => option.value === selectedYear)}
      getOptionLabel={(option) => option.label}
      getOptionValue={(option) => option.value.toString()}
    />
  );
};

export default YearSelector;
