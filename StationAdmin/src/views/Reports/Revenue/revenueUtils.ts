import { Campaign, Donation, Show } from "../../../types";
import { daysOfWeek } from "./constants";
import {
  CampaignOption,
  ReportShowDonationData,
  ShowDates,
  ReportCampaignData,
  ReportShowData,
} from "./types";

export const formatTime = (startTime: string) => {
  const [hours24, minutes] = startTime.split(":");
  const hours = Number(hours24);
  const ampm = hours >= 12 ? "PM" : "AM";
  const formattedHours = hours % 12 || 12;
  return `${formattedHours}:${minutes} ${ampm}`;
};

export const calculateTotalAndAverage = (showDates: ShowDates) => {
  const total = Object.values(showDates).reduce((sum, value) => sum + value, 0);
  const average = Math.floor(total / Object.keys(showDates).length);
  return { total, average };
};

export const createShowObject = (
  show: Show,
  showDonations: Donation[],
  showDates: ShowDates,
  formattedTime: string
) => {
  // function body
  const { total, average } = calculateTotalAndAverage(showDates);
  return {
    name: show.name,
    id: show.id,
    donations: showDonations,
    days: show.days,
    starts: formattedTime,
    dates: showDates,
    total: total,
    average: average,
  };
};

export const calculateShowDates = (
  campaignStartDate: string,
  campaignEndDate: string,
  showDays: string,
  showDonations: Donation[],
  setProgress: (value: number) => void
): ShowDates => {
  setProgress(60);
  const startDate = new Date(campaignStartDate);
  const endDate = new Date(campaignEndDate);

  const weekdays = showDays
    .trim()
    .split(",")
    .map((day) => day.trim());

  const dates: ShowDates = {};

  weekdays.forEach((day) => {
    const dayIndex = daysOfWeek.indexOf(day);
    const date = new Date(startDate);

    while (date.getDay() !== dayIndex) {
      date.setDate(date.getDate() + 1);
    }

    while (date <= endDate) {
      const formattedDate = `${date.getMonth() + 1}/${date.getDate()}/${
        date.getFullYear() % 100
      }`;
      // Calculate the sum of donations for this date
      const sum = showDonations.reduce((total, donation) => {
        const donationDate = new Date(donation.timestamp);
        if (
          donationDate.getDate() === date.getDate() &&
          donationDate.getMonth() === date.getMonth() &&
          donationDate.getFullYear() === date.getFullYear()
        ) {
          // Multiply the donation amount by 12 if it's a monthly donation
          const adjustedAmount =
            donation.installment === "Monthly"
              ? donation.amount * 12
              : donation.amount;
          return total + adjustedAmount;
        }
        return total;
      }, 0);
      dates[formattedDate] = sum;
      date.setDate(date.getDate() + 7); // Next occurrence of the day
    }
  });
  return dates;
};
export const filterShowsForDonations = (
  shows: Show[],
  showNamesFromDonations: string[],
  weekdays: string[]
) => {
  return shows.filter((show) => {
    // Check if the show name is included in the donations
    const isIncludedInDonations = showNamesFromDonations.includes(show.name);
    // Check if the show occurs on at least one weekday
    const occursOnWeekday = weekdays.some((day) => show.days.includes(day));
    return isIncludedInDonations && occursOnWeekday;
  });
};

export const processCampaignWeeks = (campaignDetails: Campaign): string[] => {
  const weeks: string[] = [];

  if (campaignDetails) {
    const startDate = new Date(campaignDetails.start);
    const endDate = new Date(campaignDetails.end);

    // Adjust startDate to the Monday of the first week
    const dayOfWeek = startDate.getDay();
    const difference = (dayOfWeek + 6) % 7;
    startDate.setDate(startDate.getDate() - difference);

    let currentMonday = new Date(startDate);

    while (currentMonday <= endDate) {
      const formattedDate = `${
        currentMonday.getMonth() + 1
      }/${currentMonday.getDate()}/${currentMonday.getFullYear() % 100}`;
      weeks.push(formattedDate);

      // Move to the next Monday
      currentMonday = new Date(
        currentMonday.setDate(currentMonday.getDate() + 7)
      );
    }
  }

  return weeks; // Ensure the function always returns an array
};

export const createCampaignObject = (
  campaign: CampaignOption,
  tempShowsWithDonations: ReportShowDonationData[],
  weeks: string[],
  campaignDetails: Campaign
) => {
  return {
    id: campaign.value,
    name: campaign.label,
    shows: tempShowsWithDonations,
    weeks: weeks,
    start: campaignDetails.start,
    end: campaignDetails.end,
  };
};

export const generateUniqueShows = (
  tempReportCampaignData: ReportCampaignData[]
) => {
  return Array.from(
    tempReportCampaignData
      .reduce((map, campaign) => {
        campaign.shows.forEach((show) => {
          const { donations, ...showWithoutDonations } = show;
          map.set(show.id, showWithoutDonations);
        });
        return map;
      }, new Map())
      .values()
  );
};

export const processYearlyData = (
  currentYearDonationsData: Donation[],
  showsData: Show[],
  setUniqueShows: (value: ReportShowData[]) => void,
  setProgress: (value: number) => void
) => {
  setProgress(50);
  const yearlyShowsSummary: ReportShowData[] = [];

  for (const show of showsData) {
    const showDonations = currentYearDonationsData.filter(
      (donation) => donation.show_name === show.name
      //todo: eventually migrate this to use ID for more accuracy - we only started tagging donations with show_id in mid-2023 so 2024 may be the time to start this.
    );

    const showTotal = showDonations.reduce(
      (sum, donation) => sum + donation.amount,
      0
    );
    const showAverage = Math.floor(
      showDonations.length ? showTotal / showDonations.length : 0
    );

    const { starts, ...otherShowProps } = show;

    const showObject: ReportShowData = {
      ...otherShowProps, // Spreads the existing properties of the show
      starts: formatTime(starts),
      total: showTotal,
      average: showAverage,
    };
    yearlyShowsSummary.push(showObject);
    setProgress(60);
  }
  setProgress(70);
  setUniqueShows(yearlyShowsSummary);
};
