import { Donation } from "../../../types";

export interface CampaignOption {
  value: number | string;
  label: string;
}

export interface ReportCampaignData {
  name: string;
  id: number | string;
  shows: ReportShowDonationData[];
  weeks: string[];
  start: string;
  end: string;
}

export interface ReportShowData {
  name: string;
  id: number;
  days: string;
  starts: string;
  total: number;
  average: number;
}

export interface ShowDates {
  [key: string]: number;
}

export interface ReportShowDonationData extends ReportShowData {
  donations: Donation[];
  dates: ShowDates;
}
