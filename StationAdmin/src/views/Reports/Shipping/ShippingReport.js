import { Component } from "react";
import moment from "moment";
import { cloneDeep } from "lodash";
import { CButton } from "@coreui/react";
import TableHeader from "./components/TableHeader";
import ResultsPagination from "../../../components/ResultsPagination";
import DisplayResults from "./components/DisplayResults";
import Spinner from "./components/Spinner";
import Modals from "./components/Modals";
import {
  updateShippingStatus,
  searchPremiums,
  updateSelect,
  resultsPropertyNames,
  shippingStatus,
  shippingStatusOptions,
  shippingStatusUpdateOptions,
} from "../constants";
import { connect } from "react-redux";
import Select from "react-select";
import { resultsPerPageOptions } from "../../Form/constants";
import useSortResultsArray from "../../Hooks/useSortResultsArray";

const today = moment().format("YYYY-MM-DD");
const dateMin = "1990-01-01";

class ShippingReport extends Component {
  constructor() {
    super();

    this.state = {
      loading: false,
      resultsPerPage: 10,
      currentPage: 1,
      totalPages: 1,
      results: [],
      sortedResults: [],
      startDate: "",
      endDate: today,
      showConfirmationModal: false,
      showProcessingModal: false,
      showFailureModal: false,
      showSuccessModal: false,
      shippingStatusSelection: shippingStatus.new,
      selectedRows: [],
      shippingStatusUpdateSelection: "",
      sortOrderAscending: false,
      sortColumn: resultsPropertyNames.count,
    };
  }

  handleDateSelect = (event) => {
    this.setState(
      {
        loading: true,
        results: [],
        sortedResults: [],
        selectedRows: [],
        [event.target.name]: event.target.value,
      },
      () => {
        this.getPremiumsList();
      }
    );
  };

  handleStatusSelection = (event) => {
    this.setState(
      { shippingStatusSelection: event.value, selectedRows: [] },
      () => {
        this.getPremiumsList();
      }
    );
  };

  handleStatusUpdateSelection = (event) => {
    this.setState({ shippingStatusUpdateSelection: event.value });
  };

  getPremiumsList = () => {
    if (this.state.startDate && this.state.endDate) {
      if (
        moment(this.state.startDate).isAfter(dateMin) &&
        moment(this.state.endDate).isAfter(dateMin) &&
        (moment(this.state.startDate).isBefore(moment(this.state.endDate)) ||
          moment(this.state.startDate).isSame(moment(this.state.endDate)))
      ) {
        const headers = {
          Authorization: `Bearer ${localStorage.getItem("jwt")}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        };
        fetch(
          `https://${
            this.props.testMode.value ? this.props.testMode.route : ""
          }api.kpfa.org/shipping/?start=${this.state.startDate}&end=${
            this.state.endDate
          }&status=${this.state.shippingStatusSelection}`,
          {
            headers,
            method: "GET",
          }
        )
          .then((response) => response.json())
          .then((response) => {
            if (response.records) {
              const totalPages = Math.ceil(
                response.records.length / this.state.resultsPerPage
              );
              const results = response.records;
              const sortedResults = useSortResultsArray(
                results,
                this.state.sortColumn,
                this.state.sortOrderAscending
              );
              this.setState({
                results,
                sortedResults,
                currentPage: 1,
                totalPages,
                loading: false,
              });
            }
            if (!response.records) {
              this.setState({
                results: [],
                sortedResults: [],
                totalPages: 1,
                currentPage: 1,
                loading: false,
              });
            }
          })
          .catch((error) => {
            console.log(error);
          });
      }
      if (moment(this.state.startDate).isAfter(moment(this.state.endDate))) {
        this.setState({
          results: [],
          sortedResults: [],
          totalPages: 1,
          currentPage: 1,
          loading: false,
        });
      }
    }
    if (
      moment(this.state.startDate).isBefore(dateMin) ||
      moment(this.state.endDate).isBefore(dateMin) ||
      !this.state.startDate ||
      !this.state.endDate
    ) {
      this.setState({
        results: [],
        sortedResults: [],
        totalPages: 1,
        currentPage: 1,
        loading: false,
      });
    }
  };

  handleResultsPerPage = (event) => {
    const resultsPerPage = parseInt(event.value);
    const totalPages = Math.ceil(this.state.results.length / resultsPerPage);
    const currentPage =
      this.state.currentPage > totalPages ? totalPages : this.state.currentPage;
    this.setState({ resultsPerPage, totalPages, currentPage });
  };

  handlePagination = (e) => {
    const paginationChange = parseInt(e.target.dataset.increment);
    this.setState({ currentPage: this.state.currentPage + paginationChange });
  };

  handlePremiumsRowInput = (event) => {
    const selectedRows = [...this.state.selectedRows];
    const rowIdValue = parseInt(event.target.value);
    if (event.target.checked) {
      selectedRows.push(rowIdValue);
      this.setState({ selectedRows });
      return;
    } else {
      const indexToDelete = selectedRows.indexOf(rowIdValue);
      if (indexToDelete !== -1) {
        selectedRows.splice(indexToDelete, 1);
        this.setState({ selectedRows });
        return;
      }
    }
  };

  handleChangeSort = (event) => {
    const results = cloneDeep(this.state.results);
    if (this.state.sortColumn === event.currentTarget.id) {
      this.setState(
        { sortOrderAscending: !this.state.sortOrderAscending },
        () => {
          const sortedResults = useSortResultsArray(
            results,
            this.state.sortColumn,
            this.state.sortOrderAscending
          );
          this.setState({ sortedResults });
        }
      );
    }
    if (this.state.sortColumn !== event.currentTarget.id) {
      this.setState({ sortColumn: event.currentTarget.id }, () => {
        const sortedResults = useSortResultsArray(
          results,
          this.state.sortColumn,
          this.state.sortOrderAscending
        );
        this.setState({ sortedResults });
      });
    }
  };

  closeModal = () => {
    this.setState({
      showConfirmationModal: false,
      showProcessingModal: false,
      showFailureModal: false,
      showSuccessModal: false,
    });
  };

  closeSuccessModal = () => {
    this.setState({
      showConfirmationModal: false,
      showProcessingModal: false,
      showFailureModal: false,
      showSuccessModal: false,
      results: [],
      sortedResults: [],
      selectedRows: [],
      totalPages: 1,
      currentPage: 1,
      startDate: "",
    });
  };

  handleShippingUpdateButton = () => {
    this.setState({
      showConfirmationModal: true,
      showProcessingModal: false,
      showFailureModal: false,
      showSuccessModal: false,
    });
  };

  confirmShippingUpdate = () => {
    this.setState({
      showProcessingModal: true,
      showConfirmationModal: false,
      showFailureModal: false,
      showSuccessModal: false,
    });
  };

  displayFailureModal = () => {
    this.setState({
      showFailureModal: true,
      formSubmitted: true,
      showConfirmationModal: false,
      showProcessingModal: false,
      showSuccessModal: false,
    });
  };

  displaySuccessModal = () => {
    this.setState({
      showSuccessModal: true,
      formSubmitted: true,
      showConfirmationModal: false,
      showProcessingModal: false,
      showFailureModal: false,
    });
  };

  render() {
    return (
      <div className="animated fadeIn">
        <div className="card">
          <div className="card-body">
            <div className="row">
              <div className="col-sm-12">
                <h5>{searchPremiums}</h5>
              </div>
            </div>
            <div className="row">
              <div className="col-sm-12 col-md-6 col-lg-3 form-group mb-2">
                <label htmlFor="start">Start date</label>

                <input
                  className="form-control me-2"
                  type="date"
                  name="startDate"
                  min={dateMin}
                  max={today}
                  onChange={this.handleDateSelect}
                  value={this.state.startDate}
                />
              </div>
              <div className="col-sm-12 col-md-6 col-lg-3 form-group mb-2">
                <label htmlFor="start">End date</label>

                <input
                  className="form-control me-2"
                  type="date"
                  name="endDate"
                  min={dateMin}
                  max={today}
                  onChange={this.handleDateSelect}
                  value={this.state.endDate}
                />
              </div>
              <div className="col-sm-12 col-md-6 col-lg-3 form-group mb-2">
                <label htmlFor="shippingStatusSearch">Status</label>
                <Select
                  id="shippingStatusSearch"
                  onChange={this.handleStatusSelection}
                  options={shippingStatusOptions}
                  defaultValue={shippingStatusOptions[0]}
                />
              </div>
              <div className="col-sm-12 col-md-6 col-lg-3 form-group mb-2">
                <label htmlFor="resultsPerPage">Results Per Page</label>
                <Select
                  id="resultsPerPage"
                  onChange={this.handleResultsPerPage}
                  options={resultsPerPageOptions}
                  defaultValue={resultsPerPageOptions[0]}
                />
              </div>
            </div>
            <div className="row mt-3">
              <div className="col-sm-6">
                {this.state.results.length > 0 ? (
                  <h6>{this.state.results.length + " Premiums Found"}</h6>
                ) : null}
              </div>
              <div className="col-sm-6 text-right">
                {this.state.results.length > 0 ? (
                  <h6>
                    {"page " +
                      this.state.currentPage +
                      " of " +
                      this.state.totalPages}
                  </h6>
                ) : null}
              </div>
            </div>
            <hr />
            <div className="row mb-3">
              <div className="col-sm-12 col-md-6 col-lg-6 d-flex flex-row align-items-center justify-content-between mb-2">
                {this.state.selectedRows.length > 0 ? (
                  <>
                    <span>
                      <strong>{this.state.selectedRows.length}</strong> Premiums
                      Selected
                    </span>
                  </>
                ) : null}
              </div>

              <div className="col-sm-12 col-md-6 col-lg-6 d-flex flex-row justify-content-end align-items-center mb-2">
                <div className="form-group row w-100 mb-0 me-2 align-items-center">
                  <label
                    className="col-sm-6 col-form-label text-right"
                    htmlFor="shippingStatusUpdate"
                  >
                    {updateSelect}
                  </label>
                  <div className="col-sm-6">
                    <Select
                      id="shippingStatusUpdate"
                      onChange={this.handleStatusUpdateSelection}
                      options={shippingStatusUpdateOptions}
                    />
                  </div>
                </div>
                <CButton
                  color="primary"
                  disabled={
                    this.state.selectedRows.length > 0 &&
                    this.state.shippingStatusUpdateSelection !== ""
                      ? false
                      : true
                  }
                  onClick={this.handleShippingUpdateButton}
                >
                  {updateShippingStatus}
                </CButton>
              </div>
            </div>
            <TableHeader
              handleChangeSort={this.handleChangeSort}
              sortOrderAscending={this.state.sortOrderAscending}
              sortColumn={this.state.sortColumn}
            />
            <Spinner loading={this.state.loading} />
            <DisplayResults
              sortedResults={this.state.sortedResults}
              currentPage={this.state.currentPage}
              totalPages={this.state.totalPages}
              resultsPerPage={this.state.resultsPerPage}
              loading={this.state.loading}
              startDate={this.state.startDate}
              endDate={this.state.endDate}
              handlePremiumsRowInput={this.handlePremiumsRowInput}
              selectedRows={this.state.selectedRows}
            />
            <ResultsPagination
              currentPage={this.state.currentPage}
              totalPages={this.state.totalPages}
              handlePagination={this.handlePagination}
            />
          </div>
        </div>
        <Modals
          testMode={this.state.testMode}
          results={this.state.results}
          showConfirmationModal={this.state.showConfirmationModal}
          showProcessingModal={this.state.showProcessingModal}
          showFailureModal={this.state.showFailureModal}
          showSuccessModal={this.state.showSuccessModal}
          closeModal={this.closeModal}
          closeSuccessModal={this.closeSuccessModal}
          displayFailureModal={this.displayFailureModal}
          displaySuccessModal={this.displaySuccessModal}
          handleShippingUpdateButton={this.handleShippingUpdateButton}
          confirmShippingUpdate={this.confirmShippingUpdate}
          selectedRows={this.state.selectedRows}
          shippingStatusUpdateSelection={
            this.state.shippingStatusUpdateSelection
          }
        />
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return { testMode: state.testMode };
};

export default connect(mapStateToProps)(ShippingReport);
