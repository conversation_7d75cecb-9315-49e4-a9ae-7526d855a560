import React from "react";
import { cloneDeep } from "lodash";
import { CSVLink } from "react-csv";
import moment from "moment";
import { shippingStatus } from "../../constants";
import { CBadge } from "@coreui/react";
import { csvHeaders } from "../constants";

const DisplayResults = ({
  sortedResults,
  currentPage,
  resultsPerPage,
  handlePremiumsRowInput,
  loading,
  selectedRows,
  startDate,
  endDate,
}) => {
  const selectedPremiumsData = [];

  if (sortedResults.length > 0) {
    const results = cloneDeep(sortedResults);
    const startIndex = (currentPage - 1) * resultsPerPage;
    const resultsSlice = results.slice(startIndex, startIndex + resultsPerPage);

    const tableRows = resultsSlice.map((premium, index) => {
      const getBadgeClassName = () => {
        if (premium.status === shippingStatus.new) {
          return "dark";
        }
        if (premium.status === shippingStatus.shipped) {
          return "success";
        }
        if (premium.status === shippingStatus.onHold) {
          return "warning";
        }
        if (premium.status === shippingStatus.canceled) {
          return "danger";
        }
      };
      const getRowClassName = () => {
        if (selectedRows.includes(premium.premium_id)) {
          return "row mt-2 mb-2 align-items-center pt-1 pb-1 bg-info";
        }
        return "row mt-2 mb-2 align-items-center pt-1 pb-1";
      };
      const isChecked = (id) => {
        if (selectedRows.includes(id)) {
          selectedPremiumsData.push(
            ...premium.donors.map((donor) => ({
              ...donor,
              fullName: `${donor.shipping_firstname} ${donor.shipping_lastname}`,
              premium_id: premium.premium_id,
              premiumName: premium.name,
              vendorCode: premium.vendor_code,
              count: 1,
            }))
          );
          return true;
        }
        return false;
      };

      const data = cloneDeep(premium.donors);
      const rowData = data.map((donor) => {
        donor.fullName =
          `${donor.shipping_firstname} ${donor.shipping_lastname}`.replace(
            /."/g,
            '""'
          );
        donor.premium_id = premium.premium_id;
        donor.premiumName = premium.name.replace(/."/g, '""');
        donor.vendorCode = premium.vendor_code;
        donor.count = 1;

        return donor;
      });

      return (
        <div className={getRowClassName()} key={"row" + index}>
          <div className="col-sm-3">
            <span
              style={{
                display: "block",
                width: "100%",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              <input
                className="me-2"
                type="checkbox"
                value={premium.premium_id}
                onChange={handlePremiumsRowInput}
                checked={isChecked(premium.premium_id)}
              />
              {premium.name}
            </span>
          </div>
          <div className="col-sm-1">
            <span>{premium.premium_id}</span>
          </div>
          <div className="col-sm-1">
            <span className="">{premium.count}</span>
          </div>
          <div className="col-sm-2">
            <span>{premium.company}</span>
          </div>
          <div className="col-sm-2">
            <span>{premium.category_name}</span>
          </div>
          <div className="col-sm-1">
            <h4>
              <CBadge color={getBadgeClassName()}>{premium.status}</CBadge>
            </h4>
          </div>
          <div className="col-sm-2">
            <CSVLink
              data={rowData}
              headers={csvHeaders}
              className={
                premium.donors.length > 0 && startDate && endDate
                  ? "btn btn-secondary"
                  : "btn btn-secondary disabled"
              }
              filename={`shipping_report_id${
                premium.premium_id
              }_fr_${startDate}_to_${endDate}_saved_${moment().format(
                "YYYY-MM-DD_HH-mm"
              )}.csv`}
            >
              CSV
            </CSVLink>
          </div>
        </div>
      );
    });

    return (
      <>
        {tableRows}
        {selectedRows.length > 1 && (
          <div className="row mt-4">
            <div className="col-sm-12 text-right">
              <CSVLink
                data={selectedPremiumsData}
                headers={csvHeaders}
                className="btn btn-primary"
                filename={`aggregated_shipping_report_${moment().format(
                  "YYYY-MM-DD_HH-mm"
                )}.csv`}
              >
                Download Selected as CSV
              </CSVLink>
            </div>
          </div>
        )}
      </>
    );
  }
  if (!sortedResults.length && loading === false) {
    return (
      <div className="row">
        <div className="col-sm-12 mt-2">
          <h4 className="mb-4 mt-4">No Results to Display</h4>
          {moment(startDate).isAfter(moment(endDate)) ? (
            <div className="alert alert-danger" role="alert">
              Invalid date range selected.
            </div>
          ) : null}
        </div>
      </div>
    );
  }
  return null;
};

export default DisplayResults;
