import React from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CCollapse,
  CCardBody,
  CCard,
} from "@coreui/react";
import { cloneDeep } from "lodash";

import { connect } from "react-redux";

class Modals extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      response: null,
      responseContent: null,
      detailedResponse: [],
      collapse: false,
    };
  }

  toggle = () => {
    const collapse = !this.state.collapse;
    this.setState({ collapse });
  };

  getSelectedRowData = () => {
    const selectedRows = [...this.props.selectedRows];
    const results = cloneDeep(this.props.results);
    const data = selectedRows.map((premiumId) => {
      const selectedRowData = results.filter((premium) => {
        if (premium.premium_id === premiumId) {
          return premium;
        }
        return null;
      });
      return selectedRowData[0];
    });
    return data;
  };

  getTransactionIds = (data) => {
    const transactionIds = [];
    data.forEach((premium) => {
      premium.donors.forEach((donor) => {
        transactionIds.push(donor.shipment_id);
      });
    });
    this.transactionIds = [...transactionIds];
    // console.log("this.transactionIds", this.transactionIds);
    return transactionIds;
  };

  showDetailedResults = () => {
    const detailedResponse = [...this.state.detailedResponse];
    const detailedResponseMarkup = detailedResponse.map(
      (transaction, index) => {
        // console.log("transaction", transaction);
        return (
          <span key={"p" + index}>
            {`${transaction.shipment_id}: ${transaction.status},   `}{" "}
          </span>
        );
      }
    );
    return detailedResponseMarkup;
  };

  transactionIds = [];

  url = `https://${
    this.props.testMode.value ? this.props.testMode.route : ""
  }api.kpfa.org/shipping`;

  confirmationModal = () => {
    if (this.props.showConfirmationModal === true) {
      const selectedRowsData = this.getSelectedRowData();
      // console.log("selectedRowsData", selectedRowsData);
      const transactionIds = this.getTransactionIds(selectedRowsData);
      return (
        <CModal
          visible={true}
          onDismiss={this.props.closeModal}
          // className={this.props.className}
          backdrop="static"
          keyboard={false}
        >
          <CModalHeader onDismiss={this.props.closeModal}>
            Please confirm the premiums to be updated
          </CModalHeader>
          <CModalBody>
            <h4 className="mb-3">
              {this.props.selectedRows.length} Premiums Selected{" "}
              {transactionIds.length > 0
                ? `(${transactionIds.length} transactions)`
                : "&nbsp;"}
            </h4>
            <div>
              {selectedRowsData.map((premium, index) => {
                return (
                  <div className="row" key={"n" + index}>
                    <div className="col-sm-12 mb-2">{`${premium.name} (${premium.premium_id})`}</div>
                  </div>
                );
              })}
            </div>
          </CModalBody>
          <CModalFooter>
            <CButton color="secondary" onClick={this.props.closeModal}>
              Cancel
            </CButton>
            <CButton color="primary" onClick={this.props.confirmShippingUpdate}>
              Confirm Changes
            </CButton>
          </CModalFooter>
        </CModal>
      );
    }
    return null;
  };

  processingModal = () => {
    if (this.props.showProcessingModal === true) {
      const headers = {
        Authorization: `Bearer ${localStorage.getItem("jwt")}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };
      const requestBody = {
        status: this.props.shippingStatusUpdateSelection,
        shipment_ids: this.transactionIds,
      };

      // console.log("requestBody", requestBody);

      fetch(this.url, {
        method: "PUT", // or 'PUT'
        body: JSON.stringify(requestBody),
        headers,
      })
        .then((response) => response.json())
        .then((response) => {
          // console.log("RESPONSE", response);
          if (response.status === "error") {
            this.props.displayFailureModal();
            this.setState({
              response: response.status,
              responseContent: response.message,
              detailedResponse: [],
            });
          }
          if (response.status === "success") {
            this.props.displaySuccessModal();
            this.setState({
              response: response.status,
              responseContent: response.message,
              detailedResponse: response.records,
            });
          }
        })
        .catch((response) => {
          // console.log(response);
          this.props.displayFailureModal();
          this.setState({
            response: response,
            responseContent: null,
            detailedResponse: [],
          });
        });

      // setTimeout(() => {
      //   this.props.displaySuccessModal();
      //   this.setState({
      //     response: true,
      //     responseContent: "97 emails were sent.",
      //     detailedResponse: [
      //       { transaction_id: "12345", status: "error" },
      //       { transaction_id: "67890", status: "success" }
      //     ]
      //   });
      // }, 3000);

      return (
        <CModal
          visible={true}
          className={this.props.className}
          backdrop="static"
          keyboard={false}
        >
          <CModalHeader>
            <span className="badge badge-warning">Please Wait</span>
          </CModalHeader>
          <CModalBody>
            <div className="d-flex justify-content-center mt-2 mb-2">
              <h3>
                <div
                  className="spinner-border text-primary"
                  style={{
                    width: "2rem",
                    height: "2rem",
                    marginRight: "0.5rem",
                  }}
                  role="status"
                >
                  <span className="sr-only">Processing Changes...</span>
                </div>
                Processing Changes...
              </h3>
            </div>
            <p>
              Please do not close this window or navigate away from this page
              until your changes have been processed and the confirmation
              message is displayed.
            </p>
          </CModalBody>
        </CModal>
      );
    }
    return null;
  };

  failureModal = () => {
    if (this.props.showFailureModal === true) {
      return (
        <CModal
          visible={true}
          onDismiss={this.closeModal}
          className={this.props.className}
          backdrop={true}
          keyboard={true}
        >
          <CModalHeader onDismiss={this.resetModals}>
            <span className="badge badge-danger">Error</span>
          </CModalHeader>
          <CModalBody>
            <p>Failed to update shipping status for selected premiums.</p>
            {this.state.responseContent ? (
              <React.Fragment>
                <p>
                  <strong>{this.state.responseContent}</strong>
                </p>
              </React.Fragment>
            ) : null}
            {!this.state.responseContent ? (
              <p>
                Error reaching KPFA server. Please check your internet
                connection and try again.
              </p>
            ) : null}
          </CModalBody>
          <CModalFooter>
            <CButton color="primary" onClick={this.props.closeModal}>
              Return to Shipping Report
            </CButton>
          </CModalFooter>
        </CModal>
      );
    }
    return null;
  };

  successModal = () => {
    if (this.props.showSuccessModal === true) {
      return (
        <CModal
          visible={true}
          onDismiss={this.props.closeModal}
          className={this.props.className}
          backdrop={true}
          keyboard={true}
        >
          <CModalHeader onDismiss={this.props.closeModal}>
            <span className="badge badge-success">
              Selected Premium(s) Updated
            </span>
          </CModalHeader>
          <CModalBody>
            <div>
              <p>
                <strong>{this.state.responseContent}</strong>
              </p>

              <div>
                <CButton
                  color="primary"
                  onClick={this.toggle}
                  style={{ marginBottom: "1rem" }}
                >
                  Show Detailed Results
                </CButton>
                <CCollapse visible={this.state.collapse}>
                  <CCard>
                    <CCardBody>
                      <div>
                        {/* {this.state.detailedResponse.map(email => {retun email.})} */}
                        <div className="row">
                          <div className="col-sm-12 mb-2">
                            {this.showDetailedResults()}
                          </div>
                        </div>
                      </div>
                    </CCardBody>
                  </CCard>
                </CCollapse>
              </div>
            </div>
          </CModalBody>
          <CModalFooter>
            <CButton color="primary" onClick={this.props.closeSuccessModal}>
              Return to Shipping Report
            </CButton>
          </CModalFooter>
        </CModal>
      );
    }
    return null;
  };

  render() {
    return (
      <React.Fragment>
        {this.confirmationModal()}
        {this.processingModal()}
        {this.failureModal()}
        {this.successModal()}
      </React.Fragment>
    );
  }
}

const mapStateToProps = (state) => {
  return { testMode: state.testMode };
};

export default connect(mapStateToProps)(Modals);
