import React from "react";

const SelectedPremiums = props => {
  // console.log("props", props);

  if (props.selectedPremiums.length > 0) {
    const selectedPremiums = [...props.selectedPremiums];
    const allPremiums = [...props.premiums];
    console.log("selectedPremiums", selectedPremiums);
    const premiumsList = selectedPremiums.map((item, index) => {
      let variation = "";
      let variationID = "";
      const selectedPremium = allPremiums.find(premium => {
        if (!premium.variants) {
          return premium.id === item;
        } else if (premium.variants) {
          // console.log(
          //   "premium.variants.variations",
          //   premium.variants.variations,
          //   item
          // );
          const parentPremium = premium.variants.variations.find(variant => {
            // console.log(variant);
            variation = variant.name;
            variationID = variant.id;
            return variant.id.toString() === item;
          });
          // console.log("parentPremium", parentPremium);
          return parentPremium ? true : false;
        }
        return false;
      });

      return (
        <li
          className="list-group-item d-flex justify-content-between align-items-center selected-premiums"
          key={`item${index}`}
        >
          <span className="premium-name">
            {selectedPremium
              ? `${selectedPremium.name} ${
                  selectedPremium.variants ? `(${variation})` : ""
                }`
              : `Cannot find premium ID ${item} in premium list returned by server.`}
          </span>

          <strong>
            {selectedPremium
              ? selectedPremium.variants
                ? `ID:${variationID}`
                : `ID:${selectedPremium.id}`
              : ""}
          </strong>

          {selectedPremium ? (
            <strong>{`$${selectedPremium.price}`}</strong>
          ) : null}

          <div
            className="btn btn-danger ms-2"
            data-index={index}
            onClick={props.handleRemovePremium}
          >
            remove
          </div>
        </li>
      );
    });
    return (
      <ul className="list-group">
        {premiumsList}
        {/* <li className="list-group-item list-group-item-dark d-flex justify-content-between align-items-center">
          <strong>Selected Premiums Total:</strong>
          <strong>$999</strong>
        </li> */}
      </ul>
    );
  }
  return (
    <ul className="list-group">
      <li
        href="#"
        className="list-group-item list-group-item-action list-group-item-warning"
      >
        No Premiums Selected
      </li>
    </ul>
  );
};

export default SelectedPremiums;
