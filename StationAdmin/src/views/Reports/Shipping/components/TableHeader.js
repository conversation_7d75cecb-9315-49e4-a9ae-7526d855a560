import React from "react";
import useShowSortArrow from "../../../Hooks/useShowSortArrow";
import {
  tableHeaders as str_tableHeaders,
  resultsPropertyNames,
} from "../../constants";
import "../shipping.css";

const TableHeader = (props) => {
  return (
    <div className="row bg-dark text-white pt-1 pb-1 font-weight-bold mt-1 mb-2">
      <div className="col-sm-3">
        <button
          type="button"
          className="column-header"
          id={resultsPropertyNames.name}
          onClick={props.handleChangeSort}
        >
          {str_tableHeaders.premium}
          {useShowSortArrow(
            resultsPropertyNames.name,
            props.sortColumn,
            props.sortOrderAscending
          )}
        </button>
      </div>
      <div className="col-sm-1">
        <button
          type="button"
          className="column-header"
          id={resultsPropertyNames.id}
          onClick={props.handleChangeSort}
        >
          {str_tableHeaders.id}
          {useShowSortArrow(
            resultsPropertyNames.id,
            props.sortColumn,
            props.sortOrderAscending
          )}
        </button>
      </div>
      <div className="col-sm-1">
        <button
          type="button"
          className="column-header"
          id={resultsPropertyNames.count}
          onClick={props.handleChangeSort}
        >
          {str_tableHeaders.count}
          {useShowSortArrow(
            resultsPropertyNames.count,
            props.sortColumn,
            props.sortOrderAscending
          )}
        </button>
      </div>
      <div className="col-sm-2">
        <button
          type="button"
          className="column-header"
          id={resultsPropertyNames.company}
          onClick={props.handleChangeSort}
        >
          {str_tableHeaders.vendor}
          {useShowSortArrow(
            resultsPropertyNames.company,
            props.sortColumn,
            props.sortOrderAscending
          )}
        </button>
      </div>
      <div className="col-sm-2">
        <button
          type="button"
          className="column-header"
          id={resultsPropertyNames.category}
          onClick={props.handleChangeSort}
        >
          {str_tableHeaders.category}
          {useShowSortArrow(
            resultsPropertyNames.category,
            props.sortColumn,
            props.sortOrderAscending
          )}
        </button>
      </div>
      <div className="col-sm-1">{str_tableHeaders.status}</div>
      <div className="col-sm-2">{str_tableHeaders.actions}</div>
    </div>
  );
};

export default TableHeader;
