import { useGetStreamersQuery } from "../../../api/kpfa";

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";
import { useEffect, useState } from "react";
import { StreamStamp } from "../../../types";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Filler,
  Legend
);

export default function StreamingReport() {
  const { data: streamsData } = useGetStreamersQuery(null, {
    pollingInterval: 60000,
  });

  const [polledStreamsData, setPolledStreamsData] = useState<StreamStamp[]>();
  const [polledLabels, setPolledLabels] = useState<string[]>();
  const [polledStreamers, setPolledStreamers] = useState<number[]>();

  useEffect(() => {
    if (!polledStreamsData?.length) {
      setPolledStreamsData(streamsData);
    } else if (streamsData && polledStreamsData) {
      const ids = new Set(polledStreamsData.map((d) => d.timestamp));
      setPolledStreamsData([
        ...streamsData.filter((d) => !ids.has(d.timestamp)),
        ...polledStreamsData,
      ]);
    }
    //eslint-disable-next-line
  }, [streamsData]);

  useEffect(() => {
    if (streamsData) {
      // Use all available data without filtering
      setPolledLabels(streamsData.map((stream) => stream.timestamp).reverse());
      setPolledStreamers(
        streamsData.map((stream) => stream.streamers).reverse()
      );
    }
    //eslint-disable-next-line
  }, [polledStreamsData]);

  //todo: add actual loading state and error state
  if (polledLabels == null || polledStreamers == null) {
    return <div>Loading Streaming Data...</div>;
  }
  
  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false, // Hide the legend
      },
      title: {
        display: true,
        text: "KPFA Online Streamers",
        font: {
          size: 18,
          weight: 'bold' as const,
        },
        padding: {
          bottom: 20
        }
      },
      tooltip: {
        callbacks: {
          title: (context: any) => {
            const date = new Date(polledLabels[context[0].dataIndex]);
            return date.toLocaleString([], { 
              month: 'numeric',
              day: 'numeric',
              hour: '2-digit', 
              minute: '2-digit' 
            });
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false // Hide the vertical grid lines
        },
        ticks: {
          maxTicksLimit: 8, // Show fewer x-axis labels
          callback: function(value: any, index: number, values: any) {
            const date = new Date(polledLabels[index]);
            // Only show time in the labels
            return date.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            });
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)' // Lighter grid lines
        }
      }
    }
  };

  const data = {
    labels: polledLabels,
    datasets: [
      {
        fill: true,
        label: "Online Listeners",
        data: polledStreamers,
        borderColor: "rgb(53, 162, 235)",
        backgroundColor: "rgba(53, 162, 235, 0.5)",
        borderWidth: 2,
        pointRadius: 3,
        tension: 0.3, // Add some curve to the line
      },
    ],
  };

  return (
    <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
      <Line options={options} data={data} />
    </div>
  );
}
