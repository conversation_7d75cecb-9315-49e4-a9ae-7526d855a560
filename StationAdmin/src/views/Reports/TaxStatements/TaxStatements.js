import { useState, useEffect } from "react";
import moment from "moment";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@coreui/react";
import { CSVLink } from "react-csv";
import currency from "currency.js";
import { startCase } from "lodash";

import useDonors from "../../Hooks/useDonors";
import {
  csvKeys,
  csvHeaders,
  dateMin,
  paymentStatusOptions,
  dateRegEx,
} from "./strings";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Select from "react-select";
import TaxYear from "./TaxYear";

const today = moment().format("YYYY-MM-DD");

const TaxStatements = () => {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [paymentStatus, setPaymentStatus] = useState(
    paymentStatusOptions.success
  );
  const [minPaymentAmount, setMinPaymentAmount] = useState(5);
  const [isLoading, setIsLoading] = useState(false);
  const [donationsCSV, setDonationsCSV] = useState([]);
  const [searchParams, setSearchParams] = useState({
    startDate,
    endDate,
    paymentStatus,
    minPaymentAmount,
  });
  const [donations, updateDonations] = useDonors(searchParams, setIsLoading);

  useEffect(() => {
    const donationsCSVarr = [];
    donations.forEach((donorRecord) => {
      if (donations.length > 0) {
        const obj = {};
        for (let [key, value] of Object.entries(donorRecord)) {
          if (csvKeys.includes(key)) {
            if (key !== "donations") {
              obj[key] = value ? String(value).replace(/."/g, '""') : "";
            } else if (key === "donations") {
              if (donorRecord[key].length > 0) {
                const taxDeductibleValues = [];
                donorRecord["donations"].forEach((donation, index) => {
                  const [totalPayment, totalRefunds] = donation.payments.reduce(
                    (total, current) => {
                      return [
                        currency(total[0]).add(current.amount).value,
                        currency(total[1]).add(current.amount_refunded).value,
                      ];
                    },
                    [0, 0]
                  );
                  const netPayment =
                    currency(totalPayment).subtract(totalRefunds).value;
                  const premiumValueTotal =
                    donation.premiums.length > 0
                      ? donation.premiums.reduce(
                          (total, current) =>
                            currency(total).add(current.fmv).value,
                          0
                        )
                      : 0.0;
                  const taxDeductible =
                    currency(netPayment).subtract(premiumValueTotal).value;
                  obj[`donation_${index + 1}_amount`] = netPayment;
                  obj[`donation_${index + 1}_fmvTotal`] = premiumValueTotal;
                  obj[`donation_${index + 1}_taxDeductible`] = taxDeductible;
                  taxDeductibleValues.push(taxDeductible);

                  obj[`donation_${index + 1}_timestamp`] = moment(
                    donation.timestamp
                  ).format("M/D/YYYY");
                });
                obj.totalTaxDeductible = taxDeductibleValues.reduce(
                  (total, current) => currency(total).add(current).value,
                  0
                );
              }
            }
          }
        }
        donationsCSVarr.push(obj);
      }
    });

    setDonationsCSV(donationsCSVarr);
  }, [donations]);

  const isSearchInvalid = (() => {
    if (
      startDate.match(dateRegEx) &&
      endDate.match(dateRegEx) &&
      moment(startDate).isAfter(moment(dateMin)) &&
      moment(endDate).isAfter(moment(dateMin)) &&
      moment(endDate).isAfter(moment(startDate)) &&
      !isLoading
    ) {
      return false;
    }
    return true;
  })();

  return (
    <>
      <div className="row mb-3">
        <div className="col-sm-12">
          <div className="h5">
            Select a date range to download donor tax statement data in CSV
            format.
          </div>
        </div>
      </div>

      <div className="row mb-4">
        <div className="col-sm-12 col-md-6 col-lg-3 form-group">
          <label htmlFor="start">Start date</label>

          <input
            className="form-control me-2"
            type="date"
            name="startDate"
            min={dateMin}
            max={today}
            onChange={(e) => {
              setStartDate(e.target.value);
              updateDonations([]);
            }}
            value={startDate}
          />
        </div>
        <div className="col-sm-12 col-md-6 col-lg-3 form-group">
          <label htmlFor="start">End date</label>

          <input
            className="form-control me-2"
            type="date"
            name="endDate"
            min={dateMin}
            max={today}
            onChange={(e) => {
              setEndDate(e.target.value);
              updateDonations([]);
            }}
            value={endDate}
          />
        </div>
        <div className="col-sm-12 col-md-6 col-lg-2 form-group">
          <label htmlFor="paymentStatus">Payment Status</label>
          <Select
            id="paymentStatus"
            onChange={(e) => {
              setPaymentStatus(e.value);
              updateDonations([]);
            }}
            options={[
              {
                value: paymentStatusOptions.success,
                label: startCase(paymentStatusOptions.success),
              },
              {
                value: paymentStatusOptions.fail,
                label: startCase(paymentStatusOptions.fail),
              },
            ]}
          />
        </div>
        <div className="col-sm-12 col-md-6 col-lg-2 form-group">
          <label htmlFor="minpayment">Min. Payment Amount</label>
          <input
            name="minpayment"
            type="number"
            className="form-control me-2"
            onChange={(e) => {
              setMinPaymentAmount(Number(e.target.value));
              updateDonations([]);
            }}
            onBlur={(e) => {
              const value =
                Number(e.target.value) >= 5 ? Number(e.target.value) : 5;
              setMinPaymentAmount(value);
              updateDonations([]);
            }}
            value={minPaymentAmount}
            min="5"
          />
        </div>
        <div className="col-sm-12 col-md-6 col-lg-2 d-flex align-items-end">
          <CButton
            block
            disabled={isSearchInvalid}
            onClick={() => {
              if (!isSearchInvalid) {
                setSearchParams({
                  startDate,
                  endDate,
                  paymentStatus,
                  minPaymentAmount,
                });
              }
            }}
          >
            Search
          </CButton>
        </div>
      </div>
      <div className="row mb-4">
        <div className="col-sm-12 d-flex justify-content-center align-items-center">
          {isLoading ? (
            <h4>
              <CSpinner
                // style={{ width: "3rem", height: "3rem" }}
                color="primary"
              />
              <span className="ms-2">Searching Donor Records...</span>
            </h4>
          ) : null}
          {donations.length > 0 && !isLoading ? (
            <h4>{`${donations.length} donor records found.`}</h4>
          ) : null}
        </div>
      </div>
      <div className="row mb-5">
        <div className="col-sm-12 d-flex justify-content-center align-items-center">
          <CSVLink
            data={donationsCSV}
            headers={csvHeaders}
            className={
              donations.length > 0 && !isLoading
                ? "btn btn-primary btn-lg"
                : "btn btn-primary btn-lg disabled"
            }
            filename={`TaxStatements_fr_${startDate}_to_${endDate}_saved_${moment().format(
              "YYYY-MM-DD_HH-mm"
            )}.csv`}
          >
            <FontAwesomeIcon icon="file-csv" className="me-2" />
            Download Tax Statement CSV
          </CSVLink>
        </div>
      </div>
      <TaxYear />
    </>
  );
};

export default TaxStatements;
