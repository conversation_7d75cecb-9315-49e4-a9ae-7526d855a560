import { FC, useEffect, useState } from "react"; // Import FC and useState
import { CButton } from "@coreui/react";
import { jsonToCSV, useCSVReader } from "react-papaparse";
import { toTitleCase } from "../../../helpers";
import { useAppSelector } from "../../../hooks";

interface TaxYearDonorData {
  donor_id: string;
  firstname: string;
  lastname: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone?: string;
  email?: string;
  Total_Payment_Amount: string;
  Total_Fair_Market_Value: string;
  Total_Tax_Deductible: string;
}

interface AllegianceDonorData {
  "Last Name": string;
  "First Name": string;
  "Address 1": string;
  "Address 2"?: string;
  City: string;
  State: string;
  Zip: string;
  Zip4?: string;
  Telephone?: string;
  Email?: string;
  "Amt Paid": string;
}

const TaxYear: FC = () => {
  const [donorsData, setDonorsData] = useState<TaxYearDonorData[]>([]);
  const [donorsWithEmails, setDonorsWithEmails] = useState<TaxYearDonorData[]>(
    []
  );

  const [emailResponse, setEmailResponse] = useState<{
    status: number;
    message: string;
  }>();
  const [submitting, setSubmitting] = useState(false);
  const { CSVReader } = useCSVReader();

  const testMode = useAppSelector((state) => state.testMode);
  const userEmail = useAppSelector((state) => state.user.email);

  // Update donorsWithEmails whenever donorsData changes
  //
  // todo: implement de-duplication for emails.
  // unfortunately, we can't yet implement this for physical letters, because many donors have different addresses on file for them for duplicate accounts
  useEffect(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const filteredDonors = donorsData.filter((donor) => {
      // Ensure the email is valid
      const hasValidEmail = donor.email && emailRegex.test(donor.email);

      // Convert Total_Tax_Deductible to an integer and check if it's greater than 0
      const taxDeductible = parseInt(donor.Total_Tax_Deductible, 10);
      const hasValidTaxDeductible = !isNaN(taxDeductible) && taxDeductible > 0;

      // Only include donors with valid email and a Total_Tax_Deductible greater than 0
      return hasValidEmail && hasValidTaxDeductible;
    });

    setDonorsWithEmails(filteredDonors);
  }, [donorsData]);

  const convertAllegianceToTaxYearFormat = (
    allegianceData: any[]
  ): TaxYearDonorData[] => {
    return allegianceData.map((row) => {
      // Extract 'Amt Paid' and remove any non-numeric characters
      const amtPaidRaw = row[10]?.replace(/[^0-9.]/g, "") || "0";
      const amtPaid = parseFloat(amtPaidRaw).toFixed(2);

      return {
        donor_id: "", // Handle donor_id assignment
        firstname: toTitleCase(row[1]?.trim()) || "",
        lastname: toTitleCase(row[0]?.trim()) || "",
        address1: toTitleCase(row[3]?.trim()) || "",
        address2: toTitleCase(row[2]?.trim()) || "",
        city: toTitleCase(row[4]?.trim()) || "",
        state: toTitleCase(row[5]?.trim) || "",
        postal_code: row[6]?.trim() || "",
        country: row[7] || "US", // Default to 'US' if country is not provided
        phone: row[8]?.replace(/[^\d]/g, "") || "", // Remove non-numeric characters from phone
        email: row[9]?.trim() || "",
        Total_Payment_Amount: amtPaid,
        Total_Fair_Market_Value: "0.00",
        Total_Tax_Deductible: amtPaid,
      };
    });
  };

  const handleUploadTaxYearSummary = (results: any) => {
    const donors = results.data.map((row: any) => ({
      donor_id: row[0] || "",
      firstname: row[1] || "",
      lastname: row[2] || "",
      address1: row[3] || "",
      address2: row[4] || "",
      city: row[5] || "",
      state: row[6] || "",
      postal_code: row[7] || "",
      country: row[8] || "US", // Assuming 'US' if country is not provided
      phone: row[9]?.replace(/[^\d]/g, "") || "", // Remove non-numeric characters from phone
      email: row[10] || "",
      Total_Payment_Amount: parseFloat(row[11]).toFixed(2),
      Total_Fair_Market_Value: parseFloat(row[12]).toFixed(2),
      Total_Tax_Deductible: parseFloat(row[13]).toFixed(2),
    })) as TaxYearDonorData[];

    setDonorsData((prev: TaxYearDonorData[]) => [...prev, ...donors]);
  };

  const handleImportAllegianceDonors = (results: any) => {
    const allegianceDonors = convertAllegianceToTaxYearFormat(
      results.data as AllegianceDonorData[]
    );
    setDonorsData((prev: TaxYearDonorData[]) => [...prev, ...allegianceDonors]);
  };

  const handleExportCSV = () => {
    const csv = jsonToCSV(donorsData);
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = "DonorsData.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleEmails = () => {
    const url = `https://${
      testMode.value ? "api.staging." : "api."
    }kpfa.org/email`;

    const headers = {
      Authorization: `Bearer ${localStorage.getItem("jwt")}`,
      "Content-Type": "application/json",
      Accept: "application/json",
    };

    const sendData = (data: TaxYearDonorData[]) => {
      fetch(url, {
        method: "POST",
        body: JSON.stringify({ type: "tax_letter", donors: data }),
        headers,
      })
        .then((response) => response.json())
        .then((response) => {
          console.log(response);
          if (response.status === "error") {
            setEmailResponse(response);
          }
          if (response.status === "success") {
            setEmailResponse(response);
          }
        })
        .catch((response) => {
          console.log(response);
          setEmailResponse(response);
        })
        .finally(() => {
          setSubmitting(false);
        });
    };

    if (!submitting) {
      setSubmitting(true);

      const quarter = Math.ceil(donorsWithEmails.length / 4);
      for (let i = 0; i < 4; i++) {
        const part = donorsWithEmails.slice(i * quarter, (i + 1) * quarter);
        sendData(part);
      }
    }
  };

  return (
    <>
      <h3 className="mb-3">End of Year Tax Report</h3>
      {userEmail === "<EMAIL>" ? (
        <>
          <CSVReader onUploadAccepted={handleUploadTaxYearSummary}>
            {({ getRootProps }: any) => (
              <CButton className="me-3" variant="outline" {...getRootProps()}>
                Upload Tax Year Summary
              </CButton>
            )}
          </CSVReader>
          <CSVReader onUploadAccepted={handleImportAllegianceDonors}>
            {({ getRootProps }: any) => (
              <CButton variant="outline" {...getRootProps()}>
                Import Allegiance Donors
              </CButton>
            )}
          </CSVReader>
          <button onClick={handleExportCSV}>Export to CSV</button>
        </>
      ) : null}

      <CButton onClick={handleEmails}>Email</CButton>
      {emailResponse?.status}
      <br />
      {emailResponse?.message}
      {/*
      { emailResponse ?
        <>
          <p>The Email was not sent successfully.</p>
          {emailResponse.message ? (
            <React.Fragment>
              <p>
                <strong>{emailResponse.message}</strong>
              </p>
            </React.Fragment>
          ) : null}
          {!emailResponse.message ? (
            <p>
              Error reaching KPFA server. Please check your internet connection
              and try again.
            </p>
          ) : null}
    </>
      : null }
          */}
    </>
  );
};

export default TaxYear;
