export const shippingStatus = {
  shipped: "Shipped",
  onHold: "On Hold",
  new: "New",
  canceled: "Canceled",
};

export const shippingStatusOptions = [
  { value: shippingStatus.new, label: shippingStatus.new },
  { value: shippingStatus.shipped, label: shippingStatus.shipped },
  { value: shippingStatus.onHold, label: shippingStatus.onHold },
  { value: shippingStatus.canceled, label: shippingStatus.canceled },
];

export const shippingStatusUpdateOptions = [
  { value: shippingStatus.new, label: shippingStatus.new },
  { value: shippingStatus.shipped, label: shippingStatus.shipped },
  { value: shippingStatus.onHold, label: shippingStatus.onHold },
  { value: shippingStatus.canceled, label: shippingStatus.canceled },
];

export const searchPremiums = "Search for Premiums in Date Range";
export const updateShippingStatus = "Update";
export const updateSelect = "Update Selected Premiums";

export const tableHeaders = {
  premium: "Premium",
  id: "ID",
  count: "Count",
  vendor: "Vendor",
  category: "Category",
  status: "Status",
  actions: "Actions",
};

export const resultsPropertyNames = {
  name: "name",
  id: "premium_id",
  count: "count",
  category: "category_name",
  company: "company",
};
