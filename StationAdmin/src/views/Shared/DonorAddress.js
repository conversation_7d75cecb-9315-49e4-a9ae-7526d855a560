import React from "react";

export default function DonorAddress({ selectedDonor }) {
  return (
    <address>
      {selectedDonor.firstname} {selectedDonor.lastname}
      <br />
      {selectedDonor.address1}
      <br />
      {selectedDonor.address2 ? selectedDonor.address2 : ""}
      {selectedDonor.address2 ? <br /> : null}
      {selectedDonor.city}, {selectedDonor.state}
      <br />
      {selectedDonor.postal_code}
      <br />
      {selectedDonor.country}
      <br />
    </address>
  );
}
