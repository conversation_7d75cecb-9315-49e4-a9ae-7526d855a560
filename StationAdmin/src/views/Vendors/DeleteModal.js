import {
  CButton,
  CButtonGroup,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";
import { useState } from "react";
import { useDeleteVendorMutation } from "../../api/kpfa.ts";
import Alert from "../../components/Alert";

export default function DeleteModal({ toggle, visible, selectedVendor }) {
  const [deleteVendor, { isLoading, isSuccess }] = useDeleteVendorMutation();
  const [alert, setAlert] = useState({ display: false });

  const handleDelete = () => {
    deleteVendor(selectedVendor.id)
      .unwrap()
      .then((fulfilled) => {
        setAlert({
          type: "success",
          display: true,
          message: fulfilled.message,
        });
      })
      .catch((rejected) => {
        setAlert({
          type: "fail",
          display: true,
          message: rejected?.data?.message,
          error: rejected?.error,
        });
      });
  };

  return (
    <CModal
      visible={visible}
      onClose={toggle}
      backdrop="static"
      keyboard={false}
    >
      <CModalHeader onClose={toggle}>
        <h4>Confirm Vendor Deletion</h4>
      </CModalHeader>
      <CModalBody className="py-4">
        {isSuccess
          ? null
          : `Are you sure you want to delete ${selectedVendor.company}?`}
        <Alert alert={alert} />
      </CModalBody>
      <CModalFooter>
        <CButtonGroup>
          <CButton
            onClick={toggle}
            disabled={isLoading}
            color={isSuccess ? "primary" : "danger"}
            variant="outline"
            size="sm"
          >
            {isSuccess ? "Back" : "Cancel"}
          </CButton>
          {isSuccess ? null : (
            <CButton
              onClick={handleDelete}
              disabled={isLoading}
              className="text-white"
              size="sm"
              color="danger"
            >
              Delete
            </CButton>
          )}
        </CButtonGroup>
      </CModalFooter>
    </CModal>
  );
}
