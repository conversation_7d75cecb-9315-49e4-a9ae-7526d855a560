import React from "react";
import {
  CButton,
  CButtonGroup,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from "@coreui/react";

export default function DisplayResults({ vendors, vendorAction }) {
  return (
    <div className="mx-auto p-3 bg-white rounded shadow my-3">
      <h3 className="mb-3">Vendors</h3>
      <CTable responsive>
        <CTableHead>
          <CTableRow>
            <CTableHeaderCell scope="col">Company</CTableHeaderCell>
            <CTableHeaderCell scope="col">Email</CTableHeaderCell>
            <CTableHeaderCell scope="col">Contact</CTableHeaderCell>
            <CTableHeaderCell scope="col">Phone</CTableHeaderCell>
            <CTableHeaderCell scope="col">Address</CTableHeaderCell>
            <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {vendors.map((vendor) => (
            <CTableRow key={vendor.id}>
              <CTableDataCell scope="row" className="align-middle">
                <a href={vendor.website}>{vendor.company} </a>
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                <a href={`mailto:${vendor.email}`}>{vendor.email}</a>
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                {vendor.contact}
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                {vendor.phone}
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                {vendor.address}, {vendor.city}, {vendor.state} {vendor.zip}
              </CTableDataCell>
              <CTableDataCell className="align-middle">
                <CButtonGroup>
                  <CButton
                    onClick={(e) => vendorAction(e, "edit")}
                    variant="outline"
                    size="sm"
                    data-id={vendor.id}
                  >
                    Edit
                  </CButton>
                  <CButton
                    onClick={(e) => vendorAction(e, "delete")}
                    data-id={vendor.id}
                    variant="outline"
                    size="sm"
                  >
                    Delete
                  </CButton>
                </CButtonGroup>
              </CTableDataCell>
            </CTableRow>
          ))}
        </CTableBody>
      </CTable>
    </div>
  );
}
