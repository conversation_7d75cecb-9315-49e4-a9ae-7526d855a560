import { useGetVendorsQuery } from "../../api/kpfa.ts";
import React, { useRef, useState } from "react";
import DisplayResults from "./DisplayResults";
import VendorForm from "./VendorForm";
import { CButton } from "@coreui/react";
import DeleteModal from "./DeleteModal";
import { Link } from "react-router-dom";

export default function ManageVendors() {
  const [showEdit, setShowEdit] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState();
  const [alert, setAlert] = useState({
    type: "",
    display: false,
    message: "",
  });

  const {
    data: vendorsData,
    isLoading: vendorsDataIsLoading,
  } = useGetVendorsQuery();

  const formRef = useRef();

  const vendorAction = (e, action) => {
    setSelectedVendor(
      vendorsData.filter(
        (vendor) => vendor.id === parseInt(e.target.dataset.id)
      )[0]
    );

    if (action === "edit") {
      setShowEdit(true);
      setAlert({ display: false });
      formRef.current.scrollIntoView({ behavior: "smooth" });
    }

    if (action === "delete") {
      setShowEdit(false);
      setShowDelete(true);
    }
  };

  const handleNewVendorClick = () => {
    setShowEdit(true);
    setAlert({ display: false });

    if (selectedVendor) {
      setSelectedVendor(null);
    }
  };

  return (
    <>
      {!showEdit || (showEdit && selectedVendor) ? (
        <CButton className="me-1" onClick={handleNewVendorClick}>
          Create New Vendor
        </CButton>
      ) : null}
      <CButton color="dark">
        <Link className="text-white text-decoration-none" to="/premiums">
          Manage Premiums
        </Link>
      </CButton>

      <VendorForm
        ref={formRef}
        visible={showEdit}
        selectedVendor={selectedVendor}
        setSelectedVendor={setSelectedVendor}
        toggleEdit={() => setShowEdit(false)}
        alert={alert}
        setAlert={setAlert}
      />

      {!vendorsDataIsLoading && vendorsData ? (
        <DisplayResults vendors={vendorsData} vendorAction={vendorAction} />
      ) : (
        <div className="d-flex justify-content-center pt-3">
          <div className="spinner-border text-info">
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      )}
      {showDelete ? (
        <DeleteModal
          toggle={() => setShowDelete(false)}
          visible={showDelete}
          selectedVendor={selectedVendor}
        />
      ) : null}
    </>
  );
}
