import React, { forwardRef, useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import InputFlexible from "../Form/InputFlexible";
import vendorSchema from "./VendorSchema";
import { CRow, CCol, CButton, CButtonGroup } from "@coreui/react";
import <PERSON>Field from "../Form/SelectField";
import { useAddVendorMutation, useEditVendorMutation } from "../../api/kpfa.ts";
import Alert from "../../components/Alert";
import { stateOptions } from "../Form/constants";

const blankInitialValues = {
  company: "",
  email: "",
  contact: "",
  address: "",
  city: "",
  state: null,
  zip: "",
  website: "",
  phone: "",
};

const VendorForm = forwardRef(
  (
    { selectedVendor, setSelectedVendor, toggleEdit, alert, setAlert, visible },
    ref
  ) => {
    const [initialValues, setInitialValues] = useState(blankInitialValues);
    const [editVendor] = useEditVendorMutation();
    const [addVendor] = useAddVendorMutation();

    useEffect(() => {
      selectedVendor
        ? setInitialValues({
            company: selectedVendor.company,
            email: selectedVendor.email,
            contact: selectedVendor.contact,
            address: selectedVendor.address,
            city: selectedVendor.city,
            state: selectedVendor.state,
            zip: selectedVendor.zip,
            website: selectedVendor.website,
            phone: selectedVendor.phone,
          })
        : setInitialValues(blankInitialValues);
    }, [selectedVendor]);

    return (
      <div ref={ref}>
        {visible ? (
          <div className="bg-white rounded shadow">
            <Formik
              enableReinitialize={true}
              initialValues={initialValues}
              validationSchema={vendorSchema}
              onSubmit={(values, { setSubmitting }) => {
                setAlert({ display: false });

                let body = values;

                if (selectedVendor) {
                  body.id = selectedVendor.id;
                }

                const mutation = selectedVendor ? editVendor : addVendor;

                mutation({ body })
                  .unwrap()
                  .then((fulfilled) => {
                    setAlert({
                      type: "success",
                      display: true,
                      message: `Successfully ${
                        selectedVendor ? "updated" : "created"
                      } vendor: ${fulfilled.company}`,
                    });
                    body.id = fulfilled.id;
                    setSelectedVendor(body);
                  })
                  .catch((rejected) => {
                    setAlert({
                      type: "fail",
                      display: true,
                      message: rejected?.data?.message,
                      error: rejected?.error,
                    });
                  });

                setSubmitting(false);
              }}
            >
              {() => (
                <Form className="col-12 mt-2 mb-4 p-3">
                  <h3>
                    {selectedVendor ? `Edit Vendor` : "Create New Vendor"}
                  </h3>
                  <CRow>
                    <CCol xs={12} sm className="mb-4">
                      <Field
                        name="company"
                        label="Company"
                        component={InputFlexible}
                      />
                    </CCol>
                    <CCol xs={12} sm>
                      <Field
                        name="email"
                        label="Email"
                        component={InputFlexible}
                      />
                    </CCol>
                    <CCol className="mb-4" xs={12} sm>
                      <Field
                        name="phone"
                        label="Phone"
                        component={InputFlexible}
                      />
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol className="mb-4" xs={12} sm>
                      <Field
                        name="contact"
                        label="Contact"
                        component={InputFlexible}
                      />
                    </CCol>
                    <CCol xs={12} sm className="mb-4">
                      <Field
                        name="website"
                        label="Website"
                        component={InputFlexible}
                      />
                    </CCol>
                  </CRow>
                  <CRow>
                    <CCol xs={12} sm className="mb-4">
                      <Field
                        name="address"
                        label="Address"
                        component={InputFlexible}
                      />
                    </CCol>
                    <CCol xs={12} sm className="mb-4">
                      <Field
                        name="city"
                        label="City"
                        component={InputFlexible}
                      />
                    </CCol>
                    <CCol xs={12} sm className="mb-4">
                      <Field
                        name="state"
                        label="State"
                        labelPlacement="top"
                        component={SelectField}
                        options={stateOptions}
                      />
                    </CCol>
                    <CCol xs={12} sm className="mb-4">
                      <Field name="zip" label="Zip" component={InputFlexible} />
                    </CCol>
                  </CRow>

                  <CRow className="pb-4">
                    <CCol>
                      <CButtonGroup>
                        <CButton type="submit">
                          {selectedVendor ? "Edit" : "Create"}
                        </CButton>
                        <CButton onClick={toggleEdit} variant="outline">
                          Cancel
                        </CButton>
                      </CButtonGroup>
                      <Alert alert={alert} />
                    </CCol>
                  </CRow>
                </Form>
              )}
            </Formik>
          </div>
        ) : null}
      </div>
    );
  }
);

export default VendorForm;
