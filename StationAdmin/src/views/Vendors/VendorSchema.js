import * as Yup from "yup";
import { errorStrings } from "../Form/validationStrings";

const vendorSchema = Yup.object().shape({
  company: Yup.string().required(errorStrings.required),
  contact: Yup.string().required(errorStrings.required),
  website: Yup.string().required(errorStrings.required),
  address: Yup.string().required(errorStrings.required),
  city: Yup.string().required(errorStrings.required),
  state: Yup.string().required(errorStrings.required).nullable(),
  zip: Yup.string().required(errorStrings.required),
  phone: Yup.string().required(errorStrings.required),
  email: Yup.string().required(errorStrings.required),
});

export default vendorSchema;
