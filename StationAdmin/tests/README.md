# StationAdmin E2E Tests

This directory contains end-to-end tests for the StationAdmin application, specifically focusing on the CreateDonation form functionality with **Stripe credit card payments**.

## Overview

The E2E tests are built using [Playwright](https://playwright.dev/) and provide comprehensive testing of the Stripe credit card donation creation workflow, including:

- **One-time credit card donations** (Visa, Mastercard, Amex)
- **Monthly credit card subscriptions**
- **Form validation** and error handling
- **Different card types** and amounts
- **Edge cases** and error scenarios

## Test Structure

### Test Files

- `create-donation.test.ts` - Main donation creation flow tests
- `create-donation-validation.test.ts` - Form validation and error handling tests
- `utils/test-helpers.ts` - Shared utilities and helper functions

### Test Categories

#### 1. One-Time Donations
- Credit card payments (Visa, Mastercard, Amex)
- Check payments
- Different donation amounts
- Form validation

#### 2. Monthly Subscriptions
- Credit card subscriptions
- Different subscription amounts
- Recurring payment setup

#### 3. Validation & Error Handling
- Required field validation
- Email format validation
- Credit card validation
- Phone number validation
- ZIP code validation
- Declined card handling
- Expiration date validation

## Running Tests

### Prerequisites

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Install Playwright browsers**:
   ```bash
   npx playwright install
   ```

3. **Set up test credentials**:
   ```bash
   # Copy the example environment file (gitignored for security)
   cp .env.example .env

   # Edit .env and add your CallCenter account credentials
   nano .env
   ```

   Add your CallCenter credentials to `.env`:
   ```env
   # Existing app configuration
   PORT=3000
   CHOKIDAR_USEPOLLING=true

   # Test credentials for E2E tests
   TEST_CALLCENTER_EMAIL=<EMAIL>
   TEST_CALLCENTER_PASSWORD=your_password_here
   ```

4. **Start the development server with ngrok tunnel**:
   ```bash
   # Start StationAdmin
   npm start

   # In another terminal, start ngrok tunnel (required for Google OAuth)
   ngrok http 3000
   ```

   **Important**: Tests use the ngrok tunnel `https://pet-leopard-fully.ngrok-free.app` for Google OAuth authentication. Make sure this tunnel is active and pointing to your local StationAdmin on port 3000.

### Test Commands

#### Run all E2E tests
```bash
npm run test:e2e
```

#### Run tests with browser UI (headed mode)
```bash
npm run test:e2e:headed
```

#### Run tests with Playwright UI (interactive)
```bash
npm run test:e2e:ui
```

#### Run tests in debug mode
```bash
npm run test:e2e:debug
```

#### Run specific test files
```bash
# Main donation creation tests
npm run test:e2e:create-donation

# Validation and error handling tests
npm run test:e2e:validation
```

#### Run all tests (unit + E2E)
```bash
npm run test:all
```

## Test Data

### Default Test Donor
```javascript
{
  firstName: 'Test',
  lastName: 'Donor',
  email: '<EMAIL>',
  phone: '(*************',
  address1: '123 Test Street',
  city: 'Berkeley',
  state: 'CA',
  zip: '94710',
  country: 'US'
}
```

### Test Credit Cards
- **Visa Success**: `****************`
- **Visa Declined**: `****************`
- **Mastercard Success**: `****************`
- **Amex Success**: `***************`

## Key Features

### Automated Test Flow
The tests use helper functions to automate the complete donation flow:

1. **Authentication** - Mock login to StationAdmin
2. **Form Filling** - Automated donor and payment information entry
3. **Submission** - Form submission and confirmation
4. **Verification** - Success/error state validation

### Unique Test Data
Each test generates unique donor data to avoid conflicts:
```javascript
const uniqueDonor = generateUniqueTestDonor();
```

### Comprehensive Validation
Tests cover all form validation scenarios:
- Required fields
- Format validation (email, phone, ZIP)
- Credit card validation
- Amount limits
- Expiration dates

## Test Architecture

### Helper Functions
- `loginToStationAdmin()` - Mock authentication
- `fillDonorInformation()` - Fill donor form fields
- `fillCreditCardInformation()` - Fill payment form fields
- `completeDonationFlow()` - End-to-end donation creation
- `generateUniqueTestDonor()` - Create unique test data

### Test Organization
- **Describe blocks** group related tests
- **beforeEach** handles common setup (login)
- **Console logging** provides test progress feedback
- **Assertions** verify expected behavior

## Configuration

### Playwright Config
The tests are configured in `playwright.config.ts`:
- **Base URL**: `http://localhost:3000`
- **Browsers**: Chromium, Firefox, WebKit
- **Parallel execution**: Enabled
- **Retries**: 2 on CI, 0 locally
- **Traces**: On first retry

### Test Selectors
Tests use `data-testid` attributes for reliable element selection:
```html
<button data-testid="submit-donation">Submit</button>
```

## Troubleshooting

### Common Issues

1. **Server not running**
   - Ensure `npm start` is running on port 3000
   - Check that the app loads at `http://localhost:3000`

2. **Authentication issues**
   - Tests use mock authentication via localStorage
   - Verify auth state is properly set in `loginToStationAdmin()`

3. **Element not found**
   - Check that `data-testid` attributes exist in components
   - Verify selectors match actual DOM structure

4. **Timing issues**
   - Tests include appropriate waits and timeouts
   - Increase timeout values if needed for slower environments

### Debug Mode
Use debug mode to step through tests interactively:
```bash
npm run test:e2e:debug
```

## Contributing

When adding new tests:

1. **Use helper functions** for common operations
2. **Generate unique test data** to avoid conflicts
3. **Add appropriate assertions** to verify behavior
4. **Include console logging** for test progress
5. **Follow existing naming conventions**
6. **Update this README** if adding new test categories

## Future Enhancements

Potential areas for test expansion:
- Premium selection testing
- Campaign assignment testing
- Donor search and selection
- Payment method switching
- Mobile responsive testing
- Performance testing
- Accessibility testing
