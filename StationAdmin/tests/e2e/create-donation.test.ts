import { test, expect } from '@playwright/test';

test.describe('StationAdmin Donation Tests', () => {

  // Helper function for authentication
  async function authenticateAdmin(page) {
    const testEmail = process.env.TEST_ADMIN_EMAIL;
    const testPassword = process.env.TEST_ADMIN_PASSWORD;

    if (!testEmail || !testPassword) {
      throw new Error('No admin credentials in .env file');
    }

    console.log(`🔑 Authenticating with: ${testEmail}`);

    // Go to homepage and click Sign Out to trigger OAuth
    await page.goto('/');
    await page.waitForTimeout(2000);

    // First, let's see what's on the page before clicking Sign Out
    const beforeButtons = await page.locator('button').allTextContents().catch(() => []);
    const beforeLinks = await page.locator('a').allTextContents().catch(() => []);
    console.log('🔍 Before Sign Out - buttons:', beforeButtons.slice(0, 5));
    console.log('🔍 Before Sign Out - links:', beforeLinks.slice(0, 5));

    // Check if Sign Out link is actually visible
    const signOutVisible = await page.locator('a:has-text("Sign Out")').isVisible().catch(() => false);
    console.log(`🔍 Sign Out link visible: ${signOutVisible}`);

    if (!signOutVisible) {
      throw new Error('Sign Out link not found on page');
    }

    console.log('🔄 Clicking Sign Out to trigger OAuth...');

    // Try clicking Sign Out and wait longer for OAuth redirect
    await page.click('a:has-text("Sign Out")');
    console.log('✅ Clicked Sign Out link');

    // Wait longer for potential OAuth redirect
    console.log('⏳ Waiting for OAuth redirect...');
    await page.waitForTimeout(5000);

    // Check if we're on an OAuth page now
    let currentUrl = page.url();
    console.log(`🔍 URL after wait: ${currentUrl}`);

    // If still on same page, try alternative approaches
    if (currentUrl.includes('pet-leopard-fully.ngrok-free.app') && !currentUrl.includes('oauth') && !currentUrl.includes('google')) {
      console.log('🔄 Still on StationAdmin page, trying alternative OAuth access...');

      // Try common OAuth endpoints
      const oauthUrls = [
        '/auth/google',
        '/oauth/google',
        '/login',
        '/auth',
        '/signin'
      ];

      for (const oauthUrl of oauthUrls) {
        console.log(`🔍 Trying OAuth URL: ${oauthUrl}`);
        await page.goto(oauthUrl);
        await page.waitForTimeout(2000);

        const hasGoogleButton = await page.locator('button:has-text("Sign in with Google"), button:has-text("Google")').isVisible().catch(() => false);
        if (hasGoogleButton) {
          console.log(`✅ Found OAuth page at: ${oauthUrl}`);
          break;
        }
      }
    }

    await page.waitForLoadState('networkidle');

    // Take screenshot to see what page we're on
    await page.screenshot({ path: 'test-results/after-signout.png', fullPage: true });

    // Debug what we actually see on the page
    const finalUrl = page.url();
    const pageTitle = await page.title();
    const allButtons = await page.locator('button').allTextContents().catch(() => []);
    const allLinks = await page.locator('a').allTextContents().catch(() => []);

    console.log(`🔍 Final URL: ${finalUrl}`);
    console.log(`🔍 Page title: ${pageTitle}`);
    console.log('🔍 All buttons:', allButtons);
    console.log('🔍 All links:', allLinks.slice(0, 5));

    // Look for Google Sign In button with various selectors
    const buttonSelectors = [
      'button:has-text("Sign in with Google")',
      'button:has-text("Google")',
      'button:has-text("Sign in")',
      'button[type="submit"]',
      'button',
      'a:has-text("Sign in with Google")',
      'a:has-text("Google")',
      '[role="button"]:has-text("Google")',
      '.btn:has-text("Google")',
      '.google-signin-btn'
    ];

    let clickedButton = false;

    for (const selector of buttonSelectors) {
      try {
        const elements = await page.locator(selector).count();
        if (elements > 0) {
          const isVisible = await page.locator(selector).first().isVisible().catch(() => false);
          if (isVisible) {
            const buttonText = await page.locator(selector).first().textContent().catch(() => 'no text');
            console.log(`🎯 Found button with selector "${selector}": "${buttonText}"`);

            await page.locator(selector).first().click();
            console.log(`✅ Clicked button: ${buttonText}`);
            clickedButton = true;
            break;
          }
        }
      } catch (error) {
        console.log(`❌ Failed to click ${selector}:`, error.message);
      }
    }

    if (!clickedButton) {
      console.log('❌ Could not find any clickable Google Sign In button');
      console.log('🔍 Let me try clicking the first button available...');

      // Fallback: try clicking the first button on the page
      const firstButton = await page.locator('button').first().isVisible().catch(() => false);
      if (firstButton) {
        const buttonText = await page.locator('button').first().textContent().catch(() => 'no text');
        console.log(`🎯 Trying first button: "${buttonText}"`);
        await page.locator('button').first().click();
        clickedButton = true;
      } else {
        throw new Error('No buttons found on OAuth page');
      }
    }

    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Fill email
    await page.waitForSelector('input[type="email"], input[name="identifier"]', { timeout: 15000 });
    await page.fill('input[type="email"], input[name="identifier"]', testEmail);
    await page.click('button:has-text("Next"), #identifierNext');

    // Fill password
    await page.waitForTimeout(3000);
    await page.waitForSelector('input[type="password"], input[name="password"]', { timeout: 15000 });
    await page.fill('input[type="password"], input[name="password"]', testPassword);
    await page.click('button:has-text("Next"), button:has-text("Sign in"), #passwordNext');

    // Wait for redirect back to StationAdmin
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);

    console.log('✅ OAuth authentication completed');
  }
  
  test('authenticate and access Create New Donation button', async ({ page }) => {
    console.log('🚀 Test 1: Authentication and Create New Donation button access');
    
    try {
      // Authenticate
      await authenticateAdmin(page);
      
      // Navigate to donors page
      console.log('🎯 Navigating to /#/donors...');
      await page.goto('/#/donors');
      await page.waitForTimeout(3000);
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/donors-page-authenticated.png', fullPage: true });
      
      // Check for Create New Donation button
      const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
      
      console.log(`🎯 Create New Donation button visible: ${createButton}`);
      
      if (createButton) {
        console.log('🎉 SUCCESS! Authentication working and Create New Donation button found');
      } else {
        console.log('❌ Create New Donation button not found after authentication');
        
        // Debug what we see
        const buttons = await page.locator('button').allTextContents().catch(() => []);
        console.log('🔍 Available buttons:', buttons.slice(0, 10));
      }
      
    } catch (error) {
      console.log('❌ Authentication test failed:', error.message);
      await page.screenshot({ path: 'test-results/auth-test-error.png', fullPage: true });
    }
  });

  test.skip('one-time donation submission (DRAFT)', async ({ page }) => {
    console.log('🚀 Test 2: One-time donation submission');
    
    try {
      // Authenticate
      await authenticateAdmin(page);
      
      // Navigate to donors page and click Create New Donation
      await page.goto('/#/donors');
      await page.waitForTimeout(3000);
      
      const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
      if (!createButton) {
        throw new Error('Create New Donation button not found');
      }
      
      await page.click('button:has-text("Create New Donation")');
      await page.waitForTimeout(2000);
      
      // Force test mode
      await page.evaluate(() => {
        if (window.store) {
          window.store.dispatch({ 
            type: 'testMode/toggleTestMode', 
            payload: { value: true, route: 'api.staging.' }
          });
        }
        localStorage.setItem('testMode', 'true');
      });
      
      // Fill donation form
      await page.fill('input#amount', '50');
      await page.fill('input[name="firstName"]', 'Test');
      await page.fill('input[name="lastName"]', 'OneTime');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="phone"]', '5105551234');
      await page.fill('input[name="address1"]', '123 Test St');
      await page.fill('input[name="city"]', 'Berkeley');
      await page.selectOption('select[name="state"]', 'CA');
      await page.fill('input[name="zip"]', '94710');
      
      // Select credit card payment
      await page.click('input[type="radio"][value="card"]');
      
      // Fill credit card info
      await page.fill('input[name="cardNumber"]', '****************');
      await page.fill('input[name="securityCode"]', '123');
      await page.selectOption('select[name="expirationMonth"]', '12');
      await page.selectOption('select[name="expirationYear"]', '2025');
      
      console.log('✅ One-time donation form filled');
      
      // Take screenshot before submission
      await page.screenshot({ path: 'test-results/one-time-before-submit.png', fullPage: true });
      
      // Submit donation
      console.log('💳 Submitting one-time donation...');
      await page.click('button:has-text("Submit")');
      
      // Wait for result
      await page.waitForTimeout(5000);
      await page.screenshot({ path: 'test-results/one-time-after-submit.png', fullPage: true });
      
      console.log('🎉 One-time donation test completed');
      
    } catch (error) {
      console.log('❌ One-time donation test failed:', error.message);
      await page.screenshot({ path: 'test-results/one-time-error.png', fullPage: true });
    }
  });

  test.skip('monthly subscription submission (DRAFT)', async ({ page }) => {
    console.log('🚀 Test 3: Monthly subscription submission');
    
    try {
      // Authenticate
      await authenticateAdmin(page);
      
      // Navigate to donors page and click Create New Donation
      await page.goto('/#/donors');
      await page.waitForTimeout(3000);
      
      const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
      if (!createButton) {
        throw new Error('Create New Donation button not found');
      }
      
      await page.click('button:has-text("Create New Donation")');
      await page.waitForTimeout(2000);
      
      // Force test mode
      await page.evaluate(() => {
        if (window.store) {
          window.store.dispatch({ 
            type: 'testMode/toggleTestMode', 
            payload: { value: true, route: 'api.staging.' }
          });
        }
        localStorage.setItem('testMode', 'true');
      });
      
      // Click Monthly button
      await page.click('button:has-text("Monthly")');
      
      // Fill donation form
      await page.fill('input#amount', '25');
      await page.fill('input[name="firstName"]', 'Test');
      await page.fill('input[name="lastName"]', 'Monthly');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="phone"]', '5105551234');
      await page.fill('input[name="address1"]', '456 Monthly St');
      await page.fill('input[name="city"]', 'Oakland');
      await page.selectOption('select[name="state"]', 'CA');
      await page.fill('input[name="zip"]', '94612');
      
      // Select credit card payment
      await page.click('input[type="radio"][value="card"]');
      
      // Fill credit card info
      await page.fill('input[name="cardNumber"]', '****************');
      await page.fill('input[name="securityCode"]', '123');
      await page.selectOption('select[name="expirationMonth"]', '12');
      await page.selectOption('select[name="expirationYear"]', '2025');
      
      console.log('✅ Monthly subscription form filled');
      
      // Take screenshot before submission
      await page.screenshot({ path: 'test-results/monthly-before-submit.png', fullPage: true });
      
      // Submit subscription
      console.log('💳 Submitting monthly subscription...');
      await page.click('button:has-text("Submit")');
      
      // Wait for result
      await page.waitForTimeout(5000);
      await page.screenshot({ path: 'test-results/monthly-after-submit.png', fullPage: true });
      
      console.log('🎉 Monthly subscription test completed');
      
    } catch (error) {
      console.log('❌ Monthly subscription test failed:', error.message);
      await page.screenshot({ path: 'test-results/monthly-error.png', fullPage: true });
    }
  });
});
