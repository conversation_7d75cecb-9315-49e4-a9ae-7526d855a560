import { test, expect } from '@playwright/test';

test.describe('StationAdmin Donation Tests', () => {

  test('manual authentication setup', async ({ page }) => {
    console.log('🔧 Manual Authentication Setup');
    console.log('📋 Instructions:');
    console.log('1. <PERSON><PERSON>er window will open to StationAdmin');
    console.log('2. Complete OAuth manually if needed');
    console.log('3. Navigate to /#/donors');
    console.log('4. Verify Create New Donation button is visible');
    console.log('5. Test will save authentication state automatically');

    const testEmail = process.env.TEST_ADMIN_EMAIL;
    console.log(`🔑 Use credentials: ${testEmail}`);

    // Go to StationAdmin homepage
    await page.goto('/');
    await page.waitForTimeout(3000);

    // Use page.pause() for manual interaction
    console.log('⏸️  Browser paused for manual authentication...');
    console.log('💡 Complete OAuth and navigate to /#/donors, then resume test');

    await page.pause();

    // After manual interaction, verify we're authenticated
    console.log('🔍 Checking authentication state...');

    // Navigate to donors page
    await page.goto('/#/donors');
    await page.waitForTimeout(3000);

    // Check for Create New Donation button
    const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);

    if (createButton) {
      console.log('✅ Authentication successful! Create New Donation button found');

      // Save authentication state
      await page.context().storageState({ path: 'playwright/.auth/user.json' });
      console.log('💾 Authentication state saved to: playwright/.auth/user.json');

      // Test clicking the button
      await page.click('button:has-text("Create New Donation")');
      await page.waitForTimeout(2000);

      const hasForm = await page.locator('input#amount').isVisible().catch(() => false);
      if (hasForm) {
        console.log('🎉 SUCCESS! Donation form accessible');
      } else {
        console.log('⚠️ Button clicked but form not visible');
      }

    } else {
      console.log('❌ Authentication failed - Create New Donation button not found');

      // Debug what we see
      const buttons = await page.locator('button').allTextContents().catch(() => []);
      console.log('🔍 Available buttons:', buttons.slice(0, 10));

      throw new Error('Authentication verification failed');
    }
  });

  test.skip('access Create New Donation button with stored auth', async ({ page }) => {
    console.log('🚀 Test: Accessing Create New Donation button with pre-authenticated state');

    // Navigate to donors page (should be authenticated via storage state)
    console.log('🎯 Navigating to /#/donors...');
    await page.goto('/#/donors');
    await page.waitForTimeout(3000);

    // Take screenshot
    await page.screenshot({ path: 'test-results/donors-page-with-stored-auth.png', fullPage: true });

    // Check for Create New Donation button
    const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);

    console.log(`🎯 Create New Donation button visible: ${createButton}`);

    if (createButton) {
      console.log('🎉 SUCCESS! Pre-authenticated state working - Create New Donation button found');

      // Try clicking it to make sure it works
      await page.click('button:has-text("Create New Donation")');
      await page.waitForTimeout(2000);

      const hasForm = await page.locator('input#amount').isVisible().catch(() => false);
      console.log(`💰 Donation form visible after click: ${hasForm}`);

      if (hasForm) {
        console.log('🎉 COMPLETE SUCCESS! Can access donation form with stored authentication');

        // Take screenshot of the form
        await page.screenshot({ path: 'test-results/donation-form-accessible.png', fullPage: true });
      } else {
        console.log('❌ Create New Donation button clicked but form not visible');
      }

    } else {
      console.log('❌ Create New Donation button not found - authentication state may be invalid');

      // Debug what we see
      const buttons = await page.locator('button').allTextContents().catch(() => []);
      const links = await page.locator('a').allTextContents().catch(() => []);
      console.log('🔍 Available buttons:', buttons.slice(0, 10));
      console.log('🔍 Available links:', links.slice(0, 5));

      // Check if we need to re-authenticate
      const hasSignOut = await page.locator('a:has-text("Sign Out")').isVisible().catch(() => false);
      const hasGoogleSignIn = await page.locator('button:has-text("Sign in with Google")').isVisible().catch(() => false);

      console.log(`🔍 Sign Out visible: ${hasSignOut}`);
      console.log(`🔍 Google Sign In visible: ${hasGoogleSignIn}`);

      if (hasGoogleSignIn) {
        console.log('⚠️ Authentication state expired - need to re-run setup');
      }
    }
  });

  test.skip('one-time donation submission (DRAFT)', async ({ page }) => {
    console.log('🚀 Test 2: One-time donation submission');
    
    try {
      // Authenticate
      await authenticateAdmin(page);
      
      // Navigate to donors page and click Create New Donation
      await page.goto('/#/donors');
      await page.waitForTimeout(3000);
      
      const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
      if (!createButton) {
        throw new Error('Create New Donation button not found');
      }
      
      await page.click('button:has-text("Create New Donation")');
      await page.waitForTimeout(2000);
      
      // Force test mode
      await page.evaluate(() => {
        if (window.store) {
          window.store.dispatch({ 
            type: 'testMode/toggleTestMode', 
            payload: { value: true, route: 'api.staging.' }
          });
        }
        localStorage.setItem('testMode', 'true');
      });
      
      // Fill donation form
      await page.fill('input#amount', '50');
      await page.fill('input[name="firstName"]', 'Test');
      await page.fill('input[name="lastName"]', 'OneTime');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="phone"]', '5105551234');
      await page.fill('input[name="address1"]', '123 Test St');
      await page.fill('input[name="city"]', 'Berkeley');
      await page.selectOption('select[name="state"]', 'CA');
      await page.fill('input[name="zip"]', '94710');
      
      // Select credit card payment
      await page.click('input[type="radio"][value="card"]');
      
      // Fill credit card info
      await page.fill('input[name="cardNumber"]', '****************');
      await page.fill('input[name="securityCode"]', '123');
      await page.selectOption('select[name="expirationMonth"]', '12');
      await page.selectOption('select[name="expirationYear"]', '2025');
      
      console.log('✅ One-time donation form filled');
      
      // Take screenshot before submission
      await page.screenshot({ path: 'test-results/one-time-before-submit.png', fullPage: true });
      
      // Submit donation
      console.log('💳 Submitting one-time donation...');
      await page.click('button:has-text("Submit")');
      
      // Wait for result
      await page.waitForTimeout(5000);
      await page.screenshot({ path: 'test-results/one-time-after-submit.png', fullPage: true });
      
      console.log('🎉 One-time donation test completed');
      
    } catch (error) {
      console.log('❌ One-time donation test failed:', error.message);
      await page.screenshot({ path: 'test-results/one-time-error.png', fullPage: true });
    }
  });

  test.skip('monthly subscription submission (DRAFT)', async ({ page }) => {
    console.log('🚀 Test 3: Monthly subscription submission');
    
    try {
      // Authenticate
      await authenticateAdmin(page);
      
      // Navigate to donors page and click Create New Donation
      await page.goto('/#/donors');
      await page.waitForTimeout(3000);
      
      const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
      if (!createButton) {
        throw new Error('Create New Donation button not found');
      }
      
      await page.click('button:has-text("Create New Donation")');
      await page.waitForTimeout(2000);
      
      // Force test mode
      await page.evaluate(() => {
        if (window.store) {
          window.store.dispatch({ 
            type: 'testMode/toggleTestMode', 
            payload: { value: true, route: 'api.staging.' }
          });
        }
        localStorage.setItem('testMode', 'true');
      });
      
      // Click Monthly button
      await page.click('button:has-text("Monthly")');
      
      // Fill donation form
      await page.fill('input#amount', '25');
      await page.fill('input[name="firstName"]', 'Test');
      await page.fill('input[name="lastName"]', 'Monthly');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="phone"]', '5105551234');
      await page.fill('input[name="address1"]', '456 Monthly St');
      await page.fill('input[name="city"]', 'Oakland');
      await page.selectOption('select[name="state"]', 'CA');
      await page.fill('input[name="zip"]', '94612');
      
      // Select credit card payment
      await page.click('input[type="radio"][value="card"]');
      
      // Fill credit card info
      await page.fill('input[name="cardNumber"]', '****************');
      await page.fill('input[name="securityCode"]', '123');
      await page.selectOption('select[name="expirationMonth"]', '12');
      await page.selectOption('select[name="expirationYear"]', '2025');
      
      console.log('✅ Monthly subscription form filled');
      
      // Take screenshot before submission
      await page.screenshot({ path: 'test-results/monthly-before-submit.png', fullPage: true });
      
      // Submit subscription
      console.log('💳 Submitting monthly subscription...');
      await page.click('button:has-text("Submit")');
      
      // Wait for result
      await page.waitForTimeout(5000);
      await page.screenshot({ path: 'test-results/monthly-after-submit.png', fullPage: true });
      
      console.log('🎉 Monthly subscription test completed');
      
    } catch (error) {
      console.log('❌ Monthly subscription test failed:', error.message);
      await page.screenshot({ path: 'test-results/monthly-error.png', fullPage: true });
    }
  });
});
