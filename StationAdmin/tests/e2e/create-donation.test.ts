import { test, expect } from '@playwright/test';

test.describe('StationAdmin Donation Tests', () => {

  // Helper function for manual authentication setup
  async function setupAuthentication(page) {
    const testEmail = process.env.TEST_ADMIN_EMAIL;
    const testPassword = process.env.TEST_ADMIN_PASSWORD;

    if (!testEmail || !testPassword) {
      throw new Error('No admin credentials in .env file');
    }

    console.log(`🔑 Setting up authentication for: ${testEmail}`);

    // Go to homepage first
    await page.goto('/');
    await page.waitForTimeout(2000);

    // Take screenshot to see current state
    await page.screenshot({ path: 'test-results/homepage-state.png', fullPage: true });

    // Debug what's on the page
    const currentUrl = page.url();
    const pageTitle = await page.title();
    const allButtons = await page.locator('button').allTextContents().catch(() => []);
    const allLinks = await page.locator('a').allTextContents().catch(() => []);

    console.log(`🔍 Current URL: ${currentUrl}`);
    console.log(`🔍 Page title: ${pageTitle}`);
    console.log('🔍 All buttons:', allButtons);
    console.log('🔍 All links:', allLinks.slice(0, 10));

    // Check if already authenticated
    const hasSignOut = await page.locator('a:has-text("Sign Out")').isVisible().catch(() => false);
    const hasCreateButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);

    console.log(`🔍 Sign Out visible: ${hasSignOut}`);
    console.log(`🔍 Create New Donation visible: ${hasCreateButton}`);

    if (hasCreateButton) {
      console.log('✅ Already authenticated and can access donation features');
      return true;
    }

    if (hasSignOut) {
      console.log('🔄 Found existing session, clicking Sign Out to start fresh...');
      await page.click('a:has-text("Sign Out")');
      await page.waitForTimeout(5000);

      // Take screenshot after sign out
      await page.screenshot({ path: 'test-results/after-signout-manual.png', fullPage: true });

      // Check what we see after sign out
      const afterSignOutButtons = await page.locator('button').allTextContents().catch(() => []);
      console.log('🔍 Buttons after sign out:', afterSignOutButtons);
    }

    // Look for Google Sign In button
    const hasGoogleSignIn = await page.locator('button:has-text("Sign in with Google")').isVisible().catch(() => false);

    console.log(`🔍 Google Sign In button visible: ${hasGoogleSignIn}`);

    if (hasGoogleSignIn) {
      console.log('✅ Found Google Sign In button');

      // For now, let's use a manual approach
      console.log('🔧 MANUAL AUTHENTICATION REQUIRED:');
      console.log('1. The browser window should be open');
      console.log('2. Click "Sign in with Google"');
      console.log(`3. Login with: ${testEmail}`);
      console.log('4. Complete the OAuth flow');
      console.log('5. The test will continue automatically...');

      // Wait for authentication to complete (user does it manually)
      console.log('⏳ Waiting for manual authentication...');

      // Wait for either the Create New Donation button or donors page
      try {
        await page.waitForFunction(() => {
          const createButton = document.querySelector('button:has-text("Create New Donation")');
          const donorsPage = window.location.href.includes('/#/donors');
          return createButton || donorsPage;
        }, { timeout: 60000 });

        console.log('✅ Authentication completed!');
        return true;
      } catch (error) {
        console.log('❌ Authentication timeout - please complete OAuth manually');
        return false;
      }
    } else {
      console.log('❌ No Google Sign In button found');
      console.log('🔧 MANUAL WORKAROUND:');
      console.log('1. The browser window should be open');
      console.log('2. Manually navigate to the OAuth page or click Sign Out');
      console.log('3. Complete the Google OAuth flow');
      console.log(`4. Login with: ${testEmail}`);
      console.log('5. Navigate back to StationAdmin');
      console.log('6. The test will continue in 60 seconds...');

      // Give user time to manually authenticate
      console.log('⏳ Waiting 60 seconds for manual authentication...');
      await page.waitForTimeout(60000);

      // Check if authentication worked
      await page.goto('/#/donors');
      await page.waitForTimeout(3000);

      const createButtonAfterManual = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
      if (createButtonAfterManual) {
        console.log('✅ Manual authentication successful!');
        return true;
      } else {
        console.log('❌ Manual authentication failed or incomplete');
        return false;
      }
    }
  }

  test('authenticate and access Create New Donation button', async ({ page }) => {
    console.log('🚀 Test 1: Manual Authentication and Create New Donation button access');

    try {
      // Setup authentication (manual process)
      const authSuccess = await setupAuthentication(page);

      if (!authSuccess) {
        console.log('❌ Authentication setup failed');
        return;
      }

      // Navigate to donors page
      console.log('🎯 Navigating to /#/donors...');
      await page.goto('/#/donors');
      await page.waitForTimeout(3000);

      // Take screenshot
      await page.screenshot({ path: 'test-results/donors-page-authenticated.png', fullPage: true });

      // Check for Create New Donation button
      const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);

      console.log(`🎯 Create New Donation button visible: ${createButton}`);

      if (createButton) {
        console.log('🎉 SUCCESS! Authentication working and Create New Donation button found');

        // Try clicking it to make sure it works
        await page.click('button:has-text("Create New Donation")');
        await page.waitForTimeout(2000);

        const hasForm = await page.locator('input#amount').isVisible().catch(() => false);
        console.log(`💰 Donation form visible after click: ${hasForm}`);

        if (hasForm) {
          console.log('🎉 COMPLETE SUCCESS! Can access donation form');
        }

      } else {
        console.log('❌ Create New Donation button not found after authentication');

        // Debug what we see
        const buttons = await page.locator('button').allTextContents().catch(() => []);
        console.log('🔍 Available buttons:', buttons.slice(0, 10));
      }

    } catch (error) {
      console.log('❌ Authentication test failed:', error.message);
      await page.screenshot({ path: 'test-results/auth-test-error.png', fullPage: true });
    }
  });

  test.skip('one-time donation submission (DRAFT)', async ({ page }) => {
    console.log('🚀 Test 2: One-time donation submission');
    
    try {
      // Authenticate
      await authenticateAdmin(page);
      
      // Navigate to donors page and click Create New Donation
      await page.goto('/#/donors');
      await page.waitForTimeout(3000);
      
      const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
      if (!createButton) {
        throw new Error('Create New Donation button not found');
      }
      
      await page.click('button:has-text("Create New Donation")');
      await page.waitForTimeout(2000);
      
      // Force test mode
      await page.evaluate(() => {
        if (window.store) {
          window.store.dispatch({ 
            type: 'testMode/toggleTestMode', 
            payload: { value: true, route: 'api.staging.' }
          });
        }
        localStorage.setItem('testMode', 'true');
      });
      
      // Fill donation form
      await page.fill('input#amount', '50');
      await page.fill('input[name="firstName"]', 'Test');
      await page.fill('input[name="lastName"]', 'OneTime');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="phone"]', '5105551234');
      await page.fill('input[name="address1"]', '123 Test St');
      await page.fill('input[name="city"]', 'Berkeley');
      await page.selectOption('select[name="state"]', 'CA');
      await page.fill('input[name="zip"]', '94710');
      
      // Select credit card payment
      await page.click('input[type="radio"][value="card"]');
      
      // Fill credit card info
      await page.fill('input[name="cardNumber"]', '****************');
      await page.fill('input[name="securityCode"]', '123');
      await page.selectOption('select[name="expirationMonth"]', '12');
      await page.selectOption('select[name="expirationYear"]', '2025');
      
      console.log('✅ One-time donation form filled');
      
      // Take screenshot before submission
      await page.screenshot({ path: 'test-results/one-time-before-submit.png', fullPage: true });
      
      // Submit donation
      console.log('💳 Submitting one-time donation...');
      await page.click('button:has-text("Submit")');
      
      // Wait for result
      await page.waitForTimeout(5000);
      await page.screenshot({ path: 'test-results/one-time-after-submit.png', fullPage: true });
      
      console.log('🎉 One-time donation test completed');
      
    } catch (error) {
      console.log('❌ One-time donation test failed:', error.message);
      await page.screenshot({ path: 'test-results/one-time-error.png', fullPage: true });
    }
  });

  test.skip('monthly subscription submission (DRAFT)', async ({ page }) => {
    console.log('🚀 Test 3: Monthly subscription submission');
    
    try {
      // Authenticate
      await authenticateAdmin(page);
      
      // Navigate to donors page and click Create New Donation
      await page.goto('/#/donors');
      await page.waitForTimeout(3000);
      
      const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
      if (!createButton) {
        throw new Error('Create New Donation button not found');
      }
      
      await page.click('button:has-text("Create New Donation")');
      await page.waitForTimeout(2000);
      
      // Force test mode
      await page.evaluate(() => {
        if (window.store) {
          window.store.dispatch({ 
            type: 'testMode/toggleTestMode', 
            payload: { value: true, route: 'api.staging.' }
          });
        }
        localStorage.setItem('testMode', 'true');
      });
      
      // Click Monthly button
      await page.click('button:has-text("Monthly")');
      
      // Fill donation form
      await page.fill('input#amount', '25');
      await page.fill('input[name="firstName"]', 'Test');
      await page.fill('input[name="lastName"]', 'Monthly');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="phone"]', '5105551234');
      await page.fill('input[name="address1"]', '456 Monthly St');
      await page.fill('input[name="city"]', 'Oakland');
      await page.selectOption('select[name="state"]', 'CA');
      await page.fill('input[name="zip"]', '94612');
      
      // Select credit card payment
      await page.click('input[type="radio"][value="card"]');
      
      // Fill credit card info
      await page.fill('input[name="cardNumber"]', '****************');
      await page.fill('input[name="securityCode"]', '123');
      await page.selectOption('select[name="expirationMonth"]', '12');
      await page.selectOption('select[name="expirationYear"]', '2025');
      
      console.log('✅ Monthly subscription form filled');
      
      // Take screenshot before submission
      await page.screenshot({ path: 'test-results/monthly-before-submit.png', fullPage: true });
      
      // Submit subscription
      console.log('💳 Submitting monthly subscription...');
      await page.click('button:has-text("Submit")');
      
      // Wait for result
      await page.waitForTimeout(5000);
      await page.screenshot({ path: 'test-results/monthly-after-submit.png', fullPage: true });
      
      console.log('🎉 Monthly subscription test completed');
      
    } catch (error) {
      console.log('❌ Monthly subscription test failed:', error.message);
      await page.screenshot({ path: 'test-results/monthly-error.png', fullPage: true });
    }
  });
});
