import { test, expect } from '@playwright/test';

test.describe('Simple StationAdmin Authentication Test', () => {
  
  test('get to Create New Donation button', async ({ page }) => {
    console.log('🚀 Testing: Sign Out → OAuth → Create New Donation button');
    
    // Get admin credentials
    const testEmail = process.env.TEST_ADMIN_EMAIL;
    const testPassword = process.env.TEST_ADMIN_PASSWORD;
    
    if (!testEmail || !testPassword) {
      console.log('❌ No admin credentials in .env file');
      console.log('💡 Add TEST_ADMIN_EMAIL and TEST_ADMIN_PASSWORD to .env');
      return;
    }
    
    console.log(`🔑 Will authenticate with: ${testEmail}`);
    
    // Step 1: Go to homepage
    console.log('📍 Step 1: Going to homepage...');
    await page.goto('/');
    await page.waitForTimeout(2000);
    
    // Step 2: Click Sign Out to trigger OAuth
    console.log('📍 Step 2: Clicking Sign Out to trigger OAuth...');
    await page.click('a:has-text("Sign Out")');
    await page.waitForTimeout(3000);
    
    // Take screenshot after Sign Out
    await page.screenshot({ path: 'test-results/after-signout.png', fullPage: true });
    
    // Step 3: Check if we see OAuth login page
    const hasLoginButton = await page.locator('button:has-text("Sign in with Google")').isVisible().catch(() => false);
    console.log(`📍 Step 3: OAuth login button visible: ${hasLoginButton}`);
    
    if (!hasLoginButton) {
      console.log('❌ Sign Out did not trigger OAuth login page');
      return;
    }
    
    // Step 4: Do OAuth flow
    console.log('📍 Step 4: Starting OAuth flow...');
    
    try {
      // Click Google Sign In
      await page.click('button:has-text("Sign in with Google")');
      console.log('✅ Clicked Google Sign In');
      
      // Wait for Google OAuth page
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Fill email
      await page.waitForSelector('input[type="email"], input[name="identifier"]', { timeout: 15000 });
      await page.fill('input[type="email"], input[name="identifier"]', testEmail);
      await page.click('button:has-text("Next"), #identifierNext');
      console.log('✅ Filled email and clicked Next');
      
      // Fill password
      await page.waitForTimeout(3000);
      await page.waitForSelector('input[type="password"], input[name="password"]', { timeout: 15000 });
      await page.fill('input[type="password"], input[name="password"]', testPassword);
      await page.click('button:has-text("Next"), button:has-text("Sign in"), #passwordNext');
      console.log('✅ Filled password and clicked Sign In');
      
      // Wait for redirect back to StationAdmin
      console.log('⏳ Waiting for redirect to StationAdmin...');
      await page.waitForTimeout(8000);
      
      console.log('✅ OAuth completed');
      
    } catch (error) {
      console.log('❌ OAuth failed:', error.message);
      await page.screenshot({ path: 'test-results/oauth-error.png', fullPage: true });
      return;
    }
    
    // Step 5: Navigate to donors page
    console.log('📍 Step 5: Navigating to /#/donors...');
    await page.goto('/#/donors');
    await page.waitForTimeout(3000);
    
    // Take screenshot of donors page
    await page.screenshot({ path: 'test-results/donors-page.png', fullPage: true });
    
    // Step 6: Check for Create New Donation button
    console.log('📍 Step 6: Looking for Create New Donation button...');
    const createButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
    
    console.log(`🎯 Create New Donation button visible: ${createButton}`);
    
    if (createButton) {
      console.log('🎉 SUCCESS! Found Create New Donation button');
      console.log('✅ Authentication flow working correctly');
    } else {
      console.log('❌ Create New Donation button not found');
      
      // Debug what we see
      const buttons = await page.locator('button').allTextContents().catch(() => []);
      const links = await page.locator('a').allTextContents().catch(() => []);
      console.log('🔍 Available buttons:', buttons.slice(0, 10));
      console.log('🔍 Available links:', links.slice(0, 10));
    }
  });
});
