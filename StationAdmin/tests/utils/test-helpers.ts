// <PERSON>IM<PERSON><PERSON>IED TEST HELPERS - FOCUS ON STRIPE CC PAYMENTS ONLY

import { Page } from '@playwright/test';

// Simple test data - no complex interfaces
export const SIMPLE_TEST_DONOR = {
  firstName: 'Test',
  lastName: 'Donor',
  email: '<EMAIL>',
  phone: '5105551234',
  address1: '123 Test St',
  city: 'Berkeley',
  state: 'CA',
  zip: '94710'
};

export const SIMPLE_TEST_CARD = {
  cardNumber: '****************',
  securityCode: '123',
  expirationMonth: '12',
  expirationYear: '2025'
};

// REMOVED COMPLEX AUTHENTICATION - TESTS WILL RUN DIRECTLY AGAINST LOCALHOST

/**
 * Fill donor information form using real StationAdmin selectors
 */
export async function fillDonorInformation(page: Page, donor: TestDonor = DEFAULT_TEST_DONOR): Promise<void> {
  // Wait for form to be visible
  await page.waitForSelector('input[name="firstName"]', { state: 'visible' });

  // Fill basic contact information
  await page.fill('input[name="firstName"]', donor.firstName);
  await page.fill('input[name="lastName"]', donor.lastName);
  await page.fill('input[name="email"]', donor.email);
  await page.fill('input[name="phone"]', donor.phone);

  // Fill address information
  await page.fill('input[name="address1"]', donor.address1);

  if (donor.address2) {
    await page.fill('input[name="address2"]', donor.address2);
  }

  await page.fill('input[name="city"]', donor.city);
  await page.selectOption('select[name="state"]', donor.state);
  await page.fill('input[name="zip"]', donor.zip);
  await page.selectOption('select[name="country"]', donor.country);
}

/**
 * Fill credit card information using real StationAdmin selectors
 */
export async function fillCreditCardInformation(page: Page, card: TestCard = DEFAULT_TEST_CARD): Promise<void> {
  // Select credit card payment type using radio button
  await page.click('input[type="radio"][value="card"]');

  // Wait for card fields to be visible
  await page.waitForSelector('input[name="cardNumber"]', { state: 'visible' });

  // Fill card information using the actual field names from CardPaymentForm.js
  await page.fill('input[name="cardNumber"]', card.cardNumber);
  await page.fill('input[name="securityCode"]', card.securityCode);
  await page.selectOption('select[name="expirationMonth"]', card.expirationMonth);
  await page.selectOption('select[name="expirationYear"]', card.expirationYear);
}

// Removed check payment functionality - focusing only on Stripe credit card payments

/**
 * Set donation amount using real AmountSelector component
 */
export async function setDonationAmount(page: Page, amount: string): Promise<void> {
  await page.fill('input#amount', amount);
}

/**
 * Set donation type (One-Time or Monthly) using real DonationType component
 */
export async function setDonationType(page: Page, type: 'one-time' | 'monthly'): Promise<void> {
  if (type === 'monthly') {
    // Click the Monthly toggle button
    await page.click('button:has-text("Monthly")');
  } else {
    // Click the One-Time toggle button
    await page.click('button:has-text("One-Time")');
  }

  // Wait a moment for the state to update
  await page.waitForTimeout(500);
}

/**
 * Submit donation form and handle confirmation using real SubmitModal
 */
export async function submitDonation(page: Page): Promise<void> {
  // Click the submit button (likely in SubmitCart component)
  await page.click('button:has-text("Submit"), button:has-text("Create Donation"), .btn-primary:has-text("Submit")');

  // Wait for confirmation modal to appear
  await page.waitForSelector('.modal:has-text("Please review and confirm"), .modal:has-text("confirm")', { state: 'visible' });
}

/**
 * Confirm donation in the confirmation modal
 */
export async function confirmDonation(page: Page): Promise<void> {
  // Click the confirm button in the modal
  await page.click('.modal button:has-text("Confirm"), .modal button:has-text("Submit"), .modal .btn-primary');

  // Wait for processing modal or spinner
  await page.waitForSelector('.modal:has-text("Processing"), .spinner, .loading', { state: 'visible', timeout: 5000 }).catch(() => {
    // Processing might be very fast, so don't fail if we don't see it
  });
}

/**
 * Wait for donation success
 */
export async function waitForDonationSuccess(page: Page): Promise<void> {
  // Look for success indicators - checkmark, success message, or redirect
  await page.waitForSelector(
    '.modal:has-text("Success"), .modal:has-text("created"), img[src*="checkmark"], .alert-success, h2:has-text("Success")',
    { timeout: 15000 }
  );
}

/**
 * Complete full credit card donation flow
 */
export async function completeCreditCardDonationFlow(page: Page, options: {
  amount: string;
  type: 'one-time' | 'monthly';
  donor?: TestDonor;
  card?: TestCard;
}): Promise<void> {
  // Navigate to create donation page
  await page.goto('/donors/create-donation');

  // Set donation details
  await setDonationAmount(page, options.amount);
  await setDonationType(page, options.type);

  // Fill donor information
  await fillDonorInformation(page, options.donor);

  // Fill credit card information
  await fillCreditCardInformation(page, options.card);

  // Submit and confirm
  await submitDonation(page);
  await confirmDonation(page);
  await waitForDonationSuccess(page);
}

/**
 * Handle Google OAuth authentication flow
 */
async function handleGoogleOAuth(page: Page, email: string, password: string): Promise<void> {
  try {
    // Wait for Google login page to load
    await page.waitForSelector('input[type="email"], input[name="identifier"]', { timeout: 10000 });

    // Fill in email
    await page.fill('input[type="email"], input[name="identifier"]', email);
    await page.click('button:has-text("Next"), #identifierNext');

    // Wait for password field
    await page.waitForSelector('input[type="password"], input[name="password"]', { timeout: 10000 });

    // Fill in password
    await page.fill('input[type="password"], input[name="password"]', password);
    await page.click('button:has-text("Next"), #passwordNext');

    // Wait for potential 2FA or consent screens
    await page.waitForTimeout(2000);

    // Handle consent screen if it appears
    const consentButton = page.locator('button:has-text("Continue"), button:has-text("Allow"), button:has-text("Accept")');
    const hasConsent = await consentButton.isVisible().catch(() => false);

    if (hasConsent) {
      await consentButton.click();
    }

  } catch (error) {
    console.error('❌ Google OAuth flow failed:', error);
    throw new Error(`Google OAuth authentication failed: ${error.message}`);
  }
}

/**
 * Enable test mode for Stripe testing
 */
async function enableTestMode(page: Page): Promise<void> {
  await page.evaluate(() => {
    // Enable test mode in localStorage
    localStorage.setItem('testMode', 'true');

    // Force test mode in Redux store if available
    if (window.store) {
      window.store.dispatch({ type: 'testMode/toggleTestMode', payload: true });
    }
  });

  console.log('🧪 Test mode enabled for Stripe testing');
}

/**
 * Generate unique test data to avoid conflicts
 */
export function generateUniqueTestDonor(baseDonor: TestDonor = DEFAULT_TEST_DONOR): TestDonor {
  const timestamp = Date.now();
  return {
    ...baseDonor,
    firstName: `${baseDonor.firstName}${timestamp}`,
    email: `test.${timestamp}@example.com`
  };
}
