{"$schema": "https://json.schemastore.org/tsconfig", "display": "Create React App", "compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "target": "es2015", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "react-jsx", "moduleResolution": "node", "noEmit": true, "noFallthroughCasesInSwitch": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true}}