# Installing Ansible

## Ubuntu
<code>
apt-get install ansible
apt-get install sshpass
</code>

## OSX (10.9.3) host:

Checkout archive
<code>
<NAME_EMAIL>:pozar/ansible.git
cd ansible
</code>

Install XCode with "Command Line Tools"
* Verify with "pkgutil --pkg-info=com.apple.pkg.CLTools_Executables"

Or force the Command Line Tools install if it is too old...
<code>
sudo rm -rf /Library/Developer/CommandLineTools
sudo xcode-select --install
</code>

Install Ansible as per the directions here...
https://docs.ansible.com/ansible/latest/installation_guide/intro_installation.html#installing-ansible-on-macos
https://docs.ansible.com/ansible/latest/installation_guide/intro_installation.html#from-pip

Install PIP via easy_install...
<code>
sudo easy_install pip
</code>

Once pip is installed, you can install Ansible:
<code>
sudo pip install ansible
brew install hudochenkov/sshpass/sshpass
</code>

Via PIP, the ansible binaries will be installed someplace like 
/usr/local/bin/ansible 
Add the directory to PATH...
<code>
export PATH=$PATH:/usr/local/bin/
</code>

Don't install both brew and pip methods as you will have multiple installs of ansible

Install the rest of the requirements
<code>
pip install --upgrade -r requirements.txt
bash (if you are not running it already)
source env-setup
</code>

## Run Ansible:

For all hosts:
<code>
ansible-playbook -i inventory playbook.yml
</code>

Or for a single host and using passwords over SSH: 
<code>
ansible-playbook -i inventory playbook.yml -l ansible.local --ask-pass
</code>