# KPFA Ansible

## Introduction

This is the provisioning system for the KPFA Linux servers

## Installation

Please read the INSTALL.md document.

Once Ansible is running, install 3rd party packages using Ansible Galaxy:

<code>ansible-galaxy install -r requirements.yml</code>

This is necessary for the Libretime roles to function.

## Running Ansible against KPFA infrastructure

Hostnames will typically have a ".local" appended to them as that is how they
will show up in mDNS. The ansible configs will also use this. So the host
"ansible" will be "ansible.local".

To run Ansible against a single host:

In this case we are going to run ansible against an UN-provisioned VM so you
will need to give it a password something like "XXXXX".

<code>ansible-playbook -i inventory playbook.yml -l ansible --ask-pass</code>

or without the need for a password such as having an ssh key already deployed.

<code>ansible-playbook -i inventory playbook.yml -l ansible.local</code>

If your username on the VM is different than your local username, you can pass a username when running the playbook:

`ansible-playbook -i inventory playbook.yml -l ansible -u your-remote-username`

To add a host -

- Update /etc/hosts or some other way that your host can resolve the IP address of the hostname
- Add the hostname to inventory/hosts
- Check if /etc/sudoers file on the new VM has a line like:
  <code>%sudo ALL=(ALL:ALL) NOPASSWD:ALL</code>

## Roles

### common

The <code>common</code> role will be used on all hosts to provide baseline configuration.

This will:

- set born on date
- Message of the Day (MOTD) welcome banner
- install bash, zsh, sudo
- add users & ssh keys

### nginx_certbot

This role sets up Nginx with Let's Encrypt SSL certificates. The role is application-agnostic and can be customized for any web application. Key features include:

- Automated SSL certificate management with Let's Encrypt
- Secure Nginx configuration with modern SSL settings
- Support for both TCP ports and Unix sockets
- Automatic HTTP to HTTPS redirection
- Application-specific configuration via the `app_specific_https_config` variable
- Zero-downtime certificate provisioning with automatic bootstrapping

The role handles the complete SSL setup lifecycle:

1. When certificates don't exist, it creates a temporary HTTP configuration that handles ACME challenges
2. It then obtains certificates through certbot using the webroot method (keeping nginx running)
3. Once certificates are available, it deploys the full HTTP+HTTPS configuration

This solution avoids the chicken-and-egg problem where nginx needs certificates to start but certbot needs nginx running to validate the domain.

See [roles/nginx_certbot/README.md](roles/nginx_certbot/README.md) for detailed documentation.

#### Basic Usage

```yaml
- hosts: webserver
  become: yes
  roles:
    - role: nginx_certbot
      vars:
        domain_name: "myapp.example.com"
        certbot_admin_email: "<EMAIL>"
```

#### Using with LibreTime

```yaml
- hosts: libretime.example.com
  become: yes
  roles:
    - role: nginx_certbot
      vars:
        domain_name: "libretime.example.com"
        certbot_admin_email: "<EMAIL>"
        create_proxy_params: true
        app_listen_port: "{{ libretime_listen_port }}"
        app_specific_https_config: "{{ lookup('template', 'templates/nginx/libretime-https.j2') }}"
      tags: ["nginx", "letsencrypt", "certbot", "ssl"]
```

The LibreTime configuration uses a template file `templates/nginx/libretime-https.j2` that sets up:

- Proxy configuration to the LibreTime application
- API endpoints for the LibreTime API
- Media file serving
- Proper headers for reverse proxy
- Request size limits for uploads

#### Using with Application-Specific Configuration

```yaml
- hosts: webserver
  become: yes
  roles:
    - role: nginx_certbot
      vars:
        domain_name: "myapp.example.com"
        certbot_admin_email: "<EMAIL>"
        create_proxy_params: true
        app_specific_https_config: |
          # Web root configuration
          root /var/www/myapp;
          index index.php index.html;

          # PHP handler
          location ~ \.php$ {
            include fastcgi_params;
            fastcgi_pass unix:/run/php/php-fpm.sock;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
          }

          location / {
            try_files $uri $uri/ /index.php$is_args$args;
          }
```

#### Running with Specific Tags

To run only the Nginx and SSL configuration:

```bash
ansible-playbook -i inventory/hosts playbook.yml --tags nginx,ssl -u username
```

To run with Let's Encrypt staging (test) mode:

```bash
ansible-playbook -i inventory/hosts playbook.yml --tags nginx,ssl -u username --extra-vars "certbot_staging=true"
```

## Secrets

You can use Ansible Vault to encrypt sensitive credentials.

1. Retrieve the vault password from [Bitwarden](https://vault.bitwarden.com/#/vault?organizationId=d726e772-1d9e-406e-b334-afa501203ae7&itemId=1569d426-d7bf-4dc1-8c65-b1fb0075788f&action=view)
2. Save the vault password in this repository's root directory as <code>.vault-password.txt</code>
3. To encrypt strings within a host variable file, first run:

<code>ansible-vault encrypt_string 'secret_string_goes_here' --name 'insert_secure_variable_name' --vault-password-file .vault-password.txt</code>

4. Then, copy the encrypted output to the relevant host variable file.
5. To use this password file when running the playbook, pass it like so:

<code>ansible-playbook -i inventory playbook.yml -l hostname -u your-remote-username --vault-password-file .vault-password.txt</code>

## SSH Agent Forwarding for Git Operations

The API deployment tasks require SSH agent forwarding to clone repositories from private Git servers. This allows the remote server to use your local SSH keys for authentication without copying them to the server.

### Prerequisites

1. Ensure SSH agent forwarding is enabled in `ansible.cfg`:

   ```
   [ssh_connection]
   ssh_args = -o ForwardAgent=yes
   ```

2. Start the SSH agent and add your key before running Ansible:

   ```bash
   eval $(ssh-agent)
   ssh-add ~/.ssh/id_rsa  # or path to the specific key needed for git.kpfa.org
   ```

3. Verify your key is loaded:

   ```bash
   ssh-add -l
   ```

### Running the Playbook with SSH Forwarding

For API deployments, use:

```bash
eval $(ssh-agent) && ssh-add ~/.ssh/id_rsa && ansible-playbook playbook.yml -l api.staging.kpfa.org --tags deploy -u paloma --vault-password-file .vault-password.txt
```

This one-liner will:

1. Start the SSH agent
2. Add your SSH key to the agent
3. Run the playbook using the paloma user (which has Git access)
4. Forward your SSH credentials to the remote server for Git operations

### Troubleshooting

If you encounter "Permission denied (publickey)" errors:

1. Verify your SSH key is added to the Git server (git.kpfa.org)
2. Ensure your local SSH agent has the correct key loaded
3. Check if `ForwardAgent yes` is properly set in ansible.cfg
4. Try running with the `-vvv` flag for verbose output:

   ```bash
   ansible-playbook -vvv playbook.yml -l api.staging.kpfa.org --tags deploy -u paloma
   ```

## SNMP Configuration

To deploy SNMP monitoring configuration to a specific host:

```bash
ansible-playbook -i inventory/hosts playbook.yml --limit api.staging.kpfa.org --tags "snmpd" -u your-username
```

This will install and configure the SNMP daemon on the target host with the predefined configuration.

## Notes
- PostgreSQL: Legacy applications use the custom `postgresql` role, while new applications use `geerlingguy.postgresql`.
