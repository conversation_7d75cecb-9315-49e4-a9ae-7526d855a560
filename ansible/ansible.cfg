[defaults]
ansible_managed = %a %b %d %H:%M:%S %z %Y
filter_plugins = filter_plugins
allow_world_readable_tmpfiles = true
inventory = inventory/hosts
nocows = 1
forks = 50
# stdout_callback = actionable
host_key_checking = False
vault_password_file = .vault-password.txt

[local]
localhost ansible_python_interpreter=/usr/local/bin/python/2.7.8

[ssh_connection]
ssh_args = -o ForwardAgent=yes -o ControlMaster=auto -o ControlPersist=60s
retries = 3
pipelining = True
timeout = 30
