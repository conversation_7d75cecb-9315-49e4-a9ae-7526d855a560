---
### Generic site vars

### Login credentials
users:
  - username: ansible
    name: Ansible User
    email:
    phone:
    state: present
    groups: sudo
    uid: 2000
    privilege: 15
    role: network-admin
    sha512_hash: "$y$j9T$MsyhNhe9i.Y2n06kzgOj2.$F/bYHgjTAQXZqJWdjHrULB83Up4Ffhjbmi8ylrvLvl2"
    # bcrypt_hash: "$2b$12$uWGvfplHhgzdYEa5wn2ZquCen5OUSF4qyFDr11TCnsvVrTtL4zlEq"
    sshKeys: "{{ lookup('file', 'inventory/group_vars/sshkeys/ansible.pub') }}"
    admin_group: true
  - username: pozar
    name: <PERSON>
    email:
    phone:
    state: present
    groups: sudo
    uid: 2001
    privilege: 15
    role: network-admin
    sha512_hash: "$6$yY/fgx41Gu0htTI1$zIjWSbtmfBqmdqMCuhcjjc0NOwXnvhRnC9EBrUV7KojtLZd2G38JR0VwECAu5hDD3FG4tLPJUkw4HEEq8x5LP0"
    # bcrypt_hash: "$2b$12$uWGvfplHhgzdYEa5wn2ZquCen5OUSF4qyFDr11TCnsvVrTtL4zlEq"
    sshKeys: "{{ lookup('file', 'inventory/group_vars/sshkeys/pozar.pub') }}"
    admin_group: true
  - username: mkohn
    name: Mike Kohn
    email: <EMAIL>
    phone: 6092408035
    state: present
    groups: sudo
    uid: 2002
    privilege: 15
    role: network-admin
    sha512_hash:
    # bcrypt_hash:
    sshKeys: "{{ lookup('file', 'inventory/group_vars/sshkeys/mkohn.pub') }}"
    admin_group: true
  - username: gust
    name: ky gustafson
    email: <EMAIL>
    phone: ************
    state: present
    groups: sudo
    uid: 2003
    privilege: 15
    role: network-admin
    sha512_hash:
    # bcrypt_hash: "$2b$12$uWGvfplHhgzdYEa5wn2ZquCen5OUSF4qyFDr11TCnsvVrTtL4zlEq"
    sshKeys: "{{ lookup('file', 'inventory/group_vars/sshkeys/gust.pub') }}"
    admin_group: true
  - username: paloma
    name: Paloma Matchett
    email: <EMAIL>
    phone: 5104178669
    state: present
    groups: sudo
    uid: 2004
    privilege: 15
    role: network-admin
    sha512_hash: "a4d56906d1f116624260fbcbf7de5252d8fe20eaa7671832fa93005619f5341099cdef75dbfb7e1b28f907b6677f04e53a58f4897ff3f7c60ad0f51327851311"
    # bcrypt_hash: "$2b$12$.ycdg/n9Ju5/kMb.fAHMfeZzN.EC5GT1oAu70vpwueToJJ4iZasu6"
    sshKeys: "{{ lookup('file', 'inventory/group_vars/sshkeys/paloma.pub') }}"
    admin_group: true
  - username: jmatyas
    name: Jay Matyas
    email: <EMAIL>
    state: present
    groups: sudo
    uid: 2005
    privilege: 15
    role: network-admin
    sha512_hash:
    # bcrypt_hash: "$2b$12$uWGvfplHhgzdYEa5wn2ZquCen5OUSF4qyFDr11TCnsvVrTtL4zlEq"
    sshKeys: "{{ lookup('file', 'inventory/group_vars/sshkeys/jmatyas.pub') }}"
    admin_group: true
    volunteer_group: false
