# Staging-specific configuration
enable_staging_features: true

# PHP error settings - set to true to allow debugging real errors
# while still filtering out deprecation warnings 
display_php_errors: true

# Set to true during testing/debugging to display PHP errors in the browser
# Set to false for normal operation
# To enable: ansible-playbook -i inventory/hosts --tags "php-config" --limit api.staging.kpfa.org -u paloma -e "display_php_errors=true" playbook.yml
# To disable: ansible-playbook -i inventory/hosts --tags "php-config" --limit api.staging.kpfa.org -u paloma -e "display_php_errors=false" playbook.yml 