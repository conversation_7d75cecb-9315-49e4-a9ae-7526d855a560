# Production Environment Configuration

# Feature Flags
enable_staging_features: false
enable_graphql_playground: false
enable_debug: false

# CORS Configuration
cors_allowed_origins:
  - "https://admin.kpfa.org"
  - "https://next.kpfa.org"

# Environment
node_environment: "production"

# Database Configuration - ensure it uses the correct database name
database_url: "postgresql://keystone:{{ vault_keystone_db_password }}@localhost:5432/keystone"

# Git
keystone_repo_branch: "main" 