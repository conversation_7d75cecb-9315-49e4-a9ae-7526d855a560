# Staging Environment Configuration

# Feature Flags
enable_staging_features: true
enable_graphql_playground: true
enable_debug: true

# CORS Configuration
cors_allowed_origins:
  - "https://pet-leopard-fully.ngrok-free.app"
  - "https://api.staging.kpfa.org"

# Environment
node_environment: "staging"

# Database Configuration - ensure it uses the correct database name
database_url: "postgresql://keystone:{{ vault_keystone_db_password }}@localhost:5432/keystone"

# Git
keystone_repo_branch: "3-merge-donor-accounts"

