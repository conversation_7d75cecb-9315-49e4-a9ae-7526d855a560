# Network and system configuration

private_ip: "**********"
public_ip: "***********"
ipv6_address: "2607:f598:f001:0::20"

# Environment configuration
ansible_environment: "production"
enable_staging_features: false

# Required nginx_certbot variables
domain_name: "api.kpfa.org"
certbot_admin_email: "<EMAIL>"
certbot_staging: false # Set to false for production certificates

# Nginx configuration
create_proxy_params: true

# Certbot variables (for geerlingguy.certbot role)
certbot_install_method: package
certbot_auto_renew: true
certbot_auto_renew_user: root
certbot_auto_renew_hour: 3
certbot_auto_renew_minute: 30
certbot_create_if_missing: true
certbot_create_method: standalone

# Metabase access configuration
enable_metabase_access: true
metabase_server_ip: "**********"  # Metabase server IP
