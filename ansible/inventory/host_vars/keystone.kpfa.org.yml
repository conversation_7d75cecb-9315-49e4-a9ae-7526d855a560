# Production Keystone Environment Configuration for keystone.kpfa.org

# Feature Flags
enable_staging_features: false
enable_graphql_playground: false
enable_debug: false

# CORS Configuration for Production
# Allows the production PHP API and the Next.js Admin UI to communicate with Keystone.
cors_allowed_origins:
  - "https://api.kpfa.org"
  - "https://next-admin.kpfa.org" # Assuming the Next.js admin will also be on a subdomain

# Environment
node_environment: "production"

# Git branch for deployment
keystone_repo_branch: "main" 