---
# Network and system configuration
private_ip: "**********"
public_ip: "***********"
ipv6_address: "2607:f598:f001:0:de24:11ff:fed4:5069"

# Domain Configuration
domain_name: "keystone.kpfa.org"
certbot_admin_email: "<EMAIL>"

# Environment Configuration
enable_staging_features: false
node_environment: "production"
enable_graphql_playground: false  # Disable playground for production security
enable_debug: false  # Disable debugging for production

# CORS Configuration for production
cors_enabled: true
cors_allowed_origins:
  - "https://admin.kpfa.org"  # Admin frontend
  - "https://next.kpfa.org"   # New Next.js frontend

# Database Configuration
database_url: "postgresql://keystone:{{ vault_keystone_db_password }}@localhost:5432/keystone"

# Git Configuration - ensure production tracks main branch
keystone_repo_branch: "main"

# Vault variables should be in the vault file
