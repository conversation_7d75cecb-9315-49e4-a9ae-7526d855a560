---
# Network and system configuration

private_ip: "**********"
public_ip: "***********"
ipv6_address: "2607:f598:f001:0:de24:11ff:fed4:5067"

# Domain Configuration
domain_name: "keystone.staging.kpfa.org"
certbot_admin_email: "<EMAIL>"

# Environment Configuration
enable_staging_features: true
node_environment: "production"  # Use production for better security
enable_graphql_playground: true  # Enable playground for testing
enable_debug: true  # Enable debugging features

# CORS Configuration for staging
cors_enabled: true
cors_allowed_origins:
  - "https://admin.kpfa.org"  # Admin frontend
  - "https://next.kpfa.org"   # New Next.js frontend
  - "https://pet-leopard-fully.ngrok-free.app"  # Local development through ngrok

# Database Configuration
database_url: "postgresql://keystone:{{ vault_keystone_db_password }}@localhost:5432/keystone"

# Vault variables should be in the vault file
