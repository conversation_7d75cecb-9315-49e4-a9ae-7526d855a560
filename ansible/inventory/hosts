# For additional details on hosts, see host_vars
#
# WordPress hosts have their LEMP stack and config entirely managed by Trellis: https://git.kpfa.org/KPFA/trellis — only their SSH keys are deployed by this playbook

[python2_hosts]

[python3_hosts]
ansible
librenms.kpfa.org
metabase-app
metabase-db
libretime.staging.kpfa.org
wp.staging.kpfa.org
wp.staging.kpfa.org
staging.streams.kpfa.org
api.staging.kpfa.org
api.production.kpfa.org
keystone.staging.kpfa.org
keystone.production.kpfa.org

[python3_hosts:vars]
ansible_python_interpreter=/usr/bin/python3

# ansible ansible_host=********** ansible_user=ansiblee new_hostname=ansible
ansible ansible_host=********** new_hostname=ansible
librenms.kpfa.org ansible_host=********** new_hostname=librenms
metabase-app ansible_host=********** new_hostname=metabase-app
metabase-db ansible_host=********** new_hostname=metabase-db
wp.production.kpfa.org ansible_host=********** new_hostname=wp.production.kpfa.org
#libretime.kpfa.org ansible_host=********** new_hostname=libretime 
wp.staging.kpfa.org ansible_host=*********** new_hostname=wp.staging.kpfa.org
libretime.staging.kpfa.org ansible_host=********** new_hostname=libretime.staging.kpfa.org
# streams.kpfa.org ansible_host=*********** new_hostname=streams
staging.streams.kpfa.org ansible_host=*********** new_hostname=staging.streams.kpfa.org
api.staging.kpfa.org ansible_host=*********** new_hostname=api.staging.kpfa.org
api.production.kpfa.org ansible_host=********** new_hostname=api.production.kpfa.org
keystone.staging.kpfa.org ansible_host=********** new_hostname=keystone.staging.kpfa.org
keystone.production.kpfa.org ansible_host=********** new_hostname=keystone.production.kpfa.org

[kpfa-vm]
# ansible.local new_hostname=ansible ansible_host=**********
ansible
librenms.kpfa.org
metabase-app
metabase-db
wp.staging.kpfa.org
wp.production.kpfa.org
libretime.staging.kpfa.org
staging.streams.kpfa.org
api.staging.kpfa.org
api.production.kpfa.org
keystone.staging.kpfa.org
keystone.production.kpfa.org

# API server groups
[api:children]
api_staging
api_production

[api_staging]
api.staging.kpfa.org

[api_production]
api.production.kpfa.org

# Keystone servers
[keystone]
keystone.staging.kpfa.org
keystone.production.kpfa.org
