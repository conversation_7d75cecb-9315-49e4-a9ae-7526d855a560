---
- name: Main Ansible run
  hosts: localhost
  connection: local
  gather_facts: yes

  pre_tasks:
    - local_action: shell git log --pretty=format:"%h" -1
      register: git_hash
      changed_when:
        false
        # - local_action: lineinfile line="{{ git_hash.stdout }}" create=yes state=present dest=~/.ansible/.git_hash
    - local_action: lineinfile line="{{git_hash.stdout}}" create=yes state=present dest=~/.ansible/.git_hash

- name: All VMs
  hosts: kpfa-vm
  gather_facts: yes
  become: yes
  roles:
    - { role: common, tags: ["common"] }

- name: Set up Metabase on metabase-app
  hosts: metabase-app
  become: yes
  roles:
    - role: static_ip
    - { role: letsencrypt, tags: ["letsencrypt"] }
    - metabase

- name: Set up PostgreSQL on metabase-db
  hosts: metabase-db
  become: yes
  roles:
    - postgresql
    - metabase-db

- name: Provision static_ip for WordPress hosts
  hosts: wp.staging.kpfa.org,wp.production.kpfa.org
  become: yes
  roles:
    - role: static_ip

- name: Provision Keystone Servers
  hosts: keystone
  become: yes
  roles:
    # Step 1: Configure static IP
    - role: static_ip
      tags: ["static_ip"]

    # Step 2: Set up PostgreSQL
    - role: geerlingguy.postgresql
      tags: ["postgresql", "database"]

    # Step 3: Configure nginx and certbot
    - role: nginx_certbot
      vars:
        app_listen_port: 3000
        create_proxy_params: true
        app_specific_https_config: "{{ lookup('template', 'templates/nginx/keystone-https.j2') }}"
      tags: ["nginx", "letsencrypt", "certbot", "ssl"]

    # Step 4: Install Node.js
    - role: geerlingguy.nodejs
      nodejs_version: "{{ node_version }}"
      tags: ["nodejs"]

    # Step 5: Install and configure KeystoneJS
    - role: keystone
      tags: ["keystone", "deploy"]

  handlers:
    - name: restart nginx
      service:
        name: nginx
        state: restarted

- name: Provision Staging API VM
  hosts: api_staging
  become: yes
  roles:
    - role: static_ip
      tags: ["static_ip"]
    - role: api
      tags: ["api", "deploy", "env_config", "database", "mail"]
    - role: nginx_certbot
      vars:
        app_listen_port: 8000
        create_proxy_params: true
        app_specific_https_config: "{{ lookup('template', 'templates/nginx/api-https.j2') }}"
        domain_name: "api.staging.kpfa.org"
        php_version: "8.2"
      tags: ["nginx", "letsencrypt", "certbot", "ssl"]

- name: Provision Station Admin API Production API
  hosts: api_production
  become: yes
  roles:
    - role: static_ip
      tags: ["static_ip"]
    - role: api
      tags: ["api", "deploy", "env_config", "database", "mail"]
    - role: nginx_certbot
      vars:
        app_listen_port: 8000
        create_proxy_params: true
        app_specific_https_config: "{{ lookup('template', 'templates/nginx/api-https.j2') }}"
        domain_name: "api.production.kpfa.org"
        php_version: "8.2"
      tags: ["nginx", "letsencrypt", "certbot", "ssl"]

- name: Set up LibreTime with HTTPS
  hosts: libretime.staging.kpfa.org
  become: yes
  pre_tasks:
    # Clean up any existing tasks here

  roles:
    # Step 1: Configure static IP
    - role: static_ip
      tags: ["static_ip"]

    # Step 2: Configure nginx and certbot with the new role
    - role: nginx_certbot
      vars:
        app_listen_port: "{{ libretime_listen_port }}"
        create_proxy_params: true
        app_specific_https_config: "{{ lookup('template', 'templates/nginx/libretime-https.j2') }}"
      tags: ["nginx", "letsencrypt", "certbot", "ssl"]

    # Step 3: Install and configure LibreTime
    - role: libretime-role
      tags: ["libretime"]

  handlers:
    - name: restart nginx
      service:
        name: nginx
        state: restarted

- name: set up streaming vm
  hosts: staging.streams.kpfa.org
  become: yes
  roles:
    # Step 1: Configure static IP
    - role: static_ip
      tags: ["static_ip"]

    # Step 2: Configure nginx and certbot with the new role
    - role: nginx_certbot
      vars:
        app_listen_port: 8000
        create_proxy_params: true
        app_specific_https_config: "{{ lookup('template', 'templates/nginx/staging.streams-https.j2') }}"
      tags: ["nginx", "letsencrypt", "certbot", "ssl"]

    # Step 3: Install and configure Icecast
    - role: icecast
      tags: ["icecast"]

    - role: liquidsoap

  handlers:
    - name: restart nginx
      service:
        name: nginx
        state: restarted
