---
- name: Main Ansible run
  hosts: localhost
  connection: local
  gather_facts: no

  pre_tasks:
    - local_action: shell git log --pretty=format:"%h" -1
      register: git_hash
      changed_when: false

    - local_action: lineinfile line="{{ git_hash.stdout }}" create=yes state=present dest=~/.ansible/.git_hash

- hosts: ubuntu_template
  become: yes
  gather_facts: no
  roles:
    - common
    - ifcfg
