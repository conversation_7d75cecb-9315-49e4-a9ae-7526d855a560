# API Role

This role deploys and configures the KPFA API application.

## Features

- PHP setup and configuration
- Database setup and migrations
- Application deployment
- Environment configuration
- Email/Postfix configuration
- Stats Service
- Metabase database access

## Stats Service

The Stats Service sets up a systemd service that runs the stats script to maintain real-time statistics for the KPFA API.

### Implementation

- Updates the stats script to run at a longer interval (60 seconds instead of 10 seconds)
- Creates a systemd service to ensure the script runs reliably and restarts if it fails
- Configures log rotation for `/var/log/kpfa-stats.log`

### Deployment

To deploy or update the stats service, use the main playbook with the stats_service tag:

```bash
ansible-playbook playbook.yml --tags "stats_service"
```

### Monitoring

The stats service is designed for reliability with:

- Automatic restarts if the process crashes
- Log rotation to prevent disk space issues
- Standard systemd status monitoring

To check the service status:

```bash
systemctl status kpfa-stats.service
```

To view logs:

```bash
journalctl -u kpfa-stats.service
# or
tail -f /var/log/kpfa-stats.log
```

## Metabase Access

This role can configure the database to allow remote access from Metabase for analytics and reporting.

### Configuration

The Metabase access is configured with these variables:

- `enable_metabase_access`: Set to `true` to enable remote database access for Metabase (default: `false`)
- `metabase_server_ip`: The IP address of the Metabase server (leave empty to allow from any host)
- `vault_mariadb_metabase_user_password`: Password for the Metabase database user (stored in vault)

### Deployment

To configure Metabase access:

```bash
ansible-playbook playbook.yml --tags "metabase"
```

Or together with other database tasks:

```bash
ansible-playbook playbook.yml --tags "database,metabase"
```

## Database Migrations

The API role includes a simple database migration system to safely apply schema changes.

### How it works

- Migration files are stored in `api/migrations/` directory
- Each migration file should be named with a numeric prefix (e.g., `001_add_link_payment_method.sql`)
- A `migrations` table tracks which migrations have been applied
- Migrations are applied in alphabetical order during deployment
- Each migration is only run once per database

### Creating a migration

1. Create a new SQL file in `api/migrations/` with a descriptive name:
   ```
   api/migrations/002_add_new_feature.sql
   ```

2. Write your SQL schema changes:
   ```sql
   -- Migration: Add new feature
   -- Date: 2025-01-09
   -- Description: Brief description of changes

   ALTER TABLE `table_name` ADD COLUMN `new_column` VARCHAR(255);
   ```

3. Migrations will be automatically applied during deployment

### Running migrations manually

To run only migrations:
```bash
ansible-playbook playbook.yml --tags "migrations" --limit api.staging.kpfa.org
```

### Configuration

Migration settings can be customized in role defaults:
- `mariadb_migration_user`: Database user for migrations (default: `api_db`)
- `mariadb_migration_password`: Database password (default: uses vault password)
- `mariadb_migration_database`: Target database (default: `api_db`)