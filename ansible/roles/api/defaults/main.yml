---
# PHP Configuration
php_version: "8.2"
php_fpm_pool_user: "www-data"
php_fpm_pool_group: "www-data"
php_webserver_daemon: "nginx"

# Application Configuration
api_app_path: "/var/www/api"
api_repo_url: "****************:KPFA/api.git"
api_repo_branch: "master"
api_env_file: "{{ api_app_path }}/.env"
api_uploads_dir: "{{ api_app_path }}/uploads" # Path to the uploads directory
deploy_key_path: "" # Path to the SSH private key for Git access
api_url: "{{ ansible_fqdn }}" # Used in templates for application URL

# Database Configuration
mariadb_root_password: "{{ vault_mariadb_root_password }}"
mariadb_databases:
  - name: api_db
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci
mariadb_users:
  - name: api_db
    password: "{{ vault_mariadb_api_user_password }}"
    priv: "api_db.*:ALL"
    host: localhost

# Staging-specific features (disabled by default)
enable_staging_features: false # Set to true in staging environment

# Metabase Access Configuration
enable_metabase_access: false # Set to true to enable Metabase access to the database
metabase_server_ip: "" # IP address of the Metabase server (leave empty to allow from any host)

# Migration Configuration
mariadb_host: "localhost"
mariadb_migration_user: "api_db"
mariadb_migration_password: "{{ vault_mariadb_api_user_password }}"
mariadb_migration_database: "api_db"

