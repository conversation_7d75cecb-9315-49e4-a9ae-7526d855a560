---
# Pre-tasks for MySQL/MariaDB setup
- name: Ensure MySQL log directory exists
  ansible.builtin.file:
    path: /var/log/mysql
    state: directory
    mode: '0750'
    owner: mysql
    group: mysql
  tags: ["database", "setup"]

# MariaDB requires the InnoDB directory to be properly initialized
- name: Stop MariaDB if running
  service:
    name: mariadb
    state: stopped
  ignore_errors: yes
  tags: ["database", "setup"]

- name: Clean any existing InnoDB log files
  ansible.builtin.file:
    path: "/var/lib/mysql/{{ item }}"
    state: absent
  with_items:
    - "ib_logfile0"
    - "ib_logfile1"
    - "ibdata1" 
  tags: ["database", "setup"]

- name: Ensure MySQL data directory has correct permissions
  ansible.builtin.file:
    path: /var/lib/mysql
    state: directory
    owner: mysql
    group: mysql
    mode: '0750'
    recurse: yes
  tags: ["database", "setup"]

- name: Include geerlingguy.mysql role
  ansible.builtin.import_role:
    name: geerlingguy.mysql
  vars:
    # Use MariaDB packages explicitly
    mysql_packages:
      - mariadb-client
      - mariadb-server
      - python3-mysqldb
    # Set credentials and databases
    mysql_root_password: "{{ mariadb_root_password }}"
    mysql_databases: "{{ mariadb_databases }}"
    mysql_users: "{{ mariadb_users }}"
    # Network configuration
    mysql_bind_address: "127.0.0.1"
    mysql_port: "3306"
    # Replication configuration 
    mysql_server_id: 0
    mysql_replication_role: "none"
    mysql_replication_user: {}
    mysql_replication_master: localhost
    mysql_disable_log_bin: true
    # Include custom configuration file
    mysql_config_include_files:
      - src: "{{ playbook_dir }}/templates/mariadb/mariadb-optimizations.cnf"
        force: true
    # Performance optimizations
    mysql_key_buffer_size: "256M"
    mysql_max_allowed_packet: "64M"
    mysql_table_open_cache: "2000"
    mysql_sort_buffer_size: "4M"
    mysql_read_buffer_size: "2M"
    mysql_read_rnd_buffer_size: "4M"
    mysql_myisam_sort_buffer_size: "64M"
    mysql_thread_cache_size: "8"
    mysql_query_cache_type: "1"
    mysql_query_cache_size: "64M"
    mysql_query_cache_limit: "2M"
    mysql_max_connections: "151"
    mysql_tmp_table_size: "64M"
    mysql_max_heap_table_size: "64M"
    mysql_innodb_buffer_pool_size: "512M"
    mysql_innodb_log_file_size: "128M"
    mysql_innodb_flush_log_at_trx_commit: "1"
    mysql_innodb_file_per_table: "1"

- name: Ensure directory exists for MariaDB configuration files
  file:
    path: /etc/mysql/conf.d
    state: directory
    mode: '0755'
    owner: root
    group: root

- name: Copy MariaDB optimization config
  template:
    src: "{{ playbook_dir }}/templates/mariadb/mariadb-optimizations.cnf"
    dest: /etc/mysql/conf.d/mariadb-optimizations.cnf
    owner: root
    group: root
    mode: '0644'
  notify: restart mysql

# Force reinitialize MariaDB if it failed to start
- name: Ensure MariaDB is started
  block:
    - name: Check MariaDB status first
      command: systemctl status mariadb
      register: mariadb_status
      failed_when: false
      changed_when: false

    - name: Reinitialize MariaDB database if needed
      command: mysql_install_db --user=mysql --ldata=/var/lib/mysql
      args:
        creates: /var/lib/mysql/mysql/user.frm
      when: mariadb_status.rc != 0
      become: yes

    - name: Start MariaDB after potential reinitialization
      service:
        name: mariadb
        state: started
        enabled: yes
      when: mariadb_status.rc != 0
  tags: ["database", "setup"] 