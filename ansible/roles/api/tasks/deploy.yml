---
- name: Include geerlingguy.git role
  ansible.builtin.import_role:
    name: geerlingguy.git
  vars:
    git_config_global:
      - option: user.name
        value: "KPFA API Deployer"
      - option: user.email
        value: "<EMAIL>"
  tags: ["app_deploy", "git", "git_setup"]

- name: Include geerlingguy.composer role
  ansible.builtin.import_role:
    name: geerlingguy.composer
  vars:
    composer_global_packages: []
  tags: ["app_deploy", "composer", "composer_setup"]

- name: Ensure API application directory exists
  file:
    path: "{{ api_app_path }}"
    state: directory
    mode: "0755"
  become: yes
  tags: ["app_deploy", "app_dir"]

- name: Set temporary permissions for Git operations
  file:
    path: "{{ api_app_path }}"
    state: directory
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: "0755"
    recurse: yes  # Changed from 'no' to 'yes' to recursively change all files
  become: yes
  tags: ["app_deploy", "git", "git_ops"]

- name: Accept Git server host key
  shell: ssh-keyscan -H git.kpfa.org >> ~/.ssh/known_hosts
  become: no # Use the Ansible command-line user
  args:
    creates: ~/.ssh/known_hosts
  tags: ["app_deploy", "git", "git_ops"]

- name: Clone or update API repository
  git:
    repo: "{{ api_repo_url }}"
    dest: "{{ api_app_path }}"
    version: "{{ api_repo_branch }}"
    force: yes
    depth: 1
    update: yes # Always pull latest changes
  become: no # Use paloma user for Git authentication
  tags: ["app_deploy", "git", "git_clone"]

- name: Configure git to ignore file mode changes
  command: git config core.filemode false
  args:
    chdir: "{{ api_app_path }}"
  become: no # Use paloma user for Git operations
  tags: ["app_deploy", "git", "git_config"]

- name: Set ownership for PHP-FPM (code files)
  file:
    path: "{{ api_app_path }}"
    state: directory
    owner: "{{ php_fpm_pool_user }}"
    group: "{{ php_fpm_pool_group }}"
    mode: "0755"
    recurse: yes
  become: yes
  tags: ["app_deploy", "permissions"]

- name: Create and set permissions for uploads directory
  file:
    path: "{{ api_uploads_dir }}"
    state: directory
    owner: "{{ php_fpm_pool_user }}"
    group: "{{ php_fpm_pool_group }}"
    mode: "0775"  # More permissive mode to allow file uploads
  become: yes
  tags: ["app_deploy", "permissions", "uploads"]

- name: Install Composer dependencies
  composer:
    command: install
    working_dir: "{{ api_app_path }}"
    no_dev: "{{ not enable_staging_features }}"
  become: yes
  become_user: "{{ php_fpm_pool_user }}"
  tags: ["app_deploy", "composer", "composer_deps"] 