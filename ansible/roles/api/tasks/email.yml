---
# Email configuration tasks - Combines hostname and postfix configuration

# Set up the proper hostname
- name: Set proper hostname based on environment
  hostname:
    name: "{{ 'api.staging.kpfa.org' if ansible_environment == 'staging' else 'api.kpfa.org' }}"
  tags: ["email"]

- name: Ensure /etc/hosts has the correct entry
  lineinfile:
    path: /etc/hosts
    regexp: '^127\.0\.1\.1'
    line: "********* {{ 'api.staging.kpfa.org api-staging' if ansible_environment == 'staging' else 'api.kpfa.org api' }}"
    state: present
  tags: ["email"]

- name: Set /etc/mailname for email configuration
  copy:
    content: "kpfa.org"
    dest: /etc/mailname
    owner: root
    group: root
    mode: '0644'
  notify: restart postfix
  tags: ["email"]

# Install and configure Postfix
- name: Accept repository label changes
  ansible.builtin.shell: apt-get --allow-releaseinfo-change update
  tags: ["email"]

- name: Install Postfix
  apt:
    name: postfix
    state: present
    update_cache: no
  tags: ["email"]

- name: Configure Postfix settings
  command: "postconf -e '{{ item }}'"
  become: yes
  with_items:
    - "myhostname={{ 'api.staging.kpfa.org' if ansible_environment == 'staging' else 'api.kpfa.org' }}"
    - "mydestination=$myhostname, localhost.localdomain, localhost"
    - "relayhost={{ smtp_relay | default('[smtp.gmail.com]:587') }}"
    - "smtp_sasl_auth_enable=yes"
    - "smtp_sasl_password_maps=hash:/etc/postfix/sasl_passwd"
    - "smtp_sasl_security_options=noanonymous"
    - "smtp_tls_security_level=may"
    - "smtp_tls_CApath=/etc/ssl/certs"
    - "inet_interfaces=all"
    - "inet_protocols=all"
    - "smtp_helo_name={{ 'api.staging.kpfa.org' if ansible_environment == 'staging' else 'api.kpfa.org' }}"
    # Set domain for all outgoing mail to kpfa.org
    - "myorigin=kpfa.org"
    # Use public_ip from host vars for consistent outbound IP
    - "smtp_bind_address={{ public_ip }}"
  notify: restart postfix
  tags: ["email"]

# Configure sender address rewriting to ensure all mail comes from kpfa.org
- name: Ensure generic maps are set up to rewrite addresses
  command: "postconf -e '{{ item }}'"
  become: yes
  with_items:
    - "smtp_generic_maps = hash:/etc/postfix/generic"
  notify: restart postfix
  tags: ["email"]

- name: Create generic address mapping file
  copy:
    content: |
      @api.staging.kpfa.org @kpfa.org
      @api-staging @kpfa.org
      @localhost @kpfa.org
      @localhost.localdomain @kpfa.org
      <EMAIL> <EMAIL>
      <EMAIL> <EMAIL>
    dest: /etc/postfix/generic
    owner: root
    group: root
    mode: '0644'
  notify: restart postfix
  tags: ["email"]

- name: Generate the generic database
  command: postmap /etc/postfix/generic
  become: yes
  notify: restart postfix
  tags: ["email"]

# Explicitly remove any conflicting configuration lines
- name: Ensure no duplicate password maps configuration
  lineinfile:
    path: /etc/postfix/main.cf
    regexp: "^smtp_sasl_password_maps\\s*=(?!\\s*hash:/etc/postfix/sasl_passwd)"
    state: absent
  notify: restart postfix
  tags: ["email"]

# Create the password file with proper permissions
- name: Create SASL password file
  template:
    src: sasl_passwd.j2
    dest: /etc/postfix/sasl_passwd
    owner: root
    group: root
    mode: '0600'
  notify: restart postfix
  tags: ["email"]

- name: Generate the SASL password database
  command: postmap /etc/postfix/sasl_passwd
  become: yes
  notify: restart postfix
  tags: ["email"]

# Verify configuration was applied correctly
- name: Verify smtp_sasl_password_maps is set correctly
  shell: postconf smtp_sasl_password_maps
  register: sasl_maps_check
  changed_when: false
  tags: ["email"]

- name: Debug sasl_maps_check
  debug:
    var: sasl_maps_check.stdout
  changed_when: false
  tags: ["email"]
  
# Force postfix to reload to ensure settings take effect
- name: Force Postfix reload
  systemd:
    name: postfix
    state: reloaded
  become: yes
  tags: ["email"] 