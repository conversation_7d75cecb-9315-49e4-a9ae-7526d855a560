---
# Environment and configuration files

- name: Ensure config directory exists
  file:
    path: "{{ api_app_path }}/config"
    state: directory
    owner: "{{ php_fpm_pool_user }}"
    group: "{{ php_fpm_pool_group }}"
    mode: "0755"
  become: yes
  tags: ["env_config", "config_dir"]

- name: Create environment configuration file (.env)
  template:
    src: templates/api/env.j2
    dest: "{{ api_env_file }}"
    owner: "{{ php_fpm_pool_user }}"
    group: "{{ php_fpm_pool_group }}"
    mode: "0640"
  become: yes
  tags: ["env_config", "env_file"]

- name: Create config.php file
  template:
    src: templates/api/config.php.j2
    dest: "{{ api_app_path }}/config/config.php"
    owner: "{{ php_fpm_pool_user }}"
    group: "{{ php_fpm_pool_group }}"
    mode: "0640"
  become: yes
  tags: ["env_config", "config_file"]

- name: Restart PHP-FPM service
  service:
    name: "php{{ php_version }}-fpm"
    state: restarted
  become: yes
  tags: ["env_config", "restart_service"] 