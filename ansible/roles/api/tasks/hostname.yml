---
- name: Set proper hostname based on environment
  hostname:
    name: "{{ 'api.staging.kpfa.org' if ansible_environment == 'staging' else 'api.kpfa.org' }}"

- name: Ensure /etc/hosts has the correct entry
  lineinfile:
    path: /etc/hosts
    regexp: '^127\.0\.1\.1'
    line: "********* {{ 'api.staging.kpfa.org api-staging' if ansible_environment == 'staging' else 'api.kpfa.org api' }}"
    state: present

- name: Set /etc/mailname for email configuration
  copy:
    content: "{{ 'api.staging.kpfa.org' if ansible_environment == 'staging' else 'api.kpfa.org' }}"
    dest: /etc/mailname
    owner: root
    group: root
    mode: '0644'
  notify: restart postfix 