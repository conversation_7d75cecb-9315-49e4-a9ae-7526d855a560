---
- name: Ensure locale-related packages are installed
  apt:
    name: 
      - locales
      - language-pack-en
    state: present
  become: yes
  tags: ["locale"]

- name: Ensure en_US.UTF-8 locale is generated
  locale_gen:
    name: en_US.UTF-8
    state: present
  become: yes
  tags: ["locale"]

- name: Configure locale settings
  copy:
    dest: /etc/default/locale
    content: |
      LANG=en_US.UTF-8
      LANGUAGE=en_US:en
      LC_ALL=en_US.UTF-8
    mode: '0644'
  become: yes
  tags: ["locale"]

- name: Set system timezone to America/Los_Angeles
  timezone:
    name: America/Los_Angeles
  become: yes
  tags: ["timezone", "locale", "setup"] 