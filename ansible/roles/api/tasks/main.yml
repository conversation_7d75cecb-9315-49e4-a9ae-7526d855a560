---
- name: Include locale configuration tasks
  ansible.builtin.import_tasks: locale.yml
  tags: ["locale", "setup"]

- name: Include PHP setup tasks
  ansible.builtin.import_tasks: php.yml
  tags: ["php", "setup"]

- name: Include database setup tasks
  ansible.builtin.import_tasks: database.yml
  tags: ["database", "setup"]

- name: Include application deployment tasks
  ansible.builtin.import_tasks: deploy.yml
  tags: ["deploy", "app_deploy"]

- name: Include environment configuration tasks
  ansible.builtin.import_tasks: env_config.yml
  tags: ["env_config", "config"]

- name: Include staging-specific tasks
  ansible.builtin.import_tasks: staging.yml
  tags: ["staging"]
  when: enable_staging_features | bool

- name: Include PHP tasks
  import_tasks: php.yml
  tags: ["php"]

- name: Include email configuration tasks
  import_tasks: email.yml
  tags: ["email", "mail", "postfix"]

- name: Include database tasks
  import_tasks: database.yml
  tags: ["database"]

- name: Include database migration tasks
  import_tasks: migrations.yml
  tags: ["migrations", "database"]

- name: Include stats service tasks
  import_tasks: stats_service.yml
  tags: ["stats_service"]

- name: Include Metabase access configuration
  import_tasks: metabase_access.yml
  tags: ["metabase", "database"] 