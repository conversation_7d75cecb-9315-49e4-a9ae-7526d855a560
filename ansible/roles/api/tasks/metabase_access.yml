---
# Configure remote access for Metabase
- name: Create MariaDB user for Metabase with remote access
  mysql_user:
    name: metabase-app
    password: "{{ vault_mariadb_metabase_user_password }}"
    host: "{{ metabase_server_ip | default('%') }}"
    priv: "api_db.*:SELECT"
    state: present
  when: enable_metabase_access | default(false) | bool
  tags: ["database", "metabase"]

- name: Ensure ufw is installed
  package:
    name: ufw
    state: present
  when:
    - enable_metabase_access | default(false) | bool
    - metabase_server_ip is defined
  tags: ["database", "firewall", "metabase"]

- name: Add firewall rule to allow MariaDB connections from Metabase
  ufw:
    rule: allow
    port: 3306
    proto: tcp
    src: "{{ metabase_server_ip }}"
    comment: Allow Metabase to access MariaDB
  when:
    - enable_metabase_access | default(false) | bool
    - metabase_server_ip is defined
  tags: ["database", "firewall", "metabase"]