---
# Database migration tasks for API
# Provides a simple migration system to track and apply database schema changes

- name: Check if migrations table exists
  mysql_query:
    login_host: "{{ mariadb_host | default('localhost') }}"
    login_user: "{{ mariadb_migration_user | default('api_db') }}"
    login_password: "{{ mariadb_migration_password | default(vault_mariadb_api_user_password) }}"
    login_db: "{{ mariadb_migration_database | default('api_db') }}"
    query: "SHOW TABLES LIKE 'migrations'"
  register: migrations_table_check
  tags: ["migrations", "database"]

- name: Create migrations tracking table
  mysql_query:
    login_host: "{{ mariadb_host | default('localhost') }}"
    login_user: "{{ mariadb_migration_user | default('api_db') }}"
    login_password: "{{ mariadb_migration_password | default(vault_mariadb_api_user_password) }}"
    login_db: "{{ mariadb_migration_database | default('api_db') }}"
    query: |
      CREATE TABLE IF NOT EXISTS `migrations` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `migration` varchar(255) NOT NULL,
        `applied_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `migration` (`migration`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  when: migrations_table_check.rowcount[0] == 0
  tags: ["migrations", "database"]

- name: Find migration files
  find:
    paths: "{{ api_app_path }}/migrations"
    patterns: "*.sql"
    file_type: file
  register: migration_files
  tags: ["migrations", "database"]

- name: Get list of applied migrations
  mysql_query:
    login_host: "{{ mariadb_host | default('localhost') }}"
    login_user: "{{ mariadb_migration_user | default('api_db') }}"
    login_password: "{{ mariadb_migration_password | default(vault_mariadb_api_user_password) }}"
    login_db: "{{ mariadb_migration_database | default('api_db') }}"
    query: "SELECT migration FROM migrations"
  register: applied_migrations
  tags: ["migrations", "database"]

- name: Set applied migrations list
  set_fact:
    applied_migration_names: "{{ applied_migrations.query_result[0] | map(attribute='migration') | list }}"
  when: applied_migrations.query_result[0] is defined
  tags: ["migrations", "database"]

- name: Set empty applied migrations list if none exist
  set_fact:
    applied_migration_names: []
  when: applied_migrations.query_result[0] is not defined
  tags: ["migrations", "database"]

- name: Apply pending migrations
  block:
    - name: Read migration file content
      slurp:
        src: "{{ item.path }}"
      register: migration_content
      loop: "{{ migration_files.files | sort(attribute='path') }}"
      when: item.path | basename not in applied_migration_names
      
    - name: Execute migration SQL
      mysql_query:
        login_host: "{{ mariadb_host | default('localhost') }}"
        login_user: "{{ mariadb_migration_user | default('api_db') }}"
        login_password: "{{ mariadb_migration_password | default(vault_mariadb_api_user_password) }}"
        login_db: "{{ mariadb_migration_database | default('api_db') }}"
        query: "{{ item.content | b64decode }}"
      loop: "{{ migration_content.results }}"
      when: item.content is defined
      register: migration_execution
      
    - name: Record successful migrations
      mysql_query:
        login_host: "{{ mariadb_host | default('localhost') }}"
        login_user: "{{ mariadb_migration_user | default('api_db') }}"
        login_password: "{{ mariadb_migration_password | default(vault_mariadb_api_user_password) }}"
        login_db: "{{ mariadb_migration_database | default('api_db') }}"
        query: "INSERT INTO migrations (migration) VALUES ('{{ item.source | basename }}')"
      loop: "{{ migration_execution.results }}"
      when:
        - item.source is defined
        - item.failed is not defined or not item.failed

  rescue:
    - name: Migration failed
      fail:
        msg: "Migration failed: {{ ansible_failed_result.msg }}"
      
  tags: ["migrations", "database"]

- name: Display migration status
  debug:
    msg: "Applied {{ migration_execution.results | selectattr('source', 'defined') | list | length }} new migrations"
  when: migration_execution is defined
  tags: ["migrations", "database"]
