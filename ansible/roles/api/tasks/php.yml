---
- name: Add Ondrej PHP repository
  apt_repository:
    repo: ppa:ondrej/php
    state: present
    update_cache: true

- name: Include geerlingguy.php role
  ansible.builtin.import_role:
    name: geerlingguy.php
  vars:
    php_enable_php_fpm: true
    php_enable_webserver: false
    php_packages_state: "latest"
    php_install_recommends: false
    php_version_specific_packages_repo: "ppa:ondrej/php"
    php_default_version_debian: "{{ php_version }}"
    php_date_timezone: "America/Los_Angeles"
    php_fpm_conf_path: "/etc/php/{{ php_version }}/fpm/php-fpm.conf"
    php_fpm_pool_conf_path: "/etc/php/{{ php_version }}/fpm/pool.d/www.conf"
    php_fpm_daemon: "php{{ php_version }}-fpm"
    php_conf_paths:
      - "/etc/php/{{ php_version }}/fpm"
      - "/etc/php/{{ php_version }}/cli"
    php_extension_conf_paths:
      - "/etc/php/{{ php_version }}/fpm/conf.d"
      - "/etc/php/{{ php_version }}/cli/conf.d"
    php_webserver_daemon: "nginx"
    php_remove_packages:
      - "php5.6*"
      - "php7.0*"
      - "php7.1*"
      - "php7.2*"
      - "php7.3*"
      - "php7.4*"
      - "php8.0*"
      - "php8.1*"
      - "php8.3*"
      - "php8.4*"
    php_packages:
      - "php{{ php_version }}"
      - "php{{ php_version }}-cli"
      - "php{{ php_version }}-common"
      - "php{{ php_version }}-fpm"
      - "php{{ php_version }}-mysql"
      - "php{{ php_version }}-curl"
      - "php{{ php_version }}-mbstring"
      - "php{{ php_version }}-xml"
      - "php{{ php_version }}-zip"
      - "php{{ php_version }}-intl"
      - "php{{ php_version }}-gd"
      - "php{{ php_version }}-soap"
      - "php{{ php_version }}-apcu"

- name: Install PHP SQLite extension (staging only)
  apt:
    name: "php{{ php_version }}-sqlite3"
    state: present
  become: yes
  notify: restart php-fpm
  tags: ["php", "sqlite"]
  when: enable_staging_features | bool

- name: Set PHP configuration paths variable
  set_fact:
    php_config_paths:
      - "/etc/php/{{ php_version }}/fpm"
      - "/etc/php/{{ php_version }}/cli"
  tags: ["php", "php-config"]

- name: Configure PHP error settings for each path
  include_tasks: php_error_settings.yml
  loop: "{{ php_config_paths }}"
  loop_control:
    loop_var: php_conf_path
  tags: ["php", "php-config"]

- name: Ensure PHP log directory exists
  file:
    path: /var/log/php
    state: directory
    owner: www-data
    group: www-data
    mode: '0755'
  become: yes
  tags: ["php", "php-config"] 