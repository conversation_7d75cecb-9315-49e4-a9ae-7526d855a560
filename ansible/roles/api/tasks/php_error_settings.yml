---
# Check if php.ini exists
- name: Check if php.ini exists
  stat:
    path: "{{ php_conf_path }}/php.ini"
  register: php_ini_stat
  become: yes

# Configure PHP error settings for a single path
- name: Configure error reporting
  lineinfile:
    path: "{{ php_conf_path }}/php.ini"
    regexp: "^error_reporting\\s*="
    line: "error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT & ~E_NOTICE"
    state: present
  become: yes
  notify: restart php-fpm
  when: php_ini_stat.stat.exists

- name: Configure display errors
  lineinfile:
    path: "{{ php_conf_path }}/php.ini"
    regexp: "^display_errors\\s*="
    line: "display_errors = {{ 'Off' if not display_php_errors|default(false) else 'On' }}"
    state: present
  become: yes
  notify: restart php-fpm
  when: php_ini_stat.stat.exists

- name: Configure display startup errors
  lineinfile:
    path: "{{ php_conf_path }}/php.ini"
    regexp: "^display_startup_errors\\s*="
    line: "display_startup_errors = {{ 'Off' if not display_php_errors|default(false) else 'On' }}"
    state: present
  become: yes
  notify: restart php-fpm
  when: php_ini_stat.stat.exists

- name: Configure log errors
  lineinfile:
    path: "{{ php_conf_path }}/php.ini"
    regexp: "^log_errors\\s*="
    line: "log_errors = On"
    state: present
  become: yes
  notify: restart php-fpm
  when: php_ini_stat.stat.exists

- name: Configure error log path
  lineinfile:
    path: "{{ php_conf_path }}/php.ini"
    regexp: "^error_log\\s*="
    line: "error_log = /var/log/php/error.log"
    state: present
  become: yes
  notify: restart php-fpm
  when: php_ini_stat.stat.exists 