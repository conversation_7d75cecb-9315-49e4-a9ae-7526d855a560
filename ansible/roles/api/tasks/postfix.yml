---
- name: Accept repository label changes
  shell: apt-get --allow-releaseinfo-change update
  args:
    warn: false

- name: Install Postfix
  apt:
    name: postfix
    state: present
    update_cache: no

- name: Configure Postfix to use Gmail as relay without authentication
  lineinfile:
    path: /etc/postfix/main.cf
    regexp: '^relayhost\s*='
    line: 'relayhost = [smtp.gmail.com]:587'
  notify: restart postfix

- name: Ensure SMTP authentication is disabled
  lineinfile:
    path: /etc/postfix/main.cf
    regexp: '^smtp_sasl_auth_enable\s*='
    line: 'smtp_sasl_auth_enable = no'
  notify: restart postfix

- name: Configure TLS security level
  lineinfile:
    path: /etc/postfix/main.cf
    regexp: '^smtp_tls_security_level\s*='
    line: 'smtp_tls_security_level = may'
  notify: restart postfix

- name: Remove SASL password maps configuration
  lineinfile:
    path: /etc/postfix/main.cf
    regexp: '^smtp_sasl_password_maps\s*='
    state: absent
  notify: restart postfix

- name: Remove SASL security options
  lineinfile:
    path: /etc/postfix/main.cf
    regexp: '^smtp_sasl_security_options\s*='
    state: absent
  notify: restart postfix

- name: Remove SASL password files if they exist
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - /etc/postfix/sasl_passwd
    - /etc/postfix/sasl_passwd.db
  notify: restart postfix 