---
# API Staging Server deploy key setup
- name: Ensure paloma's .ssh directory exists on api.staging.kpfa.org
  file:
    path: "/home/<USER>/.ssh"
    state: directory
    owner: paloma
    group: paloma
    mode: "0700"
  become: yes
  tags: ["deploy_keys", "git_access"]

- name: Copy encrypted deploy key to paloma on api.staging.kpfa.org
  copy:
    src: "files/deploy_keys/api_staging_deploy_key.vault"
    dest: "/home/<USER>/.ssh/id_ed25519"
    owner: paloma
    group: paloma
    mode: "0600"
    decrypt: yes
  become: yes
  tags: ["deploy_keys", "git_access"]

- name: Copy deploy key public key to paloma on api.staging.kpfa.org
  copy:
    src: "files/deploy_keys/api_staging_deploy_key.pub"
    dest: "/home/<USER>/.ssh/id_ed25519.pub"
    owner: paloma
    group: paloma
    mode: "0644"
  become: yes
  tags: ["deploy_keys", "git_access"]

- name: Ensure paloma knows the git server host key on api.staging.kpfa.org
  shell: ssh-keyscan -H git.kpfa.org >> /home/<USER>/.ssh/known_hosts
  args:
    creates: /home/<USER>/.ssh/known_hosts
  become: yes
  become_user: paloma
  tags: ["deploy_keys", "git_access"]

- name: Include geerlingguy.nodejs role
  ansible.builtin.import_role:
    name: geerlingguy.nodejs
  vars:
    nodejs_version: "20.x"
    nodejs_install_npm_user: "{{ ansible_user }}"
    nodejs_install_npm_user_home: "{{ ansible_user_dir }}"
    nodejs_install_npm_user_shell: "/bin/bash"

- name: Install development tools globally via Composer
  shell: |
    mkdir -p ~/.composer && \
    composer global require phpunit/phpunit:11.5.12 squizlabs/php_codesniffer:^3.7
  become: yes
  become_user: "{{ php_fpm_pool_user }}"
  environment:
    COMPOSER_HOME: "~/.composer"

- name: Install nodemon globally
  npm:
    name: nodemon
    global: yes
    version: "3.1.9"

- name: Create PHP log directory for Xdebug
  file:
    path: /var/log/php
    state: directory
    owner: "{{ php_fpm_pool_user }}"
    group: "{{ php_fpm_pool_group }}"
    mode: "0755"

- name: Create xdebug.log file with proper permissions
  file:
    path: /var/log/php/xdebug.log
    state: touch
    owner: "{{ php_fpm_pool_user }}"
    group: "{{ php_fpm_pool_group }}"
    mode: "0664"

- name: Configure Xdebug
  template:
    src: xdebug.ini.j2
    dest: "/etc/php/{{ php_version }}/mods-available/xdebug.ini"
    owner: root
    group: root
    mode: "0644"
  notify: restart php-fpm

- name: Enable Xdebug module for PHP-FPM
  file:
    src: "/etc/php/{{ php_version }}/mods-available/xdebug.ini"
    dest: "/etc/php/{{ php_version }}/fpm/conf.d/20-xdebug.ini"
    state: link
    force: yes
  notify: restart php-fpm

- name: Enable Xdebug module for PHP CLI
  file:
    src: "/etc/php/{{ php_version }}/mods-available/xdebug.ini"
    dest: "/etc/php/{{ php_version }}/cli/conf.d/20-xdebug.ini"
    state: link
    force: yes
  notify: restart php-fpm

- name: Install Neovim and dependencies
  apt:
    name:
      - git
      - ripgrep
      - fd-find
      - unzip
      - wget
      - curl
      - lua5.1
      - luarocks
      - fzf
    state: present
  become: yes
  tags: ["nvim_setup"]

- name: Add Neovim PPA for newer version
  apt_repository:
    repo: ppa:neovim-ppa/unstable
    state: present
    update_cache: yes
  become: yes
  tags: ["nvim_setup"]

- name: Install latest Neovim from PPA
  apt:
    name: neovim
    state: latest
  become: yes
  tags: ["nvim_setup"]

- name: Install lazygit
  shell: |
    LAZYGIT_VERSION=$(curl -s "https://api.github.com/repos/jesseduffield/lazygit/releases/latest" | grep -Po '"tag_name": "v\K[^"]*')
    curl -Lo lazygit.tar.gz "https://github.com/jesseduffield/lazygit/releases/latest/download/lazygit_${LAZYGIT_VERSION}_Linux_x86_64.tar.gz"
    tar xf lazygit.tar.gz lazygit
    install lazygit /usr/local/bin
    rm lazygit lazygit.tar.gz
  args:
    creates: /usr/local/bin/lazygit
  become: yes
  tags: ["nvim_setup"]

- name: Install ast-grep
  shell: |
    npm install -g @ast-grep/cli
    ln -sf $(which ast-grep) /usr/local/bin/sg
  args:
    creates: /usr/local/bin/sg
  become: yes
  tags: ["nvim_setup"]

- name: Create symlinks for vim and vi to nvim
  file:
    src: "/usr/bin/nvim"
    dest: "/usr/local/bin/{{ item }}"
    state: link
    force: yes
  with_items:
    - vim
    - vi
  become: yes
  tags: ["nvim_setup"]

- name: Set target user for LazyVim
  set_fact:
    lazyvim_user: "{{ target_user | default('paloma') }}"
  tags: ["nvim_setup"]

- name: Create config directory for target user
  file:
    path: "/home/<USER>/.config/nvim"
    state: directory
    owner: "{{ lazyvim_user }}"
    group: "{{ lazyvim_user }}"
    mode: "0755"
  become: yes
  tags: ["nvim_setup"]

- name: Remove existing LazyVim config if force_install is set
  file:
    path: "/home/<USER>/.config/nvim"
    state: absent
  become: yes
  when: force_install is defined and force_install | bool
  tags: ["nvim_setup"]

- name: Recreate config directory if it was removed
  file:
    path: "/home/<USER>/.config/nvim"
    state: directory
    owner: "{{ lazyvim_user }}"
    group: "{{ lazyvim_user }}"
    mode: "0755"
  become: yes
  when: force_install is defined and force_install | bool
  tags: ["nvim_setup"]

- name: Check if LazyVim is already installed
  stat:
    path: "/home/<USER>/.config/nvim/init.lua"
  register: lazyvim_init_file
  become: yes
  tags: ["nvim_setup"]

- name: Install LazyVim for target user
  git:
    repo: https://github.com/LazyVim/starter
    dest: "/home/<USER>/.config/nvim"
    depth: 1
  become: yes
  become_user: "{{ lazyvim_user }}"
  tags: ["nvim_setup"]
  when: not lazyvim_init_file.stat.exists or (force_install is defined and force_install | bool)

- name: Remove .git directory from user's config
  file:
    path: "/home/<USER>/.config/nvim/.git"
    state: absent
  become: yes
  become_user: "{{ lazyvim_user }}"
  tags: ["nvim_setup"]
  
- name: Create LazyVim data directory
  file:
    path: "/home/<USER>/.local/share/nvim"
    state: directory
    owner: "{{ lazyvim_user }}"
    group: "{{ lazyvim_user }}"
    mode: "0755"
  become: yes
  tags: ["nvim_setup"]

- name: Create LazyVim debug initialization script
  copy:
    dest: "/home/<USER>/lazyvim-init.lua"
    content: |
      local lazypath = vim.fn.stdpath("data") .. "/lazy/lazy.nvim"
      if not vim.loop.fs_stat(lazypath) then
        vim.fn.system({
          "git",
          "clone",
          "--filter=blob:none",
          "https://github.com/folke/lazy.nvim.git",
          "--branch=stable", -- latest stable release
          lazypath,
        })
      end
      vim.opt.rtp:prepend(lazypath)
      
      -- Example config from LazyVim README
      require("lazy").setup({
        spec = {
          -- Import LazyVim plugins
          { "LazyVim/LazyVim", import = "lazyvim.plugins" },
          -- Import extra plugins
          { import = "plugins" },
        },
        defaults = {
          lazy = false,
        },
        install = { colorscheme = { "tokyonight", "habamax" } },
        checker = { enabled = true },
        performance = {
          rtp = {
            disabled_plugins = {
              "gzip",
              "tarPlugin",
              "tohtml",
              "tutor",
              "zipPlugin",
            },
          },
        },
      })
      
      print("LazyVim plugin installation complete")
      vim.cmd("quit")
    owner: "{{ lazyvim_user }}"
    group: "{{ lazyvim_user }}"
    mode: "0644"
  become: yes
  tags: ["nvim_setup"]

- name: Pre-initialize LazyVim plugins
  shell: |
    HOME=/home/<USER>/home/<USER>/lazyvim-init.lua +q
  become: yes
  become_user: "{{ lazyvim_user }}"
  tags: ["nvim_setup"]
  ignore_errors: yes

- name: Set file permissions user
  set_fact:
    file_permissions_user: "{{ target_user | default('paloma') }}"
  tags: ["file_permissions"]

- name: Ensure API repository files have proper ownership and permissions
  file:
    path: "{{ api_app_path }}"
    owner: "{{ file_permissions_user }}"
    group: "{{ file_permissions_user }}"
    mode: "0755"
    recurse: yes
  become: yes
  tags: ["file_permissions"]
  
# If you need to explicitly set write permissions on specific directories
- name: Ensure specific API directories have write permissions
  file:
    path: "{{ item }}"
    state: directory
    owner: "{{ file_permissions_user }}"
    group: "{{ file_permissions_user }}"
    mode: "0775"
    recurse: yes
  with_items:
    - "{{ api_app_path }}"
    - "{{ api_app_path }}/storage"
    - "{{ api_app_path }}/bootstrap/cache"
    - "{{ api_uploads_dir }}"
  become: yes
  tags: ["file_permissions"] 