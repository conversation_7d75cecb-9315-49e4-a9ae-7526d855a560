---
# Tasks for setting up the KPFA Stats Service

- name: Copy stats script from the app
  copy:
    src: "{{ api_app_path }}/objects/stats.sh"
    dest: "{{ api_app_path }}/objects/stats.sh"
    remote_src: yes
    owner: "{{ php_fpm_pool_user }}"
    group: "{{ php_fpm_pool_group }}"
    mode: "0755"
  become: yes
  tags: ["stats_service"]

- name: Create systemd service file
  template:
    src: kpfa-stats.service.j2
    dest: /etc/systemd/system/kpfa-stats.service
    owner: root
    group: root
    mode: "0644"
  become: yes
  tags: ["stats_service"]

- name: Configure logrotate for stats service
  template:
    src: kpfa-stats-logrotate.j2
    dest: /etc/logrotate.d/kpfa-stats
    owner: root
    group: root
    mode: "0644"
  become: yes
  tags: ["stats_service"]

- name: Make sure log file exists with proper permissions
  file:
    path: /var/log/kpfa-stats.log
    state: touch
    owner: "{{ php_fpm_pool_user }}"
    group: "{{ php_fpm_pool_group }}"
    mode: "0640"
  become: yes
  tags: ["stats_service"]

- name: Reload systemd daemon
  systemd:
    daemon_reload: yes
  become: yes
  tags: ["stats_service"]

- name: Enable and start KPFA stats service
  systemd:
    name: kpfa-stats.service
    enabled: yes
    state: started
  become: yes
  tags: ["stats_service"] 