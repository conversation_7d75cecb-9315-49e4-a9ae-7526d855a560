<?php
/**
 * Simple script to test email sending from PHP
 * 
 * Usage: php check-email.php <EMAIL>
 */

if ($argc < 2) {
    echo "Usage: php check-email.php <EMAIL>\n";
    exit(1);
}

$to = $argv[1];
$subject = 'Test Email from ' . gethostname() . ' at ' . date('Y-m-d H:i:s');
$message = "This is a test email sent from " . gethostname() . "\n" .
          "Server time: " . date('Y-m-d H:i:s') . "\n" .
          "FQDN: " . gethostname() . "\n" .
          "PHP version: " . phpversion();

$headers = 'From: webmaster@' . gethostname() . "\r\n" .
    'Reply-To: webmaster@' . gethostname() . "\r\n" .
    'X-Mailer: PHP/' . phpversion();

echo "Sending test email to {$to}...\n";
$result = mail($to, $subject, $message, $headers);

if ($result) {
    echo "Email sent successfully!\n";
    echo "Check mail logs for delivery details.\n";
} else {
    echo "Email sending failed.\n";
    echo "Check mail logs for errors.\n";
}

// Suggest checking logs
echo "\nVerify mail logs with one of these commands:\n";
echo "sudo tail -f /var/log/mail.log\n";
echo "sudo tail -f /var/log/maillog\n";
echo "sudo tail -f /var/log/syslog\n"; 