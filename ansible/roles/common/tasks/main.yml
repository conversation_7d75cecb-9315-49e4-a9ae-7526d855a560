---
### Install python remotely if it is missing
- name: Capture remote operating system name from uname
  raw: uname -s
  become: no
  register: uname_output
  changed_when: false

- name: Confirm python3 installation (Linux)
  raw: env python3 -V
  become: no
  register: python_install
  changed_when: false
  ignore_errors: true
  when:
    - "'Linux' in uname_output.stdout_lines"

- name: Install python (Debian family)
  raw: apt -y update && apt install -y python3-minimal python3
  become_method: sudo
  when:
    - "'Linux' in uname_output.stdout_lines"
    - python_install.rc != 0

- name: Confirm python installation (FreeBSD)
  raw: python2.7 -V
  become: no
  register: python_install
  changed_when: false
  ignore_errors: true
  when:
    - "'FreeBSD' in uname_output.stdout_lines"

- name: Install python (FreeBSD)
  raw: pkg install python27
  become_method: sudo
  when:
    - "'FreeBSD' in uname_output.stdout_lines"
    - python_install.rc != 0

- name: Confirm python installation (OpenBSD)
  raw: /usr/local/bin/python3.8 -V
  become: no
  register: python_install
  changed_when: false
  ignore_errors: true
  when:
    - "'OpenBSD' in uname_output.stdout_lines"
    - "'gw.sfo01' in inventory_hostname"

- name: Force OpenBSD mirror location
  raw: sed -i -e 's/^.*/http:\/\/ftp3.usa.openbsd.org\/pub\/OpenBSD\//' /etc/installurl
  become_method: su
  changed_when: false
  when:
    - "'OpenBSD' in uname_output.stdout_lines"
    - "'gw.sfo01' in inventory_hostname"
    - python_install.rc != 0

- name: Install python (OpenBSD)
  raw: pkg_add -I -x -z python-3.8
  become_method: su
  when:
    - "'OpenBSD' in uname_output.stdout_lines"
    - "'gw.sfo01' in inventory_hostname"
    - python_install.rc != 0

# Write out standard login banner
- name: MOTDize
  template: src=motd.j2 dest=/etc/motd

- name: Refresh facts
  setup:

# - name: Add Debian apt source (Cumulus)
#   apt_repository: repo='deb http://ftp.us.debian.org/debian jessie main contrib non-free' state=present
#   when: ansible_system == 'Linux' and ansible_lsb['id'] == 'Cumulus Linux'

# - name: Add Debian security apt source (Cumulus)
#   apt_repository: repo='deb http://security.debian.org/ jessie/updates main contrib non-free' state=present
#   when: ansible_system == 'Linux' and ansible_lsb['id'] == 'Cumulus Linux'

- name: Install packages (Linux)
  package: name=bash,sudo state=present
  when: ansible_system == 'Linux' and ansible_lsb['id'] != 'Cumulus Linux'

- name: Install packages (FreeBSD)
  package: name=bash,zsh,sudo state=present
  when: ansible_system == 'FreeBSD'

# Configure Ubuntu Pro for older Ubuntu systems that need extended security updates
- name: Check if system requires Ubuntu Pro
  fail:
    msg: |
      This Ubuntu system ({{ ansible_distribution_version }}) requires Ubuntu Pro for extended security updates.
      Please define ubuntu_pro_token in host_vars for this system.
      Ubuntu Pro is required for Ubuntu LTS versions 20.04 and lower that are out of standard support.
  when:
    - ansible_distribution == 'Ubuntu'
    - ansible_distribution_version is version('20.04', '<=')
    - ubuntu_pro_token is not defined
  tags: ['security', 'updates', 'ubuntu-pro']

- name: Update Ubuntu Pro client
  apt:
    name: ubuntu-advantage-tools
    state: latest
    update_cache: yes
  when:
    - ansible_distribution == 'Ubuntu'
    - ansible_distribution_version is version('20.04', '<=')
  tags: ['security', 'updates', 'ubuntu-pro']

- name: Check Ubuntu Pro status
  command: pro status --format json
  register: pro_status
  changed_when: false
  failed_when: false
  when:
    - ansible_distribution == 'Ubuntu'
    - ansible_distribution_version is version('20.04', '<=')
  tags: ['security', 'updates', 'ubuntu-pro']

- name: Parse Ubuntu Pro status
  set_fact:
    pro_is_attached: "{{ (pro_status.stdout | from_json).attached }}"
  when:
    - ansible_distribution == 'Ubuntu'
    - ansible_distribution_version is version('20.04', '<=')
  tags: ['security', 'updates', 'ubuntu-pro']

- name: Include Ubuntu Pro role if not already attached
  become: true
  command: "pro attach {{ ubuntu_pro_token }}"
  register: pro_attach_result
  changed_when: "'This machine is now attached' in pro_attach_result.stdout"
  failed_when:
    - pro_attach_result.rc != 0
    - "'already attached' not in pro_attach_result.stderr"
  when: 
    - ansible_distribution == 'Ubuntu'
    - ansible_distribution_version is version('20.04', '<=')
    - ubuntu_pro_token is defined
    - not pro_is_attached
  tags: ['security', 'updates', 'ubuntu-pro']
  no_log: true  # Hide token from output

- name: Wait for Pro services to be ready
  pause:
    seconds: 5
  when:
    - ansible_distribution == 'Ubuntu'
    - ansible_distribution_version is version('20.04', '<=')
    - not pro_is_attached
  tags: ['security', 'updates', 'ubuntu-pro']

- name: Ensure ESM updates are enabled
  command: pro enable esm-infra
  register: esm_result
  changed_when: "'Enabling ESM Infra' in esm_result.stdout"
  failed_when: 
    - esm_result.rc != 0 
    - "'already enabled' not in esm_result.stdout"
    - "'To use' not in esm_result.stderr"
  when:
    - ansible_distribution == 'Ubuntu'
    - ansible_distribution_version is version('20.04', '<=')
  tags: ['security', 'updates', 'ubuntu-pro']

- name: Enable ESM Apps for Universe/Multiverse packages
  command: pro enable esm-apps
  register: esm_apps_result
  changed_when: "'Enabling ESM Apps' in esm_apps_result.stdout"
  failed_when:
    - esm_apps_result.rc != 0
    - "'already enabled' not in esm_apps_result.stdout"
    - "'To use' not in esm_result.stderr"
  when:
    - ansible_distribution == 'Ubuntu'
    - ansible_distribution_version is version('20.04', '<=')
  tags: ['security', 'updates', 'ubuntu-pro']

- name: Check security status
  command: pro security-status
  register: security_status
  changed_when: false
  when:
    - ansible_distribution == 'Ubuntu'
    - ansible_distribution_version is version('20.04', '<=')
  tags: ['security', 'updates', 'ubuntu-pro']

# Configure automatic security updates for Ubuntu systems
- name: Install unattended-upgrades package
  apt:
    name: 
      - unattended-upgrades
      - apt-listchanges
    state: present
  when: ansible_distribution == 'Ubuntu'
  tags: ['security', 'updates', 'unattended-upgrades']

- name: Configure unattended-upgrades
  template:
    src: 50unattended-upgrades.j2
    dest: /etc/apt/apt.conf.d/50unattended-upgrades
    mode: '0644'
  when: ansible_distribution == 'Ubuntu'
  tags: ['security', 'updates', 'unattended-upgrades']

- name: Enable automatic updates
  template:
    src: 20auto-upgrades.j2
    dest: /etc/apt/apt.conf.d/20auto-upgrades
    mode: '0644'
  when: ansible_distribution == 'Ubuntu'
  tags: ['security', 'updates', 'unattended-upgrades']

- name: Ensure unattended-upgrades service is enabled and running
  service:
    name: unattended-upgrades
    state: started
    enabled: yes
  when: ansible_distribution == 'Ubuntu'
  tags: ['security', 'updates', 'unattended-upgrades']

- name: User management (Linux)
  user: name={{ item.username }}
    password={{ item.sha512_hash }}
    state={{ item.state }}
    groups={{ item.groups }}
    shell=/bin/bash
  with_items: "{{users}}"
  when: ansible_system == 'Linux' and item.admin_group

### TODO: make item.groups based on ansible_system sudo defaults?
- name: User management (FreeBSD)
  user: name={{ item.username }}
    password={{ item.sha512_hash }}
    state={{ item.state }}
    groups=wheel
    shell=/usr/local/bin/bash
  with_items: "{{users}}"
  when: ansible_system == 'FreeBSD' and item.admin_group

- name: User management (OpenBSD)
  user: name={{ item.username }}
    password={{ item.bcrypt_hash }}
    state={{ item.state }}
    groups=wheel
    shell=/usr/local/bin/bash
  with_items: "{{users}}"
  when: ansible_system == 'OpenBSD' and item.admin_group

- name: User management | authorized key upload
  authorized_key: user={{ item.username }}
    key='{{ item.sshKeys }}'
    path='/home/<USER>/.ssh/authorized_keys'
    manage_dir=yes
  with_items: "{{users}}"
  when: item.state == 'present' and item.admin_group

- name: Add users to sudo (Linux)
  template: src=sudoers.j2 dest=/etc/sudoers.d/sfmix_sudoers backup=true validate='/usr/sbin/visudo -cf %s'
  when: ansible_system == 'Linux'

- name: Add users to sudo (FreeBSD)
  template: src=sudoers.j2 dest=/usr/local/etc/sudoers.d/sfmix_sudoers backup=true
  when: ansible_system == 'FreeBSD'

- name: Add users to doas (OpenBSD sudo replacement)
  template: src=doas.conf dest=/etc/doas.conf backup=true validate='doas -C %s'
  register: doas_obsd
  when: ansible_system == 'OpenBSD'

- fail: msg="OpenBSD configuration validation failed"
  when: doas_obsd is failed and ansible_system == 'OpenBSD'

- name: SSH config
  template: src=sshd_config.j2 dest=/etc/ssh/sshd_config backup=true validate='/usr/sbin/sshd -t -f %s'
  when: ansible_system != 'OpenBSD'
  notify:
    - restart sshd

- name: Add fail2ban
  raw: apt -y update && apt install -y fail2ban
  become_method: sudo
  when: ansible_system == 'Linux'

# - name: copy fail2ban local config
# file: src=jail.local dest=/etc/fail2ban/jail.local owner=root group=root mode=0644
# sudo: yes
  
# Update the OS repros after an hour...
# - name: Update apt-get repo and cache
#   apt: update_cache=yes force_apt_get=yes cache_valid_time=3600

# Upgrading all apt packages
# - name: Upgrade all apt packages
#   apt: upgrade=dist force_apt_get=yes

- name: "update hostnames"
  when: new_hostname is defined
  hostname:
    name: "{{ new_hostname }}"

- name: "Rebuild hosts file"
  when: new_hostname is defined
  replace:
    path: /etc/hosts
    regexp: '^*********.*$'
    replace: '*********	{{ new_hostname}}'

- name: Check if a reboot is needed for Debian and Ubuntu boxes
  register: reboot_required_file
  stat: path=/var/run/reboot-required

- name: Reboot the Debian or Ubuntu server
  reboot:
    msg: "Reboot initiated by Ansible due to kernel updates"
    connect_timeout: 5
    reboot_timeout: 300
    pre_reboot_delay: 0
    post_reboot_delay: 30
    test_command: uptime
  when: reboot_required_file.stat.exists

# Add wait for system to stabilize after reboot
- name: Wait for system to fully stabilize after potential reboot
  wait_for_connection:
    delay: 10
    timeout: 60
  when: reboot_required_file.stat.exists

# Include SNMP setup tasks
- name: Include SNMP configuration tasks
  include_tasks: snmpd.yml
  when: ansible_system == 'Linux'
  tags:
    - snmpd

