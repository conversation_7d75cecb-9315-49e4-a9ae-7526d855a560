---
# Use the third-party robertdebock.snmpd role with direct configuration
# All configuration is provided directly in this file rather than from group_vars

- name: Skip SNMP configuration for non-Ubuntu systems
  debug:
    msg: "SNMP configuration is currently only supported on Ubuntu systems"
  when: ansible_distribution != 'Ubuntu'
  tags:
    - snmpd

# Pre-installation connectivity check
- name: Ensure connection is stable before proceeding
  wait_for_connection:
    timeout: 60
  when: ansible_distribution == 'Ubuntu'
  tags:
    - snmpd

# Import the role with proper privileges and explicit configuration
- name: Import robertdebock.snmpd role
  import_role:
    name: robertdebock.snmpd
  become: true
  become_method: sudo # Use the same become_method as in main.yml
  vars:
    # Force install the package
    snmpd_package_state: present
    # Other configuration settings
    snmpd_security_names:
      - name: notConfigUser
        source: default
        community: pacifica
      - name: kpfaUser
        source: default
        community: kpfa
    snmpd_groups:
      - name: notConfigGroup
        security_model: v1
        security_name: notConfigUser
      - name: notConfigGroup
        security_model: v2c
        security_name: notConfigUser
      - name: kpfaGroup
        security_model: v1
        security_name: kpfaUser
      - name: kpfaGroup
        security_model: v2c
        security_name: kpfaUser
    snmpd_views:
      - name: systemview
        type: included
        subtree: ".1"
      - name: systemview
        type: included
        subtree: ".1.3.6.1.2.1.1"
      - name: systemview
        type: included
        subtree: ".1.3.6.1.2.1.25.1.1"
    snmpd_accesses:
      - group: notConfigGroup
        context: ""
        security_model: any
        security_level: noauth
        prefix: exact
        read: systemview
        write: none
        notif: none
      - group: kpfaGroup
        context: ""
        security_model: any
        security_level: noauth
        prefix: exact
        read: systemview
        write: none
        notif: none
    snmpd_syslocation: "HE - FMT2  [37.471877, -121.920068]"
    snmpd_syscontact: "<EMAIL>"
    snmpd_dontlogtcpwrappersconnects: "yes"
    snmpd_disks:
      - path: /
        minimum: 10000
  when: ansible_distribution == 'Ubuntu'
  tags:
    - snmpd

# Post-install handler to ensure service is running
- name: Ensure SNMPD service is started
  service:
    name: snmpd
    state: started
    enabled: yes
  when: ansible_distribution == 'Ubuntu'
  register: service_result
  ignore_errors: true
  tags:
    - snmpd

