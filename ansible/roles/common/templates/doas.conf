##############################################################################
# HEADER: This file was generated by Ansible on {{ ansible_managed }}
# HEADER: Source {{ template_path|lchop(55) }}
# HEADER: Git SHA hash {{ hostvars['localhost']['git_hash']['stdout_lines'][0] }}
# HEADER: Built by {{ template_uid }} on {{ template_host }}
##############################################################################
{% for user in users %}
{% if user.state == 'present' %}
permit nopass keepenv {{ user.username }} as root
{% endif %}
{% endfor %}
