{#- Define a system description based on SMBIOS field or virtualization presense -#}
{%- if ansible_virtualization_type is defined -%}
{%- set system_desc = ansible_virtualization_type -%}
{%- elif ansible_product_name is defined -%}
{%- set system_desc = ansible_product_name -%}
{%- else -%}
{%- set system_desc = "VM" -%}

{% endif %}

  This is {{ inventory_hostname }} ({{ ansible_distribution }},{{ system_desc }}) - an Ansible managed host.
  _____           _  __ _           
 |  __ \         (_)/ _(_)          
 | |__) |_ _  ___ _| |_ _  ___ __ _ 
 |  ___/ _` |/ __| |  _| |/ __/ _` |
 | |  | (_| | (__| | | | | (_| (_| |
 |_|   \__,_|\___|_|_| |_|\___\__,_|

{#  Last push: {{ ansible_managed }} (user: {{ template_uid }} git: {{ hostvars['localhost']['git_hash']['stdout_lines'][0] }}) #}

