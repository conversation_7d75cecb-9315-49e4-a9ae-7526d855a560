---
- set_fact:
    real_ansible_host: "{{ ansible_host }}"

# Modified from the examples at: 
# https://oliversmith.io/technology/2014/09/30/Installing-NodeRed-using-Ansible/
#
# Since Nodered is a NodeJS application, we need to install a few dependencies first...

- name: Add node key
  apt_key: url=https://deb.nodesource.com/gpgkey/nodesource.gpg.key state=present

- name: Add repos
  apt_repository: repo='deb https://deb.nodesource.com/node precise main'
  
- name: install git-core
  package:
    name: git-core
    state: present

- name: install nodejs
  package:
    name: nodejs
    state: present

- name: install npm
  package:
    name: npm
    state: present

- name: Setup Forever
  npm: name=forever global=yes

# There are a few ways to install Nodered; from a downloaded archive,
# using git or plain npm, I’ve chosen git as it allows me to easily
# upgrade just by changing the version tag and rerunning Ansible. All
# the functionality to get the code is built into Ansible:

- name: GetNodeRed
  git: repo=https://github.com/node-red/node-red.git dest=/home/<USER>/NodeRed version=2.2.2

- name: Install NodeRed
  npm: name=npm global=yes registry=http://registry.npmjs.org state=latest
  npm: path=/home/<USER>/NodeRed/ production=yes

- name: Create crontab entry to start NodeRed at boot
  cron:
    user: root
    name: "Start nodered at boot"
    special_time: reboot
    job: "cd /home/<USER>/NodeRed/ ; packages/node_modules/node-red/bin/node-red-pi --max-old-space-size=256 > /var/log/NodRed.log &"

# - name: 'Reboot'
#   shell: sleep 2 && reboot
#   async: 1
#   poll: 0
#   ignore_errors: true

# - name: "Wait for Raspberry PI to come back"
#   local_action: wait_for host={{ real_ansible_host }} port=22 state=started delay=10
#   become: false
