---
# Install icecast
- name: Install Icecast
  apt:
    name:
      - icecast2
    state: present
    update_cache: yes

# Icecast server configuration
- name: Create icecast configuration
  template:
    src: icecast.xml.j2
    dest: /etc/icecast2/icecast.xml
    mode: "0644"

# enable icecast server
- name: enable icecast server
  systemd:
    name: icecast2
    state: started
    enabled: yes

# create bundled ssl

- name: read cert files
  vars:
    certfiles:
    - "{{ ssl_cert_file }}"
    - "{{ ssl_key_file }}"
  command: awk 1 {{ certfiles | join(' ') }}
  register: cert_contents
  tags:
    - bundle

- name: combine cert files
  copy:
    dest: /etc/icecast2/bundle.pem
    content: "{{ cert_contents.stdout_lines | join('\n') }}"
    mode: "0644"
  tags:
    - bundle

- name: restart icecast server
  systemd:
    name: icecast2
    state: restarted
    enabled: yes
