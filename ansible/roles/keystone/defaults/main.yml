---
# Node.js Configuration
node_version: "20.x" # LTS version for all environments
npm_version: "10.9.0" # Latest stable npm version for Node.js 20.x

# Application Configuration
keystone_app_path: "/var/www/keystone"
keystone_repo_url: "****************:KPFA/api-keystone.git"
keystone_repo_branch: "main" # Default branch, can be overridden in host_vars
keystone_env_file: "{{ keystone_app_path }}/.env"
deploy_key_path: "roles/keystone/files/keystone_deploy_key.vault"  # Path to the encrypted SSH private key for Git access

# Service Configuration
service_name: "keystone"
app_listen_port: 3000 # Default KeystoneJS port
node_environment: "production"  # Must be one of: development, production

# Environment-specific features
enable_staging_features: false # Set to true in staging environment
enable_graphql_playground: false # Enable for staging/development
enable_debug: false # Enable for staging/development

# SSL/Domain Configuration
domain_name: "{{ ansible_fqdn }}" # Override in host_vars
certbot_admin_email: "<EMAIL>" # Should be overridden in host_vars
certbot_staging: false # Set to true for testing

# User/Group Configuration
app_user: "keystone"
app_group: "keystone"

# Git Configuration
git_config_global:
  - option: user.name
    value: "KPFA KeystoneJS Deployer"
  - option: user.email
    value: "<EMAIL>"

# KeystoneJS Specific Configuration
use_jwt_middleware: true
enable_automated_scheduler: true
donor_expiration_cron: "0 2 1 * *" # Monthly at 2 AM on the 1st

# CORS Configuration
cors_enabled: false  # Enable for staging/development
cors_allowed_origins: []  # Override in host_vars for staging/development
# For local development, override these in your .env file:
# cors_allowed_origins:
#   - "http://localhost:3000"
#   - "http://localhost:3001"
#   - "http://127.0.0.1:3000"
#   - "http://127.0.0.1:3001"

# PostgreSQL Configuration
postgresql_python_library: python3-psycopg2
postgresql_global_config_options:
  - option: unix_socket_directories
    value: "/var/run/postgresql"
  - option: listen_addresses
    value: "localhost"
  - option: log_directory
    value: "log"
postgresql_databases:
  - name: keystone
    encoding: "UTF-8"
    lc_collate: "en_US.UTF-8"
    lc_ctype: "en_US.UTF-8"
    template: "template0"
postgresql_users:
  - name: keystone
    password: "{{ vault_keystone_db_password }}"
    encrypted: yes
    db: keystone
    priv: "ALL"

