---
# Tasks for setting up the KeystoneJS database

- name: Create PostgreSQL user for KeystoneJS
  postgresql_user:
    name: "{{ app_user }}"
    password: "{{ vault_keystone_db_password }}"
    state: present
  become: yes
  become_user: postgres
  tags: ["database", "postgresql"]

- name: Create KeystoneJS database
  postgresql_db:
    name: keystone
    owner: "{{ app_user }}"
    encoding: "UTF-8"
    template: "template0"
    state: present
  become: yes
  become_user: postgres
  tags: ["database", "postgresql"]

- name: Make {{ app_user }} user owner of public schema
  postgresql_query:
    db: keystone
    query: "ALTER SCHEMA public OWNER TO {{ app_user }};"
  become: yes
  become_user: postgres
  tags: ["database", "postgresql"]

- name: Grant all privileges on all tables in public schema to {{ app_user }} user
  postgresql_privs:
    db: keystone
    schema: public
    type: table
    objs: ALL_IN_SCHEMA
    role: "{{ app_user }}"
    privs: ALL
    state: present
  become: yes
  become_user: postgres
  tags: ["database", "postgresql"]

- name: Grant all privileges on all sequences in public schema to {{ app_user }} user
  postgresql_privs:
    db: keystone
    schema: public
    type: sequence
    objs: ALL_IN_SCHEMA
    role: "{{ app_user }}"
    privs: ALL
    state: present
  become: yes
  become_user: postgres
  tags: ["database", "postgresql"]