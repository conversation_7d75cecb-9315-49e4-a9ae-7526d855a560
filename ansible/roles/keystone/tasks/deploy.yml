---
# Tasks for deploying the KeystoneJS application

- name: Set temporary permissions for Git operations
  file:
    path: "{{ keystone_app_path }}"
    state: directory
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: "0755"
    recurse: yes
  become: yes
  tags: ["git", "git_ops", "deploy"]

- name: Accept Git server host key
  shell: ssh-keyscan -H git.kpfa.org >> ~/.ssh/known_hosts
  become: no
  args:
    creates: ~/.ssh/known_hosts
  tags: ["git", "git_ops", "deploy"]

- name: Clone or update KeystoneJS repository
  git:
    repo: "{{ keystone_repo_url }}"
    dest: "{{ keystone_app_path }}"
    version: "{{ keystone_repo_branch }}"
    force: yes
    update: yes
  become: no
  tags: ["git", "git_clone", "deploy"]

- name: Set ownership after clone
  file:
    path: "{{ keystone_app_path }}"
    state: directory
    owner: "{{ app_user }}"
    group: "{{ app_group }}"
    recurse: yes
    mode: "0755"
  become: yes

- name: Fix Git directory ownership
  file:
    path: "{{ keystone_app_path }}/.git"
    state: directory
    owner: "{{ app_user }}"
    group: "{{ app_group }}"
    recurse: yes
  become: yes
  tags: ["git", "deploy"]

- name: Set temporary ownership for Git operations
  file:
    path: "{{ keystone_app_path }}"
    state: directory
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    recurse: yes
    mode: "0755"
  become: yes
  tags: ["git", "deploy"]

- name: Configure Git safe directory for ansible user
  shell: |
    git config --global --add safe.directory {{ keystone_app_path }}
  become: no
  tags: ["git", "deploy"]

- name: Clean Git repository state
  shell: |
    cd {{ keystone_app_path }}
    git reset --hard HEAD
    git clean -fd
    git checkout {{ keystone_repo_branch }}
    git reset --hard origin/{{ keystone_repo_branch }}
    git pull origin {{ keystone_repo_branch }}
  become: no
  tags: ["git", "deploy"]

- name: Fix ownership after Git operations
  file:
    path: "{{ keystone_app_path }}"
    state: directory
    owner: "{{ app_user }}"
    group: "{{ app_group }}"
    recurse: yes
    mode: "0755"
  become: yes
  tags: ["git", "deploy"]

- name: Install npm dependencies (skip postinstall build)
  npm:
    path: "{{ keystone_app_path }}"
    state: present
    production: "{{ not enable_staging_features }}"
  become: yes
  become_user: "{{ app_user }}"
  environment:
    npm_config_ignore_scripts: "true"
  notify: restart keystone

- name: Build Keystone for production (no Admin UI)
  shell: |
    cd {{ keystone_app_path }}
    npx keystone build --no-ui
  become: yes
  become_user: "{{ app_user }}"
  environment:
    NODE_ENV: production
    DATABASE_URL: "{{ database_url }}"
    STRIPE_SECRET_KEY: "{{ vault_stripe_live_key }}"
    STRIPE_PUBLISHABLE_KEY: "{{ vault_stripe_live_publishable_key }}"
  tags: ["build", "deploy"]
  notify: restart keystone

- name: Set file permissions
  file:
    path: "{{ keystone_app_path }}"
    owner: "{{ app_user }}"
    group: "{{ app_group }}"
    recurse: yes
    mode: "0755"