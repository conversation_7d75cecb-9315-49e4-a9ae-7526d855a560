---
# Main tasks for KeystoneJS deployment

- name: Include user and directory setup tasks
  ansible.builtin.import_tasks: setup.yml
  tags: ["setup", "deploy"]

- name: Include database setup tasks
  ansible.builtin.import_tasks: database.yml
  tags: ["database", "setup"]

- name: Include application deployment tasks
  ansible.builtin.import_tasks: deploy.yml
  tags: ["deploy", "app_deploy"]

- name: Include environment configuration tasks
  ansible.builtin.import_tasks: env_config.yml
  tags: ["env_config", "config", "deploy"]

- name: Include migrations tasks
  ansible.builtin.import_tasks: migrations.yml
  tags: ["migrations"]

- name: Include service configuration tasks
  ansible.builtin.import_tasks: service.yml
  tags: ["service", "systemd", "deploy"]