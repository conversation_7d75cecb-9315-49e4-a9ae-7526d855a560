---
# Tasks for setting up Node.js environment

- name: Install Node.js repository
  shell: |
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
  args:
    creates: /etc/apt/sources.list.d/nodesource.list

- name: Install Node.js and npm
  apt:
    name: 
      - nodejs
      - build-essential
    state: present
    update_cache: yes

- name: Update npm to specific version
  npm:
    name: npm
    version: "{{ npm_version }}"
    global: yes
    state: present

- name: Install required global npm packages
  npm:
    name: "{{ item }}"
    global: yes
    state: present
  loop:
    - typescript
    - ts-node 