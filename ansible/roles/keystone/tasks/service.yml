---
# Tasks for managing the KeystoneJS systemd service

- name: Create systemd service file
  ansible.builtin.template:
    src: keystone.service.j2
    dest: /etc/systemd/system/{{ service_name }}.service
    mode: "0644"
  notify: reload systemd

- name: Ensure systemd is reloaded
  ansible.builtin.systemd:
    daemon_reload: yes

- name: Enable and start KeystoneJS service
  ansible.builtin.systemd:
    name: "{{ service_name }}"
    state: started
    enabled: yes 