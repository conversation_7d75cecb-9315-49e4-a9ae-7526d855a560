---
# Tasks for setting up users and directories

- name: Ensure keystone group exists
  group:
    name: "{{ app_group }}"
    state: present

- name: Ensure keystone user exists
  user:
    name: "{{ app_user }}"
    group: "{{ app_group }}"
    shell: /bin/bash
    system: yes
    create_home: yes
    state: present

- name: Ensure application parent directory exists
  file:
    path: "{{ keystone_app_path | dirname }}"
    state: directory
    owner: "{{ app_user }}"
    group: "{{ app_group }}"
    mode: "0755"

- name: Set up SSH for git access (ansible user)
  block:
    - name: Ensure ansible user .ssh directory exists
      file:
        path: "/home/<USER>/.ssh"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0700"

    - name: Copy deploy key for ansible user
      copy:
        src: "{{ deploy_key_path }}"
        dest: "/home/<USER>/.ssh/id_rsa"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0600"
        decrypt: yes
      when: deploy_key_path != ""

    - name: Add git server to known hosts for ansible user
      known_hosts:
        name: "git.kpfa.org"
        key: "{{ lookup('pipe', 'ssh-keyscan -t rsa git.kpfa.org 2>/dev/null') }}"
        state: present
        path: "/home/<USER>/.ssh/known_hosts"
  become: yes
  when: deploy_key_path != ""