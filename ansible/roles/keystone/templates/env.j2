# KeystoneJS Environment Configuration
NODE_ENV={{ node_environment }}
PORT={{ app_listen_port }}

# Database Configuration (these should be set in host_vars)
DATABASE_URL={{ database_url }}

# Session Configuration
SESSION_SECRET={{ vault_session_secret }}

# API Configuration
CORS_ENABLED=true
CORS_ORIGIN={{ cors_allowed_origins | join(',') }}

# Stripe Configuration
# Use production keys for both staging and production since we want to match real data
STRIPE_SECRET_KEY={{ vault_stripe_live_key | default('sk_live_your_key_here') }}
STRIPE_PUBLISHABLE_KEY={{ vault_stripe_live_publishable_key | default('pk_live_your_key_here') }}

# Other environment-specific variables can be added here