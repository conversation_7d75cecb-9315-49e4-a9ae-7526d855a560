[Unit]
Description=KPFA KeystoneJS Application
After=network.target

[Service]
Type=simple
User={{ app_user }}
Group={{ app_group }}
WorkingDirectory={{ keystone_app_path }}

# Base Configuration
Environment=NODE_ENV={{ node_environment }}
Environment=PORT={{ app_listen_port }}
Environment=DATABASE_URL={{ database_url }}

# Feature Flags
Environment=ENABLE_STAGING_FEATURES={{ enable_staging_features | string | lower }}
Environment=ENABLE_GRAPHQL_PLAYGROUND={{ enable_graphql_playground | string | lower }}
Environment=ENABLE_DEBUG={{ enable_debug | string | lower }}

# CORS Configuration
Environment=CORS_ENABLED={{ cors_enabled | string | lower }}
Environment=CORS_ALLOWED_ORIGINS={{ cors_allowed_origins | join(',') }}

# Scheduler and Auth
Environment=USE_JWT_MIDDLEWARE={{ use_jwt_middleware | string | lower }}
Environment=ENABLE_AUTOMATED_SCHEDULER={{ enable_automated_scheduler | string | lower }}
Environment=DONOR_EXPIRATION_CRON="{{ donor_expiration_cron }}"

# Stripe Configuration
Environment=STRIPE_SECRET_KEY={{ vault_stripe_live_key }}
Environment=STRIPE_PUBLISHABLE_KEY={{ vault_stripe_live_publishable_key }}

ExecStart={{ keystone_app_path }}/node_modules/.bin/keystone start --no-ui
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=yes
ProtectSystem=full
ProtectHome=read-only
PrivateTmp=yes

[Install]
WantedBy=multi-user.target 