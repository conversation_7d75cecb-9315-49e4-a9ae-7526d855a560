# Let's Encrypt Role (Deprecated)

## ⚠️ DEPRECATION NOTICE

This role is deprecated and will only work for the metabase-app host. For all other hosts, please use the `nginx_certbot` role instead.

## Usage

This role can only be used for the metabase-app host. Any attempt to use it on other hosts will result in a failure.

Example:
```yaml
- hosts: metabase-app
  become: yes
  roles:
    - role: letsencrypt
      vars:
        domain_name: "metabase.example.com"
```

## Migration

To migrate from this role to the new `nginx_certbot` role:

1. Replace the letsencrypt role with nginx_certbot in your playbook
2. Update the variables to match the nginx_certbot format
3. Add any application-specific configuration needed

Example migration:
```yaml
# Old configuration
- hosts: webserver
  become: yes
  roles:
    - role: letsencrypt
      vars:
        domain_name: "example.com"

# New configuration
- hosts: webserver
  become: yes
  roles:
    - role: nginx_certbot
      vars:
        domain_name: "example.com"
        certbot_admin_email: "<EMAIL>"
```

## License

MIT 