---
# Validate that this role is only used for metabase-app
- name: Validate host is metabase-app
  fail:
    msg: "The letsencrypt role is deprecated and can only be used for metabase-app. Please use nginx_certbot role for other hosts."
  when: inventory_hostname != "metabase-app"

# Validate that domain_name is set
- name: Check if domain_name is set
  fail:
    msg: "The variable 'domain_name' is required but not set."
  when: domain_name is not defined

# Install Certbot and Nginx plugin
- name: Install Certbot and Nginx plugin
  apt:
    name:
      - certbot
      - python3-certbot-nginx
    state: present
    update_cache: yes

# Obtain SSL certificate for the domain
- name: Obtain SSL certificate for {{ domain_name }}
  command: certbot --nginx -d {{ domain_name }} --non-interactive --agree-tos --email <EMAIL>
  notify:
    - Reload Nginx

# Test SSL certificate renewal
- name: Test SSL certificate renewal
  command: certbot renew --dry-run
