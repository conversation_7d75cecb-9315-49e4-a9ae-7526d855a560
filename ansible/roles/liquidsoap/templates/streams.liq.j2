# THIS IS CURRENTLY NOT A PROPER TEMPLATE!

########################## SETTINGS ###########################
# Log Level
settings.server.log.level.set(5)
# Log to file
#settings.log.file.set(true)
# Append log to the file
settings.log.file.append.set(true)
# TODO: raise ulimit for this process
#list.hd(process.read.lines("ulimit -n 16384"))

########################### INPUTS ###########################

# Barix 500 STL at KPFA-Studios on BARIX-500 (10.0.2.21:8082). Source: [Wheatnet] [bPGM]
# Transport: RTP (UDP). Codec: PCM (s16be). Sample Rate: 48 kHz. Bit Depth: 16 bit. Bit Rate: 1536.2 kbps (CBR). Channels: 2 (stereo).
kpfa_bpgm_at_studios_via_barix500_rtp = input.ffmpeg(id="kpfa_bpgm_at_studios_via_barix500_rtp", self_sync=true,
  string_args=[("protocol_whitelist", "file,udp,rtp"),("reorder_queue_size","0")],"streams_available/kpfa_bpgm_at_studios_via_barix500_rtp.sdp")

# Barix 100 at studios (64.4.175.167:3033) [AIR]
# Transport: RTP (UDP). Codec: MP3. Sample Rate: 48 kHz. Bit Depth: var bit. Bit Rate: 192 kbps (CBR). Channels: 2 (stereo).
kpfa_studios_barix100_rtp = input.ffmpeg(id="kpfa_studios_barix100_rtp", self_sync=true,
  string_args=[("protocol_whitelist", "file,udp,rtp"),("reorder_queue_size","0")],"streams_available/kpfa_studios_barix100_rtp.sdp")

########################### ALERTS ###########################
# log & notify via slack
## transitions
def source_transition(old,new) =
        print("[SOURCE.ID OLD]: "^ source.id(old))
        print("[SOURCE.ID NEW]: "^ source.id(new))
        log("[SOURCE.ID OLD]: "^ source.id(old))
        log("[SOURCE.ID NEW]: "^ source.id(new))
  ## slack
        ignore(http.post(
                headers=[("Content-Type","application/json")],
                data="{\"text\":
                        \"[kpfa] stream source changed from [#{source.id(old)}] to [#{source.id(new)}] \"}",
                "https://hooks.slack.com/triggers/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"
        ))
  ##
  sequence([old,new])
end


########################### FALLBACKS ###########################
# Immediate switching achieved by adding the argument track_sensitive=false
# Silence detect the streams (15 sec of silence under -50dB, return after 1 second of audio)
# Fall back to silence (blank) means we don't need to mksafe() the inputs.
# stripped down version
source = fallback(id="fallback",
                  track_sensitive=false,
                  replay_metadata=false,
                  transition_length=0.0,
                  transitions=[source_transition],
                  [
      blank.strip(id="kpfa_bpgm_at_studios_via_barix500_rtp", max_blank=20., min_noise=2., threshold=-60., kpfa_bpgm_at_studios_via_barix500_rtp),
      blank.strip(id="kpfa_studios_barix100_rtp", max_blank=20., min_noise=2., threshold=-60., kpfa_studios_barix100_rtp),
                        blank(id="blank")
                              ])
########################### PROCESS ###########################
# Apply normalization (-14 LUFS) for Stream
# https://www.aes.org/technical/documentDownloads.cfm?docID=731
stream = normalize(source, target=-14., gain_max=12., gain_min=-12., down=0.1, up=5., threshold=-30., lufs=true)

# Apply normalization (-24 LUFS) for US Broadcast -> bPGM streams!
broadcast = normalize(source, target=-24., gain_max=12., gain_min=-12., down=0.1, up=5., threshold=-30., lufs=true)

########################### OUTPUTS ###########################
# dummy output to suppress error log
output.dummy(fallible=true, kpfa_bpgm_at_studios_via_barix500_rtp)
output.dummy(fallible=true, kpfa_studios_barix100_rtp)

# broadcast normalization levels

# Icecast MP3 192(stereo)
output.icecast(
  %mp3(bitrate=192, samplerate=48000, stereo=true, stereo_mode="stereo"), 
  mount="kpfa_bPGM_192.mp3",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",  
  description="KPFA 94.1FM Berkeley", 
  name="KPFA bPGM (-24LUFS)", 
  url="https://kpfa.org", 
  broadcast)
  
# Icecast MP3 128(stereo)
output.icecast(
  %mp3(bitrate=128, samplerate=48000, stereo=true, stereo_mode="stereo"), 
  mount="kpfa_bPGM_128.mp3",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",  
  description="KPFA 94.1FM Berkeley", 
  name="KPFA bPGM (-24LUFS)", 
  url="https://kpfa.org", 
  broadcast)

# Icecast MP3 64(joint_stereo)
output.icecast(
  %mp3(bitrate=64, samplerate=48000, stereo=true, stereo_mode="joint_stereo"),
  mount="kpfa_bPGM_64.mp3",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",  
  description="KPFA 94.1FM Berkeley", 
  name="KPFA bPGM (-24LUFS)", 
  url="https://kpfa.org",
  broadcast)

# Icecast MP3 24(mono)
output.icecast(
  %mp3(bitrate=24, samplerate=22050, stereo=false),
  mount="kpfa_bPGM_24.mp3",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",  
  description="KPFA 94.1FM Berkeley", 
  name="KPFA bPGM (-24LUFS)", 
  url="https://kpfa.org", 
  mean(broadcast))


#----------------------------
# stream normalization levels
#----------------------------

# mp3 streams

# Icecast MP3 192(stereo)
output.icecast(
  %mp3(bitrate=192, samplerate=48000, stereo=true, stereo_mode="stereo"), 
  mount="kpfa_192.mp3",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",  
  description="KPFA 94.1FM Berkeley", 
  name="KPFA", 
  url="https://kpfa.org", 
  stream)
  
# Icecast MP3 128(stereo)
output.icecast(
  %mp3(bitrate=128, samplerate=48000, stereo=true, stereo_mode="stereo"), 
  mount="kpfa_128.mp3",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",  
  description="KPFA 94.1FM Berkeley", 
  name="KPFA", 
  url="https://kpfa.org", 
  stream)

# Icecast MP3 64(joint_stereo)
output.icecast(
  %mp3(bitrate=64, samplerate=48000, stereo=true, stereo_mode="joint_stereo"),
  mount="kpfa_64.mp3",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",  
  description="KPFA 94.1FM Berkeley", 
  name="KPFA", 
  url="https://kpfa.org",
  stream)

# Icecast MP3 24(mono)
output.icecast(
  %mp3(bitrate=24, samplerate=22050, stereo=false),
  mount="kpfa_24.mp3",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",  
  description="KPFA 94.1FM Berkeley", 
  name="KPFA", 
  url="https://kpfa.org", 
  mean(stream))


# aac streams

# Icecast AAC (LC) 192(stereo) - ffmpeg encoder
output.icecast(
  %ffmpeg(format="adts",
    %audio(codec="aac", channels=2, samplerate=48000, b="192k")),
  send_icy_metadata=false,
  mount="/kpfa_192.aac",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",
  description="KPFA 94.1FM Berkeley",
  name="KPFA",
  url="https://kpfa.org",
  stream)

# Icecast AAC (LC) 128(stereo) - ffmpeg encoder
output.icecast(
  %ffmpeg(format="adts",
    %audio(codec="aac", channels=2, samplerate=48000, b="128k")),
  send_icy_metadata=false,
  mount="/kpfa_128.aac",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",
  description="KPFA 94.1FM Berkeley",
  name="KPFA",
  url="https://kpfa.org",
  stream)

# Icecast AAC (LC) 64(stereo) - ffmpeg encoder
output.icecast(
  %ffmpeg(format="adts",
    %audio(codec="aac", channels=2, samplerate=48000, b="64k")),
  send_icy_metadata=false,
  mount="/kpfa_64.aac",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",
  description="KPFA 94.1FM Berkeley",
  name="KPFA",
  url="https://kpfa.org",
  stream)

# Icecast AAC (LC) 32(stereo) - ffmpeg encoder
output.icecast(
  %ffmpeg(format="adts",
    %audio(codec="aac", channels=2, samplerate=48000, b="32k")),
  send_icy_metadata=false,
  mount="/kpfa_32.aac",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",
  description="KPFA 94.1FM Berkeley",
  name="KPFA",
  url="https://kpfa.org",
  stream)


#### KPFA AIR (no processing) ####
# Icecast AAC (LC) 192(stereo)
output.icecast(
  %ffmpeg(format="adts",
    %audio(codec="aac", channels=2, samplerate=48000, b="192k")),
  send_icy_metadata=false,
  mount="/kpfa_AIR_192.aac",
  host="localhost", port=8000, password="{{ icecast_source_password }}",
  genre="Various",
  description="KPFA 94.1FM Berkeley",
  name="KPFA",
  url="https://kpfa.org",
  mksafe(kpfa_bpgm_at_studios_via_barix500_rtp))
