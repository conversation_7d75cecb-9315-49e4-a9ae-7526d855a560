# Set password for PostgreSQL metabase superuser
- name: Set password for PostgreSQL metabase superuser
  become: yes
  become_user: postgres
  postgresql_user:
    name: metabase
    password: "{{ metabase_password }}" # Replace with your actual password variable
    encrypted: yes
    state: present
    role_attr_flags: "SUPERUSER"
  tags:
    - metabase_setup

# Create PostgreSQL database for Metabase with metabase as owner
- name: Create PostgreSQL database for Metabase
  become: yes
  become_user: postgres
  postgresql_db:
    name: metabase
    owner: metabase
  tags:
    - metabase_setup

# Configure pg_hba.conf for metabase user access from a specific IP
- name: Allow metabase user access from **********
  become: yes
  lineinfile:
    path: "/etc/postgresql/{{ postgresql_version }}/main/pg_hba.conf"
    insertafter: EOF
    line: "host    metabase    metabase    **********/32   md5"
  tags:
    - metabase_setup

# Restart PostgreSQL to apply changes
- name: Restart PostgreSQL
  become: yes
  service:
    name: postgresql
    state: restarted
  tags:
    - metabase_setup

# todo: improve, test and incorporate these provisioning steps - they were done manually for the current setup.
# Added static local IP - currently ********* for KPFA production API 
# Mysql bind to any IP -
# Set with: vi /etc/mysql/mysql.conf.d/mysqld.cnf
# Instead of skip-networking the default is now to listen only on                                                       │
# localhost which is more compatible and is not less secure.                                                            │
# bind-address            = 0.0.0.0    
#
# CREATE USER 'db_user'@'**********' IDENTIFIED BY 'password-here'; 
# GRANT ALL PRIVILEGES ON *.* TO 'api_db'@'**********';
# ALTER USER 'db_user'@'**********' REQUIRE NONE; 
# ALTER USER 'db_user'@'**********' IDENTIFIED WITH 'mysql_native_password' BY 'password-here'; 
# FLUSH PRIVILEGES; 