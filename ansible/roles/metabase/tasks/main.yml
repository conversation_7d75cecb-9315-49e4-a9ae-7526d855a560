---
# Install Nginx
- name: Install Nginx
  apt:
    name:
      - nginx
    state: present
    update_cache: yes

# Copy Nginx configuration for Metabase
- name: Create Nginx configuration for Metabase
  template:
    src: nginx-metabase.conf.j2
    dest: /etc/nginx/sites-available/{{ domain_name }}
    mode: "0644"

# Enable Nginx site configuration
- name: Enable Nginx site for Metabase
  file:
    src: /etc/nginx/sites-available/{{ domain_name }}
    dest: /etc/nginx/sites-enabled/{{ domain_name }}
    state: link

# Disable default Nginx configuration
- name: Remove default Nginx site
  file:
    path: /etc/nginx/sites-enabled/default
    state: absent

- name: Add GPG key for Eclipse Temurin
  apt_key:
    url: https://packages.adoptium.net/artifactory/api/gpg/key/public
    state: present
  become: yes

- name: Add Eclipse Temurin PPA
  apt_repository:
    repo: "deb https://packages.adoptium.net/artifactory/deb focal main"
    state: present
  become: yes

- name: Install Java JRE 21 from Eclipse Temurin
  apt:
    name: temurin-21-jre
    state: present
    update_cache: yes
  become: yes

- name: Create metabase user
  user:
    name: metabase
    shell: /bin/bash
    create_home: yes

# Metabase Setup
- name: Create Metabase directory
  file:
    path: /opt/metabase
    state: directory
    mode: "0755"
    owner: metabase

- name: Download Metabase JAR
  get_url:
    url: https://downloads.metabase.com/v0.53.6/metabase.jar
    dest: /opt/metabase/metabase.jar
    mode: "0755"
    owner: metabase
    group: metabase

# Backup directory creation
- name: Create backup directory
  file:
    path: /backup
    state: directory
    mode: "0755"
    owner: metabase

# Systemd service for Metabase
- name: Add Metabase systemd service
  template:
    src: metabase.service.j2
    dest: /etc/systemd/system/metabase.service
    mode: "0644"
    owner: metabase
  tags: metabase_service

- name: Reload systemd daemon
  systemd:
    daemon_reload: yes
  tags: metabase_service

- name: Start and enable Metabase service
  systemd:
    name: metabase
    state: started
    enabled: yes
  tags: metabase_service
