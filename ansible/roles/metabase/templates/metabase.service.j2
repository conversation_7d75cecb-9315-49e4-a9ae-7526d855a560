[Unit]
After=syslog.target
Description=Metabase service
After=network.target

[Service]
User=metabase
ExecStart=/usr/bin/java -jar /opt/metabase/metabase.jar
Environment="MB_JETTY_HOST=127.0.0.1"
Environment="MB_JETTY_PORT=3000"
Environment="MB_DB_TYPE=postgres"
Environment="MB_DB_DBNAME=metabase"
Environment="MB_DB_PORT=5433"
Environment="MB_DB_HOST=**********"
Environment="MB_DB_USER=metabase"
Environment="MB_DB_PASS={{ postgres_metabase_password }}"
Restart=always
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=metabase

[Install]
WantedBy=multi-user.target
