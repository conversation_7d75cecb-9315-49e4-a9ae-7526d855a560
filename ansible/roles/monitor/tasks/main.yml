---
- set_fact:
    real_ansible_host: "{{ ansible_host }}"

- name: install fim
  package:
    name: fim
    state: present

- name: install omxplayer
  package:
    name: omxplayer
    state: present

# Support of the omxplayer library
# Commented out for now as per David...
# "We should remove the current node playbook from ansible, replace it with an
# nvm playbook, and set that playbook to node 14."
# - name: install nodejs
#   package:
#     name: nodejs
#     state: present

# In order to display videos, redirect the console to the serial port so it
# isn't writing to the screen.  Then put up an image for the screen.
- name: Turn of dmesg logging to console
  lineinfile:
    path: /etc/rc.local
    state: present
    insertbefore: "^exit 0"
    line: "/usr/bin/dmesg -D"

- name: Enable Serial Port
  lineinfile:
    path: /boot/config.txt
    state: present
    line: "enable_uart=1"

- name: Test screen blanking is already turned off
  shell: grep -c "^.*consoleblank=0" /boot/cmdline.txt|| true
  register: test_grep_screenblank

- name: turn off screen blanking
  replace:
    dest: /boot/cmdline.txt
    regexp: '^(.* .*)$'
    replace: '\1 consoleblank=0'
  when: test_grep_screenblank.stdout == '0'

- name: Test Raspberry Logo is already turned off
  shell: grep -c "^.*logo.nologo" /boot/cmdline.txt|| true
  register: test_grep_logo

- name: turn off Raspberry Logo
  replace:
    dest: /boot/cmdline.txt
    regexp: '^(.* .*)$'
    replace: '\1 logo.nologo'
  when: test_grep_logo.stdout == '0'

# Need to create /home/<USER>/scripts
- name: Creates Pi's scripts directory
  file:
    path: /home/<USER>/scripts
    state: directory
    owner: pi
    group: pi
    mode: 0775

# Copy the Pi scripts into place...
- copy:
    src: files/home/<USER>/scripts/stop_omxplayer.sh
    dest: /home/<USER>/scripts/stop_omxplayer.sh
    owner: pi
    group: pi
    mode: 0755

# Copy the Galaxy image in place...
- copy:
    src: files/home/<USER>/galaxy-messier-192-1200px.jpg
    dest: /home/<USER>/galaxy-messier-192-1200px.jpg
    owner: pi
    group: pi
    mode: 0644

- name: Lastly... add the galaxy image command to rc.local
  lineinfile:
    path: /etc/rc.local
    state: present
    insertbefore: "^exit 0"
    line: "fim -q -a /home/<USER>/galaxy-messier-192-1200px.jpg > /dev/null 2>&1 &"

# Need to create /home/<USER>/videos
# Hand populate the videos later?
- name: Creates Pi's videos directory
  file:
    path: /home/<USER>/videos
    state: directory
    owner: pi
    group: pi
    mode: 0775

# Add pi and other users to the video group.
- name: adding existing user pi to group video
  user:
    name: pi
    groups: video
    append: yes

- name: Flush file system
  shell: sync

# Add samba 
# configure home directories to be r/w
# smbpasswd -a pi <with password>

# - name: 'Reboot'
#   shell: sleep 2 && reboot
#   async: 1
#   poll: 0
#   ignore_errors: true

# - name: "Wait for Raspberry PI to come back"
#   local_action: wait_for host={{ real_ansible_host }} port=22 state=started delay=10
#   become: false
