# Nginx Certbot Role

This role installs and configures Nginx with Let's Encrypt SSL certificates using Certbot. It provides a secure reverse proxy setup with automatic SSL certificate management.

## Features

- Application-agnostic Nginx + SSL configuration that can be customized for any app
- Automated SSL certificate management with Let's Encrypt
- Secure Nginx configuration with modern SSL settings
- Support for both TCP ports and Unix sockets
- Automatic HTTP to HTTPS redirection
- Proxy parameter configuration for proper header forwarding
- Zero-downtime certificate provisioning with automatic bootstrapping

## How It Works

The role follows a smart bootstrapping approach for SSL certificates:

1. **Detection Phase**: The role first checks if SSL certificates already exist for your domain
2. **Bootstrap Mode**: If no certificates exist, the role:
   - Sets up a temporary HTTP-only nginx configuration
   - Configures the webroot for ACME challenges
   - Keeps nginx running (rather than stopping it) during certificate acquisition
   - Obtains certificates via the webroot method
3. **Full Configuration**: Once certificates are available, the role sets up the full HTTP+HTTPS configuration
4. **Certificate Renewal**: Configures automatic renewal via a cron job

This approach ensures nginx is always available to serve ACME challenges, avoiding the chicken-and-egg problem where ngin<PERSON> needs certificates to start but certbot needs nginx running to validate the domain.

## Requirements

- Ubuntu/Debian system
- Python 3
- Access to port 80/443 for Let's Encrypt verification
- A valid domain name pointing to your server

## Role Variables

### Required Variables

```yaml
domain_name: "example.com" # The domain name for the SSL certificate
certbot_admin_email: "<EMAIL>" # Email for Let's Encrypt notifications
```

### Optional Variables

```yaml
# SSL Configuration
certbot_staging: false # Use Let's Encrypt staging environment (default: false)
ssl_protocols: "TLSv1.2 TLSv1.3" # SSL protocols to enable
ssl_ciphers: "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384"

# Nginx Configuration
app_listen_port: 8080 # Port where your application listens (default: 8080)
nginx_client_max_body_size: "64M" # Maximum upload size
nginx_remove_default_vhost: true # Remove default nginx vhost
web_root: "/var/www/html" # Default web root directory
create_proxy_params: false # Whether to create the proxy_params file

# Application-specific configuration
app_specific_https_config:
  | # Define your application-specific Nginx config here
  # This will be included in the HTTPS server block
  root /var/www/myapp;
  index index.php index.html;

  location / {
    try_files $uri $uri/ /index.php$is_args$args;
  }

  location ~ \.php$ {
    include fastcgi_params;
    fastcgi_pass unix:/run/php/php8.1-fpm.sock;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
  }
```

## Dependencies

This role depends on:

- geerlingguy.nginx
- geerlingguy.certbot

Add to your `requirements.yml`:

```yaml
- name: geerlingguy.nginx
  version: 3.2.0
- name: geerlingguy.certbot
  version: 5.3.0
```

## Example Playbooks

### Basic usage

```yaml
- hosts: webservers
  become: yes
  roles:
    - role: nginx_certbot
      vars:
        domain_name: "myapp.example.com"
        certbot_admin_email: "<EMAIL>"
        app_listen_port: 8080
```

### Using with LibreTime

```yaml
- hosts: libretime.example.com
  become: yes
  roles:
    - role: nginx_certbot
      vars:
        domain_name: "libretime.example.com"
        certbot_admin_email: "<EMAIL>"
        create_proxy_params: true
        app_listen_port: "{{ libretime_listen_port }}"
        app_specific_https_config: "{{ lookup('template', 'templates/nginx/libretime-https.j2') }}"
      tags: ["nginx", "letsencrypt", "certbot", "ssl"]
```

The LibreTime template (`templates/nginx/libretime-https.j2`) should contain:

```nginx
# LibreTime HTTPS configuration template

# Log configuration
access_log /var/log/nginx/libretime.access.log;
error_log /var/log/nginx/libretime.error.log;

# Request size configuration
client_max_body_size {{ nginx_client_max_body_size | default('64M') }};
client_body_timeout 300s;

# Proxy configuration for LibreTime
location / {
    proxy_set_header Host              $host;
    proxy_set_header X-Real-IP         $remote_addr;
    proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host  $host;
    proxy_set_header X-Forwarded-Port  $server_port;

    proxy_pass http://localhost:{{ libretime_listen_port }}/;
}

# API endpoints
location ~ ^/(api/v2|api-auth) {
    include proxy_params;
    proxy_redirect off;
    proxy_pass http://unix:/run/libretime-api.sock;
}

# Internal path for serving media files from the API
location /api/_media {
    internal;
    # This alias path must match the 'storage.path' configuration field
    alias {{ libretime_storage_dir | default('/var/lib/libretime') }};
}

# Security: prevent access to .git directories
location ~ /\.git {
    deny all;
}
```

This configuration:

- Sets up reverse proxy to the LibreTime application
- Configures API endpoints for the LibreTime API
- Handles media file serving
- Sets proper headers for reverse proxy operation
- Configures request size limits for uploads

Note: The ACME challenge location block is handled by the role itself, so it should not be included in the template.

### Using with application-specific config

```yaml
- hosts: webservers
  become: yes
  roles:
    - role: nginx_certbot
      vars:
        domain_name: "myapp.example.com"
        certbot_admin_email: "<EMAIL>"
        create_proxy_params: true
        app_specific_https_config: |
          # Log configuration
          access_log /var/log/nginx/app.access.log;
          error_log /var/log/nginx/app.error.log;

          # Web root directory
          root /var/www/myapp;

          # Default index files
          index index.php index.html index.htm;

          # PHP handler
          location ~ \.php$ {
            include fastcgi_params;
            fastcgi_pass unix:/run/php/php-fpm.sock;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
          }

          # Default location handler
          location / {
            try_files $uri $uri/ /index.php$is_args$args;
          }
```

### Using with PHP-FPM Unix socket

```yaml
- hosts: webservers
  become: yes
  roles:
    - role: nginx_certbot
      vars:
        domain_name: "myapp.example.com"
        certbot_admin_email: "<EMAIL>"
        create_proxy_params: true
        app_specific_https_config: |
          root /var/www/html;
          index index.php index.html;

          location ~ \.php$ {
            include fastcgi_params;
            fastcgi_pass unix:/run/php/php-fpm.sock;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
          }

          location / {
            try_files $uri $uri/ /index.php$is_args$args;
          }
```

## Command Examples

### Run only Nginx and SSL configuration

```bash
ansible-playbook -i inventory/hosts playbook.yml --tags nginx,ssl -u username
```

### Run everything except Nginx and SSL configuration

```bash
ansible-playbook -i inventory/hosts playbook.yml --skip-tags nginx,ssl -u username
```

### Run with Let's Encrypt staging (test) mode

```bash
ansible-playbook -i inventory/hosts playbook.yml --tags nginx,ssl -u username --extra-vars "certbot_staging=true"
```

### Run with Let's Encrypt production mode

```bash
ansible-playbook -i inventory/hosts playbook.yml --tags nginx,ssl -u username --extra-vars "certbot_staging=false"
```

### Run for a specific host only

```bash
ansible-playbook -i inventory/hosts playbook.yml --limit myapp.example.com --tags nginx,ssl -u username
```

## SSL Certificate Management

The role will:

1. Install Certbot and its Nginx plugin
2. Obtain SSL certificates for your domain
3. Configure automatic renewal
4. Set up a secure Nginx configuration
5. Redirect HTTP to HTTPS

Certificates are stored in `/etc/letsencrypt/live/{{ domain_name }}/`.

## Security Features

- Automatic HTTP to HTTPS redirection
- Modern SSL protocols and cipher suites
- Secure SSL session settings
- DH parameters for improved security
- Protection against common vulnerabilities

## Tags

- `nginx`: Configure Nginx only
- `certbot`: Configure Certbot only
- `ssl`: Configure SSL settings only
- `nginx,certbot,ssl`: Configure everything

## License

MIT

