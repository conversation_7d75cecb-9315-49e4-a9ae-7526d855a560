---
# Certbot configuration
certbot_install_method: package
certbot_auto_renew: true
certbot_auto_renew_user: root
certbot_auto_renew_hour: 3
certbot_auto_renew_minute: 30
certbot_create_if_missing: true
certbot_create_method: webroot
certbot_webroot_path: "/var/www/html"
certbot_admin_email: "<EMAIL>"  # Should be overridden in host_vars
certbot_staging: false  # Default to production mode, override in host_vars for staging/testing
certbot_install_os_packages:
  - certbot
  - python3-certbot-nginx
certbot_domains:
  - "{{ domain_name }}"
certbot_agree_tos: true
# Use --expand which handles both new certs and adding domains to existing certs
certbot_create_command: "certbot certonly --webroot --webroot-path {{ certbot_webroot_path }} --noninteractive --agree-tos --expand {% if certbot_staging|default(false) %}--staging{% endif %} --email {{ certbot_admin_email }} -d {{ domain_name }}"

# Nginx configuration
nginx_remove_default_vhost: true
nginx_client_max_body_size: "64M"

# Default proxy port to forward requests to
app_listen_port: 8080

# Default web root directory
web_root: "/var/www/html"

# Whether to create proxy_params file
create_proxy_params: false

# Directory for SSL certificates 
ssl_cert_path: "/etc/letsencrypt/live/{{ domain_name }}"
ssl_cert_file: "{{ ssl_cert_path }}/fullchain.pem"
ssl_key_file: "{{ ssl_cert_path }}/privkey.pem"

# SSL Parameters
ssl_protocols: "TLSv1.2 TLSv1.3"
ssl_ciphers: "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384"
ssl_dhparam_file: "/etc/letsencrypt/ssl-dhparams.pem" 