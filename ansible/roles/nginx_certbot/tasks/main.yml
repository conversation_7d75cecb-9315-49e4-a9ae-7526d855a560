---
# Setup SSL certificates and nginx with proper HTTPS configuration

# Ensure cron is installed for certbot renewals
- name: Ensure cron is installed
  apt:
    name: cron
    state: present
    update_cache: yes

# Ensure certbot is installed
- name: Ensure certbot is installed
  apt:
    name: certbot
    state: present
    update_cache: yes

# Ensure required directories exist
- name: Ensure SSL directories exist
  file:
    path: "/etc/letsencrypt/{{ item }}"
    state: directory
    mode: "0755"
  loop:
    - ""

# Create webroot directory for Let's Encrypt validation
- name: Ensure webroot directory exists
  file:
    path: "/var/www/html"
    state: directory
    mode: "0755"

- name: Ensure acme-challenge directory exists
  file:
    path: "/var/www/html/.well-known/acme-challenge"
    state: directory
    mode: "0755"
    recurse: yes

# Check if certificates already exist
- name: Check for existing certificates
  stat:
    path: "{{ ssl_cert_file }}"
  register: cert_file

# Create dhparams file for better SSL security
- name: Ensure dhparams file exists
  copy:
    content: |
      -----BEGIN DH PARAMETERS-----
      MIIBCAKCAQEA//////////+t+FRYortKmq/cViAnPTzx2LnFg84tNpWp4TZBFGQz
      +8yTnc4kmz75fS/jY2MMddj2gbICrsRhetPfHtXV/WVhJDP1H18GbtCFY2VVPe0a
      87VXE15/V8k1mE8McODmi3fipona8+/och3xWKE2rec1MKzKT0g6eXq8CrGCsyT7
      YdEIqUuyyOP7uWrat2DX9GgdT0Kj3jlN9K5W7edjcrsZCwenyO4KbXCeAvzhzffi
      7MA0BM0oNC9hkXL+nOmFg/+OTxIy7vKBg8P+OxtMb61zO7X8vC7CIAXFjvGDfRaD
      ssbzSibBsu/6iGtCOGEoXJf//////////wIBAg==
      -----END DH PARAMETERS-----
    dest: "{{ ssl_dhparam_file }}"
    mode: "0644"

# Create HTTP-only config for bootstrap if certificates don't exist
- name: Create HTTP-only nginx config for bootstrap if certificates don't exist
  block:
    - name: Create HTTP config for bootstrap
      template:
        src: "{{ role_path }}/templates/bootstrap-http.j2"
        dest: "/etc/nginx/sites-available/{{ domain_name }}-http.conf"
        mode: "0644"
      notify: restart nginx

    - name: Remove existing HTTP symlink/file if it exists
      file:
        path: "/etc/nginx/sites-enabled/{{ domain_name }}-http.conf"
        state: absent

    - name: Enable HTTP site
      file:
        src: "/etc/nginx/sites-available/{{ domain_name }}-http.conf"
        dest: "/etc/nginx/sites-enabled/{{ domain_name }}-http.conf"
        state: link
      notify: restart nginx

    - name: Remove HTTPS config if it exists
      file:
        path: "/etc/nginx/sites-enabled/{{ domain_name }}-https.conf"
        state: absent
      notify: restart nginx

    - name: Ensure nginx is running
      service:
        name: nginx
        state: started
  when: not cert_file.stat.exists

# Now use certbot to obtain SSL certificate if it doesn't exist
- name: Obtain SSL certificate if it doesn't exist
  command: >
    certbot certonly --webroot 
    --webroot-path /var/www/html 
    --non-interactive 
    --agree-tos 
    {% if certbot_staging | default(false) %}--staging{% endif %}
    --email {{ certbot_admin_email }}
    -d {{ domain_name }}
  when: not cert_file.stat.exists

# Refresh the certificate status
- name: Check for certificates again
  stat:
    path: "{{ ssl_cert_file }}"
  register: cert_file

# Ensure proxy_params file exists for Nginx
- name: Ensure proxy_params file exists
  template:
    src: "{{ role_path }}/templates/proxy_params.j2"
    dest: "/etc/nginx/proxy_params"
    mode: "0644"
  notify: restart nginx
  when: create_proxy_params | default(false) | bool

# Now set up the normal HTTP+HTTPS configuration
- name: Setup full nginx configuration with HTTPS
  block:
    - name: Create HTTP config
      template:
        src: "{{ role_path }}/templates/http.j2"
        dest: "/etc/nginx/sites-available/{{ domain_name }}-http.conf"
        mode: "0644"
      notify: restart nginx

    - name: Create HTTPS config
      template:
        src: "{{ role_path }}/templates/https.j2"
        dest: "/etc/nginx/sites-available/{{ domain_name }}-https.conf"
        mode: "0644"
      notify: restart nginx

    - name: Remove existing HTTP symlink/file if it exists
      file:
        path: "/etc/nginx/sites-enabled/{{ domain_name }}-http.conf"
        state: absent

    - name: Enable HTTP site
      file:
        src: "/etc/nginx/sites-available/{{ domain_name }}-http.conf"
        dest: "/etc/nginx/sites-enabled/{{ domain_name }}-http.conf"
        state: link
      notify: restart nginx

    - name: Remove existing HTTPS symlink/file if it exists
      file:
        path: "/etc/nginx/sites-enabled/{{ domain_name }}-https.conf"
        state: absent

    - name: Enable HTTPS site
      file:
        src: "/etc/nginx/sites-available/{{ domain_name }}-https.conf"
        dest: "/etc/nginx/sites-enabled/{{ domain_name }}-https.conf"
        state: link
      notify: restart nginx
  when: cert_file.stat.exists

# Verify nginx configuration
- name: Verify nginx configuration
  command: nginx -t
  changed_when: false
  register: nginx_config_valid
  ignore_errors: true

# Restart nginx to apply configuration
- name: Restart nginx if config is valid
  service:
    name: nginx
    state: restarted
  when: nginx_config_valid.rc == 0

