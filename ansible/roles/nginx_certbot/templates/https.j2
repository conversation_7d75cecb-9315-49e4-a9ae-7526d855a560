server {
  listen 443 ssl;
  server_name {{ domain_name }};
  
  # SSL configuration
  ssl_certificate {{ ssl_cert_file }};
  ssl_certificate_key {{ ssl_key_file }};
  ssl_session_cache shared:le_nginx_SSL:10m;
  ssl_session_timeout 1440m;
  ssl_session_tickets off;
  ssl_protocols {{ ssl_protocols }};
  ssl_prefer_server_ciphers off;
  ssl_ciphers "{{ ssl_ciphers }}";
  ssl_dhparam {{ ssl_dhparam_file }};
  
  # Also allow ACME challenges on HTTPS (just to be safe)
  location ^~ /.well-known/acme-challenge/ {
    allow all;
    default_type "text/plain";
    root /var/www/html;
  }
  
  {% if app_specific_https_config is defined %}
  {{ app_specific_https_config }}
  {% else %}
  # Default configuration for a simple website
  root {{ web_root | default('/var/www/html') }};
  index index.html index.htm;
  
  location / {
    try_files $uri $uri/ =404;
  }
  {% endif %}
} 