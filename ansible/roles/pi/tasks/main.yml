---
- set_fact:
    real_ansible_host: "{{ ansible_host }}"

- name: "Set the WiFi Country Code"
  shell: 'raspi-config nonint do_wifi_country US'

- name: 'Configure WIFI'
  copy: src=./files/etc/wpa_supplicant/wpa_supplicant.conf dest=/etc/wpa_supplicant/wpa_supplicant.conf mode=0600

- name: 'Update APT package cache'
  action: apt update_cache=yes

- name: 'Upgrade APT to the lastest packages'
  action: apt upgrade=safe

# - name: 'Reboot'
#   shell: sleep 2 && reboot
#   async: 1
#   poll: 0
#   ignore_errors: true

# - name: "Wait for Raspberry PI to come back"
#   local_action: wait_for host={{ real_ansible_host }} port=22 state=started delay=10
#   become: false
