---
- name: Add PostgreSQL GPG key
  apt_key:
    url: https://www.postgresql.org/media/keys/ACCC4CF8.asc
    state: present

- name: Add PostgreSQL APT repository for Ubuntu 24
  apt_repository:
    repo: "deb https://apt.postgresql.org/pub/repos/apt {{ ansible_distribution_release }}-pgdg main"
    state: present
    filename: "pgdg"
    update_cache: yes

- name: Update APT cache
  apt:
    update_cache: yes

- name: Install psycopg2 for PostgreSQL interaction
  apt:
    name: python3-psycopg2
    state: present

- name: Install PostgreSQL
  apt:
    name:
      - "postgresql-{{ postgresql_version }}"
      - postgresql-contrib
    state: present
    update_cache: yes

- name: Ensure PostgreSQL is started and enabled
  service:
    name: postgresql
    state: started
    enabled: yes

- name: Configure PostgreSQL to listen on all interfaces
  lineinfile:
    path: "/etc/postgresql/{{ postgresql_version }}/main/postgresql.conf"
    regexp: "^#?listen_addresses ="
    line: "listen_addresses = '*'"
    state: present

- name: Allow specific host to connect to PostgreSQL
  lineinfile:
    path: "/etc/postgresql/{{ postgresql_version }}/main/pg_hba.conf"
    insertafter: EOF
    line: "host    all             all             {{ allowed_host }}            md5"

- name: Restart PostgreSQL
  service:
    name: postgresql
    state: restarted
