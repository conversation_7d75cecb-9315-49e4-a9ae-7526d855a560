---
- name: Copy static netplan configuration file with variables
  become: yes
  copy:
    content: |
      network:
        version: 2
        ethernets:
          {{ interface }}:
            addresses:
              - "{{ private_ip }}{{ netmask }}"  # IPv4 private IP with netmask
              - "{{ public_ip }}/25"  # Public IP with the correct /25 netmask
              - "{{ ipv6_address }}/64"
            routes:
              - to: 0.0.0.0/0
                via: "{{ gateway }}"  # IPv4 gateway
              - to: "::/0"
                via: "{{ ipv6_gateway }}"  # IPv6 gateway
            nameservers:
              addresses:
                - "*******"  # IPv4 DNS servers as a list
                - "*******"  # IPv4 DNS servers as a list
                - "2606:4700:4700::1111"  # IPv6 DNS servers as a list
                - "2606:4700:4700::1001"  # IPv6 DNS servers as a list
            dhcp4: false
            dhcp6: false
    dest: /etc/netplan/00-installer-config.yaml
    mode: "0600"

- name: Apply netplan configuration
  become: yes
  command: netplan apply
