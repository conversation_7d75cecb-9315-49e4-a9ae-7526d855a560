---
- name: Update root user
  become: yes
  hosts:
  gather_facts: yes

  vars:
    username: root
    password:
    password_update: always

  tasks:
  - name: Update root password
    user: name={{ username }} password={{ password }} update_password={{ password_update }}

  - name: Disable ssh for root
    lineinfile: "dest=/etc/ssh/sshd_config state=present regexp='^#PermitRootLogin' line='PermitRootLogin no'"

  - name: Restart sshd
    service: name=sshd state=restarted
