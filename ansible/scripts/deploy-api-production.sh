#!/bin/bash

# Deploy API to production
cd "$(dirname "$0")/.." # Change to ansible directory

# Safety check - require explicit confirmation for production deployment
if [[ "$1" != "--confirm" ]] && [[ "$2" != "--confirm" ]]; then
    echo "⚠️  PRODUCTION DEPLOYMENT SAFETY CHECK"
    echo "This will deploy to the LIVE PRODUCTION API server at api.kpfa.org"
    echo ""
    echo "🔍 This deployment will:"
    echo "   - Deploy latest code from master branch"
    echo "   - Run composer install with production dependencies (no dev packages)"
    echo "   - Apply database migrations to LIVE production database"
    echo "   - Update file permissions"
    echo ""
    echo "🛡️  SAFETY REMINDERS:"
    echo "   - Ensure all changes are tested on staging first"
    echo "   - Verify database migrations are safe for production data"
    echo "   - Consider maintenance window for major updates"
    echo ""
    echo "⚡ To proceed, type 'production' and press Enter:"
    read -p "Confirmation: " confirmation

    if [[ "$confirmation" != "production" ]]; then
        echo "❌ Deployment cancelled. You must type 'production' exactly to proceed."
        echo ""
        echo "💡 Alternative: Use --confirm flag to skip interactive confirmation:"
        echo "   ./scripts/deploy-api-production.sh --confirm           # Deploy code only"
        echo "   ./scripts/deploy-api-production.sh --full-setup --confirm  # Full infrastructure + deploy"
        exit 1
    fi

    echo "✅ Confirmation received. Proceeding with production deployment..."
    echo ""
fi

# Check if --full-setup flag is provided
if [[ "$1" == "--full-setup" ]] || [[ "$2" == "--full-setup" ]]; then
    echo "🔄 Running full production setup including infrastructure..."
    ansible-playbook -i inventory/hosts \
      playbook.yml \
      --limit api.production.kpfa.org \
      --tags "api" \
      -u paloma \
      --vault-password-file .vault-password.txt
else
    echo "🚀 Running production deployment only (code + composer install + migrations)..."
    ansible-playbook -i inventory/hosts \
      playbook.yml \
      --limit api.production.kpfa.org \
      --tags "git_ops,git_clone,git_config,composer_deps,permissions,migrations" \
      -u paloma \
      --vault-password-file .vault-password.txt
fi

echo "✅ API production deployment completed successfully!"
echo "🔗 Live at: https://api.kpfa.org"
echo "📋 The deployment includes:"
echo "   - Latest code from master branch"
echo "   - Composer install with production dependencies (no dev packages)"
echo "   - Database migrations applied to production database (including QCD donation type changes)"
echo "   - Proper file permissions set"
echo ""
echo "💡 Usage:"
echo "   ./scripts/deploy-api-production.sh --confirm           # Deploy code only"
echo "   ./scripts/deploy-api-production.sh --full-setup --confirm  # Full infrastructure + deploy"
