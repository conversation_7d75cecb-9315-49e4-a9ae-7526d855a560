#!/bin/bash

# Deploy API to staging with Stripe library updates
cd "$(dirname "$0")/.." # Change to ansible directory

# Check if --full-setup flag is provided
if [[ "$1" == "--full-setup" ]]; then
    echo "🔄 Running full setup including infrastructure..."
    ansible-playbook -i inventory/hosts \
      playbook.yml \
      --limit api.staging.kpfa.org \
      --tags "api" \
      -u paloma \
      --vault-password-file .vault-password.txt
else
    echo "🚀 Running deployment only (code + composer install + migrations)..."
    ansible-playbook -i inventory/hosts \
      playbook.yml \
      --limit api.staging.kpfa.org \
      --tags "git_ops,git_clone,git_config,composer_deps,permissions,migrations" \
      -u paloma \
      --vault-password-file .vault-password.txt
fi

echo "✅ API staging deployment completed successfully!"
echo "🔗 Test at: https://api.staging.kpfa.org"
echo "📋 The deployment includes:"
echo "   - Latest code from 70/update-stripe branch"
echo "   - Composer install with updated Stripe library"
echo "   - Proper file permissions"
echo ""
echo "💡 Usage:"
echo "   ./scripts/deploy-api-staging.sh           # Deploy code only"
echo "   ./scripts/deploy-api-staging.sh --full-setup  # Full infrastructure + deploy"
