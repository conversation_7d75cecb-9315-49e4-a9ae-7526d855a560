#!/bin/bash

# Deploy Keystone to staging
cd "$(dirname "$0")/.." # Change to ansible directory

# Check if --full-setup flag is provided
if [[ "$1" == "--full-setup" ]]; then
    echo "🔄 Running full setup including database..."
    ansible-playbook -i inventory/hosts \
      --tags "keystone" \
      --limit keystone.staging.kpfa.org \
      -u paloma \
      playbook.yml
else
    echo "🚀 Running deployment only (skipping infrastructure setup)..."
    ansible-playbook -i inventory/hosts \
      --skip-tags "common,static_ip,postgresql,nginx,letsencrypt,certbot,ssl" \
      --tags "deploy" \
      --limit keystone.staging.kpfa.org \
      -u paloma \
      playbook.yml
fi

echo "✅ Staging deployment completed successfully!" 