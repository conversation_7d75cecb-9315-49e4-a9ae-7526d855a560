#!/usr/bin/env python3
import sys
import os
import re
import yaml
import argparse

# Collects user info to put into ansible/inventory/group_vars/all.yml
#   - username: matt
#     name: <PERSON>
#     state: present
#     groups: sudo
#     uid: 2000
#     nxos_hash: "###"
#     sha512_hash: "###"
#     bcrypt_hash: "###"
#     junosClass: super-user
#     sshKeys: "{{ lookup('file', 'inventory/group_vars/sshkeys/matt.pub') }}"
#     admin_group: true
#     volunteer_group: false
#     sfo01_rs_group: false

# checks that python 3 is used
python_version = sys.version_info.major
if python_version == 2:
    sys.exit("You are trying to run this using Python 2. Please use Python 3")

else:
    try:
        import crypt
        import bcrypt
        import getpass
        import hashlib
    except ImportError:
        sys.exit("Please install required modules:\nsudo apt-get install python-dev ; sudo pip install passlib bcrypt")

# default_ansible_dir = os.path.join(os.path.dirname(__file__), "..", "git", "ansible")
default_ansible_dir = os.path.join(os.path.dirname(__file__), ".." )
parser = argparse.ArgumentParser()
parser.add_argument('--ansible_base', help='path to the ansible folder', default=default_ansible_dir)
args = parser.parse_args()

# Get the top uid number that is already assigned and use the next number...
# Load file from relative getpasspath
basepath = os.path.split(args.ansible_base)
basepath = basepath + ("inventory", "group_vars", "all.yml")
fname = os.path.join(*basepath)
f = open(fname)

uids = []
# Parse file
doc = yaml.load(f, Loader=yaml.FullLoader)
users_doc = doc["users"]
for user_data in users_doc:
    uid = ""
    if "uid" in user_data:
         uid = user_data["uid"]
    uid=int(uid)
    uids.append(uid)

suids = sorted(uids)
uid = suids[-1]
uid += 1

# user info
password = getpass.getpass("Please enter your password: ").encode()
password_check = getpass.getpass("Please confirm your password: ").encode()

if password != password_check:
    sys.exit("Your password doesn't match! Please try running this script again.")
else:
    print("\nMatching password! Please continue...\n")

login = input("Please enter your user login: ")

# want to write a login check to make sure the selected login isn't already in use
#login_check = 
#if login == login_check:
#    sys.exit("That login is in use. Please try running this script again and select an alternate login ID.")
#else:
#    print("\nYour login is unique! Please continue...\n")

fullName = input("Please enter your full name: ")
email = input("Please enter your email: ")
phone = input("Please enter your phone: ")

sha512_hash = hashlib.sha512(password).hexdigest()
bcrypt_hash = bcrypt.hashpw(password,bcrypt.gensalt())

print("\n")
print("## Add the text below to the end of the \"users\" section of:\n## %s" % fname)
print("  - username: %s" % login)
print("    name: %s" % fullName)
print("    email: %s" % email)
print("    phone: %s" % phone)
print("    state: present")
print("    groups: sudo")
print("    uid: %d" % uid)
print("    sha512_hash: \"%s\"" % sha512_hash)
print("    bcrypt_hash: \"%s\"" % bcrypt_hash.decode())
print("    sshKeys: \"{{ lookup('file', 'inventory/group_vars/sshkeys/%s.pub') }}\"" % login)
print("    admin_group: true")
print("    volunteer_group: false")
print("## End of your section to be added.")
print("\n## Please add your public ssh key at:\n## ../inventory/group_vars/sshkeys/%s.pub" % login)
print("\n## Please check these changes into git under a branch called \"%s\" and open a PR for review.\n" % login)

