<?php
# set your default time-zone
date_default_timezone_set("America/Los_Angeles");

# Money Format
setlocale(LC_MONETARY, "en_US");

# Station Name
$station_name = "{{ station_name | default('KPFA') }}";

# admin email
$admin_email = "{{ admin_email }}";

# donor_dir email
$donor_dir_email = "{{ donor_dir_email }}";

# api_url
$api_url = "{{ ansible_fqdn }}";

# admin_url
$admin_url = "{{ admin_url | default('https://admin.kpfa.org/') }}";

# members_url
$members_url = "{{ members_url | default('https://members.kpfa.org/') }}";

# stripe keys
$stripe_live_key = "{{ vault_stripe_live_key | default('sk_live_your_key_here') }}";
$stripe_testing_key = "{{ vault_stripe_testing_key | default('sk_test_8Nhs4cLJieHplDES5aTXByoH') }}";
$stripe_live_product_id = "{{ vault_stripe_live_product_id | default('prod_Iu6NoGJQH5L35H') }}"; // stripe_product_id of donation
$stripe_test_product_id = "{{ vault_stripe_test_product_id | default('prod_Iu8KSDZ1IFcY0k') }}"; // stripe_product_id of donation
$endpoint_secret = "{{ vault_stripe_endpoint_secret | default('whsec_your_secret_here') }}";

# Google oAuth
// init configuration
$clientID = "{{ vault_google_client_id | default('your-client-id.apps.googleusercontent.com') }}";
$clientSecret = "{{ vault_google_client_secret | default('your-client-secret') }}";
$redirectUri = "$api_url/login";

# Slack
$slack_bearer_token = "{{ vault_slack_bearer_token | default('xoxb-your-token-here') }}";
$slack_channel = "{{ vault_slack_channel | default('your-channel-id') }}";

# Gitea
$gitea_secret_key = "{{ vault_gitea_secret_key | default('your-secret-key') }}";

# Cloudflare Workers
$cloudflare_show_sync_token = "{{ vault_cloudflare_token | default('your-token-here') }}";

# database
// specify your own database credentials
$db_host = "localhost";
$db_name = "api_db";
$db_user = "api_db";
$db_pass = "{{ vault_mariadb_api_user_password }}";
$db_type = "mysql"; // mysql or pgsql

# JWT
// variables used for jwt
// HS256 is symmetric algorithm, it does not require private/public key pairs.
$key = "{{ vault_jwt_key | default('your-secure-key-here') }}";
$iss = $api_url; // [issuer] claim identifies the principal that issued the JWT
$aud = $api_url; // [audience] claim identifies the recipients that the JWT is intended for
$iat = time(); // [issued at] claim identifies the time at which the JWT was issue
// nbf and exp are Registered Claim Names, and Claims are in payload.
$nbf = $iat - 1; // [not before] claim identifies the time before which the JWT MUST NOT be accepted for processing
$exp = $nbf + 60 * 60 * 6; // [expire] claim identifies when the token should cease to be valid (6 hours)

// ----------------- nothing to edit below this line--------------------
//

# set output to json, except from nojson explicit scripts
if (!isset($nojson)) {
    header("Access-Control-Allow-Origin: *");
    header("Content-Type: application/json; charset=UTF-8");
    header(
        "Access-Control-Allow-Methods: GET, POST, OPTIONS, HEAD, PUT, DELETE, PATCH",
    );
    header("Access-Control-Max-Age: 3600");
    header(
        "Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization",
    );
}
# CORS required headers
// https://www.moesif.com/blog/technical/cors/Authoritative-Guide-to-CORS-Cross-Origin-Resource-Sharing-for-REST-APIs/

// Load Composer's autoloader && database
require_once __DIR__ . "/../vendor/autoload.php";

# Authorization
require_once __DIR__ . "/../objects/token.php";
// temp
$_GET["testing"] = "{{ 'yes' if ansible_environment == 'staging' else 'no' }}";
// Test or Live mode?
if (
    (isset($_GET["testing"]) && $_GET["testing"] == "yes") ||
    (!empty($data->source) &&
        in_array(
            $data->source,
            ["Testing", "TESTING", "testing", "test", "Test"],
            true,
        ))
) {
    # DEBUG ON - Only in test mode
    error_reporting(E_ALL & ~E_DEPRECATED & ~E_STRICT & ~E_NOTICE);
    ini_set("display_errors", {{ 'true' if display_php_errors|default(false) else 'false' }});
    ini_set("display_startup_errors", {{ 'true' if display_php_errors|default(false) else 'false' }});

    ## Test Mode
    # Stripe-testing
    $stripe = new \Stripe\StripeClient($stripe_testing_key);
    $TESTMODE = 1;
    // test url
    $testflag = "?testing=yes";
    $product_id = $stripe_test_product_id;
} else {
    ## Live Mode
    # Stripe
    $stripe = new \Stripe\StripeClient($stripe_live_key);
    $testflag = null;
    $product_id = $stripe_live_product_id;
} 