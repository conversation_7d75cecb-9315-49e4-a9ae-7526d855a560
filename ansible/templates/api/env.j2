APP_NAME=KPFA-API
APP_ENV={{ 'staging' if ansible_environment == 'staging' else 'production' }}
APP_KEY={{ vault_api_app_key | default('') }}
APP_DEBUG={{ 'true' if ansible_environment == 'staging' else 'false' }}
APP_URL=https://{{ ansible_fqdn }}

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database connection details
# Using MariaDB (MySQL compatible)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=api_db
DB_USERNAME=api_db
DB_PASSWORD={{ vault_mariadb_api_user_password }}

# Cache and session configuration
CACHE_DRIVER=file
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Mail settings
MAIL_MAILER=smtp
MAIL_HOST={{ mail_host }}
MAIL_PORT={{ mail_port }}
MAIL_USERNAME={{ mail_username }}
MAIL_ENCRYPTION={{ mail_encryption }}
MAIL_FROM_ADDRESS={{ mail_from_address }}
MAIL_FROM_NAME="${APP_NAME}"

# New Relic monitoring (if enabled)
NEW_RELIC_ENABLED={{ 'true' if vault_new_relic_enabled | default(false) else 'false' }}
NEW_RELIC_LICENSE_KEY={{ vault_new_relic_license_key | default('') }}
NEW_RELIC_APP_NAME="KPFA API - {{ ansible_environment }}"

# API specific settings
API_THROTTLE=60
API_PAGINATION_LIMIT=50
API_DEFAULT_FORMAT=json
API_CORS_ALLOWED_ORIGINS={{ vault_api_cors_allowed_origins | default('*') }}

# Database configuration
db_host = "localhost"
db_name = "api_db"
db_user = "api_db"
db_pass = "{{ vault_mariadb_api_user_password }}"
db_type = "mysql"

# Station configuration
station_name = "KPFA"
admin_email = "{{ admin_email }}"
donor_dir_email = "{{ donor_dir_email }}"

# URLs
# api_url is now defined in the role defaults
admin_url = "https://admin.kpfa.org/"
members_url = "https://members.kpfa.org/"

# Stripe
stripe_live_key = "{{ vault_stripe_live_key | default('sk_live_your_key_here') }}"
stripe_testing_key = "{{ vault_stripe_testing_key | default('sk_test_8Nhs4cLJieHplDES5aTXByoH') }}"
stripe_live_product_id = "{{ vault_stripe_live_product_id | default('prod_Iu6NoGJQH5L35H') }}"
stripe_test_product_id = "{{ vault_stripe_test_product_id | default('prod_Iu8KSDZ1IFcY0k') }}"
endpoint_secret = "{{ vault_stripe_endpoint_secret | default('whsec_your_secret_here') }}"

# Google OAuth
client_id = "{{ vault_google_client_id | default('your-client-id.apps.googleusercontent.com') }}"
client_secret = "{{ vault_google_client_secret | default('your-client-secret') }}"
redirect_uri = "{{ api_url }}/login"

# Slack
slack_bearer_token = "{{ vault_slack_bearer_token | default('xoxb-your-token-here') }}"
slack_channel = "{{ vault_slack_channel | default('your-channel-id') }}"

# Gitea
gitea_secret_key = "{{ vault_gitea_secret_key | default('your-secret-key') }}"

# Cloudflare 
cloudflare_show_sync_token = "{{ vault_cloudflare_token | default('your-token-here') }}"

# JWT Configuration
key = "{{ vault_jwt_key | default('your-secure-key-here') }}"
iss = "{{ api_url }}"
aud = "{{ api_url }}"

# Set environment
environment = "{{ 'staging' if ansible_environment == 'staging' else 'production' }}"

# Xdebug
{{ 'xdebug_enabled = true' if ansible_environment == 'staging' else '# xdebug_enabled = false' }} 
