[mysqld]
# MariaDB Performance Optimizations

# Network Settings
bind-address = 0.0.0.0  # Allow connections from all interfaces

# Enable Event Scheduler
event_scheduler = ON

# InnoDB Settings
innodb_buffer_pool_size = 512M
innodb_log_file_size = 128M
innodb_flush_log_at_trx_commit = 1
innodb_file_per_table = 1
innodb_flush_method = O_DIRECT
innodb_log_buffer_size = 16M
innodb_doublewrite = 1
innodb_read_io_threads = 8
innodb_write_io_threads = 8
innodb_open_files = 400

# MyISAM Settings
key_buffer_size = 256M
myisam_sort_buffer_size = 64M
join_buffer_size = 4M
sort_buffer_size = 4M

# Connection Settings
max_connections = 151
thread_cache_size = 8
thread_stack = 256K
max_allowed_packet = 64M

# Query Cache Configuration
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# Temp Tables
tmp_table_size = 64M
max_heap_table_size = 64M

# Table Settings
table_open_cache = 2000
table_definition_cache = 1400
open_files_limit = 10000

# Binary Logging (disabled for non-replication setups)
disable_log_bin = 1

# Other Settings
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

[client]
default-character-set = utf8mb4 