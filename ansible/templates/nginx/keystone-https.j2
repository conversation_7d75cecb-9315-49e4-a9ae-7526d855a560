# Proxy configuration for KeystoneJS API
location / {
    proxy_pass http://127.0.0.1:{{ app_listen_port }};
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # Standard timeouts should be sufficient for API operations
    proxy_read_timeout 60;
    proxy_connect_timeout 60;
    proxy_send_timeout 60;
}

# Handle static files and uploads if they exist
location /files {
    alias {{ keystone_app_path }}/public;
    expires 30d;
    add_header Cache-Control "public, no-transform";
}

# Security headers
add_header X-Frame-Options "DENY" always;  # Stricter since we don't need iframe support
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
# Stricter CSP since we only need API functionality
add_header Content-Security-Policy "default-src 'none'; connect-src 'self';" always; 