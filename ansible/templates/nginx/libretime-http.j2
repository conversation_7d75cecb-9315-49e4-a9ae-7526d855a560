# LibreTime HTTP configuration template

# Allow Let's Encrypt challenge requests
location ^~ /.well-known/acme-challenge/ {
  allow all;
  default_type "text/plain";
  root /var/www/html;
  
  # Disable caching
  add_header Cache-Control "no-cache, no-store, must-revalidate";
  add_header Pragma "no-cache";
  add_header Expires "0";
}

# Redirect all other traffic to HTTPS
location / {
  return 301 https://$host$request_uri;
} 