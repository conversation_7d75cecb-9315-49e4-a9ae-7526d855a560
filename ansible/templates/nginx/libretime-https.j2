# LibreTime HTTPS configuration template

# Log configuration
access_log /var/log/nginx/libretime.access.log;
error_log /var/log/nginx/libretime.error.log;

# Request size configuration
client_max_body_size {{ nginx_client_max_body_size | default('64M') }};
client_body_timeout 300s;

# Proxy configuration for LibreTime
location / {
    proxy_set_header Host              $host;
    proxy_set_header X-Real-IP         $remote_addr;
    proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host  $host;
    proxy_set_header X-Forwarded-Port  $server_port;

    proxy_pass http://localhost:{{ libretime_listen_port }}/;
}

# API endpoints
location ~ ^/(api/v2|api-auth) {
    include proxy_params;
    proxy_redirect off;
    proxy_pass http://unix:/run/libretime-api.sock;
}

# Internal path for serving media files from the API
location /api/_media {
    internal;
    # This alias path must match the 'storage.path' configuration field
    alias {{ libretime_storage_dir | default('/var/lib/libretime') }};
}

# Security: prevent access to .git directories
location ~ /\.git {
    deny all;
} 
