# Streams HTTPS configuration template

# Log configuration
access_log /var/log/nginx/streams.access.log;
error_log /var/log/nginx/streams.error.log;

# Request size configuration
client_max_body_size {{ nginx_client_max_body_size | default('512M') }};
client_body_timeout 300s;

# Security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

# Include common proxy parameters
include proxy_params;

# Proxy configuration for Icecast
location / {
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_pass http://localhost:{{ app_listen_port }};
}

# Status page
location /status.xsl {
    proxy_pass http://localhost:{{ app_listen_port }}/status.xsl;
}

# Stream endpoints
location ~ ^/(stream|live) {
    proxy_pass http://localhost:{{ app_listen_port }};
    proxy_buffering off;
    proxy_cache off;
    proxy_read_timeout 0;
    proxy_send_timeout 0;
}

# Security: prevent access to .git directories
location ~ /\.git {
    deny all;
} 