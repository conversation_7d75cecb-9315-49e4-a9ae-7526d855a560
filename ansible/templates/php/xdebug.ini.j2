; Xdebug configuration for PHP {{ php_version }}
zend_extension=xdebug.so

; Enable Xdebug
xdebug.mode=debug,develop
xdebug.start_with_request=trigger
xdebug.client_host=127.0.0.1
xdebug.client_port=9003
xdebug.idekey=PHPSTORM

; Configure logs
xdebug.log=/var/log/php/xdebug.log
xdebug.log_level=7

; Development settings
xdebug.discover_client_host=1
xdebug.max_nesting_level=256
xdebug.var_display_max_depth=10

; Output formatting
xdebug.cli_color=1
xdebug.force_display_errors=1
xdebug.show_exception_trace=0
xdebug.var_display_max_children=256 