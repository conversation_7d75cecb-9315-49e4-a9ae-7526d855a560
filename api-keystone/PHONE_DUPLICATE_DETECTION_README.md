# Phone Duplicate Detection System

## Overview

The phone duplicate detection system automatically analyzes CSV exports of donor data to identify potential duplicates based on exact phone number matches after normalization. It auto-creates `DuplicateDonor` records and `DuplicateGroup` suggestions that staff can review and approve through the Keystone admin interface.

## Key Features

✅ **Auto-creates DuplicateDonor records** - No need to pre-import legacy data  
✅ **Intelligent suggestion types** - NEW_GROUP, ADD_TO_GROUP  
✅ **Date-ordered primary donor selection** - Most recently created donor becomes primary  
✅ **Dry-run mode** - Test without making database changes  
✅ **Phone normalization** - Handles various phone formats consistently  
✅ **Exact matching only** - Simple and reliable duplicate detection  
✅ **Detailed metadata** - Stores matching criteria and detection details  
✅ **Automatic fallback** - Uses test data if production CSV not found  

## Data Export & Primary Donor Selection

### Export Donors from MySQL
```bash
# Export donors with phone numbers, ordered by most recent donation date (newest first)
# If no donations, fallback to date_created DESC for ordering
sudo mysql -u root -p api_db -e "
SELECT 'id','phone','date_created','last_donation_date' 
UNION ALL 
SELECT 
    d.id, 
    d.phone, 
    d.date_created,
    COALESCE(MAX(don.Timestamp), d.date_created) as last_donation_date
FROM donors d 
LEFT JOIN donations don ON d.id = don.donor_id 
WHERE d.phone IS NOT NULL AND d.phone != '' 
GROUP BY d.id, d.phone, d.date_created 
ORDER BY last_donation_date DESC, d.date_created DESC;
" --batch --raw > donors_with_phone_by_donation_date.csv
```

### Primary Donor Logic
- **Donors are exported ordered by most recent donation date (newest first)**
- **When duplicate phone numbers are found, the donor with the most recent donation becomes the primary donor**
- **If no donations exist for any duplicate, the most recently created donor account becomes primary**
- **This ensures the most actively donating account is preserved as the primary record**

This approach is optimal because:
- Preserves the account that has the most recent donor relationship
- Database sorting is faster than application sorting
- The intent is explicit in the SQL command
- Script logic remains simple and processes donors in order
- More recent donors are typically more complete/accurate
- Maintains donor engagement history with the most active account

## Enhanced Primary Donor Selection (Latest Update)

### Donation-Based Priority System

The duplicate detection system now uses a sophisticated priority system for selecting the primary donor:

1. **🎯 Most Recent Donation First** - Donor with the newest donation date becomes primary
2. **📅 Creation Date Fallback** - If no donations exist, newest account creation becomes primary  
3. **📊 Data Preservation** - Maintains connection to the most active donor relationship

### Example Selection Logic

Consider these duplicate donors with phone `(*************`:

```
Donor A: Created 2020-01-15, Last Donation: 2024-11-15  ← PRIMARY (most recent donation)
Donor B: Created 2023-06-20, Last Donation: 2023-08-10
Donor C: Created 2021-03-10, Last Donation: 2021-03-10  
```

**Result**: Donor A becomes primary because of the 2024-11-15 donation, even though Donor B has a newer account creation date.

### Test Data

The included `test_donors_donation_sample.csv` contains realistic test scenarios:

```csv
id,phone,date_created,last_donation_date
12345,(*************,2020-01-15,2024-11-15  # Should be primary - recent donation
54321,************,2023-06-20,2023-08-10    # Duplicate - older donation  
78901,**********,2021-03-10,2021-03-10      # Duplicate - oldest donation
```

### System Output

When processing duplicates, the system now logs detailed primary donor selection:

```
📦 Processing group 1/2: 3 records with phone **********
   📋 Suggestion type: NEW_GROUP
   📊 Existing donors: 0, New donors to create: 3
   ✅ Created NEW_GROUP suggestion: Phone Match: 3 donors with (*************
   ✅ Created 3 new DuplicateDonor records
   🎯 Primary donor: ID 12345 (last donation: 2024-11-15)
```

### Benefits

- **Preserves Active Relationships** - Keeps the account with recent donation activity
- **Maintains Donor Engagement** - Primary account reflects current giving patterns  
- **Supports Retention** - Staff can contact the donor using their most active account
- **Flexible Fallback** - Gracefully handles accounts with no donation history
- **Audit Trail** - Clear logging shows why each donor was selected as primary

## Usage

### Production Phone Analysis

```bash
# Analyze all records from CSV
npm run analyze-phone-duplicates

# Analyze limited records (for testing)
npm run analyze-phone-duplicates -- --limit=1000

# Dry run (no database changes)
npm run analyze-phone-duplicates -- --dry-run --limit=100
```

**Requirements:**
- Place your CSV file as: `api-keystone/scripts/donors_with_phone_by_donation_date.csv`
- CSV format: `id,phone,date_created,last_donation_date` (with headers)
- Supports various phone formats: `(*************`, `************`, `**********`, etc.
- If no production CSV found, automatically uses `test_donors_donation_sample.csv` for testing

## How It Works

### Phone Normalization
- Removes all non-digit characters except country codes
- Standardizes to 10-digit US format: `**********`
- Handles formats: `(*************`, `************`, `******-555-0103`, etc.

### Exact Matching
- Only creates suggestions for **exact matches** after normalization
- No fuzzy matching or confidence scoring - human review handles edge cases
- Simple and reliable approach

### Suggestion Types

**NEW_GROUP** - Creates a new duplicate group
- Used when no existing groups contain any of the duplicate donors
- Used when multiple existing groups would conflict (staff review handles complex merges)
- Safe default that allows staff to make merge decisions manually

**ADD_TO_GROUP** - Adds donors to an existing group  
- Used when exactly one existing group contains some of the duplicate donors
- Automatically connects new donors to the existing group

## Database Schema

### DuplicateDonor
- `kpfaId` - Legacy donor ID from CSV
- `phone` - Original phone number
- `dataSource` - Set to 'PHONE_ANALYSIS' for auto-created records
- `groupId` - Links to DuplicateGroup when assigned

### DuplicateGroup
- `status` - 'SUGGESTED', 'PENDING', 'CONFIRMED', 'REJECTED'
- `suggestionType` - 'NEW_GROUP', 'ADD_TO_GROUP'
- `matchingCriteria` - JSON with detection details
- `autoCreated` - true for script-generated suggestions

## Staff Workflow

### 1. Review Suggestions in Keystone Admin
- Filter DuplicateGroups by `status = "SUGGESTED"`
- Review matching criteria and original phone formats
- See normalized versions and suggestion reasoning

### 2. Accept/Reject Suggestions
- **Accept** - Change status to "PENDING" for further review
- **Reject** - Change status to "REJECTED" 
- **Modify** - Edit group membership before accepting

### 3. Final Confirmation
- Review "PENDING" groups for final approval
- Change status to "CONFIRMED" when ready to merge
- System tracks who reviewed and when

## Next.js Admin Integration

The system uses **loose coupling** where:
- **Keystone** stores minimal data (kpfaId, grouping, suggestions)
- **Next.js admin** aggregates full donor details from legacy API
- Staff can see rich donor information while approving duplicates
- Changes flow back to Keystone for persistence

## Example Output

```
🎉 Phone duplicate analysis completed!
📈 Final stats:
   - Total records analyzed: 1,000
   - Records with valid phones: 999
   - Unique phone numbers: 995
   - Duplicate groups found: 4
   - NEW_GROUP suggestions: 3
   - ADD_TO_GROUP suggestions: 1
   - DuplicateDonor records created: 7
   - Total suggestions created: 4

🎯 Next steps:
   1. Visit the Keystone admin to review 4 new suggestions
   2. Filter DuplicateGroups by status = "SUGGESTED"
   3. Staff can accept/reject suggestions using admin interface
```

## Test Data

The included `test_donors_donation_sample.csv` contains:
- **105 total records** with various phone formats
- **6 duplicate groups** with 2-3 records each
- **14 total duplicate records** across all groups
- Examples of different phone formatting styles

The script automatically uses test data if no production CSV is found!

## Architecture Benefits

1. **No Legacy Import Required** - Works directly with CSV exports
2. **Thin Storage Layer** - Keystone stores minimal duplicate metadata
3. **Rich Admin Interface** - Next.js provides full donor context
4. **Flexible Workflow** - Staff can review, modify, accept, or reject
5. **Audit Trail** - Complete history of who reviewed what and when
6. **Scalable** - Handles large datasets with batching and progress tracking
7. **Simple & Reliable** - Exact matching only, human review for edge cases 