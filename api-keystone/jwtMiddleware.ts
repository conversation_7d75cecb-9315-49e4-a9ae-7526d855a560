import { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";

// TypeScript declaration for jsonwebtoken module
declare module 'jsonwebtoken';

export const jwtMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  // Log all requests with their paths and auth status
  console.log(`JWT middleware: ${req.path}, Auth header: ${req.headers.authorization ? 'present' : 'missing'}`);
  
  // Bypass full JWT verification for auth paths
  if (req.path === "/signin") {
    return next();
  }
  
  // Special handling for GraphQL - validate token but don't block
  if (req.path === "/api/graphql") {
    const token = req.headers.authorization?.split(" ")[1];
    
    if (!token) {
      console.log('GraphQL request has no token');
      // Don't block GraphQL requests without tokens, let the resolvers handle auth
      return next();
    }
    
    try {
      const decoded = jwt.decode(token, { complete: true });
      
      if (!decoded) {
        console.log('GraphQL request has invalid token format');
      } else {
        const payload = decoded.payload as any;
        console.log(`GraphQL request access level: ${payload.data?.access_level || 'none'}`);
      }
      
      // Always allow GraphQL requests through, let resolvers handle auth
      return next();
    } catch (error: any) {
      console.error('Error decoding GraphQL JWT:', error);
      return next();
    }
  }

  // For non-GraphQL paths, enforce token validation
  const token = req.headers.authorization?.split(" ")[1];

  if (!token) {
    return res.status(401).json({ message: "No token provided" });
  }

  try {
    const decoded = jwt.decode(token, {
      complete: true,
    });
    /* todo: fix signature verification. decode is ok for now, until we add more lists to this db
    const decode = jwt.verify(
      token,
      Buffer.from(jwtSecret, "base64"), { algorithms: ['HS512'] })
    );
*/
    if (!decoded) {
      return res.status(401).json({ message: "Invalid token" });
    }

    const payload = decoded.payload as any;
    const accessLevel = payload.data?.access_level;

    if (accessLevel !== "Admin") {
      return res
        .status(403)
        .json({ message: "Forbidden: Admin access required" });
    }

    next();
  } catch (error: any) {
    return res.status(401).json({ message: error.message });
  }
};
