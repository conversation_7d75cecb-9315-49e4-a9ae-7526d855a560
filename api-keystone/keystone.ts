import { config } from "@keystone-6/core";
import { lists, extendGraphqlSchema } from "./schema";
import { withAuth, session } from "./auth";
import dotenv from "dotenv";
import { jwtMiddleware } from "./jwtMiddleware";
import { startScheduler, stopScheduler } from "./lib/scheduler/scheduler";
import { Request, Response, NextFunction } from "express";

dotenv.config();

function getEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    throw new Error(`Environment variable ${name} is not set`);
  }
  return value;
}

// Determine if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';
const isStaging = process.env.IS_STAGING === 'true';
const enableStagingFeatures = process.env.ENABLE_STAGING_FEATURES === 'true';

// CORS options are now driven by environment variables set via Ansible
const corsOriginEnv = process.env.CORS_ORIGIN;
let allowedOrigins: string[] | boolean = false; // Default to disabling CORS

if (corsOriginEnv) {
  allowedOrigins = corsOriginEnv.split(',').map(origin => origin.trim());
} else if (isDevelopment) {
  // Fallback for local development if CORS_ORIGIN is not set
  allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3001',
    'http://127.0.0.1:3002',
    'http://127.0.0.1:3003',
    'https://pet-leopard-fully.ngrok-free.app',
  ];
}

const corsOptions = {
  origin: allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  maxAge: 86400, // 24 hours
};

console.log(`CORS configured for the following origins:`, allowedOrigins);

const useJwtMiddleware = process.env.USE_JWT_MIDDLEWARE === "true";

// Flag to control whether automated scheduler should run
const enableAutomatedScheduler = process.env.ENABLE_AUTOMATED_SCHEDULER !== "false";

// Auth middleware that saves the token even for GraphQL requests
const authHeaderMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (req.headers.authorization) {
    console.log('GraphQL request received with auth header');
    // Store authorization info for GraphQL resolvers
    req.app.locals.auth = req.headers.authorization;
  }
  next();
};

export default withAuth(
  config({
    db: {
      provider: "postgresql",
      url: getEnvVar("DATABASE_URL"),
      onConnect: async () => {
        console.log("📅 Database connected successfully");
        
        // Start the automated scheduler after DB connection
        if (enableAutomatedScheduler) {
          console.log("🚀 Starting automated data retention scheduler...");
          startScheduler({
            donorExpirationCron: process.env.DONOR_EXPIRATION_CRON || '0 2 1 * *', // Monthly
            enableLogging: true,
            maxRetries: 3
          });
        } else {
          console.log("⏸️  Automated scheduler disabled via ENABLE_AUTOMATED_SCHEDULER=false");
        }
      },
      enableLogging: true,
      idField: { kind: "cuid" },
    },
    server: {
      cors: corsOptions,
      extendExpressApp: (app, createContext) => {
        // Log server start for debugging
        console.log("Keystone server starting...");
        
        // Apply auth header middleware to all requests
        app.use(authHeaderMiddleware);
        
        // Apply JWT middleware if configured (skips GraphQL)
        if (useJwtMiddleware) {
          app.use(jwtMiddleware);
        }

        // Add route to check scheduler status
        app.get('/api/scheduler-status', async (req, res) => {
          try {
            const { getScheduler } = await import('./src/scheduler');
            const scheduler = getScheduler();
            const status = scheduler.getStatus();
            
            res.json({
              enabled: enableAutomatedScheduler,
              ...status,
              message: enableAutomatedScheduler 
                ? 'Automated data retention scheduler is running' 
                : 'Automated scheduler is disabled'
            });
          } catch (error) {
            res.status(500).json({ 
              error: 'Failed to get scheduler status',
              details: String(error)
            });
          }
        });

        // Add route to manually trigger expiration (admin only)
        app.post('/api/expire-donor-data', async (req, res) => {
          try {
            // TODO: Add authentication check here
            const { getScheduler } = await import('./lib/scheduler/scheduler');
            const scheduler = getScheduler();
            
            console.log('📋 Manual expiration triggered via API');
            await scheduler.runManualExpiration();
            
            res.json({ 
              success: true, 
              message: 'Donor data expiration completed successfully',
              triggeredAt: new Date().toISOString()
            });
          } catch (error) {
            console.error('❌ Manual expiration failed:', error);
            res.status(500).json({ 
              success: false,
              error: 'Failed to run donor data expiration',
              details: String(error)
            });
          }
        });
      },
    },
    lists,
    session,
    graphql: {
      debug: process.env.NODE_ENV !== 'production',
      playground: process.env.NODE_ENV !== 'production',
      extendGraphqlSchema,
    },
  }),
);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  stopScheduler();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  stopScheduler();
  process.exit(0);
});
