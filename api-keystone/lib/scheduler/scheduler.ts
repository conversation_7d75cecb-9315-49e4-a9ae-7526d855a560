import * as cron from 'node-cron';
import { getContext } from '@keystone-6/core/context';
import config from '../../keystone';
import * as PrismaModule from '.prisma/client';
import { expireDonorData } from '../../scripts/expireDonorData';

/**
 * Automated Data Retention Scheduler for KPFA
 * 
 * Handles automated execution of data retention policies including:
 * - 18-month donor data expiration
 * - Cleanup logging and monitoring
 * - Error handling and notifications
 */

interface SchedulerConfig {
  // Run monthly on the 1st at 2 AM
  donorExpirationCron: string;
  enableLogging: boolean;
  maxRetries: number;
}

const defaultConfig: SchedulerConfig = {
  donorExpirationCron: '0 2 1 * *', // 2 AM on the 1st of every month
  enableLogging: true,
  maxRetries: 3
};

class DataRetentionScheduler {
  private config: SchedulerConfig;
  private tasks: cron.ScheduledTask[] = [];
  private lastRun: Record<string, Date> = {};

  constructor(config: SchedulerConfig = defaultConfig) {
    this.config = { ...defaultConfig, ...config };
  }

  /**
   * Initialize all scheduled tasks
   */
  start(): void {
    console.log('🕐 Starting Data Retention Scheduler');
    console.log(`📅 Donor expiration schedule: ${this.config.donorExpirationCron}`);

    // Schedule donor data expiration
    const donorExpirationTask = cron.schedule(
      this.config.donorExpirationCron,
      () => this.runDonorExpiration(),
      {
        scheduled: true,
        timezone: 'America/Los_Angeles' // PST/PDT for KPFA
      }
    );

    this.tasks.push(donorExpirationTask);

    // Schedule weekly health check (Sundays at 6 AM)
    const healthCheckTask = cron.schedule(
      '0 6 * * 0',
      () => this.runHealthCheck(),
      {
        scheduled: true,
        timezone: 'America/Los_Angeles'
      }
    );

    this.tasks.push(healthCheckTask);

    console.log('✅ Data Retention Scheduler started successfully');
  }

  /**
   * Stop all scheduled tasks
   */
  stop(): void {
    console.log('🛑 Stopping Data Retention Scheduler');
    this.tasks.forEach(task => {
      task.stop();
    });
    this.tasks = [];
    console.log('✅ Data Retention Scheduler stopped');
  }

  /**
   * Execute donor data expiration with retry logic
   */
  private async runDonorExpiration(): Promise<void> {
    const taskName = 'donor-expiration';
    let attempt = 0;
    
    console.log('\n🔄 Automated Donor Data Expiration Starting');
    console.log(`⏰ Started at: ${new Date().toISOString()}`);

    while (attempt < this.config.maxRetries) {
      try {
        attempt++;
        
        if (attempt > 1) {
          console.log(`🔄 Retry attempt ${attempt}/${this.config.maxRetries}`);
        }

        const summary = await expireDonorData();
        
        // Log results
        console.log(`✅ Donor expiration completed successfully`);
        console.log(`📊 Groups processed: ${summary.groupsProcessed}`);
        console.log(`🗑️  Donors deleted: ${summary.totalDeleted}`);
        
        if (summary.errors.length > 0) {
          console.warn(`⚠️  Warnings: ${summary.errors.length} errors encountered`);
          summary.errors.forEach((error, index) => {
            console.warn(`   ${index + 1}. ${error}`);
          });
        }

        // Store last successful run
        this.lastRun[taskName] = new Date();
        
        // Log to database for audit trail
        await this.logScheduledRun(taskName, 'SUCCESS', summary);
        
        return; // Success, exit retry loop

      } catch (error) {
        console.error(`❌ Donor expiration failed (attempt ${attempt}):`, error);
        
        if (attempt >= this.config.maxRetries) {
          console.error(`🚨 All ${this.config.maxRetries} attempts failed`);
          
          // Log failure to database
          await this.logScheduledRun(taskName, 'FAILED', { error: String(error) });
          
          // TODO: Send notification to administrators
          this.notifyFailure(taskName, error);
          
          return;
        }
        
        // Wait before retry (exponential backoff)
        const waitTime = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s...
        console.log(`⏳ Waiting ${waitTime}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  /**
   * Run health check to monitor scheduler status
   */
  private async runHealthCheck(): Promise<void> {
    console.log('\n🏥 Running Data Retention Health Check');
    
    try {
      const context = getContext(config, PrismaModule);
      
      // Check for overdue expired groups (older than 19 months)
      const overdueDate = new Date(Date.now() - (19 * 30 * 24 * 60 * 60 * 1000));
      
      const overdueGroups = await context.query.DuplicateGroup.findMany({
        where: {
          status: { equals: 'MERGED' },
          mergedAt: { lt: overdueDate }
        },
        query: 'id name mergedAt'
      });

      if (overdueGroups.length > 0) {
        console.warn(`⚠️  Found ${overdueGroups.length} overdue groups (>19 months old)`);
        console.warn('   This may indicate the expiration process is not running correctly');
        
        // Log warning
        await this.logScheduledRun('health-check', 'WARNING', {
          overdueGroups: overdueGroups.length,
          oldestGroup: overdueGroups[0]?.mergedAt
        });
      } else {
        console.log('✅ No overdue groups found - expiration running correctly');
        await this.logScheduledRun('health-check', 'SUCCESS', { overdueGroups: 0 });
      }

      // Check last run times
      const lastRun = this.lastRun['donor-expiration'];
      if (lastRun) {
        const daysSinceLastRun = (Date.now() - lastRun.getTime()) / (1000 * 60 * 60 * 24);
        console.log(`📅 Last donor expiration run: ${daysSinceLastRun.toFixed(1)} days ago`);
      } else {
        console.warn('⚠️  No record of previous donor expiration runs');
      }

    } catch (error) {
      console.error('❌ Health check failed:', error);
      await this.logScheduledRun('health-check', 'FAILED', { error: String(error) });
    }
  }

  /**
   * Log scheduled run results to database for audit
   */
  private async logScheduledRun(taskName: string, status: string, data: any): Promise<void> {
    try {
      const context = getContext(config, PrismaModule);
      
      // You might want to create a ScheduledTaskLog model for this
      // For now, we'll log to console and potentially a file
      const logEntry = {
        timestamp: new Date().toISOString(),
        taskName,
        status,
        data: JSON.stringify(data, null, 2)
      };
      
      if (this.config.enableLogging) {
        console.log(`📝 Log Entry:`, logEntry);
        
        // TODO: Store in database table if desired
        // await context.query.ScheduledTaskLog.createOne({ data: logEntry });
      }
      
    } catch (error) {
      console.error('Failed to log scheduled run:', error);
    }
  }

  /**
   * Notify administrators of failures
   */
  private notifyFailure(taskName: string, error: any): void {
    // TODO: Implement notification system
    // Could send email, Slack message, etc.
    console.error(`🚨 NOTIFICATION: ${taskName} failed with error:`, error);
    
    // For now, just log prominently
    console.error('=' .repeat(80));
    console.error('🚨 CRITICAL: Automated data retention task failed');
    console.error(`Task: ${taskName}`);
    console.error(`Time: ${new Date().toISOString()}`);
    console.error(`Error: ${error}`);
    console.error('Action required: Check logs and run manually if needed');
    console.error('=' .repeat(80));
  }

  /**
   * Get status of scheduler
   */
  getStatus(): any {
    return {
      isRunning: this.tasks.length > 0,
      tasksCount: this.tasks.length,
      lastRuns: this.lastRun,
      config: this.config
    };
  }

  /**
   * Manually trigger donor expiration (for testing/emergency)
   */
  async runManualExpiration(): Promise<void> {
    console.log('🔧 Manual trigger: Running donor expiration...');
    await this.runDonorExpiration();
  }
}

// Export singleton instance
let schedulerInstance: DataRetentionScheduler | null = null;

export function getScheduler(config?: SchedulerConfig): DataRetentionScheduler {
  if (!schedulerInstance) {
    schedulerInstance = new DataRetentionScheduler(config);
  }
  return schedulerInstance;
}

export function startScheduler(config?: SchedulerConfig): void {
  const scheduler = getScheduler(config);
  scheduler.start();
}

export function stopScheduler(): void {
  if (schedulerInstance) {
    schedulerInstance.stop();
    schedulerInstance = null;
  }
}

export { DataRetentionScheduler }; 