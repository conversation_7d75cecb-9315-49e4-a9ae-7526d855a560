# Stripe Integration for KPFA Keystone

This module provides read-only integration with Stripe for building custom reports and linking payment data with KPFA's donor management system.

## 🎯 Purpose

- **Read-only access** to Stripe data for reporting
- **Link Stripe customers** with existing Keystone donor records
- **Custom reports** combining donor and payment data
- **GraphQL queries** following Keystone's native patterns

## 🚀 Setup

### 1. Environment Variables

Add these to your `.env` file:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
# For production, use: sk_live_... and pk_live_...
```

### 2. Test the Integration

```bash
yarn test-stripe
```

This will verify your Stripe connection and basic functionality.

### 3. Start Keystone

```bash
yarn dev
```

The Stripe GraphQL queries will be available in your admin UI.

## 📊 Available GraphQL Queries

### Health Check
```graphql
query {
  stripeHealthCheck {
    status
    message
    timestamp
  }
}
```

### Find Customer by ID
```graphql
query {
  stripeCustomer(customerId: "cus_example123") {
    id
    email
    name
    phone
    created
    currency
    balance
  }
}
```

### Search Customers by Email
```graphql
query {
  stripeCustomerByEmail(email: "<EMAIL>") {
    id
    email
    name
    created
  }
}
```

### Comprehensive Customer Report
```graphql
query {
  stripeCustomerReport(customerId: "cus_example123") {
    customer {
      id
      email
      name
    }
    paymentMethods {
      id
      type
      card {
        brand
        last4
        expMonth
        expYear
      }
    }
    subscriptions {
      id
      status
      amount
      currency
      interval
    }
    charges {
      id
      amount
      currency
      status
      created
      description
    }
    totalSpent
    lastPaymentDate
  }
}
```

### Recent Transactions
```graphql
query {
  stripeRecentTransactions(input: {
    customerId: "cus_example123"
    limit: 10
  }) {
    id
    amount
    currency
    status
    created
    description
    receiptUrl
  }
}
```

## 🔗 Linking Donors with Stripe

The integration provides utilities to link Keystone donors with Stripe customers:

### Automatic Linking
- **By Stripe ID**: If `stripe_cus_id` field is populated (highest confidence)
- **By Email**: Matching email addresses (medium confidence)

### Manual Linking
Use the `donorStripeLink.ts` utilities in your custom scripts:

```typescript
import { findStripeCustomerForDonor, getDonorStripeReport } from './lib/stripe/donorStripeLink';

// Find Stripe customer for a donor
const match = await findStripeCustomerForDonor(donorId, context);

// Get comprehensive report
const report = await getDonorStripeReport(donorId, context);
```

## 🏗️ Architecture

### Files Structure
```
lib/stripe/
├── stripeService.ts      # Core Stripe API wrapper
├── stripeSchema.ts       # GraphQL schema extensions
├── donorStripeLink.ts    # Donor-Stripe linking utilities
├── types.ts              # TypeScript type definitions
└── README.md             # This file
```

### Key Components

1. **StripeService**: Singleton service for Stripe API calls
2. **GraphQL Extensions**: Native Keystone schema extensions
3. **Linking Utilities**: Functions to connect donors with Stripe customers
4. **Type Safety**: Full TypeScript support

## 🛡️ Security & Best Practices

### Read-Only Design
- No write operations to Stripe
- Only retrieval and reporting functions
- Safe for production use

### Error Handling
- Graceful degradation when Stripe is unavailable
- Detailed logging for debugging
- No sensitive data exposure

### Rate Limiting
- Respects Stripe's API rate limits
- Implements reasonable query limits
- Caches where appropriate

## 📈 Custom Reports

### Example: Monthly Donation Report
```typescript
// Get recent transactions for reporting
const transactions = await stripeService.getRecentTransactions(30, 100);

// Process and aggregate data
const monthlyTotal = transactions
  .filter(charge => charge.status === 'succeeded')
  .reduce((sum, charge) => sum + charge.amount, 0) / 100;
```

### Example: Donor Payment History
```typescript
// Get comprehensive donor report
const report = await getDonorStripeReport(donorId, context);

console.log(`Total Stripe spending: $${report.summary.totalStripeSpent}`);
console.log(`Active subscriptions: ${report.summary.activeSubscriptions}`);
```

## 🔧 Troubleshooting

### Common Issues

1. **"No such API key"**
   - Check your `STRIPE_SECRET_KEY` in `.env`
   - Ensure you're using the correct test/live key

2. **GraphQL queries not available**
   - Restart your Keystone server after adding the integration
   - Check the console for schema compilation errors

3. **Customer not found**
   - Verify the customer ID exists in your Stripe account
   - Check if you're using test vs live data

### Debug Mode
Set `NODE_ENV=development` to enable detailed logging.

## 🚀 Next Steps

1. **Test with real data**: Update `.env` with your actual Stripe keys
2. **Build custom reports**: Use the GraphQL queries in your frontend
3. **Link existing donors**: Run bulk linking utilities
4. **Monitor usage**: Set up logging and monitoring for production

## 📚 Resources

- [Stripe API Documentation](https://stripe.com/docs/api)
- [Keystone GraphQL Extensions](https://keystonejs.com/docs/guides/schema-extension)
- [KPFA Donor Management System](../README.md)
