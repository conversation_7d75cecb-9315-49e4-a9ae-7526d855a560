import { getStripeService } from './stripeService';
import type { Context } from '@keystone-6/core/types';

/**
 * Utility functions to link Keystone donors with Stripe customers
 * 
 * This module provides functions to find connections between your donor
 * database and Stripe customer records for comprehensive reporting.
 */

export interface DonorStripeMatch {
  donor: any; // Keystone donor record
  stripeCustomer: any; // Stripe customer record
  matchType: 'stripe_id' | 'email' | 'manual';
  confidence: 'high' | 'medium' | 'low';
}

/**
 * Find Stripe customer for a Keystone donor
 */
export async function findStripeCustomerForDonor(
  donorId: string,
  context: Context
): Promise<DonorStripeMatch | null> {
  const stripeService = getStripeService();
  
  // Get the donor from Keystone
  const donor = await context.query.DuplicateDonor.findOne({
    where: { id: donorId },
    query: 'id kpfaId firstname lastname email stripe_cus_id',
  });
  
  if (!donor) {
    return null;
  }
  
  // First, try direct Stripe customer ID match (highest confidence)
  if (donor.stripe_cus_id) {
    const stripeCustomer = await stripeService.getCustomer(donor.stripe_cus_id);
    if (stripeCustomer) {
      return {
        donor,
        stripeCustomer,
        matchType: 'stripe_id',
        confidence: 'high',
      };
    }
  }
  
  // Second, try email match (medium confidence)
  if (donor.email) {
    const stripeCustomers = await stripeService.searchCustomersByEmail(donor.email);
    if (stripeCustomers.length > 0) {
      // If multiple customers with same email, take the most recent
      const mostRecentCustomer = stripeCustomers.sort((a, b) => b.created - a.created)[0];
      return {
        donor,
        stripeCustomer: mostRecentCustomer,
        matchType: 'email',
        confidence: stripeCustomers.length === 1 ? 'medium' : 'low',
      };
    }
  }
  
  return null;
}

/**
 * Find Keystone donor for a Stripe customer
 */
export async function findDonorForStripeCustomer(
  stripeCustomerId: string,
  context: Context
): Promise<DonorStripeMatch | null> {
  const stripeService = getStripeService();
  
  // Get the Stripe customer
  const stripeCustomer = await stripeService.getCustomer(stripeCustomerId);
  if (!stripeCustomer) {
    return null;
  }
  
  // First, try direct Stripe customer ID match
  const donorByStripeId = await context.query.DuplicateDonor.findMany({
    where: { stripe_cus_id: { equals: stripeCustomerId } },
    query: 'id kpfaId firstname lastname email stripe_cus_id',
    take: 1,
  });
  
  if (donorByStripeId.length > 0) {
    return {
      donor: donorByStripeId[0],
      stripeCustomer,
      matchType: 'stripe_id',
      confidence: 'high',
    };
  }
  
  // Second, try email match
  if (stripeCustomer.email) {
    const donorsByEmail = await context.query.DuplicateDonor.findMany({
      where: { email: { equals: stripeCustomer.email } },
      query: 'id kpfaId firstname lastname email stripe_cus_id',
      take: 5, // Limit to avoid too many results
    });
    
    if (donorsByEmail.length > 0) {
      // Prefer donors that don't already have a Stripe ID
      const donorWithoutStripeId = donorsByEmail.find(d => !d.stripe_cus_id);
      const selectedDonor = donorWithoutStripeId || donorsByEmail[0];
      
      return {
        donor: selectedDonor,
        stripeCustomer,
        matchType: 'email',
        confidence: donorsByEmail.length === 1 ? 'medium' : 'low',
      };
    }
  }
  
  return null;
}

/**
 * Get comprehensive donor-Stripe report
 */
export async function getDonorStripeReport(
  donorId: string,
  context: Context
): Promise<{
  donor: any;
  stripeMatch: DonorStripeMatch | null;
  stripeReport: any | null;
  summary: {
    hasStripeAccount: boolean;
    totalStripeSpent: number;
    lastStripePayment: Date | null;
    activeSubscriptions: number;
  };
}> {
  const stripeService = getStripeService();
  
  // Get the donor
  const donor = await context.query.DuplicateDonor.findOne({
    where: { id: donorId },
    query: 'id kpfaId firstname lastname email stripe_cus_id phone address1 city state',
  });
  
  if (!donor) {
    throw new Error(`Donor with ID ${donorId} not found`);
  }
  
  // Find Stripe match
  const stripeMatch = await findStripeCustomerForDonor(donorId, context);
  
  let stripeReport = null;
  let summary = {
    hasStripeAccount: false,
    totalStripeSpent: 0,
    lastStripePayment: null as Date | null,
    activeSubscriptions: 0,
  };
  
  if (stripeMatch) {
    // Get comprehensive Stripe report
    stripeReport = await stripeService.getCustomerReport(stripeMatch.stripeCustomer.id);
    
    summary = {
      hasStripeAccount: true,
      totalStripeSpent: stripeReport.totalSpent,
      lastStripePayment: stripeReport.lastPayment ? new Date(stripeReport.lastPayment.created * 1000) : null,
      activeSubscriptions: stripeReport.subscriptions.filter(sub => 
        ['active', 'trialing'].includes(sub.status)
      ).length,
    };
  }
  
  return {
    donor,
    stripeMatch,
    stripeReport,
    summary,
  };
}

/**
 * Bulk link donors with Stripe customers
 * Useful for initial setup or periodic reconciliation
 */
export async function bulkLinkDonorsWithStripe(
  context: Context,
  limit: number = 100
): Promise<{
  processed: number;
  matched: number;
  updated: number;
  errors: string[];
}> {
  const results = {
    processed: 0,
    matched: 0,
    updated: 0,
    errors: [] as string[],
  };
  
  try {
    // Get donors without Stripe IDs but with emails
    const donors = await context.query.DuplicateDonor.findMany({
      where: {
        AND: [
          { email: { not: { equals: null } } },
          { email: { not: { equals: '' } } },
          { stripe_cus_id: { equals: null } },
        ],
      },
      query: 'id kpfaId email firstname lastname',
      take: limit,
    });
    
    for (const donor of donors) {
      results.processed++;
      
      try {
        const stripeMatch = await findStripeCustomerForDonor(donor.id, context);
        
        if (stripeMatch && stripeMatch.matchType === 'email' && stripeMatch.confidence === 'medium') {
          // Update the donor with the Stripe customer ID
          await context.query.DuplicateDonor.updateOne({
            where: { id: donor.id },
            data: { stripe_cus_id: stripeMatch.stripeCustomer.id },
          });
          
          results.matched++;
          results.updated++;
          console.log(`✅ Linked donor ${donor.kpfaId} with Stripe customer ${stripeMatch.stripeCustomer.id}`);
        } else if (stripeMatch) {
          results.matched++;
          console.log(`🔍 Found potential match for donor ${donor.kpfaId} (confidence: ${stripeMatch.confidence})`);
        }
      } catch (error) {
        const errorMsg = `Error processing donor ${donor.kpfaId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        results.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }
  } catch (error) {
    const errorMsg = `Bulk linking failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    results.errors.push(errorMsg);
    console.error(errorMsg);
  }
  
  return results;
}
