import { getStripeService } from './stripeService';
import type { 
  CustomerSearchInput, 
  TransactionSearchInput, 
  ReportInput 
} from './types';

/**
 * Stripe GraphQL Schema Extension for Keystone
 * 
 * This follows Keystone's native pattern for extending GraphQL schema
 * with custom queries and mutations.
 */

export const stripeTypeDefs = `
  # Stripe Customer data
  type StripeCustomer {
    id: String!
    email: String
    name: String
    phone: String
    created: Int!
    currency: String
    balance: Int
    description: String
    metadata: JSON
    address: StripeAddress
  }

  # Stripe Address (billing address)
  type StripeAddress {
    line1: String
    line2: String
    city: String
    state: String
    postal_code: String
    country: String
  }

  # Stripe Payment Method
  type StripePaymentMethod {
    id: String!
    type: String!
    card: StripeCard
    created: Int!
  }

  type StripeCard {
    brand: String
    last4: String
    expMonth: Int
    expYear: Int
  }

  # Stripe Subscription
  type StripeSubscription {
    id: String!
    status: String!
    currentPeriodStart: Int!
    currentPeriodEnd: Int!
    amount: Int!
    currency: String!
    interval: String!
    intervalCount: Int!
    customerId: String!
    metadata: StripeMetadata
  }

  # Stripe Invoice
  type StripeInvoice {
    id: String!
    status: String!
    amountDue: Int!
    amountPaid: Int!
    currency: String!
    created: Int!
    dueDate: Int
    subscriptionId: String
    customerId: String!
  }

  # Stripe Charge/Payment
  type StripeCharge {
    id: String!
    amount: Int!
    currency: String!
    status: String!
    created: Int!
    description: String
    receiptUrl: String
    paymentMethod: String
    transactionId: String
    donorId: String
    donorName: String
    premiums: [String!]
    hasPremiums: Boolean!
  }

  # Comprehensive customer report
  type StripeCustomerReport {
    customer: StripeCustomer
    paymentMethods: [StripePaymentMethod!]!
    subscriptions: [StripeSubscription!]!
    charges: [StripeCharge!]!
    totalSpent: Float!
    lastPaymentDate: Int
  }

  # Donation analytics
  type StripeDonationReport {
    totalDonations: Int!
    totalAmount: Float!
    averageAmount: Float!
    currency: String!
    periodStart: String!
    periodEnd: String!
    topDonors: [StripeTopDonor!]!
    paymentMethodBreakdown: [StripePaymentMethodStats!]!
  }

  type StripeTopDonor {
    customerId: String!
    email: String
    totalAmount: Float!
    donationCount: Int!
  }

  type StripePaymentMethodStats {
    type: String!
    count: Int!
    totalAmount: Float!
  }

  # Health check
  type StripeHealthCheck {
    status: String!
    message: String!
    timestamp: String!
  }

  # Payment reconciliation types
  type StripePaymentReconciliation {
    stripePayments: [StripeCharge!]!
    unmatchedPayments: [StripeCharge!]!
    totalStripePayments: Int!
    totalUnmatchedPayments: Int!
    dateRange: String!
  }

  # Input types
  input StripeCustomerSearchInput {
    email: String
    customerId: String
    limit: Int
  }

  input StripeTransactionSearchInput {
    customerId: String
    startDate: String
    endDate: String
    status: String
    limit: Int
  }

  input StripeReportInput {
    startDate: String
    endDate: String
    includeSubscriptions: Boolean
    includeOneTime: Boolean
    limit: Int
  }

  input StripePaymentReconciliationInput {
    startDate: String!
    endDate: String!
    limit: Int
    premiumsOnly: Boolean
  }

  # Extend the existing Query type
  extend type Query {
    # Single customer lookup
    stripeCustomer(customerId: String!): StripeCustomer

    # Search customers
    stripeCustomers(input: StripeCustomerSearchInput!): [StripeCustomer!]!

    # Get comprehensive customer report
    stripeCustomerReport(customerId: String!): StripeCustomerReport

    # Get recent transactions
    stripeRecentTransactions(input: StripeTransactionSearchInput!): [StripeCharge!]!

    # Get donation analytics report
    stripeDonationReport(input: StripeReportInput!): StripeDonationReport

    # Health check
    stripeHealthCheck: StripeHealthCheck!

    # Find Stripe customer by email (useful for linking with Keystone donors)
    stripeCustomerByEmail(email: String!): [StripeCustomer!]!

    # Payment reconciliation - find Stripe payments that don't match Station Admin
    stripePaymentReconciliation(input: StripePaymentReconciliationInput!): StripePaymentReconciliation!

    # Get individual Stripe objects by ID
    stripeCharge(chargeId: String!): StripeCharge
    stripeSubscription(subscriptionId: String!): StripeSubscription
    stripeInvoice(invoiceId: String!): StripeInvoice
  }
`;

export const stripeResolvers = {
  Query: {
    stripeCustomer: async (
      root: any,
      { customerId }: { customerId: string },
      context: any
    ) => {
      const stripeService = getStripeService();
      const customer = await stripeService.getCustomer(customerId);
      
      if (!customer) {
        throw new Error(`Customer with ID ${customerId} not found`);
      }
      
      return {
        id: customer.id,
        email: customer.email,
        name: customer.name,
        phone: customer.phone,
        created: customer.created,
        currency: customer.currency,
        balance: customer.balance,
        description: customer.description,
        metadata: customer.metadata,
        address: customer.address ? {
          line1: customer.address.line1,
          line2: customer.address.line2,
          city: customer.address.city,
          state: customer.address.state,
          postal_code: customer.address.postal_code,
          country: customer.address.country,
        } : null,
      };
    },

    stripeCustomers: async (
      root: any,
      { input }: { input: CustomerSearchInput },
      context: any
    ) => {
      const stripeService = getStripeService();
      
      if (input.email) {
        const customers = await stripeService.searchCustomersByEmail(input.email);
        return customers.map(customer => ({
          id: customer.id,
          email: customer.email,
          name: customer.name,
          phone: customer.phone,
          created: customer.created,
          currency: customer.currency,
          balance: customer.balance,
          description: customer.description,
          metadata: customer.metadata,
        }));
      }
      
      if (input.customerId) {
        const customer = await stripeService.getCustomer(input.customerId);
        return customer ? [{
          id: customer.id,
          email: customer.email,
          name: customer.name,
          phone: customer.phone,
          created: customer.created,
          currency: customer.currency,
          balance: customer.balance,
          description: customer.description,
          metadata: customer.metadata,
          address: customer.address ? {
            line1: customer.address.line1,
            line2: customer.address.line2,
            city: customer.address.city,
            state: customer.address.state,
            postal_code: customer.address.postal_code,
            country: customer.address.country,
          } : null,
        }] : [];
      }
      
      return [];
    },

    stripeCustomerReport: async (
      root: any,
      { customerId }: { customerId: string },
      context: any
    ) => {
      const stripeService = getStripeService();
      const report = await stripeService.getCustomerReport(customerId);
      
      return {
        customer: report.customer ? {
          id: report.customer.id,
          email: report.customer.email,
          name: report.customer.name,
          phone: report.customer.phone,
          created: report.customer.created,
          currency: report.customer.currency,
          balance: report.customer.balance,
          description: report.customer.description,
          metadata: report.customer.metadata,
          address: report.customer.address ? {
            line1: report.customer.address.line1,
            line2: report.customer.address.line2,
            city: report.customer.address.city,
            state: report.customer.address.state,
            postal_code: report.customer.address.postal_code,
            country: report.customer.address.country,
          } : null,
        } : null,
        paymentMethods: report.paymentMethods.map(pm => ({
          id: pm.id,
          type: pm.type,
          card: pm.card ? {
            brand: pm.card.brand,
            last4: pm.card.last4,
            expMonth: pm.card.exp_month,
            expYear: pm.card.exp_year,
          } : null,
          created: pm.created,
        })),
        subscriptions: report.subscriptions.map(sub => ({
          id: sub.id,
          status: sub.status,
          currentPeriodStart: sub.current_period_start,
          currentPeriodEnd: sub.current_period_end,
          amount: sub.items.data[0]?.price.unit_amount || 0,
          currency: sub.currency,
          interval: sub.items.data[0]?.price.recurring?.interval || '',
          intervalCount: sub.items.data[0]?.price.recurring?.interval_count || 1,
        })),
        charges: report.charges.map(charge => ({
          id: charge.id,
          amount: charge.amount,
          currency: charge.currency,
          status: charge.status,
          created: charge.created,
          description: charge.description,
          receiptUrl: charge.receipt_url,
          paymentMethod: charge.payment_method_details?.type || '',
        })),
        totalSpent: report.totalSpent,
        lastPaymentDate: report.lastPayment?.created || null,
      };
    },

    stripeRecentTransactions: async (
      root: any,
      { input }: { input: TransactionSearchInput },
      context: any
    ) => {
      const stripeService = getStripeService();
      
      if (input.customerId) {
        const charges = await stripeService.getCustomerCharges(input.customerId, input.limit);
        return charges.map(charge => ({
          id: charge.id,
          amount: charge.amount,
          currency: charge.currency,
          status: charge.status,
          created: charge.created,
          description: charge.description,
          receiptUrl: charge.receipt_url,
          paymentMethod: charge.payment_method_details?.type || '',
        }));
      }
      
      // Get recent transactions across all customers
      const days = input.startDate && input.endDate ? 
        Math.ceil((new Date(input.endDate).getTime() - new Date(input.startDate).getTime()) / (1000 * 60 * 60 * 24)) : 
        30;
      
      const charges = await stripeService.getRecentTransactions(days, input.limit);
      return charges.map(charge => ({
        id: charge.id,
        amount: charge.amount,
        currency: charge.currency,
        status: charge.status,
        created: charge.created,
        description: charge.description,
        receiptUrl: charge.receipt_url,
        paymentMethod: charge.payment_method_details?.type || '',
      }));
    },

    stripeHealthCheck: async () => {
      const stripeService = getStripeService();
      const health = await stripeService.healthCheck();
      
      return {
        status: health.status,
        message: health.message,
        timestamp: new Date().toISOString(),
      };
    },

    stripeCustomerByEmail: async (
      root: any,
      { email }: { email: string },
      context: any
    ) => {
      const stripeService = getStripeService();
      const customers = await stripeService.searchCustomersByEmail(email);

      return customers.map(customer => ({
        id: customer.id,
        email: customer.email,
        name: customer.name,
        phone: customer.phone,
        created: customer.created,
        currency: customer.currency,
        balance: customer.balance,
        description: customer.description,
        metadata: customer.metadata,
        address: customer.address ? {
          line1: customer.address.line1,
          line2: customer.address.line2,
          city: customer.address.city,
          state: customer.address.state,
          postal_code: customer.address.postal_code,
          country: customer.address.country,
        } : null,
      }));
    },

    stripePaymentReconciliation: async (
      root: any,
      { input }: { input: { startDate: string; endDate: string; limit?: number; premiumsOnly?: boolean } },
      context: any
    ) => {
      const stripeService = getStripeService();

      // Get Stripe payments for the date range (no limit - fetch all)
      const stripePayments = await stripeService.getPaymentsByDateRange(
        input.startDate,
        input.endDate,
        input.limit || 50000, // Very high limit to effectively remove the cap
        input.premiumsOnly || false
      );

      // Convert Stripe charges to our GraphQL format
      const formattedStripePayments = await Promise.all(stripePayments.map(async charge => {
        // Extract metadata
        const metadata = charge.metadata || {};
        const transactionId = metadata.transaction_id || null;
        const donorId = metadata.donor_id || null;
        const premiumsRaw = metadata.Premium || '[]';

        // Parse premiums (JSON array)
        let premiums: string[] = [];
        try {
          premiums = JSON.parse(premiumsRaw);
          if (!Array.isArray(premiums)) premiums = [];
        } catch (e) {
          premiums = [];
        }

        // Get donor name from Stripe customer if available
        let donorName = null;
        if (charge.customer && typeof charge.customer === 'string') {
          try {
            const customer = await stripeService.getCustomer(charge.customer);
            donorName = customer?.name || null;
          } catch (e) {
            console.warn(`Could not fetch customer ${charge.customer} for charge ${charge.id}`);
          }
        }

        return {
          id: charge.id,
          amount: charge.amount,
          currency: charge.currency,
          status: charge.status,
          created: charge.created,
          description: charge.description,
          receiptUrl: charge.receipt_url,
          paymentMethod: charge.payment_method_details?.type || '',
          transactionId,
          donorId,
          donorName,
          premiums,
          hasPremiums: premiums.length > 0,
        };
      }));

      // Filter to only successful payments for reconciliation
      const successfulStripePayments = formattedStripePayments.filter(payment => payment.status === 'succeeded');

      // For now, return all successful payments as unmatched
      // The actual matching will be done in the frontend component
      // where we can fetch Station Admin data and compare
      const unmatchedPayments = successfulStripePayments;

      return {
        stripePayments: formattedStripePayments,
        unmatchedPayments,
        totalStripePayments: formattedStripePayments.length,
        totalUnmatchedPayments: unmatchedPayments.length,
        dateRange: `${input.startDate} to ${input.endDate}`,
      };
    },

    stripeCharge: async (
      root: any,
      { chargeId }: { chargeId: string },
      context: any
    ) => {
      const stripeService = getStripeService();
      const charge = await stripeService.getCharge(chargeId);

      if (!charge) {
        throw new Error(`Charge with ID ${chargeId} not found`);
      }

      return {
        id: charge.id,
        amount: charge.amount,
        currency: charge.currency,
        status: charge.status,
        created: charge.created,
        description: charge.description,
        receiptUrl: charge.receipt_url,
        paymentMethod: charge.payment_method_details?.type || '',
        transactionId: charge.metadata?.transaction_id || null,
        donorId: charge.metadata?.donor_id || null,
        donorName: charge.metadata?.donor_name || null,
        premiums: charge.metadata?.Premium ?
          (Array.isArray(JSON.parse(charge.metadata.Premium)) ? JSON.parse(charge.metadata.Premium) : []) : [],
        hasPremiums: !!(charge.metadata?.Premium && JSON.parse(charge.metadata.Premium).length > 0),
      };
    },

    stripeSubscription: async (
      root: any,
      { subscriptionId }: { subscriptionId: string },
      context: any
    ) => {
      const stripeService = getStripeService();
      const subscription = await stripeService.getSubscription(subscriptionId);

      if (!subscription) {
        throw new Error(`Subscription with ID ${subscriptionId} not found`);
      }

      return {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: subscription.current_period_start,
        currentPeriodEnd: subscription.current_period_end,
        amount: subscription.items.data[0]?.price.unit_amount || 0,
        currency: subscription.currency,
        interval: subscription.items.data[0]?.price.recurring?.interval || '',
        intervalCount: subscription.items.data[0]?.price.recurring?.interval_count || 1,
        customerId: subscription.customer as string,
        metadata: subscription.metadata,
      };
    },

    stripeInvoice: async (
      root: any,
      { invoiceId }: { invoiceId: string },
      context: any
    ) => {
      const stripeService = getStripeService();
      const invoice = await stripeService.getInvoice(invoiceId);

      if (!invoice) {
        throw new Error(`Invoice with ID ${invoiceId} not found`);
      }

      return {
        id: invoice.id,
        status: invoice.status,
        amountDue: invoice.amount_due,
        amountPaid: invoice.amount_paid,
        currency: invoice.currency,
        created: invoice.created,
        dueDate: invoice.due_date,
        subscriptionId: invoice.subscription as string || null,
        customerId: invoice.customer as string,
      };
    },
  },
};
