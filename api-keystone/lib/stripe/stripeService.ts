import Stripe from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Stripe Service - Read-only wrapper for Stripe API
 * 
 * This service provides read-only access to Stripe data for building custom reports
 * and integrating payment data with KPFA's donor management system.
 */

class StripeService {
  private stripe: Stripe | null = null;
  private isInitialized: boolean = false;
  private initializationError: string | null = null;

  constructor() {
    const secretKey = process.env.STRIPE_SECRET_KEY;

    if (!secretKey) {
      this.initializationError = 'STRIPE_SECRET_KEY environment variable is required';
      console.warn('⚠️ Stripe service not initialized: STRIPE_SECRET_KEY missing');
      return;
    }

    try {
      this.stripe = new Stripe(secretKey, {
        apiVersion: '2024-12-18.acacia', // Use latest API version
        typescript: true,
      });

      this.isInitialized = true;
      console.log('🔗 Stripe service initialized successfully');
    } catch (error) {
      this.initializationError = `Failed to initialize Stripe: ${error}`;
      console.error('❌ Stripe service initialization failed:', error);
    }
  }

  /**
   * Get customer by ID
   */
  async getCustomer(customerId: string): Promise<Stripe.Customer | null> {
    try {
      const customer = await this.stripe.customers.retrieve(customerId);
      return customer as Stripe.Customer;
    } catch (error) {
      console.error(`Error fetching customer ${customerId}:`, error);
      return null;
    }
  }

  /**
   * Search customers by email
   */
  async searchCustomersByEmail(email: string): Promise<Stripe.Customer[]> {
    try {
      const customers = await this.stripe.customers.search({
        query: `email:'${email}'`,
        limit: 10,
      });
      return customers.data;
    } catch (error) {
      console.error(`Error searching customers by email ${email}:`, error);
      return [];
    }
  }

  /**
   * Get customer's payment methods
   */
  async getCustomerPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]> {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        limit: 100,
      });
      return paymentMethods.data;
    } catch (error) {
      console.error(`Error fetching payment methods for customer ${customerId}:`, error);
      return [];
    }
  }

  /**
   * Get customer's subscriptions
   */
  async getCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]> {
    try {
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        limit: 100,
      });
      return subscriptions.data;
    } catch (error) {
      console.error(`Error fetching subscriptions for customer ${customerId}:`, error);
      return [];
    }
  }

  /**
   * Get customer's charges/payments
   */
  async getCustomerCharges(customerId: string, limit: number = 100): Promise<Stripe.Charge[]> {
    try {
      const charges = await this.stripe.charges.list({
        customer: customerId,
        limit: Math.min(limit, 100), // Stripe max is 100
      });
      return charges.data;
    } catch (error) {
      console.error(`Error fetching charges for customer ${customerId}:`, error);
      return [];
    }
  }

  /**
   * Get customer's invoices
   */
  async getCustomerInvoices(customerId: string, limit: number = 100): Promise<Stripe.Invoice[]> {
    try {
      const invoices = await this.stripe.invoices.list({
        customer: customerId,
        limit: Math.min(limit, 100),
      });
      return invoices.data;
    } catch (error) {
      console.error(`Error fetching invoices for customer ${customerId}:`, error);
      return [];
    }
  }

  /**
   * Get payment intent by ID
   */
  async getPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent | null> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
      return paymentIntent;
    } catch (error) {
      console.error(`Error fetching payment intent ${paymentIntentId}:`, error);
      return null;
    }
  }

  /**
   * Get charge by ID
   */
  async getCharge(chargeId: string): Promise<Stripe.Charge | null> {
    try {
      const charge = await this.stripe.charges.retrieve(chargeId);
      return charge;
    } catch (error) {
      console.error(`Error fetching charge ${chargeId}:`, error);
      return null;
    }
  }

  /**
   * Get subscription by ID
   */
  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription | null> {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      return subscription;
    } catch (error) {
      console.error(`Error fetching subscription ${subscriptionId}:`, error);
      return null;
    }
  }

  /**
   * Get invoice by ID
   */
  async getInvoice(invoiceId: string): Promise<Stripe.Invoice | null> {
    try {
      const invoice = await this.stripe.invoices.retrieve(invoiceId);
      return invoice;
    } catch (error) {
      console.error(`Error fetching invoice ${invoiceId}:`, error);
      return null;
    }
  }

  /**
   * Get comprehensive customer data for reporting
   */
  async getCustomerReport(customerId: string): Promise<{
    customer: Stripe.Customer | null;
    paymentMethods: Stripe.PaymentMethod[];
    subscriptions: Stripe.Subscription[];
    charges: Stripe.Charge[];
    invoices: Stripe.Invoice[];
    totalSpent: number;
    lastPayment: Stripe.Charge | null;
  }> {
    const [customer, paymentMethods, subscriptions, charges, invoices] = await Promise.all([
      this.getCustomer(customerId),
      this.getCustomerPaymentMethods(customerId),
      this.getCustomerSubscriptions(customerId),
      this.getCustomerCharges(customerId),
      this.getCustomerInvoices(customerId),
    ]);

    // Calculate total spent from successful charges
    const totalSpent = charges
      .filter(charge => charge.status === 'succeeded')
      .reduce((total, charge) => total + charge.amount, 0) / 100; // Convert from cents

    // Find most recent successful payment
    const lastPayment = charges
      .filter(charge => charge.status === 'succeeded')
      .sort((a, b) => b.created - a.created)[0] || null;

    return {
      customer,
      paymentMethods,
      subscriptions,
      charges,
      invoices,
      totalSpent,
      lastPayment,
    };
  }

  /**
   * Get recent transactions for reporting
   */
  async getRecentTransactions(
    days: number = 30,
    limit: number = 100
  ): Promise<Stripe.Charge[]> {
    try {
      const startDate = Math.floor((Date.now() - (days * 24 * 60 * 60 * 1000)) / 1000);

      const charges = await this.stripe.charges.list({
        created: { gte: startDate },
        limit: Math.min(limit, 100),
      });

      return charges.data;
    } catch (error) {
      console.error(`Error fetching recent transactions:`, error);
      return [];
    }
  }

  /**
   * Get payments by date range for reconciliation with pagination
   */
  async getPaymentsByDateRange(
    startDate: string,
    endDate: string,
    limit: number = 10000,
    premiumsOnly: boolean = false
  ): Promise<Stripe.Charge[]> {
    try {
      const startTimestamp = Math.floor(new Date(startDate).getTime() / 1000);
      const endTimestamp = Math.floor(new Date(endDate).getTime() / 1000);

      const allCharges: Stripe.Charge[] = [];
      let hasMore = true;
      let startingAfter: string | undefined = undefined;

      while (hasMore && allCharges.length < limit) {
        const batchLimit = Math.min(100, limit - allCharges.length); // Stripe API limit is 100 per request

        const charges = await this.stripe.charges.list({
          created: {
            gte: startTimestamp,
            lte: endTimestamp
          },
          limit: batchLimit,
          starting_after: startingAfter,
        });

        // Filter for premiums if requested
        const filteredCharges = premiumsOnly
          ? charges.data.filter(charge => {
              const premiumMetadata = charge.metadata?.Premium;
              if (!premiumMetadata) return false;
              try {
                const premiums = JSON.parse(premiumMetadata);
                return Array.isArray(premiums) && premiums.length > 0;
              } catch (e) {
                return false;
              }
            })
          : charges.data;

        allCharges.push(...filteredCharges);
        hasMore = charges.has_more;

        if (charges.data.length > 0) {
          startingAfter = charges.data[charges.data.length - 1].id;
        } else {
          hasMore = false;
        }
      }

      const filterMsg = premiumsOnly ? ' (with premiums)' : '';
      console.log(`Fetched ${allCharges.length} Stripe charges${filterMsg} for date range ${startDate} to ${endDate}`);
      return allCharges;
    } catch (error) {
      console.error(`Error fetching payments by date range:`, error);
      return [];
    }
  }

  /**
   * Health check for Stripe connection
   */
  async healthCheck(): Promise<{ status: 'ok' | 'error'; message: string }> {
    try {
      // Try to fetch account info as a simple health check
      await this.stripe.accounts.retrieve();
      return { status: 'ok', message: 'Stripe connection healthy' };
    } catch (error) {
      return { 
        status: 'error', 
        message: `Stripe connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  /**
   * Check if service is properly initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
let stripeServiceInstance: StripeService | null = null;

export function getStripeService(): StripeService {
  if (!stripeServiceInstance) {
    stripeServiceInstance = new StripeService();
  }
  return stripeServiceInstance;
}

export { StripeService };
export default getStripeService;
