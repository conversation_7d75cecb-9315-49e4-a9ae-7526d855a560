import Stripe from 'stripe';

/**
 * Custom types for Stripe integration with KPFA donor system
 */

export interface CustomerReport {
  customer: Stripe.Customer | null;
  paymentMethods: Stripe.PaymentMethod[];
  subscriptions: Stripe.Subscription[];
  charges: Stripe.Charge[];
  invoices: Stripe.Invoice[];
  totalSpent: number;
  lastPayment: Stripe.Charge | null;
}

export interface DonorStripeData {
  stripeCustomerId: string;
  customerReport: CustomerReport;
  donorId?: string; // Link to Keystone donor record
}

export interface PaymentSummary {
  totalAmount: number;
  currency: string;
  paymentDate: Date;
  status: 'succeeded' | 'pending' | 'failed';
  paymentMethod: string;
  description?: string;
}

export interface SubscriptionSummary {
  id: string;
  status: Stripe.Subscription.Status;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  amount: number;
  currency: string;
  interval: string;
  intervalCount: number;
}

export interface StripeHealthCheck {
  status: 'ok' | 'error';
  message: string;
  timestamp: Date;
}

export interface ReportFilters {
  startDate?: Date;
  endDate?: Date;
  customerId?: string;
  email?: string;
  status?: 'succeeded' | 'pending' | 'failed';
  limit?: number;
}

export interface DonationReport {
  totalDonations: number;
  totalAmount: number;
  averageAmount: number;
  currency: string;
  period: {
    start: Date;
    end: Date;
  };
  topDonors: Array<{
    customerId: string;
    email: string;
    totalAmount: number;
    donationCount: number;
  }>;
  paymentMethodBreakdown: Array<{
    type: string;
    count: number;
    totalAmount: number;
  }>;
}

// GraphQL input types
export interface CustomerSearchInput {
  email?: string;
  customerId?: string;
  limit?: number;
}

export interface TransactionSearchInput {
  customerId?: string;
  startDate?: string; // ISO date string
  endDate?: string;   // ISO date string
  status?: 'succeeded' | 'pending' | 'failed';
  limit?: number;
}

export interface ReportInput {
  startDate?: string;
  endDate?: string;
  includeSubscriptions?: boolean;
  includeOneTime?: boolean;
  limit?: number;
}
