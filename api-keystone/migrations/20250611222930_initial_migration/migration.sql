-- Create<PERSON><PERSON>
CREATE TYPE "ShowTypeType" AS ENUM ('news_politics', 'Music', 'Culture', 'Special', 'Talk');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL DEFAULT '',
    "email" TEXT NOT NULL DEFAULT '',
    "password" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DuplicateDonor" (
    "id" TEXT NOT NULL,
    "kpfaId" INTEGER NOT NULL,
    "group" TEXT,
    "primaryGroup" TEXT,
    "firstname" TEXT NOT NULL DEFAULT '',
    "lastname" TEXT NOT NULL DEFAULT '',
    "phone" TEXT NOT NULL DEFAULT '',
    "email" TEXT NOT NULL DEFAULT '',
    "address1" TEXT NOT NULL DEFAULT '',
    "address2" TEXT NOT NULL DEFAULT '',
    "partner_firstname" TEXT NOT NULL DEFAULT '',
    "partner_lastname" TEXT NOT NULL DEFAULT '',
    "city" TEXT NOT NULL DEFAULT '',
    "state" TEXT NOT NULL DEFAULT '',
    "country" TEXT NOT NULL DEFAULT '',
    "postal_code" TEXT NOT NULL DEFAULT '',
    "notes" TEXT NOT NULL DEFAULT '',
    "type" TEXT NOT NULL DEFAULT '',
    "membership_level" TEXT NOT NULL DEFAULT '',
    "deceased" BOOLEAN NOT NULL DEFAULT false,
    "donotsolicit" BOOLEAN NOT NULL DEFAULT false,
    "stripe_cus_id" TEXT NOT NULL DEFAULT '',
    "paypal_user_id" TEXT NOT NULL DEFAULT '',
    "memsys_id" INTEGER,
    "allegiance_id" INTEGER,
    "date_created" TIMESTAMP(3),
    "date_updated" TIMESTAMP(3),
    "paperless" BOOLEAN NOT NULL DEFAULT false,
    "paperless_token" TEXT NOT NULL DEFAULT '',
    "dataSource" TEXT DEFAULT 'LEGACY_SYNC',
    "isModified" BOOLEAN NOT NULL DEFAULT false,
    "lastModifiedAt" TIMESTAMP(3),
    "lastModifiedBy" TEXT NOT NULL DEFAULT '',

    CONSTRAINT "DuplicateDonor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DuplicateGroup" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL DEFAULT '',
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "suggestionType" TEXT,
    "matchingCriteria" JSONB,
    "reviewedBy" TEXT NOT NULL DEFAULT '',
    "reviewedAt" TIMESTAMP(3),
    "reviewNotes" TEXT NOT NULL DEFAULT '',
    "autoCreated" BOOLEAN NOT NULL DEFAULT false,
    "mergedAt" TIMESTAMP(3),
    "mergedBy" TEXT NOT NULL DEFAULT '',
    "mergedDonorsData" JSONB,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DuplicateGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DonorPairDecision" (
    "id" TEXT NOT NULL,
    "donorPair" TEXT NOT NULL DEFAULT '',
    "donor1" TEXT,
    "donor2" TEXT,
    "decision" TEXT NOT NULL,
    "decidedBy" TEXT NOT NULL DEFAULT '',
    "decidedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT NOT NULL DEFAULT '',
    "originatingGroup" TEXT,
    "confidence" INTEGER,

    CONSTRAINT "DonorPairDecision_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Show" (
    "id" TEXT NOT NULL,
    "oldId" INTEGER,
    "name" TEXT NOT NULL DEFAULT '',
    "hosts" TEXT NOT NULL DEFAULT '',
    "description" TEXT NOT NULL DEFAULT '',
    "imgUrl" TEXT NOT NULL DEFAULT '',
    "starts" TEXT NOT NULL DEFAULT '',
    "ends" TEXT NOT NULL DEFAULT '',
    "duration" INTEGER NOT NULL,
    "days" TEXT NOT NULL DEFAULT '',
    "weeks" INTEGER NOT NULL,
    "dadId" INTEGER NOT NULL,
    "type" "ShowTypeType" NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "Show_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "DuplicateDonor_kpfaId_key" ON "DuplicateDonor"("kpfaId");

-- CreateIndex
CREATE UNIQUE INDEX "DuplicateDonor_primaryGroup_key" ON "DuplicateDonor"("primaryGroup");

-- CreateIndex
CREATE INDEX "DuplicateDonor_group_idx" ON "DuplicateDonor"("group");

-- CreateIndex
CREATE UNIQUE INDEX "DuplicateGroup_name_key" ON "DuplicateGroup"("name");

-- CreateIndex
CREATE UNIQUE INDEX "DonorPairDecision_donorPair_key" ON "DonorPairDecision"("donorPair");

-- CreateIndex
CREATE INDEX "DonorPairDecision_donor1_idx" ON "DonorPairDecision"("donor1");

-- CreateIndex
CREATE INDEX "DonorPairDecision_donor2_idx" ON "DonorPairDecision"("donor2");

-- CreateIndex
CREATE INDEX "DonorPairDecision_originatingGroup_idx" ON "DonorPairDecision"("originatingGroup");

-- CreateIndex
CREATE UNIQUE INDEX "Show_oldId_key" ON "Show"("oldId");

-- AddForeignKey
ALTER TABLE "DuplicateDonor" ADD CONSTRAINT "DuplicateDonor_group_fkey" FOREIGN KEY ("group") REFERENCES "DuplicateGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DuplicateDonor" ADD CONSTRAINT "DuplicateDonor_primaryGroup_fkey" FOREIGN KEY ("primaryGroup") REFERENCES "DuplicateGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DonorPairDecision" ADD CONSTRAINT "DonorPairDecision_donor1_fkey" FOREIGN KEY ("donor1") REFERENCES "DuplicateDonor"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DonorPairDecision" ADD CONSTRAINT "DonorPairDecision_donor2_fkey" FOREIGN KEY ("donor2") REFERENCES "DuplicateDonor"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DonorPairDecision" ADD CONSTRAINT "DonorPairDecision_originatingGroup_fkey" FOREIGN KEY ("originatingGroup") REFERENCES "DuplicateGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;
