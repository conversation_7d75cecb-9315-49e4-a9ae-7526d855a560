/*
  Warnings:

  - You are about to drop the `DonorPairDecision` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "DonorPairDecision" DROP CONSTRAINT "DonorPairDecision_donor1_fkey";

-- DropForeignKey
ALTER TABLE "DonorPairDecision" DROP CONSTRAINT "DonorPairDecision_donor2_fkey";

-- DropForeignKey
ALTER TABLE "DonorPairDecision" DROP CONSTRAINT "DonorPairDecision_originatingGroup_fkey";

-- DropTable
DROP TABLE "DonorPairDecision";

-- CreateTable
CREATE TABLE "RejectedGroupSuggestion" (
    "id" TEXT NOT NULL,
    "groupSignature" TEXT NOT NULL DEFAULT '',
    "rejectedBy" TEXT NOT NULL DEFAULT '',
    "rejectedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "originalGroupName" TEXT NOT NULL DEFAULT '',

    CONSTRAINT "RejectedGroupSuggestion_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "RejectedGroupSuggestion_groupSignature_key" ON "RejectedGroupSuggestion"("groupSignature");
