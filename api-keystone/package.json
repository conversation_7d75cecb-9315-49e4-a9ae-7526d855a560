{"name": "keystone-app", "version": "1.0.2", "private": true, "scripts": {"dev": "keystone dev", "start": "keystone start", "build": "keystone build", "postinstall": "keystone build --no-ui --frozen", "analyze-phone-duplicates": "tsx scripts/analyzePhoneDuplicates.ts", "expire-donor-data": "tsx scripts/expireDonorData.ts", "test-expiration": "tsx scripts/testExpiration.ts expiration", "test-expiration-destructive": "tsx scripts/testExpiration.ts expiration 800000 destructive", "test-scheduler": "tsx scripts/testExpiration.ts scheduler", "cleanup-deleted-orphans": "tsx scripts/cleanupDeletedOrphans.ts", "cleanup-deleted-orphans-dry-run": "tsx scripts/cleanupDeletedOrphans.ts --dry-run", "test-stripe": "tsx scripts/testStripeIntegration.ts", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@graphql-tools/schema": "^10.0.23", "@keystone-6/auth": "^8.1.0", "@keystone-6/core": "^6.1.0", "@keystone-6/fields-document": "^9.1.1", "dotenv": "^16.4.5", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "node-cron": "^3.0.3", "stripe": "^18.2.1", "tsx": "^4.7.0", "typescript": "^5.8.3"}, "devDependencies": {"@types/mysql2": "github:types/mysql2", "@types/node": "^22.15.24", "@types/node-cron": "^3.0.11", "c8": "^10.1.3"}}