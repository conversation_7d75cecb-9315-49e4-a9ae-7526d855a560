// This file is automatically generated by Keystone, do not modify it manually.
// Modify your Keystone config when you want to change this.

datasource postgresql {
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
  provider          = "postgresql"
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        String    @id @default(cuid())
  name      String    @default("")
  email     String    @unique @default("")
  password  String
  createdAt DateTime? @default(now())
}

model DuplicateDonor {
  id                String          @id @default(cuid())
  kpfaId            Int             @unique
  group             DuplicateGroup? @relation("DuplicateDonor_group", fields: [groupId], references: [id])
  groupId           String?         @map("group")
  primaryGroup      DuplicateGroup? @relation("DuplicateDonor_primaryGroup", fields: [primaryGroupId], references: [id])
  primaryGroupId    String?         @unique @map("primaryGroup")
  firstname         String          @default("")
  lastname          String          @default("")
  phone             String          @default("")
  email             String          @default("")
  address1          String          @default("")
  address2          String          @default("")
  partner_firstname String          @default("")
  partner_lastname  String          @default("")
  city              String          @default("")
  state             String          @default("")
  country           String          @default("")
  postal_code       String          @default("")
  notes             String          @default("")
  type              String          @default("")
  membership_level  String          @default("")
  deceased          Boolean         @default(false)
  donotsolicit      Boolean         @default(false)
  stripe_cus_id     String          @default("")
  paypal_user_id    String          @default("")
  memsys_id         Int?
  allegiance_id     Int?
  date_created      DateTime?
  date_updated      DateTime?
  paperless         Boolean         @default(false)
  paperless_token   String          @default("")
  dataSource        String?         @default("LEGACY_SYNC")
  isModified        Boolean         @default(false)
  lastModifiedAt    DateTime?
  lastModifiedBy    String          @default("")

  @@index([groupId])
}

model DuplicateGroup {
  id               String           @id @default(cuid())
  name             String           @unique @default("")
  status           String           @default("PENDING")
  suggestionType   String?
  matchingCriteria Json?
  reviewedBy       String           @default("")
  reviewedAt       DateTime?
  reviewNotes      String           @default("")
  autoCreated      Boolean          @default(false)
  primaryDonor     DuplicateDonor?  @relation("DuplicateDonor_primaryGroup")
  duplicateDonors  DuplicateDonor[] @relation("DuplicateDonor_group")
  mergedAt         DateTime?
  mergedBy         String           @default("")
  mergedDonorsData Json?
  createdAt        DateTime?        @default(now())
}

model Show {
  id          String       @id @default(cuid())
  oldId       Int?         @unique
  name        String       @default("")
  hosts       String       @default("")
  description String       @default("")
  imgUrl      String       @default("")
  starts      String       @default("")
  ends        String       @default("")
  duration    Int
  days        String       @default("")
  weeks       Int
  dadId       Int
  type        ShowTypeType
  active      Boolean      @default(true)
}

enum ShowTypeType {
  news_politics
  Music
  Culture
  Special
  Talk
}
