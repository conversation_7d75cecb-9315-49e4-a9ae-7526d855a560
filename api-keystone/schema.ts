// Welcome to your schema
//   Schema driven development is Keystone's modus operandi
//
// This file is where we define the lists, fields and hooks for our data.
// If you want to learn more about how lists are configured, please read
// - https://keystonejs.com/docs/config/lists

import { list } from "@keystone-6/core";
import { allowAll, denyAll } from "@keystone-6/core/access";
import { mergeSchemas } from "@graphql-tools/schema";
import { stripeTypeDefs, stripeResolvers } from "./lib/stripe/stripeSchema";

// see https://keystonejs.com/docs/fields/overview for the full list of fields
//   this is a few common fields for an example
import {
  text,
  relationship,
  password,
  timestamp,
  select,
  integer,
  checkbox,
  json,
} from "@keystone-6/core/fields";

// the document field is a more complicated field, so it has it's own package
import { document } from "@keystone-6/fields-document";
// if you want to make your own fields, see https://keystonejs.com/docs/guides/custom-fields

// when using Typescript, you can refine your types to a stricter subset by importing
// the generated types from '.keystone/types'
import type { Lists } from ".keystone/types";

export const lists: Lists = {
  User: list({
    //   You can find out more about authentication and access control at https://keystonejs.com/docs/guides/auth-and-access-control
    access: allowAll,

    // this is the fields for our User list
    fields: {
      // by adding isRequired, we enforce that every User should have a name
      //   if no name is provided, an error will be displayed
      name: text({ validation: { isRequired: true } }),
      email: text({
        validation: { isRequired: true },
        // by adding isIndexed: 'unique', we're saying that no user can have the same
        // email as another user - this may or may not be a good idea for your project
        isIndexed: "unique",
      }),

      password: password({ validation: { isRequired: true } }),

      createdAt: timestamp({
        // this sets the timestamp to Date.now() when the user is first created
        defaultValue: { kind: "now" },
      }),
    },
  }),

  DuplicateDonor: list({
    access: allowAll,
    fields: {
      kpfaId: integer({
        isIndexed: "unique",
        validation: { isRequired: true },
        hooks: {
          resolveInput: ({ resolvedData }) => {
            // Ensure kpfaId is always stored as a number
            if (
              resolvedData.kpfaId !== undefined &&
              resolvedData.kpfaId !== null
            ) {
              if (typeof resolvedData.kpfaId !== "number") {
                // Convert to number if it's a string or other type
                const numericId = parseInt(
                  String(resolvedData.kpfaId).trim(),
                  10,
                );
                if (!isNaN(numericId) && numericId > 0) {
                  return numericId;
                }
                // If conversion fails, throw an error instead of returning null
                throw new Error(`Invalid kpfaId: ${resolvedData.kpfaId}. Must be a positive integer.`);
              }
            }
            return resolvedData.kpfaId;
          },
        },
      }),
      // Duplicate management relationships
      group: relationship({
        ref: "DuplicateGroup.duplicateDonors",
        many: false,
        hooks: {
          validateInput: async ({ resolvedData, item, context, addValidationError }) => {
            // Only run this hook when a donor is being connected to a group
            // Handle the type checking for relationship data
            const groupData = resolvedData?.group;
            if (groupData && typeof groupData === 'object' && 'connect' in groupData && groupData.connect?.id) {
              try {
                const groupId = groupData.connect.id;
                
                // Only proceed if this is a new connection or different group
                if (!item?.groupId || item.groupId !== groupId) {
                  // Check if the group has MERGED status
                  const group = await context.query.DuplicateGroup.findOne({
                    where: { id: groupId },
                    query: 'id status'
                  });
                  
                  // If the group is in MERGED status, change it to PENDING
                  if (group && group.status === 'MERGED') {
                    await context.query.DuplicateGroup.updateOne({
                      where: { id: groupId },
                      data: { status: 'PENDING' }
                    });
                  }
                }
              } catch (error) {
                // Log error but don't stop the operation
                console.error('Error checking/updating group status:', error);
              }
            }
          }
        }
      }),
      primaryGroup: relationship({
        ref: "DuplicateGroup.primaryDonor",
        many: false,
      }),
      // Working copy of donor fields (can be modified during duplicate resolution)
      firstname: text(),
      lastname: text(),
      phone: text(),
      email: text(),
      address1: text(),
      address2: text(),
      partner_firstname: text(),
      partner_lastname: text(),
      city: text(),
      state: text(),
      country: text(),
      postal_code: text(),
      notes: text(),
      type: text(),
      membership_level: text(),
      deceased: checkbox(),
      donotsolicit: checkbox(),
      stripe_cus_id: text(),
      paypal_user_id: text(),
      memsys_id: integer(), // Legacy API sends this as a number
      allegiance_id: integer(), // Legacy API sends this as a number
      date_created: timestamp(),
      date_updated: timestamp(),
      paperless: checkbox(),
      paperless_token: text(),
      // Metadata for tracking data source and modifications
      dataSource: select({
        type: "string",
        options: [
          { label: "Legacy Sync", value: "LEGACY_SYNC" },
          { label: "New Donation", value: "NEW_DONATION" },
          { label: "Manual Entry", value: "MANUAL_ENTRY" },
          { label: "Merge Result", value: "MERGE_RESULT" },
        ],
        defaultValue: "LEGACY_SYNC",
      }),
      isModified: checkbox({ 
        defaultValue: false,
        ui: {
          description: 'True if this record has been modified from its original legacy state'
        }
      }),
      lastModifiedAt: timestamp(),
      lastModifiedBy: text(),
    },
    ui: {
      labelField: 'kpfaId',
      description: 'Working copy of donor records for duplicate management',
    },
  }),

  DuplicateGroup: list({
    access: allowAll,
    fields: {
      name: text({ isIndexed: "unique", validation: { isRequired: true } }),
      status: select({
        type: "string",
        options: [
          { label: "Suggested - Pending Review", value: "SUGGESTED" },
          { label: "Unconfirmed", value: "PENDING" },
          { label: "Confirmed", value: "READY_TO_MERGE" },
          { label: "Merged", value: "MERGED" },
          { label: "Rejected", value: "REJECTED" },
        ],
        defaultValue: "PENDING",
        validation: { isRequired: true },
      }),
      // Enhanced fields for suggestion metadata
      suggestionType: select({
        type: "string",
        options: [
          { label: "New Duplicate Group", value: "NEW_GROUP" },
          { label: "Addition to Existing Group", value: "ADD_TO_GROUP" },
        ],
        ui: {
          description: 'Only relevant when status is SUGGESTED'
        }
      }),
      // Matching criteria used for this suggestion
      matchingCriteria: json({
        ui: {
          description: 'Details about why these donors were grouped (phone match, etc.)'
        }
      }),
      // Staff decision tracking
      reviewedBy: text({
        ui: {
          description: 'Staff member who reviewed this suggestion'
        }
      }),
      reviewedAt: timestamp({
        ui: {
          description: 'When this suggestion was reviewed'
        }
      }),
      reviewNotes: text({
        ui: {
          description: 'Notes from staff review'
        }
      }),
      // Auto-creation threshold tracking
      autoCreated: checkbox({ 
        defaultValue: false,
        ui: {
          description: 'True if this suggestion was auto-created by script'
        }
      }),
      primaryDonor: relationship({
        ref: "DuplicateDonor.primaryGroup",
        many: false,
      }),
      duplicateDonors: relationship({
        ref: "DuplicateDonor.group",
        many: true,
      }),
      // Fields for tracking merge operations
      mergedAt: timestamp(),
      mergedBy: text(),
      // Store essential merge metadata for historical record
      mergedDonorsData: json(), // Stores minimal merge metadata (mergedDate, mergedBy, primaryDonorKpfaId)
      // Track creation timestamp
      createdAt: timestamp({ defaultValue: { kind: "now" } }),
    },
    ui: {
      description: 'Groups of duplicate donors - includes both suggestions and confirmed groups',
    },
  }),

  Show: list({
    //todo: actual ACL! before launch!! duh!
    access: allowAll,
    fields: {
      oldId: integer({
        isIndexed: "unique",
      }),
      name: text({
        validation: { isRequired: true },
      }),
      hosts: text({
        validation: { isRequired: true },
      }),
      description: text({
        validation: { isRequired: true },
      }),
      imgUrl: text(),
      starts: text({
        validation: {
          isRequired: true,
          match: {
            regex: /^([01]\d|2[0-3]):?([0-5]\d):?([0-5]\d)$/,
            explanation: "Start time must be in the format HH:MM:SS",
          },
        },
      }),
      ends: text({
        validation: { isRequired: true },
      }),
      duration: integer({
        validation: { isRequired: true },
      }),
      days: text({
        validation: {
          isRequired: true,
          match: {
            regex:
              /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)(,(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday))*$/,
            explanation:
              "Days must include at least one day of the week and be separated by commas if more than one day is specified.",
          },
        },
      }),
      //note: WordPress and PHP store ther weeks as 01...53, keeping leading zero. To correctly store in the new db as an integer we need to remove the leading zero.
      weeks: integer({
        validation: {
          min: 1,
          max: 53,
          isRequired: true,
        },
      }),
      dadId: integer({
        validation: { isRequired: true },
      }),
      type: select({
        type: "enum",
        validation: { isRequired: true },
        options: [
          { label: "News & Politics", value: "news_politics" },
          "Music",
          "Culture",
          "Special",
          "Talk",
        ],
      }),
      active: checkbox({
        defaultValue: true,
      }),
    },
  }),
};

// Add bulk merge mutation and Stripe integration
export const extendGraphqlSchema = (schema: any) =>
  mergeSchemas({
    schemas: [schema],
    typeDefs: `
      ${stripeTypeDefs}
      type Query {
        keystone: KeystoneMeta!
        getSuggestedDuplicates(status: String, limit: Int): [DuplicateGroup]
      }

      type Mutation {
        createDuplicateSuggestion(
          triggerDonorKpfaId: Int!
          candidateKpfaIds: [Int!]!
          matchingCriteria: JSON
        ): DuplicateGroup
        
        acceptSuggestion(
          groupId: ID!
          staffMember: String!
        ): DuplicateGroup
        
        rejectSuggestion(
          groupId: ID!
          staffMember: String!
        ): DuplicateGroup
        
        markDonorsAsNotDuplicates(
          kpfaIds: [Int!]!
          staffMember: String!
        ): DuplicateGroup
        
        removeDonorFromGroup(
          groupId: ID!
          donorId: ID!
        ): DuplicateGroup
        
        deleteGroupWithMembers(
          groupId: ID!
          rejectGroupSuggestion: Boolean
        ): DeleteGroupResult
      }
      
      type DeleteGroupResult {
        id: ID!
        name: String!
        groupRejected: Boolean!
        deletedDonorCount: Int!
      }


    `,
    resolvers: {
      Query: {
        getSuggestedDuplicates: async (root: any, { status = 'SUGGESTED', limit = 50 }: { status?: string; limit?: number }, context: any) => {
          const where: any = { status: { equals: status } };

          return await context.query.DuplicateGroup.findMany({
            where,
            take: limit,
            orderBy: [{ createdAt: 'desc' }],
            query: `
              id name status suggestionType autoCreated createdAt
              duplicateDonors { id kpfaId firstname lastname phone }
              primaryDonor { id kpfaId firstname lastname phone }
              matchingCriteria
            `,
          });
        },
        // Merge Stripe resolvers
        ...stripeResolvers.Query,
      },

      Mutation: {
        createDuplicateSuggestion: async (
          root: any,
          { triggerDonorKpfaId, candidateKpfaIds, matchingCriteria }: {
            triggerDonorKpfaId: number;
            candidateKpfaIds: number[];
            matchingCriteria: any;
          },
          context: any
        ) => {
          // Find the trigger donor
          const triggerDonor = await context.query.DuplicateDonor.findOne({
            where: { kpfaId: triggerDonorKpfaId },
            query: 'id kpfaId firstname lastname',
          });

          if (!triggerDonor) {
            throw new Error(`Trigger donor with kpfaId ${triggerDonorKpfaId} not found`);
          }

          // Find candidate donors
          const candidateDonors = await context.query.DuplicateDonor.findMany({
            where: { kpfaId: { in: candidateKpfaIds } },
            query: 'id kpfaId firstname lastname',
          });

          if (candidateDonors.length === 0) {
            throw new Error('No candidate donors found');
          }

          // Generate suggestion name
          const triggerName = `${triggerDonor.firstname || ''} ${triggerDonor.lastname || ''}`.trim();
          const candidateNames = candidateDonors
            .slice(0, 2)
            .map((d: any) => `${d.firstname || ''} ${d.lastname || ''}`.trim())
            .filter((name: string) => name.length > 0);

          const suggestionName = candidateNames.length > 0
            ? `Phone Match: ${triggerName} + ${candidateNames.join(', ')}`
            : `Phone Match: ${triggerName} (${triggerDonor.kpfaId})`;

          // Create all donor IDs for the group
          const allDonorIds = [triggerDonor.id, ...candidateDonors.map((d: any) => d.id)];

          // Create the suggestion as a DuplicateGroup with SUGGESTED status
          return await context.query.DuplicateGroup.createOne({
            data: {
              name: suggestionName,
              status: 'SUGGESTED',
              suggestionType: 'NEW_GROUP',
              duplicateDonors: {
                connect: allDonorIds.map((id: string) => ({ id }))
              },
              primaryDonor: { connect: { id: triggerDonor.id } },
              matchingCriteria,
              autoCreated: true,
            },
            query: `
              id name status suggestionType autoCreated createdAt
              primaryDonor { id kpfaId firstname lastname }
              duplicateDonors { id kpfaId firstname lastname }
            `,
          });
        },

        acceptSuggestion: async (
          root: any,
          { groupId, staffMember }: { groupId: string; staffMember: string },
          context: any
        ) => {
          // Update the group status from SUGGESTED to PENDING
          return await context.query.DuplicateGroup.updateOne({
            where: { id: groupId },
            data: {
              status: 'PENDING',
              reviewedBy: staffMember,
            },
            query: 'id name status reviewedBy',
          });
        },

        rejectSuggestion: async (
          root: any,
          { groupId, staffMember }: { groupId: string; staffMember: string; notes?: string },
          context: any
        ) => {
          // First get the group to verify it exists
          const group = await context.query.DuplicateGroup.findOne({
            where: { id: groupId },
            query: `id name status`,
          });

          if (!group) {
            throw new Error(`Duplicate group with id ${groupId} not found`);
          }

          // Update the group status to REJECTED
          // This serves as the single source of truth for rejection tracking
          return await context.query.DuplicateGroup.updateOne({
            where: { id: groupId },
            data: {
              status: 'REJECTED',
              reviewedBy: staffMember,
            },
            query: 'id name status reviewedBy',
          });
        },

        markDonorsAsNotDuplicates: async (
          root: any,
          { kpfaIds, staffMember }: {
            kpfaIds: number[];
            staffMember: string;
          },
          context: any
        ) => {
          // Validate input
          if (kpfaIds.length < 2) {
            throw new Error('At least 2 donors are required to mark as not duplicates');
          }

          // Remove duplicates and sort
          const uniqueKpfaIds = [...new Set(kpfaIds)].sort((a, b) => a - b);
          
          // Create a rejected group to prevent future suggestions of these donors
          const groupName = `Manual rejection: ${uniqueKpfaIds.join(', ')}`;
          
          return await context.query.DuplicateGroup.createOne({
            data: {
              name: groupName,
              status: 'REJECTED',
              reviewedBy: staffMember,
              autoCreated: false,
              reviewNotes: `Manually marked as not duplicates for KPFA IDs: ${uniqueKpfaIds.join(', ')}`,
            },
            query: 'id name status reviewedBy',
          });
        },

        removeDonorFromGroup: async (
          root: any,
          { groupId, donorId }: {
            groupId: string;
            donorId: string;
          },
          context: any
        ) => {
          console.log(`Starting removeDonorFromGroup for donor ${donorId} from group ${groupId}`);
          
          // First get the group to check current state
          const group = await context.query.DuplicateGroup.findOne({
            where: { id: groupId },
            query: `
              id name status
              duplicateDonors { id kpfaId }
              primaryDonor { id kpfaId }
            `,
          });

          if (!group) {
            throw new Error(`Duplicate group with id ${groupId} not found`);
          }

          // Find the donor to be removed
          const donorToRemove = await context.query.DuplicateDonor.findOne({
            where: { id: donorId },
            query: 'id kpfaId group { id } primaryGroup { id }',
          });

          if (!donorToRemove) {
            throw new Error(`Donor with id ${donorId} not found`);
          }

          console.log('Group:', JSON.stringify(group, null, 2));
          console.log('Donor to remove:', JSON.stringify(donorToRemove, null, 2));

          const isDuplicateDonor = group.duplicateDonors?.some((d: any) => d.id === donorId);
          const isPrimaryDonor = group.primaryDonor?.id === donorId;

          console.log(`isDuplicateDonor: ${isDuplicateDonor}, isPrimaryDonor: ${isPrimaryDonor}`);

          if (!isDuplicateDonor && !isPrimaryDonor) {
            throw new Error(`Donor ${donorId} is not part of group ${groupId}`);
          }

          // Note: RejectedGroupSuggestion records don't need cleanup since they don't 
          // have relationships to specific donor records, only kpfaId signatures

          // Handle the case where the donor is BOTH primary and in duplicateDonors
          // Remove from duplicateDonors first if needed
          if (isDuplicateDonor) {
            console.log('Disconnecting from duplicateDonors...');
            await context.query.DuplicateGroup.updateOne({
              where: { id: groupId },
              data: {
                duplicateDonors: { disconnect: { id: donorId } },
              },
              query: 'id',
            });
          }

          // Handle primary donor reassignment
          if (isPrimaryDonor) {
            console.log('Handling primary donor reassignment...');
            const remainingDonors = group.duplicateDonors?.filter((d: any) => d.id !== donorId);
            
            if (remainingDonors && remainingDonors.length > 0) {
              console.log(`Setting new primary donor: ${remainingDonors[0].id}`);
              await context.query.DuplicateGroup.updateOne({
                where: { id: groupId },
                data: {
                  primaryDonor: { connect: { id: remainingDonors[0].id } },
                },
                query: 'id',
              });
            } else {
              console.log('Disconnecting primary donor (no remaining donors)');
              await context.query.DuplicateGroup.updateOne({
                where: { id: groupId },
                data: {
                  primaryDonor: { disconnect: true },
                },
                query: 'id',
              });
            }
          }

          // Check the donor state before deletion
          console.log('Checking donor state before deletion...');
          const donorBeforeDeletion = await context.query.DuplicateDonor.findOne({
            where: { id: donorId },
            query: 'id kpfaId group { id } primaryGroup { id }',
          });
          
          console.log('Donor state before deletion:', JSON.stringify(donorBeforeDeletion, null, 2));

          // Now delete the DuplicateDonor record using context.db
          console.log('Attempting to delete DuplicateDonor using context.db...');
          try {
            await context.db.DuplicateDonor.deleteOne({
              where: { id: donorId },
            });
            console.log('DuplicateDonor deleted successfully');
          } catch (deleteError: any) {
            console.error('Delete error:', deleteError);
            throw new Error(`Failed to delete donor: ${deleteError.message}`);
          }

          // Check the final group state and update status if needed
          const updatedGroup = await context.query.DuplicateGroup.findOne({
            where: { id: groupId },
            query: `
              id name status
              duplicateDonors { id kpfaId }
              primaryDonor { id kpfaId }
            `,
          });

          const totalDonors = (updatedGroup.duplicateDonors?.length || 0) + (updatedGroup.primaryDonor ? 1 : 0);

          if (totalDonors <= 1) {
            await context.query.DuplicateGroup.updateOne({
              where: { id: groupId },
              data: { status: 'PENDING' },
              query: 'id',
            });
          }

          console.log('removeDonorFromGroup completed successfully');

          // Return the final group state
          return await context.query.DuplicateGroup.findOne({
            where: { id: groupId },
            query: `
              id name status
              duplicateDonors { id kpfaId firstname lastname }
              primaryDonor { id kpfaId firstname lastname }
            `,
          });
        },

        deleteGroupWithMembers: async (
          root: any,
          { groupId, rejectGroupSuggestion }: {
            groupId: string;
            rejectGroupSuggestion?: boolean;
          },
          context: any
        ) => {
          console.log(`Starting deleteGroupWithMembers for group ${groupId}`);
          
          // Create sudo context for elevated privileges
          const sudoContext = context.sudo();
          
          // First get the group with all its data
          const group = await context.query.DuplicateGroup.findOne({
            where: { id: groupId },
            query: `
              id name status autoCreated
              duplicateDonors { id kpfaId }
              primaryDonor { id kpfaId }
            `,
          });

          if (!group) {
            throw new Error(`Duplicate group with id ${groupId} not found`);
          }

          console.log('Group to delete:', JSON.stringify(group, null, 2));

          // Get all donor IDs in the group for cleanup
          const allDonorIds = [
            ...(group.duplicateDonors?.map((d: any) => d.id) || []),
            ...(group.primaryDonor ? [group.primaryDonor.id] : [])
          ];

          const allDonorKpfaIds = [
            ...(group.duplicateDonors?.map((d: any) => d.kpfaId) || []),
            ...(group.primaryDonor ? [group.primaryDonor.kpfaId] : [])
          ];

          // If requested, mark the group as rejected before deletion to prevent future suggestions
          if (rejectGroupSuggestion && group.autoCreated === true) {
            console.log('Marking group as rejected before deletion...');
            try {
              await context.query.DuplicateGroup.updateOne({
                where: { id: groupId },
                data: {
                  status: 'REJECTED',
                  reviewedBy: 'System', // Could be enhanced to track actual user
                },
                query: 'id',
              });
              console.log('Group marked as rejected');
            } catch (error) {
              console.error('Error marking group as rejected:', error);
              // Don't fail the deletion if status update fails
            }
          }

          // Disconnect all relationships first
          console.log('Disconnecting group relationships...');
          if (group.duplicateDonors && group.duplicateDonors.length > 0) {
            await context.query.DuplicateGroup.updateOne({
              where: { id: groupId },
              data: {
                duplicateDonors: { disconnect: group.duplicateDonors.map((d: any) => ({ id: d.id })) },
              },
              query: 'id',
            });
          }

          if (group.primaryDonor) {
            await context.query.DuplicateGroup.updateOne({
              where: { id: groupId },
              data: {
                primaryDonor: { disconnect: true },
              },
              query: 'id',
            });
          }

          // Now delete all duplicate donors using sudo context to bypass any access control
          console.log('Deleting duplicate donors using sudo context...');
          
          for (const donorId of allDonorIds) {
            try {
              console.log(`Deleting donor ${donorId}`);
              
              // Double-check the donor still exists before attempting deletion
              const donorExists = await sudoContext.query.DuplicateDonor.findOne({
                where: { id: donorId },
                query: 'id',
              });
              
              if (donorExists) {
                await sudoContext.db.DuplicateDonor.deleteOne({
                  where: { id: donorId },
                });
                console.log(`Successfully deleted donor ${donorId}`);
              } else {
                console.log(`Donor ${donorId} no longer exists, skipping deletion`);
              }
            } catch (deleteError: any) {
              console.error(`Error deleting donor ${donorId}:`, deleteError);
              throw new Error(`Failed to delete donor ${donorId}: ${deleteError.message}`);
            }
          }

          console.log(`Successfully deleted ${allDonorIds.length} donors`);

          // Finally, delete the group itself
          console.log('Deleting the group...');
          const deletedGroup = await context.query.DuplicateGroup.deleteOne({
            where: { id: groupId },
            query: 'id name',
          });

          console.log('deleteGroupWithMembers completed successfully');

          return {
            ...deletedGroup,
            groupRejected: rejectGroupSuggestion && group.autoCreated,
            deletedDonorCount: allDonorIds.length,
          };
        },
      },
    },
  });
