# Donor Import Scripts

This directory contains scripts for importing donor data into Keystone for duplicate detection.

## Quick Start - Test Mode

To test the import process with a small batch of 10 records:

```bash
# Test with sample CSV data (recommended for first test)
npm run sync-donors-test

# Test with database connection (first 10 records)
npm run sync-donors-test-db
```

## Full Import

Once you've tested and verified everything works:

```bash
# Import from CSV file (recommended for production)
npm run sync-donors

# Import from database (requires database connection)
npm run sync-donors -- --source=database
```

## Files

### `syncAllDonors.ts`
Main import script that supports both CSV and database sources.

**Features:**
- ✅ Test mode (`--test`) - processes only 10 records
- ✅ CSV import mode (default) - reads from `donors_export.csv`
- ✅ Database import mode (`--source=database`) - connects to PHP MySQL database
- ✅ Batch processing for performance
- ✅ Progress tracking and ETA
- ✅ Error handling and recovery

**Usage:**
```bash
# Test modes
tsx scripts/syncAllDonors.ts --test                    # Test with CSV
tsx scripts/syncAllDonors.ts --test --source=database # Test with database

# Full import
tsx scripts/syncAllDonors.ts                          # Full CSV import
tsx scripts/syncAllDonors.ts --source=database        # Full database import
```

### `donors_export_test.csv`
Sample CSV file with 10 test donor records. Used automatically when running in test mode.

**Test data includes:**
- Various phone number formats: `(*************`, `************`, `******-555-9999`, `5105551111`
- Different membership levels: Basic, Vigilant, Vibrant
- Mix of deceased/active and solicit/no-solicit donors
- Various address formats and partner information

### `donors_export.csv`
Your actual donor export file. Place your full donor export here for production import.

**Required CSV columns:**
```
id,firstname,lastname,phone,email,address1,address2,partner_firstname,partner_lastname,
city,state,country,postal_code,notes,type,membership_level,deceased,donotsolicit,
stripe_cus_id,paypal_user_id,memsys_id,allegiance_id,date_created,date_updated,
paperless,paperless_token
```

## Testing Workflow

1. **Start with test mode:**
   ```bash
   npm run sync-donors-test
   ```

2. **Verify in Keystone admin:**
   - Go to http://localhost:3000/admin
   - Check "Duplicate Donors" section
   - Verify 10 test records were imported correctly

3. **Test duplicate detection:**
   - The test data includes some donors with similar phone numbers
   - Run duplicate detection to see if it finds matches

4. **Scale up gradually:**
   ```bash
   # Test with your actual data (first 10 records)
   npm run sync-donors-test
   
   # When ready, run full import
   npm run sync-donors
   ```

## Environment Variables

For database import mode, set these in your `.env` file:

```env
PHP_DB_HOST=localhost
PHP_DB_USER=your_username
PHP_DB_PASSWORD=your_password
PHP_DB_NAME=kpfa_api
PHP_DB_PORT=3306
```

## Performance

- **Test mode:** ~10 records in <30 seconds
- **Full import:** ~54k records in 3-8 minutes
- **Batch size:** 250 records per batch (10 for test mode)
- **Memory usage:** Optimized for large datasets

## Troubleshooting

### CSV file not found
```
📁 CSV file not found: /path/to/donors_export.csv
💡 To use CSV import, place your donor export in: donors_export.csv
```
**Solution:** Place your CSV export in `api-keystone/scripts/donors_export.csv`

### Database connection failed
```
💥 Fatal error during import: Error: connect ECONNREFUSED
```
**Solution:** Check your database credentials in `.env` file

### Import stuck or slow
- Check database connection
- Reduce batch size in the script
- Monitor system resources

## Next Steps

After successful import:

1. **Verify data in Keystone admin**
2. **Run batch duplicate detection**
3. **Test the duplicate detection workflow**
4. **Set up real-time duplicate detection in PHP** 