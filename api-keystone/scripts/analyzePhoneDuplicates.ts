/**
 * Phone Duplicate Analysis Script
 * 
 * Analyzes a CSV file with id,phone columns to detect potential duplicates
 * and create DuplicateGroup records with SUGGESTED status in Keystone.
 * 
 * This script auto-creates DuplicateDonor records from CSV data and properly handles two scenarios:
 * 1. NEW_GROUP - Create new duplicate group when no existing groups found
 * 2. ADD_TO_GROUP - Add donors to existing group when some are already grouped
 * 
 * Usage: 
 *   npm run analyze-phone-duplicates -- --limit=100
 *   npm run analyze-phone-duplicates -- --dry-run --limit=10
 */

import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import { normalizePhoneNumber, isValidForDuplicateDetection } from '../utils/phoneNormalization';

const prisma = new PrismaClient();

interface PhoneRecord {
  id: number;
  phone: string;
  dateCreated?: string;
  lastDonationDate?: string;
  normalizedPhone?: string;
}

interface PhoneGroup {
  normalizedPhone: string;
  records: PhoneRecord[];
}

interface ExistingGroupInfo {
  groupId: string;
  groupName: string;
  donorIds: string[];
}

interface SuggestionPlan {
  type: 'NEW_GROUP' | 'ADD_TO_GROUP';
  phoneGroup: PhoneGroup;
  existingGroups: ExistingGroupInfo[];
  newDonorIds: string[];
  allDonorIds: string[];
  donorsToCreate: PhoneRecord[];
}

interface AnalysisStats {
  totalRecords: number;
  validPhones: number;
  uniquePhones: number;
  duplicateGroups: number;
  newGroupSuggestions: number;
  addToGroupSuggestions: number;
  skippedExisting: number;
  donorsCreated: number;
  errors: number;
  startTime: Date;
  dryRun: boolean;
}

// CSV parsing function
function parseCSVLine(line: string): string[] {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        current += '"';
        i++; // Skip next quote
      } else {
        inQuotes = !inQuotes;
      }
    } else if (char === ',' && !inQuotes) {
      result.push(current);
      current = '';
    } else {
      current += char;
    }
  }
  
  result.push(current);
  return result;
}

async function loadPhoneRecordsFromCSV(limit?: number): Promise<PhoneRecord[]> {
  const csvPath = path.join(__dirname, 'donors_with_phone_by_donation_date.csv');
  const legacyCsvPath = path.join(__dirname, 'donors_with_phone_by_date.csv');
  const testCsvPath = path.join(__dirname, 'test_donors_donation_sample.csv');
  const legacyTestCsvPath = path.join(__dirname, 'test_donors_sample.csv');
  
  let actualCsvPath = csvPath;
  let isTestData = false;
  
  // Try new filename first, then legacy, then test data
  if (!fs.existsSync(csvPath)) {
    if (fs.existsSync(legacyCsvPath)) {
      console.log(`📁 New CSV format not found: ${csvPath}`);
      console.log(`📁 Using legacy format: ${legacyCsvPath}`);
      actualCsvPath = legacyCsvPath;
    } else if (fs.existsSync(testCsvPath)) {
      console.log(`📁 Production CSV not found: ${csvPath}`);
      console.log(`📁 Using test data with donation dates: ${testCsvPath}`);
      actualCsvPath = testCsvPath;
      isTestData = true;
    } else if (fs.existsSync(legacyTestCsvPath)) {
      console.log(`📁 Production CSV not found: ${csvPath}`);
      console.log(`📁 Using legacy test data: ${legacyTestCsvPath}`);
      actualCsvPath = legacyTestCsvPath;
      isTestData = true;
    } else {
      console.log(`📁 CSV file not found: ${csvPath}`);
      console.log(`💡 Please place your phone export CSV file as: donors_with_phone_by_donation_date.csv`);
      console.log(`💡 Expected format: id,phone,date_created,last_donation_date (with headers)`);
              console.log(`💡 Export command: sudo mysql -u root -p api_db -e "(SELECT 'id','phone','date_created','last_donation_date') UNION ALL (SELECT d.id, d.phone, d.date_created, COALESCE(MAX(don.Timestamp), d.date_created) as last_donation_date FROM donors d LEFT JOIN donations don ON d.id = don.donor_id WHERE d.phone IS NOT NULL AND d.phone != '' GROUP BY d.id, d.phone, d.date_created) ORDER BY last_donation_date DESC, date_created DESC;" --batch --raw > donors_with_phone_by_donation_date.csv`);
        console.log(`💡 Or run with test data by ensuring test_donors_donation_sample.csv exists`);
      throw new Error('CSV file not found');
    }
  }
  
  console.log(`📁 Reading CSV file: ${actualCsvPath}`);
  if (isTestData) {
    console.log(`🧪 Using TEST DATA - perfect for validation!`);
  }
  
  const content = fs.readFileSync(actualCsvPath, 'utf-8');
  const lines = content.split('\n').filter(line => line.trim());
  
  if (lines.length < 2) {
    throw new Error('CSV file must have at least a header row and one data row');
  }
  
  // Parse header - support both tab and comma separated
  const headerLine = lines[0];
  const delimiter = headerLine.includes('\t') ? '\t' : ',';
  const headers = headerLine.split(delimiter).map(h => h.toLowerCase().trim());
  
  console.log(`📊 Found ${lines.length - 1} data rows with ${headers.length} columns`);
  console.log(`📋 Headers: ${headers.join(', ')}`);
  
  // Find column indices
  const idIndex = headers.findIndex(h => h === 'id');
  const phoneIndex = headers.findIndex(h => h === 'phone');
  const dateCreatedIndex = headers.findIndex(h => h === 'date_created');
  const lastDonationDateIndex = headers.findIndex(h => h === 'last_donation_date');
  
  if (idIndex === -1 || phoneIndex === -1) {
    throw new Error(`Required columns not found. Expected: id, phone. Found: ${headers.join(', ')}`);
  }
  
  console.log(`📋 Using columns: id (${idIndex}), phone (${phoneIndex})${dateCreatedIndex !== -1 ? `, date_created (${dateCreatedIndex})` : ''}${lastDonationDateIndex !== -1 ? `, last_donation_date (${lastDonationDateIndex})` : ''}`);
  
  // Important: CSV should be ordered by last_donation_date DESC (newest donation first) for proper primary donor selection
  if (lastDonationDateIndex !== -1) {
    console.log(`✅ Donation date column found - CSV should be ordered by last_donation_date DESC for optimal primary donor selection`);
    console.log(`   📌 First donor in each duplicate group will become the primary donor (most recent donation)`);
  } else if (dateCreatedIndex !== -1) {
    console.log(`✅ Date column found - CSV should be ordered by date_created DESC for optimal primary donor selection`);
    console.log(`   📌 First donor in each duplicate group will become the primary donor (most recent account)`);
  } else {
    console.log(`⚠️  No date columns found - using CSV order for primary donor selection`);
  }
  
  // Parse data rows
  const records: PhoneRecord[] = [];
  const maxRecords = limit || lines.length - 1;
  
  for (let i = 1; i <= Math.min(maxRecords, lines.length - 1); i++) {
    try {
      const values = lines[i].split(delimiter);
      
      const id = parseInt(values[idIndex]?.trim() || '');
      const phone = values[phoneIndex]?.trim() || '';
      const dateCreated = dateCreatedIndex !== -1 ? values[dateCreatedIndex]?.trim() : undefined;
      const lastDonationDate = lastDonationDateIndex !== -1 ? values[lastDonationDateIndex]?.trim() : undefined;
      
      if (isNaN(id) || !phone) {
        continue; // Skip invalid rows
      }
      
      records.push({ 
        id, 
        phone, 
        dateCreated,
        lastDonationDate 
      });
      
    } catch (error) {
      console.warn(`⚠️  Skipping invalid row ${i}: ${error}`);
    }
  }
  
  console.log(`✅ Successfully parsed ${records.length} phone records`);
  if (limit && records.length >= limit) {
    console.log(`🎯 Limited to ${limit} records as requested`);
  }
  
  return records;
}

function groupRecordsByPhone(records: PhoneRecord[]): Map<string, PhoneRecord[]> {
  console.log('🔍 Grouping records by normalized phone numbers...');
  
  const phoneGroups = new Map<string, PhoneRecord[]>();
  let validPhones = 0;
  
  for (const record of records) {
    if (!isValidForDuplicateDetection(record.phone)) {
      continue;
    }
    
    const normalized = normalizePhoneNumber(record.phone);
    if (!normalized) {
      continue;
    }
    
    validPhones++;
    record.normalizedPhone = normalized;
    
    if (!phoneGroups.has(normalized)) {
      phoneGroups.set(normalized, []);
    }
    
    phoneGroups.get(normalized)!.push(record);
  }
  
  console.log(`   ${validPhones.toLocaleString()} records have valid phone numbers`);
  console.log(`   ${phoneGroups.size.toLocaleString()} unique phone numbers found`);
  
  return phoneGroups;
}

/**
 * Sort donors within a group by donation activity:
 * 1. Most recent donation first
 * 2. If no donations, most recently created first
 * 3. If no dates, preserve CSV order (which should be donation date DESC)
 */
function sortDonorsByDonationActivity(records: PhoneRecord[]): PhoneRecord[] {
  return [...records].sort((a, b) => {
    // First priority: Sort by last donation date (most recent first)
    if (a.lastDonationDate && b.lastDonationDate) {
      const aDate = new Date(a.lastDonationDate);
      const bDate = new Date(b.lastDonationDate);
      if (aDate.getTime() !== bDate.getTime()) {
        return bDate.getTime() - aDate.getTime(); // DESC - most recent first
      }
    }
    
    // Second priority: If only one has donations, it comes first
    if (a.lastDonationDate && !b.lastDonationDate) return -1;
    if (!a.lastDonationDate && b.lastDonationDate) return 1;
    
    // Third priority: Sort by account creation date (most recent first)
    if (a.dateCreated && b.dateCreated) {
      const aDate = new Date(a.dateCreated);
      const bDate = new Date(b.dateCreated);
      if (aDate.getTime() !== bDate.getTime()) {
        return bDate.getTime() - aDate.getTime(); // DESC - most recent first
      }
    }
    
    // Fourth priority: If only one has creation date, it comes first
    if (a.dateCreated && !b.dateCreated) return -1;
    if (!a.dateCreated && b.dateCreated) return 1;
    
    // Final fallback: preserve original CSV order (by ID)
    return a.id - b.id;
  });
}

function findDuplicateGroups(phoneGroups: Map<string, PhoneRecord[]>): PhoneGroup[] {
  console.log('🔍 Identifying duplicate groups...');
  
  const duplicateGroups: PhoneGroup[] = [];
  
  for (const [normalizedPhone, records] of phoneGroups) {
    if (records.length > 1) {
      // Sort records by donation activity to ensure proper primary donor selection
      const sortedRecords = sortDonorsByDonationActivity(records);
      duplicateGroups.push({
        normalizedPhone,
        records: sortedRecords
      });
    }
  }
  
  // Sort by group size (largest first)
  duplicateGroups.sort((a, b) => b.records.length - a.records.length);
  
  console.log(`   Found ${duplicateGroups.length.toLocaleString()} groups with duplicates`);
  
  // Show top 10 largest groups
  console.log('   📊 Largest duplicate groups:');
  duplicateGroups.slice(0, 10).forEach((group, index) => {
    const sampleRecord = group.records[0];
    const allIds = group.records.map(r => r.id).join(', ');
    console.log(`      ${index + 1}. ${group.records.length} records with phone ${sampleRecord.phone}`);
    console.log(`         IDs: ${allIds}`);
  });
  
  return duplicateGroups;
}

async function analyzeSuggestionType(phoneGroup: PhoneGroup, dryRun: boolean = false): Promise<SuggestionPlan> {
  // Find which donors from this phone group exist in Keystone
  const kpfaIds = phoneGroup.records.map(r => r.id);
  
  const existingDonors = await prisma.duplicateDonor.findMany({
    where: { kpfaId: { in: kpfaIds } },
    select: {
      id: true,
      kpfaId: true,
      groupId: true,
      group: {
        select: {
          id: true,
          name: true,
          status: true
        }
      }
    }
  });

  // Map kpfaId to donor record for easy lookup
  const donorMap = new Map(existingDonors.map(d => [d.kpfaId!, d]));
  
  // Find existing groups these donors belong to
  const existingGroups = new Map<string, ExistingGroupInfo>();
  const newDonorIds: string[] = [];
  const allDonorIds: string[] = [];
  const donorsToCreate: PhoneRecord[] = [];

  for (const record of phoneGroup.records) {
    const donor = donorMap.get(record.id);
    if (donor) {
      // Donor already exists in Keystone
      allDonorIds.push(donor.id);
      
      if (donor.groupId && donor.group) {
        // This donor is already in a group
        if (!existingGroups.has(donor.groupId)) {
          existingGroups.set(donor.groupId, {
            groupId: donor.groupId,
            groupName: donor.group.name,
            donorIds: []
          });
        }
        existingGroups.get(donor.groupId)!.donorIds.push(donor.id);
      } else {
        // This donor exists but is not in any group
        newDonorIds.push(donor.id);
      }
    } else {
      // Donor doesn't exist in Keystone - we'll need to create it
      donorsToCreate.push(record);
    }
  }

  const existingGroupsArray = Array.from(existingGroups.values());

  // Determine suggestion type
  let type: 'NEW_GROUP' | 'ADD_TO_GROUP';
  
  if (existingGroupsArray.length === 0) {
    // No existing groups - create new group
    type = 'NEW_GROUP';
  } else if (existingGroupsArray.length === 1 && (newDonorIds.length > 0 || donorsToCreate.length > 0)) {
    // One existing group with new donors - add to group
    type = 'ADD_TO_GROUP';
  } else {
    // Multiple existing groups or all donors already grouped - create new suggestion for staff review
    // Staff can manually decide how to handle complex merge scenarios
    type = 'NEW_GROUP';
  }

  return {
    type,
    phoneGroup,
    existingGroups: existingGroupsArray,
    newDonorIds,
    allDonorIds,
    donorsToCreate
  };
}

function generateSuggestionName(plan: SuggestionPlan): string {
  const sampleRecord = plan.phoneGroup.records[0];
  const totalCount = plan.phoneGroup.records.length;
  
  switch (plan.type) {
    case 'NEW_GROUP':
      return `Phone Match: ${totalCount} donors with ${sampleRecord.phone}`;
    case 'ADD_TO_GROUP':
      const groupName = plan.existingGroups[0]?.groupName || 'Unknown Group';
      return `Add ${plan.newDonorIds.length} donors to "${groupName}" (phone: ${sampleRecord.phone})`;
    default:
      return `Phone Match: ${totalCount} donors with ${sampleRecord.phone}`;
  }
}

async function createSuggestionFromPlan(plan: SuggestionPlan, stats: AnalysisStats): Promise<void> {
  try {
    // Skip if no donors to work with
    if (plan.allDonorIds.length === 0 && plan.donorsToCreate.length === 0) {
      console.log(`   ⏭️  Skipping - no donors to process for phone ${plan.phoneGroup.normalizedPhone}`);
      return;
    }

    // Skip if all donors are already in the same group and no new donors to create
    if (plan.type === 'NEW_GROUP' && plan.existingGroups.length === 1 && plan.newDonorIds.length === 0 && plan.donorsToCreate.length === 0) {
      console.log(`   ⏭️  Skipping - all donors already in same group for phone ${plan.phoneGroup.normalizedPhone}`);
      stats.skippedExisting++;
      return;
    }

    // Check if a suggestion already exists for this phone
    const existingSuggestion = await prisma.duplicateGroup.findFirst({
      where: {
        status: 'SUGGESTED',
        matchingCriteria: {
          path: ['normalizedPhone'],
          equals: plan.phoneGroup.normalizedPhone
        }
      }
    });

    if (existingSuggestion) {
      console.log(`   ⏭️  Skipping - suggestion already exists for phone ${plan.phoneGroup.normalizedPhone}`);
      stats.skippedExisting++;
      return;
    }

    const name = generateSuggestionName(plan);
    
    const matchingCriteria = {
      matchType: 'exact_phone',
      normalizedPhone: plan.phoneGroup.normalizedPhone,
      totalRecords: plan.phoneGroup.records.length,
      detectionMethod: 'phone_analysis',
      detectedAt: new Date().toISOString(),
      sourceData: 'csv_phone_export',
      suggestionType: plan.type,
      existingGroups: plan.existingGroups.map(g => ({
        groupId: g.groupId,
        groupName: g.groupName,
        donorCount: g.donorIds.length
      })),
      recordDetails: plan.phoneGroup.records.map(r => ({
        kpfaId: r.id,
        phone: r.phone,
        normalizedPhone: r.normalizedPhone,
        dateCreated: r.dateCreated,
        lastDonationDate: r.lastDonationDate
      })),
      donorsToCreate: plan.donorsToCreate.length
    };

    if (stats.dryRun) {
      console.log(`   🧪 DRY RUN - Would create ${plan.type} suggestion: ${name}`);
      console.log(`   🧪 DRY RUN - Would create ${plan.donorsToCreate.length} new DuplicateDonor records`);
      console.log(`   🧪 DRY RUN - Would connect ${plan.allDonorIds.length} existing donors`);
      
      // Log primary donor selection details for dry run
      if (plan.phoneGroup.records.length > 0) {
        const primaryRecord = plan.phoneGroup.records[0]; // First record after sorting
        const donationInfo = primaryRecord.lastDonationDate 
          ? `last donation: ${primaryRecord.lastDonationDate}`
          : `no donations, created: ${primaryRecord.dateCreated || 'unknown'}`;
        console.log(`   🎯 DRY RUN - Would set primary donor: ID ${primaryRecord.id} (${donationInfo})`);
      }
      
      // Update stats for dry run
      switch (plan.type) {
        case 'NEW_GROUP':
          stats.newGroupSuggestions++;
          break;
        case 'ADD_TO_GROUP':
          stats.addToGroupSuggestions++;
          break;
      }
      stats.donorsCreated += plan.donorsToCreate.length;
      return;
    }

    // Create new DuplicateDonor records for donors that don't exist
    const createdDonorIds: string[] = [];
    for (const record of plan.donorsToCreate) {
      const createdDonor = await prisma.duplicateDonor.create({
        data: {
          kpfaId: record.id,
          phone: record.phone,
          dataSource: 'PHONE_ANALYSIS',
          isModified: false,
          lastModifiedAt: new Date(),
          lastModifiedBy: 'phone_analysis_script'
        }
      });
      createdDonorIds.push(createdDonor.id);
      stats.donorsCreated++;
    }

    // Combine existing and newly created donor IDs
    const allDonorIdsForGroup = [...plan.allDonorIds, ...createdDonorIds];

    // Create suggestion as DuplicateGroup with SUGGESTED status
    await prisma.duplicateGroup.create({
      data: {
        name,
        status: 'SUGGESTED',
        suggestionType: plan.type,
        matchingCriteria,
        autoCreated: true,
        createdAt: new Date(),
        // Connect all relevant donors
        duplicateDonors: {
          connect: allDonorIdsForGroup.map(id => ({ id }))
        },
        // Set primary donor: First donor in the group becomes primary
        // Since records are sorted by donation activity (most recent donation first),
        // this ensures the donor with the most recent donation becomes the primary donor
        ...(allDonorIdsForGroup.length > 0 && {
          primaryDonor: { connect: { id: allDonorIdsForGroup[0] } }
        })
      }
    });

    // Update stats
    switch (plan.type) {
      case 'NEW_GROUP':
        stats.newGroupSuggestions++;
        break;
      case 'ADD_TO_GROUP':
        stats.addToGroupSuggestions++;
        break;
    }

    console.log(`   ✅ Created ${plan.type} suggestion: ${name}`);
    if (createdDonorIds.length > 0) {
      console.log(`   ✅ Created ${createdDonorIds.length} new DuplicateDonor records`);
    }
    
    // Log primary donor selection details
    if (plan.phoneGroup.records.length > 0) {
      const primaryRecord = plan.phoneGroup.records[0]; // First record after sorting
      const donationInfo = primaryRecord.lastDonationDate 
        ? `last donation: ${primaryRecord.lastDonationDate}`
        : `no donations, created: ${primaryRecord.dateCreated || 'unknown'}`;
      console.log(`   🎯 Primary donor: ID ${primaryRecord.id} (${donationInfo})`);
    }
    
  } catch (error) {
    console.error(`❌ Error creating suggestion for phone ${plan.phoneGroup.normalizedPhone}:`, error);
    stats.errors++;
  }
}

async function processDuplicateGroups(duplicateGroups: PhoneGroup[], stats: AnalysisStats): Promise<void> {
  console.log('🔄 Analyzing duplicate groups and creating suggestions...');
  
  if (stats.dryRun) {
    console.log('🧪 DRY RUN MODE - No actual changes will be made to the database');
  }
  
  for (let i = 0; i < duplicateGroups.length; i++) {
    const group = duplicateGroups[i];
    
    console.log(`\n📦 Processing group ${i + 1}/${duplicateGroups.length}: ${group.records.length} records with phone ${group.normalizedPhone}`);
    
    // Analyze what type of suggestion this should be
    const plan = await analyzeSuggestionType(group, stats.dryRun);
    console.log(`   📋 Suggestion type: ${plan.type}`);
    console.log(`   📊 Existing donors: ${plan.allDonorIds.length}, New donors to create: ${plan.donorsToCreate.length}`);
    
    await createSuggestionFromPlan(plan, stats);
    
    // Progress update
    const percentage = (((i + 1) / duplicateGroups.length) * 100).toFixed(1);
    console.log(`   📈 Progress: ${i + 1}/${duplicateGroups.length} groups (${percentage}%)`);
    
    // Small delay to prevent overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const limitArg = args.find(arg => arg.startsWith('--limit='));
  const dryRunArg = args.find(arg => arg.startsWith('--dry-run'));
  const limit = limitArg ? parseInt(limitArg.split('=')[1]) : null;
  const dryRun = !!dryRunArg;
  
  console.log('🚀 Starting phone duplicate analysis...');
  console.log('📞 This will analyze phone numbers from CSV and create duplicate suggestions\n');
  
  if (dryRun) {
    console.log('🧪 DRY RUN MODE - No changes will be made to the database');
  }
  
  if (limit) {
    console.log(`🎯 LIMIT: Processing only ${limit} records from CSV`);
  } else {
    console.log('📊 Processing ALL records from CSV');
  }
  
  const stats: AnalysisStats = {
    totalRecords: 0,
    validPhones: 0,
    uniquePhones: 0,
    duplicateGroups: 0,
    newGroupSuggestions: 0,
    addToGroupSuggestions: 0,
    skippedExisting: 0,
    donorsCreated: 0,
    errors: 0,
    startTime: new Date(),
    dryRun,
  };

  try {
    // Step 1: Load phone records from CSV
    const records = await loadPhoneRecordsFromCSV(limit ?? undefined);
    stats.totalRecords = records.length;

    if (records.length === 0) {
      console.log('❌ No valid phone records found in CSV');
      process.exit(1);
    }

    // Step 2: Group records by normalized phone numbers
    const phoneGroups = groupRecordsByPhone(records);
    stats.uniquePhones = phoneGroups.size;
    stats.validPhones = Array.from(phoneGroups.values()).reduce((sum, group) => sum + group.length, 0);

    // Step 3: Find groups with duplicates
    const duplicateGroups = findDuplicateGroups(phoneGroups);
    stats.duplicateGroups = duplicateGroups.length;

    if (duplicateGroups.length === 0) {
      console.log('✅ No duplicate phone numbers found!');
      console.log('   All phone numbers are unique.');
      process.exit(0);
    }

    // Step 4: Create suggestions based on analysis
    await processDuplicateGroups(duplicateGroups, stats);

    const totalTime = Date.now() - stats.startTime.getTime();
    const totalSuggestions = stats.newGroupSuggestions + stats.addToGroupSuggestions;

    console.log('\n🎉 Phone duplicate analysis completed!');
    console.log(`⏱️  Total time: ${formatDuration(totalTime)}`);
    console.log('📈 Final stats:');
    console.log(`   - Total records analyzed: ${stats.totalRecords.toLocaleString()}`);
    console.log(`   - Records with valid phones: ${stats.validPhones.toLocaleString()}`);
    console.log(`   - Unique phone numbers: ${stats.uniquePhones.toLocaleString()}`);
    console.log(`   - Duplicate groups found: ${stats.duplicateGroups.toLocaleString()}`);
    console.log(`   - NEW_GROUP suggestions: ${stats.newGroupSuggestions.toLocaleString()}`);
    console.log(`   - ADD_TO_GROUP suggestions: ${stats.addToGroupSuggestions.toLocaleString()}`);
    console.log(`   - Total suggestions created: ${totalSuggestions.toLocaleString()}`);
    console.log(`   - DuplicateDonor records created: ${stats.donorsCreated.toLocaleString()}`);
    console.log(`   - Skipped (already exist): ${stats.skippedExisting.toLocaleString()}`);
    console.log(`   - Errors: ${stats.errors.toLocaleString()}`);

    if (dryRun) {
      console.log(`\n🧪 DRY RUN COMPLETE - No actual changes were made`);
      console.log(`🧪 Would have created ${totalSuggestions} suggestions and ${stats.donorsCreated} donor records`);
    } else if (totalSuggestions > 0) {
      console.log(`\n🎯 Next steps:`);
      console.log(`   1. Visit the Keystone admin to review ${totalSuggestions} new suggestions`);
      console.log(`   2. Filter DuplicateGroups by status = "SUGGESTED" to see suggestions`);
      console.log(`   3. Staff can accept/reject suggestions using the admin interface`);
      console.log(`   4. Accepted suggestions will change status to "PENDING" for further review`);
    }

  } catch (error) {
    console.error('💥 Fatal error during analysis:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
main().catch(console.error); 