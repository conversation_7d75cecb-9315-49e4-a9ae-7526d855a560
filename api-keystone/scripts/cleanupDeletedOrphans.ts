import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

interface DeletedOrphan {
  id: string;
  firstname?: string;
  lastname?: string;
  email?: string;
}

function parseCSVLine(line: string): string[] {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }
  
  result.push(current.trim());
  return result;
}

async function readCSVFile(filePath: string): Promise<DeletedOrphan[]> {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const lines = fileContent.split('\n').filter(line => line.trim());
    
    if (lines.length === 0) {
      throw new Error('CSV file is empty');
    }
    
    // Parse header row to get column indices
    const headers = parseCSVLine(lines[0]);
    const idIndex = headers.findIndex(h => h.toLowerCase() === 'id');
    const firstnameIndex = headers.findIndex(h => h.toLowerCase() === 'firstname');
    const lastnameIndex = headers.findIndex(h => h.toLowerCase() === 'lastname');
    const emailIndex = headers.findIndex(h => h.toLowerCase() === 'email');
    
    if (idIndex === -1) {
      throw new Error('CSV file must have an "id" column');
    }
    
    const results: DeletedOrphan[] = [];
    
    // Parse data rows (skip header)
    for (let i = 1; i < lines.length; i++) {
      const row = parseCSVLine(lines[i]);
      
      if (row.length > idIndex && row[idIndex]) {
        // Remove commas from the kpfaId field before parsing as integer
        const cleanedId = row[idIndex].trim().replace(/,/g, '');
        
        if (cleanedId && !isNaN(parseInt(cleanedId))) {
          results.push({
            id: cleanedId,
            firstname: firstnameIndex !== -1 ? row[firstnameIndex]?.trim() : undefined,
            lastname: lastnameIndex !== -1 ? row[lastnameIndex]?.trim() : undefined,
            email: emailIndex !== -1 ? row[emailIndex]?.trim() : undefined
          });
        }
      }
    }
    
    console.log(`📄 Read ${results.length} orphaned donor records from CSV`);
    return results;
    
  } catch (error) {
    throw new Error(`Failed to read CSV file: ${error}`);
  }
}

async function deleteDuplicateDonorsByKpfaIds(kpfaIds: number[]): Promise<number> {
  console.log(`🗑️  Attempting to delete ${kpfaIds.length} DuplicateDonors with matching kpfaIds...`);
  
  // First, fetch all the donors we're about to delete to get detailed info
  const donorsToDelete = await prisma.duplicateDonor.findMany({
    where: {
      kpfaId: {
        in: kpfaIds
      }
    },
    include: {
      group: {
        select: {
          id: true,
          name: true,
          status: true,
          _count: {
            select: {
              duplicateDonors: true
            }
          }
        }
      }
    }
  });

  console.log(`🔍 Found ${donorsToDelete.length} DuplicateDonors to delete with group information:`);
  
  // Group donors by their duplicate group for better logging
  const donorsByGroup = new Map<string, typeof donorsToDelete>();
  const orphanDonors: typeof donorsToDelete = [];
  
  donorsToDelete.forEach(donor => {
    if (donor.group) {
      const groupKey = `${donor.group.id}:${donor.group.name}`;
      if (!donorsByGroup.has(groupKey)) {
        donorsByGroup.set(groupKey, []);
      }
      donorsByGroup.get(groupKey)!.push(donor);
    } else {
      orphanDonors.push(donor);
    }
  });

  // Log individual donors being removed from groups
  for (const [groupKey, donors] of donorsByGroup) {
    const [groupId, groupName] = groupKey.split(':');
    const group = donors[0].group!;
    const remainingInGroup = group._count.duplicateDonors - donors.length;
    
    console.log(`\n📦 Group "${groupName}" (${group.status}) - removing ${donors.length} donors, ${remainingInGroup} will remain:`);
    donors.forEach(donor => {
      console.log(`  🗑️  Removing donor kpfaId: ${donor.kpfaId} (id: ${donor.id})`);
    });
    
    if (remainingInGroup <= 1) {
      console.log(`  ⚠️  Group will become empty/single-donor and be deleted later`);
    } else {
      console.log(`  ✅ Group will continue with ${remainingInGroup} donors`);
    }
  }

  // Log orphan donors (not in any group)
  if (orphanDonors.length > 0) {
    console.log(`\n🔗 Orphan DuplicateDonors (not in any group) - ${orphanDonors.length} to delete:`);
    orphanDonors.forEach(donor => {
      console.log(`  🗑️  Removing orphan donor kpfaId: ${donor.kpfaId} (id: ${donor.id})`);
    });
  }

  // Delete in batches to avoid overwhelming the database
  const batchSize = 100;
  let totalDeleted = 0;
  
  for (let i = 0; i < kpfaIds.length; i += batchSize) {
    const batch = kpfaIds.slice(i, i + batchSize);
    
    try {
      const result = await prisma.duplicateDonor.deleteMany({
        where: {
          kpfaId: {
            in: batch
          }
        }
      });
      
      totalDeleted += result.count;
      console.log(`✅ Deleted batch ${Math.floor(i/batchSize) + 1}: ${result.count} records`);
    } catch (error) {
      console.error(`❌ Error deleting batch ${Math.floor(i/batchSize) + 1}:`, error);
    }
  }
  
  console.log(`🎯 Total DuplicateDonors deleted: ${totalDeleted}`);
  return totalDeleted;
}

async function findAndDeleteEmptyDuplicateGroups(deletedKpfaIds?: number[]): Promise<number> {
  console.log(`🔍 Finding DuplicateGroups with 1 or fewer donors...`);
  
  // Find all groups with their associated duplicate donors
  const allGroups = await prisma.duplicateGroup.findMany({
    include: {
      duplicateDonors: true,
      _count: {
        select: {
          duplicateDonors: true
        }
      }
    }
  });
  
  let groupsToDelete;
  
  if (deletedKpfaIds && deletedKpfaIds.length > 0) {
    // In dry-run mode or after deletions, simulate what groups would be empty
    groupsToDelete = allGroups.filter(group => {
      // Count how many donors would remain after deletions
      const remainingDonors = group.duplicateDonors.filter(donor => 
        !deletedKpfaIds.includes(donor.kpfaId)
      );
      return remainingDonors.length <= 1;
    });
  } else {
    // Regular mode - find groups that are currently empty or have 1 donor
    groupsToDelete = allGroups.filter(group => 
      group._count.duplicateDonors <= 1
    );
  }
  
  console.log(`🎯 Found ${groupsToDelete.length} groups to delete:`);
  groupsToDelete.forEach(group => {
    const currentCount = group._count.duplicateDonors;
    if (deletedKpfaIds && deletedKpfaIds.length > 0) {
      const remainingCount = group.duplicateDonors.filter(donor => 
        !deletedKpfaIds.includes(donor.kpfaId)
      ).length;
      console.log(`  - Group "${group.name}" (${group.status}) currently has ${currentCount} donors, would have ${remainingCount} after cleanup`);
    } else {
      console.log(`  - Group "${group.name}" (${group.status}) with ${currentCount} donors`);
    }
  });
  
  if (groupsToDelete.length === 0) {
    console.log(`✅ No empty/single-donor groups found to delete`);
    return 0;
  }
  
  // Delete the groups
  let totalDeleted = 0;
  for (const group of groupsToDelete) {
    try {
      await prisma.duplicateGroup.delete({
        where: { id: group.id }
      });
      totalDeleted++;
      console.log(`✅ Deleted group: ${group.name}`);
    } catch (error) {
      console.error(`❌ Error deleting group ${group.name}:`, error);
    }
  }
  
  console.log(`🎯 Total DuplicateGroups deleted: ${totalDeleted}`);
  return totalDeleted;
}

async function main() {
  try {
    console.log(`🚀 Starting cleanup of deleted orphan donors...`);
    
    const csvPath = path.join(__dirname, '../deleted_orphans.csv');
    
    if (!fs.existsSync(csvPath)) {
      throw new Error(`CSV file not found at: ${csvPath}`);
    }
    
    // Step 1: Read the CSV file
    const deletedOrphans = await readCSVFile(csvPath);
    
    if (deletedOrphans.length === 0) {
      console.log(`⚠️  No valid orphan records found in CSV file`);
      return;
    }
    
    // Convert IDs to numbers for database query
    const kpfaIds = deletedOrphans
      .map(orphan => parseInt(orphan.id))
      .filter(id => !isNaN(id));
    
    console.log(`📊 Processing ${kpfaIds.length} unique kpfaIds from CSV`);
    
    // Step 1.5: Check how many of these kpfaIds actually exist in our current database
    console.log(`🔍 Checking which kpfaIds actually exist in current database...`);
    const existingDonors = await prisma.duplicateDonor.findMany({
      where: {
        kpfaId: {
          in: kpfaIds
        }
      },
      select: {
        id: true,
        kpfaId: true
      }
    });
    
    const existingKpfaIds = existingDonors.map(donor => donor.kpfaId);
    console.log(`📋 Found ${existingDonors.length} DuplicateDonors in current database (out of ${kpfaIds.length} from CSV)`);
    
    // Let's also check the total count of DuplicateDonors and DuplicateGroups in the database
    const totalDonors = await prisma.duplicateDonor.count();
    const totalGroups = await prisma.duplicateGroup.count();
    console.log(`📊 Current database totals: ${totalDonors} DuplicateDonors, ${totalGroups} DuplicateGroups`);
    
    if (existingDonors.length === 0) {
      console.log(`⚠️  No matching records found in current database. Nothing to delete.`);
      return;
    }
    
    console.log(`📝 Sample of existing kpfaIds to be deleted: [${existingKpfaIds.slice(0, 10).join(', ')}]${existingKpfaIds.length > 10 ? '...' : ''}`);
    console.log(`📝 kpfaId range: ${Math.min(...existingKpfaIds)} to ${Math.max(...existingKpfaIds)}`);
    
    // Let's also check how many unique kpfaIds we have in existingKpfaIds
    const uniqueExistingKpfaIds = [...new Set(existingKpfaIds)];
    console.log(`📝 Unique kpfaIds to delete: ${uniqueExistingKpfaIds.length} (total records: ${existingKpfaIds.length})`);
    
    if (uniqueExistingKpfaIds.length !== existingKpfaIds.length) {
      console.log(`⚠️  Some kpfaIds appear multiple times in the database!`);
    }
    
    // Step 2: Delete DuplicateDonors with matching kpfaIds (only the ones that exist)
    const deletedDonors = await deleteDuplicateDonorsByKpfaIds(existingKpfaIds);
    
    // Step 3: Clean up empty DuplicateGroups
    // Pass the existing kpfaIds so the function can simulate what would happen after deletion
    const deletedGroups = await findAndDeleteEmptyDuplicateGroups(existingKpfaIds);
    
    console.log(`🎉 Cleanup completed successfully!`);
    console.log(`📈 Summary:`);
    console.log(`   - DuplicateDonors deleted: ${deletedDonors}`);
    console.log(`   - DuplicateGroups deleted: ${deletedGroups}`);
    
  } catch (error) {
    console.error(`💥 Script failed:`, error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Add dry-run mode
const isDryRun = process.argv.includes('--dry-run');

if (isDryRun) {
  console.log(`🔍 DRY RUN MODE - No actual deletions will be performed`);
  
  // Override deletion functions for dry run
  const originalDeleteMany = prisma.duplicateDonor.deleteMany;
  const originalDeleteGroup = prisma.duplicateGroup.delete;
  
  // @ts-ignore
  prisma.duplicateDonor.deleteMany = async (args: any) => {
    console.log(`[DRY RUN] Would delete DuplicateDonors with kpfaIds:`, args.where.kpfaId.in);
    return { count: args.where.kpfaId.in.length };
  };
  
  // @ts-ignore
  prisma.duplicateGroup.delete = async (args: any) => {
    console.log(`[DRY RUN] Would delete DuplicateGroup with id:`, args.where.id);
    return { id: args.where.id };
  };
}

main(); 