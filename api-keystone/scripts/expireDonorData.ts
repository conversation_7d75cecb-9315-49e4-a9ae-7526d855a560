#!/usr/bin/env tsx

import dotenv from 'dotenv';
import { getContext } from '@keystone-6/core/context';
import config from '../keystone';
import * as PrismaModule from '.prisma/client';

// Load environment variables
dotenv.config();

// 18 months in milliseconds
const EXPIRATION_PERIOD = 18 * 30 * 24 * 60 * 60 * 1000; // 18 months

interface ExpiredDonorSummary {
  totalExpired: number;
  totalDeleted: number;
  groupsProcessed: number;
  groupsWithHistoricalData: number;
  errors: string[];
}

interface DonorData {
  id: string;
  kpfaId: number;
  firstname: string;
  lastname: string;
  phone: string;
  email: string;
  address1: string;
  city: string;
  state: string;
  postal_code: string;
}

/**
 * Data Retention Script for Database Cleanup
 * 
 * Deletes duplicate donor records from merged groups after 18 months
 * while preserving primary donor records (which are active in main database)
 * and historical merge statistics.
 */
async function expireDonorData(): Promise<ExpiredDonorSummary> {
  const context = getContext(config, PrismaModule);
  const summary: ExpiredDonorSummary = {
    totalExpired: 0,
    totalDeleted: 0,
    groupsProcessed: 0,
    groupsWithHistoricalData: 0,
    errors: []
  };

  const expirationDate = new Date(Date.now() - EXPIRATION_PERIOD);
  
  console.log(`🗂️  Starting duplicate donor data cleanup`);
  console.log(`📅 Cleaning data older than: ${expirationDate.toISOString()}`);
  console.log(`⏰ 18 months cutoff period applied`);
  console.log(`🔒 Primary donors will be preserved (active in main database)`);

  try {
    // Find merged groups older than 18 months
    const expiredGroups = await context.query.DuplicateGroup.findMany({
      where: {
        status: { equals: 'MERGED' },
        mergedAt: { lt: expirationDate }
      },
      query: `
        id
        name
        mergedAt
        mergedBy
        mergedDonorsData
        duplicateDonors {
          id
          kpfaId
          firstname
          lastname
          phone
          email
          address1
          city
          state
          postal_code
        }
        primaryDonor {
          id
          kpfaId
          firstname
          lastname
        }
      `
    });

    console.log(`🔍 Found ${expiredGroups.length} expired merged groups`);

    for (const group of expiredGroups) {
      try {
        console.log(`\n📋 Processing group: ${group.name} (merged ${group.mergedAt})`);

        // Create simplified historical summary for essential records only
        const historicalSummary = {
          mergedDate: group.mergedAt,
          mergedBy: group.mergedBy,
          primaryDonorKpfaId: group.primaryDonor?.kpfaId,
        };

        // Count donors to be deleted (excluding primary donor)
        const nonPrimaryDonors = group.duplicateDonors.filter((donor: DonorData) => 
          !group.primaryDonor || donor.id !== group.primaryDonor.id
        );
        const donorsToDelete = nonPrimaryDonors.length;
        let deletedCount = 0;

        console.log(`  📊 Total duplicates: ${group.duplicateDonors.length}, Primary: ${group.primaryDonor ? 1 : 0}, To delete: ${donorsToDelete}`);

        // Delete only non-primary duplicate donors
        for (const donor of nonPrimaryDonors) {
          try {
            await context.query.DuplicateDonor.deleteOne({
              where: { id: donor.id }
            });
            deletedCount++;
            console.log(`  ✅ Deleted duplicate donor ${donor.kpfaId}`);
          } catch (donorError) {
            const errorMsg = `Failed to delete donor ${donor.kpfaId}: ${donorError}`;
            console.error(`  ❌ ${errorMsg}`);
            summary.errors.push(errorMsg);
          }
        }

        // Log primary donor preservation
        if (group.primaryDonor) {
          const primaryInDuplicates = group.duplicateDonors.find((d: DonorData) => d.id === group.primaryDonor.id);
          if (primaryInDuplicates) {
            console.log(`  🔒 Preserved primary donor ${group.primaryDonor.kpfaId} (active in main database)`);
          }
        }

        // Update the group with essential merge metadata
        await context.query.DuplicateGroup.updateOne({
          where: { id: group.id },
          data: {
            // Keep original name in production, just add essential merge metadata
            mergedDonorsData: historicalSummary,
            // Note: We keep the group record for audit purposes but remove PII
          }
        });

        summary.totalExpired += donorsToDelete;
        summary.totalDeleted += deletedCount;
        summary.groupsProcessed++;
        summary.groupsWithHistoricalData++;

        console.log(`  📊 Summary: ${deletedCount}/${donorsToDelete} donors deleted`);
        console.log(`  📋 Essential merge metadata preserved`);

      } catch (groupError) {
        const errorMsg = `Failed to process group ${group.id}: ${groupError}`;
        console.error(`❌ ${errorMsg}`);
        summary.errors.push(errorMsg);
      }
    }

    console.log(`✅ Deleted ${summary.totalDeleted} old duplicate donors`);

  } catch (error) {
    const errorMsg = `Critical error in expiration process: ${error}`;
    console.error(`🚨 ${errorMsg}`);
    summary.errors.push(errorMsg);
  }

  return summary;
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting KPFA Donor Data Expiration (18-month GDPR Cleanup)');
    console.log('='.repeat(60));

    const summary = await expireDonorData();

    console.log('\n' + '='.repeat(60));
    console.log('📈 EXPIRATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`📊 Groups processed: ${summary.groupsProcessed}`);
    console.log(`👥 Total donors expired: ${summary.totalExpired}`);
    console.log(`🗑️  Total donors deleted: ${summary.totalDeleted}`);
    console.log(`📋 Groups with essential metadata: ${summary.groupsWithHistoricalData}`);
    
    if (summary.errors.length > 0) {
      console.log(`❌ Errors encountered: ${summary.errors.length}`);
      summary.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    } else {
      console.log('✅ No errors encountered');
    }

    console.log('\n🎉 Data expiration cleanup completed');
    
    if (summary.totalDeleted > 0) {
      console.log('\n📝 Cleanup Note: Duplicate donor data has been cleaned up after');
      console.log('   the 18-month retention period. Primary donors and essential');
      console.log('   merge metadata have been preserved for business continuity.');
    }

  } catch (error) {
    console.error('🚨 Fatal error:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n👋 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('🚨 Script failed:', error);
      process.exit(1);
    });
}

export { expireDonorData, main };