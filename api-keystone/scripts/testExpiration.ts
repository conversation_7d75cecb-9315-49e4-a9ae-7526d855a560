#!/usr/bin/env tsx

import dotenv from 'dotenv';
import { getContext } from '@keystone-6/core/context';
import config from '../keystone';
import * as PrismaModule from '.prisma/client';

// Load environment variables
dotenv.config();

/**
 * Test Script for Donor Data Expiration
 * 
 * This script helps test the expiration system by:
 * 1. Finding existing merged groups
 * 2. Temporarily aging them for testing
 * 3. Running expiration
 * 4. Restoring original dates
 */

interface TestGroup {
  id: string;
  name: string;
  originalMergedAt: string;
  donorCount: number;
}

async function testExpiration(testDurationMinutes: number = 1, destructive: boolean = false): Promise<void> {
  const context = getContext(config, PrismaModule);
  
  console.log('🧪 Starting Donor Data Expiration Test');
  console.log(`⏱️  Test duration: ${testDurationMinutes} minute(s)`);
  console.log(`🚨 Destructive mode: ${destructive ? 'ENABLED (no restore)' : 'DISABLED (will restore)'}`);
  console.log('=' .repeat(60));

  const testGroups: TestGroup[] = [];
  let restoredCount = 0;

  try {
    // 1. Find existing merged groups to test with
    const existingGroups = await context.query.DuplicateGroup.findMany({
      where: {
        status: { equals: 'MERGED' },
        // Find groups that have donor records
        duplicateDonors: { some: {} }
      },
      query: `
        id
        name
        mergedAt
        duplicateDonors {
          id
          kpfaId
          firstname
          lastname
          email
        }
      `,
      take: 3 // Test with just a few groups
    });

    if (existingGroups.length === 0) {
      console.log('❌ No merged groups with donor records found for testing');
      console.log('   Create some test merged groups first');
      return;
    }

    console.log(`📋 Found ${existingGroups.length} merged groups for testing:`);
    existingGroups.forEach((group, index) => {
      console.log(`   ${index + 1}. ${group.name} (${group.duplicateDonors.length} donors)`);
      group.duplicateDonors.forEach((donor: any) => {
        console.log(`      - Donor ${donor.kpfaId}: ${donor.firstname} ${donor.lastname} (${donor.email || 'no email'})`);
      });
    });

    console.log(`\n📝 These specific donors will be processed:`);
    const allDonors = existingGroups.flatMap(group => group.duplicateDonors);
    allDonors.forEach((donor: any) => {
      console.log(`   🎯 KPFA ID ${donor.kpfaId}: ${donor.firstname} ${donor.lastname}`);
    });

    // 2. Age the groups temporarily for testing
    const testDate = new Date(Date.now() - (testDurationMinutes * 60 * 1000)); // X minutes ago
    console.log(`\n⏰ Temporarily aging groups to: ${testDate.toISOString()}`);

    for (const group of existingGroups) {
      // Store original data
      testGroups.push({
        id: group.id,
        name: group.name,
        originalMergedAt: group.mergedAt,
        donorCount: group.duplicateDonors.length
      });

      // Temporarily age the group
      await context.query.DuplicateGroup.updateOne({
        where: { id: group.id },
        data: {
          mergedAt: testDate.toISOString(),
          name: `[TEST] ${group.name}`
        }
      });

      console.log(`   ✅ Aged group: ${group.name}`);
    }

    // 3. Import and run the expiration function
    console.log('\n🔄 Running expiration test...');
    const { expireDonorData } = await import('./expireDonorData');
    const summary = await expireDonorData();

    // 4. Display results
    console.log('\n📊 TEST RESULTS:');
    console.log('=' .repeat(60));
    console.log(`Groups processed: ${summary.groupsProcessed}`);
    console.log(`Donors expired: ${summary.totalExpired}`);
    console.log(`Donors deleted: ${summary.totalDeleted}`);
    console.log(`Groups with historical data: ${summary.groupsWithHistoricalData}`);
    
    if (summary.errors.length > 0) {
      console.log(`Errors: ${summary.errors.length}`);
      summary.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    // 5. Verify the changes
    console.log('\n🔍 Verifying changes...');
    for (const testGroup of testGroups) {
      const updatedGroup = await context.query.DuplicateGroup.findOne({
        where: { id: testGroup.id },
        query: `
          id
          name
          mergedDonorsData
          duplicateDonors {
            id
          }
        `
      });

      if (updatedGroup) {
        const remainingDonors = updatedGroup.duplicateDonors.length;
        const deletedDonors = testGroup.donorCount - remainingDonors;
        
        console.log(`   📋 ${testGroup.name}:`);
        console.log(`      - Original donors: ${testGroup.donorCount}`);
        console.log(`      - Remaining donors: ${remainingDonors}`);
        console.log(`      - Deleted donors: ${deletedDonors}`);
        console.log(`      - Name changed: ${updatedGroup.name}`);
        console.log(`      - Historical data: ${updatedGroup.mergedDonorsData ? 'Yes' : 'No'}`);
      }
    }

    if (destructive) {
      console.log('\n🚨 DESTRUCTIVE MODE: Data will NOT be restored!');
      console.log('📝 To verify in UI, check these groups:');
      testGroups.forEach((group, index) => {
        console.log(`   ${index + 1}. Search for group: "${group.name.replace('[TEST] ', '').replace('[EXPIRED] [TEST] ', '')}"`);
      });
      console.log('\n💡 Expected UI behavior:');
      console.log('   - Groups should show as [EXPIRED] or [TEST]');
      console.log('   - Duplicate donor records should be missing');
      console.log('   - Essential merge metadata should be preserved in mergedDonorsData');
      console.log('   - UI should handle missing donors gracefully');
      return; // Exit early, don't restore
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    if (!destructive) {
      // 6. Restore original data (cleanup) - only if not destructive
      console.log('\n🔄 Restoring original group data...');
      
      for (const testGroup of testGroups) {
        try {
          // Check if group still exists and restore it
          const currentGroup = await context.query.DuplicateGroup.findOne({
            where: { id: testGroup.id },
            query: 'id name'
          });

          if (currentGroup) {
            await context.query.DuplicateGroup.updateOne({
              where: { id: testGroup.id },
              data: {
                mergedAt: testGroup.originalMergedAt,
                name: testGroup.name.replace('[TEST] ', '').replace('[EXPIRED] [TEST] ', '')
              }
            });
            
            restoredCount++;
            console.log(`   ✅ Restored: ${testGroup.name}`);
          } else {
            console.log(`   ⚠️  Group no longer exists: ${testGroup.name}`);
          }
        } catch (restoreError) {
          console.error(`   ❌ Failed to restore ${testGroup.name}:`, restoreError);
        }
      }

      console.log(`\n✅ Restored ${restoredCount}/${testGroups.length} groups`);
      console.log('\n🎉 Test completed!');
      
      if (restoredCount < testGroups.length) {
        console.log('\n⚠️  WARNING: Some groups could not be restored.');
        console.log('   You may need to manually check the database.');
      }
    } else {
      console.log('\n🚨 DESTRUCTIVE TEST COMPLETED - NO RESTORATION PERFORMED');
      console.log('💾 Remember to restore your database backup when done testing!');
    }
  }
}

/**
 * Test the scheduler directly with a short timeline
 */
async function testScheduler(cronExpression: string = '*/1 * * * *'): Promise<void> {
  console.log('🕐 Testing Scheduler with custom cron...');
  console.log(`📅 Cron expression: ${cronExpression} (every minute)`);
  
  // Import scheduler functions
  const { startScheduler, getScheduler, stopScheduler } = await import('../src/scheduler');
  
  // Start scheduler with test configuration
  startScheduler({
    donorExpirationCron: cronExpression,
    enableLogging: true,
    maxRetries: 1 // Reduce retries for faster testing
  });

  console.log('⏱️  Scheduler started - waiting for execution...');
  console.log('   Press Ctrl+C to stop the test');

  // Keep the process alive
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping test scheduler...');
    stopScheduler();
    process.exit(0);
  });
}

/**
 * Main execution
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'expiration';

  try {
    switch (command) {
      case 'expiration':
        const minutes = parseInt(args[1]) || 1;
        const destructive = args[2] === 'destructive';
        await testExpiration(minutes, destructive);
        break;
        
      case 'scheduler':
        const cron = args[1] || '*/1 * * * *';
        await testScheduler(cron);
        break;
        
      default:
        console.log('Usage:');
        console.log('  tsx scripts/testExpiration.ts expiration [minutes] [destructive]');
        console.log('  tsx scripts/testExpiration.ts scheduler [cron]');
        console.log('');
        console.log('Examples:');
        console.log('  tsx scripts/testExpiration.ts expiration 1     # Test with 1-minute age (safe)');
        console.log('  tsx scripts/testExpiration.ts expiration 800000 destructive  # Test with 19+ months (destructive)');
        console.log('  tsx scripts/testExpiration.ts scheduler "*/1 * * * *"  # Test scheduler every minute');
        break;
    }
  } catch (error) {
    console.error('🚨 Test script failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  main()
    .then(() => {
      if (process.argv[2] !== 'scheduler') {
        console.log('\n👋 Test script completed');
        process.exit(0);
      }
    })
    .catch((error) => {
      console.error('🚨 Test script failed:', error);
      process.exit(1);
    });
}

export { testExpiration, testScheduler }; 