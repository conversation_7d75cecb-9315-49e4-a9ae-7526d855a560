/**
 * Phone number normalization and matching utilities
 * Handles US phone numbers, removes formatting, and accounts for country codes
 */

export interface PhoneMatchResult {
  isMatch: boolean;
  confidence: number; // 0-100
  normalizedPhone1: string;
  normalizedPhone2: string;
  matchType: 'exact' | 'without_country_code' | 'partial' | 'none';
}

/**
 * Normalize a phone number for comparison
 * - Removes all non-numeric characters
 * - Handles US country code (+1)
 * - Returns standardized format for comparison
 */
export function normalizePhoneNumber(phone: string | null | undefined): string | null {
  if (!phone || typeof phone !== 'string') {
    return null;
  }

  // Remove all non-numeric characters
  const digitsOnly = phone.replace(/\D/g, '');
  
  if (digitsOnly.length === 0) {
    return null;
  }

  // Handle US numbers with country code
  if (digitsOnly.length === 11 && digitsOnly.startsWith('1')) {
    // Remove the '1' prefix for US numbers
    return digitsOnly.substring(1);
  }

  // Handle standard 10-digit US numbers
  if (digitsOnly.length === 10) {
    return digitsOnly;
  }

  // For international numbers or other formats, return as-is
  // but only if they're reasonable length (7-15 digits)
  if (digitsOnly.length >= 7 && digitsOnly.length <= 15) {
    return digitsOnly;
  }

  // Invalid phone number
  return null;
}

/**
 * Compare two phone numbers and determine if they match
 * Returns detailed matching information
 */
export function comparePhoneNumbers(phone1: string | null | undefined, phone2: string | null | undefined): PhoneMatchResult {
  const normalized1 = normalizePhoneNumber(phone1);
  const normalized2 = normalizePhoneNumber(phone2);

  const result: PhoneMatchResult = {
    isMatch: false,
    confidence: 0,
    normalizedPhone1: normalized1 || '',
    normalizedPhone2: normalized2 || '',
    matchType: 'none'
  };

  // If either phone is null/invalid, no match
  if (!normalized1 || !normalized2) {
    return result;
  }

  // Exact match
  if (normalized1 === normalized2) {
    result.isMatch = true;
    result.confidence = 100;
    result.matchType = 'exact';
    return result;
  }

  // Check if one is a subset of the other (for partial matches)
  const longer = normalized1.length > normalized2.length ? normalized1 : normalized2;
  const shorter = normalized1.length > normalized2.length ? normalized2 : normalized1;

  // If the shorter number is the last N digits of the longer number
  if (longer.endsWith(shorter) && shorter.length >= 7) {
    result.isMatch = true;
    result.confidence = 85;
    result.matchType = 'without_country_code';
    return result;
  }

  // Check for partial matches (last 7 digits for US numbers)
  if (normalized1.length >= 7 && normalized2.length >= 7) {
    const last7_1 = normalized1.slice(-7);
    const last7_2 = normalized2.slice(-7);
    
    if (last7_1 === last7_2) {
      result.isMatch = true;
      result.confidence = 75;
      result.matchType = 'partial';
      return result;
    }
  }

  return result;
}

/**
 * Check if a phone number is likely a US number
 */
export function isUSPhoneNumber(phone: string | null | undefined): boolean {
  const normalized = normalizePhoneNumber(phone);
  if (!normalized) return false;
  
  // US numbers are typically 10 digits
  return normalized.length === 10;
}

/**
 * Format a phone number for display
 */
export function formatPhoneForDisplay(phone: string | null | undefined): string {
  const normalized = normalizePhoneNumber(phone);
  if (!normalized) return phone || '';

  // Format US numbers as (XXX) XXX-XXXX
  if (normalized.length === 10) {
    return `(${normalized.slice(0, 3)}) ${normalized.slice(3, 6)}-${normalized.slice(6)}`;
  }

  // For other formats, just return the normalized version
  return normalized;
}

/**
 * Validate if a phone number is reasonable for duplicate detection
 */
export function isValidForDuplicateDetection(phone: string | null | undefined): boolean {
  const normalized = normalizePhoneNumber(phone);
  if (!normalized) return false;

  // Must be at least 7 digits
  if (normalized.length < 7) return false;

  // Check for obviously fake numbers
  const fakePatterns = [
    /^1234567/, // 1234567...
    /^0000000/, // 0000000...
    /^1111111/, // 1111111...
    /^2222222/, // etc.
    /^3333333/,
    /^4444444/,
    /^5555555/,
    /^6666666/,
    /^7777777/,
    /^8888888/,
    /^9999999/,
    /^5551212/, // Classic fake number
    /^8675309/, // Jenny's number
  ];

  return !fakePatterns.some(pattern => pattern.test(normalized));
} 