<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Check for valid HTTP method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(["message" => "Method not allowed. Use GET request."]);
    exit;
}

try {
    // Include necessary files
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../objects/database.php';
    require_once __DIR__ . '/../objects/account.php';
    require_once __DIR__ . '/../src/Service/DonorService.php';
    require_once __DIR__ . '/../objects/user.php';

    // Instantiate database and donor objects
    $database = new Database();
    $db = $database->getConnection();
    $donor = new Donor($db);
    $user = new User($db);

    // Create DonorService instance
    $donorService = new Kpfa\Service\DonorService($db, $donor);

    // Authentication check
    $jwt = null;
    $data = null;

    // Check for authorization header
    $headers = apache_request_headers();
    if (isset($headers['Authorization'])) {
        $authHeader = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $jwt = $matches[1];
        }
    }

    if (!$jwt) {
        http_response_code(401);
        echo json_encode(["message" => "Access denied. Authorization token required."]);
        exit;
    }

    // Validate the JWT and get user data
    try {
        $data = $user->validateJWT($jwt);
        if (!$data) {
            http_response_code(401);
            echo json_encode(["message" => "Access denied. Invalid token."]);
            exit;
        }
    } catch (Exception $e) {
        http_response_code(401);
        echo json_encode(["message" => "Access denied. Token validation failed: " . $e->getMessage()]);
        exit;
    }

    // Check if user has appropriate access level (Admin or Staff)
    $accessLevel = $data->data->access_level ?? '';
    if (!in_array($accessLevel, ['Admin', 'Staff'])) {
        http_response_code(403);
        echo json_encode(["message" => "Access denied. Insufficient permissions."]);
        exit;
    }

    // Get and validate query parameters
    $phoneNumber = $_GET['phone'] ?? '';
    $excludeDonorId = isset($_GET['exclude_id']) ? (int) $_GET['exclude_id'] : null;
    $limit = isset($_GET['limit']) ? (int) $_GET['limit'] : 10;

    // Validate phone parameter
    if (empty($phoneNumber)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Phone number parameter is required",
            "error" => [
                "code" => "MISSING_PARAMETER",
                "details" => "Please provide a 'phone' query parameter"
            ]
        ]);
        exit;
    }

    // Validate limit parameter
    if ($limit < 1 || $limit > 100) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Limit must be between 1 and 100",
            "error" => [
                "code" => "INVALID_PARAMETER",
                "details" => "Limit parameter must be between 1 and 100"
            ]
        ]);
        exit;
    }

    // Call the DonorService method to find potential duplicates
    $result = $donorService->findPotentialDuplicatesByPhone($phoneNumber, $excludeDonorId, $limit);

    // Log the search for debugging/auditing
    error_log("Duplicate search performed - Phone: {$phoneNumber}, User: {$data->data->email}, Results: {$result['total_found']}");

    // Return the results
    http_response_code(200);
    echo json_encode($result);

} catch (InvalidArgumentException $e) {
    // Handle validation errors
    http_response_code(400);
    echo json_encode([
        "success" => false,
        "message" => "Invalid input parameters",
        "error" => [
            "code" => "VALIDATION_ERROR",
            "details" => $e->getMessage()
        ]
    ]);
} catch (Exception $e) {
    // Handle general errors
    error_log("Error in duplicate search endpoint: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "An error occurred while searching for duplicates",
        "error" => [
            "code" => "SERVER_ERROR",
            "details" => $e->getMessage()
        ]
    ]);
}
?> 