<?php
header("Content-Type: text/html; charset=utf-8");

// Extract the token from the query parameters
$token = filter_input(INPUT_GET, "token", FILTER_DEFAULT);
$token = $token !== null ? (string) $token : null;

// Start HTML output
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Opt-in Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            text-align: center;
        }
        .message {
            padding: 20px;
            margin: 20px auto;
            border-radius: 5px;
            max-width: 600px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .error {
            color: #D8000C;
            background-color: #FFD2D2;
        }
        .success {
            color: #4F8A10;
            background-color: #DFF2BF;
        }
        .info {
            color: #00529B;
            background-color: #BDE5F8;
        }
    </style>
</head>
<body>';

if (!$token) {
    http_response_code(400); // Bad Request
    echo '<div class="message error">Missing token. Please check the link and try again.</div>';
    exit();
}

try {
    // Database query and update logic...
    // Prepare a statement to select the donor based on the token
    $query = "SELECT id FROM donors WHERE paperless_token = :token LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(":token", $token, PDO::PARAM_STR);
    $stmt->execute();

    $donor = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$donor) {
        http_response_code(404); // Not Found
        echo '<div class="message error">Donor not found or invalid token.</div>';
        exit();
    }

    // Prepare a statement to update the `paperless` attribute in the donors table
    $updateQuery = "UPDATE donors SET paperless = TRUE WHERE id = :donorId";
    $updateStmt = $db->prepare($updateQuery);
    $updateStmt->bindParam(":donorId", $donor["id"], PDO::PARAM_INT);
    $updateStmt->execute();

    if ($updateStmt->rowCount() > 0) {
        echo '<div class="message success">You have successfully opted in for paperless communications. Thank you!</div>';
    } else {
        echo '<div class="message info">No update needed. You are already opted in for paperless communications or the donor was not found.</div>';
    }
} catch (Exception $e) {
    http_response_code(500); // Internal Server Error
    echo '<div class="message error">An error occurred: ' .
        htmlspecialchars($e->getMessage()) .
        "</div>";
}

echo "</body></html>";
