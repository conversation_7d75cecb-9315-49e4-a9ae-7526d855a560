<?php

function processDonationPayments(
    PDOStatement $stmt3,
    float $donation_amount,
): array {
    $totalPaidAmount = 0.0;
    $totalRefundedAmount = 0.0;
    $payments = []; // Array to hold detailed payment information

    while ($row3 = $stmt3->fetch(PDO::FETCH_ASSOC)) {
        $paymentAmount = (float) $row3["payment_amount"] ?? 0.0;
        $amountRefunded = (float) $row3["amount_refunded"] ?? 0.0;

        if ($row3["status"] === "succeeded") {
            $totalPaidAmount += $paymentAmount;
            $totalRefundedAmount += $amountRefunded;
        }

        // Create detailed payment record and add to payments array
        $payment = [
            "id" => filter_var(
                $row3["id"],
                FILTER_VALIDATE_INT,
                FILTER_NULL_ON_FAILURE,
            ),
            "customer_id" => $row3["customer_id"],
            "payment_id" => $row3["payment_id"],
            "processor" => $row3["processor"],
            "method" => $row3["method"],
            "card_type" => $row3["card_type"],
            "amount" => $paymentAmount,
            "amount_refunded" => $amountRefunded,
            "status" => $row3["status"],
            "transaction_status" => $row3["status"], // Assuming direct reflection of 'status'
            "last4" => $row3["last4"] ?? null,
            "exp_month" => $row3["exp_month"] ?? null,
            "exp_year" => $row3["exp_year"] ?? null,
            "last_updated_by" => $row3["last_updated_by"],
            "date_created" => $row3["date_created"],
            "date_updated" => $row3["date_updated"],
            "date_deposited" => $row3["date_deposited"],
        ];
        array_push($payments, $payment);
    }

    $netAmount = $totalPaidAmount - $totalRefundedAmount;

    // Logic to determine donation status based on net amount
    $donation_status = determineDonationStatus(
        $donation_amount,
        $netAmount,
        $totalRefundedAmount,
    );

    return [
        "donation_status" => $donation_status,
        "payments" => $payments,
    ];
}

function determineDonationStatus(
    $donation_amount,
    $netAmount,
    $totalRefundedAmount,
) {
    if ($totalRefundedAmount > 0 && $netAmount <= 0) {
        return "Refunded";
    } elseif ($netAmount >= $donation_amount) {
        return "Paid";
    } elseif ($netAmount > 0 && $netAmount < $donation_amount) {
        return "Partially Paid";
    }
    return "Unpaid";
}

// [ACCOUNTS]
try {
    // ACL
    $access_levels = ["Admin", "CallCenter"];
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }
    // format function
    $filesize = 0;
    $filelength = 0;
    function filesize_formatted($path) {
        $size = filesize($path);
        $units = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
        $power = $size > 0 ? floor(log($size, 1024)) : 0;
        return number_format($size / pow(1024, $power), 2, ".", ",") .
            " " .
            $units[$power];
    }
    // resources
    $resources = ["donors", "foundations", "volunteers", "staff"];

    // check to see if resource contains sub resource
    if (strpos($resource, "/") !== false) {
        // explodable
        $piece = explode("/", $resource);
        $resource = $piece[0]; // reassign resource
        $entity = $piece[1]; // entity
    } else {
        // not explodable
    }

    // resource check
    if (!in_array($resource, $resources)) {
        foreach ($resources as $resource) {
            $resource_array["resources"][] = [
                "links" => [
                    "rel" => "self",
                    "href" => "$api_url/$collection/$resource/",
                ],
            ];
        }
        echo json_encode($resource_array);
        die();
    }

    // Accounts

    if ($resource == "foundations") {
        http_response_code(400); // bad request
        throw new Exception("feature coming soon!");
    }
    if ($resource == "volunteers") {
        http_response_code(400); // bad request
        throw new Exception("feature coming soon!");
    }
    if ($resource == "staff") {
        http_response_code(400); // bad request
        throw new Exception("feature coming soon!");
    }
    if ($resource == "donors") {
        // READ donor
        if (!isset($_GET["s"]) && isset($entity)) {
            // query donor
            $stmt = $donor->read($entity);
            $num = $stmt->rowCount();

            // check if more than 0 record found
            if ($num > 0) {
                // donor array
                $donors = [];
                $donors["records"] = [];

                // retrieve our table contents

                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    // sql was executed in object
                    extract($row);
                    // reset donations array
                    $donations = [];
                    //Donations
                    // read pledge table for each donor to find a list of matching pledge ids
                    $stmt2 = $donor->readDonations($donor_id);
                    // number of rows
                    $num2 = $stmt2->rowCount();
                    // check if more than 0 records found
                    if ($num2 > 0) {
                        // retrieve our table contents
                        while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                            extract($row2); //readDonations
                            // reset subscriptions array
                            $subscriptions = [];
                            //Subscriptions
                            // read subscription table for each donor id
                            $stmt1 = $donor->readSubscriptionsByTransaction_id(
                                $transaction_id,
                            );
                            // number of rows
                            $num1 = $stmt1->rowCount();
                            // check if more than 0 records found
                            if ($num1 > 0) {
                                // retrieve our table contents
                                while (
                                    $row1 = $stmt1->fetch(PDO::FETCH_ASSOC)
                                ) {
                                    extract($row1);
                                    // create array
                                    $subscription = [
                                        "id" => $subscription_id,
                                        "processor" => $processor,
                                        "plan_id" => $plan_id,
                                        "amount" => floatval(
                                            $subscription_amount,
                                        ),
                                        "interval" => $interval,
                                        "active" => filter_var(
                                            $active,
                                            FILTER_VALIDATE_BOOLEAN,
                                            FILTER_NULL_ON_FAILURE,
                                        ),
                                        "date_canceled" => $date_canceled,
                                        "date_created" => $date_created,
                                    ];
                                    array_push($subscriptions, $subscription); // add subscription item to subscriptions array
                                } // while
                            } // if

                            // Payments
                            // reset payments array
                            $payments = [];
                            // read payments table for each donation
                            $stmt3 = $donor->readPaymentbyDonationid(
                                $donation_id,
                            );

                            $result = processDonationPayments(
                                $stmt3,
                                $donation_amount,
                            );
                            $donation_status = $result["donation_status"];
                            $payments = $result["payments"]; // Detailed payment records, if needed

                            //Premiums
                            // reset premiums array
                            $premiums = [];
                            // read premiumGifts table for each transaction
                            $stmt4 = $donor->readGifts($donation_id);
                            // number of rows
                            $num4 = $stmt4->rowCount();
                            // check if more than 0 records found
                            if ($num4 > 0) {
                                // retrieve our table contents
                                while (
                                    $row4 = $stmt4->fetch(PDO::FETCH_ASSOC)
                                ) {
                                    extract($row4);
                                    // create array
                                    $premium = [
                                        "id" => intval($premium_id),
                                        "donation_id" => intval($donation_id),
                                        "name" => $name,
                                        "price" => floatval($price),
                                        "cog" => floatval($cog),
                                        "fmv" => floatval($fmv),
                                        "qty" => intval($qty),
                                        "download_url" => $download_url,
                                        "img_url" => $img_url,
                                        "category" => $category,
                                        "status" => $shipment_status,
                                    ];
                                    array_push($premiums, $premium); // add premium item to premiums array
                                } // while
                            } // if (readGifts)

                            // Recording
                            // the date
                            $date = date("Ymd", strtotime($timestamp));
                            $y = date("Y", strtotime($timestamp));
                            $m = date("m", strtotime($timestamp));
                            $d = date("d", strtotime($timestamp));
                            $filename = null;
                            $filesize = null;
                            $filelength = null;
                            $duration = null;
                            $length = null;
                            foreach (
                                glob(
                                    "/var/www/api.kpfa.org/htdocs/recordings/$y/$m/$d/in-8004395732-$phone-$date-*.wav",
                                )
                                as $filename
                            ) {
                                $dur = ltrim(
                                    shell_exec(
                                        "soxi -d " . $filename . " 2>&1",
                                    ),
                                    "0:",
                                );
                                $duration = substr($dur, 0, strpos($dur, "."));
                                $length = str_replace(":", "m", $duration);
                                $filesize = filesize_formatted($filename);
                                $filelength = "$length" . "s";
                                $filename = str_replace(
                                    "/var/www/api.kpfa.org/htdocs",
                                    "https://$api_url",
                                    $filename,
                                );
                            }
                            // create donation array
                            $donation = [
                                "id" => intval($donation_id),
                                "type" => $type,
                                "installment" => $installment,
                                "status" => $donation_status,
                                "amount" => floatval($donation_amount),
                                "transaction_id" => $transaction_id,
                                "timestamp" => $timestamp,
                                "comments" => $comments,
                                "add_me" => filter_var(
                                    $add_me,
                                    FILTER_VALIDATE_BOOLEAN,
                                    FILTER_NULL_ON_FAILURE,
                                ),
                                "read_onair" => filter_var(
                                    $read_onair,
                                    FILTER_VALIDATE_BOOLEAN,
                                    FILTER_NULL_ON_FAILURE,
                                ),
                                "donation_match" => filter_var(
                                    $donation_match,
                                    FILTER_VALIDATE_BOOLEAN,
                                    FILTER_NULL_ON_FAILURE,
                                ),
                                "show_name" => $show_name,
                                "source" => $source,
                                "ipaddress" => $ipaddress,
                                "browser" => $browser,
                                "campaign_id" => intval($campaign_id),
                                "campaign" => $campaign,
                                "updated" => $updated,
                                "recording_url" => $filename,
                                "recording_size" => $filesize,
                                "recording_length" => $filelength,
                                "shipping_firstname" => $shipping_firstname,
                                "shipping_lastname" => $shipping_lastname,
                                "shipping_address1" => $shipping_address1,
                                "shipping_address2" => $shipping_address2,
                                "shipping_city" => $shipping_city,
                                "shipping_state" => $shipping_state,
                                "shipping_country" => $shipping_country,
                                "shipping_postal_code" => $shipping_postal_code,
                                "payments" => $payments,
                                "premiums" => $premiums,
                                "subscriptions" => $subscriptions,
                            ];
                            array_push($donations, $donation); // add donation item to donations array
                        } // while (readDonations loop)
                    } // if (loop)

                    // set cards array
                    $cards = [];
                    // donor cards
                    if (!empty($stripe_cus_id)) {
                        $cards = $donor->readCardByCustomer_id($stripe_cus_id);
                    }
                    // Build the donor array
                    $donor_item = [
                        "id" => intval($donor_id),
                        "stripe_cus_id" => $stripe_cus_id,
                        "allegiance_id" => filter_var(
                            $allegiance_id,
                            FILTER_VALIDATE_INT,
                            FILTER_NULL_ON_FAILURE,
                        ),
                        "memsys_id" => filter_var(
                            $memsys_id,
                            FILTER_VALIDATE_INT,
                            FILTER_NULL_ON_FAILURE,
                        ),
                        "type" => $donor_type,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "partner_firstname" => $partner_firstname,
                        "partner_lastname" => $partner_lastname,
                        "phone" => $phone,
                        "email" => $email,
                        "address1" => $address1,
                        "address2" => $address2,
                        "city" => $city,
                        "state" => $state,
                        "country" => $country,
                        "postal_code" => $postal_code,
                        "phone" => $phone,
                        "email" => $email,
                        "notes" => $notes,
                        "donotsolicit" => (bool) $donotsolicit,
                        "deceased" => (bool) $deceased,
                        "donations" => $donations,
                        "cards" => $cards,
                        "date_created" => $date_created,
                        "date_updated" => $date_updated,
                    ];
                    // Push the donor item into the donor array
                    array_push($donors["records"], $donor_item);
                } // end while for $stmt

                echo json_encode($donors);
            } else {
                http_response_code(404); // not found
                throw new Exception("No donor found.");
            }
        }
        // READ donors
        if (!isset($_GET["s"]) && !isset($entity)) {
            if (isset($_GET["payment_start"])) {
                // check to see if payment_end is also included
                if (!isset($_GET["payment_end"])) {
                    http_response_code(400); // bad request
                    throw new Exception(
                        "[payment_end] is required when [payment_start] is specified.",
                    );
                }
                // Assign variables
                $payment_start = $_GET["payment_start"];
                $payment_end = $_GET["payment_end"];
            } else {
                http_response_code(400); // bad request
                throw new Exception(
                    "[payment_start] and [payment_end] are required.",
                );
            }
            // query donors
            $stmt = $donor->readDonorsBetweenPayments(
                $payment_start,
                $payment_end,
            );
            $num = $stmt->rowCount();

            // check if more than 0 record found
            if ($num > 0) {
                // donor array
                $donors = [];
                $donors["records"] = [];

                // retrieve our table contents

                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    // sql was executed in object
                    extract($row);
                    // reset donations array
                    $donations = [];
                    //Donations
                    // read pledge table for each donor to find a list of matching pledge ids
                    $stmt2 = $donor->readDonationsBetweenRange(
                        $donor_id,
                        $payment_start,
                        $payment_end,
                    );
                    // number of rows
                    $num2 = $stmt2->rowCount();
                    // check if more than 0 records found
                    if ($num2 > 0) {
                        // retrieve our table contents
                        while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                            extract($row2); //readDonations
                            // reset subscriptions array
                            $subscriptions = [];
                            //Subscriptions
                            // read subscription table for each donor id
                            $stmt1 = $donor->readSubscriptionsByTransaction_id(
                                $transaction_id,
                            );
                            // number of rows
                            $num1 = $stmt1->rowCount();
                            // check if more than 0 records found
                            if ($num1 > 0) {
                                // retrieve our table contents
                                while (
                                    $row1 = $stmt1->fetch(PDO::FETCH_ASSOC)
                                ) {
                                    extract($row1);
                                    // create array
                                    $subscription = [
                                        "id" => $subscription_id,
                                        "processor" => $processor,
                                        "plan_id" => $plan_id,
                                        "amount" => floatval(
                                            $subscription_amount,
                                        ),
                                        "interval" => $interval,
                                        "active" => filter_var(
                                            $active,
                                            FILTER_VALIDATE_BOOLEAN,
                                            FILTER_NULL_ON_FAILURE,
                                        ),
                                        "date_canceled" => $date_canceled,
                                        "date_created" => $date_created,
                                    ];
                                    array_push($subscriptions, $subscription); // add subscription item to subscriptions array
                                } // while
                            } // if
                            // Payments
                            // reset payments array
                            $payments = [];
                            // Filter by Payment Range
                            if (isset($_GET["payment_start"])) {
                                // check to see if payment_end is also included
                                if (!isset($_GET["payment_end"])) {
                                    http_response_code(400); // bad request
                                    throw new Exception(
                                        "[payment_end] is required when [payment_start] is specified.",
                                    );
                                }
                                // Assign variables
                                $payment_start = $_GET["payment_start"];
                                $payment_end = $_GET["payment_end"];

                                // // Filter by payment_status or any
                                // if ((isset($_GET["payment_status"]))) {
                                //     $payment_status = $_GET["payment_status"];
                                // } else { $payment_status = "%";}

                                // Filter by payment_amount_gte or default 0
                                if (isset($_GET["payment_amount_gte"])) {
                                    $payment_amount_gte =
                                        $_GET["payment_amount_gte"];
                                } else {
                                    $payment_amount_gte = 0;
                                }

                                $stmt3 = $donor->readPaymentsRange(
                                    $donation_id,
                                    $payment_start,
                                    $payment_end,
                                    $payment_amount_gte,
                                );
                            } else {
                                // read payments table for each donation
                                $stmt3 = $donor->readPaymentbyDonationid(
                                    $donation_id,
                                );
                            }

                            $result = processDonationPayments(
                                $stmt3,
                                $donation_amount,
                            );
                            $donation_status = $result["donation_status"];
                            $payments = $result["payments"]; // Detailed payment records, if needed

                            //Premiums
                            // reset premiums array
                            $premiums = [];
                            // read premiumGifts table for each transaction
                            $stmt4 = $donor->readGifts($donation_id);
                            // number of rows
                            $num4 = $stmt4->rowCount();
                            // check if more than 0 records found
                            if ($num4 > 0) {
                                // retrieve our table contents
                                while (
                                    $row4 = $stmt4->fetch(PDO::FETCH_ASSOC)
                                ) {
                                    extract($row4);
                                    // create array
                                    $premium = [
                                        "id" => intval($premium_id),
                                        "donation_id" => intval($donation_id),
                                        "name" => $name,
                                        "price" => floatval($price),
                                        "cog" => floatval($cog),
                                        "fmv" => floatval($fmv),
                                        "qty" => intval($qty),
                                        "download_url" => $download_url,
                                        "img_url" => $img_url,
                                        "category" => $category,
                                        "status" => $shipment_status,
                                    ];
                                    array_push($premiums, $premium); // add premium item to premiums array
                                } // while
                            } // if (readGifts)
                            // Recording
                            // the date
                            $date = date("Ymd", strtotime($timestamp));
                            $y = date("Y", strtotime($timestamp));
                            $m = date("m", strtotime($timestamp));
                            $d = date("d", strtotime($timestamp));
                            $filename = null;
                            $filesize = null;
                            $filelength = null;
                            $duration = null;
                            $length = null;
                            foreach (
                                glob(
                                    "/var/www/api.kpfa.org/htdocs/recordings/$y/$m/$d/in-8004395732-$phone-$date-*.wav",
                                )
                                as $filename
                            ) {
                                $dur = ltrim(
                                    shell_exec(
                                        "soxi -d " . $filename . " 2>&1",
                                    ),
                                    "0:",
                                );
                                $duration = substr($dur, 0, strpos($dur, "."));
                                $length = str_replace(":", "m", $duration);
                                $filesize = filesize_formatted($filename);
                                $filelength = "$length" . "s";
                                $filename = str_replace(
                                    "/var/www/api.kpfa.org/htdocs",
                                    "https://$api_url",
                                    $filename,
                                );
                            }
                            // create donation array
                            $donation = [
                                "id" => intval($donation_id),
                                "type" => $type,
                                "installment" => $installment,
                                "status" => $donation_status,
                                "amount" => floatval($donation_amount),
                                "transaction_id" => $transaction_id,
                                "timestamp" => $timestamp,
                                "comments" => $comments,
                                "add_me" => filter_var(
                                    $add_me,
                                    FILTER_VALIDATE_BOOLEAN,
                                    FILTER_NULL_ON_FAILURE,
                                ),
                                "read_onair" => filter_var(
                                    $read_onair,
                                    FILTER_VALIDATE_BOOLEAN,
                                    FILTER_NULL_ON_FAILURE,
                                ),
                                "donation_match" => filter_var(
                                    $donation_match,
                                    FILTER_VALIDATE_BOOLEAN,
                                    FILTER_NULL_ON_FAILURE,
                                ),
                                "show_name" => $show_name,
                                "source" => $source,
                                "ipaddress" => $ipaddress,
                                "browser" => $browser,
                                "campaign_id" => intval($campaign_id),
                                "campaign" => $campaign,
                                "updated" => $updated,
                                "recording_url" => $filename,
                                "recording_size" => $filesize,
                                "recording_length" => $filelength,
                                "shipping_firstname" => $shipping_firstname,
                                "shipping_lastname" => $shipping_lastname,
                                "shipping_address1" => $shipping_address1,
                                "shipping_address2" => $shipping_address2,
                                "shipping_city" => $shipping_city,
                                "shipping_state" => $shipping_state,
                                "shipping_country" => $shipping_country,
                                "shipping_postal_code" => $shipping_postal_code,
                                "payments" => $payments,
                                "premiums" => $premiums,
                                "subscriptions" => $subscriptions,
                            ];
                            // if ((!empty($donation) && (isset($_GET["payment_start"])))) {
                            //     // Push the donation item into the donation array
                            //     array_push($donations, $donation);
                            // }
                            // // No filter: just display records
                            // if (!empty($donation) && (!isset($_GET["payment_start"]))) {
                            //     // Push the donation item into the donation array
                            //     array_push($donations, $donation);
                            // }
                            // Push the donation item into the donation array
                            array_push($donations, $donation);
                        } // while (readDonations loop)
                    } // if (loop)

                    // Build the donor array
                    $donor_item = [
                        "id" => intval($donor_id),
                        "stripe_cus_id" => $stripe_cus_id,
                        "allegiance_id" => filter_var(
                            $allegiance_id,
                            FILTER_VALIDATE_INT,
                            FILTER_NULL_ON_FAILURE,
                        ),
                        "memsys_id" => filter_var(
                            $memsys_id,
                            FILTER_VALIDATE_INT,
                            FILTER_NULL_ON_FAILURE,
                        ),
                        "type" => $donor_type,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "partner_firstname" => $partner_firstname,
                        "partner_lastname" => $partner_lastname,
                        "phone" => $phone,
                        "email" => $email,
                        "address1" => $address1,
                        "address2" => $address2,
                        "city" => $city,
                        "state" => $state,
                        "country" => $country,
                        "postal_code" => $postal_code,
                        "phone" => $phone,
                        "email" => $email,
                        "notes" => $notes,
                        "donotsolicit" => (bool) $donotsolicit,
                        "deceased" => (bool) $deceased,
                        "donations" => $donations,
                        "date_created" => $date_created,
                        "date_updated" => $date_updated,
                    ];
                    // Push the donor item into the donor array
                    //array_push($donors["records"], $donor_item);
                    //FILTER: Don't push record if there is not donation doesn't have a payment record and filter is attached
                    //print_r($payments);
                    if (!empty($payments) && isset($_GET["payment_start"])) {
                        // Push the donor item into the donor array
                        array_push($donors["records"], $donor_item);
                    }
                    // No filter: just display records
                    if (!empty($payments) && !isset($_GET["payment_start"])) {
                        // Push the donor item into the donor array
                        array_push($donors["records"], $donor_item);
                    }
                } // end while for $stmt

                echo json_encode($donors);
            } else {
                http_response_code(404); // not found
                throw new Exception("No donors found.");
            }
        }
        // SEARCH donors
        if (isset($_GET["s"]) && !isset($entity)) {
            if (!empty($_GET["s"])) {
                $keywords = $_GET["s"];
            } else {
                throw new Exception("Please provide a search term.");
            }

            // query donors
            $stmt = $donor->searchDonors(ltrim($keywords));
            $num = $stmt->rowCount();

            // check if more than 0 record found
            if ($num > 0) {
                // donor array
                $donors = [];
                $donors["records"] = [];

                // retrieve our table contents

                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    // sql was executed in object
                    extract($row);
                    // reset donations array
                    $donations = [];
                    //Donations
                    // read pledge table for each donor to find a list of matching pledge ids
                    $stmt2 = $donor->readDonations($donor_id);
                    // number of rows
                    $num2 = $stmt2->rowCount();
                    // check if more than 0 records found
                    if ($num2 > 0) {
                        // retrieve our table contents
                        while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                            extract($row2); //readDonations
                            // reset subscriptions array
                            $subscriptions = [];
                            //Subscriptions
                            // read subscription table for each donor id
                            $stmt1 = $donor->readSubscriptionsByTransaction_id(
                                $transaction_id,
                            );
                            // number of rows
                            $num1 = $stmt1->rowCount();
                            // check if more than 0 records found
                            if ($num1 > 0) {
                                // retrieve our table contents
                                while (
                                    $row1 = $stmt1->fetch(PDO::FETCH_ASSOC)
                                ) {
                                    extract($row1);
                                    // create array
                                    $subscription = [
                                        "id" => $subscription_id,
                                        "processor" => $processor,
                                        "plan_id" => $plan_id,
                                        "amount" => floatval(
                                            $subscription_amount,
                                        ),
                                        "interval" => $interval,
                                        "active" => filter_var(
                                            $active,
                                            FILTER_VALIDATE_BOOLEAN,
                                            FILTER_NULL_ON_FAILURE,
                                        ),
                                        "date_canceled" => $date_canceled,
                                        "date_created" => $date_created,
                                    ];
                                    array_push($subscriptions, $subscription); // add subscription item to subscriptions array
                                } // while
                            } // if
                            // Payments
                            // reset payments array
                            $payments = [];
                            // read payments table for each donation
                            $stmt3 = $donor->readPaymentbyDonationid(
                                $donation_id,
                            );
                            $result = processDonationPayments(
                                $stmt3,
                                $donation_amount,
                            );
                            $donation_status = $result["donation_status"];
                            $payments = $result["payments"]; // Detailed payment records, if needed

                            //Premiums
                            // reset premiums array
                            $premiums = [];
                            // read premiumGifts table for each transaction
                            $stmt4 = $donor->readGifts($donation_id);
                            // number of rows
                            $num4 = $stmt4->rowCount();
                            // check if more than 0 records found
                            if ($num4 > 0) {
                                // retrieve our table contents
                                while (
                                    $row4 = $stmt4->fetch(PDO::FETCH_ASSOC)
                                ) {
                                    extract($row4);
                                    // create array
                                    $premium = [
                                        "id" => intval($premium_id),
                                        "donation_id" => intval($donation_id),
                                        "name" => $name,
                                        "price" => floatval($price),
                                        "cog" => floatval($cog),
                                        "fmv" => floatval($fmv),
                                        "qty" => intval($qty),
                                        "download_url" => $download_url,
                                        "img_url" => $img_url,
                                        "category" => $category,
                                        "status" => $shipment_status,
                                    ];
                                    array_push($premiums, $premium); // add premium item to premiums array
                                } // while
                            } // if (readGifts)
                            // Recording
                            // the date
                            $date = date("Ymd", strtotime($timestamp));
                            $y = date("Y", strtotime($timestamp));
                            $m = date("m", strtotime($timestamp));
                            $d = date("d", strtotime($timestamp));
                            $filename = null;
                            $filesize = null;
                            $filelength = null;
                            $duration = null;
                            $length = null;
                            foreach (
                                glob(
                                    "/var/www/api.kpfa.org/htdocs/recordings/$y/$m/$d/in-8004395732-$phone-$date-*.wav",
                                )
                                as $filename
                            ) {
                                $dur = ltrim(
                                    shell_exec(
                                        "soxi -d " . $filename . " 2>&1",
                                    ),
                                    "0:",
                                );
                                $duration = substr($dur, 0, strpos($dur, "."));
                                $length = str_replace(":", "m", $duration);
                                $filesize = filesize_formatted($filename);
                                $filelength = "$length" . "s";
                                $filename = str_replace(
                                    "/var/www/api.kpfa.org/htdocs",
                                    "https://$api_url",
                                    $filename,
                                );
                            }

                            // create donation array
                            $donation = [
                                "id" => intval($donation_id),
                                "type" => $type,
                                "installment" => $installment,
                                "status" => $donation_status,
                                "amount" => floatval($donation_amount),
                                "transaction_id" => $transaction_id,
                                "timestamp" => $timestamp,
                                "comments" => $comments,
                                "add_me" => filter_var(
                                    $add_me,
                                    FILTER_VALIDATE_BOOLEAN,
                                    FILTER_NULL_ON_FAILURE,
                                ),
                                "read_onair" => filter_var(
                                    $read_onair,
                                    FILTER_VALIDATE_BOOLEAN,
                                    FILTER_NULL_ON_FAILURE,
                                ),
                                "donation_match" => filter_var(
                                    $donation_match,
                                    FILTER_VALIDATE_BOOLEAN,
                                    FILTER_NULL_ON_FAILURE,
                                ),
                                "show_name" => $show_name,
                                "source" => $source,
                                "ipaddress" => $ipaddress,
                                "browser" => $browser,
                                "campaign_id" => intval($campaign_id),
                                "campaign" => $campaign,
                                "updated" => $updated,
                                "recording_url" => $filename,
                                "recording_size" => $filesize,
                                "recording_length" => $filelength,
                                "shipping_firstname" => $shipping_firstname,
                                "shipping_lastname" => $shipping_lastname,
                                "shipping_address1" => $shipping_address1,
                                "shipping_address2" => $shipping_address2,
                                "shipping_city" => $shipping_city,
                                "shipping_state" => $shipping_state,
                                "shipping_country" => $shipping_country,
                                "shipping_postal_code" => $shipping_postal_code,
                                "payments" => $payments,
                                "premiums" => $premiums,
                                "subscriptions" => $subscriptions,
                            ];
                            array_push($donations, $donation); // add donation item to donations array
                        } // while (readDonations loop)
                    } // if (loop)

                    // Build the donor array
                    $donor_item = [
                        "id" => intval($donor_id),
                        "stripe_cus_id" => $stripe_cus_id,
                        "allegiance_id" => filter_var(
                            $allegiance_id,
                            FILTER_VALIDATE_INT,
                            FILTER_NULL_ON_FAILURE,
                        ),
                        "memsys_id" => filter_var(
                            $memsys_id,
                            FILTER_VALIDATE_INT,
                            FILTER_NULL_ON_FAILURE,
                        ),
                        "type" => $donor_type,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "partner_firstname" => $partner_firstname,
                        "partner_lastname" => $partner_lastname,
                        "phone" => $phone,
                        "email" => $email,
                        "address1" => $address1,
                        "address2" => $address2,
                        "city" => $city,
                        "state" => $state,
                        "country" => $country,
                        "postal_code" => $postal_code,
                        "phone" => $phone,
                        "email" => $email,
                        "notes" => $notes,
                        "donotsolicit" => (bool) $donotsolicit,
                        "deceased" => (bool) $deceased,
                        "donations" => $donations,
                        "date_created" => $date_created,
                        "date_updated" => $date_updated,
                    ];
                    // Push the donor item into the donor array
                    array_push($donors["records"], $donor_item);
                } // end while for $stmt

                echo json_encode($donors);
            } else {
                http_response_code(404); // not found
                throw new Exception("No donor found.");
            }
        }
    }
} catch (Exception $e) {
    echo json_encode(["message" => $e->getMessage()]);
}
