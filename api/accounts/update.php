<?php
require_once __DIR__ . '/../src/Service/DonorService.php';
use InvalidArgumentException;
use PDOException;

// [ACCOUNTS]
try {
    // ACL
    $access_levels = array("Admin", "CallCenter");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    // UPDATE        UPDATE     PUT,PATCH (PUT replaces entire data, PATCH replaces only part of it)

    // resources
    $resources = array("donors", "foundations", "volunteers", "staff");

    // check to see if resource contains sub resource
    if (strpos($resource, '/') !== false) {
        // explodable
        $piece = explode("/", $resource);
        $resource = $piece[0]; // reassign resource
        $entity = $piece[1]; // entity
    } else {
        // not explodable
    }

    // resource check
    if (!in_array($resource, $resources)) {
        foreach ($resources as $resource) {
            $resource_array["resources"][] =
                array(
                    "links" => array(
                        "rel" => "self",
                        "href" => "$api_url/$collection/$resource/",
                    ),
                );
        }
        echo json_encode($resource_array);
        die;
    }

    // Accounts

    if ($resource == "foundations") {
        http_response_code(400); // bad request
        throw new Exception("feature coming soon!");
    }
    if ($resource == "volunteers") {
        http_response_code(400); // bad request
        throw new Exception("feature coming soon!");
    }
    if ($resource == "staff") {
        http_response_code(400); // bad request
        throw new Exception("feature coming soon!");
    }
    if ($resource == "donors") {
        // set donor_id
        if (isset($data->id) && !empty($data->id)) {
            $donor_id = $data->id;
        } else {
            // donor_id
            if (!empty(is_numeric($entity))) {
                $donor_id = $entity;
            } else {
                http_response_code(400); // bad request
                throw new Exception("[id] is required.");
            }
        }
        // Data integrity checks
        if (isset($data->deceased) && !empty($data->deceased)) {
            // check if not boolean
            if (is_bool($data->deceased) === false) {
                http_response_code(400); // bad request
                throw new Exception("[deceased] must be boolean.");
            }
        }
        if (isset($data->donotsolicit) && !empty($data->donotsolicit)) {
            // check if not boolean
            if (is_bool($data->donotsolicit) === false) {
                http_response_code(400); // bad request
                throw new Exception("[donotsolicit] must be boolean.");
            }
        }
        
if (isset($data->merge_with) && is_array($data->merge_with) && !empty($data->merge_with)) {
    try {
        $primaryId = $donor_id;
        $secondaryIds = $data->merge_with;
        
        // Create the donor service
        $donorService = new Kpfa\Service\DonorService($db, $donor);
        
        // Log the merge attempt
        error_log("Attempting to merge donors: primary=$primaryId, secondary=" . json_encode($secondaryIds));
        
        // Call the mergeDonors method from the service
        $result = $donorService->mergeDonors($primaryId, $secondaryIds);
        
        // Return success message
        echo json_encode($result);
        exit;
    } catch (InvalidArgumentException $e) {
        // Invalid arguments (validation errors) - 400 Bad Request
        http_response_code(400);
        echo json_encode([
            "status" => "error",
            "message" => $e->getMessage(),
            "error_type" => "validation_error"
        ]);
        exit;
    } catch (PDOException $e) {
        // Database errors - 500 Internal Server Error
        error_log("Database error during donor merge: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            "status" => "error",
            "message" => "Database error occurred during merge operation",
            "error_type" => "database_error",
            "details" => $e->getMessage()
        ]);
        exit;
    } catch (Exception $e) {
        // Other errors - 400 Bad Request
        // Roll back transaction on error if not already done
        if ($db->inTransaction()) {
            $db->rollBack();
        }
        
        error_log("Error during donor merge: " . $e->getMessage());
        http_response_code(400);
        echo json_encode([
            "status" => "error",
            "message" => $e->getMessage(),
            "error_type" => "general_error"
        ]);
        exit;
    }
}

        $i = 0;
        foreach ($data as $key => $value) {
            if (empty($value)) {
                $value = null; // set value to null if presented with empty string
            }
            $donor->update($donor_id, $key, $value); // call update donor function
        }
        // Read the Donor and send as JSON back
        echo json_encode($donor->read($donor_id)->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);
    }
} catch (Exception $e) {
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
