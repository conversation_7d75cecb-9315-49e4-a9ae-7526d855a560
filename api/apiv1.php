<?php
$bootstrap = require __DIR__ . "/bootstrap.php";
$donationService = $bootstrap["donationService"];

try {
    #https://lh3.googleusercontent.com/-cpYCrP36Nc8/VsWO7emBMRI/AAAAAAAAAyU/0rv7Lnl0aNI/s1600-h/image%25255B5%25255D.png

    /*
    Operation     SQL     HTTP
    ---------    ------     -----
    CREATE        INSERT     POST
    READ          SELECT     GET
    UPDATE        UPDATE     PUT,PATCH (PUT replaces entire data, PATCH replaces only part of it)
    DELETE        DELETE     DELETE
    */

    # Resources are plural (generally)
    # Objects are singular

    // Down with the hierarchy!

    // [GET, POST, PUT, PATCH, DELETE] #HTTP_METHOD
    $method = filter_input(INPUT_GET, "method");
    // [/login, /accounts, /pledges, /premiums, /receipts, /shipping, /shows, /stats, /campaigns, /email, /mail]
    $collection = filter_input(INPUT_GET, "collection");
    // [123, donors/123,]
    $resource = filter_input(INPUT_GET, "resource");
    // (filter) [s, start, end]

    $api_url = $_SERVER["SERVER_NAME"];

    // Collections Array
    $collections = [
        "programs",
        "login",
        "accounts",
        "users",
        "campaigns",
        "donors",
        "donations",
        "premiums",
        "receipt",
        "receipts",
        "subscriptions",
        "payments",
        "shipping",
        "shows",
        "stats",
        "email",
        "mail",
        "station",
        "stripe-webhook",
        "slack-webhook",
    ];

    // Special case: receipts
    if ($collection == "receipts" && $method == "GET") {
        $nojson = true;
    }

    // get JSON input
    $data = json_decode(file_get_contents("php://input"));

    // CHECK collection, LOAD objects & INSTANTIATE class
    if (in_array($collection, $collections)) {
        # CONFIG
        require_once "config/config.php";

        # DATABASE
        require_once "objects/database.php";

        # LOGIN
        require_once "objects/user.php";
        $user = new User($db); // $var is the object type of $collection class

        # EMAIL
        require_once "objects/email.php";
        $email_msg = new Email($db);

        # ACCOUNT
        require_once "objects/account.php";
        $donor = new Donor($db);
        $foundation = new Foundation($db);
        $volunteer = new Volunteer($db);
        $staff = new Staff($db);

        # DONATION
        require_once "objects/donation.php";
        $donation = new Donation($db, $donationService);

        # PREMIUM
        require_once "objects/premium.php";
        $premium = new Premium($db);
        $premium_vendor = new Premium_vendor($db);
        $premium_category = new Premium_category($db);

        # RECEIPT
        require_once "objects/receipt.php";
        $receipt = new Receipt($db);

        # PAYMENT
        require_once "objects/payment.php";
        $payment = new Payment($db);

        # SUBSCRIPTION
        require_once "objects/subscription.php";
        $subscription = new Subscription($db);

        # SHIPPING
        require_once "objects/shipping.php";
        $shipping = new Shipping($db);
        $shipping_address = new Shipping_address($db);

        # CAMPAIGNS
        require_once "objects/campaign.php";
        $campaign = new Campaign($db);

        # STATS
        require_once "objects/stat.php";
        $stat = new Stat($db);

        # SHOWS
        require_once "objects/show.php";
        $show = new Show($db);
    } else {
        // collection not in array
        foreach ($collections as $collection) {
            $collection_array["collections"][] = [
                "links" => [
                    "rel" => "self",
                    "href" => "https://$api_url/$collection/",
                ],
            ];
        }
        header("Content-Type: application/json");
        echo json_encode($collection_array, JSON_UNESCAPED_SLASHES);
        die();
    }

    // CRUD
    switch ($method) {
        case "POST": // CREATE
            if (file_exists("$collection/create.php")) {
                require_once "$collection/create.php";
            } else {
                http_response_code(404); // Not Found
                throw new Exception("Collection Not Found");
            }
            break;
        case "GET": // READ
            if ($collection == "accounts" && $resource == "optin") {
                require_once "accounts/optin.php";
            } elseif (file_exists("$collection/read.php")) {
                require_once "$collection/read.php";
            } else {
                http_response_code(404); // Not Found
                throw new Exception("Collection Not Found");
            }

            break;
        case "PUT": // UPDATE
            if (file_exists("$collection/update.php")) {
                require_once "$collection/update.php";
            } else {
                http_response_code(404); // Not Found
                throw new Exception("Collection Not Found");
            }

            break;
        case "PATCH": // UPDATE single key/val
            if (file_exists("$collection/update.php")) {
                require_once "$collection/update.php";
            } else {
                http_response_code(404); // Not Found
                throw new Exception("Collection Not Found");
            }

            break;
        case "DELETE": // DELETE
            if (file_exists("$collection/delete.php")) {
                require_once "$collection/delete.php";
            } else {
                http_response_code(404); // Not Found
                throw new Exception("Collection Not Found");
            }
            break;
        case "OPTIONS": // OPTIONS
            if (file_exists("$collection/options.php")) {
                require_once "$collection/options.php";
            } else {
                http_response_code(404); // Not Found
                throw new Exception("Collection Not Found");
            }
            break;
        default:
            http_response_code(400); // Not Found
            throw new Exception("Please use a valid method");
    }
} catch (Exception $e) {
    //  display message, write error to log, die.
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage(),
    ]);
    error_log($e->getMessage(), 0);
}
