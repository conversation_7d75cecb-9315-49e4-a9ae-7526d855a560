html,
body {
    height: 100%;
    /* The html and body elements cannot have any padding or margin. */
}
/* Wrapper for page content to push down footer */
#wrap {
    font-size: 14pt; 
    min-height: 100%;
    height: auto !important;
    margin: 0 auto -30px;
    page-break-inside: avoid; /* Please don't break my page content up browser */

}

/* Set the fixed height of the footer here */
#push {
    height: 100px;
}

.page-footer {
    background-color: #f5f5f5;
    height: 30px;
    font-size: 12pt; 
}


.container {
/*	background:url(https://kpfa.org/wp-content/themes/kpfa-v2/dist/images/bg_verylight.jpg) no-repeat top right #e6e6e6 fixed; */
}
