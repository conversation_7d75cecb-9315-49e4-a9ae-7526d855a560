$(function () {
  var cardNumber = $("#cardNumber");
  var exp_month = $("#exp_month");
  var exp_year = $("#exp_year");
  var CVV = $("#cvv");
  var cardNumberField = $("#card-number-field");
  var confirmButton = $("#submit");
  var visa = $("#visa");
  var amex = $("#amex");
  var discover = $("#discover");
  var mastercard = $("#mastercard");

  // Use the payform library to format and validate
  // the payment fields.

  cardNumber.payform("formatCardNumber");
  CVV.payform("formatCardCVC");

  cardNumber.keyup(function () {
    amex.removeClass("transparent");
    visa.removeClass("transparent");
    discover.removeClass("transparent");
    mastercard.removeClass("transparent");

    if ($.payform.validateCardNumber(cardNumber.val()) == false) {
      cardNumberField.addClass("has-error");
    } else {
      cardNumberField.removeClass("has-error");
      cardNumberField.addClass("has-success");
    }

    if ($.payform.parseCardType(cardNumber.val()) == "visa") {
      mastercard.addClass("transparent");
      amex.addClass("transparent");
      discover.addClass("transparent");
    } else if ($.payform.parseCardType(cardNumber.val()) == "amex") {
      mastercard.addClass("transparent");
      visa.addClass("transparent");
      discover.addClass("transparent");
    } else if ($.payform.parseCardType(cardNumber.val()) == "mastercard") {
      amex.addClass("transparent");
      visa.addClass("transparent");
      discover.addClass("transparent");
    } else if ($.payform.parseCardType(cardNumber.val()) == "discover") {
      mastercard.addClass("transparent");
      amex.addClass("transparent");
      visa.addClass("transparent");
    }
  });

  confirmButton.click(function (e) {
    e.preventDefault();

    var isCardValid = $.payform.validateCardNumber(cardNumber.val());
    var isCvvValid = $.payform.validateCardCVC(CVV.val());

    if (!isCardValid) {
      alert("Incorrect card number");
    } else if (!isCvvValid) {
      alert("Incorrect CVV");
    } else {
      // Everything is correct. Grab thejson
      $.getJSON(window.location + "?json", function (data) {
        //function is what happens on success
        // log to console (data is the JSON string)
        //console.log(data);
        var transaction_id = data.transaction_id;
        var donor_id = data.donor_id;
        var donation_id = data.id;
        var firstname = data.firstname;
        var lastname = data.lastname;
        var address1 = data.address1;
        var address2 = data.address2;
        var city = data.city;
        var state = data.state;
        var country = data.country;
        var postal_code = data.postal_code;
        var phone = data.phone;
        var email = data.email;
        var amount = data.payment_balance;
        var source = data.source;
        var installment = data.installment;
        var shipping_address1 = data.shipping_address1;
        var shipping_address2 = data.shipping_address2;
        var shipping_city = data.shipping_city;
        var shipping_state = data.shipping_state;
        var shipping_country = data.shipping_country;
        var shipping_postal_code = data.shipping_postal_code;
        /// add more
        // disable the button
        confirmButton.attr("disabled", true);

        // Everything is okay, let's charge!
        var xhr = new XMLHttpRequest(); // new HttpRequest instance
        var theUrl = window.location.origin + "/payments/";
        xhr.open("POST", theUrl);
        xhr.setRequestHeader(
          "Content-Type",
          "application/x-www-form-urlencoded;charset=UTF-8"
        );
        xhr.send(
          JSON.stringify({
            // send the string as JSON
            method: "card",
            donation_id: donation_id,
            donor_id: donor_id,
            transaction_id: transaction_id,
            firstname: firstname,
            lastname: lastname,
            address1: address1,
            address2: address2,
            city: city,
            state: state,
            country: country,
            postal_code: postal_code,
            shipping_address1: shipping_address1,
            shipping_address2: shipping_address2,
            shipping_city: shipping_city,
            shipping_state: shipping_state,
            shipping_country: shipping_country,
            shipping_postal_code: shipping_postal_code,
            phone: phone,
            email: email,
            amount: amount,
            cardnumber: cardNumber.val(),
            cc_securitycode: CVV.val(),
            exp_month: exp_month.val(),
            exp_year: exp_year.val(),
            installment: installment,
            source: source,
          })
        );

        xhr.onload = function () {
          if (xhr.readyState === xhr.DONE) {
            if (xhr.status === 200) {
              alert("Thank you! Your donation has been successfully paid");
              window.location = window.location.origin + "/receipts/" + transaction_id;
            }
            if (JSON.parse(xhr.responseText).status === "error") {
              alert(JSON.parse(xhr.responseText).message);
              // re-enable the button
              confirmButton.attr("disabled", false);
            }
          }
          if (JSON.parse(xhr.responseText).status === "error") {
            alert(JSON.parse(xhr.responseText).message);
            // re-enable the button
            confirmButton.attr("disabled", false);
          }
        }
      })
    };
  });
}

  //confirmButton.click(false);
);
