<?php
require_once "config/config.php";
require_once "vendor/autoload.php";

use Doctrine\DBAL\DriverManager;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\ORMSetup;
use DoctrineExtensions\TablePrefix;
use Kpfa\Service\DonationService;

$paths = ["./src/Entity"];
$isDevMode = true;

// the connection configuration
$dbParams = [
    "driver" => "pdo_mysql",
    "user" => $db_user,
    "password" => $db_pass,
    "dbname" => $db_name,
];

$evm = new \Doctrine\Common\EventManager();

// Table Prefix
$tablePrefix = new TablePrefix("kpfa_");
$evm->addEventListener(\Doctrine\ORM\Events::loadClassMetadata, $tablePrefix);

$config = ORMSetup::createAttributeMetadataConfiguration($paths, $isDevMode);
$connection = DriverManager::getConnection($dbParams, $config);
$connection
    ->getDatabasePlatform()
    ->registerDoctrineTypeMapping("enum", "string");
$entityManager = new EntityManager($connection, $config, $evm);
$donationService = new DonationService($entityManager);

return [
    "entityManager" => $entityManager,
    "donationService" => $donationService,
];
