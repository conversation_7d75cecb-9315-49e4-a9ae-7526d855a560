<?php
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    // name
    if (!empty($data->name)) {
        $campaign->name = $data->name;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[name] is required.");
    }

    // type
    if (!empty($data->type) && in_array($data->type, array(
        'marathon', 'mailer', 'email', 'event', 'general'
    ), true)) {
        $campaign->type = $data->type;
    } else {
        throw new Exception("[type] is required and must be of type 'marathon','mailer','email','event', or 'general'");
    }

    // goal
    if (!empty($data->goal)) {
        if (filter_var($data->goal, FILTER_VALIDATE_FLOAT)) {
            $campaign->goal = $data->goal;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[goal] must be an integer.");
        }
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[goal] is required.");
    }

    // start
    if (!empty($data->name)) {
        $campaign->start = $data->start;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[start] is required and must be in the YYYY-MM-DD hh:mm:ss format.");
    }

    // end
    if (!empty($data->end)) {
        $campaign->end = $data->end;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[end] is required and must be in the YYYY-MM-DD hh:mm:ss format");
    }

    // active
    if (isset($data->active)) {
        if (NULL !== $data->active = filter_var($data->active, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE)) {
            $campaign->active = $data->active;
        } else {
            http_response_code(400); // bad request
            throw new Exception("[active] must be boolean.");
        }
    } else {
        http_response_code(400); // bad request
        throw new Exception("[active] is required.");
    }

    // Optional
    // gift_title
    if (!empty($data->gift_title)) {
        $campaign->gift_title = $data->gift_title;
    } else $campaign->gift_title = null;

    // gift_link
    if (!empty($data->gift_link)) {
        $campaign->gift_link = $data->gift_link;
    } else $campaign->gift_link = null;

    // notes
    if (!empty($data->notes)) {
        $campaign->notes = $data->notes;
    } else $campaign->notes = null;

    // create the campaign
    $campaign->create();


    // if unable to update the campaign, tell the user
} catch (Exception $e) {
    //  display message, write error to log, die.
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    error_log($e->getMessage(), 0);
}
