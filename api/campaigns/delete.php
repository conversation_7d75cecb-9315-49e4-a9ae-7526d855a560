<?php
// Delete Campaign
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }    

    // check to see if resource contains sub resource
    if (strpos($resource, '/') !== false) {
        // explodable
        $piece = explode("/", $resource);
        $resource = $piece[0]; // reassign resource
        $entity = $piece[1]; // entity
    } else {
        // not explodable
    }

    // set campaign_id to be deleted
    if (isset($resource)) { // is ID in URL?
        $campaign->id = $resource;
    }
    if (isset($data->id)) { // is the id in the body ($data)
        $campaign->id = $data->id;
    }
    // Require id
    if (empty($campaign->id)) {
        http_response_code(400); // bad request
        throw new Exception("[id] is required");
    }

    // delete the campaign
    if ($campaign->delete($campaign->id)) {
        echo json_encode(array(
            "message" => "Campaign $campaign->id was deleted.",
        ));
    }

    // if unable to update the campaigns, tell the user
} catch (Exception $e) {
    //  display message, write error to log, die.
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    error_log($e->getMessage(), 0);
}
