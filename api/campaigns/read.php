<?php
// Campaigns
try {
    if ($collection == "campaigns") {
        // READ campaigns
        if ((!isset($_GET["s"])) && (!isset($resource))) {
            // Check to see if we should filter by DATE
            if (!empty($_GET['start']) && !empty($_GET['end'])) {
                $startdate = date('Y-m-d', strtotime($_GET['start']));
                $enddate = date('Y-m-d', strtotime($_GET['end']));
                // filter campaigns by date
                $stmt = $campaign->readDate($startdate, $enddate);
                $num = $stmt->rowCount();
            }
            if (!empty($_GET['active']) && $_GET['active'] == "true") {
                // filter campaigns by actove
                $stmt = $campaign->readActiveCampaigns();
                $num = $stmt->rowCount();
            } else {
                // read campaigns
                $stmt = $campaign->readCampaigns();
                $num = $stmt->rowCount();
            }
            // read campaign values
            // check if more than 0 record found
            if ($num > 0) {
                // campaign(s) found, create array
                $campaign_arr = array();
                // retrieve campaign contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    // iterate through campaigns and create campaign item.
                    extract($row);
                    // ACL, don't show notes to Guest (non logged in users)
                    if ($access_level == "Guest") {
                        $notes = null;
                    }
                    $date1 = new DateTime($start);
                    $date2 = new DateTime($end);
                    $duration  = $date2->diff($date1)->format('%a');
                    $campaign_item = array(
                        "id" => intval($id),
                        "type" => $type,
                        "name" => $name,
                        "active" => boolval($active),
                        "start" => $start,
                        "end" => $end,
                        "duration" => floatval($duration),
                        "goal" => floatval($goal),
                        "gift_title" => $gift_title,
                        "gift_link" => $gift_link,
                        "notes" => $notes,
                    );
                    // add further detail
                    // FIXME TODO this is a duplicate read
                    $stmt2 = $campaign->read($id); // returns null when no rows found
                    $num2 = $stmt2->rowCount();

                    // campaign_item info
                    while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                        extract($row2);
                        if ($duration == 0) {
                            $duration = 1; // all campaigns are atleast one day.
                        }
                        // active
                        $date = date("Y-m-d H:i:s");
                        $date_int = date('Ymd', strtotime($date)); // on 4th may 2016, would have been 20160504
                        $start_int = date('Ymd', strtotime($start));
                        $end_int = date('Ymd', strtotime($end));
                        $active = ($date_int >= $start_int && $date_int <= $end_int) ? true : false;
                        // Build the Campaign array
                        if ($pledged > 0) {
                            $paid_percent = $paid / $pledged * 100;
                        } else {
                            $paid_percent = 0;
                        }
                        // add campaign items
                        $campaign_item["count"] = intval($count);
                        $campaign_item["pledged"] = floatval($pledged);
                        $campaign_item["pledged_percent"] = floatval(number_format(($pledged / $goal) * 100, 2, '.', ''));
                        $campaign_item["paid"] = floatval($paid);
                        $campaign_item["sustainer_total"] = floatval($sustainer_total);
                        $campaign_item["sustainer_count"] = floatval($sustainer_count);
                        $campaign_item["paid_percent"] = floatval(number_format($paid_percent, 2, '.', ''));
                        $campaign_item["pledge_daily_average"] = floatval(number_format($pledged / $duration, 2, '.', ''));
                        if ($count > 0) {
                            $campaign_item["pledge_donation_average"] = floatval(number_format(($pledged / $count), 2, '.', ''));
                        } else {
                            $campaign_item["pledge_donation_average"] = 0;
                        }
                    } // while
                    // Push the campaign item into the campaign array
                    array_push($campaign_arr, $campaign_item);
                } // while
                echo json_encode($campaign_arr);
            } else {
                echo json_encode(array(
                    "message" => "No campaigns found.",
                ));
            }
        }
        // READ campaign
        if ((!isset($_GET["s"])) && (isset($resource))) {
            $stmt = $campaign->read($resource);
            $num = $stmt->rowCount();
            // check if more than 0 record found
            if ($num > 0) {
                // campaign array
                $campaign_arr = array();
                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);
                    // ACL, don't show notes to Guest (non logged in users)
                    if ($access_level == "Guest") {
                        $notes = null;
                    }
                    // active
                    $date = date("Y-m-d H:i:s");
                    $date_int = date('Ymd', strtotime($date)); // on 4th may 2016, would have been 20160504
                    $start_int = date('Ymd', strtotime($start));
                    $end_int = date('Ymd', strtotime($end));
                    $active = ($date_int >= $start_int && $date_int <= $end_int) ? true : false;
                    // Build the Campaign array
                    if ($pledged > 0) {
                        $paid_percent = $paid / $pledged * 100;
                    } else {
                        $paid_percent = 0;
                    }
                    $campaign_item = array(
                        "id" => intval($id),
                        "type" => $type,
                        "name" => $name,
                        "active" => boolval($active),
                        "start" => $start,
                        "end" => $end,
                        "goal" => floatval($goal),
                        "count" => intval($count),
                        "pledged" => floatval($pledged),
                        "pledged_percent" => floatval(number_format(($pledged / $goal) * 100, 2)),
                        "paid" => floatval($paid),
                        "sustainer_total" => floatval($sustainer_total),
                        "sustainer_count" => floatval($sustainer_count),
                        "paid_percent" => floatval(number_format($paid_percent, 2)),
                        "notes" => $notes,
                    );
                    // Push the campaign item into the campaign array
                    array_push($campaign_arr, $campaign_item);
                } // while
                echo json_encode($campaign_arr);
            } else {
                echo json_encode(array(
                    "message" => "No campaign found.",
                ));
            }
        }
        // SEARCH campaigns //FIXME
        if ((isset($_GET["s"])) && (!isset($resource))) {
            $keywords = isset($_GET["s"]) ? $_GET["s"] : json_encode(array(
                "message" => "Please provide a search term.",
            ));
            // query campaigns
            $stmt = $campaign->searchCampaigns($keywords);
            $num = $stmt->rowCount();
            // check if more than 0 record found
            if ($num > 0) {
                // campaign array
                $campaign_arr = array();
                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);
                    // ACL, don't show notes to Guest (non logged in users)
                    if ($access_level == "Guest") {
                        $notes = null;
                    }
                    // active
                    $date = date("Y-m-d H:i:s");
                    $date_int = date('Ymd', strtotime($date)); // on 4th may 2016, would have been 20160504
                    $start_int = date('Ymd', strtotime($start));
                    $end_int = date('Ymd', strtotime($end));
                    $active = ($date_int >= $start_int && $date_int <= $end_int) ? true : false;
                    // Build the Campaign array
                    if ($pledged > 0) {
                        $paid_percent = $paid / $pledged * 100;
                    } else {
                        $paid_percent = 0;
                    }
                    $campaign_item = array(
                        "id" => intval($id),
                        "type" => $type,
                        "name" => $name,
                        "active" => boolval($active),
                        "start" => $start,
                        "end" => $end,
                        "goal" => floatval($goal),
                        "count" => intval($count),
                        "pledged" => floatval($pledged),
                        "pledged_percent" => floatval(number_format(($pledged / $goal) * 100, 2)),
                        "paid" => floatval($paid),
                        "sustainer_total" => floatval($sustainer_total),
                        "sustainer_count" => floatval($sustainer_count),
                        "paid_percent" => floatval(number_format($paid_percent, 2)),
                        "notes" => $notes,
                    );
                    // Push the campaign item into the campaign array
                    array_push($campaign_arr, $campaign_item);
                } // while
                echo json_encode($campaign_arr);
            } else {
                echo json_encode(array(
                    "message" => "No campaigns found.",
                ));
            }
        }
    }
} catch (Exception $e) {
    echo json_encode(array(
        "message" => "collection is required.",
    ));
    die;
}
