-- MySQL dump 10.13  Distrib 5.7.33, for Linux (x86_64)
--
-- Host: localhost    Database: api_db
-- ------------------------------------------------------
-- Server version	5.7.33-0ubuntu0.18.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `accounts`
--

DROP TABLE IF EXISTS `accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donor_id` int(11) DEFAULT NULL,
  `volunteer_id` int(11) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `foundation_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_2` (`id`,`donor_id`,`volunteer_id`,`employee_id`,`foundation_id`),
  KEY `id` (`id`,`donor_id`,`volunteer_id`,`employee_id`,`foundation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `caller_log`
--

DROP TABLE IF EXISTS `caller_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `caller_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donor_id` int(11) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `number` bigint(20) NOT NULL,
  `duration` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `donor_id` (`donor_id`),
  CONSTRAINT `donorid_fkib` FOREIGN KEY (`donor_id`) REFERENCES `donors` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1099 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `campaigns`
--

DROP TABLE IF EXISTS `campaigns`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Campaign name',
  `type` enum('marathon','mailer','email','event','general') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `goal` decimal(11,2) NOT NULL COMMENT 'Campaign goal',
  `start` datetime NOT NULL COMMENT 'Start Date',
  `end` datetime NOT NULL COMMENT 'End Date',
  `active` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `donations`
--

DROP TABLE IF EXISTS `donations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `donations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'donation_id',
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `account_id` int(11) DEFAULT NULL,
  `donor_id` int(11) NOT NULL,
  `transaction_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_id` int(11) DEFAULT NULL,
  `type` enum('Pledge','In-Kind','Vehicle','Bequest','Grant','Contract','Corporate Match','QCD','Donor Advised Fund','Major','Minor','Mailer') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Pledge',
  `amount` decimal(13,2) NOT NULL,
  `installment` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `comments` mediumtext COLLATE utf8mb4_unicode_ci,
  `add_me` char(1) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Marketing & Communication',
  `read_onair` tinyint(1) NOT NULL DEFAULT '0',
  `ipaddress` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ipv4 (15) and ipv6 (45)',
  `browser` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `show_name` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source` enum('WebSite','PhoneRoom','StationAdmin','Testing','PaySol','CallCenter') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `donation_match` tinyint(1) NOT NULL DEFAULT '0',
  `premiums_cart` longtext COLLATE utf8mb4_unicode_ci COMMENT 'Premium Cart at time of donation',
  `updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `donor_id` (`donor_id`),
  KEY `campaign_id` (`campaign_id`),
  KEY `timestamp` (`timestamp`),
  CONSTRAINT `donations_campaigns_ibfk_1` FOREIGN KEY (`campaign_id`) REFERENCES `campaigns` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `donations_donors_ibfk_1` FOREIGN KEY (`donor_id`) REFERENCES `donors` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=136670 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `donors`
--

DROP TABLE IF EXISTS `donors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `donors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firstname` varchar(35) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `lastname` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Phone Number (E.164)',
  `email` varchar(320) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'email address',
  `address1` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address2` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `partner_firstname` varchar(35) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `partner_lastname` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(225) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'US' COMMENT 'ISO 3166-1 alpha-2',
  `postal_code` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` mediumtext COLLATE utf8mb4_unicode_ci COMMENT 'Notes',
  `type` set('Individual','Couple','Foundation','Corporate','Goverment','Trust','Charity','Fund','Test','Fraudulent') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Individual',
  `membership_level` enum('Basic','Vigilant','Vibrant') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deceased` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'alive or deceased',
  `donotsolicit` tinyint(1) DEFAULT NULL,
  `stripe_cus_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Stripe Customer ID',
  `paypal_user_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'PayPal ID',
  `allegiance_id` int(11) DEFAULT NULL COMMENT 'Allegiance ID',
  `memsys_id` mediumint(6) unsigned DEFAULT NULL COMMENT 'MemSys ID',
  `date_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `memsys_id` (`memsys_id`),
  UNIQUE KEY `stripe_cus_id` (`stripe_cus_id`),
  UNIQUE KEY `paypal_user_id` (`paypal_user_id`),
  UNIQUE KEY `email_2` (`email`),
  KEY `email` (`email`),
  KEY `phone` (`phone`),
  KEY `lastname` (`lastname`)
) ENGINE=InnoDB AUTO_INCREMENT=62703 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `download_log`
--

DROP TABLE IF EXISTS `download_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `download_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `show` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `episode` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `post_id` int(11) DEFAULT NULL,
  `count` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `foundations`
--

DROP TABLE IF EXISTS `foundations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `foundations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) DEFAULT NULL,
  `type` set('Family Foundation','Private Foundation','Corporate Foundation','Community Foundation','Federal','State','Local') COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact_person` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address1` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `postal_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fax` bigint(20) DEFAULT NULL,
  `email` varchar(320) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `icecast_logs`
--

DROP TABLE IF EXISTS `icecast_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `icecast_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `datetime_start` datetime NOT NULL,
  `datetime_end` datetime DEFAULT NULL,
  `ip` varchar(20) NOT NULL,
  `country_code` varchar(4) DEFAULT NULL,
  `mount` varchar(90) NOT NULL,
  `status_code` int(11) DEFAULT NULL,
  `duration` int(11) DEFAULT NULL,
  `sent_bytes` bigint(11) DEFAULT NULL,
  `agent` varchar(400) DEFAULT NULL,
  `referer` varchar(400) DEFAULT NULL,
  `server` varchar(50) DEFAULT NULL,
  `user` varchar(20) DEFAULT NULL,
  `pass` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000793 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payments`
--

DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donation_id` int(11) NOT NULL COMMENT 'Foreign Key to donations',
  `customer_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'stripe_customer_id',
  `payment_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'stripe charge_id, paypal payments id, check#',
  `amount` decimal(13,2) NOT NULL COMMENT 'Amount',
  `amount_refunded` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT 'Amount Refunded',
  `method` set('card','check','cash','in-kind','bank_account','us_bank_account','epayment') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Method of Payment',
  `processor` enum('Stripe','PayPal','P-check','K-check','Staff','Bitcoin') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Payment Processor',
  `fingerprint` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'stripe_card_fingerprint',
  `card_type` set('credit','debit','prepaid','unknown') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last4` smallint(4) unsigned DEFAULT NULL,
  `brand` set('Amex','Diners Club','Discover','JCB','MasterCard','Visa','UnionPay','Unknown','American Express','Diners') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `exp_month` tinyint(2) DEFAULT NULL,
  `exp_year` smallint(4) unsigned DEFAULT NULL,
  `last_updated_by` int(11) DEFAULT NULL COMMENT 'user_id',
  `date_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation time',
  `date_updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record update date and time',
  `date_deposited` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Date Deposited into Station Bank account',
  `status` set('succeeded','pending','failed') COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `donations_id` (`donation_id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `payments_ibfk` FOREIGN KEY (`donation_id`) REFERENCES `donations` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=142753 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `premium_categories`
--

DROP TABLE IF EXISTS `premium_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `premium_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created` datetime NOT NULL,
  `modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `premium_vendors`
--

DROP TABLE IF EXISTS `premium_vendors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `premium_vendors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `website` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` varchar(225) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `zip` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(254) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='vendor table linked to premiums';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `premiums`
--

DROP TABLE IF EXISTS `premiums`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `premiums` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `img_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `download_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `price` decimal(8,2) NOT NULL,
  `cog` int(11) NOT NULL COMMENT 'Cost of Goods / Monthly Cost',
  `fmv` int(11) NOT NULL COMMENT 'Fair Market Value',
  `qty` int(11) NOT NULL COMMENT 'Quantity',
  `variant_name` set('Size','Color','Delivery') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `variation_name` set('Small', 'Medium', 'Large', 'XL', 'XXL', '3XL', '4XL', 'Black', 'White', 'One Size Fits Most', 'Post Mail', 'Download', 'Red', 'Blue') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parentID` int(11) DEFAULT NULL,
  `category_id` int(11) NOT NULL,
  `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `featured` tinyint(1) NOT NULL DEFAULT '0',
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `vendor_id` int(11) DEFAULT NULL,
  `vendor_code` varchar(48) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort_weight` INT NOT NULL DEFAULT '50' COMMENT 'sort weight',
  `date_created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'date_created'
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `vendor_id` (`vendor_id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `premiums_ibfk_1` FOREIGN KEY (`vendor_id`) REFERENCES `premium_vendors` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `premiums_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `premium_categories` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=212201 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shipment_addresses`
--

DROP TABLE IF EXISTS `shipment_addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shipment_addresses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firstname` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `lastname` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address1` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `postal_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `donation_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `donation_id` (`donation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=60849 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shipments`
--

DROP TABLE IF EXISTS `shipments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shipments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donation_id` int(11) NOT NULL,
  `premium_id` int(11) NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `quantity` int(11) NOT NULL DEFAULT '1',
  `status` set('Shipped','On Hold','New','Cancelled','returned') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'New',
  `ship_date` date DEFAULT NULL,
  `tracking_number` varchar(35) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `premium_id` (`premium_id`),
  KEY `donation_id` (`donation_id`),
  KEY `timestamp` (`timestamp`),
  KEY `updated` (`updated`),
  CONSTRAINT `fk_donation_id` FOREIGN KEY (`donation_id`) REFERENCES `donations` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_premium_id` FOREIGN KEY (`premium_id`) REFERENCES `premiums` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=18799 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shows`
--

DROP TABLE IF EXISTS `shows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shows` (
  `id` tinyint(4) NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Program Name',
  `hosts` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Program Hosts',
  `description` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Program Description',
  `img_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `starts` time NOT NULL COMMENT 'Starting Time',
  `ends` time NOT NULL COMMENT 'Ending Time',
  `duration` smallint(6) NOT NULL COMMENT 'Length in seconds',
  `days` set('Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Day of the Week',
  `weeks` set('01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','40','41','42','43','44','45','46','47','48','49','50','51','52','53') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Weeks of the Year',
  `dad_id` int(11) NOT NULL COMMENT 'Dad ID',
  `type` set('News & Politics','Music','Culture','Special','Talk') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Program Type',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Active Program',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `staff`
--

DROP TABLE IF EXISTS `staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) DEFAULT NULL,
  `supervisor_id` int(11) NOT NULL,
  `first_name` varchar(225) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(225) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department` enum('Engineering','Operations','Production','Business','Development','Communications','General Managment') COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(320) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone_extension` int(11) DEFAULT NULL,
  `description` mediumtext COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `stream_log`
--

DROP TABLE IF EXISTS `stream_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stream_log` (
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `stream` varchar(12) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Stream Name',
  `streamers` int(11) NOT NULL COMMENT 'Listener Count',
  `show` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `episode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `post_id` mediumint(9) DEFAULT NULL,
  UNIQUE KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `stripe_prices`
--

DROP TABLE IF EXISTS `stripe_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stripe_prices` (
  `id` varchar(40) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` int(11) NOT NULL,
  `product_id` varchar(40) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `amount_2` (`amount`),
  KEY `amount` (`amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subscriptions`
--

DROP TABLE IF EXISTS `subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscriptions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Stripe subscription_id',
  `customer_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `donor_id` int(11) NOT NULL COMMENT 'kpfa donor_id',
  `processor` enum('Stripe') COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `plan_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `transaction_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `amount` decimal(13,2) NOT NULL,
  `interval` enum('month') COLLATE utf8mb4_unicode_ci NOT NULL,
  `active` tinyint(1) NOT NULL,
  `date_canceled` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `donorid_ibfk` (`donor_id`),
  KEY `transactionid_ibfk` (`transaction_id`),
  CONSTRAINT `customerid_ibfk` FOREIGN KEY (`customer_id`) REFERENCES `payments` (`customer_id`),
  CONSTRAINT `donorid_ibfk` FOREIGN KEY (`donor_id`) REFERENCES `donors` (`id`),
  CONSTRAINT `transactionid_ibfk` FOREIGN KEY (`transaction_id`) REFERENCES `donations` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subscriptions_bkup`
--

DROP TABLE IF EXISTS `subscriptions_bkup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscriptions_bkup` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Stripe subscription_id',
  `customer_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `donor_id` int(11) NOT NULL COMMENT 'kpfa donor_id',
  `processor` enum('Stripe') COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `plan_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `transaction_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `amount` decimal(13,2) NOT NULL,
  `interval` enum('month') COLLATE utf8mb4_unicode_ci NOT NULL,
  `active` tinyint(1) NOT NULL,
  `date_canceled` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `donorid_ibfk` (`donor_id`),
  KEY `transactionid_ibfk` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `temp`
--

DROP TABLE IF EXISTS `temp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `temp` (
  `donor_id` int(11) NOT NULL,
  `total_fmv` decimal(8,0) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firstname` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `lastname` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(2048) COLLATE utf8mb4_unicode_ci NOT NULL,
  `access_level` set('Admin','Staff','CallCenter','Donor') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Donor',
  `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='admin and users';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2021-05-07 20:30:53
