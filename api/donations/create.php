<?php
// Set donation property values
// [Required Section]
try {
    // replace Microsoft smart quotes
    function convert_smart_quotes($string) {
        $search = [chr(145), chr(146), chr(147), chr(148), chr(151)];

        $replace = ["'", "'", '"', '"', "-"];

        return str_replace($search, $replace, $string);
    }
    // source
    if (!empty($data->source)) {
        $donation->source = $data->source;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[source] is required.");
    }

    // firstname
    if (!empty($data->firstname)) {
        $donation->firstname = convert_smart_quotes($data->firstname);
    } else {
        http_response_code(400); // bad request
        throw new Exception("[firstname] is required.");
    }

    // lastname
    if (!empty($data->lastname)) {
        $donation->lastname = convert_smart_quotes($data->lastname);
    } else {
        http_response_code(400); // bad request
        throw new Exception("[lastname] is required.");
    }

    // address1
    if (!empty($data->address1)) {
        $donation->address1 = $data->address1;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[address1] is required.");
    }

    // city
    if (!empty($data->city)) {
        $donation->city = $data->city;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[city] is required.");
    }

    // state
    if (!empty($data->state)) {
        $donation->state = $data->state;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[state] is required.");
    }

    // country
    if (!empty($data->country)) {
        $donation->country = $data->country;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[country] is required.");
    }

    // amount
    if (!empty($data->amount)) {
        $donation->amount = $data->amount;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[amount] is required.");
    }

    // postal_code
    if (!empty($data->postal_code)) {
        $donation->postal_code = $data->postal_code;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[postal_code] is required.");
    }

    // installment
    if (
        !empty($data->installment) &&
        in_array($data->installment, ["One-Time", "Monthly"], true)
    ) {
        $donation->installment = $data->installment;
    } else {
        http_response_code(400); // bad request
        throw new Exception(
            "[installment] is required and must be of type 'One-Time' or 'Monthly'",
        );
    }

    // Type
    if (isset($data->type) && !empty($data->type)) {
        if (
            !in_array($data->type, [
                "Pledge",
                "In-Kind",
                "Vehicle",
                "Bequest",
                "Corporate Match",
                "QCD",
                "Donor Advised Fund",
                "Major",
                "Minor",
            ])
        ) {
            http_response_code(400); // Bad Request
            throw new Exception("[$data->type] is not a valid value for type");
        }
        $donation->type = $data->type;
    } else {
        $donation->type = "Pledge";
    }

    // Email (donor)
    if (isset($data->email) && !empty($data->email)) {
        if (
            !empty($data->email) &&
            filter_var($data->email, FILTER_VALIDATE_EMAIL)
        ) {
            if (stripos($data->email, "vinxdame") !== false) {
                //fraud
                http_response_code(400); // bad request
                throw new Exception("Please contact the station.");
            }
            if (stripos($data->email, "@zmat.xyz") !== false) {
                //fraud
                http_response_code(400); // bad request
                throw new Exception("Please contact the station.");
            }
            if (stripos($data->email, "aba.com") !== false) {
                //bad email
                http_response_code(400); // bad request
                throw new Exception(
                    "Please do not use fake email addresses. Note: email addresses are not required.",
                );
            }
            if (stripos($data->email, "abetteranswer") !== false) {
                //bad email
                http_response_code(400); // bad request
                throw new Exception(
                    "Please do not use fake email addresses. Note: email addresses are not required.",
                );
            }
            if (stripos($data->email, "noemail") !== false) {
                //bad email
                http_response_code(400); // bad request
                throw new Exception("Please use a real email addresses.");
            }
            if (stripos($data->email, "none@") !== false) {
                // bad email
                http_response_code(400); // bad request
                throw new Exception("Please use a real email addresses.");
            }
            if (stripos($data->email, "@domain.com") !== false) {
                // bad email
                http_response_code(400); // bad request
                throw new Exception("Please use a real email addresses.");
            }
            if (stripos($data->email, "@none") !== false) {
                // bad email
                http_response_code(400); // bad request
                throw new Exception("Please use a real email addresses.");
            }
            if (stripos($data->email, "refused.com") !== false) {
                //bad email
                http_response_code(400); // bad request
                throw new Exception("Please use a real email addresses.");
            }
            if (stripos($data->email, "isnotreal.com") !== false) {
                //bad email
                http_response_code(400); // bad request
                throw new Exception(
                    "Please use a real email addresses. Note: email addresses are not required.",
                );
            }
            // Break apart email
            [$username, $domain] = explode("@", $data->email);
            // Misspelled domain check
            $bad_sbc_arr = [
                "sbcgobal.net",
                "sbcgloble.net",
                "sbcgloba.net",
                "sbcblobal.net",
                "sbcgloal.net",
                "sbcgkibal.net",
                "sbcglobal.neet",
                "sbcgloblal.net",
                "sbcglobal.ent",
            ];
            $bad_gmail_arr = ["gmil.com", "gmail.co"];
            // Misspelled domain fix
            if (in_array($domain, $bad_sbc_arr)) {
                $data->email = $username . "@sbcglobal.net";
            }
            if (in_array($domain, $bad_gmail_arr)) {
                $data->email = $username . "@gmail.com";
            }
            $donation->email = $data->email;
        } else {
            http_response_code(400); // bad request
            throw new Exception("Please re-check your email address.");
        }
    } else {
        $donation->email = null;
    }
    // address2 (donor)
    if (isset($data->address2) && !empty($data->address2)) {
        $donation->address2 = $data->address2;
    } else {
        $donation->address2 = null;
    }

    // shipping_firstname
    if (isset($data->shipping_firstname) && !empty($data->shipping_firstname)) {
        $donation->shipping_firstname = $data->shipping_firstname;
    } else {
        $donation->shipping_firstname = null;
    }

    // shipping_lastname
    if (isset($data->shipping_lastname) && !empty($data->shipping_lastname)) {
        $donation->shipping_lastname = $data->shipping_lastname;
    } else {
        $donation->shipping_lastname = null;
    }

    // shipping_address1
    if (isset($data->shipping_address1) && !empty($data->shipping_address1)) {
        $donation->shipping_address1 = $data->shipping_address1;
    } else {
        $donation->shipping_address1 = null;
    }

    // shipping_address2
    if (isset($data->shipping_address2) && !empty($data->shipping_address2)) {
        $donation->shipping_address2 = $data->shipping_address2;
    } else {
        $donation->shipping_address2 = null;
    }

    // shipping_city
    if (isset($data->shipping_city) && !empty($data->shipping_city)) {
        $donation->shipping_city = $data->shipping_city;
    } else {
        $donation->shipping_city = null;
    }

    // shipping_state
    if (isset($data->shipping_state) && !empty($data->shipping_state)) {
        $donation->shipping_state = $data->shipping_state;
    } else {
        $donation->shipping_state = null;
    }

    // shipping_country
    if (isset($data->shipping_country) && !empty($data->shipping_country)) {
        $donation->shipping_country = $data->shipping_country;
    } else {
        $donation->shipping_country = null;
    }

    // shipping_postal_code
    if (
        isset($data->shipping_postal_code) &&
        !empty($data->shipping_postal_code)
    ) {
        $donation->shipping_postal_code = $data->shipping_postal_code;
    } else {
        $donation->shipping_postal_code = null;
    }

    // phone (donor)
    if (isset($data->phone) && !empty($data->phone)) {
        $donation->phone = preg_replace("/[^0-9]/", "", $data->phone); // strip anything non-numeric

        if (strpos($donation->phone, "8486767") !== false) {
            //bad phone
            http_response_code(400); // bad request
            throw new Exception("Please do not use the station phone number.");
        }
        // bad numbers
        $bad_phone_array = [
            "123456",
            "000000",
            "222222",
            "333333",
            "444444",
            "555555",
            "666666",
            "777777",
            "888888",
            "999999",
            "5551212",
            "8675309",
        ];

        // use str_replace to search array and if match is found, then throw alert
        if (
            str_replace($bad_phone_array, "", $donation->phone) !=
            $donation->phone
        ) {
            http_response_code(400); // bad request
            throw new Exception("Please use a real phone number.");
        }
    } else {
        $donation->phone = null;
    }

    // campaign_id
    if (isset($data->campaign_id) && !empty($data->campaign_id)) {
        $donation->campaign_id = $data->campaign_id;
    } else {
        $donation->campaign_id = null;
    }

    // if cardnumber is set, and there is a card number then method is card
    if (isset($data->cardnumber) && !empty($data->cardnumber)) {
        $data->method = "card";
        $donation->method = "card";
    } else {
        $donation->method = null;
    }
    // check
    if (
        isset($data->method) &&
        ($data->method == "check" || $data->method == "epayment")
    ) {
        $donation->method = $data->method;
        // payment_id (check#)
        if (isset($data->payment_id)) {
            // can't use empty because 0 is valid
            $donation->payment_id = $data->payment_id;
        } else {
            http_response_code(400); // bad request
            throw new Exception("[payment_id] is required.");
        }

        // amount (payment_amount)
        if (!empty($data->amount)) {
            $donation->amount = $data->amount;
        } else {
            http_response_code(400); // bad request
            throw new Exception("[amount] is required.");
        }

        // processor (payment_processor)
        if (!empty($data->processor)) {
            $donation->processor = $data->processor;
        } else {
            http_response_code(400); // bad request
            throw new Exception("[processor] is required.");
        }

        // status (payment_status)
        if (!empty($data->status)) {
            $donation->status = $data->status;
        } else {
            http_response_code(400); // bad request
            throw new Exception("[status] is required.");
        }
    }
    if (
        isset($data->method) &&
        in_array($data->method, ["check", "epayment", "cash"])
    ) {
        // date_deposited (payment_date_deposited)
        if (!empty($data->date_deposited)) {
            $donation->date_deposited = $data->date_deposited;
        } else {
            http_response_code(400); // bad request
            throw new Exception("[date_deposited] is required.");
        }
    }
    // card (with card number)
    if (isset($data->method) && $data->method == "card") {
        // cardnumber (payment)
        if (isset($data->cardnumber) && !empty($data->cardnumber)) {
            $donation->cardnumber = $data->cardnumber;
        } else {
            http_response_code(400); // bad request
            throw new Exception("Please enter a card number.");
        }

        // exp_month (payment)
        if (isset($data->exp_month) && !empty($data->exp_month)) {
            $donation->exp_month = $data->exp_month;
        } else {
            $donation->exp_month = null;
        }

        // exp_year (payment)
        if (isset($data->exp_year) && !empty($data->exp_year)) {
            $donation->exp_year = $data->exp_year;
        } else {
            $donation->exp_year = null;
        }

        // cc_securitycode (payment)
        if (isset($data->cc_securitycode) && !empty($data->cc_securitycode)) {
            $donation->cc_securitycode = $data->cc_securitycode;
        } else {
            $donation->cc_securitycode = null;
        }

        // cardtype (payment)
        if (isset($data->cardtype) && !empty($data->cardtype)) {
            $donation->cardtype = $data->cardtype;
        } else {
            $donation->cardtype = null;
        }
    }
    // cash
    if (isset($data->method) && $data->method == "cash") {
        $donation->method = $data->method;
    }
    // stripe
    if (isset($data->method) && $data->method == "stripe") {
        $donation->method = $data->method;
    }
    // in-kind
    if (isset($data->method) && $data->method == "in-kind") {
        $donation->method = $data->method;
    }
    // bill-me-later
    if (isset($data->method) && $data->method == "bill-me-later") {
        $donation->method = $data->method;
    }

    // Comments
    if (isset($data->comments) && !empty($data->comments)) {
        // Detect all caps and make sentance case
        if (mb_strtoupper($data->comments, "utf-8") == $data->comments) {
            function sentence_case($string) {
                $sentences = preg_split(
                    "/([.?!]+)/",
                    $string,
                    -1,
                    PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE,
                );
                $new_string = "";
                foreach ($sentences as $key => $sentence) {
                    $new_string .=
                        ($key & 1) == 0
                            ? ucfirst(strtolower(trim($sentence)))
                            : $sentence . " ";
                }
                return trim($new_string);
            }
            $data->comments = sentence_case($data->comments);
        }
        $donation->comments = substr($data->comments, 0, 499); //Stripe 500 character limit
    } else {
        $donation->comments = null;
    }

    // Read On-Air?
    if (isset($data->read_onair) && !empty($data->read_onair)) {
        $donation->read_onair = intval($data->read_onair);
    } else {
        $donation->read_onair = 0;
    }

    //Is the paperless communication field checked?
    if (isset($data->paperless) && !empty($data->paperless)) {
        $donation->paperless = $data->paperless;
    } else {
        $donation->paperless = 0;
    }

    // Donation Match?
    if (isset($data->donation_match) && !empty($data->donation_match)) {
        $donation->donation_match = intval($data->donation_match);
    } else {
        $donation->donation_match = 0;
    }

    // add_me
    if (isset($data->add_me) && !empty($data->add_me)) {
        $donation->add_me = $data->add_me;
    } else {
        $donation->add_me = null;
    }
    // premiums
    if (isset($data->premiums) && !empty($data->premiums)) {
        $donation->premiums = $data->premiums;
    } else {
        $donation->premiums = null;
    }
    // premiums_cart
    if (isset($data->premiums_cart) && !empty($data->premiums_cart)) {
        $donation->premiums_cart = $data->premiums_cart;
    } else {
        $donation->premiums_cart = null;
    }
    // updated
    if (isset($data->updated) && !empty($data->updated)) {
        $donation->updated = $data->updated;
    } else {
        $donation->updated = date("Y-m-d H:i:s");
    }

    if (isset($data->donor_id)) {
        $donation->donor_id = $data->donor_id;
    } else {
        $donation->donor_id = null;
    }

    // if we haven't died, try and create the donation
    $donation->create();

    // Send error if something is wrong
} catch (Exception $e) {
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage(),
    ]);
}
