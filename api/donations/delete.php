<?php
// DELETE donation
if (isset($resource)) {
    // set donation id to be deleted
    $donation_id = intval(strip_tags($resource));
}

// DELETE donations
if (!isset($resource)) {
    try { // donation_id
        if (!empty($data->donation_ids)) {
            // set donation ids to be deleted
            $donation->donation_id = $data->donation_ids;
        } else {
            throw new Exception("[donation_ids] are required.");
        }
    } catch (Exception $e) {
        http_response_code(400); // bad request
        echo json_encode(array(
            "status" => "error",
            "message" => $e->getMessage(),
        ));
        die;
    }
}
// CANCEL subscription
if (isset($data->action) && !empty($data->action)) {
    if ($data->action == "cancel") {
        try { // subscription_id
            if (!empty($data->subscription_id)) {
                // set donation ids to be deleted
                $donation->subscription_id = $data->subscription_id;
            } else {
                throw new Exception("[subscription_id] is required.");
            }
        } catch (Exception $e) {
            http_response_code(400); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
            die;
        }
        // UPDATE (CANCEL) SUBSCRIPTION
        if ($donation->cancel_stripe_subscription($data->subscription_id)) {
            echo json_encode(array(
                "status" => "success",
                "message" => "Subscription $data->subscription_id was successfully cancelled",
            ));

        } else { // Unable to cancel the subscription, tell the user.
            echo json_encode(array(
                "status" => "error",
                "message" => "Failed to cancel subscription.",
            ));
        }
    }
    die;
}
// if we haven't died, try and delete the donation
if ($donation->delete($donation_id)) { // call donation update function
    http_response_code(200); // Success!
    echo json_encode(array(
        "status" => "success",
        "message" => "Donation $donation_id was deleted.",
    ));
} else { // Unable to delete the donation, tell the user.
    http_response_code(400); // bad request
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
