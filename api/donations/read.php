<?php
// Donations
try {
    // ACL
    $access_levels = array("Admin", "Staff", "CallCenter");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    global $api_url;
    // format function
    $filesize = 0;
    $filelength = 0;
    function filesize_formatted($path)
    {
        $size = filesize($path);
        $units = array('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB');
        $power = $size > 0 ? floor(log($size, 1024)) : 0;
        return number_format($size / pow(1024, $power), 2, '.', ',') . ' ' . $units[$power];
    }

    if ($collection == "donations") {
        // READ donation
        if ((!isset($_GET["s"])) && (isset($resource))) {
            // query donation
            $stmt = $donation->readDonation($resource);
            $num = $stmt->rowCount();

            // check if more than 0 record found
            if ($num > 0) {
                // Initiate variables
                $shipping_firstname = isset($shipping_firstname) ? $shipping_firstname : NULL;
                $shipping_lastname = isset($shipping_lastname) ? $shipping_lastname : NULL;
                $shipping_address1 = isset($shipping_address1) ? $shipping_address1 : NULL;
                $shipping_address2 = isset($shipping_address2) ? $shipping_address2 : NULL;
                $shipping_city = isset($shipping_city) ? $shipping_city : NULL;
                $shipping_state = isset($shipping_state) ? $shipping_state : NULL;
                $shipping_country = isset($shipping_country) ? $shipping_country : NULL;
                $shipping_postal_code = isset($shipping_postal_code) ? $shipping_postal_code : NULL;

                // donation array
                $donation_arr = array();
                $donation_arr["records"] = array();
                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);
                    // decode comments if avail
                    if (!empty($comments)) {
                        $comments = html_entity_decode($comments); // required for php8.1+
                    }
                    // reset subscriptions array
                    $subscriptions = array();
                    // Subscriptions
                    // read subscription table for each donor id
                    $stmt1 = $donation->readSubscriptionsByTransaction_id($transaction_id);
                    // number of rows
                    $num1 = $stmt1->rowCount();
                    // check if more than 0 records found
                    if ($num1 > 0) {
                        // retrieve our table contents
                        while ($row1 = $stmt1->fetch(PDO::FETCH_ASSOC)) {
                            extract($row1);
                            // create array
                            $subscription = array(
                                "id" => $subscription_id,
                                "processor" => $processor,
                                "plan_id" => $plan_id,
                                "amount" => floatval($subscription_amount),
                                "interval" => $interval,
                                "active" => filter_var($active, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                                "date_canceled" => $date_canceled,
                                "date_created" => $date_created,
                            );
                            array_push($subscriptions, $subscription); // add premium item to premiums array
                        } // while
                    } // if
                    
                    // reset premiums array
                    $premiums_arr = array();
                    // reset premiums_ID array
                    $premiums_ID_arr = array();

                    // read premiumGifts table for each transaction
                    $stmt2 = $donation->readGifts($id);
                    // number of rows
                    $num2 = $stmt2->rowCount();
                    // check if more than 0 records found
                    if ($num2 > 0) {
                        // retrieve our table contents
                        while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                            extract($row2);
                            // create array
                            $premium_item = array(
                                "id" => intval($premium_id),
                                "name" => $name,
                                "price" => floatval($price),
                                "cog" => floatval($cog),
                                "fmv" => floatval($fmv),
                                "qty" => intval($qty),
                                "download_url" => $download_url,
                                "img_url" => $img_url,
                                "category" => $category,
                            );
                            array_push($premiums_arr, $premium_item); // add premium item to premiums array
                            array_push($premiums_ID_arr, $premium_item['id']); // add premium ID to premiums_ID array
                        } // while

                    } // if
                    // reset payments array
                    $payments_arr = array();
                    $payment_item = array();

                    // read Payments table for each donation
                    $stmt3 = $donation->readPayments($id);
                    $major_donation = false; // default status
                    $total_payments = NULL; // default
                    $total_amount_refunded = NULL; // default
                    $amount = NULL; // default
                    $amount_refunded = NULL; // default

                    // number of rows
                    $num3 = $stmt3->rowCount();
                    // check if more than 0 records found
                    if ($num3 > 0) {
                        // retrieve our table contents
                        while ($row3 = $stmt3->fetch(PDO::FETCH_ASSOC)) {
                            extract($row3);

                            # Payment_status: Paid, Partially Refunded, and Refunded
                            if ($status == "succeeded") { // transaction_status
                                $payment_status = "Paid"; // Default status
                                if (floatval($amount_refunded) > 0 && floatval($amount_refunded) < floatval($amount)) {
                                    $payment_status = "Partially Refunded";
                                }
                                if (floatval($amount_refunded) >= floatval($amount)) {
                                    $payment_status = "Refunded";
                                }

                                if (floatval($amount) >= 1000) { // major donation: if any successful payment is > 1k then the donation is major
                                    $major_donation = true;
                                }

                                // tally the total payment amount (since we're in the loop)
                                $total_payments += floatval($amount);
                                $total_amount_refunded += floatval($amount_refunded);
                            } else {
                                $payment_status = "Failed";
                            }
                            // create array
                            $payment_item = array(
                                "id" => intval($payments_id),
                                "customer_id" => $customer_id,
                                "payment_id" => $payment_id,
                                "processor" => $processor,
                                "method" => $method,
                                "card_type" => $card_type,
                                "amount" => floatval($amount),
                                "amount_refunded" => floatval($amount_refunded),
                                "status" => $payment_status,
                                "transaction_status" => $status,
                                "last4" => filter_var($last4, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                                "exp_month" => filter_var($exp_month, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                                "exp_year" => filter_var($exp_year, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                                "payment_id" => $payment_id,
                                "date_created" => $date_created,
                                "date_updated" => $date_updated,
                                "date_deposited" => $date_deposited,
                            );

                            // add premium item to premiums array
                            array_push($payments_arr, $payment_item);
                        } // while

                        # Donation_status: Paid, Unpaid, Partially Refunded, and Refunded (the total of all payment_status)
                        $donation_status = "Failed"; // Default Status
                        if (!empty($total_payments)) {
                            if ($total_payments < floatval($donation_amount)) {
                                $donation_status = "Partially Paid";
                            }
                            if ($total_payments >= floatval($donation_amount)) {
                                $donation_status = "Paid";
                            }
                            if (!empty($total_amount_refunded)) {
                                if (floatval($total_amount_refunded) > 0 && floatval($total_amount_refunded) < floatval($total_payments)) {
                                    $donation_status = "Partially Refunded";
                                }
                                if (floatval($total_amount_refunded) >= floatval($total_payments)) {
                                    $donation_status = "Refunded";
                                }
                            }
                            if (floatval($total_payments) == 0) {
                                $donation_status = "Unpaid";
                            }
                        }
                    } //if
                    else {
                        $donation_status = "Unpaid"; // Default Status
                    }
                    // reset shipments array
                    $shipments_arr = array();

                    // read shipments table for each donation
                    $stmt4 = $donation->readShipments($id);
                    // number of rows
                    $num4 = $stmt4->rowCount();
                    // check if more than 0 records found
                    if ($num4 > 0) {
                        // retrieve our table contents
                        while ($row4 = $stmt4->fetch(PDO::FETCH_ASSOC)) {
                            extract($row4);
                            // create array
                            $shipment_item = array(
                                "id" => intval($shipment_id),
                                "premium_id" => intval($premium_id),
                                "status" => $shipment_status,
                            );
                            array_push($shipments_arr, $shipment_item); // add premium item to premiums array
                        } // while
                    } //if

                    // Recording
                    // the date
                    $date = date("Ymd", strtotime($timestamp));
                    $y = date("Y", strtotime($timestamp));
                    $m = date("m", strtotime($timestamp));
                    $d = date("d", strtotime($timestamp));
                    $filename = null;
                    $filesize = null;
                    $filelength = null;
                    $duration = null;
                    $length = null;
                    foreach (glob("/var/www/$api_url/htdocs/recordings/$y/$m/$d/in-8004395732-$phone-$date-*.wav") as $filename) {
                        $dur = ltrim(shell_exec("soxi -d " . $filename . " 2>&1"), '0:');
                        $duration = substr($dur, 0, strpos($dur, "."));
                        $length = str_replace(":", "m", $duration);
                        $filesize = filesize_formatted($filename);
                        $filelength = "$length" . "s";
                        $filename = str_replace("/var/www/$api_url/htdocs", "https://$api_url", $filename);
                    }
                    // ACL: "Staff" shouldn't be able to see donor details.
                    if ($access_level == "Staff") {
                        $phone = null;
                        $email = null;
                        $recording_url = null;
                        $recording_size = null;
                        $recording_length = null;
                        $lastname = null;
                        $address1 = null;
                        $address2 = null;
                        $postal_code = null;
                        $shipping_firstname = null;
                        $shipping_lastname = null;
                        $shipping_address1 = null;
                        $shipping_address2 = null;
                        $shipping_city = null;
                        $shipping_state = null;
                        $shipping_country = null;
                        $shipping_postal_code = null;
                        $payments = null;
                        $subscriptions = null;
                    }
                    $donation_item = array(
                        "id" => intval($id),
                        "account_id" => filter_var($account_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "donor_id" => filter_var($donor_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "transaction_id" => $transaction_id,
                        "timestamp" => $timestamp,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "address1" => $address1,
                        "address2" => $address2,
                        "city" => $city,
                        "state" => $state,
                        "country" => $country,
                        "postal_code" => $postal_code,
                        "shipping_firstname" => $shipping_firstname,
                        "shipping_lastname" => $shipping_lastname,
                        "shipping_address1" => $shipping_address1,
                        "shipping_address2" => $shipping_address2,
                        "shipping_city" => $shipping_city,
                        "shipping_state" => $shipping_state,
                        "shipping_country" => $shipping_country,
                        "shipping_postal_code" => $shipping_postal_code,
                        "phone" => $phone,
                        "email" => $email,
                        "type" => $type,
                        "amount" => floatval($donation_amount),
                        "status" => $donation_status,
                        "major_donation" => $major_donation,
                        "installment" => $installment,
                        "comments" => $comments,
                        "add_me" => filter_var($add_me, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                        "read_onair" => filter_var($read_onair, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                        "donation_match" => filter_var($donation_match, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                        "show_name" => $show_name,
                        "source" => $source,
                        "campaign_id" => intval($campaign_id),
                        "campaign" => $campaign,
                        "updated" => $updated,
                        "recording_url" => $filename,
                        "recording_size" => $filesize,
                        "recording_length" => $filelength,
                        "premiums_ID" => $premiums_ID_arr,
                        "premiums" => $premiums_arr,
                        "payments" => $payments_arr,
                        "shipments" => $shipments_arr,
                        "subscriptions" => $subscriptions,
                    );
                    // Push the donation item into the donation array
                    // Filter by paid status
                    if (isset($_GET["status"])) {
                        if ($paid_status == "Paid" && $_GET["status"] == "paid") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Unpaid" && $_GET["status"] == "unpaid") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Partially Paid" && $_GET["status"] == "partiallypaid") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Partially Refunded" && $_GET["status"] == "partiallyrefunded") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Refunded" && $_GET["status"] == "refunded") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                    } else { // don't filter, push all results
                        array_push($donation_arr["records"], $donation_item);
                    }
                } // end while for $stmt

                echo json_encode($donation_arr);
            } else {
                http_response_code(404); // not found
                echo json_encode(array(
                    "message" => "No donations found.",
                ));
            }
        }
        // READ donations
        if ((!isset($_GET["s"])) && (!isset($resource))) {

            // Check to see if we should filter by CAMPAIGN_ID
            if (!empty($_GET['campaign_id']) && empty($_GET['start']) && empty($_GET['end'])) {
                $campaign_id = intval($_GET['campaign_id']);

                $stmt = $donation->readbyCampaign($campaign_id);
                $num = $stmt->rowCount();
            }
            // Check to see if we should filter by DATE OR DATE AND CAMPAIGN_ID
            else if (!empty($_GET['start']) && !empty($_GET['end'])) {
                $startdate = date('Y-m-d', strtotime($_GET['start']));
                $enddate = date('Y-m-d', strtotime($_GET['end']));

                // filter donations by DATE and CAMPAIGN_ID
                if (!empty($_GET['campaign_id'])) {
                    $startdate = date('Y-m-d', strtotime($_GET['start']));
                    $enddate = date('Y-m-d', strtotime($_GET['end']));
                    $campaign_id = intval($_GET['campaign_id']);

                    $stmt = $donation->readDateandCampaign($startdate, $enddate, $campaign_id);
                    $num = $stmt->rowCount();
                } else { // filter donations by DATE
                    $stmt = $donation->readDate($startdate, $enddate, $access_level);
                    $num = $stmt->rowCount();
                }
            } else {
                // read most recent donations
                $stmt = $donation->readDonations();
                $num = $stmt->rowCount();
            }

            // check if more than 0 record found
            if ($num > 0) {
                // Initiate variables
                $shipping_firstname = isset($shipping_firstname) ? $shipping_firstname : NULL;
                $shipping_lastname = isset($shipping_lastname) ? $shipping_lastname : NULL;
                $shipping_address1 = isset($shipping_address1) ? $shipping_address1 : NULL;
                $shipping_address2 = isset($shipping_address2) ? $shipping_address2 : NULL;
                $shipping_city = isset($shipping_city) ? $shipping_city : NULL;
                $shipping_state = isset($shipping_state) ? $shipping_state : NULL;
                $shipping_country = isset($shipping_country) ? $shipping_country : NULL;
                $shipping_postal_code = isset($shipping_postal_code) ? $shipping_postal_code : NULL;


                // donation array
                $donation_arr = array();
                $donation_arr["records"] = array();
                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);
                    // decode comments if avail
                    if (!empty($comments)) {
                        $comments = html_entity_decode($comments); // required for php8.1+
                    }
                    // reset subscriptions array
                    $subscriptions = array();
                    // Subscriptions
                    // read subscription table for each donor id
                    $stmt1 = $donation->readSubscriptionsByTransaction_id($transaction_id);
                    // number of rows
                    $num1 = $stmt1->rowCount();
                    // check if more than 0 records found
                    if ($num1 > 0) {
                        // retrieve our table contents
                        while ($row1 = $stmt1->fetch(PDO::FETCH_ASSOC)) {
                            extract($row1);
                            // create array
                            $subscription = array(
                                "id" => $subscription_id,
                                "processor" => $processor,
                                "plan_id" => $plan_id,
                                "amount" => floatval($subscription_amount),
                                "interval" => $interval,
                                "active" => filter_var($active, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                                "date_canceled" => $date_canceled,
                                "date_created" => $date_created,
                            );
                            array_push($subscriptions, $subscription); // add premium item to premiums array
                        } // while
                    } // if

                    // reset premiums array
                    $premiums_arr = array();
                    // reset premiums_ID array
                    $premiums_ID_arr = array();

                    // read premiumGifts table for each transaction
                    $stmt2 = $donation->readGifts($id);
                    // number of rows
                    $num2 = $stmt2->rowCount();
                    // check if more than 0 records found
                    if ($num2 > 0) {
                        // retrieve our table contents
                        while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                            extract($row2);
                            // create array
                            $premium_item = array(
                                "id" => intval($premium_id),
                                "name" => $name,
                                "price" => floatval($price),
                                "cog" => floatval($cog),
                                "fmv" => floatval($fmv),
                                "qty" => floatval($qty),
                                "download_url" => $download_url,
                                "img_url" => $img_url,
                                "category" => $category,
                                "firstname" => $firstname,
                                "lastname" => $lastname,
                                "address1" => $address1,
                                "address2" => $address2,
                                "city" => $city,
                                "state" => $state,
                                "country" => $country,
                                "postal_code" => $postal_code,
                                "shipment_status" => $shipment_status,
                            );
                            array_push($premiums_arr, $premium_item); // add premium item to premiums array
                            array_push($premiums_ID_arr, $premium_item['id']); // add premium ID to premiums_ID array
                        } // while

                    } // if

                    // reset payments array
                    $payments_arr = array();
                    $payment_item = array();

                    // read Payments table for each donation
                    $stmt3 = $donation->readPayments($id);
                    $major_donation = false; // default status
                    $total_payments = NULL; // default
                    $total_amount_refunded = NULL; // default
                    $amount = NULL; // default
                    $amount_refunded = NULL; // default

                    // number of rows
                    $num3 = $stmt3->rowCount();
                    // check if more than 0 records found
                    if ($num3 > 0) {
                        // retrieve our table contents
                        while ($row3 = $stmt3->fetch(PDO::FETCH_ASSOC)) {
                            extract($row3);

                            # Payment_status: Paid, Partially Refunded, and Refunded
                            if ($status == "succeeded") { // transaction_status
                                $payment_status = "Paid"; // Default status
                                if (floatval($amount_refunded) > 0 && floatval($amount_refunded) < floatval($amount)) {
                                    $payment_status = "Partially Refunded";
                                }
                                if (floatval($amount_refunded) >= floatval($amount)) {
                                    $payment_status = "Refunded";
                                }

                                if (floatval($amount) >= 1000) { // major donation: if any successful payment is > 1k then the donation is major
                                    $major_donation = true;
                                }

                                // tally the total payment amount (since we're in the loop)
                                $total_payments += floatval($amount);
                                $total_amount_refunded += floatval($amount_refunded);
                            } else {
                                $payment_status = "Failed";
                            }
                            // create array
                            $payment_item = array(
                                "id" => intval($payments_id),
                                "customer_id" => $customer_id,
                                "payment_id" => $payment_id,
                                "processor" => $processor,
                                "method" => $method,
                                "card_type" => $card_type,
                                "amount" => floatval($amount),
                                "amount_refunded" => floatval($amount_refunded),
                                "status" => $payment_status,
                                "transaction_status" => $status,
                                "last4" => filter_var($last4, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                                "exp_month" => filter_var($exp_month, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                                "exp_year" => filter_var($exp_year, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                                "payment_id" => $payment_id,
                                "date_created" => $date_created,
                                "date_updated" => $date_updated,
                                "date_deposited" => $date_deposited,
                            );

                            // add premium item to premiums array
                            array_push($payments_arr, $payment_item);
                        } // while

                        # Donation_status: Paid, Unpaid, Partially Refunded, and Refunded (the total of all payment_status)
                        $donation_status = "Failed"; // Default Status
                        if (!empty($total_payments)) {
                            if ($total_payments < floatval($donation_amount)) {
                                $donation_status = "Partially Paid";
                            }
                            if ($total_payments >= floatval($donation_amount)) {
                                $donation_status = "Paid";
                            }
                            if (!empty($total_amount_refunded)) {
                                if (floatval($total_amount_refunded) > 0 && floatval($total_amount_refunded) < floatval($total_payments)) {
                                    $donation_status = "Partially Refunded";
                                }
                                if (floatval($total_amount_refunded) >= floatval($total_payments)) {
                                    $donation_status = "Refunded";
                                }
                            }
                            if (floatval($total_payments) == 0) {
                                $donation_status = "Unpaid";
                            }
                        }
                    } //if
                    else {
                        $donation_status = "Unpaid"; // Default Status
                    }

                    // reset shipments array
                    $shipments_arr = array();

                    // read shipments table for each donation
                    $stmt4 = $donation->readShipments($id);
                    // number of rows
                    $num4 = $stmt4->rowCount();
                    // check if more than 0 records found
                    if ($num4 > 0) {
                        // retrieve our table contents
                        while ($row4 = $stmt4->fetch(PDO::FETCH_ASSOC)) {
                            extract($row4);
                            // create array
                            $shipment_item = array(
                                "id" => intval($shipment_id),
                                "premium_id" => intval($premium_id),
                                "status" => $shipment_status,
                            );
                            array_push($shipments_arr, $shipment_item); // add shipment item to shipment array
                        } // while
                    } //if

                    // Recording
                    // the date
                    $date = date("Ymd", strtotime($timestamp));
                    $y = date("Y", strtotime($timestamp));
                    $m = date("m", strtotime($timestamp));
                    $d = date("d", strtotime($timestamp));
                    $filename = null;
                    $filesize = null;
                    $filelength = null;
                    $duration = null;
                    $length = null;
                    foreach (glob("/var/www/$api_url/htdocs/recordings/$y/$m/$d/in-8004395732-$phone-$date-*.wav") as $filename) {
                        $dur = ltrim(shell_exec("soxi -d " . $filename . " 2>&1"), '0:');
                        $duration = substr($dur, 0, strpos($dur, "."));
                        $length = str_replace(":", "m", $duration);
                        $filesize = filesize_formatted($filename);
                        $filelength = "$length" . "s";
                        $filename = str_replace("/var/www/$api_url/htdocs", "https://$api_url", $filename);
                    }
                    // ACL: "Staff" shouldn't be able to see donor details.
                    if ($access_level == "Staff") {
                        $phone = null;
                        $email = null;
                        $recording_url = null;
                        $recording_size = null;
                        $recording_length = null;
                        $lastname = null;
                        $address1 = null;
                        $address2 = null;
                        $postal_code = null;
                        $shipping_firstname = null;
                        $shipping_lastname = null;
                        $shipping_address1 = null;
                        $shipping_address2 = null;
                        $shipping_city = null;
                        $shipping_state = null;
                        $shipping_country = null;
                        $shipping_postal_code = null;
                        $payments = null;
                        $subscriptions = null;
                    }
                    // Build the Donation array
                    $donation_item = array(
                        "id" => intval($id),
                        "account_id" => filter_var($account_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "donor_id" => filter_var($donor_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "transaction_id" => $transaction_id,
                        "timestamp" => $timestamp,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "address1" => $address1,
                        "address2" => $address2,
                        "city" => $city,
                        "state" => $state,
                        "country" => $country,
                        "postal_code" => $postal_code,
                        "shipping_firstname" => $shipping_firstname,
                        "shipping_lastname" => $shipping_lastname,
                        "shipping_address1" => $shipping_address1,
                        "shipping_address2" => $shipping_address2,
                        "shipping_city" => $shipping_city,
                        "shipping_state" => $shipping_state,
                        "shipping_country" => $shipping_country,
                        "shipping_postal_code" => $shipping_postal_code,
                        "phone" => $phone,
                        "email" => $email,
                        "type" => $type,
                        "amount" => floatval($donation_amount),
                        "status" => $donation_status,
                        "major_donation" => $major_donation,
                        "installment" => $installment,
                        "comments" => $comments,
                        "add_me" => filter_var($add_me, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                        "read_onair" => filter_var($read_onair, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                        "donation_match" => filter_var($donation_match, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                        "show_name" => $show_name,
                        "source" => $source,
                        "campaign_id" => intval($campaign_id),
                        "campaign" => $campaign,
                        "updated" => $updated,
                        "recording_url" => $filename,
                        "recording_size" => $filesize,
                        "recording_length" => $filelength,
                        "premiums_ID" => $premiums_ID_arr,
                        "premiums" => $premiums_arr,
                        "payments" => $payments_arr,
                        "shipments" => $shipments_arr,
                        "subscriptions" => $subscriptions,
                    );
                    // Push the donation item into the donation array
                    // Filter by paid status
                    if (isset($_GET["status"])) {
                        if ($paid_status == "Paid" && $_GET["status"] == "paid") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Unpaid" && $_GET["status"] == "unpaid") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Partially Paid" && $_GET["status"] == "partiallypaid") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Partially Refunded" && $_GET["status"] == "partiallyrefunded") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Refunded" && $_GET["status"] == "refunded") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                    } else { // don't filter, push all results
                        array_push($donation_arr["records"], $donation_item);
                    }
                } // end while for $stmt

                echo json_encode($donation_arr);
            } else {
                http_response_code(200); // empty array
                echo json_encode(array(
                    "records" => [],
                ));
            }
        }
        // SEARCH donations
        if ((isset($_GET["s"])) && (!isset($resource))) {
            $keywords = isset($_GET["s"]) ? $_GET["s"] : json_encode(array("message" => "Please provide a search term."));

            // query donations
            $stmt = $donation->searchDonations($keywords);
            $num = $stmt->rowCount();

            // check if more than 0 record found
            if ($num > 0) {
                // Initiate variables
                $shipping_firstname = isset($shipping_firstname) ? $shipping_firstname : NULL;
                $shipping_lastname = isset($shipping_lastname) ? $shipping_lastname : NULL;
                $shipping_address1 = isset($shipping_address1) ? $shipping_address1 : NULL;
                $shipping_address2 = isset($shipping_address2) ? $shipping_address2 : NULL;
                $shipping_city = isset($shipping_city) ? $shipping_city : NULL;
                $shipping_state = isset($shipping_state) ? $shipping_state : NULL;
                $shipping_country = isset($shipping_country) ? $shipping_country : NULL;
                $shipping_postal_code = isset($shipping_postal_code) ? $shipping_postal_code : NULL;

                // donation array
                $donation_arr = array();
                $donation_arr["records"] = array();
                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);
                    // decode comments if avail
                    if (!empty($comments)) {
                        $comments = html_entity_decode($comments); // required for php8.1+
                    }
                    // reset subscriptions array
                    $subscriptions = array();
                    // Subscriptions
                    // read subscription table for each donor id
                    $stmt1 = $donation->readSubscriptionsByTransaction_id($transaction_id);
                    // number of rows
                    $num1 = $stmt1->rowCount();
                    // check if more than 0 records found
                    if ($num1 > 0) {
                        // retrieve our table contents
                        while ($row1 = $stmt1->fetch(PDO::FETCH_ASSOC)) {
                            extract($row1);
                            // create array
                            $subscription = array(
                                "id" => $subscription_id,
                                "processor" => $processor,
                                "plan_id" => $plan_id,
                                "amount" => floatval($subscription_amount),
                                "interval" => $interval,
                                "active" => filter_var($active, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                                "date_canceled" => $date_canceled,
                                "date_created" => $date_created,
                            );
                            array_push($subscriptions, $subscription); // add premium item to premiums array
                        } // while
                    } // if

                    // reset premiums array
                    $premiums_arr = array();
                    // reset premiums_ID array
                    $premiums_ID_arr = array();

                    // read premiumGifts table for each transaction
                    $stmt2 = $donation->readGifts($id);
                    // number of rows
                    $num2 = $stmt2->rowCount();
                    // check if more than 0 records found
                    if ($num2 > 0) {
                        // retrieve our table contents
                        while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                            extract($row2);
                            // create array
                            $premium_item = array(
                                "id" => intval($premium_id),
                                "name" => $name,
                                "price" => intval($price),
                                "cog" => intval($cog),
                                "fmv" => intval($fmv),
                                "qty" => intval($qty),
                                "download_url" => $download_url,
                                "img_url" => $img_url,
                                "category" => $category,
                            );
                            array_push($premiums_arr, $premium_item); // add premium item to premiums array
                            array_push($premiums_ID_arr, $premium_item['id']); // add premium ID to premiums_ID array
                        } // while

                    } // if
                    // reset payments array
                    $payments_arr = array();
                    $payment_item = array();

                    // read Payments table for each donation
                    $stmt3 = $donation->readPayments($id);
                    $major_donation = false; // default status
                    $total_payments = NULL; // default
                    $total_amount_refunded = NULL; // default
                    $amount = NULL; // default
                    $amount_refunded = NULL; // default

                    // number of rows
                    $num3 = $stmt3->rowCount();
                    // check if more than 0 records found
                    if ($num3 > 0) {
                        // retrieve our table contents
                        while ($row3 = $stmt3->fetch(PDO::FETCH_ASSOC)) {
                            extract($row3);

                            # Payment_status: Paid, Partially Refunded, and Refunded
                            if ($status == "succeeded") { // transaction_status
                                $payment_status = "Paid"; // Default status
                                if (floatval($amount_refunded) > 0 && floatval($amount_refunded) < floatval($amount)) {
                                    $payment_status = "Partially Refunded";
                                }
                                if (floatval($amount_refunded) >= floatval($amount)) {
                                    $payment_status = "Refunded";
                                }

                                if (floatval($amount) >= 1000) { // major donation: if any successful payment is > 1k then the donation is major
                                    $major_donation = true;
                                }

                                // tally the total payment amount (since we're in the loop)
                                $total_payments += floatval($amount);
                                $total_amount_refunded += floatval($amount_refunded);
                            } else {
                                $payment_status = "Failed";
                            }
                            // create array
                            $payment_item = array(
                                "id" => intval($payments_id),
                                "customer_id" => $customer_id,
                                "payment_id" => $payment_id,
                                "processor" => $processor,
                                "method" => $method,
                                "card_type" => $card_type,
                                "amount" => floatval($amount),
                                "amount_refunded" => floatval($amount_refunded),
                                "status" => $payment_status,
                                "transaction_status" => $status,
                                "last4" => filter_var($last4, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                                "exp_month" => filter_var($exp_month, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                                "exp_year" => filter_var($exp_year, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                                "payment_id" => $payment_id,
                                "date_created" => $date_created,
                                "date_updated" => $date_updated,
                                "date_deposited" => $date_deposited,
                            );

                            // add premium item to premiums array
                            array_push($payments_arr, $payment_item);
                        } // while

                        # Donation_status: Paid, Unpaid, Partially Refunded, and Refunded (the total of all payment_status)
                        $donation_status = "Failed"; // Default Status
                        if (!empty($total_payments)) {
                            if ($total_payments < floatval($donation_amount)) {
                                $donation_status = "Partially Paid";
                            }
                            if ($total_payments >= floatval($donation_amount)) {
                                $donation_status = "Paid";
                            }
                            if (!empty($total_amount_refunded)) {
                                if (floatval($total_amount_refunded) > 0 && floatval($total_amount_refunded) < floatval($total_payments)) {
                                    $donation_status = "Partially Refunded";
                                }
                                if (floatval($total_amount_refunded) >= floatval($total_payments)) {
                                    $donation_status = "Refunded";
                                }
                            }
                            if (floatval($total_payments) == 0) {
                                $donation_status = "Unpaid";
                            }
                        }
                    } //if
                    else {
                        $donation_status = "Unpaid"; // Default Status
                    }
                    // reset shipments array
                    $shipments_arr = array();

                    // read shipments table for each donation
                    $stmt4 = $donation->readShipments($id);
                    // number of rows
                    $num4 = $stmt4->rowCount();
                    // check if more than 0 records found
                    if ($num4 > 0) {
                        // retrieve our table contents
                        while ($row4 = $stmt4->fetch(PDO::FETCH_ASSOC)) {
                            extract($row4);
                            // create array
                            $shipment_item = array(
                                "id" => intval($shipment_id),
                                "premium_id" => intval($premium_id),
                                "status" => $shipment_status,
                            );
                            array_push($shipments_arr, $shipment_item); // add premium item to premiums array
                        } // while
                    } //if
                    // Build the Donation array
                    if (empty($payment_item["status"])) {
                        $paid_status = "Unpaid";
                    }
                    // Recording
                    // the date
                    $date = date("Ymd", strtotime($timestamp));
                    $y = date("Y", strtotime($timestamp));
                    $m = date("m", strtotime($timestamp));
                    $d = date("d", strtotime($timestamp));
                    $filename = null;
                    $filesize = null;
                    $filelength = null;
                    $duration = null;
                    $length = null;
                    foreach (glob("/var/www/$api_url/htdocs/recordings/$y/$m/$d/in-8004395732-$phone-$date-*.wav") as $filename) {
                        $dur = ltrim(shell_exec("soxi -d " . $filename . " 2>&1"), '0:');
                        $duration = substr($dur, 0, strpos($dur, "."));
                        $length = str_replace(":", "m", $duration);
                        $filesize = filesize_formatted($filename);
                        $filelength = "$length" . "s";
                        $filename = str_replace("/var/www/$api_url/htdocs", "https://$api_url", $filename);
                    }
                    // ACL: "Staff" shouldn't be able to see donor details.
                    if ($access_level == "Staff") {
                        $phone = null;
                        $email = null;
                        $recording_url = null;
                        $recording_size = null;
                        $recording_length = null;
                        $lastname = null;
                        $address1 = null;
                        $address2 = null;
                        $postal_code = null;
                        $shipping_firstname = null;
                        $shipping_lastname = null;
                        $shipping_address1 = null;
                        $shipping_address2 = null;
                        $shipping_city = null;
                        $shipping_state = null;
                        $shipping_country = null;
                        $shipping_postal_code = null;
                        $payments = null;
                        $subscriptions = null;
                    }
                    // Build the Donation array
                    $donation_item = array(
                        "id" => intval($id),
                        "account_id" => filter_var($account_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "donor_id" => filter_var($donor_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "transaction_id" => $transaction_id,
                        "timestamp" => $timestamp,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "address1" => $address1,
                        "address2" => $address2,
                        "city" => $city,
                        "state" => $state,
                        "country" => $country,
                        "postal_code" => $postal_code,
                        "shipping_firstname" => $shipping_firstname,
                        "shipping_lastname" => $shipping_lastname,
                        "shipping_address1" => $shipping_address1,
                        "shipping_address2" => $shipping_address2,
                        "shipping_city" => $shipping_city,
                        "shipping_state" => $shipping_state,
                        "shipping_country" => $shipping_country,
                        "shipping_postal_code" => $shipping_postal_code,
                        "phone" => $phone,
                        "email" => $email,
                        "type" => $type,
                        "amount" => floatval($donation_amount),
                        "status" => $donation_status,
                        "major_donation" => $major_donation,
                        "installment" => $installment,
                        "comments" => $comments,
                        "add_me" => filter_var($add_me, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                        "read_onair" => filter_var($read_onair, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                        "donation_match" => filter_var($donation_match, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                        "show_name" => $show_name,
                        "source" => $source,
                        "campaign_id" => intval($campaign_id),
                        "campaign" => $campaign,
                        "updated" => $updated,
                        "recording_url" => $filename,
                        "recording_size" => $filesize,
                        "recording_length" => $filelength,
                        "premiums_ID" => $premiums_ID_arr,
                        "premiums" => $premiums_arr,
                        "payments" => $payments_arr,
                        "shipments" => $shipments_arr,
                        "subscriptions" => $subscriptions,
                    );
                    // Push the donation item into the donation array
                    // Filter by paid status
                    if (isset($_GET["status"])) {
                        if ($paid_status == "Paid" && $_GET["status"] == "paid") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Unpaid" && $_GET["status"] == "unpaid") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Partially Paid" && $_GET["status"] == "partiallypaid") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Partially Refunded" && $_GET["status"] == "partiallyrefunded") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                        if ($paid_status == "Refunded" && $_GET["status"] == "refunded") {
                            array_push($donation_arr["records"], $donation_item);
                        }
                    } else { // don't filter, push all results
                        array_push($donation_arr["records"], $donation_item);
                    }
                } // end while for $stmt

                echo json_encode($donation_arr);
            } else {
                http_response_code(404); // not found
                echo json_encode(array(
                    "message" => "No donations found.",
                ));
            }
        }
    }
} catch (Exception $e) {
    //  display message
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
