<?php
// Set donation property values
// [Required Section]
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }
    // source
    if (!empty($data->source) && in_array($data->source, array(
        'StationAdmin',
        'CallCenter',
        'PhoneRoom',
        'WebSite',
        'Testing',
        'PaySol',
        'Allegiance',
    ), true)) {
        $donation->source = $data->source;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[source] is required and must be of type ['StationAdmin', 'CallCenter', 'PhoneRoom', 'WebSite', 'Testing', 'PaySol', 'Allegiance'].");
    }

    // donation_id
    if (!empty($data->donation_id)) {
        $donation->donation_id = $data->donation_id;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[donation_id] is required.");
    }

    // pledge_amount
    if (!empty($data->amount)) {
        $donation->amount = $data->amount;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[amount] is required.");
    }

    // installment
    if (!empty($data->installment) && in_array($data->installment, array(
        'One-Time',
        'Monthly',
    ), true)) {
        $donation->installment = $data->installment;
    } else {
        http_response_code(400); // bad request
        throw new Exception("[installment] is required and must be of type One-Time or Monthly");
    }

    // [Optional Section]

    // Type
    if (isset($data->type) && !empty($data->type)) {
        $donation->type = $data->type;
    } else {
        $donation->type = "pledge";
    }
    // Email
    if (isset($data->email) && !empty($data->email)) {
        // email
        if (!empty($data->email) && filter_var($data->email, FILTER_VALIDATE_EMAIL)) {
            $donation->email = $data->email;
        } else {
            http_response_code(400); // bad request
            throw new Exception("[email] is in an invalid format.");
        }
    } else {
        $donation->email = null;
    }
    // address2
    if (isset($data->address2) && !empty($data->address2)) {
        $donation->address2 = $data->address2;
    } else {
        $donation->address2 = null;
    }
    // shipping_firstname
    if (isset($data->shipping_firstname) && !empty($data->shipping_firstname)) {
        $donation->shipping_firstname = $data->shipping_firstname;
    } else {
        $donation->shipping_firstname = null;
    }
    // shipping_lastname
    if (isset($data->shipping_lastname) && !empty($data->shipping_lastname)) {
        $donation->shipping_lastname = $data->shipping_lastname;
    } else {
        $donation->shipping_lastname = null;
    }
    // shipping_address1
    if (isset($data->shipping_address1) && !empty($data->shipping_address1)) {
        $donation->shipping_address1 = $data->shipping_address1;
    } else {
        $donation->shipping_address1 = null;
    }
    // shipping_address2
    if (isset($data->shipping_address2) && !empty($data->shipping_address2)) {
        $donation->shipping_address2 = $data->shipping_address2;
    } else {
        $donation->shipping_address2 = null;
    }
    // shipping_city
    if (isset($data->shipping_city) && !empty($data->shipping_city)) {
        $donation->shipping_city = $data->shipping_city;
    } else {
        $donation->shipping_city = null;
    }
    // shipping_state
    if (isset($data->shipping_state) && !empty($data->shipping_state)) {
        $donation->shipping_state = $data->shipping_state;
    } else {
        $donation->shipping_state = null;
    }
    // shipping_country
    if (isset($data->shipping_country) && !empty($data->shipping_country)) {
        $donation->shipping_country = $data->shipping_country;
    } else {
        $donation->shipping_country = null;
    }
    // shipping_postal_code
    if (isset($data->shipping_postal_code) && !empty($data->shipping_postal_code)) {
        $donation->shipping_postal_code = $data->shipping_postal_code;
    } else {
        $donation->shipping_postal_code = null;
    }
    // cardnumber
    if (isset($data->cardnumber) && !empty($data->cardnumber)) {
        $donation->cardnumber = $data->cardnumber;
    } else {
        $donation->cardnumber = null;
    }
    // exp_month
    if (isset($data->exp_month) && !empty($data->exp_month)) {
        $donation->exp_month = $data->exp_month;
    } else {
        $donation->exp_month = null;
    }
    // exp_year
    if (isset($data->exp_year) && !empty($data->exp_year)) {
        $donation->exp_year = $data->exp_year;
    } else {
        $donation->exp_year = null;
    }
    // cc_securitycode
    if (isset($data->cc_securitycode) && !empty($data->cc_securitycode)) {
        $donation->cc_securitycode = $data->cc_securitycode;
    } else {
        $donation->cc_securitycode = null;
    }
    // cardtype
    if (isset($data->cardtype) && !empty($data->cardtype)) {
        $donation->cardtype = $data->cardtype;
    } else {
        $donation->cardtype = null;
    }
    // Comments
    if (isset($data->comments) && !empty($data->comments)) {
        $donation->comments = substr($data->comments, 0, 499); //Stripe 500 character limit
    } else {
        $data->comments = null;
    }
    // Read On-Air?
    if ($data->read_onair === true) {
        $donation->read_onair = 1;
    }
    if (empty($data->read_onair)) {
        $donation->read_onair = 0;
    }

    // Donation Match?
    if ($data->donation_match == true) {
        $donation->donation_match = 1;
    }
    if (empty($data->donation_match)) {
        $donation->donation_match = 0;
    }

    // premiums
    if (isset($data->premiums) && !empty($data->premiums)) {
        $donation->premiums = $data->premiums;
    } else {
        $donation->premiums = null;
    }
    // premiums_cart
    if (isset($data->premiums_cart) && !empty($data->premiums_cart)) {
        $donation->premiums_cart = $data->premiums_cart;
    } else {
        $donation->premiums_cart = null;
    }
    // campaign_id
    if (isset($data->campaign_id) && !empty($data->campaign_id)) {
        $donation->campaign_id = $data->campaign_id;
    } else {
        $donation->campaign_id = null;
    }
    // transaction_id
    if (isset($data->transaction_id) && !empty($data->transaction_id)) {
        $donation->transaction_id = $data->transaction_id;
    } else {
        $donation->transaction_id = null;
    }

    // if we haven't died, try and update the donation
    $donation->update();

    // Send error if incoming JSON invalid
} catch (Exception $e) {
    //  display message
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
