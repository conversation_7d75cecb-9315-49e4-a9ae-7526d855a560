<?php
// [EMAIL] [CREATE]

//set global variables
global $error;
//set Success class
//class Success extends Exception {};

//BULK (POST)
try {
    // ACL
    $access_levels = ["Admin"];
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }
    // check to see if there is a body
    if (empty($data)) {
        http_response_code(400); // bad param
        throw new Exception("Please provide a JSON body");
    }
    // intialize success counter
    $successcount = 0;

    if (!empty($data->transaction_ids)) {
        // Check if transaction_ids are present and not empty
        $transaction_ids = $data->transaction_ids;
        // total number of submitted emails
        $totalcount = count($data->transaction_ids);
    } elseif ($data->type === "tax_letter") {
        $totalcount = count($data->donors);
    } else {
        http_response_code(400); // bad param
        throw new Exception("[transaction_ids] must be correctly specified.");
    }
    if (!empty($data->type)) {
        // Check if type is present and not empty
        $type = $data->type; // set type
    } else {
        http_response_code(400); // bad param
        throw new Exception("[type] must be correctly specified.");
    }

    // initialize transactions array
    $transactions_arr = [];

    // try and send
    if (!empty($type)) {
        if (!empty($transaction_ids)) {
            foreach ($transaction_ids as $transaction_id) {
                // loop through list [transaction_ids]
                $sendMessage = $email_msg->sendMessage($type, $transaction_id); // call sendMessage function
                if ($sendMessage[1] == true) {
                    //boolean
                    $successcount++; // number of successes
                    $transaction = [
                        "transaction_id" => $transaction_id,
                        "status" => "success",
                        "id" => $sendMessage[0],
                        "email" => $sendMessage[2],
                    ];
                    array_push($transactions_arr, $transaction);
                } else {
                    // couldn't send the message tell the user.
                    $transaction = [
                        "transaction_id" => $transaction_id,
                        "status" => "error",
                        "id" => $sendMessage[0],
                        "message" => $sendMessage[2],
                    ];
                    array_push($transactions_arr, $transaction);
                }
            }
        } elseif ($type === "tax_letter") {
            foreach ($data->donors as $donor) {
                // loop through list [donor]
                $sendMessage = $email_msg->sendTaxLetter($donor);
                if ($sendMessage[1] == true) {
                    //boolean
                    $successcount++; // number of successes
                    $transaction = [
                        "status" => "success",
                        "name" => $sendMessage[0],
                        "email" => $sendMessage[2],
                    ];
                    array_push($transactions_arr, $transaction);
                } else {
                    // couldn't send the message tell the user.
                    $transaction = [
                        "status" => "error",
                        "name" => $sendMessage[0],
                        "message" => $sendMessage[2],
                    ];
                    array_push($transactions_arr, $transaction);
                }
            }
        }

        if ($totalcount == $successcount) {
            echo json_encode([
                "status" => "success",
                "message" => "$successcount emails successfully sent.",
                "records" => $transactions_arr,
            ]);
        } elseif ($successcount == 0 || $sendMessage[1] == false) {
            echo json_encode([
                "status" => "error",
                "message" => "$successcount out of $totalcount emails successfully sent",
                "records" => $transactions_arr,
            ]);
        } else {
            echo json_encode([
                "status" => "mixed",
                "message" => "$successcount out of $totalcount emails successfully sent.",
                "records" => $transactions_arr,
            ]);
        }
    } else {
        // couldn't send the message tell the user.
        http_response_code(400); // bad param
        throw new Exception("[type] or [transaction_id] are incorrect");
    }
} catch (Exception $e) {
    //  display message
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage(),
    ]);
    die();
}

