<?php
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }
    //TODO
    throw new Exception("This function hasn't been created yet");
} catch (Exception $e) {
    //  display message
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
