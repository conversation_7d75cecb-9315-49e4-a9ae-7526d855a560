<?php
/* **
 * Send Authorization (login.php) POST
 * Receive Refresh Token // TODO
 * Send Refresh Token // TODO
 * ADMIN can Create new user // done
 * Recieve Access Token( login.php)
 * Send Access Token (script that requries token.php)
 * Recieve Resource (script)
 */

// Access tokens carry the necessary information to access a resource directly. Access tokens usually have an expiration date and are short-lived. (60 Seconds).
// Refresh tokens carry the information necessary to get a new access token. Refresh tokens are usually subject to strict storage requirements to ensure they are not leaked. They can also be blacklisted by the authorization server.

// Authentication confirms that users are who they say they are. (Username via password or oAuth (google))
// Authorization gives those users permission to access a resource. (JWT token)
//
// Authentication is what confirms identity: the Username via password or oAuth (google) that generates the authorization
// Authorization is what grants access: the JWT token that's sent with every request
// generate json web token
use Firebase\JWT\JWT;

try {
    // Authentication oAuth2

    // get authenticate code from Google OAuth Flow
    // Accept either JSON or POST FormData
    if (!empty($data->credential)) {
        $id_token = $data->credential;
    }
    if (isset($_POST["credential"])) {
        $id_token = $_POST["credential"];
    }
    if (isset($id_token)) {
        // using OAuth JWT
        $client = new Google_Client(["client_id" => $clientID]); // Specify the client id of the app that accesses the backend
        $payload = $client->verifyIdToken($id_token); // The verifyIdToken function verifies the JWT signature, the aud claim, the exp claim, and the iss claim.
        if ($payload) {
            // assign email var
            $user->email = $payload["email"];
            //use only domain
            $host_names = explode(".", $_SERVER["SERVER_NAME"]);
            $bottom_host_name =
                $host_names[count($host_names) - 2] .
                "." .
                $host_names[count($host_names) - 1];

            if ($payload["hd"] == $bottom_host_name) {
                // verify domain is kpfa.org
                // search user table by email
                $num = $user->search()->rowCount();
                if ($num == 0) {
                    // user not found, try and create user.
                    $user->firstname = $payload["given_name"];
                    $user->lastname = $payload["family_name"];
                    $user->password = random_bytes(64); // set random password
                    $user->access_level = "Staff";
                    $user->create(); // Create user, function returns true if success, else error
                    // reissue search to reread DB and retrieve user contents
                    $row = $user->search()->fetch(PDO::FETCH_ASSOC);
                }
                if ($num > 0) {
                    // user found by email
                    // retrieve user contents
                    $row = $user->search()->fetch(PDO::FETCH_ASSOC);
                }
                extract($row);
                $jwt_token = [
                    "iss" => $iss,
                    "aud" => $aud,
                    "iat" => $iat,
                    "nbf" => $nbf,
                    "exp" => $exp,
                    "data" => [
                        "id" => intval($id),
                        "firstname" => $payload["given_name"],
                        "lastname" => $payload["family_name"],
                        "picture" => $payload["picture"],
                        "email" => $email,
                        "access_level" => $access_level,
                    ],
                ];

                // set response code - OK
                http_response_code(200);

                // generate jwt Access Token
                $jwt = JWT::encode($jwt_token, $key, "HS512");
                echo json_encode([
                    "message" => "Successful login.",
                    "type" => "Access Token", // TODO Should be Refresh Token
                    "jwt" => $jwt,
                ]);

                // update last_login timestamp
                $user->update_login();

                die(); // we're done
            } else {
                // email domain is invalid
                http_response_code(400); // Bad request
                throw new Exception("[email] domain must match API");
            }
        } else {
            http_response_code(401); // unauthorized
            throw new Exception("[token] is invalid.");
        }
    }

    // Authentication u/n & p/w
    // require email
    if (!empty($data->email)) {
        $user->email = $data->email;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[email] is required.");
    }

    // require password
    if (!empty($data->password)) {
        $user->password = $data->password;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[password] is required.");
    }

    // check credentials
    // query users
    $stmt = $user->search();
    $num = $stmt->rowCount();

    // user found
    if ($num > 0) {
        // retrieve user contents
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        extract($row);

        if (password_verify($data->password, $password)) {
            // check pw supplied from data against hash of pw (created by password_hash()) in db
            //generate JWT Token
            $jwt_token = [
                "iss" => $iss,
                "aud" => $aud,
                "iat" => $iat,
                "nbf" => $nbf,
                "exp" => $exp,
                "data" => [
                    "id" => $id,
                    "firstname" => $firstname,
                    "lastname" => $lastname,
                    "email" => $email,
                    "access_level" => $access_level,
                ],
            ];
            // set response code - OK
            http_response_code(200);

            // Authorization //
            // generate jwt Access Token
            $jwt = JWT::encode($jwt_token, $key, "HS512"); // $key from config.php
            echo json_encode([
                "message" => "Successful login.",
                "type" => "Access Token", // TODO Should be Refresh Token
                "jwt" => $jwt, // used in token.php
            ]);
        } else {
            http_response_code(401); // unauthorized
            throw new Exception("[password] is incorrect.");
        }
    } else {
        http_response_code(401); // unauthorized
        throw new Exception("[email] is incorrect.");
    }

    // Send error if something is wrong
} catch (Exception $e) {
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage(),
    ]);
    die();
}
