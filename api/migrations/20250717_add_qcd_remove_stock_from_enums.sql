-- Migration: Add 'QCD' donation type, remove 'Stock' and 'Sustainer' donation types, and remove stock payment options
-- Date: 2025-07-17
-- Issue: #74

-- Note: This adds 'QCD' as a new donation type option and removes 'Stock' and 'Sustainer' from donation types.
-- Also removes 'stock' from payment methods and 'Stock' from payment processors.
-- All existing Stock donations have been manually removed from database.
-- QCD donations can be of any payment type.

-- Modify the donations enum to add 'QCD' and remove 'Stock' and 'Sustainer'
ALTER TABLE donations MODIFY COLUMN type
    enum('Pledge','In-Kind','Vehicle','Bequest','Grant','Contract','Corporate Match','QCD','Donor Advised Fund','Major','Minor','Mailer')
    NOT NULL DEFAULT 'Pledge';

-- Check what payment methods currently exist
SELECT DISTINCT method FROM payments ORDER BY method;

-- Check what payment processors currently exist
SELECT DISTINCT processor FROM payments ORDER BY processor;

-- Remove 'stock' from payment methods (this should be safe as it's not commonly used)
ALTER TABLE payments MODIFY COLUMN method
    set('card','check','cash','in-kind','bank_account','us_bank_account','epayment')
    NOT NULL;

-- Remove 'Stock' from payment processors (this should be safe as it's not commonly used)
ALTER TABLE payments MODIFY COLUMN processor
    enum('Stripe','PayPal','P-check','K-check','Staff','Paya','Venmo','DipJar','ACH')
    DEFAULT NULL;

-- Verify the changes
SELECT type, COUNT(*) as count FROM donations GROUP BY type ORDER BY type;
SELECT method, COUNT(*) as count FROM payments GROUP BY method ORDER BY method;
SELECT processor, COUNT(*) as count FROM payments GROUP BY processor ORDER BY processor;
