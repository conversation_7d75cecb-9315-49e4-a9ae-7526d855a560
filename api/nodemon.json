{"watch": ["."], "ext": "php", "ignore": ["node_modules/**/*", "storage/**/*", "*.log", ".git/**/*", "vendor/**/*"], "exec": "clear && for test in tests/*Test.php; do echo \"\\n=== $(basename \"$test\" .php) ===\"; php -d error_reporting='E_ALL & ~E_DEPRECATED' /usr/local/bin/phpunit --colors=always --testdox --no-coverage \"$test\" 2>&1 | sed 's/{\"message.*}//' | grep -v -E '(transaction_id:|sendmail:|PHPUnit|Runtime:|Configuration:|Time:|Memory:|^\\.\\.|\\.\\.\\.|\\.{10})' | grep -v '^$'; done", "legacyWatch": true}