<?php
// Provides classes for Donor, Foundation, Volunteer, Staff

class Donor {
    // database connection and table name
    private $conn;
    private $donors_table = "donors";
    private $donations_table = "donations";
    private $payments_table = "payments";
    private $premiums_table = "premiums";
    private $campaigns_table = "campaigns";
    private $shipment_addresses_table = "shipment_addresses";
    private $premium_categories_table = "premium_categories";
    private $shipments_table = "shipments";
    private $subscriptions_table = "subscriptions";

    // object properties
    public $id;
    public $name;
    public $email;
    public $date_created;

    public function __construct($db) {
        $this->conn = $db;
    }

    public function determineDonationPaymentStatus(
        $donation_id,
        $donation_amount,
    ) {
        $totalPayments = 0;
        $totalRefunded = 0;
        $query =
            "SELECT amount, status, amount_refunded FROM " .
            $this->payments_table .
            " WHERE donation_id = :donation_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":donation_id", $donation_id);
        $stmt->execute();

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if ($row["status"] == "succeeded") {
                $totalPayments += $row["amount"];
                $totalRefunded += $row["amount_refunded"];
            }
        }

        if ($totalRefunded >= $totalPayments && $totalPayments > 0) {
            return "Refunded";
        } elseif ($totalRefunded > 0 && $totalRefunded < $totalPayments) {
            return "Partially Refunded";
        } elseif (
            $totalPayments >= $donation_amount &&
            $totalRefunded < $totalPayments
        ) {
            return "Paid";
        } elseif ($totalPayments < $donation_amount && $totalPayments > 0) {
            return "Partially Paid";
        } elseif ($totalPayments == 0) {
            return "Unpaid";
        }

        return "Unpaid"; // Default case if none of the above conditions are met
    }

    // CREATE donor
    public function create() {
        try {
            // This function creates a new donor
            $query =
                "INSERT INTO
                " .
                $this->donors_table .
                "
                SET
                firstname=:firstname,
                lastname=:lastname,
                phone=:phone,
                email=:email,
                address1=:address1,
                address2=:address2,
                partner_firstname=:partner_firstname,
                partner_lastname=:partner_lastname,
                city=:city,
                state=:state,
                country=:country,
                postal_code=:postal_code,
                notes=:notes,
                `type`=:type,
                membership_level=:membership_level,
                deceased=:deceased,
                donotsolicit=:donotsolicit,
                stripe_cus_id=:stripe_cus_id,
                paypal_user_id=:paypal_user_id,
                memsys_id=:memsys_id,
                allegiance_id=:allegiance_id,
                date_created=:date_created,
                paperless=:paperless
                ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->firstname = htmlspecialchars(
                strip_tags(ucwords(strtolower(trim($this->firstname)))),
            );
            $this->lastname = htmlspecialchars(
                strip_tags(ucwords(strtolower(trim($this->lastname)))),
            );
            $this->address1 = htmlspecialchars(
                strip_tags(ucwords(strtolower(trim($this->address1)))),
            );
            if (!empty($this->address2)) {
                $this->address2 = htmlspecialchars(
                    strip_tags(ucwords(strtolower(trim($this->address2)))),
                );
            } else {
                $this->address2 = null;
            }
            $this->city = htmlspecialchars(
                strip_tags(ucwords(strtolower(trim($this->city)))),
            );
            $this->state = htmlspecialchars(strip_tags(trim($this->state)));
            $this->country = htmlspecialchars(strip_tags(trim($this->country)));
            $this->postal_code = htmlspecialchars(
                strip_tags($this->postal_code),
            );
            if (!empty($this->type)) {
                if (
                    !in_array($this->type, [
                        "Individual",
                        "Couple",
                        "Foundation",
                        "Corporate",
                        "Goverment",
                        "Trust",
                        "Charity",
                        "Fund",
                        "Test",
                        "Fraudulent",
                    ])
                ) {
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "$this->type is not a valid value for type",
                    );
                }
            } else {
                $this->type = "Individual";
            }
            // default

            // SF city typo fix
            if (
                $this->city == "SF" ||
                $this->city == "San Fransisco" ||
                $this->city == "S.f" ||
                $this->city == "S.f." ||
                $this->city == "S F" ||
                $this->city == "Sf"
            ) {
                $this->city = "San Francisco";
            }
            // Berkeley typo fix
            if (
                $this->city == "Berk" ||
                $this->city == "Berkley" ||
                $this->city == "Berkely" ||
                $this->city == "Berkly"
            ) {
                $this->city = "Berkeley";
            }
            // Oakland typo fix
            if ($this->city == "Okland") {
                $this->city = "Oakland";
            }
            // Santa Cruz
            if ($this->city == "Santa Cruze") {
                $this->city = "Santa Cruz";
            }
            // Sebastopol
            if (
                $this->city == "Sebastapol" ||
                $this->city == "Sebastepol" ||
                $this->city == "Sebasopol" ||
                $this->city == "Sebastotol" ||
                $this->city == "Sebastople" ||
                $this->city == "Sebastopal"
            ) {
                $this->city = "Sebastopol";
            }
            // San Rafael
            if (
                $this->city == "San Raphael" ||
                $this->city == "San Raphel" ||
                $this->city == "Sanrafel"
            ) {
                $this->city = "San Rafael";
            }
            // optional //
            // partner_firstname
            if (isset($this->partner_firstname)) {
                $this->partner_firstname = htmlspecialchars(
                    strip_tags(
                        ucwords(strtolower(trim($this->partner_firstname))),
                    ),
                );
            } else {
                $this->partner_firstname = null;
            }

            // partner_lastname
            if (isset($this->partner_lastname)) {
                $this->partner_lastname = htmlspecialchars(
                    strip_tags(
                        ucwords(strtolower(trim($this->partner_lastname))),
                    ),
                );
            } else {
                $this->partner_lastname = null;
            }

            // notes
            if (isset($this->notes) && !empty($this->notes)) {
                $this->notes = htmlspecialchars(
                    strip_tags($this->notes),
                    ENT_COMPAT,
                    "UTF-8",
                );
            } else {
                $this->notes = null;
            }
            // membership_level
            if (isset($this->membership_level)) {
                $this->membership_level = htmlspecialchars(
                    strip_tags($this->membership_level),
                );
            } else {
                $this->membership_level = null;
            }
            // deceased
            if (isset($this->deceased)) {
                $this->deceased = htmlspecialchars(strip_tags($this->deceased));
            } else {
                $this->deceased = 0;
            }
            // donotsolicit
            if (isset($this->donotsolicit)) {
                $this->donotsolicit = htmlspecialchars(
                    strip_tags($this->donotsolicit),
                );
            } else {
                $this->donotsolicit = 0;
            }
            // stripe_cus_id
            if (isset($this->stripe_cus_id)) {
                $this->stripe_cus_id = htmlspecialchars(
                    strip_tags($this->stripe_cus_id),
                );
            } else {
                $this->stripe_cus_id = null;
            }
            // paypal_user_id
            if (isset($this->paypal_user_id)) {
                $this->paypal_user_id = htmlspecialchars(
                    strip_tags($this->paypal_user_id),
                );
            } else {
                $this->paypal_user_id = null;
            }
            // memsys_id
            if (isset($this->memsys_id)) {
                $this->memsys_id = htmlspecialchars(
                    strip_tags($this->memsys_id),
                );
            } else {
                $this->memsys_id = null;
            }
            // allegiance_id
            if (
                isset($this->allegiance_id) &&
                is_numeric($this->allegiance_id)
            ) {
                $this->allegiance_id = $this->allegiance_id;
            } else {
                $this->allegiance_id = null;
            }
            // date_created
            if (isset($this->date_created)) {
                $this->date_created = htmlspecialchars(
                    strip_tags($this->date_created),
                );
            } else {
                $this->date_created = date("Y-m-d H:i:s");
            }

            // bind
            $stmt->bindParam(":firstname", $this->firstname, PDO::PARAM_STR);
            $stmt->bindParam(":lastname", $this->lastname, PDO::PARAM_STR);
            $stmt->bindParam(
                ":partner_firstname",
                $this->partner_firstname,
                PDO::PARAM_STR,
            );
            $stmt->bindParam(
                ":partner_lastname",
                $this->partner_lastname,
                PDO::PARAM_STR,
            );
            $stmt->bindParam(":address1", $this->address1, PDO::PARAM_STR);
            $stmt->bindParam(":address2", $this->address2, PDO::PARAM_STR);
            $stmt->bindParam(":city", $this->city, PDO::PARAM_STR);
            $stmt->bindParam(":state", $this->state, PDO::PARAM_STR);
            $stmt->bindParam(":country", $this->country, PDO::PARAM_STR);
            $stmt->bindParam(
                ":postal_code",
                $this->postal_code,
                PDO::PARAM_STR,
            );
            $stmt->bindParam(":phone", $this->phone);
            $stmt->bindParam(":email", $this->email, PDO::PARAM_STR);
            $stmt->bindParam(":notes", $this->notes, PDO::PARAM_STR);
            $stmt->bindParam(":type", $this->type);
            $stmt->bindParam(":membership_level", $this->membership_level);
            $stmt->bindParam(":deceased", $this->deceased, PDO::PARAM_INT);
            $stmt->bindParam(
                ":donotsolicit",
                $this->donotsolicit,
                PDO::PARAM_INT,
            );
            $stmt->bindParam(
                ":stripe_cus_id",
                $this->stripe_cus_id,
                PDO::PARAM_STR,
            );
            $stmt->bindParam(":paypal_user_id", $this->paypal_user_id);
            $stmt->bindParam(":memsys_id", $this->memsys_id);
            $stmt->bindParam(
                ":allegiance_id",
                $this->allegiance_id,
                PDO::PARAM_INT,
            );
            $stmt->bindParam(":date_created", $this->date_created);
            $stmt->bindParam(":paperless", $this->paperless);

            // execute query
            $stmt->execute();

            // return ID from database
            return $this->conn->lastInsertId();

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
            exit();
        }
    }
    // READ donor by donor_id
    public function read($donor_id) {
        try {
            $query =
                "SELECT
                " .
                $this->donors_table .
                ".`id` as donor_id,
                " .
                $this->donors_table .
                ".`firstname`,
                " .
                $this->donors_table .
                ".`lastname`,
                " .
                $this->donors_table .
                ".`partner_firstname`,
                " .
                $this->donors_table .
                ".`partner_lastname`,
                " .
                $this->donors_table .
                ".`phone`,
                " .
                $this->donors_table .
                ".`email`,
                " .
                $this->donors_table .
                ".`address1`,
                " .
                $this->donors_table .
                ".`address2`,
                " .
                $this->donors_table .
                ".`city`,
                " .
                $this->donors_table .
                ".`state`,
                " .
                $this->donors_table .
                ".`country`,
                " .
                $this->donors_table .
                ".`postal_code`,
                " .
                $this->donors_table .
                ".`type` as donor_type,
                " .
                $this->donors_table .
                ".`notes`,
                " .
                $this->donors_table .
                ".`membership_level`,
                " .
                $this->donors_table .
                ".`donotsolicit`,
                " .
                $this->donors_table .
                ".`deceased`,
                " .
                $this->donors_table .
                ".`stripe_cus_id`,
                " .
                $this->donors_table .
                ".`paypal_user_id`,
                " .
                $this->donors_table .
                ".`memsys_id`,
                " .
                $this->donors_table .
                ".`allegiance_id`,
                " .
                $this->donors_table .
                ".`date_created`,
                " .
                $this->donors_table .
                ".`date_updated`
        FROM
            " .
                $this->donors_table .
                "
        WHERE " .
                $this->donors_table .
                ".`id` = ?";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // sanitize
            $donor_id = intval($donor_id);

            // keywords
            $id = "{$donor_id}";

            // bind
            $stmt->bindParam(1, $id, PDO::PARAM_INT);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // fraud check
    public function fraud_check() {
        try {
            $query =
                "SELECT
                `type`
            FROM
                " .
                $this->donors .
                "
            WHERE
                id = :id;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be found
            $stmt->bindParam(":id", $this->id);

            // execute query
            $stmt->execute();

            // return stmt
            return $stmt;
        } catch (PDOException $e) {
            // MySQL Exception
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            // Code Exception
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ donors (limit 100 DESC by ID)
    public function readDonors() {
        try {
            //select all data
            $query =
                "SELECT
                " .
                $this->donors_table .
                ".`id` as donor_id,
                " .
                $this->donors_table .
                ".`firstname`,
                " .
                $this->donors_table .
                ".`lastname`,
                " .
                $this->donors_table .
                ".`partner_firstname`,
                " .
                $this->donors_table .
                ".`partner_lastname`,
                " .
                $this->donors_table .
                ".`phone`,
                " .
                $this->donors_table .
                ".`email`,
                " .
                $this->donors_table .
                ".`address1`,
                " .
                $this->donors_table .
                ".`address2`,
                " .
                $this->donors_table .
                ".`city`,
                " .
                $this->donors_table .
                ".`state`,
                " .
                $this->donors_table .
                ".`country`,
                " .
                $this->donors_table .
                ".`postal_code`,
                " .
                $this->donors_table .
                ".`type` as donor_type,
                " .
                $this->donors_table .
                ".`notes`,
                " .
                $this->donors_table .
                ".`membership_level`,
                " .
                $this->donors_table .
                ".`donotsolicit`,
                " .
                $this->donors_table .
                ".`deceased`,
                " .
                $this->donors_table .
                ".`stripe_cus_id`,
                " .
                $this->donors_table .
                ".`paypal_user_id`,
                " .
                $this->donors_table .
                ".`memsys_id`,
                " .
                $this->donors_table .
                ".`allegiance_id`,
                " .
                $this->donors_table .
                ".`date_created`,
                " .
                $this->donors_table .
                ".`date_updated`
            FROM
				" .
                $this->donors_table .
                "
            		ORDER BY
                	`id` DESC LIMIT 100";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ Donors between payment dates
    public function readDonorsBetweenPayments($payment_start, $payment_end) {
        try {
            //select all data
            $query =
                "SELECT
                " .
                $this->donors_table .
                ".`id` as donor_id,
                " .
                $this->donors_table .
                ".`firstname`,
                " .
                $this->donors_table .
                ".`lastname`,
                " .
                $this->donors_table .
                ".`partner_firstname`,
                " .
                $this->donors_table .
                ".`partner_lastname`,
                " .
                $this->donors_table .
                ".`phone`,
                " .
                $this->donors_table .
                ".`email`,
                " .
                $this->donors_table .
                ".`address1`,
                " .
                $this->donors_table .
                ".`address2`,
                " .
                $this->donors_table .
                ".`city`,
                " .
                $this->donors_table .
                ".`state`,
                " .
                $this->donors_table .
                ".`country`,
                " .
                $this->donors_table .
                ".`postal_code`,
                " .
                $this->donors_table .
                ".`type` as donor_type,
                " .
                $this->donors_table .
                ".`notes`,
                " .
                $this->donors_table .
                ".`membership_level`,
                " .
                $this->donors_table .
                ".`donotsolicit`,
                " .
                $this->donors_table .
                ".`deceased`,
                " .
                $this->donors_table .
                ".`stripe_cus_id`,
                " .
                $this->donors_table .
                ".`paypal_user_id`,
                " .
                $this->donors_table .
                ".`memsys_id`,
                " .
                $this->donors_table .
                ".`allegiance_id`,
                " .
                $this->donors_table .
                ".`date_created`,
                " .
                $this->donors_table .
                ".`date_updated`
            FROM
                " .
                $this->donors_table .
                "
            LEFT JOIN " .
                $this->donations_table .
                " ON " .
                $this->donations_table .
                ".`donor_id` = " .
                $this->donors_table .
                ".`id`
            LEFT JOIN " .
                $this->payments_table .
                " ON " .
                $this->payments_table .
                ".`donation_id` = " .
                $this->donations_table .
                ".`id`
            WHERE " .
                $this->payments_table .
                ".`date_created` >= ?
            AND " .
                $this->payments_table .
                ".`date_created` <= ?
            ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $payment_start);
            $stmt->bindParam(2, $payment_end);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ donor subscriptions
    public function readSubscriptionsByDonor_id($donor_id) {
        try {
            // query to read single record
            $query =
                "SELECT
        " .
                $this->subscriptions_table .
                ".`id` as `subscription_id`,
        " .
                $this->subscriptions_table .
                ".`customer_id`,
        " .
                $this->subscriptions_table .
                ".`donor_id`,
        " .
                $this->subscriptions_table .
                ".`processor`,
        " .
                $this->subscriptions_table .
                ".`date_created`,
        " .
                $this->subscriptions_table .
                ".`plan_id`,
        " .
                $this->subscriptions_table .
                ".`amount` as subscription_amount,
        " .
                $this->subscriptions_table .
                ".`interval`,
        " .
                $this->subscriptions_table .
                ".`active`,
        " .
                $this->subscriptions_table .
                ".`date_canceled`
        FROM
            " .
                $this->subscriptions_table .
                "
        WHERE
            `donor_id` = ?
        ORDER BY `date_created` DESC
        ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be found
            $stmt->bindParam(1, $donor_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ donor CC on file w/ stripe
    public function readCardByCustomer_id($customer_id) {
        global $stripe;
        $cards = $stripe->customers->allSources($customer_id, [
            "object" => "card",
        ]);
        return $cards;
    }
    // READ donor subscriptions
    public function readSubscriptionsByTransaction_id($transaction_id) {
        try {
            // query to read single record
            $query =
                "SELECT
         " .
                $this->subscriptions_table .
                ".`id` as `subscription_id`,
         " .
                $this->subscriptions_table .
                ".`processor`,
         " .
                $this->subscriptions_table .
                ".`plan_id`,
         " .
                $this->subscriptions_table .
                ".`amount` as subscription_amount,
         " .
                $this->subscriptions_table .
                ".`interval`,
         " .
                $this->subscriptions_table .
                ".`active`,
         " .
                $this->subscriptions_table .
                ".`date_created`,
         " .
                $this->subscriptions_table .
                ".`date_canceled`
        FROM
            " .
                $this->subscriptions_table .
                "
        WHERE
            `transaction_id` = ?
        ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be found
            $stmt->bindParam(1, $transaction_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ donor donations
    public function readDonations($donor_id) {
        try {
            // query to read single record
            $query =
                "SELECT
            " .
                $this->donations_table .
                ".`id` as `donation_id`,
            " .
                $this->donations_table .
                ".`type`,
            " .
                $this->donations_table .
                ".`installment`,
            " .
                $this->donations_table .
                ".`timestamp`,
            " .
                $this->donations_table .
                ".`transaction_id`,
            " .
                $this->donations_table .
                ".`amount` as donation_amount,
            " .
                $this->donations_table .
                ".`comments`,
            " .
                $this->donations_table .
                ".`add_me`,
            " .
                $this->donations_table .
                ".`read_onair`,
            " .
                $this->donations_table .
                ".`ipaddress`,
            " .
                $this->donations_table .
                ".`browser`,
            " .
                $this->donations_table .
                ".`show_name`,
            " .
                $this->donations_table .
                ".`source`,
            " .
                $this->donations_table .
                ".`campaign_id`,
            " .
                $this->campaigns_table .
                ".`name` AS `campaign`,
            " .
                $this->donations_table .
                ".`updated`,
            " .
                $this->donations_table .
                ".`donation_match`,
            " .
                $this->shipment_addresses_table .
                ".`firstname` as shipping_firstname,
            " .
                $this->shipment_addresses_table .
                ".`lastname` as shipping_lastname,
            " .
                $this->shipment_addresses_table .
                ".`address1` as shipping_address1,
            " .
                $this->shipment_addresses_table .
                ".`address2` as shipping_address2,
            " .
                $this->shipment_addresses_table .
                ".`city` as shipping_city,
            " .
                $this->shipment_addresses_table .
                ".`state` as shipping_state,
            " .
                $this->shipment_addresses_table .
                ".`country` as shipping_country,
            " .
                $this->shipment_addresses_table .
                ".`postal_code` shipping_postal_code
		FROM
            " .
                $this->donations_table .
                "
        LEFT JOIN " .
                $this->campaigns_table .
                " ON " .
                $this->donations_table .
                ".`campaign_id` = " .
                $this->campaigns_table .
                ".`id`
        LEFT JOIN " .
                $this->shipment_addresses_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->shipment_addresses_table .
                ".`donation_id`
		WHERE
			`donor_id` = ?
		ORDER BY `timestamp` DESC
		";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be found
            $stmt->bindParam(1, $donor_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ Donations between payment dates
    public function readDonationsBetweenRange(
        $donor_id,
        $payment_start,
        $payment_end,
    ) {
        try {
            // query to read single record
            $query =
                "SELECT
            " .
                $this->donations_table .
                ".`id` as `donation_id`,
            " .
                $this->donations_table .
                ".`type`,
            " .
                $this->donations_table .
                ".`installment`,
            " .
                $this->donations_table .
                ".`timestamp`,
            " .
                $this->donations_table .
                ".`transaction_id`,
            " .
                $this->donations_table .
                ".`amount` as donation_amount,
            " .
                $this->donations_table .
                ".`comments`,
            " .
                $this->donations_table .
                ".`add_me`,
            " .
                $this->donations_table .
                ".`read_onair`,
            " .
                $this->donations_table .
                ".`ipaddress`,
            " .
                $this->donations_table .
                ".`browser`,
            " .
                $this->donations_table .
                ".`show_name`,
            " .
                $this->donations_table .
                ".`source`,
            " .
                $this->donations_table .
                ".`campaign_id`,
            " .
                $this->campaigns_table .
                ".`name` AS `campaign`,
            " .
                $this->donations_table .
                ".`updated`,
            " .
                $this->donations_table .
                ".`donation_match`,
            " .
                $this->shipment_addresses_table .
                ".`firstname` as shipping_firstname,
            " .
                $this->shipment_addresses_table .
                ".`lastname` as shipping_lastname,
            " .
                $this->shipment_addresses_table .
                ".`address1` as shipping_address1,
            " .
                $this->shipment_addresses_table .
                ".`address2` as shipping_address2,
            " .
                $this->shipment_addresses_table .
                ".`city` as shipping_city,
            " .
                $this->shipment_addresses_table .
                ".`state` as shipping_state,
            " .
                $this->shipment_addresses_table .
                ".`country` as shipping_country,
            " .
                $this->shipment_addresses_table .
                ".`postal_code` shipping_postal_code
		FROM
            " .
                $this->donations_table .
                "
        LEFT JOIN " .
                $this->campaigns_table .
                " ON " .
                $this->donations_table .
                ".`campaign_id` = " .
                $this->campaigns_table .
                ".`id`
        LEFT JOIN " .
                $this->shipment_addresses_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->shipment_addresses_table .
                ".`donation_id`
        LEFT JOIN " .
                $this->payments_table .
                " ON " .
                $this->payments_table .
                ".`donation_id` = " .
                $this->donations_table .
                ".`id`
		WHERE
        " .
                $this->donations_table .
                ".`donor_id` = ?
            AND " .
                $this->payments_table .
                ".`date_created` >= ?
            AND " .
                $this->payments_table .
                ".`date_created` <= ?

		";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $donor_id, PDO::PARAM_INT);
            $stmt->bindParam(2, $payment_start);
            $stmt->bindParam(3, $payment_end);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ Payment by Donation ID
    public function readPaymentbyDonationid($donation_id) {
        //TODO: move to payments.

        // This function reads a single record from the payments table and returns the result
        $query =
            "SELECT
                    " .
            $this->payments_table .
            ".`id`,
                    " .
            $this->payments_table .
            ".`donation_id`,
                    " .
            $this->payments_table .
            ".`customer_id`,
                    " .
            $this->payments_table .
            ".`payment_id`,
                    " .
            $this->payments_table .
            ".`amount` as payment_amount,
                    " .
            $this->payments_table .
            ".`amount_refunded`,
                    " .
            $this->payments_table .
            ".`method`,
                    " .
            $this->payments_table .
            ".`processor`,
                    " .
            $this->payments_table .
            ".`fingerprint`,
                    " .
            $this->payments_table .
            ".`card_type`,
                    " .
            $this->payments_table .
            ".`last4`,
                    " .
            $this->payments_table .
            ".`brand`,
                    " .
            $this->payments_table .
            ".`exp_month`,
                    " .
            $this->payments_table .
            ".`exp_year`,
                    " .
            $this->payments_table .
            ".`last_updated_by`,
                    " .
            $this->payments_table .
            ".`date_created`,
                    " .
            $this->payments_table .
            ".`date_updated`,
                    " .
            $this->payments_table .
            ".`date_deposited`,
                    " .
            $this->payments_table .
            ".`status`
            FROM	" .
            $this->payments_table .
            "
            LEFT JOIN " .
            $this->donations_table .
            " ON " .
            $this->payments_table .
            ".`donation_id` = " .
            $this->donations_table .
            ".`id`
            LEFT JOIN " .
            $this->donors_table .
            " ON " .
            $this->donations_table .
            ".`donor_id` = " .
            $this->donors_table .
            ".`id`
            WHERE " .
            $this->payments_table .
            ".`donation_id` = ?;";

        // prepare query statement
        $stmt = $this->conn->prepare($query);

        // bind variable values
        $stmt->bindParam(1, $donation_id);
        // $stmt->bindParam(2, $enddate);
        // $stmt->bindParam(3, $premium_id);
        // $stmt->bindParam(4, $status);

        // execute query
        $stmt->execute();

        // return values from database
        return $stmt;
    }
    // READ Payment between dates
    public function readPaymentsRange(
        $donation_id,
        $payment_start,
        $payment_end,
        $payment_amount_gte,
    ) {
        try {
            // This function reads a payment records from the payments table and returns the result(s)
            $query =
                "SELECT
                    " .
                $this->payments_table .
                ".`id`,
                    " .
                $this->payments_table .
                ".`donation_id`,
                    " .
                $this->payments_table .
                ".`customer_id`,
                    " .
                $this->payments_table .
                ".`payment_id`,
                    " .
                $this->payments_table .
                ".`amount` as payment_amount,
                    " .
                $this->payments_table .
                ".`amount_refunded`,
                    " .
                $this->payments_table .
                ".`method`,
                    " .
                $this->payments_table .
                ".`processor`,
                    " .
                $this->payments_table .
                ".`fingerprint`,
                    " .
                $this->payments_table .
                ".`card_type`,
                    " .
                $this->payments_table .
                ".`last4`,
                    " .
                $this->payments_table .
                ".`brand`,
                    " .
                $this->payments_table .
                ".`exp_month`,
                    " .
                $this->payments_table .
                ".`exp_year`,
                    " .
                $this->payments_table .
                ".`last_updated_by`,
                    " .
                $this->payments_table .
                ".`date_created`,
                    " .
                $this->payments_table .
                ".`date_updated`,
                    " .
                $this->payments_table .
                ".`date_deposited`,
                    " .
                $this->payments_table .
                ".`status`
            FROM	" .
                $this->payments_table .
                "
            LEFT JOIN " .
                $this->donations_table .
                " ON " .
                $this->payments_table .
                ".`donation_id` = " .
                $this->donations_table .
                ".`id`
            LEFT JOIN " .
                $this->donors_table .
                " ON " .
                $this->donations_table .
                ".`donor_id` = " .
                $this->donors_table .
                ".`id`
            WHERE " .
                $this->payments_table .
                ".`donation_id` = ?
            AND " .
                $this->payments_table .
                ".`date_created` >= ?
            AND " .
                $this->payments_table .
                ".`date_created` <= ?
            AND " .
                $this->payments_table .
                ".`amount` >= ?
            ;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $donation_id, PDO::PARAM_INT);
            $stmt->bindParam(2, $payment_start);
            $stmt->bindParam(3, $payment_end);
            $stmt->bindParam(4, $payment_amount_gte);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ Payment between dates
    public function readTaxReceipt(
        $payment_start,
        $payment_end,
        $payment_status,
        $payment_amount_gte,
    ) {
        try {
            // This function reads a payment records from the payments table and returns the result(s)
            $query =
                "SELECT
                    " .
                $this->donors_table .
                ".`id` as `donor_id`,
                    " .
                $this->donors_table .
                ".`firstname`,
                    " .
                $this->donors_table .
                ".`lastname`,
                    " .
                $this->donors_table .
                ".`partner_firstname`,
                    " .
                $this->donors_table .
                ".`partner_lastname`,
                    " .
                $this->donors_table .
                ".`address1`,
                    " .
                $this->donors_table .
                ".`address2`,
                    " .
                $this->donors_table .
                ".`city`,
                    " .
                $this->donors_table .
                ".`state`,
                    " .
                $this->donors_table .
                ".`postal_code`,
                    " .
                $this->donors_table .
                ".`country`,
                    " .
                $this->donors_table .
                ".`phone`,
                    " .
                $this->donors_table .
                ".`email`,
                    SUM(total_payments - amount_refunded) AS `Total_Payment_Amount`,
                    SUM(coalesce(total_fmv, 0)) AS `Total_Fair_Market_Value`,
                    SUM((total_payments - amount_refunded) - coalesce(total_fmv, 0)) AS `Total_Tax_Deductible`
                FROM
                    donors

                    -- 1. (JOIN donations)
                INNER JOIN( -- every donation may or may not have a payment(s), which may or may not have premium(s)
                    SELECT
                    " .
                $this->donations_table .
                ".donor_id,
                        " .
                $this->donations_table .
                ".id as `donation_id`,
                        total_payments,
                        amount_refunded,
                        total_fmv
                        FROM
                        " .
                $this->donations_table .
                "

                    -- 2. (JOIN Payments)
                    INNER JOIN( -- every payment may, or may not have a shipment (premium)
                        SELECT
                        pa.donation_id,
                        SUM(pa.amount) AS `total_payments`,
                        SUM(pa.amount_refunded) AS `amount_refunded`,
                        AVG(donation_fmv) as `total_fmv` -- consolidate each donation_fmv as table below is by payment_id
                    FROM
                        " .
                $this->payments_table .
                " AS pa

                    -- 3. (JOIN premiums)
                    LEFT JOIN( -- Select (shipped) premiums from shipment list
                        SELECT
                            SUM(p.fmv) AS donation_fmv, -- each payment_id will have same fmv
                            s.donation_id AS donation_id
                        FROM
                            " .
                $this->premiums_table .
                " AS p

                        -- 4. (JOIN shipments)
                        INNER JOIN " .
                $this->shipments_table .
                " AS s
                        ON
                            p.id = s.premium_id
                        WHERE
                            s.status = \"Shipped\"
                        GROUP BY
                            s.donation_id

                    ) AS prems_temp -- 3
                    ON
                        prems_temp.donation_id = pa.donation_id
                    WHERE
                        pa.status = :payment_status
                        AND pa.date_created >= :payment_start
                        AND pa.date_created <= :payment_end
                        AND pa.amount >= :payment_amount_gte
                    GROUP BY
                        pa.donation_id
                    ORDER BY `prems_temp`.`donation_fmv`  DESC
                        ) AS pmt_temp -- 2

                    ON
                        donations.id = pmt_temp.donation_id
                    ) AS dntn_temp -- 1

                ON
                    donors.id = dntn_temp.donor_id
                    WHERE
                    donors.type !=\"Fraudulent\"

                GROUP BY " .
                $this->donors_table .
                ".id
                ORDER BY " .
                $this->donors_table .
                ".id ASC;
                ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(":payment_status", $payment_status);
            $stmt->bindParam(":payment_start", $payment_start);
            $stmt->bindParam(":payment_end", $payment_end);
            $stmt->bindParam(":payment_amount_gte", $payment_amount_gte);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ Gifts by donation_id
    public function readGifts($donation_id) {
        try {
            // query to read gifts table for single transaction_id
            $query =
                "SELECT
                " .
                $this->premiums_table .
                ".`id` as `premium_id`,
                " .
                $this->shipments_table .
                ".`donation_id`,
                " .
                $this->premiums_table .
                ".`name`,
                " .
                $this->premiums_table .
                ".`price`,
                " .
                $this->premiums_table .
                ".`cog`,
                " .
                $this->premiums_table .
                ".`fmv`,
                " .
                $this->shipments_table .
                ".`quantity` as `qty`,
                " .
                $this->premiums_table .
                ".`download_url`,
                " .
                $this->premiums_table .
                ".`img_url`,
                " .
                $this->premium_categories_table .
                ".`name` as category,
                " .
                $this->shipments_table .
                ".`status` as shipment_status
        FROM
            " .
                $this->shipments_table .
                "
        LEFT JOIN " .
                $this->premiums_table .
                " ON `" .
                $this->shipments_table .
                "`.`premium_id` = " .
                $this->premiums_table .
                ".`id`
        LEFT JOIN " .
                $this->premium_categories_table .
                " ON " .
                $this->premiums_table .
                ".`category_id` = " .
                $this->premium_categories_table .
                ".`id`
        WHERE
            `" .
                $this->shipments_table .
                "`.`donation_id` = ?";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of donation_id to be found
            $stmt->bindParam(1, $donation_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // SEARCH donors
    public function searchDonors($keywords) {
        try {
            // This function searches by:
            // firstname + lastname, partner_firstname + partner_lastname, phone, id, or email.
            // TODO: Filter with Date search?

            // select all query
            $query =
                "SELECT
                " .
                $this->donors_table .
                ".`id` as donor_id,
                " .
                $this->donors_table .
                ".`firstname`,
                " .
                $this->donors_table .
                ".`lastname`,
                " .
                $this->donors_table .
                ".`partner_firstname`,
                " .
                $this->donors_table .
                ".`partner_lastname`,
                " .
                $this->donors_table .
                ".`phone`,
                " .
                $this->donors_table .
                ".`type` as donor_type,
                " .
                $this->donors_table .
                ".`email`,
                " .
                $this->donors_table .
                ".`address1`,
                " .
                $this->donors_table .
                ".`address2`,
                " .
                $this->donors_table .
                ".`city`,
                " .
                $this->donors_table .
                ".`state`,
                " .
                $this->donors_table .
                ".`country`,
                " .
                $this->donors_table .
                ".`postal_code`,
                " .
                $this->donors_table .
                ".`notes`,
                " .
                $this->donors_table .
                ".`membership_level`,
                " .
                $this->donors_table .
                ".`donotsolicit`,
                " .
                $this->donors_table .
                ".`deceased`,
                " .
                $this->donors_table .
                ".`stripe_cus_id`,
                " .
                $this->donors_table .
                ".`paypal_user_id`,
                " .
                $this->donors_table .
                ".`memsys_id`,
                " .
                $this->donors_table .
                ".`allegiance_id`,
                " .
                $this->donors_table .
                ".`date_created`,
                " .
                $this->donors_table .
                ".`date_updated`
            FROM
                " .
                $this->donors_table .
                "
            WHERE
                CONCAT_WS(' ',`firstname`,`lastname`) LIKE ?
                OR CONCAT_WS(' ',`partner_firstname`,`partner_lastname`) LIKE ?
                OR `id` = ?
                OR `allegiance_id` = ?
                OR `id` = (SELECT " .
                $this->donations_table .
                ".donor_id FROM $this->donations_table, $this->donors_table WHERE donations.id = ? LIMIT 1)
                OR `email` LIKE ?
                OR `phone` LIKE ?

                ORDER BY (
            CASE
                WHEN id=? then 100
                WHEN allegiance_id=? then 95
                WHEN email=? then 90
                WHEN phone=? then 80
                ELSE 0
            END )
            DESC LIMIT 100;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // sanitize
            $keywords = urldecode(htmlspecialchars(strip_tags($keywords)));

            // keywords
            $name = "%{$keywords}%";
            $partner_name = "%{$keywords}%";
            $id = "{$keywords}";
            $allegiance_id = "{$keywords}";
            $donation_id = "{$keywords}";
            $phone = "{$keywords}%";
            $email = "{$keywords}%";

            // bind
            $stmt->bindParam(1, $name);
            $stmt->bindParam(2, $partner_name);
            $stmt->bindParam(3, $id, PDO::PARAM_INT);
            $stmt->bindParam(4, $allegiance_id, PDO::PARAM_INT);
            $stmt->bindParam(5, $donation_id, PDO::PARAM_INT);
            $stmt->bindParam(6, $email);
            $stmt->bindParam(7, $phone);
            $stmt->bindParam(8, $id, PDO::PARAM_INT);
            $stmt->bindParam(9, $allegiance_id, PDO::PARAM_INT);
            $stmt->bindParam(10, $email);
            $stmt->bindParam(11, $phone);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // SEARCH donor (returns 1 donor, the most likely match)
    public function searchDonor() {
        try {
            // This function searches by:
            // email, stripe_cus_id, phone, (firstname + lastname + postalcode).
            // TODO: Filter with Date search?

            // select all query
            $query =
                "SELECT
                    " .
                $this->donors_table .
                ".`id` as donor_id,
                    " .
                $this->donors_table .
                ".`firstname`,
                    " .
                $this->donors_table .
                ".`lastname`,
                    " .
                $this->donors_table .
                ".`partner_firstname`,
                    " .
                $this->donors_table .
                ".`partner_lastname`,
                    " .
                $this->donors_table .
                ".`phone`,
                    " .
                $this->donors_table .
                ".`type` as donor_type,
                    " .
                $this->donors_table .
                ".`email`,
                    " .
                $this->donors_table .
                ".`address1`,
                    " .
                $this->donors_table .
                ".`address2`,
                    " .
                $this->donors_table .
                ".`city`,
                    " .
                $this->donors_table .
                ".`state`,
                    " .
                $this->donors_table .
                ".`country`,
                    " .
                $this->donors_table .
                ".`postal_code`,
                    " .
                $this->donors_table .
                ".`notes`,
                    " .
                $this->donors_table .
                ".`membership_level`,
                    " .
                $this->donors_table .
                ".`donotsolicit`,
                    " .
                $this->donors_table .
                ".`deceased`,
                    " .
                $this->donors_table .
                ".`stripe_cus_id`,
                    " .
                $this->donors_table .
                ".`paypal_user_id`,
                    " .
                $this->donors_table .
                ".`memsys_id`,
                    " .
                $this->donors_table .
                ".`allegiance_id`,
                    " .
                $this->donors_table .
                ".`date_created`,
                    " .
                $this->donors_table .
                ".`date_updated`
                FROM
                    " .
                $this->donors_table .
                "
                WHERE
                    email=:email
                    OR stripe_cus_id=:stripe_cus_id
                    OR phone=:phone
                    OR lastname=:lastname
                        AND firstname=:firstname
                        AND postal_code=:postal_code
                ORDER BY (
                CASE
                    WHEN email=:email then 100
                    WHEN stripe_cus_id=:stripe_cus_id then 90
                    WHEN phone=:phone then 80
                    WHEN lastname=:lastname
                    AND firstname=:firstname
                    AND postal_code=:postal_code then 70
                    ELSE 0
                END )
                DESC LIMIT 1;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind
            $stmt->bindParam(":email", $this->email, PDO::PARAM_STR);
            $stmt->bindParam(
                ":stripe_cus_id",
                $this->stripe_cus_id,
                PDO::PARAM_STR,
            );
            $stmt->bindParam(":phone", $this->phone, PDO::PARAM_STR);
            $stmt->bindParam(":lastname", $this->lastname, PDO::PARAM_STR);
            $stmt->bindParam(":firstname", $this->firstname, PDO::PARAM_STR);
            $stmt->bindParam(
                ":postal_code",
                $this->postal_code,
                PDO::PARAM_STR,
            );

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // UPDATE donor
    public function update($donor_id, $key, $value) {
        try {
            // Check to see if donor exists
            if ($this->read($donor_id)->rowCount() < 1) {
                http_response_code(404); // Not Found
                throw new Exception("[Donor ID] could not be found.");
            }

            // This function updates the donor by donor_id.
            // UPDATE `donors` SET `address2` = 'test' WHERE `donors`.`id` = 65;

            // allowlist for $key & evaluate
            switch ($key) {
                case "firstname":
                    $col_id = "firstname";
                    break;
                case "lastname":
                    $col_id = "lastname";
                    break;
                case "partner_firstname":
                    $col_id = "partner_firstname";
                    break;
                case "partner_lastname":
                    $col_id = "partner_lastname";
                    break;
                case "phone":
                    $col_id = "phone";
                    break;
                case "email":
                    // if there is a value, check to see if its valid
                    if (
                        !empty($value) &&
                        filter_var($value, FILTER_VALIDATE_EMAIL) === false
                    ) {
                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "$value is not a valid value for $key",
                        );
                    } else {
                        // the value is likey null
                        $col_id = "email";
                    }
                    break;
                case "address1":
                    $col_id = "address1";
                    break;
                case "address2":
                    $col_id = "address2";
                    break;
                case "city":
                    $col_id = "city";
                    break;
                case "state":
                    $col_id = "state";
                    break;
                case "country":
                    $col_id = "country";
                    break;
                case "postal_code":
                    $col_id = "postal_code";
                    break;
                case "notes":
                    $col_id = "notes";
                    break;
                case "stripe_cus_id":
                    $col_id = "stripe_cus_id";
                    break;
                case "allegiance_id":
                    $col_id = "allegiance_id";
                    break;
                case "type":
                    // check if value is in array
                    $type_arr = [
                        "Individual",
                        "Couple",
                        "Foundation",
                        "Corporate",
                        "Goverment",
                        "Trust",
                        "Charity",
                        "Fund",
                        "Test",
                        "Fraudulent",
                    ];
                    if (in_array("$value", $type_arr)) {
                        $col_id = "type";
                    } else {
                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "$value is not a valid value for $key",
                        );
                    }
                    break;
                case "membership_level":
                    // check if value is in array
                    $type_arr = ["Basic", "Vigilant", "Vibrant"];
                    if (in_array("$value", $type_arr)) {
                        $col_id = "membership_level";
                    } else {
                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "$value is not a valid value for $key",
                        );
                    }
                    break;
                case "donotsolicit":
                    $col_id = "donotsolicit";
                    $value = +$value; // convert from boolean to int for sql
                    break;
                case "deceased":
                    $col_id = "deceased";
                    $value = +$value; // convert from boolean to int for sql
                    break;
                default:
                    http_response_code(400); // Bad Request
                    throw new Exception("$key is not an acceptable value");
            }
            $query =
                "
                UPDATE " .
                $this->donors_table .
                "
                SET $col_id = ?
                WHERE $this->donors_table.`id` = ?";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $value);
            $stmt->bindParam(2, $donor_id, PDO::PARAM_INT);

            // execute query
            $stmt->execute();

            // Hook for automated duplicate detection on phone number update.
            // This ensures that updates from staff or webhooks can also trigger suggestions.
            if ($col_id === 'phone' && !empty($value)) {
                try {
                    // The DonorService is designed to be idempotent and will exit early
                    // if no action is needed (e.g., donor is already in a group),
                    // so calling it on every phone change is safe and efficient.
                    require_once __DIR__ . '/../src/Service/DonorService.php';
                    $donorService = new \Kpfa\Service\DonorService($this->conn, new self($this->conn));
                    $donorService->checkForDuplicatesAndNotifyKeystone($donor_id);
                } catch (Exception $e) {
                    // Log error but don't break the update flow
                    error_log("Duplicate detection hook failed during donor update for donor {$donor_id}: " . $e->getMessage());
                }
            }

            // return values from database
            return true;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            die(
                json_encode([
                    "message" => $e->getMessage(),
                ])
            );
        }
    }
}
class Foundation {
}
class Volunteer {
}
class Staff {
}
