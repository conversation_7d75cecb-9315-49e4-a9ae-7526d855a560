<?php
//prettier-ignore
class Campaign
{

    // database connection and table active
    private $conn;
    private $campaigns_table = "campaigns";
    private $donations_table = "donations";
    private $payments_table = "payments";

    // object properties
    public $id;
    public $type;
    public $name;
    public $goal;
    public $start;
    public $end;

    // constructor with $db as database connection
    public function __construct($db)
    {
        $this->conn = $db;
    }
    /*
    Provides:
    read - returns all campaigns
    read_one  - returns selected campaign stats
    read_current - returns current campaign stats
     */

    // read single campaign
    public function read($id)
    {
        try {
            // select all query
            $query = "SELECT
            " . $this->campaigns_table . ".`id`,
            " . $this->campaigns_table . ".`type`,
            " . $this->campaigns_table . ".`name`,
            " . $this->campaigns_table . ".`goal`,
            " . $this->campaigns_table . ".`start`,
            " . $this->campaigns_table . ".`end`,
            " . $this->campaigns_table . ".`active`,
            " . $this->campaigns_table . ".`gift_link`,
            " . $this->campaigns_table . ".`gift_title`,
            " . $this->campaigns_table . ".`notes`,
            (SELECT COALESCE( SUM( IF(" . $this->donations_table . ".installment = 'Monthly', (" . $this->donations_table . ".`amount` ),0)))
            FROM " . $this->donations_table . "
            WHERE " . $this->donations_table . ".`campaign_id` = ?) AS `sustainer_total`,
            (SELECT COUNT(CASE WHEN " . $this->donations_table . ".installment = 'Monthly' THEN 1 END)
            FROM " . $this->donations_table . "
            WHERE " . $this->donations_table . ".`campaign_id` = ?) AS `sustainer_count`,
            (SELECT COALESCE(COUNT(" . $this->donations_table . ".`id`))
            FROM " . $this->donations_table . "
                WHERE " . $this->donations_table . ".`campaign_id` = ?
            ) as `count`,
            (SELECT SUM(
                            IF(
                                " . $this->donations_table . ".installment = 'Monthly',
                                (" . $this->donations_table . ".amount * 12),
                                (" . $this->donations_table . ".amount)
                            )
                        )
                FROM " . $this->donations_table . "
                WHERE " . $this->donations_table . ".`campaign_id` = ?
            ) as pledged,
            (SELECT COALESCE(SUM(
                        CASE WHEN
                            (" . $this->payments_table . ".amount = " . $this->donations_table . ".amount) AND " . $this->payments_table . ".status = 'succeeded'
                            THEN (" . $this->payments_table . ".amount - " . $this->payments_table . ".`amount_refunded`)
                            ELSE 0
                        END)
                    )
                FROM 	" . $this->payments_table . "
                LEFT JOIN " . $this->donations_table . " on " . $this->donations_table . ".id = " . $this->payments_table . ".donation_id
                WHERE 	" . $this->donations_table . ".`campaign_id` = ?
            ) as paid
                FROM
                        " . $this->campaigns_table . "
                WHERE
                        " . $this->campaigns_table . ".id = ?
                GROUP BY
                        " . $this->campaigns_table . ".`id`;
                ";

            // prepare query statement

            $stmt = $this->conn->prepare($query);
            // bind id of campaign to be read

            $stmt->bindParam(1, $id);
            $stmt->bindParam(2, $id);
            $stmt->bindParam(3, $id);
            $stmt->bindParam(4, $id);
            $stmt->bindParam(5, $id);
            $stmt->bindParam(6, $id);
            $stmt->execute();
            // return values from database
            return $stmt;
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }

    // read single campaign by date
    public function readbydate($date)
    {
        try {
            // select all query
            $query = "SELECT
                *
                FROM " . $this->campaigns_table . "
                WHERE
                :date >= start AND :date <= end";

            // prepare query statement

            $stmt = $this->conn->prepare($query);
            // bind date of campaign to be read
            $stmt->bindParam(":date", $date);

            $stmt->execute();
            // return values from database
            return $stmt;
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }

    // search campaigns by name
    public function searchCampaigns($name)
    {
        try {
            // select all query
            $query = "SELECT
        " . $this->campaigns_table . ".`id`,
        " . $this->campaigns_table . ".`type`,
        " . $this->campaigns_table . ".`name`,
        " . $this->campaigns_table . ".`goal`,
        " . $this->campaigns_table . ".`start`,
        " . $this->campaigns_table . ".`end`,
        " . $this->campaigns_table . ".`notes`,
        COALESCE(COUNT(" . $this->donations_table . ".`id`)) AS `count`,
        COALESCE( SUM( IF( " . $this->donations_table . ".installment = 'Monthly', (" . $this->donations_table . ".amount * 12), (" . $this->donations_table . ".amount) ) ),0) AS pledged,
        COALESCE( SUM( IF(" . $this->donations_table . ".installment = 'Monthly', (" . $this->donations_table . ".`amount` ),0))) AS `sustainer_total`,
        COUNT(CASE WHEN " . $this->donations_table . ".installment = 'Monthly' THEN 1 END) AS `sustainer_count`,
        COALESCE(SUM(
            (CASE
               WHEN
                (" . $this->payments_table . ".donation_id = " . $this->donations_table . ".`id`)
                AND " . $this->payments_table . ".status = 'succeeded'
                THEN (" . $this->payments_table . ".amount - " . $this->payments_table . ".`amount_refunded`)
                ELSE 0
           END)
          ),0) AS paid
            FROM
            " . $this->campaigns_table . ", " . $this->donations_table . "
            LEFT JOIN " . $this->payments_table . " on  " . $this->payments_table . ".donation_id = " . $this->donations_table . ".id
            WHERE
            `name` LIKE ?
            AND
           " . $this->donations_table . ".campaign_id= " . $this->campaigns_table . ".`id`
	       GROUP BY " . $this->campaigns_table . ".`id`
           ORDER BY
            `end` DESC LIMIT 1000";
            // prepare query statement
            $stmt = $this->conn->prepare($query);
            // bind name of campaign to be searched

            $name = "%{$name}%";

            $stmt->bindParam(1, $name);
            $stmt->execute();
            // return values from database
            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
    // read all campaigns
    public function readCampaigns()
    {
        try {
            // select all query
            $query = "SELECT
       " . $this->campaigns_table . ".`id`,
       " . $this->campaigns_table . ".`type`,
       " . $this->campaigns_table . ".`name`,
       " . $this->campaigns_table . ".`goal`,
       " . $this->campaigns_table . ".`start`,
       " . $this->campaigns_table . ".`end`,
       " . $this->campaigns_table . ".`active`,
       " . $this->campaigns_table . ".`gift_link`,
       " . $this->campaigns_table . ".`gift_title`,
       " . $this->campaigns_table . ".`notes`
       FROM
                " . $this->campaigns_table . "
            ORDER BY `end` DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);
            $stmt->execute();


            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }

    // read active campaigns
    public function readActiveCampaigns()
    {
        try {
            // select all query
            $query = "SELECT
       " . $this->campaigns_table . ".`id`,
       " . $this->campaigns_table . ".`type`,
       " . $this->campaigns_table . ".`name`,
       " . $this->campaigns_table . ".`goal`,
       " . $this->campaigns_table . ".`start`,
       " . $this->campaigns_table . ".`end`,
       " . $this->campaigns_table . ".`active`,
       " . $this->campaigns_table . ".`gift_link`,
       " . $this->campaigns_table . ".`gift_title`,
       " . $this->campaigns_table . ".`notes`
       FROM
                " . $this->campaigns_table . "
       WHERE    " . $this->campaigns_table . ".`active` = true
       ORDER BY `end` DESC";

            // prepare query statement

            $stmt = $this->conn->prepare($query);
            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }

        // should be date not datetime
    public function readCampaignsByDate($date)
    {
        try {
            $currenthour = date('Y-m-d H:00');
        // Subquery to get donation IDs with no corresponding payment records or payment status other than 'succeeded'
        $subquery = "SELECT d.id
            FROM " . $this->donations_table . " d
            LEFT JOIN " . $this->payments_table . " p ON d.id = p.donation_id
            WHERE d.source = 'WebSite' AND (p.status <> 'succeeded' OR p.status IS NULL) AND DATE(d.timestamp) = '" . $date . "'"; // Filter by date here

            // query to read single record
            $query = " SELECT
        " . $this->campaigns_table . ".`id`,
        " . $this->campaigns_table . ".`type`,
        " . $this->campaigns_table . ".`name`,
        " . $this->campaigns_table . ".`goal`,
        " . $this->campaigns_table . ".`start`,
        " . $this->campaigns_table . ".`end`,
            COALESCE(COUNT(" . $this->donations_table . ".`id`)) AS `count`,
            COALESCE(COUNT(
                CASE WHEN " . $this->donations_table . ".`timestamp` >= '" . $currenthour . "' AND " . $this->donations_table . ".campaign_id= " . $this->campaigns_table . ".`id` THEN 1
                END)) AS hour_count,
            COALESCE( SUM( IF( " . $this->donations_table . ".installment = 'Monthly', (" . $this->donations_table . ".amount * 12), (" . $this->donations_table . ".amount) ) ),0) AS pledged,
            COALESCE( SUM( IF(" . $this->donations_table . ".installment = 'Monthly', (" . $this->donations_table . ".`amount` ),0))) AS `sustainer_total`,
            COUNT(CASE WHEN " . $this->donations_table . ".installment = 'Monthly' THEN 1 END) AS `sustainer_count`,
            COALESCE(
                SUM(
                    (
                        CASE WHEN " . $this->donations_table . ".`installment` = 'Monthly' AND DATE(" . $this->donations_table . ".`timestamp`) = CURDATE() THEN(" . $this->donations_table . ".amount * 12) WHEN " . $this->donations_table . ".installment = 'One-Time' AND DATE(" . $this->donations_table . ".`timestamp`) = CURDATE() THEN(" . $this->donations_table . ".amount) ELSE 0
                        END
                    )
                ),0) AS day_total,
            COALESCE(
                    SUM(
                    (
                            CASE WHEN " . $this->donations_table . ".`installment` = 'Monthly' AND " . $this->donations_table . ".`timestamp` BETWEEN '" . $currenthour . "'  AND NOW() THEN(" . $this->donations_table . ".amount * 12) WHEN " . $this->donations_table . ".installment = 'One-Time' AND " . $this->donations_table . ".`timestamp` BETWEEN '" . $currenthour . "' AND NOW() THEN(" . $this->donations_table . ".amount) ELSE 0
                            END
                        )
                    ),0) AS hour_pledged,
                COALESCE(SUM(
                    (CASE
                       WHEN
                        (" . $this->payments_table . ".donation_id = " . $this->donations_table . ".`id`)
                        AND " . $this->payments_table . ".status = 'succeeded'
                        THEN (" . $this->payments_table . ".amount - " . $this->payments_table . ".`amount_refunded`)
                        ELSE 0
                   END)
                  ),0) AS paid
                FROM
                    " . $this->campaigns_table . ", " . $this->donations_table . "
		LEFT JOIN " . $this->payments_table . " on  " . $this->payments_table . ".donation_id = " . $this->donations_table . ".id
		WHERE cast(? as date) BETWEEN " . $this->campaigns_table . ".`start`
		    AND " . $this->campaigns_table . ".`end`
                    AND " . $this->donations_table . ".`campaign_id`= " . $this->campaigns_table . ".`id` AND " . $this->donations_table . ".id NOT IN (" . $subquery . ")
		GROUP BY " . $this->campaigns_table . ".`id`;
		";

            // prepare
            $stmt = $this->conn->prepare($query);

            // bind
            $stmt->bindParam(1, $date);

            // execute
            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }


    // datetime
    public function readCampaignsByDateTime($datetime)
    {
        try {
            // current hour
            $currenthour = date('Y-m-d H:05:00');

            // query to read single record
            $query = "SELECT
        " . $this->campaigns_table . ".`id`,
        " . $this->campaigns_table . ".`type`,
        " . $this->campaigns_table . ".`name`,
        " . $this->campaigns_table . ".`goal`,
        " . $this->campaigns_table . ".`start`,
        " . $this->campaigns_table . ".`end`,
            COALESCE(COUNT(" . $this->donations_table . ".`id`)) AS `count`,
            COUNT(
                CASE WHEN " . $this->donations_table . ".`timestamp` >= '" . $currenthour . "' AND " . $this->donations_table . ".campaign_id= " . $this->campaigns_table . ".`id` THEN 1
                END) AS hour_count,
            COALESCE( SUM( IF( " . $this->donations_table . ".installment = 'Monthly', (" . $this->donations_table . ".amount * 12), (" . $this->donations_table . ".amount) ) ),0) AS pledged,
            COALESCE( SUM( IF(" . $this->donations_table . ".installment = 'Monthly', (" . $this->donations_table . ".`amount` ),0))) AS `sustainer_total`,
            COUNT(CASE WHEN " . $this->donations_table . ".installment = 'Monthly' THEN 1 END) AS `sustainer_count`,
            COALESCE(
                SUM(
                    (
                        CASE WHEN " . $this->donations_table . ".`installment` = 'Monthly' AND DATE(" . $this->donations_table . ".`timestamp`) = CURDATE() THEN(" . $this->donations_table . ".amount * 12) WHEN " . $this->donations_table . ".installment = 'One-Time' AND DATE(" . $this->donations_table . ".`timestamp`) = CURDATE() THEN(" . $this->donations_table . ".amount) ELSE 0
                        END
                    )
                ),0) AS day_total,
            COALESCE(
                SUM(
                (
                        CASE WHEN " . $this->donations_table . ".`installment` = 'Monthly' AND " . $this->donations_table . ".`timestamp` BETWEEN '" . $currenthour . "'  AND NOW() THEN(" . $this->donations_table . ".amount * 12) WHEN " . $this->donations_table . ".installment = 'One-Time' AND " . $this->donations_table . ".`timestamp` BETWEEN '" . $currenthour . "' AND NOW() THEN(" . $this->donations_table . ".amount) ELSE 0
                        END
                    )
                ),0) AS hour_pledged,
                COALESCE(SUM(
                    (CASE
                       WHEN
                        (" . $this->payments_table . ".donation_id = " . $this->donations_table . ".`id`)
                        AND " . $this->payments_table . ".status = 'succeeded'
                        THEN (" . $this->payments_table . ".amount - " . $this->payments_table . ".`amount_refunded`)
                        ELSE 0
                   END)
                  ),0) AS paid
                FROM
                    " . $this->campaigns_table . ", " . $this->donations_table . "
		LEFT JOIN " . $this->payments_table . " on  " . $this->payments_table . ".donation_id = " . $this->donations_table . ".id
		WHERE ? BETWEEN " . $this->campaigns_table . ".`start`
		    AND " . $this->campaigns_table . ".`end`
                    AND " . $this->donations_table . ".`campaign_id`= " . $this->campaigns_table . ".`id`
		GROUP BY " . $this->campaigns_table . ".`id`;
		";

            // prepare
            $stmt = $this->conn->prepare($query);

            // bind
            $stmt->bindParam(1, $datetime);

            // execute
            $stmt->execute();

            return $stmt;
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }

    // create campaign
    public function create()
    {
        try {
            // insert query
            $query = "INSERT INTO
                " . $this->campaigns_table . "
            SET
                name=:name,
                type=:type,
                goal=:goal,
                start=:start,
                end=:end,
                active=:active,
                gift_title=:gift_title,
                gift_link=:gift_link,
                notes=:notes
                ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->name = htmlspecialchars(strip_tags($this->name));
            $this->type = htmlspecialchars(strip_tags($this->type));
            $this->goal = intval($this->goal);
            $this->start = htmlspecialchars(strip_tags($this->start));
            $this->end = htmlspecialchars(strip_tags($this->end));
            $this->active = intval($this->active);
            $this->gift_title = $this->gift_title;
            $this->gift_link = $this->gift_link;
            $this->notes = $this->notes;

            // bind values
            $stmt->bindParam(":name", $this->name);
            $stmt->bindParam(":type", $this->type);
            $stmt->bindParam(":goal", $this->goal);
            $stmt->bindParam(":start", $this->start);
            $stmt->bindParam(":end", $this->end);
            $stmt->bindParam(":active", $this->active);
            $stmt->bindParam(":gift_title", $this->gift_title);
            $stmt->bindParam(":gift_link", $this->gift_link);
            $stmt->bindParam(":notes", $this->notes);


            // execute query
            $stmt->execute();

            // Read the Premium and send as JSON
            echo json_encode($this->read($this->conn->lastInsertId())->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }

    // update the campaign
    public function update($id)
    {
        try {
            // update query
            $query = "UPDATE
                " . $this->campaigns_table . "
            SET
                name=:name,
                type=:type,
                goal=:goal,
                start=:start,
                end=:end,
                active=:active,
                gift_title=:gift_title,
                gift_link=:gift_link,
                notes=:notes
            WHERE
                id = :id";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->id = $this->id;
            $this->name = htmlspecialchars(strip_tags($this->name));
            $this->type = $this->type;
            $this->goal = intval($this->goal);
            $this->start = $this->start;
            $this->end = $this->end;
            $this->active = intval($this->active);

            // bind values
            $stmt->bindParam(":id", $this->id);
            $stmt->bindParam(":name", $this->name);
            $stmt->bindParam(":type", $this->type);
            $stmt->bindParam(":goal", $this->goal);
            $stmt->bindParam(":start", $this->start);
            $stmt->bindParam(":end", $this->end);
            $stmt->bindParam(":active", $this->active);
            $stmt->bindParam(":gift_title", $this->gift_title);
            $stmt->bindParam(":gift_link", $this->gift_link);
            $stmt->bindParam(":notes", $this->notes);

            // execute query
            $stmt->execute();

            // Read the Premium and send as JSON
            echo json_encode($this->read($this->id)->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
    // delete the campaign
    public function delete($campaign_id)
    {
        try {
            // integrity checks

            // Does it exist?
            if ($this->read($campaign_id)->rowCount() < 1) {
                http_response_code(404); // Not Found
                throw new Exception("[Campaign ID] could not be found.");
            }

            // Can it be deleted?
            if ((new Donation($this->conn))->readbyCampaign($campaign_id)->rowCount() < 0) {
                http_response_code(400); // Bad Request
                throw new Exception("Campaign must not have donations.");
            }

            // delete query
            $query = "DELETE FROM " . $this->campaigns_table . " WHERE id = ?";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->id = htmlspecialchars(strip_tags($this->id));

            // bind id of record to delete
            $stmt->bindParam(1, $this->id, PDO::PARAM_INT);

            // execute query
            $stmt->execute();
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
}
