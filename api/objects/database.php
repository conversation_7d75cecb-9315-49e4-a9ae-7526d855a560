<?php
class Database
{
    public $conn;

    // get the database connection
    public function getConnection()
    {
        global $db_host;
        global $db_name;
        global $db_user;
        global $db_pass;
        global $db_type;

        $this->conn = null;

        try {
            $this->conn = new PDO($db_type.":host=" . $db_host . ";dbname=" . $db_name, $db_user, $db_pass);            
            $this->conn->exec("SET NAMES utf8mb4");
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            die("Connection error: " . $e->getMessage());
        }

        return $this->conn;
    }
}
// instantiate database
$database = new Database();
$db = $database->getConnection();
