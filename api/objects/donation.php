<?php
use Kpfa\Service\DonationService;

// Provides class for Donation
class Donation {
    // database connection and table code
    private $conn;
    private $donations_table = "donations";
    private $payments_table = "payments";
    private $premiums_table = "premiums";
    private $shipments_table = "shipments";
    private $shipment_addresses_table = "shipment_addresses";
    private $campaigns_table = "campaigns";
    private $donors_table = "donors";
    private $premium_categories_table = "premium_categories";
    private $subscriptions_table = "subscriptions";
    private $donationService;

    // constructor with $db as database connection
    public function __construct($db, DonationService $donationService = null) {
        $this->conn = $db;
        $this->donationService = $donationService;
    }

    // create donation
    public function create() {
        try {
            global $admin_email;
            /*
            // This function creates a donation, optionally assigns campaign_id, optionally a gift(premium) w/ shipment, and a payment. //
            * DONOR lookup/read
                + fraud check
                + if not found: DONOR INSERT
            * CAMPAIGN SEARCH
            * PREMIUMS lookup/read qty
            * DONATIONS INSERT
                + fraud check
            * SHIPMENT_address CREATE
            * SHIPMENT INSERT
            * PAYMENTS CREATE
        */
            // instantiate classes used
            $donor = new Donor($this->conn);
            $campaign = new Campaign($this->conn);
            $premium = new Premium($this->conn);
            $shipping = new Shipping($this->conn);
            $shipping_address = new Shipping_address($this->conn);
            $payment = new Payment($this->conn);
            $email_msg = new Email($this->conn);

            // set DONOR variables
            $donor->lastname = $this->lastname;
            $donor->firstname = $this->firstname;
            $donor->postal_code = $this->postal_code;
            $donor->address1 = $this->address1;
            $donor->address2 = $this->address2;
            $donor->city = $this->city;
            $donor->state = $this->state;
            $donor->country = $this->country;
            $donor->email = $this->email;
            $donor->phone = $this->phone;
            $donor->paperless = $this->paperless;

            // set ipaddress
            $this->ipaddress = $_SERVER["REMOTE_ADDR"];

            if (isset($this->donor_id)) {
                /*
                 * This branch was added to improve the reliability of linking donations to existing donor accounts.
                 * When a user selects an existing donor in Station Admin, we have their donor_id number.
                 * The selected donor_id is sent in the create donation POST request,
                 * and if it is present this branch will execute, bypassing the searchDonor method. 🎉
                 */

                $donor->donor_id = $this->donor_id;
                $this->customer_id = $donor->read($this->donor_id)->fetch()[
                    "stripe_cus_id"
                ];
            } else {
                // check if there is an existing donor
                $searchDonor = null;

                // Search for an existing donor.
                if ($this->source === "WebSite") {
                    $searchDonor = $donor->searchDonor()->fetch();
                }

                // if donor exists (Production only)
                if (
                    $searchDonor
                ) {
                // returns stmt or null when empty
                // todo: determine if we want to remove the data overwriting capabilities (donor->update) in this branch.

                // Set donor_id (if found, and it should be)
                if (isset($searchDonor["donor_id"])) {
                    // Set donor_id
                    $this->donor_id = $searchDonor["donor_id"];
                }
                // Set stripe_cus_id (if found)
                if (isset($searchDonor["stripe_cus_id"])) {
                    $this->customer_id = $searchDonor["stripe_cus_id"]; // Stripe CUS ID Found
                }
                // if email empty in DB, try and update.
                if (empty($searchDonor["email"])) {
                    // instantiate Donor class and call update() function
                    $donor->update($this->donor_id, "email", $this->email);
                }
                // if phone empty in DB, try and update.
                if (empty($searchDonor["phone"])) {
                    // instantiate Donor class and call update() function
                    $donor->update($this->donor_id, "phone", $this->phone);
                }

                /* [DONOR] FRAUD CHECK */
                if ($searchDonor["donor_type"] == "Fraudulent") {
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "We've detected unusual activity and are unable to accept your donation at this time, please call the station to complete your transaction. Your CC has not been charged.",
                    );
                }
                } else {
                    /* [DONOR] CREATE if not found (or on staging) */
                    if (!($this->donor_id = $donor->create())) {
                        // INSERT new Donor (if false display message)
                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "Your donor information could not be entered into the database, please try again later. The station admin has been notified",
                        );
                    }
                    // Donor created successfully
                    // Note: Duplicate detection now only runs on phone number updates, not on creation
                }
            }

            /* [CAMPAIGN] READ */
            //only automatically assign campaign for donations from the Public Donation App
            if ($this->source === "WebSite") {
                // try and look up campaign by current time (regardless of active status)
                $campaign_result = $campaign
                    ->readbydate(date("Y-m-d H:i:s"))
                    ->fetch();
                // returns id or null when empty
                if (
                    $campaign_result &&
                    null !== ($this->campaign_id = $campaign_result["id"])
                ) {
                    $this->campaign_id = $campaign_result["id"];
                } else {
                    $this->campaign_id = null; // no campaign found
                    // As of the addition of this logic, the Public Donation App simply sets the "Pledge" type for all donations.
                    // So this branch is to enforce our business logic around donation types.
                    // If there is no campaign active, and it's a donation installment of "One-Time", we modify the donation type to "Minor" or "Major"
                    if (
                        $this->source === "WebSite" &&
                        $this->installment === "One-Time"
                    ) {
                        if ($this->amount >= 1000) {
                            $this->type = "Major";
                        } elseif ($this->amount < 1000) {
                            $this->type = "Minor";
                        }
                    }
                }
            }

            /* [PREMIUM] READ (CHECK STOCK)*/
            if (!empty($this->premiums)) {
                // We have premiums!
                $this->quantity = 1; //FIXME

                // [Premium] check validity and QTY
                foreach ($this->premiums as &$item) {
                    if (
                        !empty(
                            ($premium_fetch = $premium
                                ->readPremium($item)
                                ->fetch())
                        )
                    ) {
                        if ($premium_fetch["qty"] <= 0) {
                            http_response_code(400); // Bad Request
                            throw new Exception(
                                "Your donation could not be processed. We are out of stock, please select a different Thank-You Gift.",
                            );
                        }
                    } else {
                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "Your premium could not found. Please select a different Thank-You Gift.",
                        );
                    }
                }
            } else {
                $this->premiums = null;
            }

            /* [DONATION] FRAUD CHECK */
            // Use higher fraud limit for staging server to allow development testing
            $isStaging = isset($_SERVER['SERVER_NAME']) && $_SERVER['SERVER_NAME'] === 'api.staging.kpfa.org';

            // Set fraud limit: 500 for staging, 15 for production
            $fraudLimit = $isStaging ? 500 : 15;

            if ($this->fraud_check() >= $fraudLimit) {
                // if more than limit donations within the hour, then likely fraud.
                http_response_code(400); // Bad Request
                throw new Exception(
                    "We've detected unusual activity and are unable to accept your online donations at this time, please call the station to complete your transaction. Your CC has not been charged.",
                );
            }

            /* [DONATION] CREATE */

            // Donations (INSERT)
            $query =
                "INSERT INTO
                    " .
                $this->donations_table .
                "
                    SET
                    donor_id=:donor_id,
                    type=:type,
                    amount=:amount,
                    installment=:installment,
                    comments=:comments,
                    add_me=:add_me,
                    read_onair=:read_onair,
                    transaction_id=:transaction_id,
                    ipaddress=:ipaddress,
                    show_name=:show_name,
                    program_wp_id=:program_wp_id,
                    source=:source,
                    browser=:browser,
                    donation_match=:donation_match,
                    premiums_cart=:premiums_cart,
                    campaign_id=:campaign_id";
            $stmt = $this->conn->prepare($query);

            // set variables
            $this->transaction_id = md5(uniqid(rand(), true)); // true adds more_entropy
            // browser
            if (isset($_SERVER["HTTP_USER_AGENT"])) {
                $this->browser = $_SERVER["HTTP_USER_AGENT"];
            }

            $showAiring = $this->donationService->findShowAiring10MinutesAgo();
            if ($showAiring) {
                $this->show_name = $showAiring["name"];
                $this->program_wp_id = $showAiring["wp_id"];
            } else {
                //if we can't find a matching show that was airing 10 minutes ago in the API's db, fall back to the currently airing show on WordPress
                $this->show_name = json_decode(
                    file_get_contents(
                        "https://kpfa.org/wp-json/wp/v2/episode?per_page=1",
                    ),
                )["0"]->program_name;
            }

            // premiums_cart
            if (isset($this->premiums_cart)) {
                $this->premiums_cart = strip_tags(
                    json_encode($this->premiums_cart),
                );
            } else {
                $this->premiums_cart = null;
            }
            // Bind DONATION values
            $stmt->bindParam(":donor_id", $this->donor_id);
            $stmt->bindParam(":type", $this->type);
            $stmt->bindParam(":amount", $this->amount);
            $stmt->bindParam(":installment", $this->installment);
            $stmt->bindParam(":comments", $this->comments);
            $stmt->bindParam(":add_me", $this->add_me);
            $stmt->bindParam(":read_onair", $this->read_onair);
            $stmt->bindParam(":transaction_id", $this->transaction_id);
            $stmt->bindParam(":ipaddress", $this->ipaddress);
            $stmt->bindParam(":show_name", $this->show_name);
            $stmt->bindParam(":program_wp_id", $this->program_wp_id);
            $stmt->bindParam(":source", $this->source);
            $stmt->bindParam(":browser", $this->browser);
            $stmt->bindParam(":donation_match", $this->donation_match);
            $stmt->bindParam(":premiums_cart", $this->premiums_cart);
            $stmt->bindParam(":campaign_id", $this->campaign_id);

            // Execute Donation Query
            if ($stmt->execute()) {
                // set donation_id
                $this->donation_id = $this->conn->lastInsertId();

                global $transaction_id; // needed for create.php
                $transaction_id = $this->transaction_id;
                // bind the donation_id for donor update
            } else {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "We're having trouble processing this donation, please try again later. The station admin has been notified of this issue.",
                );
            }

            /* [SHIPMENTS] CREATE */
            if (!empty($this->premiums)) {
                // We have premiums!
                // iterate through and add
                foreach ($this->premiums as $this->premium_id) {
                    // set SHIPPING variables
                    $shipping->premium_id = $this->premium_id;
                    $shipping->donation_id = $this->donation_id;
                    $shipping->quantity = $this->quantity;
                    // insert record (reduces qty in function)
                    // pass donation source to prevent reducing premium quantity for abandoned donations
                    if ($shipping->create($this->source, $this->installment)) {
                        // success
                    }
                }
                // set SHIPPING Address variables
                $shipping_address->firstname = $this->shipping_firstname;
                $shipping_address->lastname = $this->shipping_lastname;
                $shipping_address->address1 = $this->shipping_address1;
                $shipping_address->address2 = $this->shipping_address2;
                $shipping_address->city = $this->shipping_city;
                $shipping_address->state = $this->shipping_state;
                $shipping_address->country = $this->shipping_country;
                $shipping_address->postal_code = $this->shipping_postal_code;
                $shipping_address->donation_id = $this->donation_id;
                // add shipping_address
                if ($shipping_address->create()) {
                    // success
                }
            }

            /* [PAYMENTS] CREATE */
            // set Payment variables
            $payment->donation_id = $this->donation_id;
            $payment->amount = floatval($this->amount);
            if (!empty($this->processor)) {
                $payment->processor = $this->processor;
            }
            if (empty($this->date_deposited)) {
                $payment->date_deposited = date(
                    "Y-m-d H:i:s",
                    strtotime("now"),
                );
            } else {
                $payment->date_deposited = $this->date_deposited;
            }

            if (!empty($this->payment_id)) {
                $payment->payment_id = $this->payment_id;
            }

            // [PAYMENT] Stripe (Use PaymentIntent)
            if ($this->method == "stripe") {
                $this->processor = "Stripe";
                // One-Time or Monthly (handled differently in intent)
                if (
                    !($client_secret = $payment->create_stripe_intent_payment(
                        $this,
                    ))
                ) {
                    // stripe failed, delete donation & exit
                    $this->delete($this->donation_id);
                    exit(); // error thrown in create_stripe_intent_payment
                }
            }
            // [PAYMENT] Stripe (card)
            if ($this->method == "card") {
                // Card
                $this->processor = "Stripe";
                // returns $stmt on success or message on failure.
                if (!$payment->create_stripe_payment($this)) {
                    // One-Time or Monthly logic handled in Payment()
                    // Charge failed, delete donation & exit
                    $this->delete($this->donation_id);
                    exit(); // error thrown in create_stripe_payment
                }
            }
            // [Payment] Check Payment
            if ($this->method == "check" || $this->method == "epayment") {
                // assign payment status
                $payment->status = $this->status;
                if (
                    !$payment->create_epayment_or_check_payment($this->method)
                ) {
                    // Charge failed, delete donation & exit
                    $this->delete($this->donation_id);
                    exit(); // error thrown in create_epayment_or_check_payment
                }
            }
            // [Payment] Cash Payment
            if ($this->method == "cash") {
                // Cash
                // Call create_cash_payment function from Payment Class
                if (!$payment->create_cash_payment()) {
                    // Charge failed, delete donation & exit
                    $this->delete($this->donation_id);
                    exit(); // error thrown in create_cash_payment
                }
            }
            // [Payment] in-kind Payment
            if ($this->method == "in-kind") {
                // in-kind
                // Call create_in_kind_payment function from Payment Class
                if (!$payment->create_in_kind_payment()) {
                    // Charge failed, delete donation & exit
                    $this->delete($this->donation_id);
                    exit(); // error thrown in create_cash_payment
                }
            }
            // [Payment] bill-me-later Payment
            if ($this->method == "bill-me-later") {
                // bill-me-later
                $this->processor = "Staff"; // so email works
            }

            /* [RETURN] Section */
            // Read the donation and send as JSON.
            if (isset($client_secret)) {
                $payload = $this->readDonationByTransactionID(
                    $this->transaction_id,
                )->fetch(PDO::FETCH_ASSOC);
                $payload["client_secret"] = $client_secret; // add client secret
                echo json_encode($payload, JSON_NUMERIC_CHECK);
            } else {
                echo json_encode(
                    $this->readDonationByTransactionID(
                        $this->transaction_id,
                    )->fetch(PDO::FETCH_ASSOC),
                    JSON_NUMERIC_CHECK,
                );
            }

            /* [Communication] Section  */

            // Execute Email (Receipt and Thank-you) process (after DB query)
            if (
                isset($this->transaction_id) &&
                isset($this->email) &&
                $this->method != "stripe" &&
                $this->method != "card"
            ) {
                // Checking that the donation method is not stripe is necessary to prevent confirmation emails from sending to donors before they complete an online payment.
                // The corresponding frontend logic is to filter out donations with no payments where "source" == "WebSite", as we have no mechanism for donors to create
                // donations from the Public Donation app without immediately paying.

                $email_msg->sendMessage("thankyou", $this->transaction_id); // call sendMessage function from Email Class
            } else {
                // Execute Billing process (TODO)
            }
        } catch (PDOException $e) {
            // MySQL Exception
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
            error_log($e->getMessage(), 0);

            // send email to admin
            mail(
                "$admin_email",
                "DONATION INSERT ERROR: $this->firstname $this->lastname",
                $e->getMessage(),
            );
        }
    }

    // create donation for existing donor
    public function createDonationForExistingDonor() {
        try {
            global $admin_email; // use global_admin variable
            // CREATE query
            $query =
                "INSERT INTO
                " .
                $this->donations_table .
                "
                SET
                timestamp=:timestamp,
                donor_id=:donor_id,
                transaction_id=:transaction_id,
                type=:type,
                amount=:amount,
                installment=:installment,
                comments=:comments,
                read_onair=:read_onair,
                ipaddress=:ipaddress,
                browser=:browser,
                show_name=:show_name,
                program_wp_id=:program_wp_id,
                source=:source,
                campaign_id=:campaign_id,
                donation_match=:donation_match";
            // PREPARE query
            $stmt = $this->conn->prepare($query);
            // BIND Donation values
            $stmt->bindParam(":timestamp", $this->timestamp);
            $stmt->bindParam(":donor_id", $this->donor_id, PDO::PARAM_INT);
            $stmt->bindParam(":transaction_id", $this->transaction_id);
            $stmt->bindParam(":type", $this->donation_type);
            $stmt->bindParam(":amount", $this->amount);
            $stmt->bindParam(":installment", $this->installment);
            $stmt->bindParam(":comments", $this->comments);
            $stmt->bindParam(":read_onair", $this->read_onair);
            $stmt->bindParam(":ipaddress", $this->ipaddress);
            $stmt->bindParam(":browser", $this->browser);
            $stmt->bindParam(":show_name", $this->show_name);
            $stmt->bindParam(":program_wp_id", $this->program_wp_id);
            $stmt->bindParam(":source", $this->source);
            $stmt->bindParam(":campaign_id", $this->campaign_id);
            $stmt->bindParam(":donation_match", $this->donation_match);

            // INSERT Donation
            if ($stmt->execute()) {
                global $donation_id; // needed for create.php
                $donation_id = $this->conn->lastInsertId();
                $this->donation_id = $this->conn->lastInsertId();
                global $transaction_id; // needed for create.php

                // bind the donation_id for donor update
            } else {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "We're having trouble processing this donation, please try again later.",
                );
            }

            // ** [Communication] Section  ** //
            // Execute Email (Receipt and Thank-you) process (after DB query)
            if ($this->email) {
                //(new Email($this->conn))->sendMessage("thankyou", $transaction_id); // call sendMessage function from Email Class
            } else {
                // Execute Billing process
                // include 'mail.php'; // TODO implement
            }
            // ** [RETURN] Section  ** //
            return true;
        } catch (PDOException $e) {
            // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            // PHP Error message
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // cancel a recurring donation
    public function cancel_stripe_subscription($subscription_id) {
        try {
            // include stripe
            global $stripe;

            // Cancels a customer's subscription immediately. The customer will not be charged again for the subscription.
            $stripe->subscriptions->cancel($subscription_id, []);

            // ** [RETURN] Section  ** //
            return true;
        } catch (Exception $e) {
            http_response_code(400); // bad request
            // could not cancel donors subscription
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // fraud check
    public function fraud_check() {
        try {
            // Count the number of website donation per IP address
            $query =
                "SELECT
                COUNT(`id`) as `count`
            FROM
                " .
                $this->donations_table .
                "
            WHERE
                `source` = 'Website'
                AND `timestamp` > DATE_SUB(NOW(), INTERVAL '1' HOUR)
                AND ipaddress =:ipaddress;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be found
            $stmt->bindParam(":ipaddress", $this->ipaddress);

            // execute query
            $stmt->execute();

            // return count
            return $stmt->fetch(PDO::FETCH_OBJ)->count;
        } catch (PDOException $e) {
            // MySQL Exception
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            // Code Exception
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // update the donation
    public function update() {
        // this function updates the donation, shipment, shipment_address
        try {
            // Check: donation_id?
            if (!empty($this->donation_id)) {
                $this->donation_id = htmlspecialchars(
                    strip_tags($this->donation_id),
                );
            } else {
                http_response_code(400); // Bad Request
                throw new Exception("[donation_id] is required.");
            }
            // Check: donation exist?
            if (
                !$this->readDonation($this->donation_id)->fetch(
                    PDO::FETCH_ASSOC,
                )
            ) {
                http_response_code(400); // Bad Request
                throw new Exception("[donation_id] could not be found");
            }

            // Check: campaign_id
            if (isset($this->campaign_id) && !empty($this->campaign_id)) {
                // sanitize
                $this->campaign_id = htmlspecialchars(
                    strip_tags($this->campaign_id),
                );
                // SET  campaign Object
                $campaign = new Campaign($this->conn);

                // Check to make sure campaign_id exists
                if ($campaign->read($this->campaign_id)->rowCount() == 0) {
                    http_response_code(400); // Bad Request
                    throw new Exception("[campaign_id] not found");
                }
            } else {
                $this->campaign_id = null;
            }

            // Check if shipment_addresses record exists, if not create one when shipping data is provided
            $shipment_address_exists = false;
            if ($this->shipping_firstname || $this->shipping_lastname || $this->shipping_address1 || 
                $this->shipping_city || $this->shipping_state || $this->shipping_country || $this->shipping_postal_code) {
                
                // Check if shipment_addresses record exists
                $check_shipment_address_query = 
                    "SELECT id FROM " . $this->shipment_addresses_table . " WHERE donation_id = :donation_id";
                $check_stmt = $this->conn->prepare($check_shipment_address_query);
                $check_stmt->bindParam(":donation_id", $this->donation_id, PDO::PARAM_INT);
                $check_stmt->execute();
                
                if ($check_stmt->rowCount() > 0) {
                    $shipment_address_exists = true;
                } else {
                    // Create shipment_addresses record if it doesn't exist
                    $shipping_address = new Shipping_address($this->conn);
                    $shipping_address->firstname = $this->shipping_firstname;
                    $shipping_address->lastname = $this->shipping_lastname;
                    $shipping_address->address1 = $this->shipping_address1;
                    $shipping_address->address2 = $this->shipping_address2;
                    $shipping_address->city = $this->shipping_city;
                    $shipping_address->state = $this->shipping_state;
                    $shipping_address->country = $this->shipping_country;
                    $shipping_address->postal_code = $this->shipping_postal_code;
                    $shipping_address->donation_id = $this->donation_id;
                    
                    if ($shipping_address->create()) {
                        $shipment_address_exists = true;
                    }
                }
            }

            // Define Queries:
            // UPDATE donation & shipment_address query
            // TODO: shipment_address should delete + recreate (if presented with shipment_addresses)
            $donation_update_query =
                "
                UPDATE
                " .
                $this->donations_table .
                "
		        LEFT JOIN " .
                $this->shipment_addresses_table .
                " ON " .
                $this->shipment_addresses_table .
                ".donation_id = " .
                $this->donations_table .
                ".id
                SET
                " .
                $this->shipment_addresses_table .
                ".firstname=:shipping_firstname,
                " .
                $this->shipment_addresses_table .
                ".lastname=:shipping_lastname,
                " .
                $this->shipment_addresses_table .
                ".address1=:shipping_address1,
                " .
                $this->shipment_addresses_table .
                ".address2=:shipping_address2,
                " .
                $this->shipment_addresses_table .
                ".city=:shipping_city,
                " .
                $this->shipment_addresses_table .
                ".state=:shipping_state,
                " .
                $this->shipment_addresses_table .
                ".country=:shipping_country,
                " .
                $this->shipment_addresses_table .
                ".postal_code=:shipping_postal_code,
                " .
                $this->donations_table .
                ".amount=:amount,
                " .
                $this->donations_table .
                ".installment=:installment,
                " .
                $this->donations_table .
                ".comments=:comments,
                " .
                $this->donations_table .
                ".type=:type,
                " .
                $this->donations_table .
                ".read_onair=:read_onair,
                " .
                $this->donations_table .
                ".donation_match=:donation_match,
                " .
                $this->donations_table .
                ".campaign_id=:campaign_id
                WHERE
                " .
                $this->donations_table .
                ".id=:donation_id";

            // DELETE Previous shipment(s) from shipping table
            $shipment_delete_query =
                "DELETE FROM
                 " .
                $this->shipments_table .
                "
                    WHERE
                    " .
                $this->shipments_table .
                ".donation_id=:donation_id
                    AND " .
                $this->shipments_table .
                ".status = 'New'
                    ";

            // ADD quantity back to old Premiums table
            $premiums_increase_query =
                "UPDATE
                " .
                $this->premiums_table .
                "
                    SET
                        `qty` = `qty` + 1
                    WHERE
                        `id`=:old_premium_id
                    ";
            // INSERT New Premiums into Shipments
            $shipment_insert_query =
                "INSERT INTO
                " .
                $this->shipments_table .
                "
                    SET
                        donation_id=:donation_id,
                        premium_id=:new_premium_id,
                        quantity=:quantity";

            // REDUCE Qty of Premiums from Premiums table
            $premiums_reduce_query =
                "UPDATE
                " .
                $this->premiums_table .
                "
                    SET
                        `qty` = `qty` - 1
                    WHERE
                        `id`=:new_premium_id
                    AND `qty` > 0";
            // Prepare queries
            $donation_update_stmt = $this->conn->prepare(
                $donation_update_query,
            ); // UPDATE DONATION, SHIPMENT ADDRESS
            $premiums_increase_stmt = $this->conn->prepare(
                $premiums_increase_query,
            ); // INCREASE QTY PREMIUM
            $premiums_reduce_stmt = $this->conn->prepare(
                $premiums_reduce_query,
            ); // REDUCE QTY PREMIUM
            $shipment_delete_stmt = $this->conn->prepare(
                $shipment_delete_query,
            ); // DELETE OLD SHIPMENTS(S)
            $shipment_insert_stmt = $this->conn->prepare(
                $shipment_insert_query,
            ); // INSERT NEW SHIPMENTS(S)

            // Sanitize
            //check to make sure campaign_id exists

            // comments
            if (isset($this->comments) && !empty($this->comments)) {
                $this->comments = htmlspecialchars(
                    strip_tags(substr($this->comments, 0, 499)),
                    ENT_COMPAT,
                    "UTF-8",
                ); //Stripe 500 character limit
            } else {
                $this->comments = null;
            }
            $this->type = htmlspecialchars(strip_tags($this->type));
            $this->shipping_firstname =
                $this->shipping_firstname !== null
                    ? htmlspecialchars(
                        strip_tags(
                            ucwords(
                                strtolower(trim($this->shipping_firstname)),
                            ),
                        ),
                    )
                    : null;
            $this->shipping_lastname =
                $this->shipping_lastname !== null
                    ? htmlspecialchars(
                        strip_tags(
                            ucwords(strtolower(trim($this->shipping_lastname))),
                        ),
                    )
                    : null;
            $this->shipping_address1 =
                $this->shipping_address1 !== null
                    ? htmlspecialchars(
                        strip_tags(
                            ucwords(strtolower(trim($this->shipping_address1))),
                        ),
                    )
                    : null;
            $this->shipping_address2 =
                $this->shipping_address2 !== null
                    ? htmlspecialchars(
                        strip_tags(
                            ucwords(strtolower(trim($this->shipping_address2))),
                        ),
                    )
                    : null;
            $this->shipping_city =
                $this->shipping_city !== null
                    ? htmlspecialchars(
                        strip_tags(
                            ucwords(strtolower(trim($this->shipping_city))),
                        ),
                    )
                    : null;
            $this->shipping_state =
                $this->shipping_state !== null
                    ? htmlspecialchars(strip_tags(trim($this->shipping_state)))
                    : null;
            $this->shipping_country =
                $this->shipping_country !== null
                    ? htmlspecialchars(
                        strip_tags(trim($this->shipping_country)),
                    )
                    : null;
            $this->shipping_postal_code =
                $this->shipping_postal_code !== null
                    ? htmlspecialchars(strip_tags($this->shipping_postal_code))
                    : null;
            $this->amount =
                $this->amount !== null
                    ? htmlspecialchars(strip_tags($this->amount))
                    : null;
            $this->installment =
                $this->installment !== null
                    ? htmlspecialchars(strip_tags($this->installment))
                    : null;
            $this->comments =
                $this->comments !== null
                    ? htmlspecialchars(strip_tags($this->comments))
                    : null;
            $this->read_onair = boolval($this->read_onair);
            $this->donation_match = boolval($this->donation_match);
            $this->source = htmlspecialchars(strip_tags($this->source));

            // SF shipping_city
            if (
                $this->shipping_city == "SF" ||
                $this->shipping_city == "San Fransisco" ||
                $this->shipping_city == "S.f" ||
                $this->shipping_city == "S.f." ||
                $this->shipping_city == "S F" ||
                $this->shipping_city == "Sf"
            ) {
                $this->shipping_city = "San Francisco";
            } // Berkeley shipping_city
            if (
                $this->shipping_city == "Berk" ||
                $this->shipping_city == "Berkley" ||
                $this->shipping_city == "Berkely" ||
                $this->shipping_city == "Berkly" ||
                $this->shipping_city == "Burkeley" ||
                $this->shipping_city == "Burkley"
            ) {
                $this->shipping_city = "Berkeley";
            }
            // Santa Cruz
            if ($this->shipping_city == "Santa Cruze") {
                $this->shipping_city = "Santa Cruz";
            }

            // Premiums
            if ($this->premiums) {
                $this->quantity = 1; //FIXME
            } else {
                $this->premiums = null;
                $this->qty = null;
            }

            // bind values
            $donation_update_stmt->bindParam(
                ":shipping_firstname",
                $this->shipping_firstname,
            );
            $donation_update_stmt->bindParam(
                ":shipping_lastname",
                $this->shipping_lastname,
            );
            $donation_update_stmt->bindParam(
                ":shipping_address1",
                $this->shipping_address1,
            );
            $donation_update_stmt->bindParam(
                ":shipping_address2",
                $this->shipping_address2,
            );
            $donation_update_stmt->bindParam(
                ":shipping_city",
                $this->shipping_city,
            );
            $donation_update_stmt->bindParam(
                ":shipping_state",
                $this->shipping_state,
            );
            $donation_update_stmt->bindParam(
                ":shipping_country",
                $this->shipping_country,
            );
            $donation_update_stmt->bindParam(
                ":shipping_postal_code",
                $this->shipping_postal_code,
            );
            $donation_update_stmt->bindParam(":amount", $this->amount);
            $donation_update_stmt->bindParam(
                ":installment",
                $this->installment,
            );
            $donation_update_stmt->bindParam(":comments", $this->comments);
            $donation_update_stmt->bindParam(":type", $this->type);
            $donation_update_stmt->bindParam(
                ":read_onair",
                $this->read_onair,
                PDO::PARAM_BOOL,
            );
            $donation_update_stmt->bindParam(
                ":donation_match",
                $this->donation_match,
                PDO::PARAM_BOOL,
            );
            $donation_update_stmt->bindParam(
                ":campaign_id",
                $this->campaign_id,
                PDO::PARAM_INT,
            );
            $donation_update_stmt->bindParam(
                ":donation_id",
                $this->donation_id,
                PDO::PARAM_INT,
            );

            // BIND OLD SHIPMENTS(S)
            $shipment_delete_stmt->bindParam(
                ":donation_id",
                $this->donation_id,
                PDO::PARAM_INT,
            );

            // UPDATE DONATION (STMT)
            if ($donation_update_stmt->execute()) {
                global $donation_id;
                $donation_id = $this->donation_id;
            } else {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "The donation " .
                        $this->donation_id .
                        " could not be updated, please contact the station with the error information: \"" .
                        print_r($donation_update_stmt->errorInfo()[2], true) .
                        "\"",
                );
            }

            // DELETE SHIPMENT(s) (shipment_delete_stmt)

            // GRAB ID's before we delete
            $readGifts = $this->readGifts($donation_id);
            // number of rows
            $rc = $readGifts->rowCount();
            // check if more than 0 records found
            if ($rc > 0) {
                // retrieve premium ID's
                $old_premiums_id_arr = [];
                while ($rg = $readGifts->fetch(PDO::FETCH_ASSOC)) {
                    $old_premiums_id_arr[] = $rg["premium_id"];
                }

                // DELETE the old shipment (shipment_delete_query)
                if ($shipment_delete_stmt->execute()) {
                    // since we were able to delete, we can add back the premium_qty to the old id (which we no longer know)
                    if (!empty($old_premiums_id_arr)) {
                        // premiums array
                        foreach ($old_premiums_id_arr as &$old_item) {
                            // bind values
                            $premiums_increase_stmt->bindParam(
                                ":old_premium_id",
                                $old_item,
                                PDO::PARAM_INT,
                            );
                            if ($premiums_increase_stmt->execute()) {
                                // Restore the premium count (premiums_increase_query)
                            } else {
                                http_response_code(400); // Bad Request
                                throw new Exception(
                                    "Your previous thank-you gift could not be returned in the database, please contact the station with the error information: \"" .
                                        print_r(
                                            $premiums_increase_stmt->errorInfo()[2],
                                            true,
                                        ) .
                                        "\"",
                                );
                            }
                        }
                    }
                } else {
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "The thank-you gift " .
                            $this->donation_id .
                            " could not be deleted, please contact the station with the error information: \"" .
                            print_r(
                                $shipment_delete_stmt->errorInfo()[2],
                                true,
                            ) .
                            "\"",
                    );
                }
            }
            // INSERT updated SHIPMENT(s) (shipment_insert_stmt) // TODO shipments.status
            if (!empty($this->premiums)) {
                foreach ($this->premiums as &$item) {
                    // initialize object
                    $premium = new Premium($this->conn);
                    // check to see if premium exists first
                    if (
                        !$premium->readPremium($item)->fetch(PDO::FETCH_ASSOC)
                    ) {
                        //                    if (!(new Premium($db))->readPremium($item)->fetch(PDO::FETCH_ASSOC)) {
                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "The thank-you gift $item does not exist",
                        );
                    }
                    // bind values
                    $shipment_insert_stmt->bindParam(
                        ":donation_id",
                        $this->donation_id,
                        PDO::PARAM_INT,
                    );
                    $shipment_insert_stmt->bindParam(
                        ":new_premium_id",
                        $item,
                        PDO::PARAM_INT,
                    );
                    $shipment_insert_stmt->bindParam(
                        ":quantity",
                        $this->quantity,
                        PDO::PARAM_INT,
                    );
                    // $shipment_insert_stmt->bindParam(":status", $this->status, PDO::PARAM_STR); // TODO shipments.status

                    // insert shipments into DB
                    if ($shipment_insert_stmt->execute()) {
                        // since we were able to insert, we can reduce the premium_qty

                        // REDUCE PREMIUM(s) qty (premiums_reduce_stmt)
                        foreach ($this->premiums as &$item) {
                            // bind values
                            $premiums_reduce_stmt->bindParam(
                                ":new_premium_id",
                                $item,
                                PDO::PARAM_INT,
                            );
                            if ($premiums_reduce_stmt->execute()) {
                                // Execute database premiums_reduce_query
                            } else {
                                http_response_code(400); // Bad Request
                                throw new Exception(
                                    "Your thank-you gift could not be reduced in the database, please contact the station with the error information: \"" .
                                        print_r(
                                            $premiums_reduce_stmt->errorInfo()[2],
                                            true,
                                        ) .
                                        "\"",
                                );
                            }
                        }
                    } else {
                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "Your donation premiums could not be added into the database, please contact the station with the error information: \"" .
                                print_r(
                                    $shipment_insert_stmt->errorInfo()[2],
                                    true,
                                ) .
                                "\"",
                        );
                    }
                }
            }
            // ** [RETURN] Section  ** //
            // return updated JSON
            // Read the Premium and send as JSON
            echo json_encode(
                $this->readDonation($this->donation_id)->fetch(
                    PDO::FETCH_ASSOC,
                ),
                JSON_NUMERIC_CHECK,
            );
        } catch (PDOException $e) {
            // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "donation update message" => $donation_update_stmt->errorInfo()[2],
                "premium increase message" => $premiums_increase_stmt->errorInfo()[2],
                "premium reduce message" => $premiums_reduce_stmt->errorInfo()[2],
                "shipment delete message" => $shipment_delete_stmt->errorInfo()[2],
                "shipment insert message" => $shipment_insert_stmt->errorInfo()[2],
            ]);
        } catch (Exception $e) {
            // PHP Error message
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // delete the donation shipment(s), shipment_address, shipment
    public function delete($donation_id) {
        try {
            global $admin_email; // use global_admin variable
            // only works on donations without payments or subscriptions.

            // delete query for shipments
            $shipments_delete_query =
                "DELETE FROM " .
                $this->shipments_table .
                " where donation_id = ?";

            // delete query for shipment_addresses
            $shipment_addresses_delete_query =
                "DELETE FROM " .
                $this->shipment_addresses_table .
                " where donation_id = ?";

            // delete query for donation
            $donation_delete_query =
                "DELETE FROM " . $this->donations_table . " WHERE id = ?";

            // prepare query
            $shipments_delete_stmt = $this->conn->prepare(
                $shipments_delete_query,
            );

            // prepare query
            $shipment_addresses_delete_stmt = $this->conn->prepare(
                $shipment_addresses_delete_query,
            );

            // prepare query
            $donation_delete_stmt = $this->conn->prepare(
                $donation_delete_query,
            );

            // bind
            $shipments_delete_stmt->bindParam(1, $donation_id, PDO::PARAM_INT);

            // bind
            $shipment_addresses_delete_stmt->bindParam(
                1,
                $donation_id,
                PDO::PARAM_INT,
            );

            // bind
            $donation_delete_stmt->bindParam(1, $donation_id, PDO::PARAM_INT);

            // execute shipment_addresses_delete query
            $shipments_delete_stmt->execute();

            // execute shipment_addresses_delete query
            $shipment_addresses_delete_stmt->execute();

            // execute donation_delete query
            $donation_delete_stmt->execute();

            $count = $donation_delete_stmt->rowCount(); // check affected rows using rowCount
            if ($count > 0) {
                return true;
            } else {
                http_response_code(404); // bad request
                throw new Exception("donation_id: $donation_id does not exist");
            }
        } catch (PDOException $e) {
            // MySQL Exception
            mail($admin_email, "DELETE QUERY ERROR:", $e->getMessage());
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $donation_delete_stmt->errorInfo()[2],
            ]);
        } catch (Exception $e) {
            // Code Exception
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // read donation by donation_id
    public function readDonation($keyword) {
        try {
            // This function reads a single donation by id.

            // select all query
            $donations_select_query =
                "SELECT
                " .
                $this->donations_table .
                ".`id`,
                " .
                $this->donations_table .
                ".`account_id`,
                " .
                $this->donations_table .
                ".`donor_id`,
                " .
                $this->donations_table .
                ".`timestamp`,
                " .
                $this->donors_table .
                ".`firstname`,
                " .
                $this->donors_table .
                ".`lastname`,
                " .
                $this->donors_table .
                ".`address1`,
                " .
                $this->donors_table .
                ".`address2`,
                " .
                $this->donors_table .
                ".`city`,
                " .
                $this->donors_table .
                ".`state`,
                " .
                $this->donors_table .
                ".`country`,
                " .
                $this->donors_table .
                ".`postal_code`,
                " .
                $this->shipment_addresses_table .
                ".`firstname` as shipping_firstname,
                " .
                $this->shipment_addresses_table .
                ".`lastname` as shipping_lastname,
                " .
                $this->shipment_addresses_table .
                ".`address1` as shipping_address1,
                " .
                $this->shipment_addresses_table .
                ".`address2` as shipping_address2,
                " .
                $this->shipment_addresses_table .
                ".`city` as shipping_city,
                " .
                $this->shipment_addresses_table .
                ".`state` as shipping_state,
                " .
                $this->shipment_addresses_table .
                ".`country` as shipping_country,
                " .
                $this->shipment_addresses_table .
                ".`postal_code` shipping_postal_code,
                " .
                $this->donors_table .
                ".`phone`,
                " .
                $this->donors_table .
                ".`email`,
                " .
                $this->donations_table .
                ".`type`,
                " .
                $this->donations_table .
                ".`amount` as `donation_amount`,
                " .
                $this->donations_table .
                ".`installment`,
                " .
                $this->donations_table .
                ".`comments`,
                " .
                $this->donations_table .
                ".`add_me`,
                " .
                $this->donations_table .
                ".`read_onair`,
                " .
                $this->donations_table .
                ".`transaction_id`,
                " .
                $this->donations_table .
                ".`ipaddress`,
                " .
                $this->donations_table .
                ".`browser`,
                " .
                $this->donations_table .
                ".`show_name`,
                " .
                $this->donations_table .
                ".`source`,
                " .
                $this->donations_table .
                ".`campaign_id`,
                " .
                $this->campaigns_table .
                ".`name` AS `campaign`,
                " .
                $this->donations_table .
                ".`updated`,
                " .
                $this->donations_table .
                ".`donation_match`
            FROM
            " .
                $this->donations_table .
                "
            LEFT JOIN " .
                $this->donors_table .
                " ON " .
                $this->donations_table .
                ".`donor_id` = " .
                $this->donors_table .
                ".`id`
            LEFT JOIN " .
                $this->campaigns_table .
                " ON " .
                $this->donations_table .
                ".`campaign_id` = " .
                $this->campaigns_table .
                ".`id`
            LEFT JOIN " .
                $this->shipment_addresses_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->shipment_addresses_table .
                ".`donation_id`
            WHERE " .
                $this->donations_table .
                ".`id` = ?
            ";

            // prepare query statement
            $stmt = $this->conn->prepare($donations_select_query);

            // sanitize
            $keyword = urldecode(htmlspecialchars(strip_tags($keyword)));

            $id = "{$keyword}";

            // bind
            $stmt->bindParam(1, $id);

            // execute query
            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
    // read donation by transaction_id
    public function readDonationByTransactionID($keyword) {
        // This function reads a single donation by id.

        // select all query
        $donations_select_query =
            "SELECT
        " .
            $this->donations_table .
            ".`id`,
        " .
            $this->donations_table .
            ".`account_id`,
        " .
            $this->donations_table .
            ".`donor_id`,
        " .
            $this->donations_table .
            ".`timestamp`,
        " .
            $this->donors_table .
            ".`firstname`,
        " .
            $this->donors_table .
            ".`lastname`,
        " .
            $this->donors_table .
            ".`address1`,
        " .
            $this->donors_table .
            ".`address2`,
        " .
            $this->donors_table .
            ".`city`,
        " .
            $this->donors_table .
            ".`state`,
        " .
            $this->donors_table .
            ".`country`,
        " .
            $this->donors_table .
            ".`postal_code`,
        " .
            $this->donors_table .
            ".`phone`,
        " .
            $this->donors_table .
            ".`email`,
        " .
            $this->shipment_addresses_table .
            ".`firstname` as shipping_firstname,
            " .
            $this->shipment_addresses_table .
            ".`lastname` as shipping_lastname,
            " .
            $this->shipment_addresses_table .
            ".`address1` as shipping_address1,
            " .
            $this->shipment_addresses_table .
            ".`address2` as shipping_address2,
            " .
            $this->shipment_addresses_table .
            ".`city` as shipping_city,
            " .
            $this->shipment_addresses_table .
            ".`state` as shipping_state,
            " .
            $this->shipment_addresses_table .
            ".`country` as shipping_country,
            " .
            $this->shipment_addresses_table .
            ".`postal_code` shipping_postal_code,
        " .
            $this->donations_table .
            ".`type`,
        " .
            $this->donations_table .
            ".`amount`,
        " .
            $this->donations_table .
            ".`installment`,
        " .
            $this->donations_table .
            ".`comments`,
        " .
            $this->donations_table .
            ".`add_me`,
        " .
            $this->donations_table .
            ".`read_onair`,
        " .
            $this->donations_table .
            ".`transaction_id`,
        " .
            $this->donations_table .
            ".`ipaddress`,
        " .
            $this->donations_table .
            ".`browser`,
        " .
            $this->donations_table .
            ".`show_name`,
        " .
            $this->donations_table .
            ".`source`,
        " .
            $this->donations_table .
            ".`campaign_id`,
        " .
            $this->campaigns_table .
            ".`name` AS `campaign`,
        " .
            $this->donations_table .
            ".`updated`,
        " .
            $this->donations_table .
            ".`donation_match`
            FROM
            " .
            $this->donations_table .
            "
            LEFT JOIN " .
            $this->donors_table .
            " ON " .
            $this->donations_table .
            ".`donor_id` = " .
            $this->donors_table .
            ".`id`
            LEFT JOIN " .
            $this->campaigns_table .
            " ON " .
            $this->donations_table .
            ".`campaign_id` = " .
            $this->campaigns_table .
            ".`id`
            LEFT JOIN " .
            $this->shipment_addresses_table .
            " ON " .
            $this->donations_table .
            ".`id` = " .
            $this->shipment_addresses_table .
            ".`donation_id`

            WHERE " .
            $this->donations_table .
            ".`transaction_id` = ?
            ";

        // prepare query statement
        $stmt = $this->conn->prepare($donations_select_query);

        // sanitize
        $keyword = urldecode(htmlspecialchars(strip_tags($keyword)));

        $id = "{$keyword}";

        // bind
        $stmt->bindParam(1, $id);

        // execute query
        $stmt->execute();

        $count = $stmt->rowCount(); // check affected rows using rowCount
        if ($count > 0) {
            return $stmt;
        } else {
            http_response_code(404); // not found
            throw new Exception("transaction_id: $keyword not found");
        }
    }
    // search donations by firstname, last name, id, or email
    public function searchDonations($keywords) {
        // This function searches by firstname, last name, partner_name, donation_id, id, or email.
        //TODO: Date search

        // select all query
        $donations_select_query =
            "SELECT DISTINCT
        " .
            $this->donations_table .
            ".`id`,
        " .
            $this->donations_table .
            ".`account_id`,
        " .
            $this->donations_table .
            ".`donor_id`,
        " .
            $this->donations_table .
            ".`timestamp`,
        " .
            $this->donors_table .
            ".`firstname`,
        " .
            $this->donors_table .
            ".`lastname`,
        " .
            $this->donors_table .
            ".`partner_firstname`,
        " .
            $this->donors_table .
            ".`partner_lastname`,
        " .
            $this->donors_table .
            ".`address1`,
        " .
            $this->donors_table .
            ".`address2`,
        " .
            $this->donors_table .
            ".`city`,
        " .
            $this->donors_table .
            ".`state`,
        " .
            $this->donors_table .
            ".`country`,
        " .
            $this->donors_table .
            ".`postal_code`,
        " .
            $this->shipment_addresses_table .
            ".`firstname` as shipping_firstname,
        " .
            $this->shipment_addresses_table .
            ".`lastname` as shipping_lastname,
        " .
            $this->shipment_addresses_table .
            ".`address1` as shipping_address1,
        " .
            $this->shipment_addresses_table .
            ".`address2` as shipping_address2,
        " .
            $this->shipment_addresses_table .
            ".`city` as shipping_city,
        " .
            $this->shipment_addresses_table .
            ".`state` as shipping_state,
        " .
            $this->shipment_addresses_table .
            ".`country` as shipping_country,
        " .
            $this->shipment_addresses_table .
            ".`postal_code` shipping_postal_code,
        " .
            $this->donors_table .
            ".`phone`,
        " .
            $this->donors_table .
            ".`email`,
        " .
            $this->donations_table .
            ".`type`,
        " .
            $this->donations_table .
            ".`amount` as `donation_amount`,
        " .
            $this->donations_table .
            ".`installment`,
        " .
            $this->donations_table .
            ".`comments`,
        " .
            $this->donations_table .
            ".`add_me`,
        " .
            $this->donations_table .
            ".`read_onair`,
        " .
            $this->donations_table .
            ".`transaction_id`,
        " .
            $this->donations_table .
            ".`ipaddress`,
        " .
            $this->donations_table .
            ".`browser`,
        " .
            $this->donations_table .
            ".`show_name`,
        " .
            $this->donations_table .
            ".`source`,
        " .
            $this->donations_table .
            ".`campaign_id`,
        " .
            $this->campaigns_table .
            ".`name` AS `campaign`,
        " .
            $this->donations_table .
            ".`updated`,
        " .
            $this->donations_table .
            ".`donation_match`
            FROM
            " .
            $this->donations_table .
            "
            LEFT JOIN " .
            $this->donors_table .
            " ON " .
            $this->donations_table .
            ".`donor_id` = " .
            $this->donors_table .
            ".`id`
            LEFT JOIN " .
            $this->campaigns_table .
            " ON " .
            $this->donations_table .
            ".`campaign_id` = " .
            $this->campaigns_table .
            ".`id`
            LEFT JOIN " .
            $this->shipment_addresses_table .
            " ON " .
            $this->donations_table .
            ".`id` = " .
            $this->shipment_addresses_table .
            ".`donation_id`

            WHERE CONCAT_WS(' '," .
            $this->donors_table .
            ".`firstname`, " .
            $this->donors_table .
            ".`lastname`) LIKE ?
            OR CONCAT_WS(' '," .
            $this->donors_table .
            ".`partner_firstname`, " .
            $this->donors_table .
            ".`partner_lastname`) LIKE ?
            OR " .
            $this->donations_table .
            ".`id` = ?
            OR " .
            $this->donors_table .
            ".`id` = ?
            OR " .
            $this->donors_table .
            ".email LIKE ?

            ORDER BY
            " .
            $this->donations_table .
            ".`timestamp` DESC LIMIT 1000";

        // prepare query statement
        $stmt = $this->conn->prepare($donations_select_query);

        // sanitize
        $keywords = urldecode($keywords);

        $name = "%{$keywords}%";
        $partner_name = "%{$keywords}%";
        $id = "{$keywords}";
        $id2 = "{$keywords}";
        $email = "{$keywords}";

        // bind
        $stmt->bindParam(1, $name);
        $stmt->bindParam(2, $partner_name);
        $stmt->bindParam(3, $id, PDO::PARAM_INT);
        $stmt->bindParam(4, $id2, PDO::PARAM_INT);
        $stmt->bindParam(5, $email);
        // execute query
        $stmt->execute();

        return $stmt;
    }

    public function getPremiumIDsByTransactionID($transaction_id) {
        try {
            // Query to fetch the premiums_cart from the donations table
            $query =
                "
            SELECT `premiums_cart`
            FROM " .
                $this->donations_table .
                "
            WHERE `transaction_id` = :transaction_id
            LIMIT 1
        ";

            // Prepare and execute the query
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(
                ":transaction_id",
                $transaction_id,
                PDO::PARAM_STR,
            );
            $stmt->execute();

            // Fetch the result
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!$result || empty($result["premiums_cart"])) {
                return []; // No premiums_cart found
            }

            // Decode the premiums_cart JSON
            $premiums_cart = json_decode($result["premiums_cart"], true);
            if (!is_array($premiums_cart)) {
                throw new Exception("Invalid premiums_cart format.");
            }

            // Flatten the array into a list of IDs
            $premium_ids = array_column($premiums_cart, "id");

            return $premium_ids;
        } catch (PDOException $e) {
            error_log(
                "[getPremiumIDsByTransactionID] PDOException: " .
                    $e->getMessage(),
            );
            return [];
        } catch (Exception $e) {
            error_log(
                "[getPremiumIDsByTransactionID] Exception: " . $e->getMessage(),
            );
            return [];
        }
    }

    // read Donations
    public function readDonations() {
        try {
            // This function reads the latest 100 records.
            // select all query
            $query =
                "SELECT DISTINCT
            " .
                $this->donations_table .
                ".`id`,
            " .
                $this->donations_table .
                ".`account_id`,
            " .
                $this->donations_table .
                ".`donor_id`,
            " .
                $this->donations_table .
                ".`timestamp`,
            " .
                $this->donors_table .
                ".`firstname`,
            " .
                $this->donors_table .
                ".`lastname`,
            " .
                $this->donors_table .
                ".`address1`,
            " .
                $this->donors_table .
                ".`address2`,
            " .
                $this->donors_table .
                ".`city`,
            " .
                $this->donors_table .
                ".`state`,
            " .
                $this->donors_table .
                ".`country`,
            " .
                $this->donors_table .
                ".`postal_code`,
            " .
                $this->shipment_addresses_table .
                ".`firstname` as shipping_firstname,
            " .
                $this->shipment_addresses_table .
                ".`lastname` as shipping_lastname,
            " .
                $this->shipment_addresses_table .
                ".`address1` as shipping_address1,
            " .
                $this->shipment_addresses_table .
                ".`address2` as shipping_address2,
            " .
                $this->shipment_addresses_table .
                ".`city` as shipping_city,
            " .
                $this->shipment_addresses_table .
                ".`state` as shipping_state,
            " .
                $this->shipment_addresses_table .
                ".`country` as shipping_country,
            " .
                $this->shipment_addresses_table .
                ".`postal_code` shipping_postal_code,
            " .
                $this->donors_table .
                ".`phone`,
            " .
                $this->donors_table .
                ".`email`,
            " .
                $this->donations_table .
                ".`type`,
            " .
                $this->donations_table .
                ".`amount` as `donation_amount`,
            " .
                $this->donations_table .
                ".`installment`,
            " .
                $this->donations_table .
                ".`comments`,
            " .
                $this->donations_table .
                ".`add_me`,
            " .
                $this->donations_table .
                ".`read_onair`,
            " .
                $this->donations_table .
                ".`transaction_id`,
            " .
                $this->donations_table .
                ".`ipaddress`,
            " .
                $this->donations_table .
                ".`browser`,
            " .
                $this->donations_table .
                ".`show_name`,
            " .
                $this->donations_table .
                ".`source`,
            " .
                $this->donations_table .
                ".`campaign_id`,
            " .
                $this->campaigns_table .
                ".`name` AS `campaign`,
            " .
                $this->donations_table .
                ".`updated`,
            " .
                $this->donations_table .
                ".`donation_match`
            FROM
            " .
                $this->donations_table .
                "
            LEFT JOIN " .
                $this->donors_table .
                " ON " .
                $this->donations_table .
                ".`donor_id` = " .
                $this->donors_table .
                ".`id`
            LEFT JOIN " .
                $this->campaigns_table .
                " ON " .
                $this->donations_table .
                ".`campaign_id` = " .
                $this->campaigns_table .
                ".`id`
            LEFT JOIN " .
                $this->shipment_addresses_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->shipment_addresses_table .
                ".`donation_id`
            ORDER BY
                timestamp DESC LIMIT 100";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
        } catch (PDOException $e) {
            // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            // PHP Error message
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // READ donor subscriptions
    public function readSubscriptionsByTransaction_id($transaction_id) {
        try {
            // query to read single record
            $query =
                "SELECT
         " .
                $this->subscriptions_table .
                ".`id` as `subscription_id`,
         " .
                $this->subscriptions_table .
                ".`processor`,
         " .
                $this->subscriptions_table .
                ".`plan_id`,
         " .
                $this->subscriptions_table .
                ".`amount` as subscription_amount,
         " .
                $this->subscriptions_table .
                ".`interval`,
         " .
                $this->subscriptions_table .
                ".`active`,
         " .
                $this->subscriptions_table .
                ".`date_created`,
         " .
                $this->subscriptions_table .
                ".`date_canceled`
        FROM
            " .
                $this->subscriptions_table .
                "
        WHERE
            `transaction_id` = ?
        ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be found
            $stmt->bindParam(1, $transaction_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
        } catch (PDOException $e) {
            // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            // PHP Error message
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // read Gifts by id
    public function readGifts($id) {
        // query to read gifts table for single transaction_id
        $premium_select_query =
            "SELECT
            " .
            $this->premiums_table .
            ".`id` as `premium_id`,
            " .
            $this->premiums_table .
            ".`name`,
            " .
            $this->premiums_table .
            ".`price`,
            " .
            $this->premiums_table .
            ".`cog`,
            " .
            $this->premiums_table .
            ".`fmv`,
            " .
            $this->shipments_table .
            ".`quantity` as `qty`,
            " .
            $this->premiums_table .
            ".`download_url`,
            " .
            $this->premiums_table .
            ".`img_url`,
            " .
            $this->premium_categories_table .
            ".`name` as `category`,
            " .
            $this->shipments_table .
            ".`status` as shipment_status
            FROM
                " .
            $this->shipments_table .
            "
            LEFT JOIN " .
            $this->premiums_table .
            " ON `" .
            $this->shipments_table .
            "`.`premium_id` = " .
            $this->premiums_table .
            ".`id`
            LEFT JOIN " .
            $this->premium_categories_table .
            " ON " .
            $this->premiums_table .
            ".`category_id` = " .
            $this->premium_categories_table .
            ".`id`
            WHERE
                `" .
            $this->shipments_table .
            "`.`donation_id` = ?";

        // prepare query statement
        $premium_select_stmt = $this->conn->prepare($premium_select_query);

        // bind id of premium to be found
        $premium_select_stmt->bindParam(1, $id);

        // execute query
        $premium_select_stmt->execute();

        return $premium_select_stmt;
    }
    // read Payments by donation_id
    public function readPayments($donation_id) {
        // query to read payments table for single donation_id
        $query =
            "
        SELECT
            " .
            $this->payments_table .
            ".`id` as `payments_id`,
            " .
            $this->payments_table .
            ".`customer_id`,
            " .
            $this->payments_table .
            ".`payment_id`,
            " .
            $this->payments_table .
            ".`method`,
            " .
            $this->payments_table .
            ".`processor`,
            " .
            $this->payments_table .
            ".`card_type`,
            " .
            $this->payments_table .
            ".payment_id,
            " .
            $this->payments_table .
            ".amount,
            " .
            $this->payments_table .
            ".amount_refunded,
            " .
            $this->payments_table .
            ".status,
            " .
            $this->payments_table .
            ".processor,
            " .
            $this->payments_table .
            ".brand,
            LPAD(" .
            $this->payments_table .
            ".`last4`, 4, '0') AS `last4`,
            LPAD(" .
            $this->payments_table .
            ".`exp_month`, 2, '0') AS `exp_month`,
            " .
            $this->payments_table .
            ".exp_year,
            " .
            $this->payments_table .
            ".`date_created`,
            " .
            $this->payments_table .
            ".`date_updated`,
            " .
            $this->payments_table .
            ".`date_deposited`
        FROM
            " .
            $this->payments_table .
            "

        WHERE
            `" .
            $this->payments_table .
            "`.`donation_id` = ?";

        // prepare query statement
        $stmt = $this->conn->prepare($query);

        // bind id of premium to be found
        $stmt->bindParam(1, $donation_id);

        // execute query
        $stmt->execute();

        return $stmt;
    }
    // read Shipments and status
    public function readShipments($donation_id) {
        // query to read payments table for single donation_id
        $shipments_select_query =
            "
            SELECT
                " .
            $this->shipments_table .
            ".`id` as `shipment_id`,
                " .
            $this->shipments_table .
            ".`premium_id`,
                " .
            $this->shipments_table .
            ".`status` as `shipment_status`
            FROM
                " .
            $this->shipments_table .
            "

            WHERE
                `" .
            $this->shipments_table .
            "`.`donation_id` = ?";

        // prepare query statement
        $shipments_select_stmt = $this->conn->prepare($shipments_select_query);

        // bind id of donation to be found
        $shipments_select_stmt->bindParam(1, $donation_id);

        // execute query
        $shipments_select_stmt->execute();

        return $shipments_select_stmt;
    }
    // read Donation by Date
    public function readDate($startdate, $enddate, $access_level) {
        try {
            // This function reads records between dates.
            // select all query
            $query =
                "SELECT
            " .
                $this->donations_table .
                ".`id`,
            " .
                $this->donations_table .
                ".`account_id`,
            " .
                $this->donations_table .
                ".`donor_id`,
            " .
                $this->donations_table .
                ".`timestamp`,
            " .
                $this->donors_table .
                ".`firstname`,
            " .
                $this->donors_table .
                ".`lastname`,
            " .
                $this->donors_table .
                ".`address1`,
            " .
                $this->donors_table .
                ".`address2`,
            " .
                $this->donors_table .
                ".`city`,
            " .
                $this->donors_table .
                ".`state`,
            " .
                $this->donors_table .
                ".`country`,
            " .
                $this->donors_table .
                ".`postal_code`,
            " .
                $this->donors_table .
                ".`phone`,
            " .
                $this->donors_table .
                ".`email`,
            " .
                $this->shipment_addresses_table .
                ".`firstname` as shipping_firstname,
            " .
                $this->shipment_addresses_table .
                ".`lastname` as shipping_lastname,
            " .
                $this->shipment_addresses_table .
                ".`address1` as shipping_address1,
            " .
                $this->shipment_addresses_table .
                ".`address2` as shipping_address2,
            " .
                $this->shipment_addresses_table .
                ".`city` as shipping_city,
            " .
                $this->shipment_addresses_table .
                ".`state` as shipping_state,
            " .
                $this->shipment_addresses_table .
                ".`country` as shipping_country,
            " .
                $this->shipment_addresses_table .
                ".`postal_code` shipping_postal_code,
            " .
                $this->donations_table .
                ".`type`,
            " .
                $this->donations_table .
                ".`amount` as `donation_amount`,
            " .
                $this->donations_table .
                ".`installment`,
            " .
                $this->donations_table .
                ".`comments`,
            " .
                $this->donations_table .
                ".`add_me`,
            " .
                $this->donations_table .
                ".`read_onair`,
            " .
                $this->donations_table .
                ".`transaction_id`,
            " .
                $this->donations_table .
                ".`ipaddress`,
            " .
                $this->donations_table .
                ".`browser`,
            " .
                $this->donations_table .
                ".`show_name`,
            " .
                $this->donations_table .
                ".`source`,
            " .
                $this->donations_table .
                ".`campaign_id`,
            " .
                $this->campaigns_table .
                ".`name` AS `campaign`,
            " .
                $this->donations_table .
                ".`updated`,
            " .
                $this->donations_table .
                ".`donation_match`
            FROM
            " .
                $this->donations_table .
                "
            LEFT JOIN " .
                $this->donors_table .
                " ON " .
                $this->donations_table .
                ".`donor_id` = " .
                $this->donors_table .
                ".`id`
            LEFT JOIN " .
                $this->campaigns_table .
                " ON " .
                $this->donations_table .
                ".`campaign_id` = " .
                $this->campaigns_table .
                ".`id`
            LEFT JOIN " .
                $this->shipment_addresses_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->shipment_addresses_table .
                ".`donation_id`

        WHERE
            CAST(" .
                $this->donations_table .
                ".`timestamp` AS DATE) BETWEEN ? AND ?";

            // Add subquery to exclude donations with no associated campaign or non-marathon campaign
            if ($access_level != "Admin") {
                $query .=
                    " AND (
                " .
                    $this->donations_table .
                    ".`campaign_id` IS NOT NULL
                AND " .
                    $this->donations_table .
                    ".`campaign_id` IN (
                    SELECT `id` FROM " .
                    $this->campaigns_table .
                    " WHERE `type` = 'marathon'
                )
            )";
            }

            $query .= " ORDER BY id DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $startdate);
            $stmt->bindParam(2, $enddate);
            //$stmt->bindParam(3, $status);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
        } catch (PDOException $e) {
            // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            // PHP Error message
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // read Donation by Date and campaign_id
    public function readDateandCampaign($startdate, $enddate, $campaign_id) {
        try {
            // This function reads records between dates.
            // select all query
            $query =
                "SELECT
                " .
                $this->donations_table .
                ".`id`,
                " .
                $this->donations_table .
                ".`account_id`,
                " .
                $this->donations_table .
                ".`donor_id`,
                " .
                $this->donations_table .
                ".`timestamp`,
                " .
                $this->donors_table .
                ".`firstname`,
                " .
                $this->donors_table .
                ".`lastname`,
                " .
                $this->donors_table .
                ".`address1`,
                " .
                $this->donors_table .
                ".`address2`,
                " .
                $this->donors_table .
                ".`city`,
                " .
                $this->donors_table .
                ".`state`,
                " .
                $this->donors_table .
                ".`country`,
                " .
                $this->donors_table .
                ".`postal_code`,
                " .
                $this->donors_table .
                ".`phone`,
                " .
                $this->donors_table .
                ".`email`,
                " .
                $this->donations_table .
                ".`type`,
                " .
                $this->donations_table .
                ".`amount` as `donation_amount`,
                " .
                $this->donations_table .
                ".`installment`,
                " .
                $this->donations_table .
                ".`comments`,
                " .
                $this->donations_table .
                ".`add_me`,
                " .
                $this->donations_table .
                ".`read_onair`,
                " .
                $this->donations_table .
                ".`transaction_id`,
                " .
                $this->donations_table .
                ".`ipaddress`,
                " .
                $this->donations_table .
                ".`browser`,
                " .
                $this->donations_table .
                ".`show_name`,
                " .
                $this->donations_table .
                ".`source`,
                " .
                $this->donations_table .
                ".`campaign_id`,
                " .
                $this->campaigns_table .
                ".`name` AS `campaign`,
                " .
                $this->donations_table .
                ".`updated`,
                " .
                $this->donations_table .
                ".`donation_match`
                FROM
                " .
                $this->donations_table .
                "
                INNER JOIN " .
                $this->donors_table .
                " ON " .
                $this->donations_table .
                ".`donor_id` = " .
                $this->donors_table .
                ".`id`
                INNER JOIN " .
                $this->campaigns_table .
                " ON " .
                $this->donations_table .
                ".`campaign_id` = " .
                $this->campaigns_table .
                ".`id`
                WHERE   CAST(" .
                $this->donations_table .
                ".`timestamp` AS DATE) BETWEEN ? AND ?
                AND " .
                $this->donations_table .
                ".`campaign_id` = ?
                ORDER BY
                    id DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $startdate);
            $stmt->bindParam(2, $enddate);
            $stmt->bindParam(3, $campaign_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
        } catch (PDOException $e) {
            // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            // PHP Error message
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // read Donation by campaign_id
    public function readbyCampaign($campaign_id) {
        try {
            // This function reads records by a campaign_id.
            // select query
            $query =
                "SELECT
                    " .
                $this->donations_table .
                ".`id`,
                    " .
                $this->donations_table .
                ".`account_id`,
                    " .
                $this->donations_table .
                ".`donor_id`,
                    " .
                $this->donations_table .
                ".`timestamp`,
                    " .
                $this->donors_table .
                ".`firstname`,
                    " .
                $this->donors_table .
                ".`lastname`,
                    " .
                $this->donors_table .
                ".`address1`,
                    " .
                $this->donors_table .
                ".`address2`,
                    " .
                $this->donors_table .
                ".`city`,
                    " .
                $this->donors_table .
                ".`state`,
                    " .
                $this->donors_table .
                ".`country`,
                    " .
                $this->donors_table .
                ".`postal_code`,
                    " .
                $this->donors_table .
                ".`phone`,
                    " .
                $this->donors_table .
                ".`email`,
                    " .
                $this->donations_table .
                ".`type`,
                    " .
                $this->donations_table .
                ".`amount` as `donation_amount`,
                    " .
                $this->donations_table .
                ".`installment`,
                    " .
                $this->donations_table .
                ".`comments`,
                    " .
                $this->donations_table .
                ".`add_me`,
                    " .
                $this->donations_table .
                ".`read_onair`,
                    " .
                $this->donations_table .
                ".`transaction_id`,
                    " .
                $this->donations_table .
                ".`ipaddress`,
                    " .
                $this->donations_table .
                ".`browser`,
                    " .
                $this->donations_table .
                ".`show_name`,
                    " .
                $this->donations_table .
                ".`source`,

                    " .
                $this->donations_table .
                ".`campaign_id`,
                    " .
                $this->campaigns_table .
                ".`name` AS `campaign`,
                    " .
                $this->donations_table .
                ".`updated`,
                    " .
                $this->donations_table .
                ".`donation_match`
                    FROM
                    " .
                $this->donations_table .
                "
                    INNER JOIN " .
                $this->donors_table .
                " ON " .
                $this->donations_table .
                ".`donor_id` = " .
                $this->donors_table .
                ".`id`
                    INNER JOIN " .
                $this->campaigns_table .
                " ON " .
                $this->donations_table .
                ".`campaign_id` = " .
                $this->campaigns_table .
                ".`id`
                    AND " .
                $this->donations_table .
                ".`campaign_id` = ?
                    ORDER BY
                        id DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $campaign_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
        } catch (PDOException $e) {
            // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            // PHP Error message
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
}
