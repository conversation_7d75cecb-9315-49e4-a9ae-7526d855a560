<?php
//Import PHPMailer classes into the global namespace
//These must be at the top of your script, not inside a function

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

class Email {
    // database connection and table code
    private $conn;
    private $donations_table = "donations";
    private $donors_table = "donors";
    private $shipments_table = "shipments";
    private $premiums_table = "premiums";
    private $premium_categories_table = "premium_categories";
    private $payments_table = "payments";

    // object properties
    public $id;
    public $timestamp;
    public $firstname;
    public $lastname;
    public $email;
    public $paymenttype;
    public $pledge_amount;
    public $installment; //TODO: frequency
    public $transaction_id;
    public $source;
    public $status;
    public $premiums;
    public $img_url;

    private string $style = '<style>
			  /* -------------------------------------
				  GLOBAL RESETS
			  ------------------------------------- */
			  img {
				border: none;
				-ms-interpolation-mode: bicubic;
				max-width: 100%; }

			  body {
				background-color: #f6f6f6;
				font-family: sans-serif;
				-webkit-font-smoothing: antialiased;
				font-size: 14px;
				line-height: 1.4;
				margin: 0;
				padding: 0;
				-ms-text-size-adjust: 100%;
				-webkit-text-size-adjust: 100%; }

			  table {
				border-collapse: separate;
				mso-table-lspace: 0pt;
				mso-table-rspace: 0pt;
				width: 100%; }
				table td {
				  font-family: sans-serif;
				  font-size: 14px;
				  vertical-align: top; }

			  /* -------------------------------------
				  BODY & CONTAINER
			  ------------------------------------- */

			  .body {
				background-color: #f6f6f6;
				width: 100%; }

			  /* Set a max-width, and make it display as block so it will automatically stretch to that width, but will also shrink down on a phone or something */
			  .container {
				display: block;
				margin: 0 auto !important;
				/* makes it centered */
				max-width: 580px;
				padding: 10px;
				width: 580px; }

			  /* This should also be a block element, so that it will fill 100% of the .container */
			  .content {
				box-sizing: border-box;
				display: block;
				margin: 0 auto;
				max-width: 580px;
				padding: 10px; }

			  /* -------------------------------------
				  HEADER, FOOTER, MAIN
			  ------------------------------------- */
			  .main {
				background: #ffffff;
				border-radius: 3px;
				width: 100%; }

			  .wrapper {
				box-sizing: border-box;
				padding: 20px; }

			  .content-block {
				padding-bottom: 10px;
				padding-top: 10px;
			  }

			  .footer {
				clear: both;
				margin-top: 10px;
				text-align: center;
				width: 100%; }
				.footer td,
				.footer p,
				.footer span,
				.footer a {
				  color: #999999;
				  font-size: 12px;
				  text-align: center; }

			  /* -------------------------------------
				  TYPOGRAPHY
			  ------------------------------------- */
			  h1,
			  h2,
			  h3,
			  h4 {
				color: #000000;
				font-family: sans-serif;
				font-weight: 400;
				line-height: 1.4;
				margin: 0;
				margin-bottom: 30px; }

			  h1 {
				font-size: 35px;
				font-weight: 300;
				text-align: center;
				text-transform: capitalize; }

			  p,
			  ul,
			  ol {
				font-family: sans-serif;
				font-size: 14px;
				font-weight: normal;
				margin: 0;
				margin-bottom: 15px; }
				p li,
				ul li,
				ol li {
				  list-style-position: inside;
				  margin-left: 5px; }

			  a {
				color: #3498db;
				text-decoration: underline; }

			  /* -------------------------------------
				  BUTTONS
			  ------------------------------------- */
			  .btn {
				box-sizing: border-box;
				width: 100%; }
				.btn > tbody > tr > td {
				  padding-bottom: 15px; }
				.btn table {
				  width: auto; }
				.btn table td {
				  background-color: #ffffff;
				  border-radius: 5px;
				  text-align: center; }
				.btn a {
				  background-color: #ffffff;
				  border: solid 1px #3498db;
				  border-radius: 5px;
				  box-sizing: border-box;
				  color: #3498db;
				  cursor: pointer;
				  display: inline-block;
				  font-size: 14px;
				  font-weight: bold;
				  margin: 0;
				  padding: 12px 25px;
				  text-decoration: none;
				  text-transform: capitalize; }

			  .btn-primary table td {
				background-color: #3498db; }

			  .btn-primary a {
				background-color: #3498db;
				border-color: #3498db;
				color: #ffffff; }

			  /* -------------------------------------
				  OTHER STYLES THAT MIGHT BE USEFUL
			  ------------------------------------- */
			  .last {
				margin-bottom: 0; }

			  .first {
				margin-top: 0; }

			  .align-center {
				text-align: center; }

			  .align-right {
				text-align: right; }

			  .align-left {
				text-align: left; }

			  .clear {
				clear: both; }

			  .mt0 {
				margin-top: 0; }

			  .mb0 {
				margin-bottom: 0; }

			  .preheader {
				color: transparent;
				display: none;
				height: 0;
				max-height: 0;
				max-width: 0;
				opacity: 0;
				overflow: hidden;
				mso-hide: all;
				visibility: hidden;
				width: 0; }

			  .powered-by a {
				text-decoration: none; }

			  hr {
				border: 0;
				border-bottom: 1px solid #f6f6f6;
				Margin: 20px 0; }

			  /* -------------------------------------
				  RESPONSIVE AND MOBILE FRIENDLY STYLES
			  ------------------------------------- */
			  @media only screen and (max-width: 620px) {
				table[class=body] h1 {
				  font-size: 28px !important;
				  margin-bottom: 10px !important; }
				table[class=body] p,
				table[class=body] ul,
				table[class=body] ol,
				table[class=body] td,
				table[class=body] span,
				table[class=body] a {
				  font-size: 16px !important; }
				table[class=body] .wrapper,
				table[class=body] .article {
				  padding: 10px !important; }
				table[class=body] .content {
				  padding: 0 !important; }
				table[class=body] .container {
				  padding: 0 !important;
				  width: 100% !important; }
				table[class=body] .main {
				  border-left-width: 0 !important;
				  border-radius: 0 !important;
				  border-right-width: 0 !important; }
				table[class=body] .btn table {
				  width: 100% !important; }
				table[class=body] .btn a {
				  width: 100% !important; }
				table[class=body] .img-responsive {
				  height: auto !important;
				  max-width: 100% !important;
				  width: auto !important; }}

			  /* -------------------------------------
				  PRESERVE THESE STYLES IN THE HEAD
			  ------------------------------------- */
			  @media all {
				.ExternalClass {
				  width: 100%; }
				.ExternalClass,
				.ExternalClass p,
				.ExternalClass span,
				.ExternalClass font,
				.ExternalClass td,
				.ExternalClass div {
				  line-height: 100%; }
				.apple-link a {
				  color: inherit !important;
				  font-family: inherit !important;
				  font-size: inherit !important;
				  font-weight: inherit !important;
				  line-height: inherit !important;
				  text-decoration: none !important; }
				.btn-primary table td:hover {
				  background-color: #34495e !important; }
				.btn-primary a:hover {
				  background-color: #34495e !important;
				  border-color: #34495e !important; } }

			</style>';

    // constructor with $db as database connection
    public function __construct($db) {
        $this->conn = $db;
    }

    public function generateSecureToken($length = 32) {
        // Check if OpenSSL is available
        if (function_exists("openssl_random_pseudo_bytes")) {
            // Generate secure random bytes
            $bytes = openssl_random_pseudo_bytes($length / 2);
            // Convert bytes to hexadecimal format to get the token
            $token = bin2hex($bytes);
        } else {
            // Fallback to a less secure method if OpenSSL is not available
            $token = "";
            for ($i = 0; $i < $length; $i++) {
                $token .= dechex(mt_rand(0, 15));
            }
        }
        return $token;
    }

    public function storeTokenWithDonor(int $donorId): ?string {
        if (empty($donorId)) {
            // If the donor ID is empty or zero, there's nothing to update.
            return null;
        }

        try {
            // Generate a secure token. Ensure the generateSecureToken function exists and returns a string token.
            $token = $this->generateSecureToken();

            // SQL statement to update the paperless_token column in the row where the id matches $donorId.
            $query =
                "UPDATE " .
                $this->donors_table .
                " SET paperless_token = :token WHERE id = :donorId";

            // Prepare the SQL statement for execution.
            $stmt = $this->conn->prepare($query);

            // Bind the generated token and the donor ID to the prepared statement.
            $stmt->bindParam(":token", $token, PDO::PARAM_STR);
            $stmt->bindParam(":donorId", $donorId, PDO::PARAM_INT);

            // Execute the statement.
            $stmt->execute();

            // If at least one row was updated, the token was successfully stored.
            if ($stmt->rowCount() > 0) {
                return $token; // Return the generated token.
            } else {
                // If no rows were updated, it might mean the donor ID doesn't exist.
                return null; // Indicate failure by returning null.
            }
        } catch (PDOException $e) {
            // Handle any database-related errors.
            error_log(
                "PDOException in storeTokenWithDonor: " . $e->getMessage(),
            );
            return null;
        } catch (Exception $e) {
            // Handle any other errors.
            error_log("Exception in storeTokenWithDonor: " . $e->getMessage());
            return null;
        }
    }

    // Send Email
    public function send($email, $name, $html, $subject) {
        try {
            global $donor_dir_email;
            global $TESTMODE;

            $mail = new PHPMailer(true); // Passing `true` enables exceptions

            /* disable Mailpit config until configured in Ansible
            //configuration for locally running Mailpit server for testing
            if ($TESTMODE === 1) {
                $mail->isSMTP();
                $mail->Host = "localhost";
                $mail->SMTPAuth = false;
                $mail->Port = 1025;
            }
            //end mailpit config
            */

            // Configure to use local Postfix server
            $mail->isSMTP();
            $mail->Host = "localhost";
            $mail->SMTPAuth = false;
            $mail->Port = 25;
            //Recipients
            $mail->setFrom($donor_dir_email, "KPFA"); // From
            $mail->addAddress("$email", mb_encode_mimeheader($name)); // recipient
            //$mail->addBCC('<EMAIL>');

            //Content
            $mail->isHTML(true); // Set email format to HTML
            $mail->addCustomHeader("MIME-Version: 1.0");
            $mail->CharSet = "UTF-8";
            $mail->Subject = $subject;
            $mail->Body = $html;
            //$mail->AltBody  =  $textMessage; //ADDME XXX FIXME
            $mail->send(); // send the messages

            // ** [RETURN] Section  ** //
            return true;
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }

    // Thank you HTML
    public function sendThankYou(
        $id,
        $timestamp,
        $name,
        $premiums,
        $pledge_amount,
        $installment,
        $transaction_id,
    ) {
        try {
            global $api_url;
            // Greeting
            $html =
                '
		<!doctype html>
		<html lang="en">
		  <head>
			<meta name="viewport" content="width=device-width" />
			<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <title>KPFA Donation Thank-You</title>' .
                $this->style .
                '</head>
		  <body class="">
			<table border="0" cellpadding="0" cellspacing="0" class="body">
			  <tr>
				<td>&nbsp;</td>
				<td class="container">
				  <div class="content">

					<!-- START CENTERED WHITE CONTAINER -->
					<span class="preheader">KPFA Donation Thank-You</span>
					<table class="main">

					  <!-- START MAIN CONTENT AREA -->
					  <tr>
						<td class="wrapper">
						  <table border="0" cellpadding="0" cellspacing="0">
							<tr>
							  <td>';
            $html .= "<a class=\"navbar-brand\" href=\"https://kpfa.org\"><img src=\"https://$api_url/assets/images/KPFA_logo_wide_tagline.png\" alt=\"KPFA\" width=\"100%\"></a>";
            $html .= "<p>Dear $name,</p>";

            // -----------------
            // WITH THANK YOU GIFT
            if (!empty($premiums)) {
                $html .= "<p>Thank you for supporting KPFA Radio! Because of your generous donation, KPFA 
				can continue to be a beacon for independent journalism and promote cultural diversity and 
				pluralistic community expression. Since 1949 KPFA has been community-powered 
				and solely supported by listener donations and like-minded foundations - now you are a 
				part of that community. Thank you and please continue to give your support.</p>";

                $html .= "<p>We very much appreciate your ";
                if ($installment == "Monthly") {
                    $html .= "recurring monthly ";
                }
                $html .= "donation of $$pledge_amount. ";

                $html .= "As a thank-you, you will receive ";
                // Loop for Premiums
                $i = 0;
                $dd = 0;
                $num = count($premiums);
                foreach ($premiums as $i => $element) {
                    $html .= " " . $element["name"];
                    if (!empty($element["download_url"])) {
                        ++$dd;
                    } //prems with download_url
                }
                $html .=
                    ". During fund drives, please allow 6-8 weeks after the end of the drive to receive your premium. For pledges made between drives, please allow 2-4 weeks.</p>";

                // Digital Downloads?

                if ($dd == 1) {
                    $html .=
                        "<p>Your Digital Download is immediately available by clicking on the following link.</p><ul>";
                }

                if ($dd > 1) {
                    $html .=
                        "<p>Your Digital Downloads are immediately available by clicking on the following links.</p><ul>";
                }

                if ($dd > 0) {
                    // Loop for Premiums
                    $i = 0;
                    $num = count($premiums);
                    foreach ($premiums as $i => $element) {
                        if (!empty($element["download_url"])) {
                            $html .=
                                "<li><a href=\"" .
                                $element["download_url"] .
                                "\">" .
                                $element["name"] .
                                "</a></li>\n";
                        }
                    }
                }
                $html .= "</ul>";

                $html .=
                    "<p>Again, thank you so much and we will continue fulfilling our mission.</p>";
            }

            // WITHOUT THANK YOU GIFT
            else {
                $html .=
                    "<p>Thank you for supporting KPFA Radio! Because of your generous donation KPFA can continue to be a beacon for independent journalism and promote cultural diversity and pluralistic community expression. Since 1949 KPFA has been a community powered and solely supported by listener donations and like-minded foundations - now you are a part of that community. Thank you and please continue to give your support.</p>";

                $html .= "<p>We very much appreciate your ";
                if ($installment == "Monthly") {
                    $html .= "recurring monthly ";
                }
                $html .= "donation of $$pledge_amount. ";
            }
            if (
                $campaign = (new Campaign($this->conn))
                    ->readbydate($timestamp)
                    ->fetch(PDO::FETCH_ASSOC)
            ) {
                if (!empty($campaign["gift_title"])) {
                    $html .=
                        "<h2 style=\"margin: 30px; padding: 10px; border: 6px solid #afafaf;\">We hope you enjoy this complimentary <a href=\"" .
                        $campaign["gift_link"] .
                        "\" target=\"_blank\">" .
                        $campaign["gift_title"] .
                        "</a>.</h2>";
                }
            }
            // Bottom
            $html .= '<table border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" align="center">
	   <tbody>
		 <tr>
		   <td align="center">
			 <table border="0" cellpadding="0" cellspacing="0">
			   <tbody>
				 <tr>
				 <td align="center">';
            $html .= "<a href=\"https://$api_url/receipt/$transaction_id\" target=\"_blank\">VIEW RECEIPT</a></td>";
            $html .= '</tr>
				 </tbody>
			 </table>
		   </td>
		 </tr>
	   </tbody>
	 </table>';

            // Email footer
            $html .= '<p>Vigilant as always,<br />
			Antonio Ortiz<br />
			Interim General Manager</p>
			</td>
			</tr>
			</table>
			</td>
			</tr>

			<!-- END MAIN CONTENT AREA -->
			</table>

			<!-- START FOOTER -->
			<div class="footer">
			  <table border="0" cellpadding="0" cellspacing="0">
				<tr>
				  <td class="content-block">
					<span class="apple-link">KPFA - 1929 Martin Luther King Jr Way. Berkeley, CA 94704</span>
					<br>KPFA Radio is an affiliate of Pacifica Radio Foundation who is a registered 501(c)3 organization.
					<br>EIN #94-1347046
					<br>This is a one-time transactional email because of your donation to KPFA.
				  </td>
				</tr>
			  </table>
			</div>
			<!-- END FOOTER -->

			<!-- END CENTERED WHITE CONTAINER -->
			</div>
			</td>
			<td>&nbsp;</td>
			</tr>
			</table>
			</body>
			</html>';

            // ** [RETURN] Section  ** //
            return $html;
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }

    // Bill HTML
    public function sendBill(
        $id,
        $timestamp,
        $name,
        $premiums,
        $pledge_amount,
        $installment,
        $transaction_id,
    ) {
        try {
            global $api_url;
            // email body
            $html =
                '
		<!doctype html>
		<html lang="en">
		  <head>
			<meta name="viewport" content="width=device-width" />
			<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <title>KPFA Donation Thank-You</title>
' .
                $this->style .
                '
		  </head>
		  <body class="">
			<table border="0" cellpadding="0" cellspacing="0" class="body">
			  <tr>
				<td>&nbsp;</td>
				<td class="container">
				  <div class="content">

					<!-- START CENTERED WHITE CONTAINER -->
					<span class="preheader">KPFA Donation Thank-You</span>
					<table class="main">

					  <!-- START MAIN CONTENT AREA -->
					  <tr>
						<td class="wrapper">
						  <table border="0" cellpadding="0" cellspacing="0">
							<tr>
							  <td>';
            $html .= "<a class=\"navbar-brand\" href=\"https://kpfa.org\"><img src=\"https://$api_url/assets/images/KPFA_logo_wide_tagline.png\" alt=\"KPFA\" width=\"100%\"></a>";
            $html .= "<p>Dear $name,</p>";

            // Middle

            // WITH THANK YOU GIFT
            if (!empty($premiums)) {
                $html .= "<p>Thank you for supporting KPFA Radio! Because of your generous donation, 
				KPFA can continue to be a beacon for independent journalism and promote cultural diversity 
				and pluralistic community expression. Since 1949 KPFA has been community-powered and solely 
				supported by listener donations and like-minded foundations - now you are a part of that 
				community. Thank you and please continue to give your support.</p>";

                $html .= "<p>We very much appreciate your ";
                if ($installment == "Monthly") {
                    $html .= "recurring monthly ";
                }
                $html .= "donation of $$pledge_amount. ";

                //$gifts = implode (", ", $premiums['name']);

                $html .= "As a thank-you, you will receive ";

                // Loop for Premiums
                $i = 0;
                $num = count($premiums);
                foreach ($premiums as $i => $element) {
                    $html .= " " . $element["name"];
                }

                $html .=
                    ". During fund drives, please allow 6-8 weeks after the end of the drive to receive your premium. For pledges made between drives, please allow 2-4 weeks.</p>";

                $html .=
                    "<p>Thank you so much and we will continue fulfilling our mission.</p>";
            }

            // WITHOUT THANK YOU GIFT
            else {
                $html .=
                    "<p>Thank you for supporting KPFA Radio! Because of your generous pledge KPFA can continue to be a beacon for independent journalism and promote cultural diversity and pluralistic community expression. Since 1949 KPFA has been a community powered and solely supported by listener donations and like-minded foundations - now you are a part of that community. Thank you and please continue to give your support.</p>";

                $html .= "<p>We very much appreciate your ";
                if ($installment == "Monthly") {
                    $html .= "recurring monthly ";
                }
                $html .= "donation of $$pledge_amount. ";

                $html .=
                    "<p>Again, thank you so much and we will continue fulfilling our mission.</p>";
            }
            if (
                $campaign = (new Campaign($this->conn))
                    ->readbydate($timestamp)
                    ->fetch(PDO::FETCH_ASSOC)
            ) {
                if (!empty($campaign["gift_title"])) {
                    $html .=
                        "<h2 style=\"margin: 30px; padding: 10px; border: 6px solid #afafaf;\">We hope you enjoy this complimentary <a href=\"" .
                        $campaign["gift_link"] .
                        "\" target=\"_blank\">" .
                        $campaign["gift_title"] .
                        "</a>.</h2>";
                }
            }
            $html .= '<table border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" align="center">
								  <tbody>
									<tr>
									  <td align="center">
										<table border="0" cellpadding="0" cellspacing="0">
										  <tbody>
											<tr>
											<td align="center">';
            $html .= "<a href=\"https://$api_url/receipt/$transaction_id\" target=\"_blank\">VIEW AND PAY PLEDGE</a></td>";
            $html .= '</tr>
											</tbody>
										</table>
									  </td>
									</tr>
								  </tbody>
								</table>';

            // Email footer
            $html .= '<p>Vigilant as always,<br />
				Antonio Ortiz<br />
				Interim General Manager</p>
			</td>
			</tr>
		</table>
		</td>
		</tr>

		<!-- END MAIN CONTENT AREA -->
		</table>

		<!-- START FOOTER -->
		<div class="footer">
		  <table border="0" cellpadding="0" cellspacing="0">
			<tr>
			  <td class="content-block">
				<span class="apple-link">KPFA - 1929 Martin Luther King Jr Way. Berkeley, CA 94704</span>
				<br>KPFA Radio is an affiliate of Pacifica Radio Foundation who is a registered 501(c)3 organization.
				<br>EIN #94-1347046
				<br>This is a one-time transactional email because of your donation to KPFA.
			  </td>
			</tr>
		  </table>
		</div>
		<!-- END FOOTER -->

		<!-- END CENTERED WHITE CONTAINER -->
		</div>
		</td>
		<td>&nbsp;</td>
		</tr>
		</table>
		</body>
		</html>';

            // ** [RETURN] Section  ** //
            return $html;
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }

    // Thank you HTML
    public function sendReminder(
        $id,
        $timestamp,
        $name,
        $premiums,
        $pledge_amount,
        $installment,
        $transaction_id,
    ) {
        try {
            global $api_url;
            // email body
            $html =
                '
		<!doctype html>
		<html lang="en">
		  <head>
			<meta name="viewport" content="width=device-width" />
			<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<title>KPFA Pledge reminder</title>' .
                $this->style .
                '
		  </head>
		  <body class="">
			<table border="0" cellpadding="0" cellspacing="0" class="body">
			  <tr>
				<td>&nbsp;</td>
				<td class="container">
				  <div class="content">

					<!-- START CENTERED WHITE CONTAINER -->
					<span class="preheader">KPFA Pledge Reminder</span>
					<table class="main">

					  <!-- START MAIN CONTENT AREA -->
					  <tr>
						<td class="wrapper">
						  <table border="0" cellpadding="0" cellspacing="0">
							<tr>
							  <td>';
            $html .= "<a class=\"navbar-brand\" href=\"https://kpfa.org\"><img src=\"https://$api_url/assets/images/KPFA_logo_wide_tagline.png\" alt=\"KPFA\" width=\"100%\"></a>";
            $html .=
                "<p>Dear $name,</p>
					<p>Thank you so much again for pledging to KPFA! To honor your recent pledge of $$pledge_amount made on " .
                date("F jS, Y", strtotime($timestamp)) .
                " <strong>please click the link below</strong>. You'll find instructions on how to pay by credit card or by mailing us a check.</p>

					<p>Your donation is essential for us to continue our mission as an independent media outlet, a platform for artists, musicians, and radicals to elevate voices that influence and challenge norms and narratives.</p>";

            $html .= '<h3 align="center">Helpful information:</h3>
				<ul style="list-style: none; padding-left: 0; padding-bottom: 5px;">
				<li><strong>THANK YOU GIFT:</strong> Gifts are not shipped until payment is received. Please allow up to four weeks for your gift(s) to arrive after payment is made. For any questions about your gift(s), please email <a href="mailto:<EMAIL>"><EMAIL></a>.</li>
				<li><strong>PAYMENTS:</strong> If you have questions or need to update your payment information, please contact our Membership Department at <a href="mailto:<EMAIL>"><EMAIL></a>.</li>
				<li><strong>TAX INFO:</strong> With all non-profit organizations, your contribution is tax-deductible to the fullest extent allowed by law. The law allows you to deduct the portion of your contribution not paying for goods and services, such as your premium gift. If you didn\'t request a gift, your entire donation is tax-deductible.</li>
				</ul>
								<table border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" align="center">
								  <tbody>
									<tr>
									  <td align="center">
										<table border="0" cellpadding="0" cellspacing="0">
										  <tbody>
											<tr>
											<td align="center">';
            $html .= "<a href=\"https://$api_url/receipt/$transaction_id\" target=\"_blank\">VIEW AND PAY PLEDGE</a></td>";
            $html .= '</tr>
											</tbody>
										</table>
									  </td>
									</tr>
								  </tbody>
								</table>
								<p>Today, when media outlets are either shutting their doors or selling out to corporate giants, KPFA remains a marvel and a necessity to voices and stories that may otherwise be ignored.</p>

								<p>Vigilant as always,<br />
								<br />
								Vicky Palacios<br />
								Membership Coordinator</p>
							  </td>
							</tr>
						  </table>
						</td>
					  </tr>

					<!-- END MAIN CONTENT AREA -->
					</table>

					<!-- START FOOTER -->
					<div class="footer">
					  <table border="0" cellpadding="0" cellspacing="0">
						<tr>
						  <td class="content-block">
							<span class="apple-link">KPFA - 1929 Martin Luther King Jr Way. Berkeley, CA 94704</span>
							<br>KPFA Radio is an affiliate of Pacifica Radio Foundation who is a registered 501(c)3 organization.
							<br>EIN #94-1347046
							<br>This is a one-time transactional email because of your donation to KPFA.
						  </td>
						</tr>
					  </table>
					</div>
					<!-- END FOOTER -->

				  <!-- END CENTERED WHITE CONTAINER -->
				  </div>
				</td>
				<td>&nbsp;</td>
			  </tr>
			</table>
		  </body>
		</html>
		';

            // ** [RETURN] Section  ** //
            return $html;
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // Thank you HTML
    public function sendShippingUpdate(
        $id,
        $timestamp,
        $name,
        $premiums,
        $pledge_amount,
        $installment,
        $transaction_id,
        $shipping_status,
    ) {
        try {
            global $api_url;
            // email body
            $html =
                '
				<!doctype html>
				<html lang="en">
					<head>
					<meta name="viewport" content="width=device-width" />
					<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
					<title>KPFA Shipping Update!</title> ' .
                $this->style .
                '
					</head>
					<body class="">
					<table border="0" cellpadding="0" cellspacing="0" class="body">
						<tr>
						<td>&nbsp;</td>
						<td class="container">
							<div class="content">

							<!-- START CENTERED WHITE CONTAINER -->
							<span class="preheader">KPFA Shipping Update</span>
							<table class="main">

								<!-- START MAIN CONTENT AREA -->
								<tr>
								<td class="wrapper">
									<table border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td>';

            $html .= "<a class=\"navbar-brand\" href=\"https://kpfa.org\"><img src=\"https://$api_url/assets/images/KPFA_logo_wide_tagline.png\" alt=\"KPFA\" width=\"100%\"></a>
											<h2 align=\"center\">Your Package Has Shipped!</h2>";
            $html .=
                "<p>Dear $name,</p>
							<p>Your gift of " .
                $premiums[0]["name"] .
                " has shipped and is on its way. Delivery can take up to 7 days.</p>
							<p>Thank you so much for your generous donation. Your contribution is tax deductible. </p>";

            $html .= '<table border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" align="center">
											<tbody>
											<tr>
												<td align="center">
												<table border="0" cellpadding="0" cellspacing="0">
													<tbody>
													<tr>
													<td align="center">';
            $html .= "<a href=\"https://$api_url/receipt/$transaction_id\" target=\"_blank\">VIEW RECEIPT</a></td>";
            $html .= '</tr>
													</tbody>
												</table>
												</td>
											</tr>
											</tbody>
										</table>


										<p>Vigilant as always,<br />
										<br />
										Kevin Hunsanger<br />
										Development Director</p>
										</td>
									</tr>
									</table>
								</td>
								</tr>

							<!-- END MAIN CONTENT AREA -->
							</table>

							<!-- START FOOTER -->
							<div class="footer">
								<table border="0" cellpadding="0" cellspacing="0">
								<tr>
									<td class="content-block">
									<span class="apple-link">KPFA - 1929 Martin Luther King Jr Way. Berkeley, CA 94704</span>
									<br>KPFA Radio is an affiliate of Pacifica Radio Foundation who is a registered 501(c)3 organization.
									<br>EIN #94-1347046
									<br>This is a one-time transactional email because of your donation to KPFA.
									</td>
								</tr>
								</table>
							</div>
							<!-- END FOOTER -->

							<!-- END CENTERED WHITE CONTAINER -->
							</div>
						</td>
						<td>&nbsp;</td>
						</tr>
					</table>
					</body>
				</html>
				';

            // ** [RETURN] Section  ** //
            return $html;
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
    // Send Message
    public function sendMessage($type, $transaction_id) {
        try {
            // TODO: replace with functions.
            $query =
                "SELECT
						" .
                $this->donations_table .
                ".`id`,
						" .
                $this->donations_table .
                ".`timestamp`,
						" .
                $this->donors_table .
                ".`firstname`,
						" .
                $this->donors_table .
                ".`lastname`,
						" .
                $this->donors_table .
                ".`email`,
						" .
                $this->donations_table .
                ".`amount`,
						" .
                $this->donations_table .
                ".`installment`,
						" .
                $this->payments_table .
                ".`amount` as `payment_amount`,
						" .
                $this->payments_table .
                ".`amount_refunded` as `amount_refunded`
								FROM
										" .
                $this->donations_table .
                "
										LEFT JOIN " .
                $this->donors_table .
                " ON " .
                $this->donations_table .
                ".donor_id = " .
                $this->donors_table .
                ".id
										LEFT JOIN " .
                $this->payments_table .
                " ON " .
                $this->donations_table .
                ".id = " .
                $this->payments_table .
                ".donation_id
								WHERE
									transaction_id = ?";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of pledge to be updated
            $stmt->bindParam(1, $transaction_id);

            // execute query
            $stmt->execute();

            // get retrieved row
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            // set values to object properties
            $id = $row["id"];
            $timestamp = $row["timestamp"];
            $firstname = $row["firstname"];
            $lastname = $row["lastname"];
            $name = preg_replace("/[^a-z0-9.]+/i", " ", "$firstname $lastname");
            $email = $row["email"];
            if (
                $row["payment_amount"] >= $row["amount"] &&
                $row["amount_refunded"] == 0
            ) {
                $status = "paid";
            }
            if (
                $row["payment_amount"] >= $row["amount"] &&
                $row["amount_refunded"] > 0
            ) {
                $status = "refunded";
            }
            if (
                $row["payment_amount"] - $row["amount_refunded"] <
                $row["amount"]
            ) {
                $status = "Partially Paid";
            }
            if ($row["payment_amount"] == 0) {
                $status = "unpaid";
            }
            $pledge_amount = number_format((float) $row["amount"], 2, ".", "");
            $installment = $row["installment"];

            // Query2
            $query2 =
                "SELECT
							" .
                $this->premiums_table .
                ".`name`,
							`price`,
							`cog`,
							`fmv`,
							`quantity` AS `qty`,
							`download_url`,
							`img_url`,
							" .
                $this->shipments_table .
                ".`status`,
							" .
                $this->premium_categories_table .
                ".`name` AS category
							FROM
							" .
                $this->shipments_table .
                "
							LEFT JOIN " .
                $this->premiums_table .
                " ON " .
                $this->shipments_table .
                ".`premium_id` = " .
                $this->premiums_table .
                ".`id`
							LEFT JOIN " .
                $this->premium_categories_table .
                " ON " .
                $this->premiums_table .
                ".`category_id` = " .
                $this->premium_categories_table .
                ".`id`
							LEFT JOIN " .
                $this->donations_table .
                " ON " .
                $this->shipments_table .
                ".`donation_id` = " .
                $this->donations_table .
                ".`id`
							WHERE
							" .
                $this->donations_table .
                ".`transaction_id` =  '" .
                $transaction_id .
                "'";

            // prepare query statement
            $stmt2 = $this->conn->prepare($query2);

            // initialize an array for the results
            $premiums = [];
            if ($stmt2->execute()) {
                while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                    $premiums[] = $row2;
                    $shipping_status = $row2["status"];
                }
            } else {
                throw new Exception("fail on email stmt2");
            }
            // begin

            if (!empty($email)) {
                // make sure email is available
                // Prepare message
                //////////////////////////////////////////////////////// THANKYOU EMAIL ////////////////////////////////////////////////////////
                if ($type == "thankyou") {
                    $subject = "Thank you from all of us at KPFA!"; // email subject
                    if ($status == "paid") {
                        // Paid Donation
                        $html = $this->sendThankYou(
                            $id,
                            $timestamp,
                            $name,
                            $premiums,
                            $pledge_amount,
                            $installment,
                            $transaction_id,
                        ); // call function
                    }

                    if ($status != "paid") {
                        // Bill Donation
                        $html = $this->sendBill(
                            $id,
                            $timestamp,
                            $name,
                            $premiums,
                            $pledge_amount,
                            $installment,
                            $transaction_id,
                        ); // call function
                    }
                }

                //////////////////////////////////////////////////////// REMINDER EMAIL ////////////////////////////////////////////////////////
                if ($type == "reminder") {
                    $subject =
                        "Your KPFA Invoice: Instructions to Fulfill Your Pledge!"; // email subject
                    if ($status == "unpaid") {
                        // "Reminder email"
                        $html = $this->sendReminder(
                            $id,
                            $timestamp,
                            $name,
                            $premiums,
                            $pledge_amount,
                            $installment,
                            $transaction_id,
                        ); // call function
                    }

                    if ($status != "unpaid") {
                        // "Reminder email"
                        throw new Exception(
                            "Cannot send $type email to $name. Donation status is $status",
                        );
                    }
                }
                //////////////////////////////////////////////////////// SHIPPING EMAIL ////////////////////////////////////////////////////////
                if ($type == "shipping") {
                    $subject = "KPFA Shipping update!"; // email subject
                    $html = $this->sendShippingUpdate(
                        $id,
                        $timestamp,
                        $name,
                        $premiums,
                        $pledge_amount,
                        $installment,
                        $transaction_id,
                        $shipping_status,
                    ); // call function
                } elseif (
                    !in_array($type, ["thankyou", "reminder", "shipping"], true)
                ) {
                    // type is incorrect
                    throw new Exception("[type] must be correctly specified.");
                }
            } else {
                // else email is not available
                // throw new Exception("Email address unavailable for donation $id");
            }

            // send error if issue with pledge
            if ($this->send($email, $name, $html, $subject)) {
                // everything checks out with data, try and send the message
                return [$id, true, $email]; //return success
            } else {
                throw new Exception(
                    "Something went wrong. Please contact the station.",
                );
            }
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
            // return array($id, false, $info);
        } catch (PDOException $e) {
            // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
            ]);
        }
    }
    public function sendTaxLetter(object $donor) {
        try {
            global $api_url;
            $last_year = date("Y", strtotime("-1 year"));
            $token = $this->storeTokenWithDonor(intval($donor->donor_id));
            // email body
            //$html = "Hello {$donor->firstname}";
            $html =
                '
		<!doctype html>
		<html lang="en">
		  <head>
			<meta name="viewport" content="width=device-width" />
			<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <title>KPFA Tax Deductible Donations</title>' .
                $this->style .
                '</head>
		  <body class="">
			<table border="0" cellpadding="0" cellspacing="0" class="body">
			  <tr>
				<td>&nbsp;</td>
				<td class="container">
				  <div class="content">

					<!-- START CENTERED WHITE CONTAINER -->
					<span class="preheader">KPFA Deductible Donations</span>
					<table class="main">

					  <!-- START MAIN CONTENT AREA -->
					  <tr>
						<td class="wrapper">
						  <table border="0" cellpadding="0" cellspacing="0">
							<tr>
							  <td>';
            $html .= "<a class=\"navbar-brand\" href=\"https://kpfa.org\"><img src=\"https://$api_url/assets/images/KPFA_logo_wide_tagline.png\" alt=\"KPFA\" width=\"100%\"></a>";
            $html .= "<p>Dear $donor->firstname $donor->lastname,</p>
<p>Thank you for your support to KPFA Radio, a 501c(3) educational, non-profit organization, part of
the Pacifica Foundation network. Your donation(s) are tax deductible to the fullest extent of the
law. <strong>Our FEIN is: 94-1347046</strong>.<p>
<p>According to our records the total amount you donated in $last_year was <strong>$$donor->Total_Payment_Amount</strong>. You accepted thank
you gifts with a fair market value of <strong>$$donor->Total_Fair_Market_Value</strong>, therefore your deductible amount for $last_year is
<strong>$$donor->Total_Tax_Deductible</strong>.</p>
<p>Your financial support kept KPFA continuously broadcasting in 2023, and this year we will be celebrating our 75th year of existence. Thank you!</p>";
            if (isset($token)) {
                $html .=
                    '
<p>To help KPFA save resources, and go paperless, please opt-in to receiving future tax communications via email. <a href="https://' .
                    $api_url .
                    "/accounts/optin?token=" .
                    $token .
                    '">Click here to opt-in to paperless communication</a></p> 
';
            }
            $html .= "<p>If you receive multiple tax letters, it means that we have multiple accounts with differing contact information in our system. Each letter is accurate, but we will need to consolidate accounts. Please contact us to help us expedite the consolidation process.</p>

<p>For further questions and concerns regarding your charitable tax receipt please email
<a href=\"mailto:<EMAIL>\"><EMAIL></a> or leave a message at <strong>************ Ext. 610</strong>.</p>
</p>
";

            // Email footer
            $html .= '<p>Thank you,<br />
			Vicky Palacios<br />
			Membership Coordinator</p>
			</td>
			</tr>
			</table>
			</td>
			</tr>

			<!-- END MAIN CONTENT AREA -->
			</table>

			<!-- START FOOTER -->
			<div class="footer">
			  <table border="0" cellpadding="0" cellspacing="0">
				<tr>
				  <td class="content-block">
					<span class="apple-link">KPFA - 1929 Martin Luther King Jr Way. Berkeley, CA 94704</span>
					<br>KPFA Radio is an affiliate of Pacifica Radio Foundation who is a registered 501(c)3 organization.
					<br>EIN #94-1347046
					<br>This is a one-time transactional email because of your donation to KPFA.
				  </td>
				</tr>
			  </table>
			</div>
			<!-- END FOOTER -->

			<!-- END CENTERED WHITE CONTAINER -->
			</div>
			</td>
			<td>&nbsp;</td>
			</tr>
			</table>
			</body>
			</html>';
            if (
                $this->send(
                    $donor->email,
                    "$donor->firstname $donor->lastname",
                    $html,
                    "KPFA Tax Deductible Donations",
                )
            ) {
                return [$donor->firstname, true, $donor->email];
            }
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "status" => "error",
                "message" => $e->getMessage(),
            ]);
        }
    }
}
