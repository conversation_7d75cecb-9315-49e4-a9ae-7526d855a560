<?php
// Provides class for Payment
/*
Provides:
read - returns all Payments
create  - creates check payments
 */
//prettier-ignore

class Payment
{

    // database connection and table code
    private $conn;
    private $payments_table = "payments";
    private $donors_table = "donors";
    private $donations_table = "donations";

    // object properties

    // constructor with $db as database connection
    public function __construct($db)
    {
        $this->conn = $db;
    }

    public function create_stripe_payment($payment_arr)
    {
        // This function accepts an array with all needed fields to process payment from cc.
        // it returns $stmt on success or message on failure.
        try {
            global $api_url;
            global $testflag;
            global $stripe;

            // Strip JSON string Array "premiums": ["123", "55676", "34343"]
            if (!empty($payment_arr->premiums)) {
                $premium_json = json_encode($payment_arr->premiums); // convert array to json encoded string.
            } else {
                $premium_json = null;
            }

            // POST VARS double check //
            if (!empty($payment_arr->cardnumber)) {
                $card_num = $payment_arr->cardnumber;
            } else {
                throw new Exception("[cardnumber] is required.");
            }
            if (!empty($payment_arr->cc_securitycode)) {
                $cvc = $payment_arr->cc_securitycode;
            } else {
                throw new Exception("[cc_securitycode] is required.");
            }
            if (!empty($payment_arr->exp_month)) {
                $exp_month = $payment_arr->exp_month;
            } else {
                throw new Exception("[exp_month] is required.");
            }
            if (!empty($payment_arr->exp_year)) {
                $exp_year = $payment_arr->exp_year;
            } else {
                throw new Exception("[exp_year] is required.");
            }

            if (!empty($payment_arr->installment) && in_array($payment_arr->installment, array(
                'One-Time',
                'Monthly',
            ), true)) {
            } else {
                throw new Exception("[installment] is required and must be of type 'One-Time' or 'Monthly'");
            }
            // Data validate //
            $name = "$payment_arr->firstname" . " $payment_arr->lastname";

            // idempotency on name and minute
            $hash = md5("$name" . date('h:i'));

            // Stripe //
            // Create Stripe Token
            $token = $stripe->tokens->create([
                "card" => array(
                    "number" => $card_num,
                    "exp_month" => $exp_month,
                    "exp_year" => $exp_year,
                    "cvc" => $cvc,
                    "name" => $name,
                    "address_line1" => $payment_arr->address1,
                    "address_line2" => $payment_arr->address2,
                    "address_city" => $payment_arr->city,
                    "address_zip" => $payment_arr->postal_code,
                    "address_state" => $payment_arr->state,
                    "address_country" => $payment_arr->country,
                ),
            ]);

            // Create a Customer (if not already in db)
            if (empty($payment_arr->customer_id)) {
                // passing source will create a new payment source object,
                // make it the new customer default source, and delete the old customer default
                $customer = $stripe->customers->create([
                    'source' => $token->id, //  payment source (adds and makes default)
                    'email' => $payment_arr->email,
                    'metadata' => array(
                        'name' => $name,
                        'address_line1' => $payment_arr->address1,
                        'address_line2' => $payment_arr->address2,
                        'address_city' => $payment_arr->city,
                        'address_state' => $payment_arr->state,
                        'address_zip' => $payment_arr->postal_code,
                        'country' => $payment_arr->country,
                        'phone' => $payment_arr->phone,
                        'transaction_id' => $payment_arr->transaction_id,
                        'donor_id' => intval($payment_arr->donor_id),
                        'receipt_url' => "https://$api_url/receipts/$payment_arr->transaction_id $testflag",
                    ),
                    'name' => $name,
                    'phone' => $payment_arr->phone,
                    'address' => array(
                        'line1' => $payment_arr->address1,
                        'line2' => $payment_arr->address2,
                        'city' => $payment_arr->city,
                        'state' => $payment_arr->state,
                        'postal_code' => $payment_arr->postal_code,
                        'country' => $payment_arr->country,
                    ),
                    'shipping' => array(
                        'name' => $name,
                        'phone' => $payment_arr->phone,
                        'address' => array(
                            'line1' => $payment_arr->shipping_address1,
                            'line2' => $payment_arr->shipping_address2,
                            'city' => $payment_arr->shipping_city,
                            'state' => $payment_arr->shipping_state,
                            'postal_code' => $payment_arr->shipping_postal_code,
                            'country' => $payment_arr->shipping_country,
                        ),
                    ),
                ], array(
                    "idempotency_key" => "customer-$hash",
                ));
                $payment_arr->customer_id = $customer->id; // assign customer->id for future use
            } else {
                // Existing Customer: stripe_cus_id was found in donor db lookup.

                // list all cards
                // $cards_arr = $stripe->customers->allSources(
                //     $payment_arr->customer_id,
                //     ['object' => 'card']
                // );
                // // delete old cards
                // $stripe->customers->deleteSource(
                //     $payment_arr->customer_id,
                //     [$cards_arr->id]
                //   );

                // Create a new card
                $source = $stripe->customers->createSource(
                    $payment_arr->customer_id,
                    ['source' => $token->id] // use the token
                    ,
                    array(
                        "idempotency_key" => "customer-$hash",
                    )
                );

                // Make the donor supplied card default & update email and metadata
                $customer = $stripe->customers->update(
                    $payment_arr->customer_id,
                    array(
                        'email' => $payment_arr->email,
                        'default_source' => $source->id, // make new card default source
                        'metadata' => array(
                            'transaction_id' => $payment_arr->transaction_id,
                            'donor_id' => $payment_arr->donor_id,
                            'receipt_url' => "https://$api_url/receipt/$payment_arr->transaction_id $testflag", // Receipt endpoint
                        ),
                    )
                );
            }

            // --- [CHARGE] ---
            // Charge the Customer One-Time
            if ($payment_arr->installment == 'One-Time') {

                $charge = $stripe->charges->create([ //charge the card
                    'customer' => $payment_arr->customer_id,
                    'amount' => $payment_arr->amount * 100, // amount in cents
                    'currency' => 'usd',
                    'description' => $payment_arr->source . ' donation from ' . $name, //where it came in // what it means
                    'shipping' => array(
                        'name' => $name,
                        'phone' => $payment_arr->phone,
                        'address' => array(
                            'line1' => $payment_arr->address1,
                            'line2' => $payment_arr->address2,
                            'city' => $payment_arr->city,
                            'state' => $payment_arr->state,
                            'postal_code' => $payment_arr->postal_code,
                            'country' => $payment_arr->country,
                        ),
                    ),
                    'metadata' => array(
                        'Premium' => $premium_json,
                        'Source' => $payment_arr->source,
                        'Show' => $payment_arr->show_name,
                        'Comment' => isset($payment_arr->comments) ? substr(htmlspecialchars_decode($payment_arr->comments), 0, 499) : "", // Stripe 500 character limit
                        'transaction_id' => $payment_arr->transaction_id,
                        'donor_id' => intval($payment_arr->donor_id),
                    ),
                ], array(
                    "idempotency_key" => "charge-$hash",
                ));
            }
            // Create subscription if monthly
            if ($payment_arr->installment == 'Monthly') {
                // lookup or create price and use as plan_name
                $price_id = (new Subscription($this->conn))->readCreateStripePrices($payment_arr->amount);
                if (!empty($price_id)) {
                    $subscription = $stripe->subscriptions->create([
                        'customer' => $payment_arr->customer_id,
                        'metadata' => array(
                            'transaction_id' => $payment_arr->transaction_id,
                            'donor_id' => intval($payment_arr->donor_id),
                        ),
                        'items' => [[
                            'price' => $price_id,
                            'metadata' => array(
                                'transaction_id' => $payment_arr->transaction_id,
                                'donor_id' => intval($payment_arr->donor_id),
                            ),
                        ]],
                        'default_payment_method' => $customer->default_source,
                        'payment_behavior' => 'error_if_incomplete',
                         'payment_settings' =>
                           ['payment_method_types' => ['card', 'us_bank_account', 'link']],
                    ]);
                } else {
                    throw new Exception("[price_id] could not be found or created");
                }
            }

            // grab payment_id most recent customer charge
            $this->payment_id = $stripe->charges->all(['customer' => $payment_arr->customer_id])->data[0]->id;
            $this->status = "succeeded"; // set succeded

            return true;

            // ** [Communication] Section  ** //
            // Execute Email (Receipt and Thank-you) process (after DB query)
            if (!empty($payment_arr->email) && $charge->status !== "succeeded") {
                (new Email($this->conn))->sendMessage("thankyou", $this->transaction_id); // call sendMessage function from Email Class
            }
        } catch (PDOException $e) { // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) { // PHP Error message
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
        } catch (\Stripe\Exception\CardException $e) {
            // Since it's a decline, \Stripe\Exception\CardException will be caught
            http_response_code($e->getHttpStatus()); // from stripe
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\RateLimitException $e) {
            // Too many requests made to the API too quickly
            http_response_code($e->getHttpStatus()); // from stripe
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            // Invalid parameters were supplied to Stripe's API
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\AuthenticationException $e) {
            // Authentication with Stripe's API failed
            // (maybe you changed API keys recently)
            http_response_code($e->getHttpStatus()); // from stripe
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\ApiConnectionException $e) {
            // Network communication with Stripe failed
            http_response_code($e->getHttpStatus()); // from stripe
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Display a very generic error to the user, and maybe send
            // yourself an email
            http_response_code($e->getHttpStatus()); // from stripe
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (Exception $e) {
            // Something else happened, completely unrelated to Stripe
            http_response_code(400); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
            error_log($e->getMessage(), 0);
        }
    }
    public function create_stripe_intent_payment($payment_arr)
    {
        try {
            global $api_url;
            global $stripe;
            global $paymentIntent;
            global $testflag;

            if (!empty($_SERVER["HTTP_CF_IPCOUNTRY"]) && !in_array($_SERVER["HTTP_CF_IPCOUNTRY"], ['US', 'CA', 'AU', 'GB', 'IE', 'FR'], true)) {
                // Be more skeptical towards non Western donors and send a captcha
                // TODO CAPTCHA: the first payment attempt from an IP address succeeds without restriction,
                // but subsequent requests made by that same IP address for the next several hours require a captcha verification to succeed.
            }

            // Strip JSON string Array "premiums": ["123", "55676", "34343"]
            if (!empty($payment_arr->premiums)) {
                $premium_json = json_encode($payment_arr->premiums); // convert array to json encoded string.
            } else {
                $premium_json = null;
            }

            // Data validate //
            $name = "$payment_arr->firstname" . " $payment_arr->lastname";

            // idempotency on name and minute
            $hash = md5("$name" . date('h:i'));

            // Stripe //
            // Create a Customer (if not already in db)
            if (empty($payment_arr->customer_id)) {
                // passing source will create a new payment source object,
                // make it the new customer default source, and delete the old customer default
                $customer = $stripe->customers->create([
                    'email' => $payment_arr->email,
                    'metadata' => array(
                        'name' => $name,
                        'address_line1' => $payment_arr->address1,
                        'address_line2' => $payment_arr->address2,
                        'address_city' => $payment_arr->city,
                        'address_state' => $payment_arr->state,
                        'address_zip' => $payment_arr->postal_code,
                        'country' => $payment_arr->country,
                        'phone' => $payment_arr->phone,
                        'donor_id' => intval($payment_arr->donor_id),
                        'transaction_id' => $payment_arr->transaction_id,
                        'receipt_url' => "https://$api_url/receipts/$payment_arr->transaction_id",
                    ),
                    'name' => $name,
                    'phone' => $payment_arr->phone,
                    'address' => array(
                        'line1' => $payment_arr->address1,
                        'line2' => $payment_arr->address2,
                        'city' => $payment_arr->city,
                        'state' => $payment_arr->state,
                        'postal_code' => $payment_arr->postal_code,
                        'country' => $payment_arr->country,
                    ),
                    'shipping' => array(
                        'name' => $name,
                        'phone' => $payment_arr->phone,
                        'address' => array(
                            'line1' => $payment_arr->shipping_address1,
                            'line2' => $payment_arr->shipping_address2,
                            'city' => $payment_arr->shipping_city,
                            'state' => $payment_arr->shipping_state,
                            'postal_code' => $payment_arr->shipping_postal_code,
                            'country' => $payment_arr->shipping_country,
                        ),
                    ),
                ], array(
                    "idempotency_key" => "customer-$hash",
                ));
                // reset variable
                $payment_arr->customer_id = $customer->id;

                // Update DB (SA Donor must be created first)
                (new Donor($this->conn))->update($payment_arr->donor_id, "stripe_cus_id", $customer->id);
            } else {
                // -- [Customer Update] --
                // Update existing customer with current addr, email, phone and metadata
                $stripe->customers->update(
                    $payment_arr->customer_id,
                    array(
                        'email' => $payment_arr->email,
                        'phone' => $payment_arr->phone,
                        'address' => array(
                            'line1' => $payment_arr->address1,
                            'line2' => $payment_arr->address2,
                            'city' => $payment_arr->city,
                            'state' => $payment_arr->state,
                            'postal_code' => $payment_arr->postal_code,
                            'country' => $payment_arr->country,
                        ),
                        'shipping' => array(
                            'name' => $name,
                            'phone' => $payment_arr->phone,
                            'address' => array(
                                'line1' => $payment_arr->shipping_address1,
                                'line2' => $payment_arr->shipping_address2,
                                'city' => $payment_arr->shipping_city,
                                'state' => $payment_arr->shipping_state,
                                'postal_code' => $payment_arr->shipping_postal_code,
                                'country' => $payment_arr->shipping_country,
                            ),
                        ),
                        'metadata' => array(
                            'transaction_id' => $payment_arr->transaction_id,
                            'donor_id' => intval($payment_arr->donor_id),
                            'receipt_url' => "https://$api_url/receipts/$payment_arr->transaction_id $testflag",
                        ),
                    )
                );
            }
            // Create subscription if monthly
            if (isset($payment_arr->installment) && $payment_arr->installment == 'Monthly') {
                // lookup or create price and use as plan_name
                $price_id = (new Subscription($this->conn))->readCreateStripePrices($payment_arr->amount);

                $subscription = $stripe->subscriptions->create([
                    'customer' => $payment_arr->customer_id,
                    'metadata' => array(
                        'transaction_id' => $payment_arr->transaction_id,
                        'donor_id' => $payment_arr->donor_id,
                         'Premium' => $premium_json,
                    ),
                    'items' => [[
                        'price' => $price_id,
                        'metadata' => array(
                            'transaction_id' => $payment_arr->transaction_id,
                            'donor_id' => $payment_arr->donor_id,
                        ),
                    ]],
                    'payment_behavior' => 'default_incomplete',
                    'expand' => ['latest_invoice.confirmation_secret'],
                     'payment_settings' =>
                       ['payment_method_types' => ['card', 'us_bank_account', 'link']],
                ]);

                // Handle subscription creation with new API version behavior

                // With error_if_incomplete behavior, subscription will either be active or incomplete
                if ($subscription->status === 'incomplete') {
                    // With API version 2025-06-30.basil and error_if_incomplete behavior,
                    // the invoice will be created and require payment confirmation
                    if ($subscription->latest_invoice) {
                        // Use the already-expanded invoice from subscription creation
                        $invoice = $subscription->latest_invoice;
                        $invoice_id = $invoice->id;

                            // With API version 2025-06-30.basil, use confirmation_secret instead of payment_intent
                            if ($invoice->confirmation_secret) {
                                if ($invoice->confirmation_secret->client_secret) {
                                    $client_secret = $invoice->confirmation_secret->client_secret;
                                    return $client_secret;
                                }
                            } else {
                                // For open invoices without confirmation secret, create payment intent manually
                                if ($invoice->status === 'open') {
                                    try {
                                        $payment_intent = $stripe->paymentIntents->create([
                                            'amount' => $invoice->amount_due,
                                            'currency' => $invoice->currency,
                                            'customer' => $invoice->customer,
                                            'setup_future_usage' => 'off_session',
                                            'metadata' => [
                                                'invoice_id' => $invoice_id,
                                                'subscription_id' => $subscription->id,
                                                'transaction_id' => $payment_arr->transaction_id,
                                                'donor_id' => $payment_arr->donor_id,
                                            ],
                                            'payment_method_types' => ['card', 'us_bank_account'],
                                        ]);

                                        if ($payment_intent->client_secret) {
                                            $client_secret = $payment_intent->client_secret;
                                            return $client_secret;
                                        }
                                    } catch (Exception $e) {
                                        // Failed to create payment intent
                                    }
                                }

                                // If invoice is draft, try to finalize it
                                if ($invoice->status === 'draft') {
                                    try {
                                        $finalized_invoice = $stripe->invoices->finalizeInvoice($invoice_id, [
                                            'expand' => ['payment_intent']
                                        ]);

                                        if ($finalized_invoice->payment_intent && $finalized_invoice->payment_intent->client_secret) {
                                            $client_secret = $finalized_invoice->payment_intent->client_secret;
                                            return $client_secret;
                                        }
                                    } catch (Exception $e) {
                                        // Failed to finalize draft invoice
                                    }
                                }
                            }


                    }

                    throw new Exception("Incomplete subscription created but payment intent client secret is not available. This may be due to API version changes. Subscription ID: " . $subscription->id);
                } else {
                    // Handle unexpected statuses for new donation flow
                    throw new Exception("Subscription created with unexpected status: " . $subscription->status . ". Expected 'incomplete' for new donations. Subscription ID: " . $subscription->id);
                }
            }

            // -- [Payment Intent] --
            // Requires stripe customer to have created and Donation in DB. Client then finalizes payment
            // TODO: how to prevent against duplicates? reuse token, etc.
            if (isset($payment_arr->installment) && $payment_arr->installment == 'One-Time') {

                $capture_method = "automatic";
                
                // Stripe 500 character limit
                if (!empty($payment_arr->comments)) {
                    $comments = substr(htmlspecialchars_decode($payment_arr->comments), 0, 499);
                } else {
                    $comments = null;
                }

                $paymentIntent = $stripe->paymentIntents->create([
                    'amount' => $payment_arr->amount * 100, // amount in cents
                    'currency' => 'usd',
                    'capture_method' => $capture_method,
                    'automatic_payment_methods' => [
                        'enabled' => 'true',
                    ],
                    'customer' => $payment_arr->customer_id,
                    'description' => $payment_arr->source . ' donation from ' . $name,
                    'setup_future_usage' => 'off_session',
                    'confirm' => false,
                    'metadata' => array(
                        'Premium' => $premium_json,
                        'Source' => $payment_arr->source,
                        'Show' => $payment_arr->show_name,
                        'Comment' => $comments,
                        'transaction_id' => $payment_arr->transaction_id,
                        'donor_id' => intval($payment_arr->donor_id),
                        "receipt" => "https://$api_url/receipt/$payment_arr->transaction_id $testflag", // Receipt endpoint
                    ),
                    'shipping' => array(
                        'name' => $name,
                        'phone' => $payment_arr->phone,
                        'address' => array(
                            'line1' => $payment_arr->shipping_address1,
                            'line2' => $payment_arr->shipping_address2,
                            'city' => $payment_arr->shipping_city,
                            'state' => $payment_arr->shipping_state,
                            'postal_code' => $payment_arr->shipping_postal_code,
                            'country' => $payment_arr->shipping_country,
                        ),
                    ),
                ]);

                // return ClientSecret if success
                return $paymentIntent->client_secret;
            }
        } catch (\Stripe\Exception\CardException $e) {
            // Since it's a decline, \Stripe\Exception\CardException will be caught
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\RateLimitException $e) {
            // Too many requests made to the API too quickly
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            // Invalid parameters were supplied to Stripe's API
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\AuthenticationException $e) {
            // Authentication with Stripe's API failed
            // (maybe you changed API keys recently)
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\ApiConnectionException $e) {
            // Network communication with Stripe failed
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Display a very generic error to the user, and maybe send
            // yourself an email
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (Exception $e) {
            // Something else happened, completely unrelated to Stripe
            http_response_code(400); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
            error_log($e->getMessage(), 0);
        }
    }
    public function create_cash_payment()
    {
        try {
            // check to ensure donation_id exists
            if ((new Donation($this->conn))->readDonation($this->donation_id)->rowCount() < 1) {
                http_response_code(400); // Bad Request
                throw new Exception("[donation_id] could not be found.");
            }

            // Insert payment into DB
            $query = "INSERT INTO " . $this->payments_table . " (
            " . $this->payments_table . ".`donation_id`,
            " . $this->payments_table . ".`payment_id`,
            " . $this->payments_table . ".`amount`,
            " . $this->payments_table . ".`method`,
            " . $this->payments_table . ".`processor`,
            " . $this->payments_table . ".`date_deposited`,
            " . $this->payments_table . ".`status`)
         VALUES (:donation_id, :payment_id, :amount, :method, :processor, :date_deposited, :status);";

            // sanitize
            $this->donation_id = intval(strip_tags($this->donation_id));
            $this->payment_id = rand(100, 999999);
            $this->amount = floatval(strip_tags($this->amount));
            $this->method = "cash";
            $this->processor = "Staff";
            $this->date_deposited = date("Y-m-d H:i:s", strtotime($this->date_deposited));
            $this->status = "succeeded";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(':donation_id', $this->donation_id, PDO::PARAM_INT);
            $stmt->bindParam(':payment_id', $this->payment_id, PDO::PARAM_STR);
            $stmt->bindParam(':amount', $this->amount, PDO::PARAM_STR);
            $stmt->bindParam(':method', $this->method, PDO::PARAM_STR);
            $stmt->bindParam(':processor', $this->processor, PDO::PARAM_STR);
            $stmt->bindParam(':date_deposited', $this->date_deposited, PDO::PARAM_STR);
            $stmt->bindParam(':status', $this->status, PDO::PARAM_STR);

            // execute query
            $stmt->execute();

            // returns PDOStatement Object
            return $stmt;
        } catch (PDOException $e) { // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) { // PHP Error message
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
        }
    }

    public function create_epayment_or_check_payment(string $method_flag) {
	            if (!in_array($method_flag, ['epayment', 'check'], true)) {
            throw new InvalidArgumentException("Invalid payment type: $method_flag");
        }
    try {
        // Check that donation_id exists
        if ((new Donation($this->conn))->readDonation($this->donation_id)->rowCount() < 1) {
            http_response_code(400); // Bad Request
            throw new Exception("[donation_id] could not be found.");
        }

        // Prepare the insert query
        $query = "INSERT INTO " . $this->payments_table . " (
                " . $this->payments_table . ".`donation_id`,
                " . $this->payments_table . ".`payment_id`,
                " . $this->payments_table . ".`amount`,
                " . $this->payments_table . ".`method`,
                " . $this->payments_table . ".`processor`,
                " . $this->payments_table . ".`date_deposited`,
                " . $this->payments_table . ".`status`)
        VALUES (:donation_id, :payment_id, :amount, :method, :processor, :date_deposited, :status);";

        // Sanitize inputs
        $this->donation_id = intval(strip_tags($this->donation_id));
        $this->payment_id = htmlspecialchars(strip_tags($this->payment_id));
        $this->amount = floatval(strip_tags($this->amount));
        $this->method = htmlspecialchars(strip_tags($method_flag)); // "check" or "epayment"
        $this->processor = htmlspecialchars(strip_tags($this->processor));
        $this->date_deposited = date("Y-m-d H:i:s", strtotime($this->date_deposited));
        $this->status = htmlspecialchars(strip_tags($this->status));

        // Prepare query statement
        $stmt = $this->conn->prepare($query);

        // Bind variables
        $stmt->bindParam(':donation_id', $this->donation_id, PDO::PARAM_INT);
        $stmt->bindParam(':payment_id', $this->payment_id, PDO::PARAM_STR);
        $stmt->bindParam(':amount', $this->amount, PDO::PARAM_STR);
        $stmt->bindParam(':method', $this->method, PDO::PARAM_STR);
        $stmt->bindParam(':processor', $this->processor, PDO::PARAM_STR);
        $stmt->bindParam(':date_deposited', $this->date_deposited, PDO::PARAM_STR);
        $stmt->bindParam(':status', $this->status, PDO::PARAM_STR);

        // Execute query
        $stmt->execute();

        // Return PDOStatement Object
        return $stmt;
    } catch (PDOException $e) { // PDO Error
        http_response_code(400); // Bad Request
        echo json_encode(array(
            "status" => "error",
            "message" => $stmt->errorInfo()[2],
            "function" => __METHOD__,
        ));
    } catch (Exception $e) { // PHP Error message
        echo json_encode(array(
            "status" => "error",
            "message" => $e->getMessage(),
        ));
    }
}

    public function create_in_kind_payment()
    {
        try {
            // check to ensure donation_id exists
            if ((new Donation($this->conn))->readDonation($this->donation_id)->rowCount() < 1) {
                http_response_code(400); // Bad Request
                throw new Exception("[donation_id] could not be found.");
            }

            // Insert payment into DB
            $query = "INSERT INTO " . $this->payments_table . " (
            " . $this->payments_table . ".`donation_id`,
            " . $this->payments_table . ".`payment_id`,
            " . $this->payments_table . ".`amount`,
            " . $this->payments_table . ".`method`,
            " . $this->payments_table . ".`processor`,
            " . $this->payments_table . ".`date_deposited`,
            " . $this->payments_table . ".`status`)
         VALUES (:donation_id, :payment_id, :amount, :method, :processor, :date_deposited, :status);";

            // sanitize
            $this->donation_id = intval(strip_tags($this->donation_id));
            $this->payment_id = rand(100, 999999);
            $this->amount = floatval(strip_tags($this->amount));
            $this->method = "in-kind";
            $this->processor = "Staff";
            $this->date_deposited = date("Y-m-d H:i:s", strtotime($this->date_deposited));
            $this->status = "succeeded";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(':donation_id', $this->donation_id, PDO::PARAM_INT);
            $stmt->bindParam(':payment_id', $this->payment_id, PDO::PARAM_STR);
            $stmt->bindParam(':amount', $this->amount, PDO::PARAM_STR);
            $stmt->bindParam(':method', $this->method, PDO::PARAM_STR);
            $stmt->bindParam(':processor', $this->processor, PDO::PARAM_STR);
            $stmt->bindParam(':date_deposited', $this->date_deposited, PDO::PARAM_STR);
            $stmt->bindParam(':status', $this->status, PDO::PARAM_STR);

            // execute query
            $stmt->execute();

            // returns PDOStatement Object
            return $stmt;
        } catch (PDOException $e) { // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) { // PHP Error message
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
        }
    }
    public function refund_stripe_payment()
    {
        try {
            // look up the stripe payment_id in db from the payment.id
            // This query searches the payments table and returns the result(s)
            $query = "SELECT
            " . $this->payments_table . ".`payment_id`
                FROM	" . $this->payments_table . "
                WHERE " . $this->payments_table . ".`id` = :id
            ;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(':id', $this->id, PDO::PARAM_INT);

            // execute query
            $stmt->execute();

            // retrieve results
            $results = $stmt->fetch(PDO::FETCH_ASSOC);

            // test to see if value
            if (!empty($results)) {
                // extract the results
                extract($results);
                // try the refund & send req to stripe api
                // include stripe
                global $stripe;
                $stripe->refunds->create([
                    'charge' => $payment_id,
                    'amount' => $this->amount * 100,
                    'reason' => $this->reason, // duplicate, fraudulent, and requested_by_customer
                ]);

                // Read the payment and send as JSON
                echo json_encode($this->readPayment($this->id)->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);
            } else {
                http_response_code(400); // Bad Request
                throw new Exception("[Payment ID] could not be found.");
            }
        } catch (\Stripe\Error $e) { // Stripe Error
            http_response_code($e->getHttpStatus()); // from stripe
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
            die;
        } catch (PDOException $e) { // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) { // PHP Error message
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
        }
    }

public function update_ach_payment($internal_id, $status, $new_payment_id)
{
    $query = "UPDATE {$this->payments_table} 
              SET 
                  payment_id = :new_payment_id,
                  status = :status,
                  date_updated = NOW()
              WHERE id = :internal_id";

    $stmt = $this->conn->prepare($query);
    
    // Sanitize and bind
    $internal_id = intval($internal_id);
    $new_payment_id = substr(strip_tags($new_payment_id), 0, 255); // Match column length
    
    $stmt->bindParam(':internal_id', $internal_id, PDO::PARAM_INT);
    $stmt->bindParam(':status', $status, PDO::PARAM_STR);
    $stmt->bindParam(':new_payment_id', $new_payment_id, PDO::PARAM_STR);

    if (!$stmt->execute()) {
        error_log("ACH Update Failed: " . implode(", ", $stmt->errorInfo()));
        throw new Exception("Database update failed");
    }
    
    return true;
}

    public function update_cash_check_payment()
    {
        try {
            // CHECK to see if donation exists
            if ((new Donation($this->conn))->readDonation($this->donation_id)->rowCount() < 1) {
                http_response_code(400); // Bad Request
                throw new Exception("[donation_id] could not be found.");
            }
            // CHECK to see if payment_id exists
            if ($this->readPayment($this->id)->rowCount() < 1) {
                http_response_code(400); // Bad Request
                throw new Exception("[id] could not be found.");
            }
            // This function searches the payments table and returns the result(s)
            $query = "UPDATE " . $this->payments_table . "
                    SET
                    " . $this->payments_table . ".`donation_id`=:donation_id,
                    " . $this->payments_table . ".`payment_id`=:payment_id,
                    " . $this->payments_table . ".`amount`=:amount,
                    " . $this->payments_table . ".`method`=:method,
                    " . $this->payments_table . ".`processor`=:processor,
                    " . $this->payments_table . ".`date_deposited`=:date_deposited,
                    " . $this->payments_table . ".`status`=:status
                    WHERE " . $this->payments_table . ".`id`=:id
            ;";

            // sanitize
            $this->donation_id = intval(strip_tags($this->donation_id));
            $this->payment_id = htmlspecialchars(strip_tags($this->payment_id));
            $this->amount = floatval(strip_tags($this->amount));
            $this->method = htmlspecialchars(strip_tags($this->method));
            $this->processor = htmlspecialchars(strip_tags($this->processor));
            $this->date_deposited = date("Y-m-d H:i:s", strtotime($this->date_deposited));
            $this->status = htmlspecialchars(strip_tags($this->status));
            $this->id = intval(strip_tags($this->id));

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(':id', $this->id, PDO::PARAM_INT);
            $stmt->bindParam(':donation_id', $this->donation_id, PDO::PARAM_INT);
            $stmt->bindParam(':payment_id', $this->payment_id, PDO::PARAM_STR);
            $stmt->bindParam(':amount', $this->amount, PDO::PARAM_STR);
            $stmt->bindParam(':method', $this->method, PDO::PARAM_STR);
            $stmt->bindParam(':processor', $this->processor, PDO::PARAM_STR);
            $stmt->bindParam(':date_deposited', $this->date_deposited, PDO::PARAM_STR);
            $stmt->bindParam(':status', $this->status, PDO::PARAM_STR);

            // execute query
            $stmt->execute();

            // Read the payment and send as JSON
            echo json_encode($this->readPayment($this->id)->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);

            // return values from database
            return $stmt;
        }
        // catch exceptions
        catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }
    public function update_card_payment()
    {
        try {
            // CHECK to see if donation exists
            if ((new Donation($this->conn))->readDonation($this->donation_id)->rowCount() < 1) {
                http_response_code(400); // Bad Request
                throw new Exception("[donation_id] could not be found.");
            }
            // CHECK to see if payment_id exists
            if ($this->readPayment($this->id)->rowCount() < 1) {
                http_response_code(400); // Bad Request
                throw new Exception("[id] could not be found.");
            }
            // This function searches the payments table and returns the result(s)
            $query = "UPDATE " . $this->payments_table . "
                    SET
                    " . $this->payments_table . ".`brand`=:brand,
		    " . $this->payments_table . ".`method`='card',
                    " . $this->payments_table . ".`date_created`=:date_created,
                    " . $this->payments_table . ".`last4`=:last4,
                    " . $this->payments_table . ".`exp_month`=:exp_month,
                    " . $this->payments_table . ".`exp_year`=:exp_year
                    WHERE " . $this->payments_table . ".`id`=:id
            ;";

            // sanitize
//            $this->brand = strip_tags($this->brand);
            $this->date_created = date("Y-m-d H:i:s", strtotime($this->date_created));
//            $this->last4 = intval(strip_tags($this->last4));
//            $this->exp_month = intval(strip_tags($this->exp_month));
//            $this->exp_year = intval(strip_tags($this->exp_year));
            $this->id = intval(strip_tags($this->id));

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(':id', $this->id, PDO::PARAM_INT);
            $stmt->bindParam(':brand', $this->brand, PDO::PARAM_STR);
            $stmt->bindParam(':date_created', $this->date_created, PDO::PARAM_STR);
            $stmt->bindParam(':last4', $this->last4, PDO::PARAM_INT);
            $stmt->bindParam(':exp_month', $this->exp_month, PDO::PARAM_INT);
            $stmt->bindParam(':exp_year', $this->exp_year, PDO::PARAM_INT);

            // execute query
            $stmt->execute();

            // return bool if okay
            return true;
            //echo json_encode($this->readPayment($this->id)->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);
        }
        // catch exceptions
        catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }
    public function delete()
    {
        try {
            // This function deletes a payment from the payments table and returns the result(s)
            $query = "DELETE FROM " . $this->payments_table . "
                    WHERE " . $this->payments_table . ".`id`=:id
                    AND `method` != 'card'
            ;";

            // sanitize
            $this->id = intval(strip_tags($this->id));

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(':id', $this->id, PDO::PARAM_INT);

            // execute query

            if ($stmt->execute()) {
                if ($stmt->rowCount() == 1) {
                    // return values from database
                    return true;
                } else {
                    throw new Exception("Payment Record does not exist and could not deleted", 1);
                    return false;
                }
            }
        }
        // catch exceptions
        catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }
    public function read()
    {
        try {
            // This function searches the payments table and returns the result(s)
            $query = "SELECT
                    " . $this->payments_table . ".`id`,
                    " . $this->payments_table . ".`donation_id`,
                    " . $this->payments_table . ".`customer_id`,
                    " . $this->payments_table . ".`payment_id`,
                    " . $this->payments_table . ".`amount`,
                    " . $this->payments_table . ".`amount_refunded`,
                    " . $this->payments_table . ".`method`,
                    " . $this->payments_table . ".`processor`,
                    " . $this->payments_table . ".`fingerprint`,
                    " . $this->payments_table . ".`card_type`,
                    " . $this->payments_table . ".`last4`,
                    " . $this->payments_table . ".`brand`,
                    " . $this->payments_table . ".`exp_month`,
                    " . $this->payments_table . ".`exp_year`,
                    " . $this->payments_table . ".`last_updated_by`,
                    " . $this->payments_table . ".`date_created`,
                    " . $this->payments_table . ".`date_updated`,
                    " . $this->payments_table . ".`date_deposited`,
                    " . $this->payments_table . ".`status`,
                    " . $this->donors_table . ".firstname,
                    " . $this->donors_table . ".lastname,
                    " . $this->donors_table . ".address1,
                    " . $this->donors_table . ".address2,
                    " . $this->donors_table . ".city,
                    " . $this->donors_table . ".state,
                    " . $this->donors_table . ".postal_code,
                    " . $this->donors_table . ".country,
                    " . $this->donors_table . ".email,
                    " . $this->donors_table . ".phone,
                    " . $this->donors_table . ".id as donor_id

            FROM	" . $this->payments_table . "
            LEFT JOIN " . $this->donations_table . " ON " . $this->payments_table . ".`donation_id` = " . $this->donations_table . ".`id`
            LEFT JOIN " . $this->donors_table . " ON " . $this->donations_table . ".`donor_id` = " . $this->donors_table . ".`id`
            ORDER BY `payments`.`date_created` DESC LIMIT 100;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
    public function readPayment($id)
    {
        try {
            // This function reads a single record from the payments table and returns the result
            $query = "SELECT
                    " . $this->payments_table . ".`id`,
                    " . $this->payments_table . ".`donation_id`,
                    " . $this->payments_table . ".`customer_id`,
                    " . $this->payments_table . ".`payment_id`,
                    " . $this->payments_table . ".`amount`,
                    " . $this->payments_table . ".`amount_refunded`,
                    " . $this->payments_table . ".`method`,
                    " . $this->payments_table . ".`processor`,
                    " . $this->payments_table . ".`fingerprint`,
                    " . $this->payments_table . ".`card_type`,
                    " . $this->payments_table . ".`last4`,
                    " . $this->payments_table . ".`brand`,
                    " . $this->payments_table . ".`exp_month`,
                    " . $this->payments_table . ".`exp_year`,
                    " . $this->payments_table . ".`last_updated_by`,
                    " . $this->payments_table . ".`date_created`,
                    " . $this->payments_table . ".`date_updated`,
                    " . $this->payments_table . ".`date_deposited`,
                    " . $this->payments_table . ".`status`,
                    " . $this->donors_table . ".firstname,
                    " . $this->donors_table . ".lastname,
                    " . $this->donors_table . ".address1,
                    " . $this->donors_table . ".address2,
                    " . $this->donors_table . ".city,
                    " . $this->donors_table . ".state,
                    " . $this->donors_table . ".postal_code,
                    " . $this->donors_table . ".country,
                    " . $this->donors_table . ".email,
                    " . $this->donors_table . ".phone,
                    " . $this->donors_table . ".id as donor_id

            FROM	" . $this->payments_table . "
            LEFT JOIN " . $this->donations_table . " ON " . $this->payments_table . ".`donation_id` = " . $this->donations_table . ".`id`
            LEFT JOIN " . $this->donors_table . " ON " . $this->donations_table . ".`donor_id` = " . $this->donors_table . ".`id`
            WHERE " . $this->payments_table . ".`id` = ?;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
    public function readStripePayment($id)
    {
        try {
            // This function reads a single record from the payments table and returns the result
            $query = "SELECT
                    " . $this->payments_table . ".`id`,
                    " . $this->payments_table . ".`donation_id`,
                    " . $this->payments_table . ".`customer_id`,
                    " . $this->payments_table . ".`payment_id`,
                    " . $this->payments_table . ".`amount`,
                    " . $this->payments_table . ".`amount_refunded`,
                    " . $this->payments_table . ".`method`,
                    " . $this->payments_table . ".`processor`,
                    " . $this->payments_table . ".`fingerprint`,
                    " . $this->payments_table . ".`card_type`,
                    " . $this->payments_table . ".`last4`,
                    " . $this->payments_table . ".`brand`,
                    " . $this->payments_table . ".`exp_month`,
                    " . $this->payments_table . ".`exp_year`,
                    " . $this->payments_table . ".`last_updated_by`,
                    " . $this->payments_table . ".`date_created`,
                    " . $this->payments_table . ".`date_updated`,
                    " . $this->payments_table . ".`date_deposited`,
                    " . $this->payments_table . ".`status`,
                    " . $this->donors_table . ".firstname,
                    " . $this->donors_table . ".lastname,
                    " . $this->donors_table . ".address1,
                    " . $this->donors_table . ".address2,
                    " . $this->donors_table . ".city,
                    " . $this->donors_table . ".state,
                    " . $this->donors_table . ".postal_code,
                    " . $this->donors_table . ".country,
                    " . $this->donors_table . ".email,
                    " . $this->donors_table . ".phone,
                    " . $this->donors_table . ".id as donor_id

            FROM	" . $this->payments_table . "
            LEFT JOIN " . $this->donations_table . " ON " . $this->payments_table . ".`donation_id` = " . $this->donations_table . ".`id`
            LEFT JOIN " . $this->donors_table . " ON " . $this->donations_table . ".`donor_id` = " . $this->donors_table . ".`id`
            WHERE " . $this->payments_table . ".`payment_id` = ?;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
    public function searchPayments($keywords)
    {
        try {
            // This function reads a single record from the payments table and returns the result
            $query = "SELECT
                    " . $this->payments_table . ".`id`,
                    " . $this->payments_table . ".`donation_id`,
                    " . $this->payments_table . ".`customer_id`,
                    " . $this->payments_table . ".`payment_id`,
                    " . $this->payments_table . ".`amount`,
                    " . $this->payments_table . ".`amount_refunded`,
                    " . $this->payments_table . ".`method`,
                    " . $this->payments_table . ".`processor`,
                    " . $this->payments_table . ".`fingerprint`,
                    " . $this->payments_table . ".`card_type`,
                    " . $this->payments_table . ".`last4`,
                    " . $this->payments_table . ".`brand`,
                    " . $this->payments_table . ".`exp_month`,
                    " . $this->payments_table . ".`exp_year`,
                    " . $this->payments_table . ".`last_updated_by`,
                    " . $this->payments_table . ".`date_created`,
                    " . $this->payments_table . ".`date_updated`,
                    " . $this->payments_table . ".`date_deposited`,
                    " . $this->payments_table . ".`status`,
                    " . $this->donors_table . ".firstname,
                    " . $this->donors_table . ".lastname,
                    " . $this->donors_table . ".address1,
                    " . $this->donors_table . ".address2,
                    " . $this->donors_table . ".city,
                    " . $this->donors_table . ".state,
                    " . $this->donors_table . ".postal_code,
                    " . $this->donors_table . ".country,
                    " . $this->donors_table . ".email,
                    " . $this->donors_table . ".phone,
                    " . $this->donors_table . ".id as donor_id

            FROM	" . $this->payments_table . "
            LEFT JOIN " . $this->donations_table . " ON " . $this->payments_table . ".`donation_id` = " . $this->donations_table . ".`id`
            LEFT JOIN " . $this->donors_table . " ON " . $this->donations_table . ".`donor_id` = " . $this->donors_table . ".`id`
            WHERE CONCAT_WS(' '," . $this->donors_table . ".`firstname`, " . $this->donors_table . ".`lastname`) LIKE ? OR " . $this->donations_table . ".`id` = ? OR " . $this->donors_table . ".email LIKE ?
            ORDER BY `payments`.`date_created` DESC LIMIT 100;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $keywords);
            $stmt->bindParam(2, $keywords);
            $stmt->bindParam(3, $keywords);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
    public function readDate($startdate, $enddate, $status)
    {
        try {
            // This function reads a single record from the payments table and returns the result
            $query = "SELECT
                    " . $this->payments_table . ".`id`,
                    " . $this->payments_table . ".`donation_id`,
                    " . $this->payments_table . ".`customer_id`,
                    " . $this->payments_table . ".`payment_id`,
                    " . $this->payments_table . ".`amount`,
                    " . $this->payments_table . ".`amount_refunded`,
                    " . $this->payments_table . ".`method`,
                    " . $this->payments_table . ".`processor`,
                    " . $this->payments_table . ".`fingerprint`,
                    " . $this->payments_table . ".`card_type`,
                    " . $this->payments_table . ".`last4`,
                    " . $this->payments_table . ".`brand`,
                    " . $this->payments_table . ".`exp_month`,
                    " . $this->payments_table . ".`exp_year`,
                    " . $this->payments_table . ".`last_updated_by`,
                    " . $this->payments_table . ".`date_created`,
                    " . $this->payments_table . ".`date_updated`,
                    " . $this->payments_table . ".`date_deposited`,
                    " . $this->payments_table . ".`status`,
                    " . $this->donors_table . ".firstname,
                    " . $this->donors_table . ".lastname,
                    " . $this->donors_table . ".address1,
                    " . $this->donors_table . ".address2,
                    " . $this->donors_table . ".city,
                    " . $this->donors_table . ".state,
                    " . $this->donors_table . ".postal_code,
                    " . $this->donors_table . ".country,
                    " . $this->donors_table . ".email,
                    " . $this->donors_table . ".phone,
                    " . $this->donors_table . ".id as donor_id

            FROM	" . $this->payments_table . "
            LEFT JOIN " . $this->donations_table . " ON " . $this->payments_table . ".`donation_id` = " . $this->donations_table . ".`id`
            LEFT JOIN " . $this->donors_table . " ON " . $this->donations_table . ".`donor_id` = " . $this->donors_table . ".`id`
            WHERE " . $this->payments_table . ".`date_created` >= ? AND " . $this->payments_table . ".`date_created` <= ? AND " . $this->payments_table . ".`status` = ?
            ORDER BY `payments`.`date_created` ASC;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $startdate);
            $stmt->bindParam(2, $enddate);
            $stmt->bindParam(3, $status);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
}
