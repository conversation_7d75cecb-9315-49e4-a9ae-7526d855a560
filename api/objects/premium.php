<?php
class Premium {
    // database connection and table name
    private $conn;
    private $premiums_table = "premiums";
    private $premium_vendors_table = "premium_vendors";
    private $premium_categories_table = "premium_categories";

    // object properties
    public $id;
    public $name;
    public $description;
    public $img_url;
    public $download_url;
    public $price;
    public $cog;
    public $fmv;
    public $qty;
    public $variant_name;
    public $variation_name;
    public $is_parent;
    public $parentID;
    public $active;
    public $category_id;
    public $category_name;
    public $featured;
    public $created;

    // constructor with $db as database connection
    public function __construct($db) {
        $this->conn = $db;
    }

    //adding this as a hotfix for using ampersands and quotes in premium titles. there is probably a better way to handle this - we could globalize this function or handle it more on the front end or better yet improve the way we're handling the data to store, encode, and decode with safer strings. but this is just staff used so okay for now.
    private function customSanitize($string) {
        // Characters to allow
        $allowedCharacters = [
            "&" => "[AMPERSAND]",
            '"' => "[DOUBLE_QUOTE]",
            "'" => "[SINGLE_QUOTE]",
        ];

        // Replace allowed characters with placeholders
        foreach ($allowedCharacters as $char => $placeholder) {
            $string = str_replace($char, $placeholder, $string);
        }

        // Apply htmlspecialchars
        $string = htmlspecialchars($string);

        // Restore allowed characters from placeholders
        foreach ($allowedCharacters as $char => $placeholder) {
            $string = str_replace($placeholder, $char, $string);
        }

        return $string;
    }

    // read premiums
    public function read() {
        try {
            // select single query
            $query =
                "SELECT DISTINCT
			c.name as category_name, p.id, p.name, p.description, p.cog, p.fmv,
			p.qty, p.variant_name, p.variation_name, p.parentID, IF(p.qty > 0 AND p.active = 1,TRUE,FALSE) AS active, p.img_url, p.vendor_code, p.date_created, p.sort_weight, p.vendor_id, 
			p.download_url, p.price, p.category_id, p.featured, p.created, pv.company AS vendor
			FROM
				" .
                $this->premiums_table .
                " p
 				LEFT JOIN " .
                $this->premium_vendors_table .
                " pv
             					ON p.vendor_id = pv.id
				LEFT JOIN
				" .
                $this->premium_categories_table .
                " c
						ON p.category_id = c.id
			ORDER BY
				p.sort_weight ASC, p.date_created DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
    // read active premiums
    public function readActivePremiums() {
        try {
            // select single query
            $query =
                "SELECT DISTINCT
			c.name as category_name, p.id, p.name, p.description, p.cog, p.fmv,
			p.qty, p.variant_name, p.variation_name, p.parentID, IF(p.qty > 0 AND p.active = 1,TRUE,FALSE) AS active, p.img_url, p.vendor_code, p.date_created, p.sort_weight, p.vendor_id, 
			p.download_url, p.price, p.category_id, p.featured, p.created, pv.company AS vendor
			FROM
				" .
                $this->premiums_table .
                " p
 				LEFT JOIN " .
                $this->premium_vendors_table .
                " pv
             					ON p.vendor_id = pv.id
				LEFT JOIN
				" .
                $this->premium_categories_table .
                " c
						ON p.category_id = c.id
            WHERE p.active = 1 
			ORDER BY
				p.sort_weight ASC, p.date_created DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
    // read premiums by Vendor ID
    public function premiumsByVendorID($vendor_id) {
        try {
            // select single query
            $query =
                "
                SELECT id 
                FROM
				    " .
                $this->premiums_table .
                "
    			WHERE
                    vendor_id = ?
                ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be found
            $stmt->bindParam(1, $vendor_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // read premiums by Category ID
    public function premiumsByCategoryID($category_id) {
        try {
            // select single query
            $query =
                "
                    SELECT id 
                    FROM
                        " .
                $this->premiums_table .
                "
                    WHERE
                        category_id = ?
                    ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be found
            $stmt->bindParam(1, $category_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // read children by Parent ID
    public function readChildren($parentID) {
        try {
            // query to read children of parent record and return ChildID
            $query =
                "SELECT
				id as childID, variation_name, qty as childQTY, parentID
			FROM
				" .
                $this->premiums_table .
                "

			WHERE
				parentID = ?
			";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be found
            $stmt->bindParam(1, $parentID);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // create premium
    public function create() {
        // TODO: Require Authentication within function
        try {
            // Check if title exists before trying to create
            if ($this->readPremiumbyName($this->name)->rowCount() > 0) {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "A premium with the [name] $this->name already exists. Can't create.",
                );
            }

            // query to insert record
            $query =
                "INSERT INTO
                " .
                $this->premiums_table .
                "
                    SET name=:name,
                    price=:price,
                    description=:description,
                    img_url=:img_url,
                    download_url=:download_url,
                    cog=:cog,
                    fmv=:fmv,
                    qty=:qty,
                    active=:active,
                    category_id=:category_id,
                    featured=:featured,
                    variant_name=:variant_name,
                    variation_name=:variation_name,
                    parentID=:parentID,
                    vendor_id=:vendor_id,
                    vendor_code=:vendor_code,
                    sort_weight=:sort_weight";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->name = $this->customSanitize($this->name);
            $this->price = $this->price;
            $this->active = intval($this->active);

            // bind values
            $stmt->bindParam(":name", $this->name);
            $stmt->bindParam(":price", $this->price);
            $stmt->bindParam(":description", $this->description);
            $stmt->bindParam(":img_url", $this->img_url);
            $stmt->bindParam(":download_url", $this->download_url);
            $stmt->bindParam(":cog", $this->cog);
            $stmt->bindParam(":fmv", $this->fmv);
            $stmt->bindParam(":qty", $this->qty);
            $stmt->bindParam(":active", $this->active);
            $stmt->bindParam(":category_id", $this->category_id);
            $stmt->bindParam(":featured", $this->featured);
            $stmt->bindParam(":variant_name", $this->variant_name);
            $stmt->bindParam(":variation_name", $this->variation_name);
            $stmt->bindParam(":parentID", $this->parentID);
            $stmt->bindParam(":vendor_id", $this->vendor_id);
            $stmt->bindParam(":vendor_code", $this->vendor_code);
            $stmt->bindParam(":sort_weight", $this->sort_weight);

            // Sanity Checks //
            // variant_name
            if (
                !empty($this->variant_name) &&
                !in_array($this->variant_name, ["Size", "Color", "Delivery"])
            ) {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "[variant_name] must be 'Size', 'Color', 'Delivery', or null",
                );
            }
            // variation_name
            if (
                !empty($this->variation_name) &&
                !in_array($this->variation_name, [
                    "Small",
                    "Medium",
                    "Large",
                    "XL",
                    "XXL",
                    "3XL",
                    "4XL",
                    "Black",
                    "White",
                    "One Size Fits Most",
                    "Post Mail",
                    "Download",
                    "Red",
                    "Blue",
                ])
            ) {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "[variation_name] must be 'Small', 'Medium', 'Large', 'XL', 'XXL', '3XL', '4XL', 'Black', 'White', 'One Size Fits Most', 'Post Mail', 'Download', 'Red', 'Blue', or null",
                );
            }

            // Singularity (variant_name with variation_name or ParentID)
            if (
                !empty($this->variant_name) &&
                (!empty($this->variation_name) || !empty($this->parentID))
            ) {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "A Parent premium must not contain a [parentID] or [variation_name]",
                );
            }

            // Orphan (variation_name without parentID)
            if (!empty($this->variation_name) && empty($this->parentID)) {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "A child premium must contain a [parentID]",
                );
            }

            // Unnamed child
            if (!empty($this->parentID) && empty($this->variation_name)) {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "A child premium must contain a [variation_name]",
                );
            }

            // Single
            if (
                empty($this->parentID) &&
                empty($this->variation_name) &&
                empty($this->variant_name)
            ) {
                // this is a single premium

                // execute query
                $stmt->execute();
            }

            // Parent (variant_name)
            if (
                !empty($this->variant_name) &&
                empty($this->variation_name) &&
                empty($this->parentID)
            ) {
                // variant_name must be of certain set
                // TODO: check that variant_name is in variant_name set

                // execute query
                $stmt->execute();
            }

            // Children (variations)
            // TODO: check that variation_name is in variation_name set

            // Check to make sure parentID is an integer (if set)
            if (!empty($this->parentID) && !empty($this->variation_name)) {
                if (!filter_var($this->parentID, FILTER_VALIDATE_INT)) {
                    http_response_code(400); // Bad Request
                    throw new Exception("[parentID] must be an integer");
                } else {
                    // check if premium exists before trying to create child
                    if (
                        !($tmp = $this->readPremium($this->parentID)->fetch(
                            PDO::FETCH_ASSOC,
                        ))
                    ) {
                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "[parentID] $this->parentID does not exist, please create parent before trying to add children (variations)",
                        );
                    }
                    // check if premium has a variant_name before trying to create child
                    if (!$tmp["variant_name"]) {
                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "[parentID] $this->parentID must have variant_name set before you can add children (variations)",
                        );
                    }
                    // if we're here, we have success, try and create the parent and then the children.
                }
                // execute query returns true on success or false on failure.
                // if children fail, check parent & delete parent if no children, throw exception, otherwise continue
                if (!$stmt->execute()) {
                    // check and see if parent has no chilren
                    if ($this->readChildren($this->parentID)->rowCount() == 0) {
                        // set id to parent id
                        $this->id = $this->parentID;
                        $this->delete();

                        http_response_code(400); // Bad Request
                        throw new Exception(
                            "[parentID] $this->parentID was deleted, since children failed to be created",
                        );
                    }
                }
            }

            // Read the Premium and send as JSON
            // echo json_encode($this->readPremium($this->conn->lastInsertId())->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);

            // return last insert id from database
            return $this->conn->lastInsertId();

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
            exit(); // exit on failure
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
            exit(); // exit on failure
        }
    }

    // read single premium by id
    public function readPremium($id) {
        try {
            // query to read single record
            $query =
                "SELECT DISTINCT
			c.name as category_name, p.id, p.name, p.description, p.cog, p.fmv,
			p.qty, p.variant_name, p.variation_name, p.parentID, IF(p.qty > 0 AND p.active = 1,TRUE,FALSE) AS active, p.img_url, p.vendor_code, p.date_created, p.sort_weight, p.vendor_id, 
			p.download_url, p.price, p.category_id, p.featured, p.created, pv.company AS vendor
			FROM
				" .
                $this->premiums_table .
                " p
 				LEFT JOIN " .
                $this->premium_vendors_table .
                " pv
             					ON p.vendor_id = pv.id
				LEFT JOIN
				" .
                $this->premium_categories_table .
                " c
						ON p.category_id = c.id
                WHERE p.id = ?
		 		LIMIT 0, 1;
				";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // santize args
            $id = intval($id);

            // bind id of premium to be updated
            $stmt->bindParam(1, $id);

            // execute query
            $stmt->execute();

            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // read single premium by id
    public function readPremiumbyName($name) {
        try {
            // query to read single record by exact name
            $query =
                "SELECT DISTINCT
                c.name as category_name, p.id, p.name, p.description, p.cog, p.fmv,
                p.qty, p.variant_name, p.variation_name, p.parentID, IF(p.qty > 0 AND p.active = 1,TRUE,FALSE) AS active, p.img_url, p.vendor_code, p.date_created, p.sort_weight, p.vendor_id, 
                p.download_url, p.price, p.category_id, p.featured, p.created, pv.company AS vendor
                FROM
                    " .
                $this->premiums_table .
                " p
                     LEFT JOIN " .
                $this->premium_vendors_table .
                " pv
                                     ON p.vendor_id = pv.id
                    LEFT JOIN
                    " .
                $this->premium_categories_table .
                " c
                            ON p.category_id = c.id
                    WHERE p.name = ?
                     LIMIT 0, 1;
                    ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of premium to be updated
            $stmt->bindParam(1, $name);

            // execute query
            $stmt->execute();

            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // update the premium
    public function update() {
        try {
            // Check to see if premium exists, create if doesn't exist
            if ($this->readPremium($this->id)->rowCount() < 1) {
                $this->create();
            } else {
                $query =
                    "UPDATE
                " .
                    $this->premiums_table .
                    "
                SET
                    name = :name,
                    price = :price,
                    description = :description,
                    img_url = :img_url,
                    download_url = :download_url,
                    cog = :cog,
                    fmv = :fmv,
                    qty = :qty,
                    active = :active,
                    category_id = :category_id,
                    featured = :featured,
                    variant_name = :variant_name,
                    variation_name = :variation_name,
                    parentID = :parentID,
                    vendor_id = :vendor_id,
                    vendor_code = :vendor_code,
                    sort_weight = :sort_weight
                WHERE
                    id = :id";

                // prepare query
                $stmt = $this->conn->prepare($query);

                // sanitize
                $this->id = $this->id;
                $this->name = $this->customSanitize($this->name);
                $this->price = $this->price;
                $this->description = $this->customSanitize($this->description);
                $this->img_url = $this->img_url;
                $this->download_url = $this->download_url;
                $this->cog = $this->cog;
                $this->fmv = $this->fmv;
                $this->qty = $this->qty;
                $this->active = intval($this->active);
                $this->category_id = intval($this->category_id);
                $this->featured = $this->featured;
                $this->variant_name = $this->variant_name;
                $this->variation_name = $this->variation_name;
                $this->parentID = $this->parentID;
                $this->vendor_id = $this->vendor_id;
                $this->vendor_code = $this->vendor_code;
                $this->sort_weight = $this->sort_weight;

                // bind values
                $stmt->bindParam(":id", $this->id);
                $stmt->bindParam(":name", $this->name);
                $stmt->bindParam(":price", $this->price);
                $stmt->bindParam(":description", $this->description);
                $stmt->bindParam(":img_url", $this->img_url);
                $stmt->bindParam(":download_url", $this->download_url);
                $stmt->bindParam(":cog", $this->cog);
                $stmt->bindParam(":fmv", $this->fmv);
                $stmt->bindParam(":qty", $this->qty);
                $stmt->bindParam(":active", $this->active);
                $stmt->bindParam(":category_id", $this->category_id);
                $stmt->bindParam(":featured", $this->featured);
                $stmt->bindParam(":variant_name", $this->variant_name);
                $stmt->bindParam(":variation_name", $this->variation_name);
                $stmt->bindParam(":parentID", $this->parentID);
                $stmt->bindParam(":vendor_id", $this->vendor_id);
                $stmt->bindParam(":vendor_code", $this->vendor_code);
                $stmt->bindParam(":sort_weight", $this->sort_weight);

                // Sanity Checks //
                // variant_name
                if (
                    !empty($this->variant_name) &&
                    !in_array($this->variant_name, [
                        "Size",
                        "Color",
                        "Delivery",
                    ])
                ) {
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "[variant_name] must be 'Size', 'Color', 'Delivery', or null",
                    );
                }
                // variation_name
                if (
                    !empty($this->variation_name) &&
                    !in_array($this->variation_name, [
                        "Small",
                        "Medium",
                        "Large",
                        "XL",
                        "XXL",
                        "3XL",
                        "4XL",
                        "Black",
                        "White",
                        "One Size Fits Most",
                        "Post Mail",
                        "Download",
                        "Red",
                        "Blue",
                    ])
                ) {
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "[variation_name] must be 'Small', 'Medium', 'Large', 'XL', 'XXL', '3XL', '4XL', 'Black', 'White', 'One Size Fits Most', 'Post Mail', 'Download', 'Red', 'Blue', or null",
                    );
                }

                // Singularity (variant_name with variation_name or ParentID)
                if (
                    !empty($this->variant_name) &&
                    (!empty($this->variation_name) || !empty($this->parentID))
                ) {
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "A Parent premium must not contain a [parentID] or [variation_name]",
                    );
                }

                // Orphan (variation_name without parentID)
                if (!empty($this->variation_name) && empty($this->parentID)) {
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "A child premium must contain a [parentID]",
                    );
                }

                // Unnamed child
                if (!empty($this->parentID) && empty($this->variation_name)) {
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "A child premium must contain a [variation_name]",
                    );
                }

                // Single
                if (
                    empty($this->parentID) &&
                    empty($this->variation_name) &&
                    empty($this->variant_name)
                ) {
                    // this is a single premium

                    // execute query
                    $stmt->execute();
                }

                // Parent (variant_name)
                if (
                    !empty($this->variant_name) &&
                    empty($this->variation_name) &&
                    empty($this->parentID)
                ) {
                    // variant_name must be of certain set
                    // TODO: check that variant_name is in variant_name set

                    // execute query
                    $stmt->execute();
                }

                // Children (variations)
                // TODO: check that variation_name is in variation_name set

                // Check to make sure parentID is an integer (if set)
                if (!empty($this->parentID) && !empty($this->variation_name)) {
                    if (!filter_var($this->parentID, FILTER_VALIDATE_INT)) {
                        http_response_code(400); // Bad Request
                        throw new Exception("[parentID] must be an integer");
                    } else {
                        // check if premium exists before trying to create child
                        if (
                            !($tmp = $this->readPremium($this->parentID)->fetch(
                                PDO::FETCH_ASSOC,
                            ))
                        ) {
                            http_response_code(400); // Bad Request
                            throw new Exception(
                                "[parentID] $this->parentID does not exist, please create parent before trying to add children (variations)",
                            );
                        }
                        // check if premium has a variant_name before trying to create child
                        if (!$tmp["variant_name"]) {
                            http_response_code(400); // Bad Request
                            throw new Exception(
                                "[parentID] $this->parentID must have variant_name set before you can add children (variations)",
                            );
                        }
                        // if we're here, we have success, try and create the parent and then the children.
                    }
                    // execute query returns true on success or false on failure.
                    // if children fail, check parent & delete parent if no children, throw exception, otherwise continue
                    if (!$stmt->execute()) {
                        // check and see if parent has no chilren
                        if (
                            $this->readChildren($this->parentID)->rowCount() ==
                            0
                        ) {
                            // set id to parent id
                            $this->id = $this->parentID;
                            $this->delete();

                            http_response_code(400); // Bad Request
                            throw new Exception(
                                "[parentID] $this->parentID was deleted, since children failed to be created",
                            );
                        }
                    }
                }

                // return the (parent) premium_id from database, to be read by update.php
                return $this->id;

                // Read the Premium and send as JSON
                //echo json_encode($this->readPremium($this->id)->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);
            }
            // update query
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
            exit();
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
            exit();
        }
    }

    // delete the premium
    public function delete() {
        try {
            // check if premium exists before trying to delete
            if ($this->readPremium($this->id)->rowCount() == 0) {
                http_response_code(400); // Bad Request
                throw new Exception("Premium: $this->id does not exist");
            } else {
                // Premium exists
                // Check if Parent premium has children.
                if (!empty($this->parentID)) {
                    // Child Premium.
                    // is this an only child?
                    if ($this->readChildren($this->parentID)->rowCount() == 1) {
                        // This "Parent has one child" -> make a single not a parent (drop variant_name)
                        // check if premium has shipments before trying to delete
                        if (
                            (new Shipping($this->conn))
                                ->readbyPremiumID($this->id)
                                ->rowCount() > 0
                        ) {
                            http_response_code(400); // Bad Request
                            throw new Exception(
                                "This Premium has shipments, please remove shipments before deleting.",
                            );
                        }
                        // delete child
                        $query =
                            "DELETE FROM " .
                            $this->premiums_table .
                            " WHERE id = ?";
                        // prepare query
                        $stmt = $this->conn->prepare($query);
                        // sanitize
                        $this->id = $this->customSanitize($this->id);
                        // bind id of record to delete
                        $stmt->bindParam(1, $this->id);
                        // execute query & update
                        if ($stmt->execute()) {
                            // update parent query to null variant_name to become single.
                            $query =
                                "UPDATE
                            " .
                                $this->premiums_table .
                                "
                            SET
                                variant_name = NULL
                            WHERE id = ?";
                            // prepare query
                            $stmt = $this->conn->prepare($query);
                            // bind id of record to delete
                            $stmt->bindParam(1, $this->parentID);

                            return true;
                        }
                    }
                    // if more than one child, then just delete
                    if ($this->readChildren($this->parentID)->rowCount() > 1) {
                        // This "Parent has many children"
                        // check if premium has shipments before trying to delete
                        if (
                            (new Shipping($this->conn))
                                ->readbyPremiumID($this->id)
                                ->rowCount() > 0
                        ) {
                            http_response_code(400); // Bad Request
                            throw new Exception(
                                "This Premium has shipments, please remove shipments before deleting.",
                            );
                        }
                        // delete the child
                        $query =
                            "DELETE FROM " .
                            $this->premiums_table .
                            " WHERE id = ?";
                        // prepare query
                        $stmt = $this->conn->prepare($query);
                        // sanitize
                        $this->id = $this->customSanitize($this->id);
                        // bind id of record to delete
                        $stmt->bindParam(1, $this->id);
                        // execute query
                        if ($stmt->execute()) {
                            return true;
                        }
                    }
                }
                // Single or Parent
                if (empty($this->parentID)) {
                    // Premium is Single or Parent
                    $premium_stmt = $this->readChildren($this->id); // check for children?

                    // Single, or Parent with no children
                    if ($premium_stmt->rowCount() == 0) {
                        // Check if premium has shipments before trying to delete
                        if (
                            (new Shipping($this->conn))
                                ->readbyPremiumID($this->id)
                                ->rowCount() > 0
                        ) {
                            http_response_code(400); // Bad Request
                            throw new Exception(
                                "This Premium $this->id has shipment(s), please remove shipments before deleting.",
                            );
                        }
                        // delete the Premium
                        $query =
                            "DELETE FROM " .
                            $this->premiums_table .
                            " WHERE id = ?";
                        // prepare query
                        $stmt = $this->conn->prepare($query);
                        // sanitize
                        $this->id = $this->customSanitize($this->id);
                        // bind id of record to delete
                        $stmt->bindParam(1, $this->id);
                        // execute query
                        if ($stmt->execute()) {
                            return true;
                        }
                    }
                    // Parent with children
                    if ($premium_stmt->rowCount() > 0) {
                        // iterate through and delete children. and then delete the parent
                        while (
                            $premium_children = $premium_stmt->fetch(
                                PDO::FETCH_ASSOC,
                            )
                        ) {
                            extract($premium_children);
                            // check if ($childID) premium has shipments before trying to delete
                            if (
                                (new Shipping($this->conn))
                                    ->readbyPremiumID($childID)
                                    ->rowCount() > 0
                            ) {
                                http_response_code(400); // Bad Request
                                throw new Exception(
                                    "Premium $childID has shipment(s). Please remove shipments before deleting.",
                                );
                            }
                            // delete the child
                            $query =
                                "DELETE FROM " .
                                $this->premiums_table .
                                " WHERE id = ?";
                            // prepare query
                            $stmt = $this->conn->prepare($query);

                            // bind id of record to delete
                            $stmt->bindParam(1, $childID);
                            // execute query
                            $stmt->execute();
                        }

                        // delete the former parent
                        $query =
                            "DELETE FROM " .
                            $this->premiums_table .
                            " WHERE id = ?";
                        // prepare query
                        $stmt = $this->conn->prepare($query);

                        // bind id of record to delete
                        $stmt->bindParam(1, $this->id);
                        // execute query
                        $stmt->execute();
                        return true;
                    }
                }
            }
            return false; // default
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
            exit(); // exit because we echo'd error
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
            exit(); // exit because we echo'd error
        }
    }
    // search premiums
    public function search($keywords) {
        try {
            // select all query
            $query =
                "SELECT DISTINCT
		c.name as category_name, p.id, p.name, p.description, p.cog, p.fmv,
		p.qty, p.variant_name, p.variation_name, p.parentID, IF(p.qty > 0 AND p.active = 1,TRUE,FALSE) AS active, p.img_url,  
		p.download_url, p.price, p.category_id, p.vendor_id, p.vendor_code, p.sort_weight, p.date_created, p.featured, p.created, pv.company AS vendor
		FROM
			" .
                $this->premiums_table .
                " p
			 LEFT JOIN " .
                $this->premium_vendors_table .
                " pv
							 ON p.vendor_id = pv.id
			LEFT JOIN
			" .
                $this->premium_categories_table .
                " c
					ON p.category_id = c.id
			WHERE
				p.name LIKE ? OR p.description LIKE ? OR c.name LIKE ?
			ORDER BY
				p.created DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // sanitize
            $keywords = $this->customSanitize($keywords);
            $keywords = "%{$keywords}%";

            // bind
            $stmt->bindParam(1, $keywords);
            $stmt->bindParam(2, $keywords);
            $stmt->bindParam(3, $keywords);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // upload premium
    public function upload() {
        try {
            global $api_url;

            // Get reference to uploaded image
            $image_file = $_FILES["image"];

            // Exit if no file uploaded
            if (!isset($image_file)) {
                http_response_code(400); // Bad Request
                throw new Exception("No file uploaded");
            }

            // Exit if image file is zero bytes
            if (filesize($image_file["tmp_name"]) <= 0) {
                http_response_code(400); // Bad Request
                throw new Exception("Uploaded file has no contents");
            }

            // Exit if is not a valid image file
            $image_type = exif_imagetype($image_file["tmp_name"]);
            if (!$image_type) {
                http_response_code(400); // Bad Request
                throw new Exception("Uploaded file is not an image");
            }

            // Get file extension based on file type, to prepend a dot we pass true as the second parameter
            //$image_extension = image_type_to_extension($image_type, true);

            // Create a unique image name, or not
            //$image_name = bin2hex(random_bytes(16)) . $image_extension;
            $image_name = $image_file["name"];

            // Move the temp image file to the uploads directory
            if (
                !move_uploaded_file(
                    // Temp image location
                    $image_file["tmp_name"],

                    // New image location
                    dirname(__DIR__, 1) . "/uploads/" . $image_name,
                )
            ) {
                http_response_code(500); // server error
                throw new Exception(
                    "Server could not move image, please ensure uploads directory exists and has correct perms.",
                );
            }

            // Return the url of the Premium.
            echo json_encode([
                "img_url" => "https://$api_url/uploads/$image_name",
                "filesize" => $image_file["size"],
            ]);
        } catch (Exception $e) {
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
}

class Premium_vendor {
    // database connection and table name
    private $conn;
    private $premium_vendors_table = "premium_vendors";

    // object properties
    public $id;
    public $name;
    public $description;
    public $created;

    public function __construct($db) {
        $this->conn = $db;
    }

    // read all premium vendors
    public function readAll() {
        try {
            //select all data
            $query =
                "SELECT
				`id`, `company`, `contact`, `website`, `address`, `city`, `state`, `zip`, `phone`, `email`, `updated`
				FROM
					" .
                $this->premium_vendors_table .
                "
				ORDER BY
					id";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }

    // read premium vendors by id
    public function read($id) {
        try {
            //select all data
            $query =
                "SELECT
                    `id`, `company`, `contact`, `website`, `address`, `city`, `state`, `zip`, `phone`, `email`, `updated`
                    FROM
                        " .
                $this->premium_vendors_table .
                "
                    WHERE
                        id=:id";

            $stmt = $this->conn->prepare($query);
            // bind values
            $stmt->bindParam(":id", $id);

            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // create premium vendors
    public function create() {
        try {
            // insert data
            $query =
                "INSERT INTO
                    " .
                $this->premium_vendors_table .
                "
                    SET
                    company=:company,
                    contact=:contact,
                    website=:website,
                    address=:address,
                    city=:city,
                    state=:state,
                    zip=:zip,
                    phone=:phone,
                    email=:email";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->company = htmlspecialchars(strip_tags($this->company));
            $this->contact = htmlspecialchars(strip_tags($this->contact));
            $this->website = htmlspecialchars(strip_tags($this->website));
            $this->address = htmlspecialchars(strip_tags($this->address));
            $this->city = htmlspecialchars(strip_tags($this->city));
            $this->state = htmlspecialchars(strip_tags($this->state));
            $this->zip = htmlspecialchars(strip_tags($this->zip));
            $this->phone = htmlspecialchars(strip_tags($this->phone));
            $this->email = htmlspecialchars(strip_tags($this->email));

            // bind values
            $stmt->bindParam(":company", $this->company);
            $stmt->bindParam(":contact", $this->contact);
            $stmt->bindParam(":website", $this->website);
            $stmt->bindParam(":address", $this->address);
            $stmt->bindParam(":city", $this->city);
            $stmt->bindParam(":state", $this->state);
            $stmt->bindParam(":zip", $this->zip);
            $stmt->bindParam(":phone", $this->phone);
            $stmt->bindParam(":email", $this->email);

            // execute query
            $stmt->execute();

            // Read the Premium Category and send as JSON
            // FIXME TODO
            // rename zip to postal_code
            // rename address to address1 and address2

            echo json_encode(
                $this->read($this->conn->lastInsertId())->fetch(
                    PDO::FETCH_ASSOC,
                ),
                JSON_NUMERIC_CHECK,
            );

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // update premium vendors
    public function update() {
        try {
            // integrity checks

            // Does it exist?
            if ($this->read($this->id)->rowCount() < 1) {
                http_response_code(400); // Bad Request
                throw new Exception("[Premium Vendor] could not be found.");
            }

            // update query
            $query =
                "UPDATE
                        " .
                $this->premium_vendors_table .
                "
                    SET
                    company=:company,
                    contact=:contact,
                    website=:website,
                    address=:address,
                    city=:city,
                    state=:state,
                    zip=:zip,
                    phone=:phone,
                    email=:email
                    WHERE
                        id = :id";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->company = htmlspecialchars(strip_tags($this->company));
            $this->contact = htmlspecialchars(strip_tags($this->contact));
            $this->website = htmlspecialchars(strip_tags($this->website));
            $this->address = htmlspecialchars(strip_tags($this->address));
            $this->city = htmlspecialchars(strip_tags($this->city));
            $this->state = htmlspecialchars(strip_tags($this->state));
            $this->zip = htmlspecialchars(strip_tags($this->zip));
            $this->phone = htmlspecialchars(strip_tags($this->phone));
            $this->email = htmlspecialchars(strip_tags($this->email));
            $this->id = htmlspecialchars(strip_tags($this->id));

            // bind values
            $stmt->bindParam(":company", $this->company);
            $stmt->bindParam(":contact", $this->contact);
            $stmt->bindParam(":website", $this->website);
            $stmt->bindParam(":address", $this->address);
            $stmt->bindParam(":city", $this->city);
            $stmt->bindParam(":state", $this->state);
            $stmt->bindParam(":zip", $this->zip);
            $stmt->bindParam(":phone", $this->phone);
            $stmt->bindParam(":email", $this->email);
            $stmt->bindParam(":id", $this->id);

            // execute query
            $stmt->execute();

            // Read the Premium Category and send as JSON
            // TODO
            // rename zip to postal_code
            // rename address to address1 and address2

            echo json_encode(
                $this->read($this->id)->fetch(PDO::FETCH_ASSOC),
                JSON_NUMERIC_CHECK,
            );

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }

    // delete premium vendors
    public function delete() {
        try {
            // check if premium category exists before trying to delete
            if (!$this->read($this->id)->fetch(PDO::FETCH_ASSOC)) {
                http_response_code(400); // Bad Request
                throw new Exception("Premium Vendor: $this->id does not exist");
            }
            // check if premium vendor is attached to any premiums before trying to delete
            if (
                (new Premium($this->conn))
                    ->premiumsByVendorID($this->id)
                    ->rowCount() > 0
            ) {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "Premium Vendor is assigned to Premiums. Please edit Premiums and try again.",
                );
            }

            // delete query
            $query =
                "DELETE FROM " . $this->premium_vendors_table . " WHERE id = ?";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->id = htmlspecialchars(strip_tags($this->id));

            // bind id of record to delete
            $stmt->bindParam(1, $this->id);

            // execute query
            if ($stmt->execute()) {
                return true;
            }

            return false;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
}
class Premium_category {
    // database connection and table name
    private $conn;
    private $premium_categories_table = "premium_categories";

    // object properties
    public $id;
    public $name;
    public $description;
    public $created;

    public function __construct($db) {
        $this->conn = $db;
    }

    // read premium Category
    public function read($id) {
        try {
            //select all data
            $query =
                "SELECT
					`id`, `name`, `description`, `created`, `modified`
			FROM
				" .
                $this->premium_categories_table .
                "
			WHERE
				id=:id";

            $stmt = $this->conn->prepare($query);
            // bind values
            $stmt->bindParam(":id", $id);

            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    public function readAll() {
        try {
            //select all data
            $query =
                "SELECT
					`id`, `name`, `description`, `created`, `modified`
			FROM
				" .
                $this->premium_categories_table .
                "
			ORDER BY
				id";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
    // create premium Category
    public function create() {
        try {
            // query to insert record
            $query =
                "INSERT INTO
                " .
                $this->premium_categories_table .
                "
            SET
                name=:name, description=:description";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->name = htmlspecialchars(strip_tags($this->name));
            $this->description = htmlspecialchars(
                strip_tags($this->description),
            );

            // bind values
            $stmt->bindParam(":name", $this->name);
            $stmt->bindParam(":description", $this->description);

            // execute query
            $stmt->execute();

            // Read the Premium Category and send as JSON
            // FIXME TODO this needs to read just one record, not all
            echo json_encode(
                $this->read($this->conn->lastInsertId())->fetch(
                    PDO::FETCH_ASSOC,
                ),
                JSON_NUMERIC_CHECK,
            );

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
    // update premium Category
    public function update() {
        try {
            // integrity checks

            // Does it exist?
            if ($this->read($this->id)->rowCount() < 1) {
                http_response_code(400); // Bad Request
                throw new Exception("[Premium Category] could not be found.");
            }

            // update query
            $query =
                "UPDATE
                " .
                $this->premium_categories_table .
                "
            SET
                name=:name,
                description=:description
            WHERE
                id = :id";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->name = htmlspecialchars(strip_tags($this->name));
            $this->description = htmlspecialchars(
                strip_tags($this->description),
            );

            // bind values
            $stmt->bindParam(":name", $this->name);
            $stmt->bindParam(":description", $this->description);
            $stmt->bindParam(":id", $this->id);

            // execute query
            $stmt->execute();

            // Read the Premium Category and send as JSON
            echo json_encode(
                $this->read($this->id)->fetch(PDO::FETCH_ASSOC),
                JSON_NUMERIC_CHECK,
            );

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
    // delete premium Category
    public function delete() {
        try {
            // check if premium category exists before trying to delete
            if (!$this->read($this->id)->fetch(PDO::FETCH_ASSOC)) {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "Premium Category: $this->id does not exist",
                );
            }
            // check if premium category is attached to any premiums before trying to delete
            if (
                (new Premium($this->conn))
                    ->premiumsByCategoryID($this->id)
                    ->rowCount() > 0
            ) {
                http_response_code(400); // Bad Request
                throw new Exception(
                    "Premium Category is assigned to Premiums. Please edit Premiums and try again.",
                );
            }
            // delete query
            $query =
                "DELETE FROM " .
                $this->premium_categories_table .
                " WHERE id = ?";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->id = htmlspecialchars(strip_tags($this->id));

            // bind id of record to delete
            $stmt->bindParam(1, $this->id);

            // execute query
            if ($stmt->execute()) {
                return true;
            }

            return false;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "message" => $e->getMessage(),
            ]);
        }
    }
}
