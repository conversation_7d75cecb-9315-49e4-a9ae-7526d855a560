<?php
class Receipt
{

    // database connection and table code
    private $conn;
    private $donations_table = "donations";
    private $donors_table = "donors";
    private $campaigns_table = "campaigns";
    private $shipments_table = "shipments";
    private $shipment_addresses_table = "shipment_addresses";
    private $payments_table = "payments";
    private $premiums_table = "premiums";
    private $premium_categories_table = "premium_categories";

    // constructor with $db as database connection
    public function __construct($db)
    {
        $this->conn = $db;
    }
    public function read()
    {
        //read one with premiums

        // query to read single record
        $query = "SELECT
        " . $this->donations_table . ".`id`,
        " . $this->donations_table . ".`donor_id`,
        " . $this->donations_table . ".`timestamp`,
        " . $this->donors_table . ".`firstname`,
        " . $this->donors_table . ".`lastname`,
        " . $this->donors_table . ".`partner_firstname`,
        " . $this->donors_table . ".`partner_lastname`,
        " . $this->donors_table . ".`address1`,
        " . $this->donors_table . ".`address2`,
        " . $this->donors_table . ".`city`,
        " . $this->donors_table . ".`state`,
        " . $this->donors_table . ".`country`,
        " . $this->donors_table . ".`postal_code`,
        " . $this->shipment_addresses_table . ".`firstname` as shipping_firstname,
        " . $this->shipment_addresses_table . ".`lastname` as shipping_lastname,
        " . $this->shipment_addresses_table . ".`address1` as shipping_address1,
        " . $this->shipment_addresses_table . ".`address2` as shipping_address2,
        " . $this->shipment_addresses_table . ".`city` as shipping_city,
        " . $this->shipment_addresses_table . ".`state` as shipping_state,
        " . $this->shipment_addresses_table . ".`country` as shipping_country,
        " . $this->shipment_addresses_table . ".`postal_code` as shipping_postal_code,
        " . $this->donors_table . ".`phone`,
        " . $this->donors_table . ".`email`,
        " . $this->payments_table . ".`method`,
        " . $this->payments_table . ".`brand`,
        LPAD(" . $this->payments_table . ".`last4`, 4, '0') AS last4,
        LPAD(" . $this->payments_table . ".`exp_month`, 2, '0') AS exp_month,
        " . $this->payments_table . ".`amount` as `payment_amount`,
        " . $this->payments_table . ".`amount_refunded` as `amount_refunded`,
        " . $this->payments_table . ".`exp_year`,
        " . $this->payments_table . ".`date_created`,
        " . $this->donations_table . ".`amount`,
        " . $this->donations_table . ".`installment`,
        " . $this->donations_table . ".`comments`,
        " . $this->donations_table . ".`add_me`,
        " . $this->donations_table . ".`read_onair`,
        " . $this->donations_table . ".`transaction_id`,
        " . $this->donations_table . ".`ipaddress`,
        " . $this->donations_table . ".`browser`,
        " . $this->donations_table . ".`show_name`,
        " . $this->donations_table . ".`source`,
        " . $this->donations_table . ".`updated`,
        " . $this->donations_table . ".`donation_match`,
        " . $this->donations_table . ".`campaign_id`
        FROM
            " . $this->donations_table . ",
            " . $this->donors_table . ",
            " . $this->shipments_table . ",
            " . $this->shipment_addresses_table . ",
            " . $this->payments_table . "
        WHERE
        " . $this->donations_table . ".`transaction_id` = ?
        AND " . $this->donations_table . ".`donor_id` = " . $this->donors_table . ".`id`
        AND " . $this->shipments_table . ".`donation_id` = " . $this->donations_table . ".`id`
        AND " . $this->shipment_addresses_table . ".`donation_id` = " . $this->donations_table . ".`id`
        AND " . $this->payments_table . ".`donation_id` = " . $this->donations_table . ".`id`
        LIMIT 0,1";

        // prepare query statement
        $stmt = $this->conn->prepare($query);

        // bind id of pledge to be updated
        $stmt->bindParam(1, $this->transaction_id);

        // execute query
        $stmt->execute();

        // get retrieved row
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        // set values to object properties
        $this->id = $row['id'];
        $this->donor_id = $row['donor_id'];
        $this->timestamp = $row['timestamp'];
        $this->firstname = $row['firstname'];
        $this->lastname = $row['lastname'];
        $this->partner_firstname = $row['partner_firstname'];
        $this->partner_lastname = $row['partner_lastname'];
        $this->address1 = $row['address1'];
        $this->address2 = $row['address2'];
        $this->city = $row['city'];
        $this->state = $row['state'];
        $this->country = $row['country'];
        $this->postal_code = $row['postal_code'];

        if (!empty($row['shipping_firstname'])) {
            $this->shipping_firstname = $row['shipping_firstname'];
        }
        if (!empty($row['shipping_lastname'])) {
            $this->shipping_lastname = $row['shipping_lastname'];
        }
        if (!empty($row['shipping_address1'])) {
            $this->shipping_address1 = $row['shipping_address1'];
        }
        if (!empty($row['shipping_address2'])) {
            $this->shipping_address2 = $row['shipping_address2'];
        }
        if (!empty($row['shipping_city'])) {
            $this->shipping_city = $row['shipping_city'];
        }
        if (!empty($row['shipping_state'])) {
            $this->shipping_state = $row['shipping_state'];
        }
        if (!empty($row['shipping_country'])) {
            $this->shipping_country = $row['shipping_country'];
        }
        if (!empty($row['shipping_postal_code'])) {
            $this->shipping_postal_code = $row['shipping_postal_code'];
        }

        $this->phone = $row['phone'];
        $this->email = $row['email'];
        $this->method = $row['method'];
        $this->brand = $row['brand'];
        $this->last4 = $row['last4'];
        $this->exp_month = $row['exp_month'];
        $this->exp_year = $row['exp_year'];
        $this->amount = $row['amount'];
        $this->date_created = $row['date_created'];
        $this->installment = $row['installment'];
        $this->comments = $row['comments'];
        $this->add_me = $row['add_me'];
        $this->read_onair = $row['read_onair'];
        $this->transaction_id = $row['transaction_id'];
        $this->ipaddress = $row['ipaddress'];
        $this->browser = $row['browser'];
        $this->show_name = $row['show_name'];
        $this->source = $row['source'];
        $this->campaign_id = $row['campaign_id'];
        $this->updated = $row['updated'];
        $this->donation_match = $row['donation_match'];
        if ($row['payment_amount'] >= $row['amount'] && $row['amount_refunded'] == 0) {$this->status = "paid";}
        if ($row['payment_amount'] == 0) {$this->status = "unpaid";}
        if (($row["payment_amount"] > $row['amount']) && ($row["payment_amount"] > 0)) {$this->status = "Partially Paid";}

        // Query2 -> Premiums
        $query2 = "SELECT
        " . $this->premiums_table . ".`name`,
        `price`,
        `cog`,
        `fmv`,
        `quantity` as `qty`,
        `download_url`,
        `img_url`,
        " . $this->premium_categories_table . ".`name` as category
        FROM
        " . $this->donations_table . "
        LEFT JOIN " . $this->shipments_table . " ON `" . $this->donations_table . "`.`id` = " . $this->shipments_table . ".`donation_id`
        LEFT JOIN " . $this->premiums_table . " ON `" . $this->shipments_table . "`.`premium_id` = " . $this->premiums_table . ".`id`
        LEFT JOIN " . $this->premium_categories_table . " ON " . $this->premiums_table . ".`category_id` = " . $this->premium_categories_table . ".`id`
        WHERE
            `" . $this->donations_table . "`.`transaction_id` = '" . $this->transaction_id . "'";

        // prepare query statement
        $stmt2 = $this->conn->prepare($query2);

        // initialize an array for the results
        $premiums = array();
        if ($stmt2->execute()) {
            while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                $premiums[] = $row2;
            }
        }
        $this->premiums = $premiums;

        // Query3 -> Payments
        // TODO
    }
    // read donation by transaction_id
    public function readDonationByTransactionID($keyword)
    {
        // This function reads a single donation by id.

        // select all query
        $donations_select_query = "SELECT
            " . $this->donations_table . ".`id` as `donation_id`,
            " . $this->donations_table . ".`account_id`,
            " . $this->donations_table . ".`donor_id`,
            " . $this->donations_table . ".`timestamp`,
            " . $this->donors_table . ".`firstname`,
            " . $this->donors_table . ".`lastname`,
            " . $this->donors_table . ".`partner_firstname`,
            " . $this->donors_table . ".`partner_lastname`,
            " . $this->donors_table . ".`address1`,
            " . $this->donors_table . ".`address2`,
            " . $this->donors_table . ".`city`,
            " . $this->donors_table . ".`state`,
            " . $this->donors_table . ".`country`,
            " . $this->donors_table . ".`postal_code`,
            " . $this->donors_table . ".`phone`,
            " . $this->donors_table . ".`email`,
            " . $this->shipment_addresses_table . ".`firstname` as shipping_firstname,
            " . $this->shipment_addresses_table . ".`lastname` as shipping_lastname,
            " . $this->shipment_addresses_table . ".`address1` as shipping_address1,
            " . $this->shipment_addresses_table . ".`address2` as shipping_address2,
            " . $this->shipment_addresses_table . ".`city` as shipping_city,
            " . $this->shipment_addresses_table . ".`state` as shipping_state,
            " . $this->shipment_addresses_table . ".`country` as shipping_country,
            " . $this->shipment_addresses_table . ".`postal_code` as shipping_postal_code,
            " . $this->donations_table . ".`type`,
            " . $this->donations_table . ".`amount` as `donation_amount`,
            " . $this->donations_table . ".`installment`,
            " . $this->donations_table . ".`comments`,
            " . $this->donations_table . ".`add_me`,
            " . $this->donations_table . ".`read_onair`,
            " . $this->donations_table . ".`transaction_id`,
            " . $this->donations_table . ".`ipaddress`,
            " . $this->donations_table . ".`browser`,
            " . $this->donations_table . ".`show_name`,
            " . $this->donations_table . ".`source`,
            " . $this->donations_table . ".`campaign_id`,
            " . $this->campaigns_table . ".`name` AS `campaign`,
            " . $this->donations_table . ".`updated`,
            " . $this->donations_table . ".`donation_match`,
            " . $this->payments_table . ".`amount` as `payment_amount`,
            " . $this->payments_table . ".`amount_refunded`
            FROM
            " . $this->donations_table . "
            LEFT JOIN " . $this->donors_table . " ON " . $this->donations_table . ".`donor_id` = " . $this->donors_table . ".`id`
            LEFT JOIN " . $this->campaigns_table . " ON " . $this->donations_table . ".`campaign_id` = " . $this->campaigns_table . ".`id`
            LEFT JOIN " . $this->payments_table . " ON " . $this->donations_table . ".`id` = " . $this->payments_table . ".`donation_id`
            LEFT JOIN " . $this->shipments_table . " ON " . $this->donations_table . ".`id` = " . $this->shipments_table . ".`donation_id`
            LEFT JOIN " . $this->shipment_addresses_table . " ON " . $this->donations_table . ".`id` = " . $this->shipment_addresses_table . ".`donation_id`
            WHERE " . $this->donations_table . ".`transaction_id` = ?
            ";

        // prepare query statement
        $stmt = $this->conn->prepare($donations_select_query);

        // sanitize
        $keyword = urldecode(htmlspecialchars(strip_tags($keyword)));

        $id = "{$keyword}";

        // bind
        $stmt->bindParam(1, $id);

        // execute query
        $stmt->execute();

        return $stmt;
    }
    // read Gifts by id
    public function readGifts($id)
    {
        // query to read gifts table for single transaction_id
        $premium_select_query = "SELECT
	" . $this->premiums_table . ".`id` as `premium_id`,
	" . $this->premiums_table . ".`name`,
	" . $this->premiums_table . ".`price`,
	" . $this->premiums_table . ".`cog`,
	" . $this->premiums_table . ".`fmv`,
	" . $this->shipments_table . ".`quantity` as `qty`,
	" . $this->premiums_table . ".`download_url`,
	" . $this->premiums_table . ".`img_url`,
	" . $this->premium_categories_table . ".`name` as `category`,
	" . $this->shipments_table . ".`status` as shipment_status
        FROM
            " . $this->shipments_table . "
        LEFT JOIN " . $this->premiums_table . " ON `" . $this->shipments_table . "`.`premium_id` = " . $this->premiums_table . ".`id`
        LEFT JOIN " . $this->premium_categories_table . " ON " . $this->premiums_table . ".`category_id` = " . $this->premium_categories_table . ".`id`
        WHERE
            `" . $this->shipments_table . "`.`donation_id` = ?";

        // prepare query statement
        $premium_select_stmt = $this->conn->prepare($premium_select_query);

        // bind id of premium to be found
        $premium_select_stmt->bindParam(1, $id);

        // execute query
        $premium_select_stmt->execute();

        return $premium_select_stmt;
    }
    // read Payments
    public function readPayments($donation_id)
    {
        // query to read payments table for single donation_id
        $payment_select_query = "
        SELECT
            " . $this->payments_table . ".`id`,
            " . $this->payments_table . ".`method`,
            " . $this->payments_table . ".`processor`,
            " . $this->payments_table . ".`card_type`,
            " . $this->payments_table . ".payment_id,
            " . $this->payments_table . ".amount,
            " . $this->payments_table . ".date_created,
            " . $this->payments_table . ".amount_refunded,
            " . $this->payments_table . ".status AS payment_status,
            " . $this->payments_table . ".processor,
            " . $this->payments_table . ".brand,
            LPAD(" . $this->payments_table . ".`last4`, 4, '0') AS `last4`,
            LPAD(" . $this->payments_table . ".`exp_month`, 2, '0') AS `exp_month`,
            " . $this->payments_table . ".exp_year,
            " . $this->payments_table . ".`date_created`,
            " . $this->payments_table . ".`date_updated`,
            " . $this->payments_table . ".`date_deposited`
        FROM
            " . $this->payments_table . "

        WHERE
            `" . $this->payments_table . "`.`donation_id` = ?
        ORDER BY `" . $this->payments_table . "`.`date_created` ASC
            ";

        // prepare query statement
        $payment_select_stmt = $this->conn->prepare($payment_select_query);

        // bind id of premium to be found
        $payment_select_stmt->bindParam(1, $donation_id);

        // execute query
        $payment_select_stmt->execute();

        return $payment_select_stmt;
    }
}
