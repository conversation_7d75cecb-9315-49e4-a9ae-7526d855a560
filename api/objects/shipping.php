<?php
// Provides class for Shipping
class Shipping {
    // database connection and table code
    private $conn;
    private $shipments_table = "shipments";
    private $shipment_addresses_table = "shipment_addresses";
    private $donors_table = "donors";
    private $premiums_table = "premiums";
    private $premium_categories_table = "premium_categories";
    private $premium_vendors_table = "premium_vendors";
    private $donations_table = "donations";
    private $payments_table = "payments";

    // object properties
    public $shipment_id;
    public $transaction_id;
    public $code;
    public $timestamp;
    public $quantity;
    public $shipping_stauts;
    public $tracking_number;
    public $company;
    public $premium_name;

    // constructor with $db as database connection
    public function __construct($db) {
        $this->conn = $db;
    }

    // creates a shipment
    public function create(
        string $donation_source,
        string $donation_installment,
    ) {
        try {
            // [Premium] UPDATE QTY (reduce)
            $update_query =
                "UPDATE
                    " .
                $this->premiums_table .
                "
                        SET
                        `qty` = `qty` - 1
                        WHERE
                        id=:id
                        AND `qty` > 0
                        ";

            // [Shipment] INSERT shipping record
            $insert_query =
                "INSERT INTO
                        " .
                $this->shipments_table .
                "
                        SET
                        donation_id=:donation_id,
                        premium_id=:premium_id,
                        quantity=:quantity";

            // Prepare queries
            $insert_stmt = $this->conn->prepare($insert_query);
            $update_stmt = $this->conn->prepare($update_query);

            // bind values
            $update_stmt->bindParam(":id", $this->premium_id, PDO::PARAM_INT);
            $insert_stmt->bindParam(
                ":donation_id",
                $this->donation_id,
                PDO::PARAM_INT,
            );
            $insert_stmt->bindParam(
                ":premium_id",
                $this->premium_id,
                PDO::PARAM_INT,
            );
            $insert_stmt->bindParam(
                ":quantity",
                $this->quantity,
                PDO::PARAM_INT,
            );

            if (!$insert_stmt->execute()) {
                // Execute database insert_query
                http_response_code(400); // Bad Request
                throw new Exception(
                    "Your donation premiums could not be entered into the database, please contact the station with the error information: \"" .
                        print_r($insert_stmt->errorInfo()[2], true) .
                        "\"",
                );
            }
            //do not reduce premium quantity for donations from the public donation app, as they may be abandoned.
            //we will instead reduce the quantity once a payment is made
            if (
                $donation_source !== "WebSite" &&
                $donation_installment === "One-Time"
            ) {
                if (!$update_stmt->execute()) {
                    // Execute database update_query
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "Your donation could not be processed. Please select a different Thank-You Gift.",
                    );
                }
            }

            // return ID from database
            return $this->conn->lastInsertId();

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" =>
                    $update_stmt->errorInfo()[2] . $insert_stmt->errorInfo()[2],
            ]);
        } catch (Exception $e) {
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
    // counts number of shipments between dates by status
    public function readCount($startdate, $enddate, $status) {
        # Totals
        try {
            // This function searches the shipping table (between dates) and returns the result(s)
            $query =
                "SELECT COUNT(DISTINCT " .
                $this->shipments_table .
                ".id) as `count`, " .
                $this->shipments_table .
                ".status, " .
                $this->premiums_table .
                ".name as 'name'," .
                $this->premiums_table .
                ".vendor_code as 'vendor_code', " .
                $this->premiums_table .
                ".id as premium_id, premium_categories.name as category_name, company, vendor_code
     FROM " .
                $this->shipments_table .
                "
        LEFT JOIN " .
                $this->premiums_table .
                " ON " .
                $this->shipments_table .
                ".`premium_id` = " .
                $this->premiums_table .
                ".`id`
        LEFT JOIN " .
                $this->premium_categories_table .
                " ON " .
                $this->premiums_table .
                ".`category_id` = `premium_categories`.`id`
        LEFT JOIN " .
                $this->premium_vendors_table .
                " ON " .
                $this->premiums_table .
                ".`vendor_id` = `premium_vendors`.`id`
        LEFT JOIN " .
                $this->donations_table .
                " ON " .
                $this->shipments_table .
                ".donation_id = " .
                $this->donations_table .
                ".`id`
        LEFT JOIN " .
                $this->payments_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->payments_table .
                ".`donation_id`
        WHERE " .
                $this->shipments_table .
                ".TIMESTAMP BETWEEN CAST(? AS DATETIME) AND CAST(? AS DATETIME) AND `" .
                $this->shipments_table .
                "`.`status` = ?
                AND " .
                $this->premiums_table .
                ".name IS not NULL
                AND " .
                $this->payments_table .
                ".`amount` >= " .
                $this->donations_table .
                ".amount
                AND " .
                $this->payments_table .
                ".`amount_refunded` = 0
        GROUP BY " .
                $this->premiums_table .
                ".name
        ORDER BY `count` DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $startdate);
            $stmt->bindParam(2, $enddate);
            $stmt->bindParam(3, $status);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // returns shipments between dates by status, by premium_id
    public function readShipment($startdate, $enddate, $premium_id, $status) {
        try {
            // This function searches the shipments table (between dates) and returns the result(s)
            $query =
                "SELECT DISTINCT
        " .
                $this->donations_table .
                ".id AS `donation_id`,
        " .
                $this->donors_table .
                ".firstname AS 'firstname',
        " .
                $this->donors_table .
                ".lastname AS 'lastname',
        " .
                $this->donors_table .
                ".address1 AS 'address1',
        " .
                $this->donors_table .
                ".address2 AS 'address2',
        " .
                $this->donors_table .
                ".city AS 'city',
        " .
                $this->donors_table .
                ".state AS 'state',
        " .
                $this->donors_table .
                ".country AS 'country',
        " .
                $this->donors_table .
                ".postal_code AS 'postal_code',
        " .
                $this->shipment_addresses_table .
                ".firstname AS 'shipping_firstname',
        " .
                $this->shipment_addresses_table .
                ".lastname AS 'shipping_lastname',
        " .
                $this->shipment_addresses_table .
                ".address1 AS 'shipping_address1',
        " .
                $this->shipment_addresses_table .
                ".address2 AS 'shipping_address2',
        " .
                $this->shipment_addresses_table .
                ".city AS 'shipping_city',
        " .
                $this->shipment_addresses_table .
                ".state AS 'shipping_state',
        " .
                $this->shipment_addresses_table .
                ".country AS 'shipping_country',
        " .
                $this->shipment_addresses_table .
                ".postal_code AS 'shipping_postal_code',
        " .
                $this->donors_table .
                ".email,
        " .
                $this->donors_table .
                ".phone,
        " .
                $this->premiums_table .
                ".id as premium_id,
        " .
                $this->shipments_table .
                ".tracking_number,
        " .
                $this->shipments_table .
                ".ship_date,
        " .
                $this->shipments_table .
                ".id as `shipment_id`

            FROM	" .
                $this->donations_table .
                "
                LEFT JOIN " .
                $this->shipments_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->shipments_table .
                ".`donation_id`
                LEFT JOIN " .
                $this->shipment_addresses_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->shipment_addresses_table .
                ".`donation_id`
                LEFT JOIN " .
                $this->premiums_table .
                " ON " .
                $this->shipments_table .
                ".`premium_id` = " .
                $this->premiums_table .
                ".`id`
                LEFT JOIN " .
                $this->donors_table .
                " ON " .
                $this->donations_table .
                ".`donor_id` = " .
                $this->donors_table .
                ".`id`
                LEFT JOIN " .
                $this->payments_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->payments_table .
                ".`donation_id`
                WHERE " .
                $this->shipments_table .
                ".`timestamp` BETWEEN CAST(? AS DATE) AND CAST(? AS DATE)
                        AND " .
                $this->shipments_table .
                ".premium_id = ?
                        AND " .
                $this->shipments_table .
                ".`status` = ?
                        AND " .
                $this->payments_table .
                ".`amount` >= " .
                $this->donations_table .
                ".amount
                        AND " .
                $this->payments_table .
                ".`amount_refunded` = 0;
                ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $startdate);
            $stmt->bindParam(2, $enddate);
            $stmt->bindParam(3, $premium_id);
            $stmt->bindParam(4, $status);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
            $stmt->closeCursor(); // frees memory
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // update the shipping
    public function update($shipment_id, $status) {
        try {
            // update query
            $query =
                "UPDATE
                " .
                $this->shipments_table .
                "
            SET
                " .
                $this->shipments_table .
                ".status=:shipment_status,
                " .
                $this->shipments_table .
                ".updated = NOW()
            WHERE
            	id=:shipment_id";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(":shipment_id", $shipment_id);
            $stmt->bindParam(":shipment_status", $status);

            // send error if issue with update
            if ($stmt->execute()) {
                $rowcount = print_r($stmt->rowCount(), true); // Row Count
                if ($rowcount > 0) {
                    //TODO email updates to donors
                    return [$shipment_id, true, $status];
                }
                throw new Exception("Row not updated");
            } else {
                throw new Exception($stmt->errorInfo());
            }
        } catch (Exception $e) {
            $info = $e->getMessage();
            return [$shipment_id, false, $info];
        }
    }

    // read shipments by shipping id
    public function read($shipment_id) {
        try {
            // This function reads shipments table for a single shipment_id and returns the result(s)
            $query =
                "SELECT DISTINCT COUNT(" .
                $this->shipments_table .
                ".id) as `count`, " .
                $this->shipments_table .
                ".status, " .
                $this->premiums_table .
                ".name as 'name'," .
                $this->premiums_table .
                ".vendor_code as 'vendor_code', " .
                $this->premiums_table .
                ".id as premium_id, premium_categories.name as category_name, company, vendor_code,
        " .
                $this->donations_table .
                ".id AS `donation_id`,
        " .
                $this->shipment_addresses_table .
                ".firstname AS 'shipping_firstname',
        " .
                $this->shipment_addresses_table .
                ".lastname AS 'shipping_lastname',
        " .
                $this->shipment_addresses_table .
                ".address1 AS 'shipping_address1',
        " .
                $this->shipment_addresses_table .
                ".address2 AS 'shipping_address2',
        " .
                $this->shipment_addresses_table .
                ".city AS 'shipping_city',
        " .
                $this->shipment_addresses_table .
                ".state AS 'shipping_state',
        " .
                $this->shipment_addresses_table .
                ".country AS 'shipping_country',
        " .
                $this->shipment_addresses_table .
                ".postal_code AS 'shipping_postal_code',
        " .
                $this->donors_table .
                ".email,
        " .
                $this->donors_table .
                ".phone,
        " .
                $this->premiums_table .
                ".id as premium_id,
        " .
                $this->shipments_table .
                ".tracking_number,
        " .
                $this->shipments_table .
                ".ship_date,
        " .
                $this->shipments_table .
                ".id as `shipment_id`

            FROM	" .
                $this->donations_table .
                "
                LEFT JOIN " .
                $this->shipments_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->shipments_table .
                ".`donation_id`
                LEFT JOIN " .
                $this->shipment_addresses_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->shipment_addresses_table .
                ".`donation_id`
                LEFT JOIN " .
                $this->premiums_table .
                " ON " .
                $this->shipments_table .
                ".`premium_id` = " .
                $this->premiums_table .
                ".`id`
                LEFT JOIN " .
                $this->premium_vendors_table .
                " ON " .
                $this->premiums_table .
                ".`vendor_id` = `premium_vendors`.`id`
                LEFT JOIN " .
                $this->donors_table .
                " ON " .
                $this->donations_table .
                ".`donor_id` = " .
                $this->donors_table .
                ".`id`
                LEFT JOIN " .
                $this->payments_table .
                " ON " .
                $this->donations_table .
                ".`id` = " .
                $this->payments_table .
                ".`donation_id`
                LEFT JOIN " .
                $this->premium_categories_table .
                " ON " .
                $this->premiums_table .
                ".`category_id` = " .
                $this->premium_categories_table .
                ".`id`
                WHERE " .
                $this->shipments_table .
                ".id = ?
                ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $shipment_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // delete the shipping
    public function delete() {
        try {
            // delete query
            $query = "DELETE FROM " . $this->shipments_table . " WHERE id = ?";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // bind id of record to delete
            $stmt->bindParam(1, $this->id);

            // execute query
            if ($stmt->execute()) {
                return true;
            }

            return false;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }

    // read by PremiumID
    public function readbyPremiumID($id) {
        try {
            $query =
                "SELECT
                        `id`
                      FROM
                        " .
                $this->shipments_table .
                "
                      WHERE
                        `premium_id` = ?";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind variable values
            $stmt->bindParam(1, $id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
}
class Shipping_Address {
    // database connection and table code
    private $conn;
    private $shipment_addresses_table = "shipment_addresses";

    // constructor with $db as database connection
    public function __construct($db) {
        $this->conn = $db;
    }

    // creates a shipment_address
    public function create() {
        try {
            $query =
                "INSERT INTO
                    " .
                $this->shipment_addresses_table .
                "
                SET
                    firstname=:firstname,
                    lastname=:lastname,
                    address1=:address1,
                    address2=:address2,
                    city=:city,
                    state=:state,
                    country=:country,
                    postal_code=:postal_code,
                    donation_id=:donation_id";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // Bind SHIPPING Address values
            $stmt->bindParam(":firstname", $this->firstname);
            $stmt->bindParam(":lastname", $this->lastname);
            $stmt->bindParam(":address1", $this->address1);
            $stmt->bindParam(":address2", $this->address2);
            $stmt->bindParam(":city", $this->city);
            $stmt->bindParam(":state", $this->state);
            $stmt->bindParam(":country", $this->country);
            $stmt->bindParam(":postal_code", $this->postal_code);
            $stmt->bindParam(":donation_id", $this->donation_id);

            // execute query
            $stmt->execute();

            // return ID from database
            return $this->conn->lastInsertId();

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ]);
        } catch (Exception $e) {
            echo json_encode([
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ]);
        }
    }
}
