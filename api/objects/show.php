<?php
class Show
{

    // database connection and table name
    private $conn;
    private $table_name = "shows";

    // object properties
    public $id;
    public $name;
    public $hosts;
    public $description;
    public $starts;
    public $ends;
    public $duration;
    public $days;
    public $weeks;
    public $dad_id;
    public $type;
    public $active;

    // constructor with $db as database connection
    public function __construct($db)
    {
        $this->conn = $db;
    }
// read shows
    public function readShows()
    {
        try {

            // select all query
            $query = "SELECT
				id, name, hosts, description, img_url, starts, ends, duration, days, weeks, dad_id, type, active
			FROM
				" . $this->table_name . "
			ORDER BY
				name ASC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
// create show
    public function create()
    {
        try {
            // query to insert record
            $query = "INSERT INTO
				" . $this->table_name . "
			SET
				id=:id, name=:name, hosts=:hosts, description=:description, img_url=:img_url, starts=:starts, ends=:ends, duration=:duration, days=:days, weeks=:weeks, dad_id=:dad_id, type=:type, active=:active";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->id = htmlspecialchars(strip_tags($this->id));
            $this->name = htmlspecialchars(strip_tags($this->name));
            $this->hosts = htmlspecialchars(strip_tags($this->hosts));
            $this->description = htmlspecialchars(strip_tags($this->description));
            $this->img_url = htmlspecialchars(strip_tags($this->img_url));
            $this->starts = htmlspecialchars(strip_tags($this->starts));
            $this->ends = htmlspecialchars(strip_tags($this->ends));
            $this->duration = htmlspecialchars(strip_tags($this->duration));
            $this->days = htmlspecialchars(strip_tags($this->days));
            $this->weeks = htmlspecialchars(strip_tags($this->weeks));
            $this->dad_id = htmlspecialchars(strip_tags($this->dad_id));
            $this->type = htmlspecialchars(strip_tags($this->type));
            $this->activename = htmlspecialchars(strip_tags($this->active));

            // bind values
            $stmt->bindParam(":id", $this->id);
            $stmt->bindParam(":name", $this->name);
            $stmt->bindParam(":hosts", $this->hosts);
            $stmt->bindParam(":description", $this->description);
            $stmt->bindParam(":img_url", $this->img_url);
            $stmt->bindParam(":starts", $this->starts);
            $stmt->bindParam(":ends", $this->ends);
            $stmt->bindParam(":duration", $this->duration);
            $stmt->bindParam(":days", $this->days);
            $stmt->bindParam(":weeks", $this->weeks);
            $stmt->bindParam(":dad_id", $this->dad_id);
            $stmt->bindParam(":type", $this->type);
            $stmt->bindParam(":active", $this->active);

            // execute query
            if ($stmt->execute()) {
                return true;
            }

            return false;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }

    }
// used when filling up the update show form
    public function readOne()
    {
        try {
            // query to read single record
            $query = "SELECT
				id, name, hosts, description, img_url, starts, ends, duration, days, weeks, dad_id, type, active
			FROM
				" . $this->table_name . "
			WHERE
				id = ?
			LIMIT
				0,1";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of show to be updated
            $stmt->bindParam(1, $this->id);

            // execute query
            $stmt->execute();

            // get retrieved row
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            // set values to object properties
            $this->id = $row['id'];
            $this->name = $row['name'];
            $this->hosts = $row['hosts'];
            $this->description = $row['description'];
            $this->img_url = $row['img_url'];
            $this->starts = $row['starts'];
            $this->ends = $row['ends'];
            $this->duration = $row['duration'];
            $this->days = $row['days'];
            $this->weeks = $row['weeks'];
            $this->dad_id = $row['dad_id'];
            $this->type = $row['type'];
            $this->active = $row['active'];
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }

// Now Playing
    public function readCurrent()
    {
        try {
            // query to read single record
            //TODO: Preemptions, make show not active?

            $time = date("H:i:s"); // current time
            $dow = date("l"); // current Day of Week
            $dow_ystd = date("l", time() - 60 * 60 * 24); // day of Week of yesterday
            $week = date("W"); // current week
            $query = "SELECT
				id, name, hosts, description, img_url, starts, ends, duration, days, weeks, dad_id, type, active
			FROM
				" . $this->table_name . "
			WHERE  ( ( ( ends > starts )
  				   AND ( FIND_IN_SET('$dow', days) )
  				   AND ( starts <= '$time' )
				   AND ( ends >= '$time' ) )
  				 OR ( ( ends <= starts )
  				      AND ( FIND_IN_SET('$dow_ystd', days) )
  				      AND ( ends >= '$time' ) )
#  				 OR ( ( ends <= starts )
#  				      AND ( FIND_IN_SET('$dow', days) )
#  				      AND ( ends <= '$time' ) )
)
			       AND FIND_IN_SET('$week', weeks)
			       AND active = '1'
			LIMIT  0, 1";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            if ($stmt->execute()) {
                return $stmt;

            }

            return false;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
// update the show
    public function update()
    {
        try {
            // update query
            $query = "UPDATE
				" . $this->table_name . "
			SET
				id=:id,
				name=:name,
				hosts=:hosts,
				description=:description,
				img_url=:img_url,
				starts=:starts,
				ends=:ends,
				duration=:duration,
				days=:days,
				weeks=:weeks,
				dad_id=:dad_id,
				type=:type,
				active=:active
			WHERE
				id = :id";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->id = htmlspecialchars(strip_tags($this->id));
            $this->name = htmlspecialchars(strip_tags($this->name));
            $this->hosts = htmlspecialchars(strip_tags($this->hosts));
            $this->description = htmlspecialchars(strip_tags($this->description));
            $this->starts = htmlspecialchars(strip_tags($this->starts));
            $this->ends = htmlspecialchars(strip_tags($this->ends));
            $this->duration = htmlspecialchars(strip_tags($this->duration));
            $this->days = htmlspecialchars(strip_tags($this->days));
            $this->weeks = htmlspecialchars(strip_tags($this->weeks));
            $this->dad_id = htmlspecialchars(strip_tags($this->dad_id));
            $this->type = htmlspecialchars(strip_tags($this->type));
            $this->activename = htmlspecialchars(strip_tags($this->active));

            // bind new values
            $stmt->bindParam(":id", $this->id);
            $stmt->bindParam(":name", $this->name);
            $stmt->bindParam(":hosts", $this->hosts);
            $stmt->bindParam(":starts", $this->starts);
            $stmt->bindParam(":ends", $this->ends);
            $stmt->bindParam(":duration", $this->duration);
            $stmt->bindParam(":days", $this->days);
            $stmt->bindParam(":weeks", $this->weeks);
            $stmt->bindParam(":dad_id", $this->dad_id, PDO::PARAM_INT);
            $stmt->bindParam(":type", $this->type);
            $stmt->bindParam(":active", $this->active);

            // execute the query
            if ($stmt->execute()) {
                return true;
            }

            return false;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
// delete the show
    public function delete()
    {
        try {
            // delete query
            $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->id = htmlspecialchars(strip_tags($this->id));

            // bind id of record to delete
            $stmt->bindParam(1, $this->id);

            // execute query
            if ($stmt->execute()) {
                return true;
            }

            return false;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
// search shows
    public function searchShows($keywords)
    {
        try {
            // select all query
            $query = "SELECT
				id, name, hosts, description, img_url, starts, ends, duration, days, weeks, dad_id, type, active
			FROM
				" . $this->table_name . "

			WHERE
				name LIKE ? OR description LIKE ? OR hosts LIKE ?
			ORDER BY
				name DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // sanitize
            $keywords = htmlspecialchars(strip_tags($keywords));
            $keywords = "%{$keywords}%";

            // bind
            $stmt->bindParam(1, $keywords);
            $stmt->bindParam(2, $keywords);
            $stmt->bindParam(3, $keywords);

            // execute query
            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
// Search by dateTime
    public function searchDateTime($datetime)
    {
        try {
            // query to read single record
            //TODO: Preemptions, make show not active?

            $time = date("H:i:s", strtotime($datetime)); // current time of date
            $dow = date("l", strtotime($datetime)); // current Day of Week of date
            $dow_ystd = date("l", strtotime($datetime) - 60 * 60 * 24); // day of Week of yesterday of date
            $week = date("W", strtotime($datetime)); // current week
            $query = "SELECT
				id, name, hosts, description, starts, ends, duration, days, weeks, dad_id, type, active,img_url
			FROM
				" . $this->table_name . "
			WHERE  ( ( ( ends > starts )
  				   AND ( FIND_IN_SET('$dow', days) )
  				   AND ( starts <= '$time' )
				   AND ( ends >= '$time' ) )
  				 OR ( ( ends <= starts )
  				      AND ( FIND_IN_SET('$dow_ystd', days) )
  				      AND ( ends > '$time' ) ) )
			       AND FIND_IN_SET('$week', weeks)
			       AND active = '1'
			LIMIT  0, 1";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            $stmt->execute();

            // execute the query
            if ($stmt->execute()) {
                return $stmt;
            }

            return false;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
    // Search by Date
    public function searchDate($date)
    {
        try {
            // query to read records by date
            //TODO: Preemptions, make show not active?

            $dow = date("l", strtotime($date)); // current Day of Week of date
            $dow_ystd = date("l", strtotime($date) - 60 * 60 * 24); // day of Week of yesterday of date
            $week = date("W", strtotime($date)); // current week
            $query = "SELECT
				id, name, hosts, description, starts, ends, duration, days, weeks, dad_id, type, active, img_url
			FROM
				" . $this->table_name . "
			WHERE  ( FIND_IN_SET('$dow', days))
			       AND FIND_IN_SET('$week', weeks)
			       AND active = '1'
			";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            $stmt->execute();

            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $e->getMessage(),
            ));
        }
    }
}
