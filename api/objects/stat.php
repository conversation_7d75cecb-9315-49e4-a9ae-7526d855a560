<?php
class Stat
{

    // database connection and table name
    private $conn;
    private $stream_log_table = "stream_log";
    private $icecast_log_table = "icecast_log";
    private $caller_log_table = "caller_log";
    private $download_log_table = "download_log";
    private $donors_table = "donors";

    // constructor with $db as database connection
    public function __construct($db)
    {
        $this->conn = $db;
    }
    // create streamers
    public function createStreamers()
    {
        try {
            // query to insert record
            $query = "INSERT INTO
                    " . $this->stream_log_table . "
                SET
                    stream=:stream, streamers=:streamers, `show`=:show, episode=:episode, post_id=:post_id";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->stream = htmlspecialchars(strip_tags($this->stream));
            $this->streamers = intval(strip_tags($this->streamers));
            $this->show = strip_tags($this->show);
            $this->episode = strip_tags($this->episode);
            $this->post_id = intval($this->post_id);

            // bind values
            $stmt->bindParam(":stream", $this->stream);
            $stmt->bindParam(":streamers", $this->streamers);
            $stmt->bindParam(":show", $this->show);
            $stmt->bindParam(":episode", $this->episode);
            $stmt->bindParam(":post_id", $this->post_id);

            // execute query
            if ($stmt->execute()) {
                return true;
            }
            return false;
        } catch (Exception $e) {
            echo json_encode(array("message" => $e->getMessage()));
            die;
        }
    }
    // read streamer
    public function readStreamer()
    {
        try {
            // select all query
            $query = "SELECT
                `timestamp`, `stream`, `streamers`, `show`, `episode`, `post_id`
            FROM
                " . $this->stream_log_table . "
            ORDER BY
                `timestamp` DESC LIMIT 0, 1";
            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            if ($stmt->execute()) {
                return $stmt;
            }
        } catch (PDOException $e) {
            http_response_code(500); // http failure
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        } catch (Exception $e) {
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        }
    }

    // read streamers
    public function readStreamers()
    {
        try {
            // select all query
            $query = "SELECT
                `timestamp`, `stream`, `streamers`, `show`, `episode`, `post_id`
            FROM
                " . $this->stream_log_table . "
            ORDER BY
                `timestamp` DESC
            LIMIT 0, 100";
            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // execute query
            if ($stmt->execute()) {
                return $stmt;
            }
        } catch (PDOException $e) {
            http_response_code(500); // http failure
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        } catch (Exception $e) {
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        }
    }

    // search streamers
    public function searchStreamers($keywords)
    {
        try {
            // select all query
            $query = "SELECT DISTINCT
                    `timestamp`, `stream`, streamers, `show`, `episode`, `post_id`
                FROM
                    " . $this->stream_log_table . "
                WHERE
                    `show` LIKE ?
                ORDER BY
                    `name` DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // sanitize
            $keywords = htmlspecialchars(strip_tags($keywords));
            $keywords = "%{$keywords}%";

            // bind
            $stmt->bindParam(1, $keywords);

            // execute query
            $stmt->execute();

            return $stmt;
        } catch (PDOException $e) {
            http_response_code(500); // http failure
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        } catch (Exception $e) {
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        }
    }

    // create icecast stats
    public function createIcecastStats_join()
    {
        try {
            // query to insert record
            $query = "INSERT INTO
				" . $this->icecast_log_table . "
			SET
                icecast_id=:icecast_id,
				ip=:ip,
                mount=:mount,
                agent=:agent,
                referer=:referer,
                server=:server,
                port=:port,
                datetime_start=NOW()";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // bind values
            $stmt->bindParam(":icecast_id", $this->icecast_id);
            $stmt->bindParam(":ip", $this->ip);
            $stmt->bindParam(":mount", $this->mount);
            $stmt->bindParam(":agent", $this->agent);
            $stmt->bindParam(":referer", $this->referer);
            $stmt->bindParam(":server", $this->server);
            $stmt->bindParam(":port", $this->port);

            // execute query
            if ($stmt->execute()) {
                return true;
            }
            return false;
        } catch (PDOException $e) {
            http_response_code(500); // http failure
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        } catch (Exception $e) {
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        }
    }

    public function createIcecastStats_leave()
    {
        try {
            // query to insert record
            $query = "UPDATE
				" . $this->icecast_log_table . "
			SET
                duration=:duration,
                sent_bytes=:sent_bytes,
                datetime_end=NOW()
            WHERE icecast_id =:icecast_id
            ORDER BY id
            DESC LIMIT 1;
                ";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // bind values
            $stmt->bindParam(":duration", $this->duration);
            $stmt->bindParam(":sent_bytes", $this->sent_bytes);
            $stmt->bindParam(":icecast_id", $this->icecast_id);

            // execute query
            if ($stmt->execute()) {
                return true;
            }
            return false;
        } catch (PDOException $e) {
            http_response_code(500); // http failure
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        } catch (Exception $e) {
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        }
    }

    // create caller stats
    // this function gets called upon conclusion of call from FreePBX
    public function createCaller($number, $duration)
    {
        try {
            // lookup caller by number
            $phone_select_query = "SELECT
            id as `donor_id`
            FROM
                " . $this->donors_table . "
            WHERE
                `phone`=:number";

            // query to insert record
            $caller_insert_query = "INSERT INTO
                " . $this->caller_log_table . "
            SET
                donor_id=:donor_id,
                number=:number,
                duration=:duration";
            // prepare queries
            $phone_select_stmt = $this->conn->prepare($phone_select_query);
            $caller_stmt = $this->conn->prepare($caller_insert_query);

            // sanitize queries
            $number = htmlspecialchars(strip_tags($number));
            $duration = htmlspecialchars(strip_tags($duration));

            // bind values
            $phone_select_stmt->bindParam(":number", $number);

            // execute donor_id lookup
            $phone_select_stmt->execute();

            if ($phone_select_stmt->rowCount() > 0) {
                $donor_id = $phone_select_stmt->fetch()["donor_id"]; //found donor_id

                // bind values
                $caller_stmt->bindParam(":donor_id", $donor_id);
                $caller_stmt->bindParam(":number", $number);
                $caller_stmt->bindParam(":duration", $duration);
                // insert record
                if ($caller_stmt->execute()) {
                    return true; //success
                }
                http_response_code(400); // bad request
                throw new Exception("We're having trouble inserting the caller record.");
                return false;
            } else {
                $donor_id = null; // no donor
                http_response_code(404); // not found
                throw new Exception("Donor not found by phone number $number.");
            }
        } catch (PDOException $e) {
            http_response_code(500); // http failure
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        } catch (Exception $e) {
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        }
    }

    // download stats
    public function createDownload()
    {
        try {
            // query to insert record
            $query = "INSERT INTO
		        " . $this->download_log_table . "
		        SET
                `timestamp`=FROM_UNIXTIME(:timestamp),
		        `show`=:show,
                `episode`=:episode,
                `post_id`=:post_id,
                `referer`=:referer,
                `useragent`=:useragent,
                `ipaddress`=:ipaddress
                ";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // bind values
            $stmt->bindParam(":timestamp", $this->timestamp);
            $stmt->bindParam(":show", $this->show);
            $stmt->bindParam(":episode", $this->episode);
            $stmt->bindParam(":post_id", $this->post_id);
            $stmt->bindParam(":referer", $this->referer);
            $stmt->bindParam(":useragent", $this->useragent);
            $stmt->bindParam(":ipaddress", $this->ipaddress);

            // execute query
            if ($stmt->execute()) {
                return true;
            } else {
                return false;
            }
        } catch (PDOException $e) {
            http_response_code(500); // http failure
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        } catch (Exception $e) {
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        }
    }

    // read download stats
    public function readDownloadbyDateTime()
    {
        try {
            // select all query
            $query = "SELECT
                `show`,
                episode,
                COUNT(`episode`) AS `count`
            FROM
                " . $this->download_log_table . "
            WHERE
                `timestamp` BETWEEN :datetime_start AND :datetime_end
            GROUP BY
                `show`,`episode`
            ORDER BY
                `count`
            DESC";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind values
            $stmt->bindParam(":datetime_start", $this->datetime_start);
            $stmt->bindParam(":datetime_end", $this->datetime_end);

            // execute query
            $stmt->execute();

            // return results
            if (!empty($stmt->readAll)) {
                return $stmt;
            } else {
                http_response_code(404); // not found
                throw new Exception("No downloads exist during range");
            }
        } catch (PDOException $e) {
            http_response_code(500); // http failure
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        } catch (Exception $e) {
            echo json_encode(array("message" => $e->getMessage()));
            exit();
        }
    }
}
