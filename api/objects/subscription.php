<?php
// Provides class for Subscription
class Subscription
{
    // database connection and table code
    private $conn;
    private $donors_table = "donors";
    private $donations_table = "donations";
    private $payments_table = "payments";
    private $premiums_table = "premiums";
    private $shipments_table = "shipments";
    private $shipment_addresses_table = "shipment_addresses";
    private $campaigns_table = "campaigns";
    private $premium_categories_table = "premium_categories";
    private $subscriptions_table = "subscriptions";
    private $stripe_prices_table = "stripe_prices";

    // constructor with $db as database connection
    public function __construct($db)
    {
        $this->conn = $db;
    }
    // lookup stripe_prices
    public function readCreateStripePrices($amount)
    {
        try {
            // include stripe
            global $stripe;

            // convert to cents for prices lookup
            $amount = $amount * 100; // amount in cents
            // Lookup amout by price, grab price_id
            $query = "SELECT
                " . $this->stripe_prices_table . ".`id` as 'price_id'
                    FROM	" . $this->stripe_prices_table . "
                    WHERE " . $this->stripe_prices_table . ".`amount` = ?;
                ";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of subscription to be found
            $stmt->bindParam(1, $amount);

            // execute query
            $stmt->execute();

            // retrieve results
            $stmt_arr = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!empty($stmt_arr)) {
                // extract the results
                extract($stmt_arr);
                //declare $price_id
            }

            // test to see if value
            if (empty($stmt_arr)) { //$price_id not found
                global $product_id; //defined in config.php
                // create price
                $prices = $stripe->prices->create([
                    'unit_amount' => $amount,
                    'currency' => 'usd',
                    'recurring' => ['interval' => 'month'],
                    'product' => $product_id,
                ]);
                $price_id = $prices->id;
            }
            // return values from database
            return $price_id;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }
    // create Subscription (on default card)
    public function create($payment_arr)
    {
        try {
            // Check for $transaction_id before attempt
            if ($this->read_subscription_by_transaction_id($payment_arr->transaction_id)->rowCount() > 0) {
                throw new Exception("There can only be one Subscription per Donation.");
            }

            // include stripe
            global $stripe;
            global $product_id;

            // Subscribe donor to subscription
            // convert to cents for prices lookup
            $amount = $payment_arr->amount * 100; // amount in cents
            // Lookup amout by price, grab price_id
            $query = "SELECT
                    " . $this->stripe_prices_table . ".`id` as 'price_id'
            FROM	" . $this->stripe_prices_table . "
            WHERE " . $this->stripe_prices_table . ".`amount` = ?;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of subscription to be found
            $stmt->bindParam(1, $amount);

            // execute query
            $stmt->execute();

            // try and assign price_id value
            $price_id = $stmt->fetch(PDO::FETCH_OBJ)->price_id;

            // check if price_id has value.
            if (empty($price_id)) {
                global $product_id; //defined in config.php
                // create price
                $prices = $stripe->prices->create([
                    'unit_amount' => $amount,
                    'currency' => 'usd',
                    'recurring' => ['interval' => 'month'],
                    'product' => $product_id,
                ]);
                $price_id = $prices->id;
            }
            // if it has a value then proces stripe subscription
            if (!empty($price_id)) {
                // Subscribe customer to an existing price
                $subscriptions = $stripe->subscriptions->create([
                    'customer' => $payment_arr->customer_id,
                    'items' => [
                        [
                            'price' => $price_id,
                            'metadata' => array(
                                'transaction_id' => $payment_arr->transaction_id,
                                'donor_id' => $payment_arr->donor_id,
                            ),
                        ], // as found in local stripe_prices table
                    ],
                     'payment_settings' =>
                       ['payment_method_types' => ['card', 'us_bank_account', 'link']],
                ]);
                //update donor transaction_id
                $stripe->customers->update(
                    $payment_arr->customer_id,
                    ['metadata' => ['transaction_id' => $payment_arr->transaction_id]]
                );
            } else {
                http_response_code(500); // Bad Request
                throw new Exception("[price_id] could not be found or created");
            }
            if ($subscriptions->status == "active") {
                // fetch subscription object from db
                // insert subscription query
                $query = "INSERT INTO
                        " . $this->subscriptions_table . "
                    SET
                        processor=:processor,
                        id=:id,
                        customer_id=:customer_id,
                        donor_id=:donor_id,
                        date_created=FROM_UNIXTIME(:date_created),
                        plan_id=:plan_id,
                        transaction_id=:transaction_id,
                        amount=:amount,
                        `interval`=:interval,
                        active=:active
                        ";

                // prepare query statement
                $stmt = $this->conn->prepare($query);

                // sanitize
                $processor = "Stripe"; // enum('Stripe', 'PayPal', 'P-check', 'K-check', 'cash')
                $subscription_id = $subscriptions->id; //  VARCHAR(255)
                $date_created = $subscriptions->created; // FROM_UNIXTIME(datetime)
                $plan_id = $subscriptions->items->data[0]->plan->id; //VARCHAR(255)
                $interval = $subscriptions->items->data[0]->plan->interval; // enum "month"
                $active = (bool) $subscriptions->items->data[0]->plan->active; // boolean "true"

                // bind values
                $stmt->bindParam(":processor", $processor);
                $stmt->bindParam(":id", $subscription_id);
                $stmt->bindParam(":customer_id", $payment_arr->customer_id);
                $stmt->bindParam(":donor_id", $payment_arr->donor_id, PDO::PARAM_INT);
                $stmt->bindParam(":date_created", $date_created);
                $stmt->bindParam(":plan_id", $plan_id);
                $stmt->bindParam(":transaction_id", $payment_arr->transaction_id);
                $stmt->bindParam(":amount", $amount, PDO::PARAM_INT);
                $stmt->bindParam(":interval", $interval);
                $stmt->bindParam(":active", $active);

                // execute query
                $stmt->execute();

                return ($this->read($subscriptions->id)->fetch(PDO::FETCH_ASSOC));
            }
            if ($subscriptions->status == "incomplete") {
                http_response_code(400); // Bad Request
                throw new Exception("Subscription successfully created but charge unable to be processed, please check donors card");
            }

            // catch exceptions
        } catch (PDOException $e) { // PDO Error
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) { // PHP Error message
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
        } catch (\Stripe\Exception\CardException $e) {
            // Since it's a decline, \Stripe\Exception\CardException will be caught
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\RateLimitException $e) {
            // Too many requests made to the API too quickly
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            // Invalid parameters were supplied to Stripe's API
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\AuthenticationException $e) {
            // Authentication with Stripe's API failed
            // (maybe you changed API keys recently)
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\ApiConnectionException $e) {
            // Network communication with Stripe failed
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Display a very generic error to the user, and maybe send
            // yourself an email
            http_response_code($e->getHttpStatus()); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getError()->message,
            ));
            error_log($e->getError()->message, 0);
        } catch (Exception $e) {
            // Something else happened, completely unrelated to Stripe
            http_response_code(400); // bad request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
            error_log($e->getMessage(), 0);
        }
    }
    // read Subscription
    public function read($subscription_id)
    {
        try {
            // This function searches the subscription table and returns the result(s)
            $query = "SELECT
                    " . $this->subscriptions_table . ".`id`,
                    " . $this->subscriptions_table . ".`customer_id`,
                    " . $this->subscriptions_table . ".`donor_id`,
                    " . $this->subscriptions_table . ".`processor`,
                    " . $this->subscriptions_table . ".`date_created`,
                    " . $this->subscriptions_table . ".`transaction_id`,
                    " . $this->subscriptions_table . ".`amount`,
                    " . $this->subscriptions_table . ".`interval`,
                    " . $this->subscriptions_table . ".`plan_id`,
                    " . $this->subscriptions_table . ".`active`,
                    " . $this->subscriptions_table . ".`date_canceled`
            FROM	" . $this->subscriptions_table . "
            WHERE " . $this->subscriptions_table . ".`id` = ?;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of subscription to be found
            $stmt->bindParam(1, $subscription_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }
    // read Subscription by transaction_id
    public function read_subscription_by_transaction_id($transaction_id)
    {
        try {
            // This function searches the payments table and returns the result(s)
            $query = "SELECT
                    " . $this->subscriptions_table . ".`id`,
                    " . $this->subscriptions_table . ".`customer_id`,
                    " . $this->subscriptions_table . ".`donor_id`,
                    " . $this->subscriptions_table . ".`processor`,
                    " . $this->subscriptions_table . ".`date_created`,
                    " . $this->subscriptions_table . ".`transaction_id`,
                    " . $this->subscriptions_table . ".`amount`,
                    " . $this->subscriptions_table . ".`interval`,
                    " . $this->subscriptions_table . ".`plan_id`,
                    " . $this->subscriptions_table . ".`active`,
                    " . $this->subscriptions_table . ".`date_canceled`
            FROM	" . $this->subscriptions_table . "
            WHERE " . $this->subscriptions_table . ".`transaction_id` = ?;";

            // prepare query statement
            $stmt = $this->conn->prepare($query);

            // bind id of subscription to be found
            $stmt->bindParam(1, $transaction_id);

            // execute query
            $stmt->execute();

            // return values from database
            return $stmt;
            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }
    // update Subscription (downgrade or upgrade)
    public function update($subscription_id, $amount)
    {
        try {
            // include stripe
            global $stripe;
            // Switching prices does not normally change the billing date or generate an immediate charge
            // Get the current subscription object
            $subscription = $stripe->subscriptions->retrieve($subscription_id);

            // find plan with $amount or create?

            // set price (for future payments)
            $stripe->subscriptions->update($subscription_id, [
                'cancel_at_period_end' => false,
                'proration_behavior' => 'none',
                'items' => [
                    [
                        'id' => $subscription->items->data[0]->id,
                        'price' => 'price_CBb6IXqvTLXp3f', //apply new price from price list?
                    ],
                ],
            ]);

            // catch exceptions

        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }
    // cancel Subscription (a recurring donation)
    public function cancel($subscription_id)
    {
        try {
            // include stripe
            global $stripe;

            // Cancels a customer's subscription immediately. The customer will not be charged again for the subscription.
            $stripe->subscriptions->cancel($subscription_id, []);
        } catch (Exception $e) {
            http_response_code(400); // bad request
            // could not cancel donors subscription
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
            die;
        }
        // ** [RETURN] Section  ** //
        return true;
    }
    // delete a Subscription from DB
    public function delete($subscription_id)
    {
        try {
            // delete query for donation
            $query = "DELETE FROM " . $this->subscriptions_table . " WHERE id = ?";

            // prepare query
            $stmt = $this->conn->prepare($query);

            // bind
            $stmt->bindParam(1, $subscription_id);

            // execute delete query
            $stmt->execute();
            $count = $stmt->rowCount(); // check affected rows using rowCount
            if ($count > 0) {
                return true;
            } else {
                http_response_code(404); // bad request
                throw new Exception("[subscription_id]: $subscription_id does not exist or has already been deleted");
            }
        } catch (Exception $e) {
            http_response_code(400); // bad request
            // could not cancel donors subscription
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
            die;
        }
        // ** [RETURN] Section  ** //
        return true;
    }
}
