<?php

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * get header Authorization
 * */
function getAuthorizationHeader() {
    $headers = null;
    if (isset($_SERVER["Authorization"])) {
        $headers = trim($_SERVER["Authorization"]);
    } elseif (isset($_SERVER["HTTP_AUTHORIZATION"])) {
        //Nginx or fast CGI
        $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
    } elseif (function_exists("apache_request_headers")) {
        $requestHeaders = apache_request_headers();
        // Server-side fix for bug in old Android versions (a nice side-effect of this fix means we don't care about capitalization for Authorization)
        $requestHeaders = array_combine(
            array_map("ucwords", array_keys($requestHeaders)),
            array_values($requestHeaders),
        );
        //print_r($requestHeaders);
        if (isset($requestHeaders["Authorization"])) {
            $headers = trim($requestHeaders["Authorization"]);
        }
    }
    return $headers;
}

/**
 * get access token from header
 * */
function getBearerToken() {
    $headers = getAuthorizationHeader();
    // HEADER: Get the access token from the header
    if (!empty($headers)) {
        if (preg_match("/Bearer\s(\S+)/", $headers, $matches)) {
            return $matches[1];
        }
    }
    return null; // return NULL on failure
}

function refreshToken($jwt, $key) {
    try {
        // decode jwt
        $decoded = JWT::decode($jwt, new Key($key, "HS512"));

        //TODO: do something if exception is not fired
    } catch (\Firebase\JWT\ExpiredException $e) {
        JWT::$leeway = 432000; // 5 days

        // decode jwt
        $decoded = JWT::decode($jwt, new Key($key, "HS512"));

        // Convert stdClass to array for manipulation
        $decodedArray = (array) $decoded;

        // TODO: test if token is blacklisted
        $decodedArray["iat"] = time();
        $decodedArray["exp"] = time() + 120; // Set new expiration time

        // Re-encode the JWT
        return JWT::encode($decodedArray, $key, "HS512");
    } catch (\Exception $e) {
        var_dump($e);
    }
}
//echo    $refresh = refreshToken($jwt, $key);

// check if 'Authorization' header is present ...
if (getAuthorizationHeader()) {
    // Extract the jwt from the Bearer
    if ($jwt = getBearerToken()) {
        // Header was accepted, jwt was extracted from the request
        // exception for application tokens
        // todo: process for craeting JWTs as application tokens?
        if ($jwt === $cloudflare_show_sync_token) {
        } else {
            try {
                // decode jwt against $key from config.php
                $decoded = JWT::decode($jwt, new Key($key, "HS512"));
                // set access_level
                $access_level = $decoded->data->access_level;
                // set response code 200 OK - the token was decoded.
                http_response_code(200);
                // Continue on ...
            } catch (Exception $e) {
                // if decode fails, it means jwt is invalid
                // set response code 401 Unauthorized - the token was not able to be decoded. (tampered token)?
                http_response_code(401);

                // tell the user access denied  & show error message
                echo json_encode([
                    "status" => "error",
                    "message" => "Access denied.",
                    "error" => $e->getMessage(),
                    "data" => $jwt,
                ]);
                die();
            }
        }
    } else {
        // set response code
        http_response_code(400); // Bad Request - No token was able to be extracted from the authorization header

        echo json_encode([
            "status" => "error",
            "message" =>
                "No token was able to be extracted from the authorization header.",
        ]);
        die();
    }
} else {
    // IF there's no token, then set access level to "Guest"
    $access_level = "Guest";
}
