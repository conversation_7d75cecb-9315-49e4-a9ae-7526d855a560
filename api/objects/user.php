<?php
// 'user' object
class User
{

    // database connection and table name
    private $conn;
    private $users_table = "users";

    // object properties
    public $id;
    public $firstname;
    public $lastname;
    public $email;
    public $password;

    // constructor
    public function __construct($db)
    {
        $this->conn = $db;
    }

    // create new user record
    public function create()
    {
        try {
            // Check to see if user exists
            if ($this->search()->rowCount() > 0) {
                http_response_code(400); // Bad Request
                throw new Exception("[email] account already exists.");
            }

            // insert query
            $query = "INSERT INTO " . $this->users_table . "
            SET
                firstname = :firstname,
                lastname = :lastname,
                email = :email,
                password = :password,
                access_level = :access_level";

            // prepare the query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->firstname = htmlspecialchars(strip_tags($this->firstname));
            $this->lastname = htmlspecialchars(strip_tags($this->lastname));
            $this->email = htmlspecialchars(strip_tags($this->email));
            $this->password = $this->password;
            $this->access_level = $this->access_level;

            // bind the values
            $stmt->bindParam(':firstname', $this->firstname);
            $stmt->bindParam(':lastname', $this->lastname);
            $stmt->bindParam(':email', $this->email);
            $stmt->bindParam(':access_level', $this->access_level);

            // hash the password before saving to database
            $password_hash = password_hash($this->password, PASSWORD_BCRYPT);
            $stmt->bindParam(':password', $password_hash);

            // execute the query, also check if query was successful
            if ($stmt->execute()) {
                return true;
            }
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }

    // read user by ID
    public function read($id)
    {
        try {
            // This function reads the user by id.

            $query = "SELECT email, firstname, lastname, access_level
                FROM " . $this->users_table . "
                WHERE id = ?
                LIMIT 0,1";

            // prepare the query
            $stmt = $this->conn->prepare($query);

            // bind given email value
            $stmt->bindParam(1, $id, PDO::PARAM_INT);

            // execute the query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }

    // read all users
    public function readUsers()
    {
        try {
            // This function reads the user by id.

            $query = "SELECT id, email, firstname, lastname, access_level
                 FROM " . $this->users_table . "
                 ";

            // prepare the query
            $stmt = $this->conn->prepare($query);

            // execute the query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }

    // read user (by email) from the database
    public function search()
    {
        try {
            // query to check if email exists
            $query = "SELECT id, firstname, lastname, email, access_level, password
                FROM " . $this->users_table . "
                WHERE email = ?
                LIMIT 0,1";

            // prepare the query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->email = htmlspecialchars(strip_tags($this->email));

            // bind given email value
            $stmt->bindParam(1, $this->email);

            // execute the query
            $stmt->execute();

            // return values from database
            return $stmt;

            // catch exceptions
        } catch (PDOException $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "code" => $e->getCode(),
                "message" => $stmt->errorInfo()[2],
                "function" => __METHOD__,
            ));
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "message" => $e->getMessage(),
            ));
        }
    }

    // update a user record
    public function update()
    {
        try {
            // if password needs to be updated
            $password_set = !empty($this->password) ? ", password = :password" : "";

            // if no posted password, do not update the password
            $query = "UPDATE " . $this->users_table . "
            SET
                firstname = :firstname,
                lastname = :lastname,
                email = :email
                {$password_set}
            WHERE email = :email";

            // prepare the query
            $stmt = $this->conn->prepare($query);

            // sanitize
            $this->firstname = htmlspecialchars(strip_tags($this->firstname));
            $this->lastname = htmlspecialchars(strip_tags($this->lastname));
            $this->email = htmlspecialchars(strip_tags($this->email));

            // bind the values
            $stmt->bindParam(':firstname', $this->firstname);
            $stmt->bindParam(':lastname', $this->lastname);
            $stmt->bindParam(':email', $this->email);

            // hash the password before saving to database
            if (!empty($this->password)) {
                $password_hash = password_hash($this->password, PASSWORD_BCRYPT);
                $stmt->bindParam(':password', $password_hash);
            }

            // execute the query
            if ($stmt->execute()) {
                return true;
            }
        } catch (PDOException $e) {
            echo json_encode(array("message" => $e->getMessage()));
            return false;
            die;
        }
    }

    // update a user login time
    public function update_login()
    {
        try {
            // if no posted password, do not update the password
            $query = "UPDATE " . $this->users_table . "
                SET
                " . $this->users_table . ".last_login = NOW()
                WHERE email = :email";

            // prepare the query
            $stmt = $this->conn->prepare($query);

            // bind the values
            $stmt->bindParam(':email', $this->email);

            // execute the query
            if ($stmt->execute()) {
                return true;
            }
        } catch (PDOException $e) {
            echo json_encode(array("message" => $e->getMessage()));
            return false;
            die;
        }
    }
}
