<?php
// make a payment on an existing donation

// Set payment property values
try {
    // [Required Section]
    // method
    if (
        !empty($data->method) &&
        in_array($data->method, ["cash", "check", "card", "epayment"], true)
    ) {
        $payment->method = $data->method;
    } else {
        throw new Exception(
            "[method] is required and must be of type ['cash', 'check', 'card', 'epayment'].",
        );
    }

    // amount
    if (!empty($data->amount)) {
        $payment->amount = $data->amount;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[amount] is required.");
    }

    // cash
    if ($data->method == "cash") {
        // donation_id
        if (!empty($data->donation_id)) {
            $payment->donation_id = $data->donation_id;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[donation_id] is required.");
        }

        // date_deposited
        if (!empty($data->date_deposited)) {
            $payment->date_deposited = $data->date_deposited;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[date_deposited] is required.");
        }

        // if we haven't died, try and create the payment
        $payment->create_cash_payment();

        // Read the payment and send as JSON
        echo json_encode(
            $payment->readPayment($db->lastInsertId())->fetch(PDO::FETCH_ASSOC),
            JSON_NUMERIC_CHECK,
        );
    }
    // ePayment or Check
    if ($data->method == "epayment" || $data->method == "check") {
        // donation_id
        if (!empty($data->donation_id)) {
            $payment->donation_id = $data->donation_id;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[donation_id] is required.");
        }

        // payment_id (ePayment ID or Check #)
        if (!empty($data->payment_id)) {
            $payment->payment_id = $data->payment_id;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[payment_id] is required.");
        }

        // processor (e.g., PayPal, Venmo, Bank, etc.)
        if (!empty($data->processor)) {
            $payment->processor = $data->processor;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[processor] is required.");
        }

        // date_deposited
        if (!empty($data->date_deposited)) {
            $payment->date_deposited = $data->date_deposited;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[date_deposited] is required.");
        }

        // status
        if (!empty($data->status)) {
            $payment->status = $data->status;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[status] is required.");
        }

        // if we haven't died, try and create the payment
        $payment->create_epayment_or_check_payment($data->method); // Call the unified method

        // Read the payment and send as JSON
        echo json_encode(
            $payment->readPayment($db->lastInsertId())->fetch(PDO::FETCH_ASSOC),
            JSON_NUMERIC_CHECK,
        );
    }

    // card
    if ($data->method == "card") {
        $payment->processor = "Stripe"; //set processor to stripe
        // donation_id
        if (!empty($data->donation_id)) {
            $payment->donation_id = $data->donation_id;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[donation_id] is required.");
        }

        // transaction_id
        if (!empty($data->transaction_id)) {
            $payment->transaction_id = $data->transaction_id;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[transaction_id] is required.");
        }

        // donor_id
        if (!empty($data->donor_id)) {
            $payment->donor_id = $data->donor_id;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[donor_id] is required.");
        }

        // cardnumber
        if (!empty($data->cardnumber)) {
            $payment->cardnumber = $data->cardnumber;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[cardnumber] is required.");
        }

        // cc_securitycode
        if (!empty($data->cc_securitycode)) {
            $payment->cc_securitycode = $data->cc_securitycode;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[cc_securitycode] is required.");
        }

        // exp_month
        if (!empty($data->exp_month)) {
            $payment->exp_month = $data->exp_month;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[exp_month] is required.");
        }

        // exp_year
        if (!empty($data->exp_year)) {
            $payment->exp_year = $data->exp_year;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[exp_year] is required.");
        }

        // firstname
        if (!empty($data->firstname)) {
            $payment->firstname = $data->firstname;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[firstname] is required.");
        }

        // lastname
        if (!empty($data->lastname)) {
            $payment->lastname = $data->lastname;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[lastname] is required.");
        }

        // address1
        if (!empty($data->address1)) {
            $payment->address1 = $data->address1;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[address1] is required.");
        }

        // city
        if (!empty($data->city)) {
            $payment->city = $data->city;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[city] is required.");
        }

        // state
        if (!empty($data->state)) {
            $payment->state = $data->state;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[state] is required.");
        }

        // postal_code
        if (!empty($data->postal_code)) {
            $payment->postal_code = $data->postal_code;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[postal_code] is required.");
        }

        // country
        if (!empty($data->country)) {
            $payment->country = $data->country;
        } else {
            http_response_code(400); // Bad Request
            throw new Exception("[country] is required.");
        }

        // source
        if (
            !empty($data->source) &&
            in_array(
                $data->source,
                [
                    "StationAdmin",
                    "CallCenter",
                    "PhoneRoom",
                    "WebSite",
                    "Testing",
                    "PaySol",
                ],
                true,
            )
        ) {
            $payment->source = $data->source;
        } else {
            throw new Exception(
                "[source] is required and must be of type ['StationAdmin', 'CallCenter', 'PhoneRoom', 'WebSite', 'Testing', 'PaySol'].",
            );
        }

        ///////////// OPTIONAL INFO ///////////
        // shipping_address1
        if (!empty($data->shipping_address1)) {
            $payment->shipping_address1 = $data->shipping_address1;
        } else {
            $payment->shipping_address1 = $data->address1;
        }
        // shipping_address2
        if (!empty($data->shipping_address2)) {
            $payment->shipping_address2 = $data->shipping_address2;
        } elseif (!empty($data->address2)) {
            $payment->shipping_address2 = $data->address2;
        } else {
            $payment->shipping_address2 = null;
        }

        // shipping_city
        if (!empty($data->shipping_city)) {
            $payment->shipping_city = $data->shipping_city;
        } else {
            $payment->shipping_city = $data->city;
        }
        // shipping_state
        if (!empty($data->shipping_state)) {
            $payment->shipping_state = $data->shipping_state;
        } else {
            $payment->shipping_state = $data->state;
        }
        // shipping_country
        if (!empty($data->shipping_country)) {
            $payment->shipping_country = $data->shipping_country;
        } else {
            $payment->shipping_country = $data->country;
        }
        // shipping_postal_code
        if (!empty($data->shipping_postal_code)) {
            $payment->shipping_postal_code = $data->shipping_postal_code;
        } else {
            $payment->shipping_postal_code = $data->postal_code;
        }
        /////////////////////////////////////

        // not required
        // address2
        if (!empty($data->address2)) {
            $payment->address2 = $data->address2;
        } else {
            $payment->address2 = null;
        }
        // premiums
        if (!empty($data->premiums)) {
            $payment->premiums = $data->premiums;
        } else {
            $payment->premiums = "[]";
        }
        // phone
        if (!empty($data->phone)) {
            $payment->phone = $data->phone;
        } else {
            $payment->phone = null;
        }
        // email
        if (!empty($data->email)) {
            $payment->email = $data->email;
        } else {
            $payment->email = null;
        }
        // show_name
        if (!empty($data->show_name)) {
            $payment->show_name = $data->show_name;
        } else {
            $payment->show_name = null;
        }
        // comments
        if (!empty($data->comments)) {
            $payment->comments = $data->comments;
        } else {
            $payment->comments = null;
        }

        // installment
        if (!empty($data->installment)) {
            $payment->installment = $data->installment;
        } else {
            $payment->installment = null;
        }

        // CHARGE Card & INSERT into DB
        if ($payment->create_stripe_payment($payment)) {
            // Read the payment and send as JSON (if successful)
            // This was failing when it was querying the database for the payment, so I changed it to directly use the array of data we created the payment with. The failure was difficult to locate exactly but I believe it was a race condition - since we're waiting for a webhook to be sent from Stripe before the actual row is created in our payment table, we were querying for the payment table and coming up with no results. This seems to work fine for now.
            echo json_encode($data);
        }
    }

    // Send error if incoming JSON invalid
} catch (Exception $e) {
    http_response_code(400); // Bad Request
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage(),
    ]);
}
