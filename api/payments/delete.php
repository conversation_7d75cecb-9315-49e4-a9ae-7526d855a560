<?php
// Payments
try {
    // ACL
    $access_levels = array("Admin", "Staff");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    if ($collection == "payments") {

        // is ID in URL ?
        if (!empty($resource)) {
            $payment->id = $resource;
        }

        // is ID in the body ($data) ?
        if (isset($data->id)) {
            $payment->id = $data->id;
        }

        // not found, throw error !
        if (empty($payment->id)) {
            http_response_code(400); // Bad Request
            throw new Exception("[id] is required");
        }

        // delete the payment if no exceptions
        if ($payment->delete()) {
            echo json_encode(array(
                "message" => "Payment $payment->id was deleted.",
            ));
        }
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[collection] is required.");
    }

    // if unable to delete the payment, tell the user
} catch (Exception $e) {
    //  display message, write error to log, die.
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    error_log($e->getMessage(), 0);
}
