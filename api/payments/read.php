<?php
// payments
try {
    // ACL
    $access_levels = array("Admin", "Staff");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    if ($collection == "payments") {
        // READ payment
        if ((!isset($_GET["s"])) && (isset($resource))) {
            // query payment
            $stmt = $payment->readPayment($resource);
            $num = $stmt->rowCount();

            // check if more than 0 record found
            if ($num > 0) {
                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);
                    // create array
                    $payment_item = array(
                        "id" => filter_var($id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "donation_id" => filter_var($donation_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "donor_id" => intval($donor_id),
                        "customer_id" => $customer_id,
                        "payment_id" => $payment_id,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "address1" => $address1,
                        "address2" => $address2,
                        "city" => $city,
                        "state" => $state,
                        "country" => $country,
                        "postal_code" => $postal_code,
                        "phone" => $phone,
                        "email" => $email,
                        "processor" => $processor,
                        "method" => $method,
                        "card_type" => $card_type,
                        "amount" => intval($amount),
                        "amount_refunded" => intval($amount_refunded),
                        "status" => $status,
                        "last4" => filter_var($last4, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                        "exp_month" => filter_var($exp_month, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                        "exp_year" => filter_var($exp_year, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "last_updated_by" => $last_updated_by,
                        "date_created" => $date_created,
                        "date_updated" => $date_updated,
                        "date_deposited" => $date_deposited
                    );
                } // end while for $stmt

                echo json_encode($payment_item);
            } else {
                http_response_code(404); // not found
                throw new Exception("No Payment Found.");
            }
        }
        // READ payments
        if ((!isset($_GET["s"])) && (!isset($resource))) {

            // Check to see if we should filter by DATE
            if (!empty($_GET['start']) && !empty($_GET['end']) && !empty($_GET['status'])) {
                $startdate = date('Y-m-d', strtotime($_GET['start']));
                $enddate = date('Y-m-d', strtotime($_GET['end']));
                $status = $_GET['status'];

                // filter payments by date
                // todo xxx fixme
                $stmt = $payment->readDate($startdate, $enddate, $status);
                $num = $stmt->rowCount();
            } else {
                // read most recent payments
                $stmt = $payment->read();
                $num = $stmt->rowCount();
            }

            // check if more than 0 record found
            if ($num > 0) {
                // instantiate the payments array
                $payments_arr = array();
                $payments_arr["records"] = array();
                // retrieve our table contents

                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);
                    // create array
                    $payment_item = array(
                        "id" => filter_var($id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "donation_id" => filter_var($donation_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "donor_id" => intval($donor_id),
                        "customer_id" => $customer_id,
                        "payment_id" => $payment_id,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "address1" => $address1,
                        "address2" => $address2,
                        "city" => $city,
                        "state" => $state,
                        "country" => $country,
                        "postal_code" => $postal_code,
                        "phone" => $phone,
                        "email" => $email,
                        "processor" => $processor,
                        "method" => $method,
                        "card_type" => $card_type,
                        "amount" => intval($amount),
                        "amount_refunded" => intval($amount_refunded),
                        "status" => $status,
                        "last4" => filter_var($last4, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                        "exp_month" => filter_var($exp_month, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                        "exp_year" => filter_var($exp_year, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "last_updated_by" => $last_updated_by,
                        "date_created" => $date_created,
                        "date_updated" => $date_updated,
                        "date_deposited" => $date_deposited
                    );
                    array_push($payments_arr["records"], $payment_item); // add payment item to payments array
                } // end while for $stmt

                echo json_encode($payments_arr);
            } else {
                http_response_code(404); // not found
                throw new Exception("No Payments Found.");
            }
        }
        // SEARCH paymentss //IMPLEMENT TODO
        if ((isset($_GET["s"])) && (!isset($resource))) {
            $keywords = isset($_GET["s"]) ? $_GET["s"] : json_encode(array("message" => "Please provide a search term."));

            // query payments
            $stmt = $payment->searchPayments($keywords);
            $num = $stmt->rowCount();

            // check if more than 0 record found
            if ($num > 0) {
                // instantiate the payments array
                $payments_arr = array();
                $payments_arr["records"] = array();
                // retrieve our table contents

                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);
                    // create array
                    $payment_item = array(
                        "id" => filter_var($id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "donation_id" => filter_var($donation_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "donor_id" => intval($donor_id),
                        "customer_id" => $customer_id,
                        "payment_id" => $payment_id,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "address1" => $address1,
                        "address2" => $address2,
                        "city" => $city,
                        "state" => $state,
                        "country" => $country,
                        "postal_code" => $postal_code,
                        "phone" => $phone,
                        "email" => $email,
                        "processor" => $processor,
                        "method" => $method,
                        "card_type" => $card_type,
                        "amount" => intval($amount),
                        "amount_refunded" => intval($amount_refunded),
                        "status" => $status,
                        "last4" => filter_var($last4, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                        "exp_month" => filter_var($exp_month, FILTER_VALIDATE_INT, FILTER_FLAG_ALLOW_OCTAL),
                        "exp_year" => filter_var($exp_year, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                        "last_updated_by" => $last_updated_by,
                        "date_created" => $date_created,
                        "date_updated" => $date_updated,
                        "date_deposited" => $date_deposited
                    );
                    array_push($payments_arr["records"], $payment_item); // add payment item to payments array
                } // end while for $stmt

                echo json_encode($payments_arr);
            } else {
                http_response_code(404); // not found
                throw new Exception("No Payments Found.");
            }
        }
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[collection] is required.");
    }
} catch (Exception $e) {
    //  display message
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
