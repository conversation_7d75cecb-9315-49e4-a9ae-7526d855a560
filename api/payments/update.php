<?php
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    // make a payment on an existing donation or refund cc
    // Set payment property values

    // source [not required]
    if (!empty($data->source)) {
        $payment->source = $data->source;
    }
    // [Required Section]
    // method
    if (!empty($data->method)) {
        $payment->method = $data->method;
    } else {
        throw new Exception("[method] is required.");
    }
    // id (payment id)
    if (!empty($resource)) {
        $payment->id = $resource;
    } else if (!empty($data->id)) {
        $payment->id = $data->id;
    } else {
        throw new Exception("[id] is required.");
    }
    // amount
    if (!empty($data->amount)) {
        $payment->amount = $data->amount;
    } else {
        throw new Exception("[amount] is required.");
    }

    // check
    if ($data->method == "check") {
        // status
        $status_arr = array("succeeded", "pending", "failed");
        if (!empty($data->status) && (in_array($data->status, $status_arr))) { // status [required]
            $payment->status = $data->status;
        } else {
            throw new Exception("[status] is required and must be one of the following: succeeded, pending, failed");
        }

        // donation_id
        if (!empty($data->donation_id)) {
            $payment->donation_id = $data->donation_id;
        } else {
            throw new Exception("[donation_id] is required.");
        }

        // payment_id // (check #)
        if (isset($data->payment_id)) { // can't use !empty because 0 is valid
            $payment->payment_id = $data->payment_id;
        } else {
            throw new Exception("[payment_id] is required.");
        }

        // processor
        if (!empty($data->processor)) {
            $payment->processor = $data->processor;
        } else {
            throw new Exception("[processor] is required.");
        }

        // date_deposited
        if (!empty($data->date_deposited)) {
            $payment->date_deposited = $data->date_deposited;
        } else {
            throw new Exception("[date_deposited] is required.");
        }

        // if we haven't died, try and update the payment
        $payment->update_cash_check_payment(); // call Payment update_cash_check_payment function
    }
    // cash
    if ($data->method == "cash") {
        // status
        $status_arr = array("succeeded", "pending", "failed");
        if (!empty($data->status) && (in_array($data->status, $status_arr))) { // status [required]
            $payment->status = $data->status;
        } else {
            throw new Exception("[status] is required and must be one of the following: succeeded, pending, failed");
        }
        // donation_id
        if (!empty($data->donation_id)) {
            $payment->donation_id = $data->donation_id;
        } else {
            throw new Exception("[donation_id] is required.");
        }

        // payment_id // (random cash #)
        if (isset($data->payment_id)) { // can't use !empty because 0 is valid
            $payment->payment_id = $data->payment_id;
        } else {
            throw new Exception("[payment_id] is required.");
        }

        // processor
        if (!empty($data->processor)) {
            $payment->processor = $data->processor;
        } else {
            throw new Exception("[processor] is required.");
        }

        // date_deposited
        if (!empty($data->date_deposited)) {
            $payment->date_deposited = $data->date_deposited;
        } else {
            throw new Exception("[date_deposited] is required.");
        }

        // if we haven't died, try and update the payment
        $payment->update_cash_check_payment(); // call Payment update_cash_check_payment function
    }
    // card
    if ($data->method == "card") {
        // action [required]
        if (!empty($data->action)) {
            $payment->action = $data->action;
        } else {
            throw new Exception("[action] is required.");
        }

        $reason_arr = array("duplicate", "fraudulent", "requested_by_customer");
        if (!empty($data->reason) && (in_array($data->reason, $reason_arr))) { // reason [required]
            $payment->reason = $data->reason;
        } else {
            throw new Exception("[reason] is required and must be one of the following: duplicate, fraudulent, requested_by_customer");
        }
        if ($data->action == "refund") {
            // if we haven't died, try and Update (REFUND) the payment
            $payment->refund_stripe_payment();
        } else {
            throw new Exception("[action] must be of type: 'refund'");
        }
    }
} catch (Exception $e) { // Send error if incoming JSON invalid
    http_response_code(400); // Bad Request
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
}
