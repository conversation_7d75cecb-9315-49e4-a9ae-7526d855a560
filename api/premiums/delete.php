<?php
// Premiums
try {
	// ACL
	$access_levels = array("Admin");
	if (!in_array($access_level, $access_levels)) {
		// Unauthorized access level
		http_response_code(401); // unauthorized
		throw new Exception("[user] is unauthorized.");
	}

	if ($collection == "premiums") {

		// subresource check
		// does the 
		if (strpos($resource, '/') !== false) {
			// explodable
			$piece = explode("/", $resource);
			$resource = $piece[0]; // reassign resource
			$entity = $piece[1]; // entity

			// resource check
			if (!empty($resource) && in_array($resource, array('categories', 'vendors'), true)) {

				// DELETE Premium Category
				if ($resource == "categories") {
					// [Required Section]

					// is ID in URL ?
					if (!empty($entity)) {
						$premium_category->id = $entity;
					}

					// is ID in the body ($data) ?
					if (isset($data->id)) {
						$premium_category->id = $data->id;
					}

					// not found, throw error !
					if (empty($premium_category->id)) {
						http_response_code(400); // Bad Request
						throw new Exception("[id] is required");
					}

					// DELETE the Premium Category
					if ($premium_category->delete()) {
						echo json_encode(array(
							"message" => "Premium category $premium_category->id was deleted.",
						));
					}
				}

				// DELETE Premium Vendor
				if ($resource == "vendors") {
					// [Required Section]

					// is ID in URL ?
					if (!empty($entity)) {
						$premium_vendor->id = $entity;
					}

					// is ID in the body ($data) ?
					if (isset($data->id)) {
						$premium_vendor->id = $data->id;
					}

					// not found, throw error !
					if (empty($premium_vendor->id)) {
						http_response_code(400); // Bad Request
						throw new Exception("[id] is required");
					}

					// DELETE the premium if no exceptions
					if ($premium_vendor->delete()) {
						echo json_encode(array(
							"message" => "Premium vendor $premium_vendor->id was deleted.",
						));
					}
				}
			}
		} else { // regular premium

			// is ID in URL ?
			if (!empty($resource)) {
				$premium->id = $resource;
			}

			// is ID in the body ($data) ?
			if (isset($data->id)) {
				$premium->id = $data->id;
			}

			// not found, throw error !
			if (empty($premium->id)) {
				http_response_code(400); // Bad Request
				throw new Exception("[id] is required");
			}

			// delete the premium if no exceptions
			if ($premium->delete()) {
				echo json_encode(array(
					"message" => "Premium $premium->id was deleted.",
				));
			}
		}
	} else {
		http_response_code(400); // Bad Request
		throw new Exception("[collection] is required.");
	}

	// if unable to delete the premium, tell the user
} catch (Exception $e) {
	//  display message, write error to log, die.
	echo json_encode(array(
		"status" => "error",
		"message" => $e->getMessage(),
	));
	error_log($e->getMessage(), 0);
}
