<?php
// Premiums
try {
    $resources = array("categories", "vendors");

    // check to see if resource exists
    if (!empty($resource)) {
        // check to see if resource contains sub resource
        if (strpos($resource, '/') !== false) {
            // explodable
            $piece = explode("/", $resource);
            $resource = $piece[0]; // reassign resource
            $entity = $piece[1]; // entity
        }
    }

    if ($collection == "premiums") {
        // resource check
        if (in_array($resource, $resources)) {
            if ($resource == "categories") {
                if (!empty($entity)) {
                    // query premium_category
                    $stmt = $premium_category->read($entity);
                } else {
                    // query premium_categories
                    $stmt = $premium_category->readAll();
                }
                $num = $stmt->rowCount();
                // check if more than 0 record found
                if ($num > 0) {
                    // initialize premium_categories array
                    $premium_categories_arr = array();
                    // retrieve our table contents
                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        // extract row
                        extract($row);
                        // Build the array
                        $premium_categories_item = array(
                            "id" => intval($id),
                            "name" => $name,
                            "description" => $description,
                            "created" => $created,
                            "modified" => $modified,
                        );
                        array_push($premium_categories_arr, $premium_categories_item);
                    }
                    if (!empty($entity)) {
                        echo json_encode($premium_categories_item);
                    } else {
                        echo json_encode($premium_categories_arr);
                    }
                } else {
                    http_response_code(404); // not found
                    throw new Exception("Premium category does not exist");
                }
            }

            if ($resource == "vendors") {
                if (!empty($entity)) {
                    // query premium_vendor
                    $stmt = $premium_vendor->read($entity);
                } else {
                    // query premium_vendors
                    $stmt = $premium_vendor->readAll();
                }
                $num = $stmt->rowCount();
                // check if more than 0 record found
                if ($num > 0) {
                    // initialize premium_vendors array
                    $premium_vendors_arr = array();
                    // retrieve our table contents
                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        // extract row
                        extract($row);

                        // Build the array
                        $premium_vendors_item = array(
                            "id" => intval($id),
                            "company" => $company,
                            "contact" => $contact,
                            "website" => $website,
                            "address" => $address,
                            "city" => $city,
                            "state" => $state,
                            "zip" => $zip,
                            "phone" => $phone,
                            "email" => $email,
                            "updated" => $updated,
                        );
                        array_push($premium_vendors_arr, $premium_vendors_item);
                    }
                    if (!empty($entity)) {
                        echo json_encode($premium_vendors_item);
                    } else {
                        echo json_encode($premium_vendors_arr);
                    }
                } else {
                    http_response_code(404); // not found
                    throw new Exception("Premium vendor(s) does not exist");
                }
            }
        }
        // READ premium
        if ((!isset($_GET["s"])) && (isset($resource)) && (!in_array($resource, $resources))) {
            // query premium
            $stmt = $premium->readPremium($resource);
            $num = $stmt->rowCount();
            // check if more than 0 record found
            if ($num > 0) {
                // initialize premiums array
                $premiums_arr = array();
                $premiums_arr["records"] = array();
                // retrieve our table contents
                // fetch() is faster than fetchAll()
                // http://stackoverflow.com/questions/2770630/pdofetchall-vs-pdofetch-in-a-loop
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    // extract row
                    extract($row);
                    // Single Premium
                    if (empty($variant_name) && empty($variation_name)) {
                        // Authentication check, (null if Guest)
                        $download_url = ($access_level != "Guest") ? $download_url : null;

                        // Build the array
                        $premium_item = array(
                            "id" => intval($id),
                            "name" => $name,
                            "description" => $description,
                            "price" => floatval($price),
                            "cog" => floatval($cog),
                            "fmv" => floatval($fmv),
                            "qty" => intval($qty),
                            "active" => boolval($active),
                            "featured" => boolval($featured),
                            "img_url" => $img_url,
                            "download_url" => $download_url,
                            "category_id" => intval($category_id),
                            "category_name" => $category_name,
                            "vendor_id" => intval($vendor_id),
                            "vendor" => $vendor,
                            "vendor_code" => $vendor_code,
                            "sort_weight" => intval($sort_weight),
                            "date_created" => $date_created
                        );
                        array_push($premiums_arr["records"], $premium_item);
                    }
                    // Child Premium
                    if (!empty($parentID) && !empty($variation_name)) {
                        // Build the array
                        $premium_item = array(
                            "id" => intval($id),
                            "name" => $name,
                            "description" => $description,
                            "price" => floatval($price),
                            "cog" => floatval($cog),
                            "fmv" => floatval($fmv),
                            "qty" => intval($qty),
                            "active" => boolval($active),
                            "featured" => boolval($featured),
                            "img_url" => $img_url,
                            "download_url" => $download_url,
                            "category_id" => intval($category_id),
                            "category_name" => $category_name,
                            "vendor_id" => intval($vendor_id),
                            "vendor" => $vendor,
                            "vendor_code" => $vendor_code,
                            "sort_weight" => intval($sort_weight),
                            "date_created" => $date_created,
                            "parentID" => intval($parentID),
                            "variation_name" => $variation_name
                        );
                        array_push($premiums_arr["records"], $premium_item);
                    }
                    // Parent with Child Premium(s)
                    if (!empty($variant_name)) {
                        // read the details of premium to be edited
                        $stmt2 = $premium->readChildren($id);
                        // query premiums
                        $num2 = $stmt2->rowCount();
                        // check if more than 0 records found
                        if ($num2 > 0) {
                            // Variations array
                            $variation_arr = array();
                            // retrieve our table contents
                            while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                                extract($row2);
                                // create array
                                $variations = array(
                                    "id" => intval($childID),
                                    "qty" => intval($childQTY),
                                    "name" => $variation_name,
                                );
                                array_push($variation_arr, $variations); // Push the variations to the variations array
                            }
                            // Build the array
                            $premium_item = array(
                                "id" => intval($id),
                                "name" => $name,
                                "description" => $description,
                                "price" => floatval($price),
                                "cog" => floatval($cog),
                                "fmv" => floatval($fmv),
                                "qty" => intval($qty),
                                "active" => boolval($active),
                                "featured" => boolval($featured),
                                "img_url" => $img_url,
                                "download_url" => $download_url,
                                "category_id" => intval($category_id),
                                "category_name" => $category_name,
                                "vendor_id" => intval($vendor_id),
                                "vendor" => $vendor,
                                "vendor_code" => $vendor_code,
                                "sort_weight" => intval($sort_weight),
                                "date_created" => $date_created,
                                "variants" => array(
                                    "name" => $variant_name,
                                    "variations" => $variation_arr,
                                ),
                            );
                        }
                        if (!empty($premium_item)) {
                            array_push($premiums_arr["records"], $premium_item);
                        } else {
                            http_response_code(400); // Bad Request
                            throw new Exception("Database integrity issue. Could not properly read children for premium " . $id);
                        }
                    }
                } // end while for $stmt

                echo json_encode($premiums_arr);
            } else {
                http_response_code(400); // Bad Request
                throw new Exception("No premiums found");
            }
        }
        // READ premiums
        if ((!isset($_GET["s"])) && (!isset($resource)) && (!in_array($resource, $resources))) {
            if (isset($_GET["active"]) && $_GET["active"] == "yes") {
                // query activepremiums
                $stmt = $premium->readActivePremiums();
            } else {
                // query premiums
                $stmt = $premium->read();
            }
            $num = $stmt->rowCount();
            // check if more than 0 record found
            if ($num > 0) {
                // initialize premiums array
                $premiums_arr = array();
                $premiums_arr["records"] = array();
                // retrieve our table contents
                // fetch() is faster than fetchAll()
                // http://stackoverflow.com/questions/2770630/pdofetchall-vs-pdofetch-in-a-loop
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    // extract row
                    extract($row);
                    // Regular Premium
                    if (empty($variant_name) && empty($variation_name)) {
                        // Build the array
                        $premium_item = array(
                            "id" => intval($id),
                            "name" => $name,
                            "description" => $description,
                            "price" => floatval($price),
                            "cog" => floatval($cog),
                            "fmv" => floatval($fmv),
                            "qty" => intval($qty),
                            "active" => boolval($active),
                            "featured" => boolval($featured),
                            "img_url" => $img_url,
                            "download_url" => $download_url,
                            "category_id" => intval($category_id),
                            "category_name" => $category_name,
                            "vendor_id" => intval($vendor_id),
                            "vendor" => $vendor,
                            "vendor_code" => $vendor_code,
                            "sort_weight" => intval($sort_weight),
                            "date_created" => $date_created
                        );
                        array_push($premiums_arr["records"], $premium_item);
                    }

                    // Parent and Child Premium
                    if (!empty($variant_name)) {
                        // read the details of premium to be edited
                        $stmt2 = $premium->readChildren($id); //
                        // query premiums
                        $num2 = $stmt2->rowCount();
                        // check if more than 0 records found
                        if ($num2 > 0) {
                            // Variations array
                            $variation_arr = array();
                            // retrieve our table contents
                            while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                                extract($row2);
                                // create array
                                $variations = array(
                                    "id" => intval($childID),
                                    "qty" => intval($childQTY),
                                    "name" => $variation_name,
                                );
                                array_push($variation_arr, $variations); // Push the variations to the variations array
                            }
                            // Build the array
                            $premium_item = array(
                                "id" => intval($id),
                                "name" => $name,
                                "description" => $description,
                                "price" => floatval($price),
                                "cog" => floatval($cog),
                                "fmv" => floatval($fmv),
                                "qty" => intval($qty),
                                "active" => boolval($active),
                                "featured" => boolval($featured),
                                "img_url" => $img_url,
                                "download_url" => $download_url,
                                "category_id" => intval($category_id),
                                "category_name" => $category_name,
                                "vendor_id" => intval($vendor_id),
                                "vendor" => $vendor,
                                "vendor_code" => $vendor_code,
                                "sort_weight" => intval($sort_weight),
                                "date_created" => $date_created,
                                "variants" => array(
                                    "name" => $variant_name,
                                    "variations" => $variation_arr,
                                ),
                            );
                        }
                        if (!empty($premium_item)) {
                            array_push($premiums_arr["records"], $premium_item);
                        } else {
                            http_response_code(400); // Bad Request
                            throw new Exception("Database integrity issue. Could not properly read children for premium " . $id);
                        }
                    }
                } // end while for $stmt

                echo json_encode($premiums_arr);
            } else {
                http_response_code(400); // Bad Request
                throw new Exception("No premiums found");
            }
        }
        // SEARCH premiums
        if ((isset($_GET["s"])) && (!isset($resource)) && (!in_array($resource, $resources))) {
            $keywords = isset($_GET["s"]) ? $_GET["s"] : json_encode(array("message" => "Please provide a search term."));

            // query premiums
            $stmt = $premium->search($keywords);
            $num = $stmt->rowCount();

            // check if more than 0 record found
            if ($num > 0) {
                // initialize premiums array
                $premiums_arr = array();
                $premiums_arr["records"] = array();
                // retrieve our table contents
                // fetch() is faster than fetchAll()
                // http://stackoverflow.com/questions/2770630/pdofetchall-vs-pdofetch-in-a-loop
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    // extract row
                    extract($row);
                    // Regular Premium
                    if (empty($variant_name) && empty($variation_name)) {
                        // Build the array
                        $premium_item = array(
                            "id" => intval($id),
                            "name" => $name,
                            "description" => $description,
                            "price" => floatval($price),
                            "cog" => floatval($cog),
                            "fmv" => floatval($fmv),
                            "qty" => intval($qty),
                            "active" => boolval($active),
                            "featured" => boolval($featured),
                            "img_url" => $img_url,
                            "download_url" => $download_url,
                            "category_id" => intval($category_id),
                            "category_name" => $category_name,
                            "vendor_id" => intval($vendor_id),
                            "vendor" => $vendor,
                            "vendor_code" => $vendor_code,
                            "sort_weight" => intval($sort_weight),
                            "date_created" => $date_created
                        );
                        array_push($premiums_arr["records"], $premium_item);
                    }

                    // Parent and Child Premium
                    if (!empty($variant_name)) {
                        // read the details of premium to be edited
                        $stmt2 = $premium->readChildren($id); //
                        // query premiums
                        $num2 = $stmt2->rowCount();
                        // check if more than 0 records found
                        if ($num2 > 0) {
                            // Variations array
                            $variation_arr = array();
                            // retrieve our table contents
                            while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                                extract($row2);
                                // create array
                                $variations = array(
                                    "id" => intval($childID),
                                    "qty" => intval($childQTY),
                                    "name" => $variation_name,
                                );
                                array_push($variation_arr, $variations); // Push the variations to the variations array
                            }
                            // Build the array
                            $premium_item = array(
                                "id" => intval($id),
                                "name" => $name,
                                "description" => $description,
                                "price" => floatval($price),
                                "cog" => floatval($cog),
                                "fmv" => floatval($fmv),
                                "qty" => intval($qty),
                                "active" => boolval($active),
                                "featured" => boolval($featured),
                                "img_url" => $img_url,
                                "download_url" => $download_url,
                                "category_id" => intval($category_id),
                                "category_name" => $category_name,
                                "vendor_id" => intval($vendor_id),
                                "vendor" => $vendor,
                                "vendor_code" => $vendor_code,
                                "sort_weight" => intval($sort_weight),
                                "date_created" => $date_created,
                                "variants" => array(
                                    "name" => $variant_name,
                                    "variations" => $variation_arr,
                                ),
                            );
                        }
                        if (!empty($premium_item)) {
                            array_push($premiums_arr["records"], $premium_item);
                        } else {
                            http_response_code(400); // Bad Request
                            throw new Exception("Database integrity issue. Could not properly read children for premium " . $id);
                        }
                    }
                } // end while for $stmt

                echo json_encode($premiums_arr);
            } else {
                http_response_code(400); // Bad Request
                throw new Exception("No premiums found");
            }
        }
    }
} catch (Exception $e) {
    echo json_encode(array(
        "message" => $e->getMessage(),
    ));
}
