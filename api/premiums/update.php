<?php
// Premiums
try {
	// ACL
	$access_levels = array("Admin");
	if (!in_array($access_level, $access_levels)) {
		// Unauthorized access level
		http_response_code(401); // unauthorized
		throw new Exception("[user] is unauthorized.");
	}

	if ($collection == "premiums") {

		// check to see if resource exists
		if (!empty($resource)) {
			// check to see if resource contains sub resource
			if (strpos($resource, '/') !== false) {
				// explodable
				$piece = explode("/", $resource);
				$resource = $piece[0]; // reassign resource
				$entity = $piece[1]; // entity
			}
		}

		// resource check
		if (!empty($resource) && in_array($resource, array('categories', 'vendors'), true)) {

			// Update Premium Category
			if ($resource == "categories") {
				// [Required Section]

				// set premium_category_id to be updated
				if (isset($entity)) { // is ID in URL?
					$premium_category->id = $entity;
				}
				if (isset($data->id)) { // is the id in the body ($data)
					$premium_category->id = $data->id;
				}

				if (!empty($data->name)) {
					$premium_category->name = $data->name;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[name] is required.");
				}
				// description
				if (!empty($data->description)) {
					$premium_category->description = $data->description;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[description] is required.");
				}

				// update the Premium Category
				$premium_category->update();
			}

			// Update Premium Vendor
			if ($resource == "vendors") {
				// [Required Section]

				// set premium_vendor_id to be updated
				if (isset($entity)) { // is ID in URL?
					$premium_vendor->id = $entity;
				}
				if (isset($data->id)) { // is the id in the body ($data)
					$premium_vendor->id = $data->id;
				}
				// company
				if (!empty($data->company)) {
					$premium_vendor->company = $data->company;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[company] is required.");
				}

				// contact
				if (!empty($data->contact)) {
					$premium_vendor->contact = $data->contact;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[contact] is required.");
				}
				// website
				if (!empty($data->website)) {
					$premium_vendor->website = $data->website;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[website] is required.");
				}
				// address
				if (!empty($data->address)) {
					$premium_vendor->address = $data->address;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[address] is required.");
				}
				// city
				if (!empty($data->city)) {
					$premium_vendor->city = $data->city;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[city] is required.");
				}
				// state
				if (!empty($data->state)) {
					$premium_vendor->state = $data->state;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[state] is required.");
				}
				// zip
				if (!empty($data->zip)) {
					$premium_vendor->zip = $data->zip;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[zip] is required.");
				}
				// phone
				if (!empty($data->phone)) {
					$premium_vendor->phone = $data->phone;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[phone] is required.");
				}
				// email
				if (!empty($data->email)) {
					$premium_vendor->email = $data->email;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[email] is required.");
				}

				// update the Premium Vendor
				$premium_vendor->update();
			}
		} else { // regular premium

			// is ID in URL ?
			if (!empty($resource)) {
				$premium->id = $resource;
			}

			// is ID in the body ($data) ?
			if (isset($data->id)) {
				$premium->id = $data->id;
			}

			// not found, throw error !
			if (empty($premium->id)) {
				http_response_code(400); // Bad Request
				throw new Exception("[id] is required");
			}

			// name
			if (!empty($data->name)) {
				$premium->name = $data->name;
			} else {
				http_response_code(400); // Bad Request
				throw new Exception("[name] is required.");
			}

			// price
			if (!empty($data->price)) {
				if (filter_var($data->price, FILTER_VALIDATE_FLOAT)) {
					$premium->price = $data->price;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[price] must be a float.");
				}
			} else {
				http_response_code(400); // Bad Request
				throw new Exception("[price] is required.");
			}

			// description
			if (!empty($data->description)) {
				$premium->description = $data->description;
			} else {
				http_response_code(400); // Bad Request
				throw new Exception("[description] is required.");
			}

			// category_id
			if (!empty($data->category_id)) {
				if (filter_var($data->category_id, FILTER_VALIDATE_INT)) {
					$premium->category_id = $data->category_id;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[category_id] must be an integer.");
				}
			} else {
				http_response_code(400); // Bad Request
				throw new Exception("[category_id] is required.");
			}

			// img_url
			if (!empty($data->img_url)) {
				if (filter_var($data->img_url, FILTER_VALIDATE_URL)) {
					$premium->img_url = $data->img_url;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[img_url] is not valid.");
				}
			} else {
				http_response_code(400); // Bad Request
				throw new Exception("[img_url] is required.");
			}

			// cog
			if (!empty($data->cog)) {
				if (filter_var($data->cog, FILTER_VALIDATE_FLOAT)) {
					$premium->cog = $data->cog;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[cog] must be a float.");
				}
			} else {
				http_response_code(400); // Bad Request
				throw new Exception("[cog] is required.");
			}

			// fmv
			if (!empty($data->fmv)) {
				if (filter_var($data->fmv, FILTER_VALIDATE_FLOAT)) {
					if ($data->fmv < $data->cog) {
						http_response_code(400); // Bad Request
						throw new Exception("[fmv] must be a greater than or equal to [cog].");
					}
					$premium->fmv = $data->fmv;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[fmv] must be a float.");
				}
			} else {
				http_response_code(400); // Bad Request
				throw new Exception("[fmv] is required.");
			}

			// qty
			if (isset($data->qty) && $data->qty >= 0) {
				if (filter_var($data->qty, FILTER_VALIDATE_INT, ["options" => ["min_range" => 0]]) !== false) {
					$premium->qty = $data->qty;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[qty] must be an integer greater than 0.");
				}
			} else {
				http_response_code(400); // Bad Request
				throw new Exception("[qty] is required.");
			}

			// active
			if (isset($data->active)) {
				if (NULL !== $data->active = filter_var($data->active, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE)) {
					$premium->active = $data->active;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[active] must be boolean.");
				}
			} else {
				http_response_code(400); // Bad Request
				throw new Exception("[active] is required.");
			}

			// [optional] section
			// vendor_id
			if (!empty($data->vendor_id)) {
				if (filter_var($data->vendor_id, FILTER_VALIDATE_INT)) {
					$premium->vendor_id = $data->vendor_id;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[vendor_id] must be an integer.");
				}
			} else {
				$premium->vendor_id = null;
			}

			// download_url
			if (isset($data->download_url) && !empty($data->download_url)) {
				$premium->download_url = $data->download_url;
			} else {
				$premium->download_url = null;
			}

			// featured
			if (isset($data->featured) && !empty($data->featured)) {
				$premium->featured = $data->featured;
			} else {
				$premium->featured = 0;
			}

			// vendor_code
			if (isset($data->vendor_code) && !empty($data->vendor_code)) {
				$premium->vendor_code = $data->vendor_code;
			} else {
				$premium->vendor_code = null;
			}

			// sort_weight
			if (isset($data->sort_weight) && !empty($data->sort_weight)) {
				if (filter_var($data->sort_weight, FILTER_VALIDATE_INT, ["options" => ["min_range" => 0]]) !== false) {
					$premium->sort_weight = $data->sort_weight;
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[sort_weight] must be an integer greater than 0.");
				}
			}

			// variant_name (such as Color)
			if (isset($data->variant_name) && !empty($data->variant_name)) {
				$premium->variant_name = $data->variant_name;
			} else {
				$premium->variant_name = null;
			}

			// Validation also in objects/premiums.php
			// Singularity (variant_name with variation_name or ParentID)
			if (!empty($data->variant_name) && (!empty($data->variation_name) || !empty($data->parentID))) {
				http_response_code(400); // Bad Request
				throw new Exception("A Parent premium must not contain a [parentID] or [variation_name]");
			}

			// Orphan (variation_name without parentID)
			if (!empty($data->variation_name) && empty($data->parentID)) {
				http_response_code(400); // Bad Request
				throw new Exception("A child premium must contain a [parentID]");
			}

			// Unnamed child
			if (!empty($data->parentID) && empty($data->variation_name)) {
				http_response_code(400); // Bad Request
				throw new Exception("A child premium must contain a [variation_name]");
			}


			// Single!
			if (!isset($data->parentID) && !isset($data->variant_name) && !isset($data->variation_name) && !isset($data->variants)) {
				// update the single premium
				$premium_id = $premium->update();
			}

			// Parent!
			if (isset($data->variant_name) && !empty($data->variant_name) && empty($data->variation_name)) {
				$premium->variant_name = $data->variant_name;
				// update the Parent premium, fetch the results, show as JSON
				$premium_id = $premium->update();
			} else {
				$premium->variant_name = null;
			}

			// Child! = parentID & variation_name (must not have variant_name)
			if (isset($data->parentID) && !empty($data->parentID) && isset($data->variation_name) && !empty($data->variation_name)) {
				$premium->parentID = $data->parentID;
				$premium->variation_name = $data->variation_name;
				// update the Child premium, fetch the results, show as JSON
				$premium_id = $premium->update();
			} else {
				$premium->variation_name = null;
				$premium->parentID = null;
			}

			// Nested! = if POST contains variants, iterate through and POST parent then children.
			if (isset($data->variants)) {
				// Parent
				if (isset($data->variants->name) && !empty($data->variants->name)) {
					$premium->variant_name = $data->variants->name;
					// null the child properties
					$premium->variation_name = null;
					$premium->parentID = null;

					// update the Parent
					$premium_id = $premium->update();
				}

				// Children
				if (!empty($data->variants->variations) && !empty($premium_id)) { // there's children to be updated

					// sanity checks 
					usort($data->variants->variations, function ($obj_a, $obj_b) {
						// sanity check variation_name
						if (!in_array($obj_a->name, array('Small', 'Medium', 'Large', 'XL', 'XXL', '3XL', '4XL', 'Black', 'White', 'One Size Fits Most', 'Post Mail', 'Download', 'Red', 'Blue'))) {
							http_response_code(400); // Bad Request
							throw new Exception("Child Premium [variations.name] must be 'Small', 'Medium', 'Large', 'XL', 'XXL', '3XL', '4XL', 'Black', 'White', 'One Size Fits Most', 'Post Mail', 'Download', 'Red', 'Blue'");
						}

						// uniqueness of child name
						if ($obj_a->name == $obj_b->name) {
							// Duplicate exists
							http_response_code(400); // Bad Request
							throw new Exception("Please remove duplicate child [variation_name]");
						}
					});

					// Array does not have duplicates
					foreach ($data->variants->variations as $child) {

						// null the parent properties
						$premium->variant_name = null;

						// assign parentID from above
						$premium->parentID = $premium_id;

						// update premium name
						$premium->name = "$data->name - $child->name";

						// reassign variation_name
						$premium->variation_name = $child->name;

						// reassign qty variable
						if (isset($child->qty) && $child->qty >= 0) {
							if (filter_var($child->qty, FILTER_VALIDATE_INT, ["options" => ["min_range" => 0]]) !== false) {
								$premium->qty = $child->qty;
							} else {
								http_response_code(400); // Bad Request
								throw new Exception("Child [qty] must be an integer greater than 0.");
							}
						} else {
							http_response_code(400); // Bad Request
							throw new Exception("Child [qty] is required.");
						}

						if (isset($child->id)) {
							// set premium id to child id
							$premium->id = $child->id;

							// update the child
							$premium->update();
						} else {
							// set parent_id ?

							// create the child
							$premium->create();
						}
					}
				} else {
					http_response_code(400); // Bad Request
					throw new Exception("[premium_id] must exist");
				}
			}

			if (!empty($premium_id)) {
				// Return the json for updated nested Premium.
				echo json_encode($premium->readPremium($premium_id)->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);
			}
		}
	} else {
		http_response_code(400); // Bad Request
		throw new Exception("[collection] is required.");
	}

	// if unable to update the premium, tell the user
} catch (Exception $e) {
	// check if premium_id exists
	if (isset($premium_id)) {
		// check and see if parent has no chilren
		if ($premium->readChildren($premium_id)->rowCount() == 0) {
			// set id to parent id
			$premium->id = $premium_id;
			$premium->delete();

			http_response_code(400); // Bad Request
			throw new Exception("[parentID] $premium_id was deleted. " . $e->getMessage());
		}
	}
	//  display message, write error to log, die.
	echo json_encode(array(
		"status" => "error",
		"message" => $e->getMessage(),
	));
	error_log($e->getMessage(), 0);
}
