<?php

use Kpfa\Entity\Program;

// Check Bearer Token
$headers = apache_request_headers();
if (
    !isset($headers["Authorization"]) ||
    $headers["Authorization"] !== "Bearer " . $cloudflare_show_sync_token
) {
    http_response_code(401);
    echo json_encode([
        "message" => "Unauthorized: Invalid Bearer Token.",
    ]);
    exit();
}

// Get the entity manager instance
$services = require __DIR__ . "/../bootstrap.php";
$entityManager = $services["entityManager"];

// Get posted data
$incomingJson = file_get_contents("php://input");
$programsData = json_decode($incomingJson);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode([
        "message" => "Invalid JSON provided. Error: " . json_last_error_msg(),
    ]);
    exit();
}

$createdPrograms = 0;
$updatedPrograms = 0;
$errors = [];

foreach ($programsData as $data) {
    if (!empty($data->wp_id)) {
        // Check if program with the wp_id already exists
        $existingProgram = $entityManager
            ->getRepository(Program::class)
            ->findOneBy(["wp_id" => $data->wp_id]);

        if ($existingProgram) {
            // Update the existing program with new data
            $program = $existingProgram;
            $updatedPrograms++;
        } else {
            // Create a new program instance
            $program = new Program();
            $createdPrograms++;
        }

        // Set program properties using data from POST request
        $program->setName($data->name);
        $program->setLink($data->link);
        $program->setWP_id($data->wp_id);
        $program->setCount($data->count);
        $program->setSlug($data->slug);
        $program->setKpfaB($data->kpfa_b);
        $program->setProgramCategory($data->program_category);
        $program->setShowTime($data->show_time);
        if (isset($data->confessor_id)) {
            $program->setConfessorId($data->confessor_id);
        }
        $program->setDefaultArchiveLength($data->default_archive_length);
        $program->setProgramInactive($data->program_inactive);
        $program->setDownloadableProgram($data->downloadable_program);

        try {
            if (isset($data->weeks_that_it_airs)) {
                $program->setWeeksThatItAirs($data->weeks_that_it_airs);
            }
            if (isset($data->days)) {
                $program->setDays($data->days);
            }

            $entityManager->persist($program);
            $entityManager->flush();
        } catch (TypeError $e) {
            if (strpos($e->getMessage(), "setWeeksThatItAirs") !== false) {
                $errors[] = [
                    "wp_id" => $data->wp_id,
                    "error" => $e->getMessage(),
                    "program_data" => $data,
                ];
            } else {
                $errors[] = [
                    "wp_id" => $data->wp_id,
                    "error" => $e->getMessage(),
                ];
            }
        } catch (Exception $e) {
            $errors[] = [
                "wp_id" => $data->wp_id,
                "error" => $e->getMessage(),
            ];
        }
    } else {
        $errors[] = "wp_id is missing for one of the programs.";
    }
}

$message = "";
if ($createdPrograms > 0) {
    $message .= "{$createdPrograms} program(s) were created. ";
}
if ($updatedPrograms > 0) {
    $message .= "{$updatedPrograms} program(s) were updated. ";
}

$response = [];
$response["message"] =
    $message !== "" ? $message : "No programs were created or updated.";
if (!empty($errors)) {
    $response["errors"] = $errors;
    http_response_code(400);
} else {
    http_response_code(201);
}
echo json_encode($response);

?>
