<?php

use Kpfa\Entity\Program;

// Get the entity manager instance
$services = require __DIR__ . "/../bootstrap.php";
$entityManager = $services["entityManager"];

// Search by Date
if (isset($_GET["d"])) {
    // get date
    $date = htmlspecialchars(strip_tags($_GET["d"]));

    // Query for programs based on the given date where program_inactive is 0.
    $stmt = $entityManager->getRepository(Program::class)->searchDate($date);

    $num = count($stmt);

    // check if more than 0 record found
    if ($num > 0) {
        // programs array
        $programs_arr = [];
        $programs_arr["records"] = [];

        // retrieve our table contents
        foreach ($stmt as $row) {
            $weeksArray = $row["weeks_that_it_airs"];

            $program_item = [
                "name" => $row["name"],
                "weeks_that_it_airs" => $weeksArray,
                "days" => $row["days"], // This assumes days is stored as an array in the database or is converted properly in the repository
            ];

            array_push($programs_arr["records"], $program_item);
        }

        echo json_encode($programs_arr);
    } else {
        http_response_code(404); // not found
        echo json_encode([
            "message" => "No programs found.",
        ]);
    }
}
