*🏴 Solidarity with those in public radio 🏴*
=====================================
       _____ __        __  _              ___       __          _
      / ___// /_____ _/ /_(_)___  ____   /   | ____/ /___ ___  (_)___ 
      \__ \/ __/ __ `/ __/ / __ \/ __ \ / /| |/ __  / __ `__ \/ / __ \
     ___/ / /_/ /_/ / /_/ / /_/ / / / // ___ / /_/ / / / / / / / / / /
    /____/\__/\__,_/\__/_/\____/_/ /_//_/  |_\__,_/_/ /_/ /_/_/_/ /_/  v.03
                                                                    

![](https://img.shields.io/badge/dynamic/json?color=blue&label=show&&query=streams.0.show&url=https%3A%2F%2Fapi.kpfa.org%2Fstats%2F?cacheSeconds=1)
![](https://img.shields.io/badge/dynamic/json?color=blue&label=streamers&&query=streams.0.streamers&url=https%3A%2F%2Fapi.kpfa.org%2Fstats%2F?cacheSeconds=3)
![](https://img.shields.io/badge/dynamic/json?color=blue&label=callers&&query=calls.callers&url=https%3A%2F%2Fapi.kpfa.org%2Fstats%2F?cacheSeconds=1)
![](https://img.shields.io/website.svg?label=kpfa.org&down_color=red&down_message=down&up_color=green&up_message=up&url=https%3A%2F%2Fkpfa.org)
![](https://img.shields.io/website.svg?label=secure.kpfa.org&down_color=red&down_message=down&up_color=green&up_message=up&url=https%3A%2F%2Fsecure.kpfa.org)
![](https://img.shields.io/website.svg?label=archives.kpfa.org&down_color=red&down_message=down&up_color=green&up_message=up&url=https%3A%2F%2Farchives.kpfa.org)
![](https://img.shields.io/website.svg?label=kpfa-tx&down_color=red&down_message=down&up_color=green&up_message=up&url=http%3A%2F%2F64.4.175.130)
![](https://img.shields.io/security-headers?ignoreRedirects&url=https%3A%2F%2Fapi.kpfa.org)
## About
*StationAdmin is currently in beta and is not production ready. We'd love your help testing!*

StationAdmin is a constituent relationship management (CRM) system designed to meet the needs of small to medium sized non-profit Radio Stations. It is a suite of tools that bring donors in closer relationship with their donations, station managers and hosts with their donors, and stats and reports for everyone.

StationAdmin is released as a RESTful API along with REACT front-ends for Station Management, Membership Management, Online Donations, and PhoneRoom Volunteers.


It is an open source project, licensed under [Creative Commons Attribution-NonCommercial 4.0](https://creativecommons.org/licenses/by-nc/4.0/), 
and coordinated by [KPFA](https://kpfa.org). The project website is COMING SOON!

## Requirements
    * PHP >= v8.0
    * Packages: `php-fpm php-mysql php-cli php-curl php-mbstring php-gd php-intl php-fpm unzip mysql-server mysql-client nginx composer`
    * MariaDB >= 10.2 OR MySQL >= 8.0
    * NGINX >= 1.14
    * Optional Packages `python3-certbot-nginx python3-certbot-dns-cloudflare ssh mosh sshfs snmp phpmyadmin`

## Installation
Use the dependency manager [composer](https://getcomposer.org/) to install the following.

```
    composer update
    config passwords in config/database.php
```

mysql
```
# install or copy db
CREATE USER '<api_user>'@'localhost' IDENTIFIED BY '<password>';
GRANT ALL PRIVILEGES ON <api_user>.* TO '<database>'@'localhost';
```

## Timeline
* [2018] Alpha
* [2019] Beta
* [2020] v.01
* [2021] v.02
* [2022] v.03

## Features
This API uses and conforms to the following:
* [REST](https://hackernoon.com/restful-api-designing-guidelines-the-best-practices-60e1d954e7c9)
* [CRUD](https://en.wikipedia.org/wiki/Create,_read,_update_and_delete)
* [JWT](https://jwt.io/)

## API Resources
All endpoints require authentication except login and `POST` to donations. Read endpoints are searchable with the use of `?s=query`

| Resource      | Create      | Read        | Update        | Delete
| ------------- |:------:|:------:|:------:|:------:|
| Login             | [✔] | [✖] | [✖] | [✖] |
| Accounts             | [✖] | [✔] | [✖] | [✖] |
| Campaigns    |   [✔] | [✔] | [✔] | [✔] |
| Donors    |   [✔] | [✔] | [✔] | [✖] |
| Donors/*Foundations*    |   [✖] | [✖] | [✖] | [✖] |
| Donors/*Volunteers*    |   [✖] | [✖] | [✖] | [✖] |
| Donors/*Staff*    |   [✖] | [✖] | [✖] | [✖] |
| Donations    |   [✔] | [✔] | [✔] | [✔] |
| Payments    |   [✔] | [✔] | [✔] | [✔] |
| Premiums    |   [✔] | [✔] | [✔] | [✔] |
| Premiums/*Categories*    |   [✔] | [✔] | [✔] | [✔] |
| Premiums/*Vendors*    |   [✔] | [✔] | [✔] | [✔] |
| Receipts    |   [✖] | [✔] | [✖] | [✖] |
| Shipments    |   [✖] | [✔] | [✔] | [✖] |
| Shows    |   [✖] | [✔] | [✖] | [✖] |
| Stats    |   [✔] | [✔] | [✖] | [✖] |
| Users    |   [✔] | [✔] | [✔] | [✖] |
| Email    |   [✔] | [ – ] | [ – ] | [ – ] |
| Mail    |   [✖] | [✖] | [✖] | [✖] |
| Station    |   [✖] | [✖] | [✖] | [✖] |

----
### Examples:

##### AUTHENTICATION #####
Via username / password or Google oAuth

##### AUTHORIZATIN #####
Handled in the header `Authorization` with the value `Bearer <token>`

HTTP response status codes will be returned.

##### CREATE (POST) #####
**[Donations]**
* [/donations/](/donations/)

**[Email]**
* [/email/](/email/)

**[Login]**
* [/login](/login)

**[Users]**
* [/users](/users)

##### READ (GET) #####
**[Accounts/donors]**
* [/accounts/donors/](/accounts/donors/)
* [/accounts/donors/99367](/accounts/donors/99367)
* [/accounts/donors/?s=test](/accounts/donors/?s=test)

**[Donations]**
* [/donations/](/donations/)
* [/donations/80385](/donations/80385)
* [/donations/?s=test](/donations/?s=test)
* [/donations/?start=2019-03-01&end=2019-03-28](/donations/?start=2019-03-01&end=2019-03-28)
* [/donations/?start=2019-04-27&end=2019-07-28&campaign_id=27](/donations/?start=2019-04-27&end=2019-07-28&campaign_id=27)
* [/donations/?start=2019-03-01&end=2019-03-28&status=unpaid](/donations/?start=2019-03-01&end=2019-03-28&status=unpaid)
* [/donations/?campaign_id=27](/donations/?campaign_id=27)
* [/donations/?campaign_id=27&status=unpaid](/donations/?campaign_id=27&status=unpaid)

**[Payments]**
* [/payments/](/payments/)
* [/payments/32004/](/payments/32004/)
* [/payments/?s=test](/payments/?s=test) `search by: donor name, email, donation_id`

**[Receipts]**
* [/receipts/[transaction_id]](/receipts/[transaction_id]) `HTML`
* [/receipts/[transcation_id]?json](/receipts/[transaction_id]?json) `json`
* [/receipts/[transcation_id]?pdf](/receipts/[transaction_id]?pdf) `PDF`

**[Shipping]**
* [/shipping/](/shipping/)
* [/shipping/?start=2019-04-01&end=2019-04-06&status=New](/shipping/?start=2019-04-01&end=2019-04-06&status=New)

**[Campaigns]**
* [/campaigns/](/campaigns/)
* [/campaigns/27](/campaigns/27)
* [/campaigns/?s=Spring 2019](/campaigns/?s=Spring+2019)

**[Premiums]**
* [/premiums/](/premiums/)
* [/premiums/101](/premiums/101)
* [/premiums/?s=hoodie](/premiums/?s=hoodie)
* [/premiums/categories](/premiums/categories)
* [/premiums/vendors](/premiums/vendors)

**[Stats]**
* [/stats](/stats) 
* [/stats/?dt=2019-04-30 12:00:00](/stats?dt=2019-04-30%2012:00:00) `Stats at a given datetime`
* [/stats/?d=2019-04-30](/stats?d=2019-04-30) `Stats at a given date`

**[Shows]**
* [/shows/](/shows/)
* [/shows/?s=rude](/shows/?s=rude)
* [/shows/?current=yes](/shows/?current=yes)
* [/shows/?dt=2019-04-30 12:00:00](/shows/?dt=2019-04-30%2012:00:00)
* [/shows/?d=2019-04-30](/shows/?d=2019-04-30)


##### UPDATE (PUT) #####
**[Shipping]**
* [/shipping/](/shipping/)

**[Donations]**
* [/donations/](/donations/)

##### UPDATE (PATCH) #####
**[Accounts]**
* [/accounts/donors](/accounts/donors/)

##### DELETE (DELETE) #####
* `Coming soon!`

----

##### Data Types - Donations #####

**[Required Section]**

| Field      | JSON      | Limit        
| ------------- |:------:|:------:|
| firstname             | string | varchar(225) 
| lastname             | string | varchar(225) 
| source             |  string | ('WebSite','CallCenter','PhoneRoom','StationAdmin','Testing','PaySol','Allegiance')
| firstname             |  string | 
| lastname             |  string | 
| address1             |  string | 
| city             |  string | 
| state             |  string | 
| country             |  string | 
| amount             |  number | 
| postal_code             |  string | 
| installment             |  string | ('One-Time','Monthly')



**[Optional Section]** - NULL is an acceptable value

| Field      | JSON      | Limit        
| ------------- |:------:|:------:|
| type             |  string | ('Pledge','In-Kind','Vehicle','Bequest','Corporate Match','QCD', 'Donor Advised Fund')
| method      |  string | ('card','check','cash','in_kind','bank_account')
| email             | string | 
| address2             | string | 
| shipping_firstname             | string | 
| shipping_lastname             | string | 
| shipping_address1             | string | 
| shipping_address2             | string | 
| shipping_city             | string | 
| shipping_state             | string | 
| shipping_country             | string | 
| shipping_postal_code             | string | 
| phone             | number | 
| cardnumber             | number | (13-19 digits)
| exp_month             | number | (2 digits)
| exp_year             | number | (4 digits)
| cc_securitycode             | number|  (3-4 digits)
| cardtype             | string | 
| comments             | string | (1-499 characters)
| read_onair             | boolean | 
| donation_match             | boolean | 
| add_me             | boolean | 
| premiums             | array | 
| premiums_cart             | array | 


----
## Support
Sliding-scale support is available from KPFA

## License
This project is licensed under the [Creative Commons Attribution-NonCommercial 4.0](https://creativecommons.org/licenses/by-nc/4.0/).

## Authors and acknowledgment
* [KPFA 94.1FM](https://kpfa.org) Engineering Department
