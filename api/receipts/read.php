<?php
// donation
// gift + shipping_address
// paymnt(s)
$fmt = new NumberFormatter('en_US', NumberFormatter::CURRENCY);
$TOTALPayments = 0;

// set transaction_id property of donation to be read
if (empty($resource)) {
    http_response_code(400);
    die();
}
$receipt->transaction_id = isset($resource) ? $resource : die();

// Receipts
try {
    // READ donation
    if ((!isset($_GET["s"])) && (isset($resource))) {
        // query donation
        $stmt = $receipt->readDonationByTransactionID($resource);
        $donationCount = $stmt->rowCount();

        // check if more than 0 records found
        if ($donationCount >= 1) {
            // donation array
            $donation_arr = array();

            // retrieve our table contents
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                extract($row);
                // reset premiums array
                $premiums_arr = array();
                // reset premiums_ID array
                $premiums_ID_arr = array();

                // read premiumGifts table for each transaction
                $stmt2 = $receipt->readGifts($donation_id);
                // number of rows
                $num2 = $stmt2->rowCount();
                // check if more than 0 records found
                if ($num2 > 0) {
                    // retrieve our table contents
                    while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
                        extract($row2);
                        // create array
                        $premium_item = array(
                            "id" => intval($premium_id),
                            "name" => $name,
                            "price" => floatval($price),
                            "cog" => floatval($cog),
                            "fmv" => floatval($fmv),
                            "qty" => intval($qty),
                            "download_url" => $download_url,
                            "img_url" => $img_url,
                            "category" => $category,
                            "shipment_status" => $shipment_status,
                        );
                        array_push($premiums_arr, $premium_item); // add premium item to premiums array
                        array_push($premiums_ID_arr, $premium_item['id']); // add premium ID to premiums_ID array
                    } // while

                } // if
                // reset payments array
                $payments_arr = array();

                // read Payments table for each donation
                $stmt3 = $receipt->readPayments($donation_id);
                // number of rows
                $num3 = $stmt3->rowCount();
                // check if more than 0 records found
                if ($num3 > 0) {
                    // retrieve our table contents
                    while ($row3 = $stmt3->fetch(PDO::FETCH_ASSOC)) {
                        extract($row3);
                        // create array
                        $payment_item = array(
                            "processor" => $processor,
                            "method" => $method,
                            "card_type" => $card_type,
                            "brand" => $brand,
                            "amount" => floatval($amount),
                            "amount_refunded" => floatval($amount_refunded),
                            "status" => $payment_status,
                            "last4" => $last4,
                            "exp_month" => $exp_month,
                            "exp_year" => filter_var($exp_year, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                            "payment_id" => $payment_id,
                            "date_created" => $date_created,
                            "date_updated" => $date_updated,
                            "date_deposited" => $date_deposited,

                        );
                        array_push($payments_arr, $payment_item); // add premium item to premiums array
                    } // while
                } //if
                // Build the Donation array
                // paid_status
                $payment_amount = 0;
                $total_amount_refunded = 0;
                // total amount_refunded
                foreach ($payments_arr as $payment_item) {
                    if ($payment_item['status'] == "succeeded") {
                        $payment_amount += $payment_item["amount"];
                        $total_amount_refunded += $payment_item['amount_refunded'];
                    }
                }
                if ((floatval($payment_amount) >= floatval($donation_amount)) && (floatval($amount_refunded) == 0)) {
                    $status = "paid";
                }
                if ($payment_amount >= $donation_amount && floatval($amount_refunded) > 0) {
                    $status = "refunded";
                }
                if (($payment_amount < $donation_amount) && ($payment_amount) > 0) {
                    $status = "Partially Paid";
                }
                if ($payment_amount == 0) {
                    $status = "unpaid";
                }

                $donation_item = array(
                    "id" => filter_var($donation_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                    "account_id" => filter_var($account_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                    "donor_id" => filter_var($donor_id, FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE),
                    "transaction_id" => $transaction_id,
                    "timestamp" => $timestamp,
                    "firstname" => $firstname,
                    "lastname" => $lastname,
                    "partner_firstname" => $partner_firstname,
                    "partner_lastname" => $partner_lastname,
                    "address1" => $address1,
                    "address2" => $address2,
                    "city" => $city,
                    "state" => $state,
                    "country" => $country,
                    "postal_code" => $postal_code,
                    "shipping_firstname" => $shipping_firstname,
                    "shipping_lastname" => $shipping_lastname,
                    "shipping_address1" => $shipping_address1,
                    "shipping_address2" => $shipping_address2,
                    "shipping_city" => $shipping_city,
                    "shipping_state" => $shipping_state,
                    "shipping_country" => $shipping_country,
                    "shipping_postal_code" => $shipping_postal_code,
                    "phone" => $phone,
                    "email" => filter_var($email, FILTER_VALIDATE_EMAIL),
                    "type" => $type,
                    "amount" => floatval($donation_amount),
                    "status" => $status,
                    "installment" => $installment,
                    "comments" => $comments,
                    "add_me" => filter_var($add_me, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                    "read_onair" => filter_var($read_onair, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                    "donation_match" => filter_var($donation_match, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                    "show_name" => $show_name,
                    "source" => $source,
                    "campaign_id" => intval($campaign_id),
                    "campaign" => $campaign,
                    "updated" => $updated,
                    "premiums_ID" => $premiums_ID_arr,
                    "premiums" => $premiums_arr,
                    "payments" => $payments_arr,
                );

                // Push the donation item into the donation array
                array_push($donation_arr, $donation_item);
            } // end while for $stmt
            $receipt = (object) $donation_arr[0];
            // create array (for json)
            $receipts_arr = array(
                "id" => intval($receipt->id),
                "donor_id" => intval($receipt->donor_id),
                "timestamp" => $receipt->timestamp,
                "firstname" => $receipt->firstname,
                "lastname" => $receipt->lastname,
                "partner_firstname" => $receipt->partner_firstname,
                "partner_lastname" => $receipt->partner_lastname,
                "address1" => $receipt->address1,
                "address2" => $receipt->address2,
                "city" => $receipt->city,
                "state" => $receipt->state,
                "country" => $receipt->country,
                "postal_code" => $receipt->postal_code,
                "shipping_firstname" => $receipt->shipping_firstname,
                "shipping_lastname" => $receipt->shipping_lastname,
                "shipping_address1" => $receipt->shipping_address1,
                "shipping_address2" => $receipt->shipping_address2,
                "shipping_city" => $receipt->shipping_city,
                "shipping_state" => $receipt->shipping_state,
                "shipping_country" => $receipt->shipping_country,
                "shipping_postal_code" => $receipt->shipping_postal_code,
                "phone" => $receipt->phone,
                "email" => $receipt->email,
                "amount" => floatval($receipt->amount),
                "type" => $receipt->type,
                "installment" => $receipt->installment,
                "comments" => $receipt->comments,
                "add_me" => filter_var($receipt->add_me, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                "read_onair" => filter_var($receipt->read_onair, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                "donation_match" => filter_var($receipt->donation_match, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
                "transaction_id" => $receipt->transaction_id,
                "show_name" => $receipt->show_name,
                "source" => $receipt->source,
                "status" => $receipt->status,
                "campaign_id" => $receipt->campaign_id,
                "updated" => $receipt->updated,
                "premiums" => $receipt->premiums,
                "payments" => $receipt->payments,
                "payment_amount" => floatval($payment_amount),
            );
            // set variables
            $donation_id = $receipt->id;
            $donor_id = $receipt->donor_id;
            $timestamp = $receipt->timestamp;
            $firstname = $receipt->firstname;
            $lastname = $receipt->lastname;
            $partner_firstname = $receipt->partner_firstname;
            $partner_lastname = $receipt->partner_lastname;
            $name = "$firstname $lastname";
            $address1 = $receipt->address1;
            $address2 = $receipt->address2;
            $city = $receipt->city;
            $state = $receipt->state;
            $country = $receipt->country;
            $postal_code = $receipt->postal_code;
            $shipping_firstname = $receipt->shipping_firstname;
            $shipping_lastname = $receipt->shipping_lastname;
            $shipping_name = "$shipping_firstname $shipping_lastname";
            $shipping_address1 = $receipt->shipping_address1;
            $shipping_address2 = $receipt->shipping_address2;
            $shipping_city = $receipt->shipping_city;
            $shipping_state = $receipt->shipping_state;
            $shipping_country = $receipt->shipping_country;
            $shipping_postal_code = $receipt->shipping_postal_code;
            $phone = $receipt->phone;
            $email = $receipt->email;
            $donation_amount = number_format((float) $receipt->amount, 2, '.', '');
            $donation_type = $receipt->type;
            $installment = $receipt->installment;
            $comments = $receipt->comments;
            $add_me = $receipt->add_me;
            $read_onair = $receipt->read_onair;
            $transaction_id = $receipt->transaction_id;
            $show_name = $receipt->show_name;
            $source = $receipt->source;
            $status = $receipt->status;
            $campaign_id = $receipt->campaign_id;
            $updated = $receipt->updated;
            $donation_match = $receipt->donation_match;
            $premiums = $receipt->premiums;
            $payments = $receipt->payments;

            // payment_balance
            $receipts_arr["payment_balance"] = $payment_balance = floatval($donation_amount - $payment_amount + $total_amount_refunded);

            $formatted_phone = preg_replace("/^(\d{3})(\d{3})(\d{4})$/", "$1-$2-$3", $phone);
            // Record type
            // The ternary operator is a shorthand for the if {} else {} $result = $condition ? 'true' : 'false';
            $recordtype = ($status == "paid") ? 'RECEIPT' : 'INVOICE';
        } else {
            $recordtype = "Not Found";
        }
    } else {
        http_response_code(404); // not found
        throw new Exception("[transaction_id] not found");
    }

    //////////////////////////////////////////////////////// output ////////////////////////////////////////////////////////
    switch (true) {
        case (isset($_GET["json"]) && ($donationCount >= 1)): // JSON (200)
            header("Content-Type: application/json; charset=UTF-8");
            echo json_encode($receipts_arr, JSON_NUMERIC_CHECK);
            break;
        case (isset($_GET["json"]) && ($donationCount == 0)): // JSON (404)
            http_response_code(404); // not found
            throw new Exception("Donation not found");
            break;
        case (isset($_GET["pdf"]) && ($donationCount == 0)): //PDF (404)
            http_response_code(404); // Not Found
            break;
        case (!isset($_GET["json"])): // Generate HTML (for HTML and PDF)
            header("Content-Type: text/html; charset=UTF-8");
            ob_start();

?>
            <!DOCTYPE html>
            <html lang="en">

            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
		<meta name="robots" content="noindex, nofollow, nosnippet, noarchive" />
                <link rel="stylesheet" href="/vendor/twbs/bootstrap/dist/css/bootstrap.min.css">
                <link rel="stylesheet" href="/assets/css/thankyou.css">
                <link rel="stylesheet" href="/assets/css/card.css">
                <title>KPFA DONATION <?= $recordtype ?></title>
                <meta name="description" content="KPFA DONATION <?= $recordtype ?>">
                <meta name="author" content="KPFA 94.1 FM">
            </head>

            <body>
                <div class="main">
                    <div class="container page">
                        <div class="row">

                            <div class="col-md-2">
                            </div>

                            <div class="col-md-8">

                                <div class="invoice-box">
                                    <?php if ($donationCount >= 1) { // donation exists!
                                    ?>
                                        <table>
                                            <tr>
                                                <td>
                                                    <img src="/assets/images/KPFA_logo_wide.png" alt="KPFA Logo" style="width:100%; max-width:300px;"><br />
                                                </td>

                                                <td>
                                                    <strong>DONATION <?= $recordtype ?></strong><br>
                                                    <time datetime="<?= date('Y-m-d', strtotime($timestamp)) ?>">
                                                        <?= date('F jS, Y', strtotime($timestamp)) ?></time><br>
                                                    Donation #<?= $donation_id ?><br>
                                                </td>

                                            </tr>
                                        </table>
                                        <hr />
                                        <table>
                                            <tr>
                                                <td>
                                                    <h6>Billing Address:</h6>
                                                    <strong><?= $name ?></strong><br>
                                                    <?php
                                                    if (!empty($partner_firstname)) {
                                                        echo "$partner_firstname";
                                                        if (!empty($partner_lastname)) {
                                                            echo "&nbsp;$partner_lastname";
                                                        }
                                                        echo "<br>";
                                                    }
                                                    ?><?= $address1 ?><br>
                                                    <?php
                                                    if (!empty($address2)) {
                                                        echo "$address2 <br>";
                                                    }
                                                    ?>
                                                    <?php echo "$city, $state $postal_code"; ?>
                                                    <br>
                                                    <?= $country ?><br><br>
                                                    <?= $formatted_phone ?><br>
                                                    <?= $email ?>
                                                </td>
                                                <?php if (!empty($shipping_name)) { ?>
                                                    <td>
                                                        <h6>Shipping Address:</h6>
                                                        <strong><?= $shipping_name ?></strong><br>
                                                        <?= $shipping_address1 ?><br>
                                                        <?php
                                                        if (!empty($shipping_address2)) {
                                                            echo "$shipping_address2 <br>";
                                                        }
                                                        ?>
                                                        <?php echo "$shipping_city, $shipping_state $shipping_postal_code"; ?>
                                                        <br>
                                                        <?= $shipping_country ?><br>
                                                    </td>
                                                <?php } ?>
                                            </tr>
                                        </table>
                                        <br>
                                        <table>
                                            <tr class="heading">
                                                <td>
                                                    Donation
                                                </td>

                                                <td>
                                                    Amount
                                                </td>
                                            </tr>

                                            <tr class="details">
                                                <td>
                                                    <?= $donation_type ?>
                                                </td>

                                                <td>
                                                    <?= "$$donation_amount" ?>
                                                    <?= ($installment !== "One-Time") ? " " . $installment : null ?>
                                                </td>
                                            </tr>
                                            <?php // If premium array not empty
                                            $FMVtotal = 0;
                                            if (!empty($premiums)) {
                                            ?>
                                                <tr class="heading">
                                                    <td>Gift</td>
                                                    <td></td>
                                                </tr>
                                            <?php // Loop for Premiums
                                                //FIXME if premium no longer available then display what?
                                                $i = 0;
                                                $num = (count($premiums));
                                                foreach ($premiums as $i => $element) {
                                                    if ($element['shipment_status'] === "New") {
                                                        $element['shipment_status'] = "Processing";
                                                    }
                                                    echo "<tr class=\"item\">";
                                                    if (!empty($element['download_url']) && $status == "paid") { //Only show if paid!
                                                        echo "<td><a href=\"" . $element['download_url'] . "\">" . $element['name'] . "</td></a>";
                                                    } else {
                                                        echo "<td>" . $element['name'] . "</td>";
                                                    }
                                                    echo "<td><img src=\"" . $element['img_url'] . "\" height=\"25\"  alt=\"premium image\"/>\n<br />(<a href=\"#\" class=\"tip\" data-toggle=\"tooltip\"  data-original-title=\"Shipment Status\">" . $element['shipment_status'] . " 📦</a>)
                                                    </td>";

                                                    echo "</tr>";
                                                }
                                                $FMVtotal = array_sum(array_column($premiums, 'fmv'));
                                            }
                                            ?>


                                            <?php
                                            if (!empty($payments)) {
                                            ?>
                                                <tr class="heading">
                                                    <td>Payments</td>
                                                    <td></td>
                                                </tr>
                                                <?php // Loop for Payments
                                                $i = 0;
                                                $num = (count($payments));

                                                foreach ($payments as $i => $payment) {
                                                    echo "<tr class=\"item\">";
                                                    echo "<td>";

																																																				switch ($payment['method']) {
																																																						case "us_bank_account":
																																																										echo "Bank Account " . $payment['last4'];
																																																										break;
																																																						case "card":
																																																										echo $payment['brand'] . " " . $payment['last4'];
																																																										break;
																																																						default:
																																																										echo $payment['method'] . " #" . $payment['payment_id'];
																																																										break;
																																																				}
															
                                                    echo " on ";
                                                    if ($payment['method'] == "card") {
                                                        echo date_format(date_create($payment['date_created']), "m/d/Y ");
                                                        if ($payment['status'] == "succeeded" && $payment['amount_refunded'] == $payment['amount']) { // full refund
                                                            echo " (refunded)";
                                                        }
                                                        if ($payment['status'] == "succeeded" && $payment['amount_refunded'] < $payment['amount'] && $payment['amount_refunded'] > 0) { // partial refund
                                                            echo " (partially refunded)";
                                                        }
                                                    } else {
                                                        echo date_format(date_create($payment['date_deposited']), "m/d/Y");
                                                    }
                                                    echo "</td>";
                                                    if ($payment['status'] == "succeeded" && $payment['amount_refunded'] < $payment['amount'] && $payment['amount_refunded'] == 0) {
                                                        echo "<td>" . $fmt->formatCurrency($payment['amount'], 'USD') . "</td>";
                                                        $TOTALPayments += $payment['amount']; // total payment amount
                                                    }
                                                    if ($payment['status'] == "succeeded" && $payment['amount_refunded'] == $payment['amount']) { // full refund
                                                        echo "<td><strike>" . $fmt->formatCurrency($payment['amount'], 'USD') . "</strike></td>";
                                                    }
                                                    if ($payment['status'] == "succeeded" && $payment['amount_refunded'] < $payment['amount'] && $payment['amount_refunded'] > 0) { // partial refund
                                                        echo "<td><strike>" . $fmt->formatCurrency($payment['amount'], 'USD') . "</strike>" . $fmt->formatCurrency($payment['amount'] - $payment['amount_refunded'], 'USD') . "</td>";
                                                    } else {
                                                        // echo "<td>" . $payment['status'] . "</td>";
                                                    }

                                                    echo "</tr>";
                                                }
                                                ?>

                                            <?php }
                                            ?>

                                            <tr class="total">
                                                <td></td>
                                                <td>
                                                    <?php
                                                    if (isset($installment)) {
                                                        // set format and amount
                                                        $TAXdeducsum = $fmt->formatCurrency($TOTALPayments - $FMVtotal, 'USD');
                                                        if ($TOTALPayments - $FMVtotal >= 0) {
                                                            echo "Tax Deductible Amount: $TAXdeducsum";
                                                        } else {
                                                            echo "Amount Due: " . $fmt->formatCurrency($payment_balance, 'USD');
                                                        }
                                                    }
                                                    ?>

                                        </table>
                                        <br>
                                        <hr />
                                        <table class="bg-light">
                                            <tr>
                                                <td>
                                                    <?php if (empty($payments)) { ?>
                                                        <address>
                                                            <strong>KPFA</strong><br>
                                                            P.O. BOX 11408<br>
                                                            Berkeley, CA 94712-2408<br>
                                                            United States of America<br><br>
                                                            510-848-6767 ext. 610<br>
                                                            <EMAIL><br>
                                                            <strong>EIN #94-1347046</strong>
                                                        </address>
                                                    <?php } else { ?>
                                                        <address>
                                                            <strong>KPFA</strong><br>
                                                            1929 Martin Luther King Jr. Way<br>
                                                            Berkeley, CA 94704<br>
                                                            United States of America<br><br>
                                                            510-848-6767 ext. 610<br>
                                                            <EMAIL><br>
                                                            <strong>EIN #94-1347046</strong>
                                                        </address>
                                                    <?php } ?>

                                                </td>
                                                <td>
                                                    <strong>Note:</strong><br>
                                                    <?php if ($status == "refunded") { ?>
                                                        Your donation was <strong>refunded</strong> $<?= $amount_refunded ?><br> on <?= $updated ?><br><br>
                                                        <img src="/assets/images/refunded.svg" alt="Paid Pledge Stamp" style="width:100%; max-width:120px;">
                                                    <?php } ?>
                                                    <?php if ($status == "unpaid" || $status == "Partially Paid") { ?>
                                                        To fulfil your pledge,<br>
                                                        mail a check for <?= $fmt->formatCurrency($payment_balance, 'USD') ?><br>
                                                        Include pledge #<?= $donation_id ?> in the memo<br>
                                                        Or click the button below<br><br>
                                                        <!-- Button trigger modal -->
                                                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#ModalCenter">
                                                            Pay Now!
                                                        </button>

                                                        <!-- Modal -->
                                                        <div class="modal fade" id="ModalCenter" tabindex="-1" role="dialog" aria-labelledby="ModalCenterTitle" aria-hidden="true">
                                                            <div class="modal-dialog modal-dialog-centered" role="document">
                                                                <div class="modal-content">
                                                                    <div class="modal-header">
                                                                        <h5 class="modal-title" id="ModalLongTitle">Secure Payment</h5>
                                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                            <span aria-hidden="true">&times;</span>
                                                                        </button>
                                                                    </div>

                                                                    <div class="modal-body">
                                                                        <div class="creditCardForm">
                                                                            <div class="payment">
                                                                                <form>
                                                                                    <div class="form-group owner" id="card-number-field">
                                                                                        <label for="cardNumber">Card Number</label>
                                                                                        <input type="text" class="form-control" id="cardNumber" autocomplete="cc-number">
                                                                                    </div>
                                                                                    <div class="form-group CVV">
                                                                                        <label for="cvv">CVV</label>
                                                                                        <input type="text" class="form-control" id="cvv" autocomplete="cc-csc">
                                                                                    </div>
                                                                                    <div class="form-group" id="expiration-date">
                                                                                        <label>Expiration Date</label>
                                                                                        <select id="exp_month">
                                                                                            <option value="01">01 - January</option>
                                                                                            <option value="02">02 - February </option>
                                                                                            <option value="03">03 - March</option>
                                                                                            <option value="04">04 - April</option>
                                                                                            <option value="05">05 - May</option>
                                                                                            <option value="06">06 - June</option>
                                                                                            <option value="07">07 - July</option>
                                                                                            <option value="08">08 - August</option>
                                                                                            <option value="09">09 - September</option>
                                                                                            <option value="10">10 - October</option>
                                                                                            <option value="11">11 - November</option>
                                                                                            <option value="12">12 - December</option>
                                                                                        </select>
                                                                                        <select id="exp_year">
                                                                                            <option value="22">2022</option>
                                                                                            <option value="23">2023</option>
                                                                                            <option value="24">2024</option>
                                                                                            <option value="25">2025</option>
                                                                                            <option value="26">2026</option>
                                                                                            <option value="27">2027</option>
                                                                                            <option value="28">2028</option>
                                                                                            <option value="29">2029</option>
                                                                                            <option value="30">2030</option>
                                                                                            <option value="31">2031</option>
                                                                                            <option value="21">2032</option>
                                                                                        </select>
                                                                                    </div>
                                                                                    <div class="form-group" id="credit_cards">
                                                                                        <img src="/assets/images/visa.svg" id="visa" height="39" alt="visa card logo">
                                                                                        <img src="/assets/images/mastercard.svg" id="mastercard" height="39" alt="mastercard card logo">
                                                                                        <img src="/assets/images/amex.svg" id="amex" height="39" alt="amex card logo">
                                                                                        <img src="/assets/images/discover.svg" id="discover" height="39" alt="discover card logo">
                                                                                    </div>

                                                                            </div>
                                                                            <div class="text-center">
                                                                                <p class="text-muted credit">
                                                                                    <img src="/assets/images/ssl.svg" id="visa" height="25" alt="secure lock icon">
                                                                                    KPFA takes great care in <a href="https://www.ssllabs.com/ssltest/analyze.html?d=<?= $_SERVER['SERVER_NAME'] ?>&hideResults=on">securing</a> your payment.
                                                                                </p>
                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                        <button type="submit" class="btn btn-primary" id="submit">Pay <?= $fmt->formatCurrency($payment_balance, 'USD') ?></button>
                                                                    </div>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php } ?>
                                                    <?php if ($status == "paid") { ?>
                                                        <?php if (!empty($premiums)) { ?>
                                                            Please allow at least 6-8 weeks <br>after fund drive for your <br> gift to be mailed to you.
                                                        <?php } ?>

                                                        <?php if (empty($premiums)) { ?>
                                                            Thank you for your generous support!
                                                        <?php } ?>
                                                        <br><br><strong>Update: </strong><a class="btn btn-primary btn-sm" href="https://billing.stripe.com/p/login/eVa5m70He0uZ97q8ww" role="button">Account info</a>
                                                        <br><br><img src="/assets/images/paid-stamp.png" alt="Paid Pledge Stamp" style="width:100%; max-width:120px;">
                                                    <?php } ?>

                                                </td>
                                            </tr>
                                        </table>
                                    <?php } else { // if id is not found ...
                                        http_response_code(404); // Not Found
                                        global $donor_dir_email;
                                        echo "<h1 class=\"text-center\">Pledge not found</h1><h2 class=\"text-center\">Please contact the station at <a href=\"mailto:$donor_dir_email\">$donor_dir_email</a></h2>";
                                    }
                                    ?>
                                    <div class="col-md-2">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script src="/vendor/components/jquery/jquery.min.js"></script>
                <script src="/vendor/twbs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
                <script src="/assets/js/jquery.payform.min.js" charset="utf-8"></script>
                <script src="/assets/js/script_new.js"></script>
                <script>
                    $(function() {
                        $('[data-toggle="tooltip"]').tooltip()
                    })
                </script>
            </body>

            </html>
<?php
            $receipt_html = ob_get_contents();
            ob_end_clean();

            if (isset($_GET["pdf"]) && ($donationCount >= 1)) { //PDF (200)
                $mpdf = new \Mpdf\Mpdf(['debug' => false, 'mode' => 'utf-8']);
                $mpdf->setBasePath(__DIR__); // set path relative to cwd

                $stylesheet = file_get_contents(__DIR__ . '/../assets/css/thankyou.css');
                $stylesheet2 = file_get_contents(__DIR__ . '/../vendor/twbs/bootstrap/dist/css/bootstrap.min.css');

                $mpdf->SetTitle('');
                $mpdf->WriteHTML($stylesheet, \Mpdf\HTMLParserMode::HEADER_CSS); // box-shadow bug
                $mpdf->WriteHTML($receipt_html, \Mpdf\HTMLParserMode::HTML_BODY);

                // I is for browser output, F is for local save, S is to return as string and D is for download
                //$mpdf->Output("$name-$donation_id.pdf", 'D');

                $mpdf->Output("$name-$donation_id.pdf", 'I');
            } else {
                echo $receipt_html; //HTML (200 or 404)
            }
            break;
    }
} catch (Exception $e) {
    //  display message, write error to log, die.
    header("Content-Type: application/json; charset=UTF-8");
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    error_log($e->getMessage(), 0);
}
