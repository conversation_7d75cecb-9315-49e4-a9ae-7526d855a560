<?php
exit('bye felicia');
error_reporting(E_ALL);
ini_set('display_errors', 1);
try {
// Stripe Config
    require_once __DIR__ . '/../config/stripe.php'; //

    // $amount+=100 - Increase the loop counter value by 100 for each iteration
    
    // this function creates products for every dollar from 1 - $1000
    for ($amount = 100; $amount <= 100000; $amount += 100) {
        // create price with monthly interval for $amount
        $prices = $stripe->prices->create([
            'unit_amount' => $amount,
            'currency' => 'usd',
            'recurring' => ['interval' => 'month'],
            'product' => 'prod_Iu6NoGJQH5L35H', //prod_Iu6NoGJQH5L35H = live
        ]);
        // response goes to webhook which creates price.
        echo "$prices->id for $prices->unit_amount \n";
    }
} catch (Exception $e) {
    die($e->getMessage()."\n");
}