<?php
exit('bye felicia');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Stripe Config
require_once __DIR__ . '/../config/stripe.php';

// Require database
require_once __DIR__ . '/../config/database.php';

// instantiate database
$database = new Database();
$db = $database->getConnection();
$db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$charge = null;

// random charge with no customer // ch_1FF1DrDkFAcougr4Z1WsrgsQ
$charge = \Stripe\Charge::retrieve(
    'ch_1FDyOtDkFAcougr4OpZe5cEx'
);
// charge example of installment // py_1FCvnKDkFAcougr4p3r9tyGZ
// $charge = \Stripe\Charge::retrieve([
//     'id' => 'py_1FCvnKDkFAcougr4p3r9tyGZ',
//     'expand' => ['customer'],
// ]);

//print_r($charge);
if (!empty($charge->customer->subscriptions->data)) {$installment = "Monthly";} else { $installment = "One-Time";}
echo "\n\n installment: $installment\n\n";
print_r ($charge->customer->subscriptions);
echo "\n\n installment: $installment\n\n";
