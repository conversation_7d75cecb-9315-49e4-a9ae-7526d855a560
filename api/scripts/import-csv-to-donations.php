<?php
//exit('bye felicia');
ini_set('max_execution_time', 0); // disable time limit
// this script reads a CSV file, searches for existing donor
// if donor found: create/update donation
// if donor not found: create donor

// allegiance.fingerscrossed
// AcctID    FirstName    LastName    EmailAddr    TeleNum    Addr1    Addr    City    StateProv    Cntry    USZipCode    PlgID    PlgNum    PlgAmt    PlgDate    PmtAmt    PmtDateYear    TransTime    PmtID    PmtRef    SrcCode    CCNum    CCType    ExpDateCC    BankRoutingNum    BankCustAcctNum    AllPaymentsonPledge    AllPaymentDatesonPledge    PaidAmt
try {
    // enable errors
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

    // Server_name
    $_SERVER['SERVER_NAME'] = (isset($_SERVER['SERVER_NAME'])) ? // check if SERVER_NAME is set
        $_SERVER['SERVER_NAME'] :                    // if yes, use HTTP header value
        php_uname("n");                             // if no, use php_uname()


    # CONFIG
    require_once __DIR__ . '/../config/config.php';
    # DATABASE database
    require_once __DIR__ . '/../objects/database.php';
    # DONOR CLASS
    require_once __DIR__ . '/../objects/account.php';
    # DONATION CLASS
    require_once __DIR__ . '/../objects/donation.php';
    # PAYMENT CLASS
    require_once __DIR__ . '/../objects/payment.php';
    # CAMPAIGN CLASS
    require_once __DIR__ . '/../objects/campaign.php';

    // Instantiate Classes
    $donor = new Donor($db);
    $donation = new Donation($db);
    $payment = new Payment($db);
    $campaign = new Campaign($db);

    /* Create Array from CSV file.
    Map Rows and Loop Through Them */
    $rows = array_map('str_getcsv', file('2022.allegiance.csv'));
    $header = array_shift($rows);
    $csv = array();
    $i = 0;

    // Iterate through the rows
    foreach ($rows as $row) {
        // add header to key
        $csv[] = array_combine($header, $row);

        // skip values where no payment is made
        if ($csv[$i]['PaidAmt'] != 0 || !empty($csv[$i]['PmtRef']) || $csv[$i]['PmtRef'] !== "FIX BACKOUT" || $csv[$i]['PmtRef'] !== "") {
            // Assign values to variables

            // Donor
            $donor->allegiance_id = intval($csv[$i]['AcctID']);
            $donor->firstname = ucwords(strtolower($csv[$i]['FirstName']));
            $donor->lastname = ucwords(strtolower($csv[$i]['LastName']));
            $donor->address1 = ucwords(strtolower($csv[$i]['Addr']));
            $donor->address2 = ucwords(strtolower($csv[$i]['Addr1'])); // addr1 on purpose
            $donor->city = ucwords(strtolower($csv[$i]['City']));
            $donor->state = $csv[$i]['StateProv'];
            $donor->postal_code = $csv[$i]['USZipCode'];
            $donor->country = "US";
            if (!empty($csv[$i]['TeleNum'])) {
                $donor->phone = $csv[$i]['TeleNum'];
            } else {
                $donor->phone = null;
            }
            if (!empty(strtolower($csv[$i]['EmailAddr']))) {
                $donor->email = strtolower($csv[$i]['EmailAddr']);
            } else {
                $donor->email = null;
            }
            $donor->notes = "Imported from Allegiance on " . date("m-d-Y");
            $donor->type = "Individual";

            $date = DateTime::createFromFormat('Ymd', $csv[$i]['PlgDate']);
            $donor->date_created = $date->format('Y-m-d H:i:s');

            // Donation createDonationForExistingDonor()
            if (!empty($csv[$i]['TransTime'])) {
                $date = DateTime::createFromFormat('Ymd h:i:s', $csv[$i]['PlgDate'] . $csv[$i]['TransTime']);
                $donation->timestamp = $date->format('Y-m-d H:i:s');
            } else {
                $donation->timestamp = $donor->date_created;
            }

            $donation->transaction_id = md5(uniqid(rand(), true)); // true adds more_entropy
            $donation->donation_type = "Pledge";
            $donation->amount = $csv[$i]['PlgAmt'] / 12;
            $donation->installment = "Monthly";
            $donation->comments = "Imported from Allegiance on " . date("m-d-Y");
            $donation->read_onair = 0;
            $donation->ipaddress = null;
            $donation->browser = null;
            $donation->show_name = null;
            $donation->source = "Allegiance";
            // $donation->campaign_id = NULL; // TODO: Lookup later
            $donation->donation_match = 0;
            $donation->email = $donor->email; // TODO: what's this for?

            // Payment
            // explode payments by string character o
            if (!empty($csv[$i]['AllPaymentDatesonPledge'])) {
                $AllPaymentDatesonPledge  = ($csv[$i]['AllPaymentDatesonPledge']);
                $PaymentPieces = explode("o", $AllPaymentDatesonPledge);
            } else {
                die('❌️ [AllPaymentDatesonPledge] must exist ❌️\n";');
            }

            if (!empty($csv[$i]['PmtRef'])) {
                $payment->payment_id = $csv[$i]['PmtRef'];
            } else {
                $payment->payment_id = null;
            }

            if (!empty($csv[$i]['PmtAmt'])) {
                $payment->amount = $csv[$i]['PmtAmt'];
            } else {
                die('❌️ [PmtAmt] must exist ❌️\n";');
            }

            if (!empty($csv[$i]['CCNum'])) {
                $payment->method = "card";
                // update payment update_card_payment()
                $payment->last4 = substr($csv[$i]['CCNum'], -4);
                $csv[$i]['ExpDateCC'] = str_pad($csv[$i]['ExpDateCC'], 4, "0", STR_PAD_LEFT); // pad to correctly format
                $payment->dateArray = date_parse_from_format('ny', $csv[$i]['ExpDateCC']);
                $payment->exp_month = $payment->dateArray["month"];
                $payment->exp_year = $payment->dateArray["year"];
            }
            if (!empty($csv[$i]['CCType'])) {
                if ($csv[$i]['CCType'] == "V") {
                    $payment->brand = "Visa";
                }
                if ($csv[$i]['CCType'] == "M") {
                    $payment->brand = "MasterCard";
                }
                if ($csv[$i]['CCType'] == "A") {
                    $payment->brand = "Amex";
                }
                if ($csv[$i]['CCType'] == "D") {
                    $payment->brand = "Discover";
                }
            } else {
                $payment->brand = NULL;
            }
            if (!empty($csv[$i]['BankCustAcctNum'])) {
                $payment->method = "bank_account";
                // update payment update_card_payment()
                $payment->last4 = substr($csv[$i]['BankCustAcctNum'], -4);
                $payment->exp_month = NULL;
                $payment->exp_year = NULL;
            }

            $payment->processor = "Paya";
            if (!empty($csv[$i]['PmtDateYear'])) {
                $pd = date_create_from_format('Ymd', $csv[$i]['PmtDateYear']);
                $payment->date_created = $pd->format('Y-m-d H:i:s'); // MySQL format
                $payment->date_deposited = $pd->format('Y-m-d H:i:s');
            } else if (!empty($csv[$i]['PlgDate'])) {
                $pd = date_create_from_format('Ymd', $csv[$i]['PlgDate']);
                $payment->date_created = $pd->format('Y-m-d H:i:s'); // MySQL format
                $payment->date_deposited = $pd->format('Y-m-d H:i:s');
            } else {
                die('❌️ [PmtDateYear] or [PlgDate] must exist ❌️\n";');
            }

            $payment->status = "succeeded";

            // CALL searchDonor Function from Donor Class (returns 1 donor - the most likely match)
            // fetch the results and add them to the searchDonor array
            $searchDonor = $donor->searchDonor()->fetch(PDO::FETCH_ASSOC);

            // did the query return results (find a donor)?
            if ($searchDonor) { // returns false if not found (so create a new donor)
                // set $donor_id from searchDonor
                $donor_id = $searchDonor["donor_id"];

                // if allegiance_id isn't set then update record
                if (empty($searchDonor["allegiance_id"])) {
                    // Update donor table with allegiance_id
                    $donor->update($donor_id, "allegiance_id", $donor->allegiance_id); // call update
                    // echo result
                    echo "\n[✅] SA_DONOR $donor_id updated with allegiance_id: $donor->allegiance_id";
                } else { // otherwise proceed to creating donation

                    // lookup campaign
                    if ($campaign->readbydate($donation->timestamp)->fetch(PDO::FETCH_ASSOC)) {
                        $donation->campaign_id = $campaign->readbydate($donation->timestamp)->fetch(PDO::FETCH_OBJ)->id;
                    } else {
                        $donation->campaign_id = null;
                        echo "\n[⚠️] Campaign_id not found";
                    }

                    // SEARCH for existing donation by existing stripe_charge
                    // UPDATE donations.payment_id w/ allegiance "PlgID"
                    if (substr($csv[$i]['PmtRef'], 0, 3) == "ch_") {

                        // SEARCH SA payments table for payments.donation_id by payment_id
                        // pre_existing donation w/ payments
                        $stmt = $db->prepare("SELECT `donation_id` FROM `payments` WHERE
                                    `payments`.`payment_id` =:payment_id;");
                        $stmt->execute(['payment_id' => $csv[$i]['PmtRef']]);
                        $payments_donation_id = $stmt->fetchColumn(); // retrieves just one column

                        // create link between SA donation and Allegiance PlgID
                        // UPDATE donations.payment_id w/ allegiance "PlgID"
                        if (!empty($payments_donation_id)) {
                            $query = "UPDATE `donations` SET `payment_id` = :payment_id WHERE `donations`.`id` = :donation_id;";
                            $stmt = $db->prepare($query);
                            $stmt->bindParam(':payment_id', $csv[$i]['PlgID'], PDO::PARAM_INT);
                            $stmt->bindParam(':donation_id', $payments_donation_id, PDO::PARAM_INT);
                            $stmt->execute();
                            echo "\n[✅] Updated donations.payment_id: " . $csv[$i]['PlgID'] . " from payments.payment_id: $payments_donation_id";
                        }
                    }

                    // SEARCH donations.payment_id to see if link exists
                    $stmt = $db->prepare("SELECT `id` FROM `donations` WHERE
                                            `donations`.`payment_id` =:payment_id;");
                    $stmt->execute(['payment_id' => $csv[$i]['PlgID']]);
                    $donation_id = $stmt->fetchColumn(); // retrieves just one column

                    // SEARCH
                    if (empty($donation_id)) {
                        $stmt = $db->prepare("SELECT `id` FROM `donations` WHERE
                    	donor_id = :donor_id
                    	AND amount = :amount
                    	AND installment = 'Monthly'
                    	AND source = 'Allegiance' LIMIT 1;");
                        $stmt->bindParam(':donor_id', $donor_id, PDO::PARAM_INT);
                        $stmt->bindParam(':amount', $csv[$i]['PmtAmt']);
                        $stmt->execute();

                        $donation_id = $stmt->fetchColumn(); // retrieves just one column                    	                    
                    }

                    // No link between Allegiance and SA, must create new Donation w/ Payments
                    if (empty($donation_id)) {
                        // CREATE the donation
                        $donation->donor_id = $donor_id;
                        $donation->createDonationForExistingDonor();
                        $donation->id = $db->lastInsertId();
                        $donation_id = $donation->id;
                        echo "\n[✅] Created Donation_id: $donation->id";

                        // create link between SA donation and Allegiance PlgID
                        // UPDATE donations.payment_id w/ allegiance "PlgID"
                        $query = "UPDATE `donations` SET `payment_id` = :payment_id WHERE `donations`.`id` = :donation_id;";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':payment_id', $csv[$i]['PlgID'], PDO::PARAM_INT);
                        $stmt->bindParam(':donation_id', $donation_id, PDO::PARAM_INT);
                        $stmt->execute();
                        echo "\n[✅] Updated donations.payment_id: " . $csv[$i]['PlgID'] . " from donations.id: $donation_id";
                    }

                    // Requirements satisfied, donor & donation link, can proceed to making payments
                    // skip existing charges
                    if (!empty($donation_id)) {
                        // add charges that are not STRIPE

                        // Check to ensure payments.payment_id is not DUPE
                        //if ($payment->readStripePayment($csv[$i]['PmtRef'])->rowCount() == 0) {
                        if (substr($csv[$i]['PmtRef'], 0, 3) !== "ch_" || substr($csv[$i]['PmtRef'], 0, 3) !== "py_") {

                            // INSERT (non stripe) Payments
                            $payment->donation_id = $donation_id;

                            // create multiple payments by looping through 
                            foreach ($PaymentPieces as &$payment_date) {
                                $payment->date_created = $payment_date;
                                $payment->date_deposited = $payment_date;

                                // CHECK to see if existing payment on same day, if so, skip.
                                // cast date as datetime
                                $payment_datetime = DateTime::createFromFormat('d/m/Y', $payment_date);
                                $payment_datetime2 = DateTime::createFromFormat('d/m/Y', $payment_date);
                                // call query
                                if ($payment_lookup = $payment->readDate($payment_datetime->format('Y-m-d H:i:s'), $payment_datetime2->modify('+1 day')->format('Y-m-d H:i:s'), "succeeded")) {
                                    // loop through results (to check for match)
                                    while ($existing_payments = $payment_lookup->fetch(PDO::FETCH_ASSOC)) {
                                        // check for match
                                        if ($existing_payments['donation_id'] == $payment->donation_id) {
                                            echo "Existing:" . $existing_payments['donation_id'] . " New: " . $payment->donation_id;
                                            echo "\n[⚠️] Skipping possible duplicate payment: $payment_date for payment.donation_id: $payment->donation_id because existing payment exists on same date.";
                                        } else { // not a match
                                            echo "\nFound:" . $existing_payments['donation_id'] . " NEW: " . $payment->donation_id . "\n";
                                            die("\nline 290\n");

                                            // CREATE (add) the payment(s)
                                            $payment->create_check_payment(); // This sets method as "check", update below changes to "card"
                                            $payment->id = $db->lastInsertId();
                                            echo "\n[✅] Created payment.id: $payment->id for payment.donation_id: $payment->donation_id";

                                            // UPDATE the payment with the card details
                                            $payment->update_card_payment();
                                            echo "\n[✅] Updated payment.id: $payment->id";
                                        }
                                    }
                                }
                            }
                        } else {
                            echo "\n[⚠️] Skipping: payment.id: " . $csv[$i]['PmtRef'] . " already exists";
                        }
                    }

                    // throw not found
                    if (empty($donation_id)) {
                        http_response_code(404); // Not Found
                        throw new Exception("[❌️] donation_id: $donation_id Not Found");
                    }
                }
            } else { // donor not found
                if (!empty($donor->allegiance_id)) {
                    // CREATE Donor (since not found, and not empty)
                    $donorCreate = $donor->create();

                    // echo result
                    if ($donorCreate > 0) {
                        echo "\n[✅]SA_DONOR " . $donorCreate . " CREATED for allegiance_id: $donor->allegiance_id";
                    } else {
                        echo "\n\n❌️ SA_DONOR " . $donorCreate . " NOT created for allegiance_id: $donor->allegiance_id ❌️\n";
                    }
                } else {
                    echo "\n\n❌️ SA_DONOR NOT created for allegiance_id: $donor->allegiance_id ❌️\n";
                }
            }
        } else {
            echo "\n[❌️] AccountID: " . intval($csv[$i]['AcctID']) . " has no donation this year❌️";
        }
        $i++; // add to index
        // null variables
        $payments_donation_id = null;
        $pd = null;
        $donor->allegiance_id = null;
        $donor->firstname = null;
        $donor->lastname = null;
        $donor->address1 = null;
        $donor->address2 = null;
        $donor->city = null;
        $donor->state = null;
        $donor->postal_code = null;
        $donor->country = null;
        $donor->phone = null;
        $donor->email = null;
        $donor->notes = null;
        $donor->type = null;
        $donor->date_created = null;
        $searchDonor = null;
        $donation_id = null;
        $donation->email = null;
        $donation->campaign_id = null;
        $donation->timestamp = null;
        $donation->transaction_id = null;
        $donation->donation_type = null;
        $donation->amount = null;
        $donation->installment = null;
        $donation->comments = null;
        $donation->source = null;
        $donation->donor_id = null;
        $payment->id = null;
        $payment->donation_id = null;
        $payment->payment_id = null;
        $payment->amount = null;
        $payment->processor = null;
        $payment->method = null;
        $payment->date_created = null;
        $payment->date_deposited = null;
        $payment->status = null;
        $payment->last4 = null;
        $payment->dateArray = null;
        $payment->exp_month = null;
        $payment->exp_year = null;

        // uncomment if you want to limit
        if ($i > 1) { # limit to 1, 0=1
            break;
        }
    }
    // catch exceptions
} catch (Exception $e) {
    echo json_encode(array(
        "message" => $e->getMessage(),
    ));
    exit();
} catch (TypeError $e) {
    echo json_encode(array(
        "message" => $e->getMessage(),
    ));
    exit();
}

// Allegiance_projected_CC_from_12_21.csv header fields
// Fulfilled    Sequence Name    Affil    Acct ID    Plg Num    Payment    Due Amt    Cycle    Credit Card Number    Exp Dt    Plg Amt    Shipping Amt    Mode    Level    Fund    Age    Envelope Salutation    Letter Salutation    Job Title    Company Name    Address Line 1    Address Line 2    City    State    Zip Code    Zip4    Phone    Phone Type    Pledge Date    Paid to Date Amount    Sustainer    Pledge Type    CC Type    Email    OPI User Name
