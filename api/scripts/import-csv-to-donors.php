<?php
//exit('bye felicia');
// this script reads a CSV file, searches for existing donor
// if donor found: updates the allegiance_id
// if donor not found: create new donor in DB

// Allegiance_projected_CC_from_12_21.csv header fields
// Fulfilled	Sequence Name	Affil	Acct ID	Plg Num	Payment	Due Amt	Cycle	Credit Card Number	Exp Dt	Plg Amt	Shipping Amt	Mode	Level	Fund	Age	Envelope Salutation	Letter Salutation	Job Title	Company Name	Address Line 1	Address Line 2	City	State	Zip Code	Zip4	Phone	Phone Type	Pledge Date	Paid to Date Amount	Sustainer	Pledge Type	CC Type	Email	OPI User Name
try {
    // enable errors
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

    # CONFIG
    require_once __DIR__ . '/../config/config.php';
    # DATABASE database
    require_once __DIR__ . '/../objects/database.php';
    # DONOR CLASS
    require_once __DIR__ . '/../objects/account.php';
    // Instantiate Class
    $donor = new Donor($db);

    /* Create Array from CSV file.
    Map Rows and Loop Through Them */
    $rows = array_map('str_getcsv', file('Allegiance_projected_CC_from_12_21.csv'));
    $header = array_shift($rows);
    $csv = array();
    $i = 0;

    // Iterate through the rows
    foreach ($rows as $row) {
        // add header to key
        $csv[] = array_combine($header, $row);

        // Assign value to variables
        // Donor
        $donor->allegiance_id = intval($csv[$i]['Acct ID']);
        $donor->firstname = ucwords(strtolower($csv[$i]['Letter Salutation']));
        $donor->lastname = ucwords(strtolower(substr($csv[$i]['Sequence Name'], 0, strrpos($csv[$i]['Sequence Name'], ' '))));
        $donor->address1 = ucwords(strtolower($csv[$i]['Address Line 2'])); // addr2 on purpose
        $donor->address2 = ucwords(strtolower($csv[$i]['Address Line 1'])); // addr1 on purpose
        $donor->city = ucwords(strtolower($csv[$i]['City']));
        $donor->state = $csv[$i]['State'];
        $donor->postal_code = $csv[$i]['Zip Code'];
        $donor->country = "US";
        if (!empty($csv[$i]['Phone'])) {
            $donor->phone = $csv[$i]['Phone'];
        } else {
            $donor->phone = NULL;
        }
        if (!empty(strtolower($csv[$i]['Email']))) {
            $donor->email = strtolower($csv[$i]['Email']);
        } else {
            $donor->email = NULL;
        }
        $donor->notes = "Imported from Allegiance on 12-17-2021";
        $donor->type = "Individual";
        $donor->date_created = date('Y-m-d H:i:s', strtotime(str_replace('-', '/', $csv[$i]['Pledge Date'])));

        // Donation
        $donor->payment_amount = $csv[$i]['Payment'];
        $donor->pledge_date = date('Y-m-d H:i:s', strtotime(str_replace('-', '/', $csv[$i]['Pledge Date']))); // at least since last renewal

        // Payment
        $donor->donation_amount = $csv[$i]['Payment'];

        // CALL searchDonor Function from Donor Class (returns 1 donor - the most likely match)
        // fetch the results and add them to the searchDonor array
        $searchDonor = $donor->searchDonor()->fetch(PDO::FETCH_ASSOC);

        // did the query return results (find a donor)
        if ($searchDonor) { // returns false if not found
            // set $donor_id
            $donor_id = $searchDonor["donor_id"];

            // if allegiance_id isn't set then update record
            if (empty($searchDonor["allegiance_id"])) {
                // Update donor table with allegiance_id
                $donor->update($donor_id, "allegiance_id", $donor->allegiance_id); // call update
                // echo result
                echo "\nSA_DONOR $donor_id updated with allegiance_id: $donor->allegiance_id";
            } else { // otherwise just echo record from the db
                // echo result
                echo "\nSA_DONOR $donor_id found with allegiance_id: " . $searchDonor["allegiance_id"];
            }
        } else {
            if (!empty($donor->allegiance_id)) {
                // CREATE Donor (since not found, and not empty)
                $donorCreate = $donor->create();

                // echo result
                if ($donorCreate > 0) {
                    echo "\nSA_DONOR " . $donorCreate . " CREATED for allegiance_id: $donor->allegiance_id";
                } else {
                    echo "\n\n❌️ SA_DONOR " . $donorCreate . " NOT created for allegiance_id: $donor->allegiance_id ❌️\n";
                }
            } else {
                echo "\n\n❌️ SA_DONOR NOT created for allegiance_id: $donor->allegiance_id ❌️\n";
            }
        }

        $i++; // add to index
        // null variables
        $donor->allegiance_id = NULL;
        $donor->firstname = NULL;
        $donor->lastname = NULL;
        $donor->address1 = NULL;
        $donor->address2 = NULL;
        $donor->city = NULL;
        $donor->state = NULL;
        $donor->postal_code = NULL;
        $donor->country = NULL;
        $donor->phone = NULL;
        $donor->email = NULL;
        $donor->notes = NULL;
        $donor->type = NULL;
        $donor->date_created = NULL;
        $searchDonor = NULL;
        // comment if you want to limit
        // if ($i > 10) break;
    }

    // catch exceptions
} catch (Exception $e) {
    echo json_encode(array(
        "message" => $e->getMessage(),
    ));
} catch (TypeError $e) {
    echo json_encode(array(
        "message" => $e->getMessage(),
    ));
}


// allegiance_import.csv header fields
// allegiance_id	firstname	lastname	address1	address2	city	state	postal_code	phone	email


// $donor->allegiance_id = $csv[$i]['allegiance_id'];
// $donor->firstname = ucwords(strtolower($csv[$i]['firstname']));
// $donor->lastname = ucwords(strtolower($csv[$i]['lastname']));
// $donor->address1 = ucwords(strtolower($csv[$i]['address1']));
// $donor->address2 = ucwords(strtolower($csv[$i]['address2']));
// $donor->city = ucwords(strtolower($csv[$i]['city']));
// $donor->state = $csv[$i]['state'];
// $donor->postal_code = $csv[$i]['postal_code'];
// $donor->country = "US";
// $donor->phone = $csv[$i]['phone'];
// $donor->email = $csv[$i]['email'];
