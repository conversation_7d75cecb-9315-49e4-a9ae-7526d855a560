-- Complete donor cleanup - handles all foreign key dependencies
-- Only delete donors with NO donations AND NO subscriptions
-- Keep donors who have subscriptions (they may represent donations)

-- Analysis first
SELECT 'Analysis of records to be deleted:' as info;

SELECT COUNT(*) as donors_to_delete
FROM donors d
WHERE NOT EXISTS (SELECT 1 FROM donations WHERE donor_id = d.id)
AND NOT EXISTS (SELECT 1 FROM subscriptions WHERE donor_id = d.id);

SELECT COUNT(*) as caller_log_records_to_delete
FROM caller_log c
WHERE c.donor_id IN (
    SELECT d.id FROM donors d
    WHERE NOT EXISTS (SELECT 1 FROM donations WHERE donor_id = d.id)
    AND NOT EXISTS (SELECT 1 FROM subscriptions WHERE donor_id = d.id)
);

-- Start deletion transaction
START TRANSACTION;

SELECT 'Starting deletion...' as status;

-- Step 1: Delete from caller_log first
DELETE FROM caller_log
WHERE donor_id IN (
    SELECT id FROM donors
    WHERE NOT EXISTS (SELECT 1 FROM donations WHERE donor_id = donors.id)
    AND NOT EXISTS (SELECT 1 FROM subscriptions WHERE donor_id = donors.id)
);

SELECT CONCAT('Deleted ', ROW_COUNT(), ' caller_log records') as caller_log_result;

-- Step 2: Delete donors with no donations AND no subscriptions
DELETE FROM donors
WHERE NOT EXISTS (SELECT 1 FROM donations WHERE donor_id = donors.id)
AND NOT EXISTS (SELECT 1 FROM subscriptions WHERE donor_id = donors.id);

SELECT CONCAT('Deleted ', ROW_COUNT(), ' donors') as donor_result;

-- Final verification
SELECT 
    (SELECT COUNT(*) FROM donors) as donors_remaining,
    (SELECT COUNT(*) FROM subscriptions) as subscriptions_remaining,
    (SELECT COUNT(*) FROM caller_log) as caller_log_remaining;

-- Verify no truly orphaned donors remain (no donations, no subscriptions)
SELECT COUNT(*) as truly_orphaned_donors_remaining
FROM donors
WHERE NOT EXISTS (SELECT 1 FROM donations WHERE donor_id = donors.id)
AND NOT EXISTS (SELECT 1 FROM subscriptions WHERE donor_id = donors.id);

-- Auto-commit the changes
COMMIT;

SELECT 'Cleanup completed and committed successfully!' as final_status;
