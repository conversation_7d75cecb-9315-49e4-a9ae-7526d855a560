<?php
exit('bye felicia');
########################################## PULL - MERGE - CHECK #######################
# This script merges Stripe and existing api_db records to api_db_new.
#
# It iterates over stripe charges and matches or makes records.
#
# PULL charges from stripe
#
# LOOKUP old api_db.donation table to see if record exists, if so copy donor, donation, payment, shipment, shipment_addresses to new api_db_new table.
# If no match CREATE new donor, donation, payment, shipment, shipment_addresses Records.
# v2: LASTLY CREATE PAYMENT RECORDS FOR CHECKS
#######################################################################################

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load Composer's autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// require database
require_once __DIR__ . '/../config/database-testing.php';

// Stripe API Key
//\Stripe\Stripe::setApiKey("sk_test_8Nhs4cLJieHplDES5aTXByoH");
\Stripe\Stripe::setApiKey("********************************");

// Build Queries
// Donor Lookup
$donor_lookup_query = "SELECT `id` as `donor_id`
		FROM
		`donors`
		WHERE
		memsys_id=:memsys_id
		OR email=:email
		OR lastname=:lastname
		    AND firstname=:firstname
		    AND city=:city
		OR lastname=:lastname
		    AND firstname=:firstname
		ORDER BY
		CASE
		    WHEN memsys_id=:memsys_id then 100
		    WHEN email=:email then 90
		    WHEN lastname=:lastname
		    AND firstname=:firstname
		    AND city=:city then 80
		    WHEN lastname=:lastname
		    AND firstname=:firstname then 70
		    ELSE 0
		END DESC
		LIMIT 1
		";
// Donor Insert (if not exist)
$donor_insert_query = "INSERT INTO
		`donors`
		SET
		firstname=:firstname,
		lastname=:lastname,
		phone=:phone,
		email=:email,
		address1=:address1,
		address2=:address2,
		city=:city,
		state=:state,
		country=:country,
		postal_code=:postal_code,
		memsys_id=:memsys_id";

// Donations Insert
$donation_insert_query = "INSERT INTO
		`donations`
	SET
        donor_id=:donor_id,
        type=:type,
        timestamp=:timestamp,
        amount=:amount,
        installment=:installment,
        transaction_id=:transaction_id,
        source=:source,
        status=:status";

// Payment Insert
$payment_insert_query = "INSERT INTO
        `payments`
    SET
        customer_id=:customer_id,
        payment_id=:payment_id,
        amount=:amount,
        amount_refunded=:amount_refunded,
        method=:method,
        processor=:processor,
        fingerprint=:fingerprint,
        card_type=:card_type,
        date_created=:date_created,
        last4=:last4,
        brand=:brand,
        exp_month=:exp_month,
        exp_year=:exp_year,
        status=:status";

// Prepare queries
$donation_insert_stmt = $db->prepare($donation_insert_query); // donation insert
$donor_lookup_stmt = $db->prepare($donor_lookup_query); // donor lookup
$donor_insert_stmt = $db->prepare($donor_insert_query); // donor insert
$payment_insert_stmt = $db->prepare($payment_insert_query); // payment insert

###############################################
## PULL Customers from Stripe
// iterates through all customers (100 at a time)
$customers = \Stripe\Customer::all(["limit" => 1]);
foreach ($customers->autoPagingIterator() as $customer) {
    // set
    $description = null;
    $customer_id = null;
    $memsys_id = null;
    $name = null;
    $address1 = null;
    $address2 = null;
    $city = null;
    $state = null;
    $postal_code = null;
    $email = null;
    $transaction_id = null;
    $amount = null;
    $installment = null;
    $type = null;
    $source = null;
    $timestamp = null;
    // Assign Customer variables
    if (!empty($customer->id)) {
        $customer_id = $customer->id;
    }
    if (!empty($customer->sources->data[0]->name)) {
        $name = $customer->sources->data[0]->name;
    } else if (!empty($customer->sources->data[0]->account_holder_name)) {
        $name = $customer->sources->data[0]->account_holder_name;
    } else if (!empty($customer->sources->data[0]->name)) {
        $name = $customer->sources->data[0]->name;
    } else if (!empty($customer->description)) {
        $name = $customer->description;
    } else if (!empty($customer->name)) {
        $name = $customer->name;
    } else if (!empty($customer->metadata->name)) {
        $name = $customer->metadata->name;
    } else {
        $name = "Anonymous Donor";
    }
    if (!empty($name)) {
        $pieces = explode(" ", $name, 2);
        $firstname = $pieces[0];
        $lastname = $pieces[1];
    } // First and Last name
    if (!empty($customer->created)) {
        $timestamp = $customer->created;
    }
    if (!empty($customer->metadata->old_id)) {
        $memsys_id = $customer->metadata->old_id;
    }
    if (!empty($customer->sources->data[0]->name)) {
        $name = $customer->sources->data[0]->name;
    } else {
        $name = $description;
    }
    if (!empty($customer->sources->data[0]->address_line1)) {
        $address1 = $customer->sources->data[0]->address_line1;
    } else if (!empty($customer->metadata->address_line1)) {
        $address1 = $customer->metadata->address_line1;
    }
    if (!empty($customer->sources->data[0]->address_line2)) {
        $address2 = $customer->sources->data[0]->address_line2;
    } else if (!empty($customer->metadata->address_line2)) {
        $address2 = $customer->metadata->address_line2;
    } else {
        $address2 = null;
    }
    if (!empty($customer->sources->data[0]->address_city)) {
        $city = $customer->sources->data[0]->address_city;
    } else if (!empty($customer->metadata->address_city)) {
        $city = $customer->metadata->address_city;
    }
    if (!empty($customer->sources->data[0]->address_state)) {
        $state = $customer->sources->data[0]->address_state;
    } else if (!empty($customer->metadata->address_state)) {
        $state = $customer->metadata->address_state;
    }
    if (!empty($customer->sources->data[0]->address_zip)) {
        $postal_code = $customer->sources->data[0]->address_zip;
    } else if (!empty($customer->metadata->address_zip)) {
        $postal_code = $customer->metadata->address_zip;
    }
    if (!empty($customer->sources->data[0]->country)) {
        $country = $customer->sources->data[0]->country;
    } else {
        $country = "US";
    }
    if (!empty($customer->email)) {
        $email = $customer->email;
    }
    if (!empty($customer->metadata->phone)) {
        $phone = $customer->metadata->phone;
    }
    if (!empty($customer->subscriptions->data)) {
        $installment = "Monthly";
    } else {
        $installment = "One-Time";
    }


    // MISC
    // $transaction_id = md5(uniqid(rand(), true)); // true adds more_entropy
    $type = "Pledge";

    // =============== DEBUG =====================
    echo "\n===== Customer Info =====\n";
    echo "\nCustomerID: ";
    if (!empty($customer_id)) {
        echo $customer_id;
    }
    echo "\nMemSys: ";
    if (!empty($memsys_id)) {
        echo $memsys_id;
    }
    echo "\nDonorID: ";
    if (!empty($donor_id)) {
        echo $donor_id;
    }
    echo "\nName: ";
    if (!empty($name)) {
        echo $name;
    }
    echo "\nAddress1: ";
    if (!empty($address1)) {
        echo $address1;
    }
    echo "\nAddress2: ";
    if (!empty($address2)) {
        echo $address2;
    }
    echo "\nCity: ";
    if (!empty($city)) {
        echo $city;
    }
    echo "\nState: ";
    if (!empty($state)) {
        echo $state;
    }
    echo "\nPostal_Code: ";
    if (!empty($postal_code)) {
        echo $postal_code;
    }
    echo "\nCountry: ";
    if (!empty($country)) {
        echo $country;
    }
    echo "\nEmail: ";
    if (!empty($email)) {
        echo $email;
    }
    echo "\nPhone: ";
    if (!empty($phone)) {
        echo $phone;
    }

    echo "\n--------------------\n";
    // ============ END DEBUG =================

    # STEP 1
    // MATCH: Lookup to see if DONATION record exists, else create one.
    try {
        $stmt = $db->prepare("SELECT * FROM api_db.donations WHERE `cardnumber`=:customer_id");
        $stmt->execute(['customer_id' => $customer_id]);
        $user = $stmt->fetch();
    } catch (PDOExecption $e) {
        $db->rollback();
        print "Error!: " . $e->getMessage() . "</br>";
        die;
    }

    // FOUND donation in old database.
    // ///////////////// MIGRATE Donation /////////////////////////////
    if (!empty($user)) {
        // LIST all charges by customer
        $charge_array = \Stripe\Charge::all(["customer" => $customer_id]);
        foreach ($charge_array as $charge) {
            // from Stripe Customer 
            $payment_id = $charge->id; // ch_xxx
            $amount_paid = ($charge->amount / 100); // decimal(13,2)
            $amount_refunded = ($charge->amount_refunded / 100); // decimal(13,2)                    
            $processor = "Stripe"; // enum('Stripe', 'PayPal', 'P-check', 'K-check', 'cash')
            $fingerprint = $charge->payment_method_details->card->fingerprint; // varchar (30)
            $card_type = $charge->source->funding; // credit, debit, prepaid, unknown
            $method = $charge->source->object; // set('card', 'bank_account', 'check', 'cash', 'stock', 'inkind')
            $payment_date_created = $charge->created; // FROM_UNIXTIME(datetime)
            $last4 = $charge->source->last4; // SMALLINT(4)
            $brand = $charge->source->brand; // American Express, Diners Club, Discover, JCB, MasterCard, UnionPay, Visa, or Unknown.
            $exp_month = $charge->source->exp_month; // tinyint(2)
            $exp_year = $charge->source->exp_year; // SMALLINT(4)
            $status = $charge->status; // succeeded, pending, or failed.

            // from donations table
            $source = $user['source'];
            $donation_id = $user['id'];

            // if (!empty($customer->subscriptions->data[0]->plan->metadata->Source)) {$source = $customer->subscriptions->data[0]->plan->metadata->Source;}
            // else if (!empty($charge->metadata->Source)) {$source = $charge->metadata->Source;} else { $source = "StationAdmin";}
            // if ($method == "bank_account") {$source = "PaySol";}

            // =============== DEBUG =====================

            echo "\n===== Payment Info =====\n";
            echo "\nDonationID: ";
            if (!empty($donation_id)) {
                echo $donation_id;
            }
            echo "\npayment_id: ";
            if (!empty($payment_id)) {
                echo $payment_id;
            }
            echo "\nPaymentDateCreated: ";
            if (!empty($payment_date_created)) {
                echo date("Y-m-d H:m:s", $payment_date_created);
            }
            echo "\namount paid: ";
            if (!empty($amount_paid)) {
                echo $amount_paid;
            }
            echo "\namount refunded: ";
            if (!empty($amount_refunded)) {
                echo $amount_refunded;
            }
            echo "\nprocessor: ";
            if (!empty($processor)) {
                echo $processor;
            }
            echo "\ncard_type: ";
            if (!empty($card_type)) {
                echo $card_type;
            }
            echo "\nmethod: ";
            if (!empty($method)) {
                echo $method;
            }
            echo "\nfingerprint: ";
            if (!empty($fingerprint)) {
                echo $fingerprint;
            }
            echo "\ninstallment: ";
            if (!empty($installment)) {
                echo $installment;
            }
            echo "\nlast4: ";
            if (!empty($last4)) {
                echo $last4;
            }
            echo "\nexp_month: ";
            if (!empty($exp_month)) {
                echo $exp_month;
            }
            echo "\nexp_year: ";
            if (!empty($exp_year)) {
                echo $exp_year;
            }
            echo "\nSource: ";
            if (!empty($source)) {
                echo $source;
            }
            echo "\n--------------------\n";
            // ============ END DEBUG =================

            // COPY donation record into api_db_new
            // Bind Donors
            $donor_lookup_stmt->bindParam(":donor_id", $donor_id);
            $donor_lookup_stmt->bindParam(":memsys_id", $memsys_id);
            $donor_lookup_stmt->bindParam(":email", $email);
            $donor_lookup_stmt->bindParam(":firstname", $firstname);
            $donor_lookup_stmt->bindParam(":lastname", $lastname);
            $donor_lookup_stmt->bindParam(":city", $city);

            // Bind Donation
            $donation_insert_stmt->bindParam(":donor_id", $donor_id);
            $donation_insert_stmt->bindParam(":type", $type);
            $donation_insert_stmt->bindParam(":timestamp", $timestamp);
            $donation_insert_stmt->bindParam(":amount", $amount);
            $donation_insert_stmt->bindParam(":installment", $installment);
            $donation_insert_stmt->bindParam(":transaction_id", $transaction_id);
            $donation_insert_stmt->bindParam(":source", $source);
            $donation_insert_stmt->bindParam(":status", $status);

            // Bind for Donor record Insert (if lookup fails)
            $donor_insert_stmt->bindParam(":firstname", $firstname);
            $donor_insert_stmt->bindParam(":lastname", $lastname);
            $donor_insert_stmt->bindParam(":address1", $address1);
            $donor_insert_stmt->bindParam(":address2", $address2);
            $donor_insert_stmt->bindParam(":city", $city);
            $donor_insert_stmt->bindParam(":state", $state);
            $donor_insert_stmt->bindParam(":country", $country);
            $donor_insert_stmt->bindParam(":postal_code", $postal_code);
            $donor_insert_stmt->bindParam(":phone", $phone);
            $donor_insert_stmt->bindParam(":email", $email);
            $donor_insert_stmt->bindParam(":memsys_id", $memsys_id);

            // Bind for Payments
            $payment_insert_stmt->bindParam(":donation_id", $donation_id);
            $payment_insert_stmt->bindParam(":customer_id", $customer_id);
            $payment_insert_stmt->bindParam(":payment_id", $payment_id);
            $payment_insert_stmt->bindParam(":amount", $amount_paid);
            $payment_insert_stmt->bindParam(":amount_refunded", $amount_refunded);
            $payment_insert_stmt->bindParam(":method", $method);
            $payment_insert_stmt->bindParam(":processor", $processor);
            $payment_insert_stmt->bindParam(":fingerprint", $fingerprint);
            $payment_insert_stmt->bindParam(":card_type", $card_type);
            $payment_insert_stmt->bindParam(":date_created", $payment_date_created);
            $payment_insert_stmt->bindParam(":last4", $last4);
            $payment_insert_stmt->bindParam(":brand", $brand);
            $payment_insert_stmt->bindParam(":exp_month", $exp_month);
            $payment_insert_stmt->bindParam(":exp_year", $exp_year);
            $payment_insert_stmt->bindParam(":status", $status);

            //////// INSERT PAYMENT RECORD
            try {
                $sql = "INSERT INTO `payments` (donation_id, customer_id, payment_id, amount, amount_refunded, method, processor, fingerprint, card_type, date_created, last4, brand, exp_month, exp_year, `status`) VALUES (?,?,?,?,?,?,?,?,?,FROM_UNIXTIME(?),LPAD(?,4,'0'),?,LPAD(?,2,'0'),?,?)";
                $stmt = $db->prepare($sql);
                $stmt->execute(array($donation_id, $customer_id, $payment_id, $amount_paid, $amount_refunded, $method, $processor, $fingerprint, $card_type, $payment_date_created, $last4, $brand, $exp_month, $exp_year, $status));
                echo "\nINSERT_ID: " . $db->lastInsertId() . "\n";
            } catch (PDOExecption $e) {
                $db->rollback();
                print "Error!: " . $e->getMessage() . "</br>";
            }
        }
    }
    //         } else {
    // /////////////////// CREATE Donation and Payment Record /////////////////////////////
    //             echo "\nNO DONATION FOUND: $customer_id";
    //             die;
    //             echo "\nCreating donor, donation, payment, shipment, shipment_addresses Records ...\n";
    //             // create missing donation (subscription)
    // // Bind Donors
    //             $donor_lookup_stmt->bindParam(":memsys_id", $memsys_id);
    //             $donor_lookup_stmt->bindParam(":email", $email);
    //             $donor_lookup_stmt->bindParam(":firstname", $firstname);
    //             $donor_lookup_stmt->bindParam(":lastname", $lastname);
    //             $donor_lookup_stmt->bindParam(":city", $city);

    // // Bind Donation
    // //            $donation_insert_stmt->bindParam(":donor_id", $donor_id);
    //             $donation_insert_stmt->bindParam(":type", $type);
    //             $donation_insert_stmt->bindParam(":timestamp", $timestamp);
    //             $donation_insert_stmt->bindParam(":amount", $amount);
    //             $donation_insert_stmt->bindParam(":installment", $installment);
    //             $donation_insert_stmt->bindParam(":transaction_id", $transaction_id);
    //             $donation_insert_stmt->bindParam(":source", $source);
    //             $donation_insert_stmt->bindParam(":status", $status);

    // // Bind for Donor record Insert (if lookup fails)
    //             $donor_insert_stmt->bindParam(":firstname", $firstname);
    //             $donor_insert_stmt->bindParam(":lastname", $lastname);
    //             $donor_insert_stmt->bindParam(":address1", $address1);
    //             $donor_insert_stmt->bindParam(":address2", $address2);
    //             $donor_insert_stmt->bindParam(":city", $city);
    //             $donor_insert_stmt->bindParam(":state", $state);
    //             $donor_insert_stmt->bindParam(":country", $country);
    //             $donor_insert_stmt->bindParam(":postal_code", $postal_code);
    //             $donor_insert_stmt->bindParam(":phone", $phone);
    //             $donor_insert_stmt->bindParam(":email", $email);
    //             $donor_insert_stmt->bindParam(":memsys_id", $memsys_id);

    // // Grab donor_id
    //             // Execute database donor_lookup_query (Donors)
    //             if ($donor_lookup_stmt->execute()) { // Donor Found!
    //                 echo "\nDonor Found";
    //                 $donor_id = $donor_lookup_stmt->fetch();
    //                 $donor_id = $donor_id['donor_id'];
    //             } else {
    //                 echo "\nCould not find donor";
    //                 print_r($donor_lookup_stmt->errorInfo(), true);
    //                 die;
    //             }

    // // Execute database donor_insert_query (Donors)
    //             if (empty($donor_id)) { // Donor look up FAILED
    //                 if ($donor_insert_stmt->execute()) { // INSERT new Donor
    //                     $donor_id = $db->lastInsertId(); // last insert id
    //                     echo "\nDonation Inserted: $donor_id";
    //                 } else if (!$donor_insert_stmt->execute()) {
    //                     echo "\nCould not insert donor";
    //                     print_r($donor_insert_stmt->errorInfo(), true);
    //                     die;
    //                 }
    //             }

    // // Insert Donation, grab donation_id
    //             if ($donation_insert_stmt->execute()) {
    //                 $donation_id = $db->lastInsertId();
    //                 echo "New Donation ID: $donation_id\n";
    //             } else if (!$donation_insert_stmt->execute()) {
    //                 print_r($donation_insert_stmt->errorInfo(), true);
    //                 die;
    //             }

    // // Payments
    //             $update_payments_query = "UPDATE `payments`
    //                         SET
    //                             `donation_id` = $donation_id
    //                         WHERE
    //                             `id` = $payment_id;
    //                         ";
    // // Prepare Payment
    //             $update_payments_stmt = $db->prepare($update_payments_query);

    // // Bind Payment
    //             $update_payments_stmt->bindParam(":donations_id", $donation_id);

    // // Update Payment
    //             if ($update_payments_stmt->execute()) {
    //                 echo "\nSuccessfully updated Payment Record!";
    //             } else if (!$update_payments_stmt->execute()) {
    //                 echo "\nUnable to update Payment Record!";
    //                 print_r($update_payments_stmt->errorInfo(), true);
    //                 die;
    //             }

    //         }



}
