<?php
exit('bye felicia');
# this script creates donations from payments table where no donation_id exists.
# it looks up the charge to find the info
# optionally creates donors

## FIX ME
##  maybe fixed:::: 1. creates new donation and donor_id on each run. it needs to look up donor id
##  maybe fixed:::: Does not insert phone, email address1, address2, city, state, country, postal_code
## does not update timestamp on donations

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load Composer's autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// require database
require_once __DIR__ . '/../config/database-testing.php';

// Stripe API Key
//\Stripe\Stripe::setApiKey("sk_test_8Nhs4cLJieHplDES5aTXByoH");
\Stripe\Stripe::setApiKey("********************************");

// sql
try {
    // grab the customer_id
//    $select_paymentsinfo_query = $db->prepare('SELECT customer_id, amount, id as payment_id FROM `payments` where donation_id IS NULL');
    $select_paymentsinfo_query = $db->prepare('SELECT customer_id, amount, id as payment_id FROM `payments` where donation_id IS NULL');

    $select_paymentsinfo_query->execute();

    // grab the details from stripe

    // Fetches stripe_customer data one request at a time
    while ($row = $select_paymentsinfo_query->fetch(PDO::FETCH_ASSOC)) {
        $customer_id = $row['customer_id'];
        $payment_id = $row['payment_id'];
        $customers = \Stripe\Customer::retrieve("$customer_id"); // stripe_customer array
        // set
        $description = null;
        $memsys_id = null;
        $name = null;
        $address1 = null;
        $address2 = null;
        $city = null;
        $state = null;
        $postal_code = null;
        $email = null;
        $transaction_id = null;
        $amount = $row['amount'];
        $installment = null;
        $type = null;
        $source = null;
        $timestamp = null;

        // assign
        if (!empty($customers->description)) {$description = $customers->description;}
            else if (!empty($customers->sources->data[0]->account_holder_name)) {$description = $customers->sources->data[0]->account_holder_name;}
            else if (!empty($customers->sources->data[0]->name)) {$description = $customers->sources->data[0]->name;}
            else if (!empty($customers->name)) {$description = $customers->name;}
            else {$description = $customers->metadata->name;}

        if (!empty($customers->created)) {$timestamp = $customers->created;}
        if (!empty($customers->metadata->old_id)) {$memsys_id = $customers->metadata->old_id;}
        if (!empty($customers->sources->data[0]->name)) {$name = $customers->sources->data[0]->name;}

        if (!empty($customers->sources->data[0]->address_line1)) {$address1 = $customers->sources->data[0]->address_line1;}
            else if (!empty($customers->metadata->address_line1)) {$address1 = $customers->metadata->address_line1;}
        
        if (!empty($customers->sources->data[0]->address_line2)) {$address2 = $customers->sources->data[0]->address_line2;}
            else if (!empty($customers->metadata->address_line2)) {$address2 = $customers->metadata->address_line2;}

        if (!empty($customers->sources->data[0]->address_city)) {$city = $customers->sources->data[0]->address_city;}
            else if (!empty($customers->metadata->address_city)) {$city = $customers->metadata->address_city;}

        if (!empty($customers->sources->data[0]->address_state)) {$state = $customers->sources->data[0]->address_state;}
            else if (!empty($customers->metadata->address_state)) {$state = $customers->metadata->address_state;}
       
        if (!empty($customers->sources->data[0]->address_zip)) {$postal_code = $customers->sources->data[0]->address_zip;}
            else if (!empty($customers->metadata->address_zip)) {$postal_code = $customers->metadata->address_zip;}

        if (!empty($customers->sources->data[0]->country)) {$country = $customers->sources->data[0]->country;} else $country = "US";

        if (!empty($customers->email)) {$email = $customers->email;}
        if (!empty($customers->subscriptions->data)) {$installment = "Monthly";} else { $installment = "One-Time";}

        $transaction_id = md5(uniqid(rand(), true)); // true adds more_entropy
        $type = "Pledge";
        $source = "StationAdmin";

        // echo
        if (!empty($description)) {echo "\nDescription: " . $description;} else {$description = "Anonymous Donor";}//{echo "NO_DESCRIPT"; die;}  // name is just a minimum
        if (!empty($timestamp)) {echo "\nTimestamp: " . date("Y-m-d H:m:s", $timestamp);}
        if (!empty($memsys_id)) {echo "\nMemSys: " . $memsys_id;}
        if (!empty($name)) {echo "\n" . $name;}
        if (!empty($address1)) {echo "\n" . $address1;}
        if (!empty($address2)) {echo "\n" . $address2;}
        if (!empty($city)) {echo "\n" . $city;}
        if (!empty($state)) {echo "\n" . $state;}
        if (!empty($postal_code)) {echo "\n" . $postal_code;}
        if (!empty($country)) {echo "\n" . $country;}
        if (!empty($email)) {echo "\n" . $email;}
        if (!empty($amount)) {echo "\nAmount: " . $amount;}
        if (!empty($installment)) {echo "\nInstallment: " . $installment;}
        echo "\n";echo "\n";

        //first and last name
        $pieces = explode(" ", $description, 2);
        $firstname = $pieces[0]; // piece1
        $lastname = $pieces[1]; // piece2

        //timestamp
        $timestamp = date("Y-m-d H:m:s", $timestamp);

        ## TESTING
        //print_r($customers); die;

        // create missing donation (subscription)
        // Donor Lookup
        $query4 = "SELECT `id` as `donor_id`
                FROM
                `donors`
                WHERE
                memsys_id=:memsys_id
                OR email=:email
                OR lastname=:lastname
                    AND firstname=:firstname
                    AND city=:city
                OR lastname=:lastname
                    AND firstname=:firstname
                ORDER BY
                CASE
                    WHEN memsys_id=:memsys_id then 100
                    WHEN email=:email then 90
                    WHEN lastname=:lastname
                    AND firstname=:firstname
                    AND city=:city then 80
                    WHEN lastname=:lastname
                    AND firstname=:firstname then 70
                    ELSE 0
                END DESC
                LIMIT 1
                ";
        // Donor Insert (if not exist)
        $query5 = "INSERT INTO
                `donors`
                 SET
                firstname=:firstname,
                lastname=:lastname,
                phone=:phone,
                email=:email,
                address1=:address1,
                address2=:address2,
                city=:city,
                state=:state,
                country=:country,
                postal_code=:postal_code,
                memsys_id=:memsys_id";
        
        // Donations Insert
        $query = "INSERT INTO
                `donations`
                SET
                donor_id=:donor_id,
                type=:type,
                timestamp=:timestamp,
                amount=:amount,
                installment=:installment,
                transaction_id=:transaction_id,
                source=:source,
                status=:status";

        // Prepare queries
        $stmt = $db->prepare($query); // donor lookup
        $stmt4 = $db->prepare($query4); // donor insert
        $stmt5 = $db->prepare($query5); // donation insert

        // Bind Donors
        $stmt4->bindParam(":memsys_id", $memsys_id);
        $stmt4->bindParam(":email", $email);
        $stmt4->bindParam(":firstname", $firstname);
        $stmt4->bindParam(":lastname", $lastname);
        $stmt4->bindParam(":city", $city);

        // Bind Donation
        $stmt->bindParam(":donor_id", $donor_id);
        $stmt->bindParam(":type", $type);
        $stmt->bindParam(":timestamp", $timestamp);        
        $stmt->bindParam(":amount", $amount);
        $stmt->bindParam(":installment", $installment);
        $stmt->bindParam(":transaction_id", $transaction_id);
        $stmt->bindParam(":source", $source);
        $stmt->bindParam(":status", $status);

        // Bind for Donor record Insert (if lookup fails)
        $stmt5->bindParam(":firstname", $firstname);
        $stmt5->bindParam(":lastname", $lastname);
        $stmt5->bindParam(":address1", $address1);
        $stmt5->bindParam(":address2", $address2);
        $stmt5->bindParam(":city", $city);
        $stmt5->bindParam(":state", $state);
        $stmt5->bindParam(":country", $country);
        $stmt5->bindParam(":postal_code", $postal_code);
        $stmt5->bindParam(":phone", $phone);
        $stmt5->bindParam(":email", $email);
        $stmt5->bindParam(":memsys_id", $memsys_id);

        // Grab donor_id
        // Execute database query4 (Donors)
        if ($stmt4->execute()) { // Donor Found!
            $donor_id = $stmt4->fetch();
            $donor_id = $donor_id['donor_id'];
        } else {
            echo "\nCould not find donor";
            print_r($stmt4->errorInfo(), true);
            die;
        }

        // Execute database query5 (Donors)
        if (empty($donor_id)) { // Donor look up FAILED
            if ($stmt5->execute()) { // INSERT new Donor 
                $donor_id = $db->lastInsertId(); // last insert id
            } else if (!$stmt5->execute()) {
                echo "\nCould not insert donor";
                print_r($stmt5->errorInfo(), true);
                die;
            }
        }

        // Insert Donation, grab donation_id
        if ($stmt->execute()) {
            $donation_id = $db->lastInsertId();
            echo "New Donation ID: $donation_id\n";
        } else if (!$stmt->execute()) {
            print_r($stmt->errorInfo(), true);
            die;
        }

        // Payments
        $query0 = "UPDATE `payments`
         SET
             `donation_id` = $donation_id
         WHERE
             `id` = $payment_id;
         ";
        // Prepare Payment
        $stmt0 = $db->prepare($query0);

        // Bind Payment
        $stmt0->bindParam(":donations_id", $donation_id);

        // Update Payment
        if ($stmt0->execute()) {
            echo "\nSuccessfully updated Payment Record";
        } else if (!$stmt0->execute()) {
            print_r($stmt0->errorInfo(), true);
            die;
        }

        // sleep
        echo "\n--------------------------------\n";
        //sleep(60);
        // try {
        //         // insert missing donation_id
        //         $sql = "INSERT INTO payments (donation_id) VALUES (?) WHERE `customer_id` = $customer_id and `amount` = $amount";
        //         $stmt= $db->prepare($sql);
        //         $stmt->execute(array($customer_id));
        //          echo "\nINSERT_ID: ". $db->lastInsertId(). "\n";
        //         } catch(PDOExecption $e) {
        //             $db->rollback();
        //             print "Error!: " . $e->getMessage() . "</br>";
        //         }
    }

} catch (PDOExecption $e) {
    $db->rollback();
    print "Error!: " . $e->getMessage() . "</br>";
}

//$customers = \Stripe\Customer::retrieve('cus_EPTUWEQzbNXpws');
