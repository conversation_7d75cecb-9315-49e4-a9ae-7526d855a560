<?php
exit('bye felicia');
# This script inserts payment records from stripe
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load Composer's autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// require database
require_once __DIR__ . '/../config/database-testing.php';

// Stripe API Key
// \Stripe\Stripe::setApiKey("sk_test_8Nhs4cLJieHplDES5aTXByoH");
\Stripe\Stripe::setApiKey("********************************");

// ---------------------------------------

// SELECT donation_id from DB
try {
    // select a particular customer_id
    $stmt = $db->prepare("SELECT `id`, `customer_id`, `donation_id` FROM `payments` WHERE `customer_id` LIKE 'cus_%';");
    $stmt->execute();
} catch (PDOException $e) {
    //$db->rollback();
    exit("\nERROR3: " . $e->getMessage() . "\n");
}
$responses = $stmt->fetchall(PDO::FETCH_ASSOC); //returns an array

foreach ($responses as $response) {
    //while ($response = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $id = $response['id']; // payment.id
    $customer_id = $response['customer_id']; // customer_id
    $donation_id = $response['donation_id']; // donation_id

// STRIPE
    // LIST all (100) charges from customer
    $charges = \Stripe\Charge::all(["limit" => 100, "customer" => $customer_id]);

    foreach ($charges->data as $charge) {
        // sanity check on stripe data
        if (!empty($charge->billing_details->name)) {$customer_name = $charge->billing_details->name;} // customer_name
        if (!empty($charge->id)) {$payment_id = $charge->id;} //  VARCHAR(255)
        if (!empty($charge->amount)) {$amount_paid = ($charge->amount / 100);} // decimal(13,2)
        if (!empty($charge->amount_refunded)) {$amount_refunded = ($charge->amount_refunded / 100);} else { $amount_refunded = "0";} // decimal(13,2)
        if (!empty($charge->source->object)) {$method = $charge->source->object;} // set('card', 'bank_account', 'check', 'cash', 'stock', 'inkind')
        $processor = "Stripe"; // enum('Stripe', 'PayPal', 'P-check', 'K-check', 'cash')
        if (!empty($charge->payment_method_details->card->fingerprint)) {$fingerprint = $charge->payment_method_details->card->fingerprint;} // varchar (30)
        if (!empty($charge->source->funding)) {$card_type = $charge->source->funding;} // credit, debit, prepaid, unknown
        if (!empty($charge->created)) {$date_created = date("Y-m-d H:i:s", $charge->created);} // FROM_UNIXTIME(datetime)
        if (!empty($charge->source->last4)) {$last4 = $charge->source->last4;} // SMALLINT(4)
        if (!empty($charge->source->brand)) {$brand = $charge->source->brand;} // American Express, Diners Club, Discover, JCB, MasterCard, UnionPay, Visa, or Unknown.
        if (!empty($charge->source->exp_month)) {$exp_month = $charge->source->exp_month;} // tinyint(2)
        if (!empty($charge->source->exp_year)) {$exp_year = $charge->source->exp_year;} // SMALLINT(4)
        if (!empty($charge->status)) {$status = $charge->status;} // succeeded, pending, or failed.

        echo "===== Payment Info =====";
        echo "\nDONOR:\t\t$customer_name";
        echo "\nCustomerID:\t";if (!empty($customer_id)) {echo $customer_id;}
        echo "\nDonationID:\t";if (!empty($donation_id)) {echo $donation_id;}
        echo "\npayment_id:\t";if (!empty($payment_id)) {echo $payment_id;}
        echo "\nPaymentDate:\t";if (!empty($date_created)) {echo $date_created;}
        echo "\namount paid:\t";if (!empty($amount_paid)) {echo $amount_paid;}
        echo "\namount refnd:\t";if (!empty($amount_refunded)) {echo $amount_refunded;}
        echo "\nprocessor:\t";if (!empty($processor)) {echo $processor;}
        echo "\ncard_type:\t";if (!empty($card_type)) {echo $card_type;}
        echo "\nmethod:\t\t";if (!empty($method)) {echo $method;}
        echo "\nfingerprint:\t";if (!empty($fingerprint)) {echo $fingerprint;}
        echo "\nlast4:\t\t";if (!empty($last4)) {echo $last4;}
        echo "\nbrand:\t\t";if (!empty($brand)) {echo $brand;}
        echo "\nexp_month:\t";if (!empty($exp_month)) {echo $exp_month;}
        echo "\nexp_year:\t";if (!empty($exp_year)) {echo $exp_year;}
        echo "\nstatus:\t\t";if (!empty($status)) {echo $status;}
        echo "\n\n";

// INSERT new records IN DB //ALWAYS INSERTS INSTEAD OF ONLY NEW RECORDS
        $stmt = $db->prepare("INSERT IGNORE INTO
                                `payments`
                                SET
                                `donation_id`=:donation_id,
                                `customer_id`=:customer_id,
                                `payment_id`=:payment_id,
                                `amount`=:amount,
                                `amount_refunded` =:amount_refunded,
                                `method`=:method,
                                `processor`=:processor,
                                `fingerprint`=:fingerprint,
                                `card_type`=:card_type,
                                `date_created`=:date_created,
                                `last4`=:last4,
                                `brand`=:brand,
                                `exp_month`=:exp_month,
                                `exp_year`=:exp_year,
                                `status`=:status
                                ;");
        // Bind for Payments
        $stmt->bindParam(":donation_id", $donation_id);
        $stmt->bindParam(":customer_id", $customer_id);
        $stmt->bindParam(":payment_id", $payment_id);
        $stmt->bindParam(":amount", $amount_paid);
        $stmt->bindParam(":amount_refunded", $amount_refunded);
        $stmt->bindParam(":method", $method);
        $stmt->bindParam(":processor", $processor);
        $stmt->bindParam(":fingerprint", $fingerprint);
        $stmt->bindParam(":card_type", $card_type);
        $stmt->bindParam(":date_created", $date_created);
        $stmt->bindParam(":last4", $last4);
        $stmt->bindParam(":brand", $brand);
        $stmt->bindParam(":exp_month", $exp_month);
        $stmt->bindParam(":exp_year", $exp_year);
        $stmt->bindParam(":status", $status);
        try {
            $stmt->execute();
            $last_insert_id = $db->lastInsertId();
            if ($last_insert_id > 0) {
                echo "\nINSERTED: $last_insert_id";
                sleep(65);
            } else {
                echo "\nFAILED TO INSERT";
            }

            sleep(65);

        } catch (PDOException $e) {
            $db->rollback();
            exit("\nERROR2: " . $e->getMessage() . "\n");
        }

// UPDATE PAYMENT record IN DB
        $stmt = $db->prepare("UPDATE `payments`
        SET
            `donation_id`=:donation_id,
            `customer_id`=:customer_id,
            `amount`=:amount,
            `amount_refunded` =:amount_refunded,
            `method`=:method,
            `processor`=:processor,
            `fingerprint`=:fingerprint,
            `card_type`=:card_type,
            `date_created`=:date_created,
            `last4`=:last4,
            `brand`=:brand,
            `exp_month`=:exp_month,
            `exp_year`=:exp_year,
            `status`=:status
        WHERE
            payment_id=:payment_id;");

        // Bind for Payments
        $stmt->bindParam(":donation_id", $donation_id);
        $stmt->bindParam(":customer_id", $customer_id);
        $stmt->bindParam(":amount", $amount_paid);
        $stmt->bindParam(":amount_refunded", $amount_refunded);
        $stmt->bindParam(":method", $method);
        $stmt->bindParam(":processor", $processor);
        $stmt->bindParam(":fingerprint", $fingerprint);
        $stmt->bindParam(":card_type", $card_type);
        $stmt->bindParam(":date_created", $date_created);
        $stmt->bindParam(":last4", $last4);
        $stmt->bindParam(":brand", $brand);
        $stmt->bindParam(":exp_month", $exp_month);
        $stmt->bindParam(":exp_year", $exp_year);
        $stmt->bindParam(":status", $status);
        $stmt->bindParam(":payment_id", $payment_id);

        try {
            $stmt->execute();
            $rowCount = $stmt->rowCount();
            echo "\nUPDATED: $donation_id";
        } catch (PDOException $e) {
            $db->rollback();
            exit("\nERROR1: " . $e->getMessage() . "\n");
        }
//        $stmt = null; // reset stmt
    }
}
