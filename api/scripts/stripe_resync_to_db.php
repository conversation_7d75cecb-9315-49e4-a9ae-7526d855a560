<?php
exit('bye felicia');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Stripe Config
require_once __DIR__ . '/../config/stripe.php';

// Require database
require_once __DIR__ . '/../config/database.php';

// instantiate database
$database = new Database();
$db = $database->getConnection();
$db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

/* Map Rows and Loop Through Them */
$rows = array_map('str_getcsv', file('MEMSYS-DONORS.csv'));
$header = array_shift($rows);
$csv = array();
foreach ($rows as $row) {
    $csv[] = array_combine($header, $row);
}

// searchMemsys
function searchMemsys($id, $array)
{
    foreach ($array as $key => $val) {
        if ($val['Account'] === $id) {
            return $val;
        }
    }
    return null;
}

$charge = null;
$charges = \Stripe\Charge::all(["limit" => 100, 'expand' => ['data.customer']]); // run
//$charges = \Stripe\Charge::all(["limit" => 100, "created[lte]" => **********, 'expand' => ['data.customer']]); // limit pagination to 100 records at a time // created timestamp unixtime 2019-12-26 //**********
foreach ($charges->autoPagingIterator() as $charge) {
    // (RE)SET Variables
    $charge_id = null;
    $transaction_id = null;
    $payment_id = null;
    $customer_id = null;
    $amount = null;
    $amount_refunded = null;
    $processor = null;
    $fingerprint = null;
    $card_type = null;
    $date_created = null;
    $last4 = null;
    $brand = null;
    $exp_month = null;
    $exp_year = null;
    $status = null;
    $method = null;
    $charge_id = null;
    $memsys_id = null;
    $customer_name = null;
    $firstname = null;
    $lastname = null;
    $address1 = null;
    $address2 = null;
    $city = null;
    $state = null;
    $postal_code = null;
    $country = null;
    $email = null;
    $phone = null;
    $installment = null;
    $comments = "Imported via Stripe";
    $memsys_donor = null;

    // Do something with $charge
    // print_r($charge);die;

    try {
        // Define Variables
        if (!empty($charge->customer->subscriptions->data)) {$installment = "Monthly";} else { $installment = "One-Time";}
        if (!empty($charge->customer->id)) {$customer_id = $charge->customer->id;} else { $customer_id = "cus_DHuGkKyLK7kn99";} //  VARCHAR(255)
        if (!empty($charge->billing_details->name)) {$customer_name = $charge->billing_details->name;} else if (!empty($charge->payment_method_details->card->name)) {$customer_name = $charge->payment_method_details->card->name;} else if (!empty($charge->source->account_holder_name)) {$customer_name = $charge->source->account_holder_name;} else if (!empty($charge->customer->name)) {$customer_name = $charge->customer->name;} else if (!empty($charge->customer->description)) {$customer_name = $charge->customer->description;} // customer_name
        $firstname = ucwords(trim(strtok($customer_name, ' ')));
        $lastname = ucwords(trim(strstr($customer_name, ' ')));
        if (!empty($charge->id)) {$payment_id = $charge->id;} //  VARCHAR(255)
        if (!empty($charge->amount)) {$amount_paid = ($charge->amount / 100);} // decimal(13,2)
        if (!empty($charge->amount_refunded)) {$amount_refunded = ($charge->amount_refunded / 100);} else { $amount_refunded = "0";} // decimal(13,2)
        if (!empty($charge->source->object)) {$method = $charge->source->object;} // set('card', 'bank_account', 'check', 'cash', 'stock', 'inkind')
        $processor = "Stripe"; // enum('Stripe', 'PayPal', 'P-check', 'K-check', 'cash')
        $type = "Pledge";
        if (!empty($charge->payment_method_details->card->fingerprint)) {$fingerprint = $charge->payment_method_details->card->fingerprint;} else if (!empty($charge->source->fingerprint)) {$fingerprint = $charge->source->fingerprint;} // varchar (30)
        if (!empty($charge->source->funding)) {$card_type = $charge->source->funding;} else if (!empty($charge->payment_method_details->card->funding)) {$card_type = $charge->payment_method_details->card->funding;} else { $card_type = null;} // credit, debit, prepaid, unknown
        if (!empty($charge->created)) {$date_created = $charge->created;} // FROM_UNIXTIME(datetime)
        if (!empty($charge->source->last4)) {$last4 = $charge->source->last4;} else { $last4 = $charge->payment_method_details->card->last4;} // SMALLINT(4)
        if (!empty($charge->source->brand)) {$brand = $charge->source->brand;} else if (!empty($charge->payment_method_details->card->brand)) {$brand = $charge->payment_method_details->card->brand;} else { $brand = null;} // American Express, Diners Club, Discover, JCB, MasterCard, UnionPay, Visa, or Unknown.
        if (!empty($charge->source->exp_month)) {$exp_month = $charge->source->exp_month;} else if (!empty($charge->payment_method_details->card->exp_month)) {$exp_month = $charge->payment_method_details->card->exp_month;} else { $exp_month = null;} // tinyint(2)
        if (!empty($charge->source->exp_year)) {$exp_year = $charge->source->exp_year;} else if (!empty($charge->payment_method_details->card->exp_year)) {$exp_year = $charge->payment_method_details->card->exp_year;} else { $exp_year = null;} // SMALLINT(4)
        if (!empty($charge->status)) {$status = $charge->status;} // succeeded, pending, or failed.
        if (!empty($charge->metadata->transaction_id)) {$transaction_id = $charge->metadata->transaction_id;} // succeeded, pending, or failed.
        if (!empty($charge->customer->metadata->pay_type) && ($charge->customer->metadata->pay_type == "EFT")) {$source = "PaySol";} else { $source = "StationAdmin";}

        // memsys_id
        if (!empty($charge->customer->metadata->old_id)) {$memsys_id = $charge->customer->metadata->old_id;}
        // address1
        if (!empty($charge->customer->address->line1)) {$address1 = $charge->customer->address->line1;} else if (!empty($charge->billing_details->address->line1)) {$address1 = $charge->billing_details->address->line1;} else if (!empty($charge->customer_shipping->address->line1)) {$address1 = $charge->customer_shipping->address->line1;} else if (!empty($charge->customer->metadata->address_line1)) {$address1 = $charge->customer->metadata->address_line1;} else {
            // try and lookup donor info from csv file
            $memsys_donor = searchMemsys($memsys_id, $csv); // lookup memsys_id in csv file
            if (!empty($memsys_donor)) {
                if (!empty($memsys_donor['First1'])) {$firstname = ucwords(strtolower($memsys_donor['First1']));} else {print_r($memsys_donor);
                    echo "error memsys donor first name for $memsys_id";die;}
                $lastname = ucwords(strtolower($memsys_donor['Last1']));
                $address1 = ucwords(strtolower($memsys_donor['Deliv addr']));
                $address2 = ucwords(strtolower($memsys_donor['Sec addr']));
                $city = ucwords(strtolower($memsys_donor['City']));
                $state = $memsys_donor['State'];
                $postal_code = $memsys_donor['Zipcode'];
                if (!empty($memsys_donor['Email addr1'])) {$email = $memsys_donor['Email addr1'];} else if (!empty($memsys_donor['Email addr2'])) {$email = $memsys_donor['Email addr2'];}
                $phone = $memsys_donor['Eve phone'];
            }
        }

        // address2
        if (!empty($charge->customer->address->line2)) {$address2 = $charge->customer->address->line2;} else if (!empty($charge->billing_details->address->line2)) {$address2 = $charge->billing_details->address->line2;} else if (!empty($charge->customer_shipping->address->line2)) {$address2 = $charge->customer_shipping->address->line2;} else if (!empty($charge->customer->metadata->address_line2)) {$address2 = $charge->customer->metadata->address_line2;}
        // city
        if (!empty($charge->customer->address->city)) {$city = $charge->customer->address->city;} else if (!empty($charge->billing_details->address->city)) {$city = $charge->billing_details->address->city;} else if (!empty($charge->customer->metadata->address_city)) {$city = $charge->customer->metadata->address_city;} else if (!empty($charge->customer_shipping->address->city)) {$city = $charge->customer_shipping->address->city;}
        $city = ucwords(strtolower($city));
        // state
        if (!empty($charge->customer->address->state)) {$state = $charge->customer->address->state;} else if (!empty($charge->billing_details->address->state)) {$state = $charge->billing_details->address->state;} else if (!empty($charge->customer->metadata->address_state)) {$state = $charge->customer->metadata->address_state;} else if (!empty($charge->customer_shipping->address->state)) {$state = $charge->customer_shipping->address->state;}
        // postal_code
        if (!empty($charge->customer->address->postal_code)) {$postal_code = $charge->customer->address->postal_code;} else if (!empty($charge->billing_details->address->postal_code)) {$postal_code = $charge->billing_details->address->postal_code;} else if (!empty($charge->customer->metadata->address_zip)) {$postal_code = $charge->customer->metadata->address_zip;} else if (!empty($charge->customer_shipping->address->postal_code)) {$postal_code = $charge->customer_shipping->address->postal_code;}
        // country
        if (!empty($charge->customer->address->country)) {$country = $charge->customer->address->country;} else if (!empty($charge->billing_details->address->country)) {$country = $charge->billing_details->address->country;} else if (!empty($charge->customer->metadata->country)) {$country = $charge->customer->metadata->country;} else if (!empty($charge->customer_shipping->address->country)) {$country = $charge->customer_shipping->address->country;} else { $country = "US";}
        // phone
        if (!empty($charge->customer->phone)) {$phone = $charge->customer->phone;} else if (!empty($charge->billing_details->phone)) {$phone = $charge->billing_details->phone;} else if (!empty($charge->billing_details->address->country)) {$country = $charge->billing_details->address->country;} else if (!empty($charge->customer->metadata->phone)) {$phone = $charge->customer->metadata->phone;}
        // email
        if (!empty($charge->customer->email)) {$email = $charge->customer->email;} else if (!empty($charge->billing_details->email)) {$email = $charge->billing_details->email;} else if (!empty($charge->billing_details->address->country)) {$country = $charge->billing_details->address->country;} else if (!empty($charge->customer->metadata->email)) {$email = $charge->customer->metadata->email;}

        // cleanup
        // Phone
        if (isset($phone) && !empty($phone)) {
            $phone = preg_replace("/[^0-9]/", "", $phone); // strip everything but numbers
        } else {
            $phone = null;
        }
        // Email
        if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $email = $email;
        } else if (!empty($email)) {
            $email = str_replace(' ', '', $email);
            //var_dump($email);die; // bonnie <EMAIL>
        } else {
            $email = null;
        }
        if (strpos($email, 'vinxdame') !== false) { //fraud
            $email = "<EMAIL>";
        }
        if (strpos($email, 'aba.com') !== false) { //bad email
            $email = "<EMAIL>";
        }

        // SF city typo fix
        if ($city == "SF" || $city == "San Fransisco" || $city == "S.f" || $city == "S.f." || $city == "S F" || $city == "Sf") {
            $city = "San Francisco";
        } // Berkeley typo fix
        if ($city == "Berk" || $city == "Berkley" || $city == "Berkely" || $city == "Berkly") {
            $city = "Berkeley";
        } // Oakland typo fix
        if ($city == "Okland") {
            $city = "Oakland";
        }
        // Santa Cruz
        if ($city == "Santa Cruze") {
            $city = "Santa Cruz";
        }
        // Sebastopol
        if ($city == "Sebastapol" || $city == "Sebastepol" || $city == "Sebasopol" || $city == "Sebastotol" || $city == "Sebastople") {
            $city = "Sebastopol";
        }
        // SF city typo fix
        if ($city == "S San Franciso" || $city == "SSF") {
            $city = "South San Francisco";
        }

// Lookup Donation transaction_id from customer record if it's not in charge record
        if (empty($transaction_id)) {
            $cu = \Stripe\Customer::retrieve($customer_id);
            if (!empty($cu->metadata->transaction_id)) {$transaction_id = $cu->metadata->transaction_id;} // succeeded, pending, or failed.
        }
// else Lookup Donation transaction_id in the db from existing pledges
        if (empty($transaction_id)) {
            $stmt = $db->prepare("SELECT `transaction_id` FROM `donations`,`payments` WHERE
             `payments`.`customer_id`=:customer_id and `payments`.`donation_id` = `donations`.`id` LIMIT 1;");
            $stmt->execute(['customer_id' => $customer_id]);
            $transaction_id = $stmt->fetchColumn(); // retrieves just one column
        }

    } catch (Exception $e) {
        die("\n[✖️] " . $e->getMessage());
    }
    // Echo
    echo "===== Donation Info =====";
    echo "\nDONOR:\t\t";if (!empty($customer_name)) {echo $firstname . " " . $lastname;}
    echo "\ndate_created:\t";if (!empty($date_created)) {echo gmdate("Y-m-d H:i:s", $date_created);}
    echo "\nInstallment:\t";if (!empty($installment)) {echo $installment;}
    echo "\nLine1:\t";if (!empty($address1)) {echo $address1;}
    echo "\nLine2:\t";if (!empty($address2)) {echo $address2;}
    echo "\nCity:\t";if (!empty($city)) {echo $city;}
    echo "\nState:\t";if (!empty($state)) {echo $state;}
    echo "\nPostal Code:\t";if (!empty($postal_code)) {echo $postal_code;}
    echo "\nCountry:\t";if (!empty($state)) {echo $country;}
    echo "\nPhone:\t";if (!empty($phone)) {echo $phone;}
    echo "\nEmail:\t";if (!empty($email)) {echo $email;}
    echo "\nCustomerID:\t";if (!empty($customer_id)) {echo $customer_id;}
    echo "\nTransactionID:\t";if (!empty($transaction_id)) {echo $transaction_id;}
    echo "\npayment_id:\t";if (!empty($payment_id)) {echo $payment_id;}
    echo "\nMemSys_id:\t";if (!empty($memsys_id)) {echo $memsys_id;}
    //echo "\nPaymentDate:\t";if (!empty($date_created)) {echo $date_created;}
    if (!empty($charge->created)) {$date_created = date("Y-m-d H:i:s", $charge->created);} // FROM_UNIXTIME(datetime)
    echo "\namount paid:\t";if (!empty($amount_paid)) {echo $amount_paid;}
    echo "\namount refnd:\t";if (!empty($amount_refunded)) {echo $amount_refunded;} else {echo "0";}
    echo "\nprocessor:\t";if (!empty($processor)) {echo $processor;}
    echo "\ncard_type:\t";if (!empty($card_type)) {echo $card_type;}
    echo "\nmethod:\t\t";if (!empty($method)) {echo $method;}
    echo "\nfingerprint:\t";if (!empty($fingerprint)) {echo $fingerprint;}
    echo "\nlast4:\t\t";if (!empty($last4)) {echo $last4;}
    echo "\nbrand:\t\t";if (!empty($brand)) {echo $brand;}
    echo "\nexp_month:\t";if (!empty($exp_month)) {echo $exp_month;}
    echo "\nexp_year:\t";if (!empty($exp_year)) {echo $exp_year;}
    echo "\nstatus:\t\t";if (!empty($status)) {echo $status;}
    echo "\n\n";

    // Grab donation_id from DB
    try {
// Lookup Donation_id from Transcation ID
        $stmt = $db->prepare("SELECT `id` FROM `donations` WHERE transaction_id=:transaction_id LIMIT 1;");
        $stmt->execute(['transaction_id' => $transaction_id]);
        $donation_id = $stmt->fetchColumn(); // retrieves just one column
        $rowcount = $stmt->rowCount();

// Donation Record Not Found
        if ($rowcount == 0) {
            try {
                // TODO: add transaction_id to Stripe

                // Look up Donation_id from payments
                $stmt = $db->prepare("SELECT `donation_id` FROM `payments` WHERE customer_id=:customer_id LIMIT 1;");
                $stmt->execute(['customer_id' => $customer_id]);
                $donation_id = $stmt->fetchColumn(); // retrieves just one column
                $rowcount = $stmt->rowCount();

                // Donation Record Not Found
                // Create Donation
                if ($rowcount == 0) {

                    // [Queries] Build the queries to insert the records

                    // Donations (INSERT)
                    $donation_insert_query = "INSERT INTO
                    donations
                    SET
                    timestamp=:timestamp,
                    donor_id=:donor_id,
                    type=:type,
                    amount=:amount,
                    installment=:installment,
                    comments=:comments,
                    transaction_id=:transaction_id,
                    source=:source";

                    // Donor (SELECT) (LOOKUP by email, then phone, then (lastname & firstname & city)
                    $donor_select_query = "SELECT
                        `id` as `donor_id`,
                        `stripe_cus_id` as `customer_id`
                        FROM donors
                        WHERE
                        email=:email
                        OR phone=:phone
                        OR memsys_id=:memsys_id
                        OR stripe_cus_id=:stripe_cus_id
                        OR lastname=:lastname
                            AND firstname=:firstname
                            AND city=:city
                        ORDER BY
                        CASE
                            WHEN email=:email then 100
                            WHEN memsys_id=:memsys_id then 90
                            WHEN stripe_cus_id=:stripe_cus_id then 80
                            WHEN phone=:phone then 70
                            WHEN lastname=:lastname
                            AND firstname=:firstname
                            AND city=:city then 60
                            ELSE 0
                        END DESC
                        LIMIT 1
                        ";

                    // Donor (INSERT)
                    $donor_insert_query = "INSERT IGNORE INTO
                        donors
                        SET
                        memsys_id=:memsys_id,
                        firstname=:firstname,
                        lastname=:lastname,
                        phone=:phone,
                        email=:email,
                        address1=:address1,
                        address2=:address2,
                        city=:city,
                        state=:state,
                        country=:country,
                        postal_code=:postal_code,
                        stripe_cus_id=:stripe_cus_id";

                    // Prepare queries
                    $donation_insert_stmt = $db->prepare($donation_insert_query);
                    $donor_select_stmt = $db->prepare($donor_select_query);
                    $donor_insert_stmt = $db->prepare($donor_insert_query);

                    /* [DONOR] BIND and Lookup */
                    // Bind for Donor record LOOKUP
                    $donor_select_stmt->bindParam(":email", $email, PDO::PARAM_STR);
                    $donor_select_stmt->bindParam(":memsys_id", $memsys_id, PDO::PARAM_INT);
                    $donor_select_stmt->bindParam(":stripe_cus_id", $customer_id, PDO::PARAM_STR);
                    $donor_select_stmt->bindParam(":phone", $phone, PDO::PARAM_STR);
                    $donor_select_stmt->bindParam(":lastname", $lastname, PDO::PARAM_STR);
                    $donor_select_stmt->bindParam(":firstname", $firstname, PDO::PARAM_STR);
                    $donor_select_stmt->bindParam(":city", $city, PDO::PARAM_STR);

                    /* [DONOR] SELECT Record DB*/
                    try {
                        $donor_select_stmt->execute(); // execute query
                        $donor_id = $donor_select_stmt->fetchColumn(); // retrieves just one column
                    } catch (PDOException $e) {
                        die("\n[✖️] " . $e->getMessage());
                    }

                    /* [DONOR] INSERT Record DB */
                    if (empty($donor_id)) { // Donor NOT FOUND!
                        if (!empty($email)) {$email = strtolower($email);}
                        // Bind for Donor record INSERT (if lookup fails)
                        $donor_insert_stmt->bindParam(":memsys_id", $memsys_id);
                        $donor_insert_stmt->bindParam(":firstname", $firstname);
                        $donor_insert_stmt->bindParam(":lastname", $lastname);
                        $donor_insert_stmt->bindParam(":phone", $phone);
                        $donor_insert_stmt->bindParam(":email", $email);
                        $donor_insert_stmt->bindParam(":address1", $address1);
                        $donor_insert_stmt->bindParam(":address2", $address2);
                        $donor_insert_stmt->bindParam(":city", $city);
                        $donor_insert_stmt->bindParam(":state", $state);
                        $donor_insert_stmt->bindParam(":country", $country);
                        $donor_insert_stmt->bindParam(":postal_code", $postal_code);
                        $donor_insert_stmt->bindParam(":stripe_cus_id", $customer_id);

                        try {
                            if ($donor_insert_stmt->execute()) { // INSERT new Donor
                                $donor_id = $db->lastInsertId();
                                echo "\n[✔️ ] Successfully inserted DonorID: $donor_id\n";
                            } else {
                                throw new Exception("Failed to create donor_id: $donor_id for $firstname\n");
                            }
                            if ($donor_id == 0) {
                                var_dump($donor_id);
                                throw new Exception("Donor_id = 0: $donor_id - $firstname\n");
                            }
                        }
                        // catch exception
                         catch (Exception $e) {
                            die("\n[✖️] " . $e->getMessage());
                        }
                    }

                    // Donor has been found or created ... continue to create donation
                    /* [DONATION] DB */
                    if ($installment == "One-Time") {
                        try {
                            if ($donor_id == 0) { // one last check ;)
                                var_dump($donor_id); // bool false
                                throw new Exception("Donor_id not found: $donor_id");
                            }
                            $stmt = $db->prepare("SELECT `donation_id` FROM `donations`,`payments` WHERE
                            customer_id=:customer_id and payments.donation_id = donations.id and payments.amount = donations.amount LIMIT 1;");
                            $stmt->execute(['customer_id' => $customer_id]);
                            $donation_id = $stmt->fetchColumn(); // retrieves just one column

                            // couldn't find Donation so create one :)
                            if (empty($donation_id)) {
                                $transaction_id = md5(uniqid(rand(), true)); // true adds more_entropy
                                // Bind DONATION values
                                $donation_insert_stmt->bindParam(":timestamp", $date_created);
                                $donation_insert_stmt->bindParam(":donor_id", $donor_id);
                                $donation_insert_stmt->bindParam(":type", $type);
                                $donation_insert_stmt->bindParam(":amount", $amount_paid);
                                $donation_insert_stmt->bindParam(":installment", $installment);
                                $donation_insert_stmt->bindParam(":comments", $comments);
                                $donation_insert_stmt->bindParam(":transaction_id", $transaction_id);
                                $donation_insert_stmt->bindParam(":source", $source);

                                // INSERT Donation
                                if ($donation_insert_stmt->execute()) {
                                    global $donation_id; // needed for create.php
                                    $donation_id = $db->lastInsertId();
                                    // Update Stripe with new Transaction_id
                                    try {
                                        $charge_update = \Stripe\Charge::update(
                                            $charge->id,
                                            ['metadata' => ['transaction_id' => "$transaction_id"]]);
                                    } catch (Exception $e) {
                                        echo "\nCouldn't update Sripe charge metadata\n";
                                        die("\n[✖️] " . $e->getMessage());
                                    }

                                } else {
                                    throw new Exception("Failed to insert donation for  $donor_id");
                                }
                            }
                        }
                        // catch exception
                         catch (Exception $e) {
                            die("\n[✖️] " . $e->getMessage());
                        }
                    } else {
                        //lookup to see if we can find previous donation_id, if so use that, otherwise create :)
                        try {
                            if ($donor_id == 0) { // one last check ;)
                                var_dump($donor_id); // bool false
                                throw new Exception("Donor_id not found: $donor_id");
                            }
                            if ($installment == "Monthly") {
                                // lookup donation_id
                                $stmt = $db->prepare("SELECT `donation_id` FROM `donations`,`payments` WHERE
                            customer_id=:customer_id and payments.donation_id = donations.id and payments.amount = donations.amount LIMIT 1;");
                                $stmt->execute(['customer_id' => $customer_id]);
                                $donation_id = $stmt->fetchColumn(); // retrieves just one column

                                // couldn't find Monthly donor, so create new Monthly donation
                                if (empty($donation_id)) {
                                    $transaction_id = md5(uniqid(rand(), true)); // true adds more_entropy
                                    // Bind DONATION values
                                    $donation_insert_stmt->bindParam(":timestamp", $date_created);
                                    $donation_insert_stmt->bindParam(":donor_id", $donor_id);
                                    $donation_insert_stmt->bindParam(":type", $type);
                                    $donation_insert_stmt->bindParam(":amount", $amount_paid);
                                    $donation_insert_stmt->bindParam(":installment", $installment);
                                    $donation_insert_stmt->bindParam(":comments", $comments);
                                    $donation_insert_stmt->bindParam(":transaction_id", $transaction_id);
                                    $donation_insert_stmt->bindParam(":source", $source);

                                    // INSERT Donation
                                    if ($donation_insert_stmt->execute()) {
                                        global $donation_id; // needed for create.php
                                        $donation_id = $db->lastInsertId();
                                    } else {
                                        throw new Exception("Failed to insert donation for  $donor_id");
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            die("\n[✖️] " . $e->getMessage());
                        }
                    }
                }
                if (empty($donation_id)) {
                    throw new Exception("Unable to locate donation_id: $donation_id via donor creation");
                }
            } catch (Exception $e) {
                die("\n[✖️] " . $e->getMessage());
            }
        }
        // Donation record found or created.
        if (!empty($donation_id)) {
            // success
            echo "===== Donation Info =====";
            echo "\nDonationID:\t";if (!empty($donation_id)) {echo $donation_id;}
            echo "\n\n";
        } else {
            throw new Exception("Unable to locate donation_id: $donation_id for customer_id: $customer_id using transaction_id:$transaction_id");
        }
    } catch (Exception $e) {
        die("\n[✖️] " . $e->getMessage());
    }

    // Donor has been found or created,
    // Donation has been found or created
    // Create Payment ...
    /* [PAYMENT] DB */
    if (!empty($donation_id)) {
        // INSERT new records IN DB (if it doesn't already exist)
        $payment_insert_query = "INSERT IGNORE INTO
        `payments`
        SET
        `donation_id`=:donation_id,
        `customer_id`=:customer_id,
        `payment_id`=:payment_id,
        `amount`=:amount,
        `amount_refunded` =:amount_refunded,
        `method`=:method,
        `processor`=:processor,
        `fingerprint`=:fingerprint,
        `card_type`=:card_type,
        `date_created`=:date_created,
        `date_deposited`=:date_deposited,
        `last4`=:last4,
        `brand`=:brand,
        `exp_month`=:exp_month,
        `exp_year`=:exp_year,
        `status`=:status;";

// Prepare Query
        $payment_insert_stmt = $db->prepare($payment_insert_query);

// Bind for Payments
        $payment_insert_stmt->bindParam(":donation_id", $donation_id);
        $payment_insert_stmt->bindParam(":customer_id", $customer_id);
        $payment_insert_stmt->bindParam(":payment_id", $payment_id);
        $payment_insert_stmt->bindParam(":amount", $amount_paid);
        $payment_insert_stmt->bindParam(":amount_refunded", $amount_refunded);
        $payment_insert_stmt->bindParam(":method", $method);
        $payment_insert_stmt->bindParam(":processor", $processor);
        $payment_insert_stmt->bindParam(":fingerprint", $fingerprint);
        $payment_insert_stmt->bindParam(":card_type", $card_type);
        $payment_insert_stmt->bindParam(":date_created", $date_created);
        $payment_insert_stmt->bindParam(":date_deposited", $date_created);
        $payment_insert_stmt->bindParam(":last4", $last4);
        $payment_insert_stmt->bindParam(":brand", $brand);
        $payment_insert_stmt->bindParam(":exp_month", $exp_month);
        $payment_insert_stmt->bindParam(":exp_year", $exp_year);
        $payment_insert_stmt->bindParam(":status", $status);
        try {
            $payment_insert_stmt->execute();
            $last_id = $db->lastInsertId();
            // success
            if ($last_id > 0) {
                echo "===== Payment Info =====";
                echo "\nPaymentID:\t";if (!empty($last_id)) {echo $last_id;}
                echo "\n\n";
            } else {
                // failure
                throw new Exception("PAYMENTS INSERT (stripe-resync) FAILED - couldn't insert record for $charge_id\n");
            }

        } catch (PDOException $e) {
            //$db->rollback();
            die("\n[✖️] " . $e->getMessage());
        } catch (Exception $e) {
            echo ("\n[✖️] " . $e->getMessage());
        }

    } else {
        echo "donation_id required -> Unable to locate donation_id: $donation_id for customer_id: $charge_id using transaction_id: $transaction_id";
        exit();
    }
}
