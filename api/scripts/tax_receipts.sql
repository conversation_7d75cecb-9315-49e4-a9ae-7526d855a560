-- 2023_Tax_Receipt (final 1-24-2024)
-- ✅️ Group by donation_id
-- ✅️ Only include 'shipped' premiums in FMV calc.
-- ✅️ Exclude Fraudulent Donors
-- ✅️ Filter by payment date

SELECT
    `donors`.`id` as `donor_id`,
    `donors`.`firstname`,
    `donors`.`lastname`,
    `donors`.`address1`,
    `donors`.`address2`,
    `donors`.`city`,
    `donors`.`state`,
    `donors`.`postal_code`,
    `donors`.`country`,
    `donors`.`phone`,
    `donors`.`email`,
    SUM(total_payments - amount_refunded) AS `Total_Payment_Amount`,
    SUM(coalesce(total_fmv, 0)) AS `Total_Fair_Market_Value`,
    SUM((total_payments - amount_refunded) - coalesce(total_fmv, 0)) AS `Total_Tax_Deductible`
FROM
    donors
    
    -- 1. (JOIN donations)
INNER JOIN( -- every donation may or may not have a payment(s), which may or may not have premium(s)
    SELECT
        donations.donor_id,
        donations.id as `donation_id`, 
        total_payments,
        amount_refunded,
        total_fmv
        FROM
        donations
        
    -- 2. (JOIN Payments)
    INNER JOIN( -- every payment may, or may not have a shipment (premium)
        SELECT
        pa.donation_id,
        SUM(pa.amount) AS `total_payments`,
        SUM(pa.amount_refunded) AS `amount_refunded`,
        AVG(donation_fmv) as `total_fmv` -- consolidate each donation_fmv as table below is by payment_id
    FROM
        payments AS pa

    -- 3. (JOIN premiums)
    LEFT JOIN( -- Select (shipped) premiums from shipment list
        SELECT
            SUM(p.fmv) AS donation_fmv, -- each payment_id will have same fmv
            s.donation_id AS donation_id
        FROM
            premiums AS p
            
        -- 4. (JOIN shipments)
        INNER JOIN shipments AS s
        ON
            p.id = s.premium_id
        WHERE
            s.status = "Shipped"
        GROUP BY
            s.donation_id

    ) AS prems_temp -- 3
    ON
        prems_temp.donation_id = pa.donation_id
    WHERE
        pa.status = "succeeded"
        AND pa.date_deposited >= "2023-01-01 00:00:00"
        AND pa.date_deposited <= "2023-12-31 23:59:59"
    GROUP BY
         pa.donation_id
    ORDER BY `prems_temp`.`donation_fmv`  DESC
	    ) AS pmt_temp -- 2
	    
	ON
	    donations.id = pmt_temp.donation_id
	) AS dntn_temp -- 1

ON
    donors.id = dntn_temp.donor_id
    WHERE
    donors.type !="Fraudulent"

GROUP BY donors.id
ORDER BY donors.id ASC
