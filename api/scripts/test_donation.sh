# Randomize personal information
RANDOM_ADDRESS=$((RANDOM % 9999))
RANDOM_EMAIL="test$<EMAIL>"

# Generate a realistic phone number in the format NXX-NXX-XXXX
AREA_CODE=$((200 + RANDOM % 800))
PREFIX=$((200 + RANDOM % 800))
LINE=$((1000 + RANDOM % 9000))
RANDOM_PHONE="$AREA_CODE$PREFIX$LINE"

RANDOM_NAME=$(head /dev/urandom | tr -dc 'a-z' | fold -w 5 | head -n 1)
RANDOM_CITY=$(head /dev/urandom | tr -dc 'a-z' | fold -w 7 | head -n 1)

# Curl request with the randomized values
curl -X POST -k -4 \
     --header 'Host: api.kpfa.org' \
     --header 'Content-Type: application/json' \
     --data "{
           \"add_me\": \"\",
           \"address1\": \"$RANDOM_ADDRESS Test Ave\",
           \"address2\": null,
           \"amount\": 26,
           \"browser\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",
           \"campaign_id\": 0,
           \"cardnumber\": ****************,
           \"cardtype\": \"\",
           \"cc_securitycode\": \"123\",
           \"city\": \"$RANDOM_CITY\",
           \"comments\": \"\",
           \"country\": \"US\",
           \"date_deposited\": \"\",
           \"donation_match\": false,
           \"donotshare\": \"\",
           \"email\": \"$RANDOM_EMAIL\",
           \"exp_month\": 8,
           \"exp_year\": 23,
           \"firstname\": \"$RANDOM_NAME\",
           \"installment\": \"One-Time\",
           \"lastname\": \"Zofferson\",
           \"method\": \"card\",
           \"payment_id\": \"\",
           \"phone\": $RANDOM_PHONE,
           \"postal_code\": \"12312\",
           \"premiums\": [],
           \"premiums_cart\": [],
           \"processor\": \"\",
           \"read_onair\": false,
           \"shipping_address1\": \"$RANDOM_ADDRESS Test Ave\",
           \"shipping_address2\": null,
           \"shipping_city\": \"$RANDOM_CITY\",
           \"shipping_country\": \"US\",
           \"shipping_firstname\": \"$RANDOM_NAME\",
           \"shipping_lastname\": \"Zofferson\",
           \"shipping_postal_code\": \"12312\",
           \"shipping_state\": \"NE\",
           \"show_name\": \"\",
           \"source\": \"StationAdmin\",
           \"state\": \"NE\",
           \"status\": \"\",
           \"type\": \"Pledge\"
         }" "https://localhost/donations"

