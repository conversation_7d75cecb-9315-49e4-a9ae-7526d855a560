<?php
exit('bye felicia');
# this script iterates over stripe payments and inserts payments in SA DB.

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load Composer's autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// require database
require_once __DIR__ . '/../config/database-testing.php';

// Stripe API Key
//\Stripe\Stripe::setApiKey("sk_test_8Nhs4cLJieHplDES5aTXByoH");
\Stripe\Stripe::setApiKey("********************************");

// How many per request (100 max)

// iterates through all charges, expanding the customer object
$charges = \Stripe\Charge::all(["limit" => 100, 'expand' => ['data.customer']]);

foreach ($charges->autoPagingIterator() as $charge) {
    print_r($charge);
    // [Payments] need to have an donation associated with them
    echo $customer_name = $charge->billing_details->name; // customer_name
    //$donations_id = ""; // how do we associate charge with donation? we use sql after import to match "payments.customer_id with donations.cardnumber"

    $customer_id = $charge->customer; // VARCHAR(255)
    $payment_id = $charge->id; //  VARCHAR(255)
    $amount = ($charge->amount / 100); // decimal(13,2)
    $amount_refunded = ($charge->amount_refunded / 100); // decimal(13,2)
    $method = $charge->source->object; // set('card', 'bank_account', 'check', 'cash', 'stock', 'inkind')
    $processor = "Stripe"; // enum('Stripe', 'PayPal', 'P-check', 'K-check', 'cash')
    $fingerprint = $charge->payment_method_details->card->fingerprint; // varchar (30)
    $card_type = $charge->source->funding; // credit, debit, prepaid, unknown
    $date_created = $charge->created; // FROM_UNIXTIME(datetime)
    $last4 = $charge->source->last4; // SMALLINT(4)
    $brand = $charge->source->brand; // American Express, Diners Club, Discover, JCB, MasterCard, UnionPay, Visa, or Unknown.
    $exp_month = $charge->source->exp_month; // tinyint(2)
    $exp_year = $charge->source->exp_year; // SMALLINT(4)
    $status = $charge->status; // succeeded, pending, or failed.

    // $test_array = array($customer_id, $payment_id, $amount, $amount_refunded, $method, $processor, $fingerprint, $card_type, $date_created, $last4, $brand, $exp_month, $exp_year, $status);
    // print_r($test_array);

    echo "\n";
    echo "----";
    echo "\n";
    echo "\n";

    try {
        $sql = "INSERT INTO payments (customer_id, payment_id, amount, amount_refunded, method, processor, fingerprint, card_type, date_created, last4, brand, exp_month, exp_year, `status`) VALUES (?,?,?,?,?,?,?,?,FROM_UNIXTIME(?),LPAD(?,4,'0'),?,LPAD(?,2,'0'),?,?)";
        $stmt = $db->prepare($sql);
        $stmt->execute(array($customer_id, $payment_id, $amount, $amount_refunded, $method, $processor, $fingerprint, $card_type, $date_created, $last4, $brand, $exp_month, $exp_year, $status));
        echo "\nINSERT_ID: " . $db->lastInsertId() . "\n";
    } catch (PDOExecption $e) {
        $db->rollback();
        print "Error!: " . $e->getMessage() . "</br>";
    }

}
