<?php
// [SHIPPING] [READ]
try {
    // ACL
    $access_levels = array("Admin", "Staff");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    if ($collection == "shipping") {
        // READ shipment
        if ((!isset($_GET["start"])) && (isset($resource))) {
            // query shipment totals by date
            $stmt = $shipping->read($resource); // Call read function
            $num = $stmt->rowCount();

            // check if more than 0 records found
            if ($num > 0) {

                // initialize shipping array
                $shipping_arr = array();

                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) { // fetch readCount
                    extract($row);

                    // Build the shipping array
                    $shipping_arr = array(
                        "name" => $name,
                        "premium_id" => intval($premium_id),
                        "count" => intval($count),
                        "status" => $status,
                        "category_name" => $category_name,
                        "company" => $company,
                        "vendor_code" => $vendor_code,
                        "donors" => array(),
                    );
                } // end while for $stmt

                for ($i = 0; $i < $num; $i++) {
                    //echo $shipping_arr["records"][$i]['premium_id'] . "\n";
                    // loop through each "premium_id"
                    $premium_id = $resource;

                    // read pledge gifts table for each transaction
                    $stmt2 = $shipping->readShipment($startdate, $enddate, $premium_id, $status); // Call readShipment function
                    // number of rows
                    $num2 = $stmt2->rowCount();
                    // check if more than 0 records found
                    if ($num2 > 0) {
                        // retrieve our shipments table contents
                        while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) { // fetch readShipment
                            extract($row2);
                            // initialize shipping array
                            $donors = array(
                                "donation_id" => intval($donation_id),
                                "shipment_id" => intval($shipment_id),
                                "shipping_firstname" => $shipping_firstname,
                                "shipping_lastname" => $shipping_lastname,
                                "shipping_address1" => $shipping_address1,
                                "shipping_address2" => $shipping_address2,
                                "shipping_city" => $shipping_city,
                                "shipping_state" => $shipping_state,
                                "shipping_country" => $shipping_country,
                                "shipping_postal_code" => $shipping_postal_code,
                                "tracking_number" => $tracking_number,
                                "ship_date" => $ship_date,
                                "phone" => $phone,
                                "email" => $email,

                            );
                            array_push($shipping_arr['donors'], $donors); // add donors item to shipping array
                        } // while array extract
                    } // if > 0

                } // if loop

                echo json_encode($shipping_arr);
            } else {
                http_response_code(404); // Not Found
                throw new Exception("No shipments found");
            }
        }
        // READ shipments (latest months of New)
        if ((!isset($_GET["start"])) && (!isset($resource))) {
            $startdate = date("Y-m-d H:i:s", strtotime("-1 months"));
            $enddate = date("Y-m-d H:i:s");
            $status = "New";

            // query shipment totals by date
            $stmt = $shipping->readCount($startdate, $enddate, $status); // Call readCount function
            $num = $stmt->rowCount();

            // check if more than 0 records found
            if ($num > 0) {

                // initialize shipping array
                $shipping_arr = array();
                $shipping_arr["records"] = array();

                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) { // fetch readCount
                    extract($row);

                    // Build the shipping array
                    $shipping_item = array(
                        "name" => $name,
                        "premium_id" => intval($premium_id),
                        "count" => intval($count),
                        "status" => $status,
                        "category_name" => $category_name,
                        "company" => $company,
                        "vendor_code" => $vendor_code,
                        "donors" => array(),
                    );
                    // Push the shipping item into the shipping array
                    array_push($shipping_arr["records"], $shipping_item);
                } // end while for $stmt

                for ($i = 0; $i < $num; $i++) {
                    //echo $shipping_arr["records"][$i]['premium_id'] . "\n";
                    // loop through each "premium_id"
                    $premium_id = $shipping_arr["records"][$i]['premium_id'];

                    // read pledge gifts table for each transaction
                    $stmt2 = $shipping->readShipment($startdate, $enddate, $premium_id, $status); // Call readShipment function
                    // number of rows
                    $num2 = $stmt2->rowCount();
                    // check if more than 0 records found
                    if ($num2 > 0) {
                        // retrieve our shipments table contents
                        while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) { // fetch readShipment
                            extract($row2);
                            // initialize shipping array
                            $donors = array(
                                "donation_id" => intval($donation_id),
                                "shipment_id" => intval($shipment_id),
                                "shipping_firstname" => $shipping_firstname,
                                "shipping_lastname" => $shipping_lastname,
                                "shipping_address1" => $shipping_address1,
                                "shipping_address2" => $shipping_address2,
                                "shipping_city" => $shipping_city,
                                "shipping_state" => $shipping_state,
                                "shipping_country" => $shipping_country,
                                "shipping_postal_code" => $shipping_postal_code,
                                "tracking_number" => $tracking_number,
                                "ship_date" => $ship_date,
                                "phone" => $phone,
                                "email" => $email,

                            );
                            array_push($shipping_arr["records"][$i]['donors'], $donors); // add donors item to shipping array
                        } // while array extract
                    } // if > 0

                } // if loop

                echo json_encode($shipping_arr);
            } else {
                http_response_code(404); // Not Found
                throw new Exception("No shipments found");
            }
        }
        // SEARCH shipments
        if ((isset($_GET["start"])) && (!isset($resource))) {

            # Filter by Date
            // datetime value = YYYY-MM-DD HH:MM:SS
            // start date
            if (!empty($_GET['start'])) {
                $startdate = date('Y-m-d', strtotime($_GET['start']));
            } else {
                http_response_code(400); // Not Found
                throw new Exception("start] is required");
            }

            // end date
            if (!empty($_GET['end'])) {
                $enddate = date('Y-m-d', strtotime($_GET['end']));
            } else {
                http_response_code(400); // Not Found
                throw new Exception("end] is required");
            }

            // status date
            if (!empty($_GET['status'])) {
                $status = $_GET['status']; // status
            } else {
                http_response_code(400); // Not Found
                throw new Exception("status] is required");
            }


            // query shipment totals by date
            $stmt = $shipping->readCount($startdate, $enddate, $status); // Call readCount function
            $num = $stmt->rowCount();

            // query shipment totals
            //    $stmt = $shipping->readCount(); // Call read function (last 100 records)
            //    $num  = $stmt->rowCount();

            /*
             * query to find count, name and premium_ids
             * query to find shipping records based on premium_ids //todo loop through to add to correct array
             *
             */
            // check if more than 0 records found
            if ($num > 0) {

                // initialize shipping array
                $shipping_arr = array();
                $shipping_arr["records"] = array();

                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) { // fetch readCount
                    extract($row);

                    // Build the shipping array
                    $shipping_item = array(
                        "name" => $name,
                        "premium_id" => intval($premium_id),
                        "count" => intval($count),
                        "status" => $status,
                        "category_name" => $category_name,
                        "company" => $company,
                        "vendor_code" => $vendor_code,
                        "donors" => array(),
                    );
                    // Push the shipping item into the shipping array
                    array_push($shipping_arr["records"], $shipping_item);
                } // end while for $stmt

                for ($i = 0; $i < $num; $i++) {
                    //echo $shipping_arr["records"][$i]['premium_id'] . "\n";
                    // loop through each "premium_id"
                    $premium_id = $shipping_arr["records"][$i]['premium_id'];

                    // read pledge gifts table for each transaction
                    $stmt2 = $shipping->readShipment($startdate, $enddate, $premium_id, $status); // Call readShipment function
                    // number of rows
                    $num2 = $stmt2->rowCount();
                    // check if more than 0 records found
                    if ($num2 > 0) {
                        // retrieve our shipments table contents
                        while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) { // fetch readShipment
                            extract($row2);
                            // initialize shipping array
                            $donors = array(
                                "donation_id" => intval($donation_id),
                                "shipment_id" => intval($shipment_id),
                                "shipping_firstname" => $shipping_firstname,
                                "shipping_lastname" => $shipping_lastname,
                                "shipping_address1" => $shipping_address1,
                                "shipping_address2" => $shipping_address2,
                                "shipping_city" => $shipping_city,
                                "shipping_state" => $shipping_state,
                                "shipping_country" => $shipping_country,
                                "shipping_postal_code" => $shipping_postal_code,
                                "tracking_number" => $tracking_number,
                                "ship_date" => $ship_date,
                                "phone" => $phone,
                                "email" => $email,

                            );
                            array_push($shipping_arr["records"][$i]['donors'], $donors); // add donors item to shipping array
                        } // while array extract
                    } // if > 0

                } // if loop

                echo json_encode($shipping_arr);
            } else {
                http_response_code(404); // Not Found
                throw new Exception("No shipments found");
            }
        }
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[collection] is required.");
    }
} catch (Exception $e) {
    //  display message
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
