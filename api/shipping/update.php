<?php
// [SHIPPING] [UPDATE]
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    /*
    This function bulk updates shipping status
    */

    // initialize shipment_ids array
    $shipment_ids = array();
    $transactions_arr = array();
    // intialize success counter
    $successcount = 0;
    // intialize total counter
    $totalcount = 0;

    # Grab the variables
    // shipment_ids
    if (!empty($data->shipment_ids)) {
        $shipment_ids = $data->shipment_ids;
        $totalcount = count($data->shipment_ids);
    } else {
        throw new Exception("[shipment_ids] are required.");
    }

    // status
    if (!empty($data->status)) {
        $status = $data->status;
    } else {
        throw new Exception("[status] is required.");
    }

    // UPDATE shipping status
    foreach ($shipment_ids as $shipment_id) { // loop through list [shipment_ids]
        $updateShipping = $shipping->update($shipment_id, $status); // Call shippping update function
        if ($updateShipping[1]) { //
            $successcount++; // number of successes
            $transaction = array(
                "shipment_id" => $updateShipping[0],
                "status" => 'success',
            );
            array_push($transactions_arr, $transaction);
        } else { // couldn't send the message tell the user.
            $transaction = array(
                "shipment_id" => $updateShipping[0],
                "status" => 'error',
                "message" => $updateShipping[2],
            );
            array_push($transactions_arr, $transaction);
        }
    }

    if ($totalcount == $successcount) {
        echo json_encode(array(
            "status" => "success",
            "message" => "$successcount records successfully updated.",
            "records" => $transactions_arr,
        ));
    } else if ($successcount == 0) {
        echo json_encode(array(
            "status" => "error",
            "message" => "$successcount out of $totalcount records successfully updated",
            "records" => $transactions_arr,
        ));
    } else {
        echo json_encode(array(
            "status" => "mixed",
            "message" => "$successcount out of $totalcount records successfully updated.",
            'records' => $transactions_arr,
        ));
    }
} catch (Exception $e) {
    echo json_encode(array(
        "status" => "error",
        "message" => $err['message'],
    ));
    die;
}
