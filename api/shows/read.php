<?php
// Shows
try {
    //TODO: Proxy for https://kpfa.org/wp-json/wp/v2/episode?after=2021-05-23T10:59:59&before=2021-05-23T11:00:01&type=post&subtype=episode

    // Show Current
    if ((isset($_GET["current"]) ? $_GET["current"] : "")) {

        // read the details of show to be read
        $stmt = $show->readCurrent();
        $num = $stmt->rowCount();

        // check if more than 0 record found
        if ($num > 0) {

            // shows array
            $shows_arr = array();
            $shows_arr["records"] = array();

            // retrieve our table contents
            // fetch() is faster than fetchAll()
            // http://stackoverflow.com/questions/2770630/pdofetchall-vs-pdofetch-in-a-loop
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                // extract row
                // this will make $row['name'] to
                // just $name only
                extract($row);
                $weeksArray = explode(',', $weeks);

                $show_item = array(
                    "id" => intval($id),
                    "name" => $name,
                    "hosts" => $hosts,
                    "description" => $description,
                    "starts" => $starts,
                    "ends" => $ends,
                    "duration" => intval($duration),
                    "days" => $days,
                    "weeks" => $weeksArray,
                    "dad_id" => intval($dad_id),
                    "type" => $type,
                    "img_url" => $img_url,
                    "active" => boolval($active),
                );

                array_push($shows_arr["records"], $show_item);
            }

            echo json_encode($shows_arr);
        } else {
            http_response_code(404); // not found
            throw new Exception("No shows found.");
        }
    }

    // Search Show (By Name)
    if ((isset($_GET["s"]) ? $_GET["s"] : "")) {
        $keywords = isset($_GET["s"]) ? $_GET["s"] : json_encode(array("message" => "Please provide a search term."));

        // query shows
        $stmt = $show->searchShows($keywords);
        $num = $stmt->rowCount();

        // check if more than 0 record found
        if ($num > 0) {

            // shows array
            $shows_arr = array();
            $shows_arr["records"] = array();

            // retrieve our table contents
            // fetch() is faster than fetchAll()
            // http://stackoverflow.com/questions/2770630/pdofetchall-vs-pdofetch-in-a-loop
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                // extract row
                // this will make $row['name'] to
                // just $name only
                extract($row);
                $weeksArray = explode(',', $weeks);

                $show_item = array(
                    "id" => intval($id),
                    "name" => $name,
                    "hosts" => $hosts,
                    "description" => $description,
                    "starts" => $starts,
                    "ends" => $ends,
                    "duration" => intval($duration),
                    "days" => $days,
                    "weeks" => $weeksArray,
                    "dad_id" => intval($dad_id),
                    "type" => $type,
                    "img_url" => $img_url,
                    "active" => boolval($active),
                );

                array_push($shows_arr["records"], $show_item);
            }

            echo json_encode($shows_arr);
        } else {
            http_response_code(404); // not found
            throw new Exception("No shows found.");
        }
    }
    // Search by Date
    if ((isset($_GET["d"]) ? $_GET["d"] : "")) {
        // get date
        $date = isset($_GET["d"]) ? $_GET["d"] : "";

        // sanitize
        $date = htmlspecialchars(strip_tags($date));

        // query shows
        $stmt = $show->searchDate($date);
        $num = $stmt->rowCount();

        // check if more than 0 record found
        if ($num > 0) {

            // shows array
            $shows_arr = array();
            $shows_arr["records"] = array();

            // retrieve our table contents
            // fetch() is faster than fetchAll()
            // http://stackoverflow.com/questions/2770630/pdofetchall-vs-pdofetch-in-a-loop
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                // extract row
                // this will make $row['name'] to
                // just $name only
                extract($row);
                $weeksArray = explode(',', $weeks);

                $show_item = array(
                    "id" => intval($id),
                    "name" => $name,
                    "hosts" => $hosts,
                    "description" => $description,
                    "starts" => $starts,
                    "ends" => $ends,
                    "duration" => intval($duration),
                    "days" => $days,
                    "weeks" => $weeksArray,
                    "dad_id" => intval($dad_id),
                    "type" => $type,
                    "img_url" => $img_url,
                    "active" => boolval($active),
                );

                array_push($shows_arr["records"], $show_item);
            }

            echo json_encode($shows_arr);
        } else {
            http_response_code(404); // not found
            throw new Exception("No shows found.");
        }

    }
    // Search by DateTime
    if ((isset($_GET["dt"]) ? $_GET["dt"] : "")) {
        // get date
        $date = isset($_GET["dt"]) ? $_GET["dt"] : "";

        // sanitize
        $date = htmlspecialchars(strip_tags($date));

        // query shows
        $stmt = $show->searchDateTime($date);
        $num = $stmt->rowCount();

        // check if more than 0 record found
        if ($num > 0) {

            // shows array
            $shows_arr = array();
            $shows_arr["records"] = array();

            // retrieve our table contents
            // fetch() is faster than fetchAll()
            // http://stackoverflow.com/questions/2770630/pdofetchall-vs-pdofetch-in-a-loop
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                // extract row
                // this will make $row['name'] to
                // just $name only
                extract($row);
                $weeksArray = explode(',', $weeks);

                $show_item = array(
                    "id" => intval($id),
                    "name" => $name,
                    "hosts" => $hosts,
                    "description" => $description,
                    "starts" => $starts,
                    "ends" => $ends,
                    "duration" => intval($duration),
                    "days" => $days,
                    "weeks" => $weeksArray,
                    "dad_id" => intval($dad_id),
                    "type" => $type,
                    "img_url" => $img_url,
                    "active" => boolval($active),
                );

                array_push($shows_arr["records"], $show_item);
            }

            echo json_encode($shows_arr);
        } else {
            http_response_code(404); // not found
            throw new Exception("No shows found.");
        }
    }
    // READ shows
    if ((!isset($_GET["s"])) && (!isset($_GET["current"])) && (!isset($_GET["dt"])) && (!isset($_GET["d"]))) {
        // query shows
        $stmt = $show->readShows();
        $num = $stmt->rowCount();

        // check if more than 0 record found
        if ($num > 0) {

            // shows array
            $shows_arr = array();
            $shows_arr["records"] = array();

            // retrieve our table contents
            // fetch() is faster than fetchAll()
            // http://stackoverflow.com/questions/2770630/pdofetchall-vs-pdofetch-in-a-loop
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                // extract row
                // this will make $row['name'] to
                // just $name only
                extract($row);
                $weeksArray = explode(',', $weeks);
                $show_item = array(
                    "id" => intval($id),
                    "name" => $name,
                    "hosts" => $hosts,
                    "description" => $description,
                    "starts" => $starts,
                    "ends" => $ends,
                    "duration" => intval($duration),
                    "days" => $days,
                    "weeks" => $weeksArray,
                    "dad_id" => intval($dad_id),
                    "type" => $type,
                    "img_url" => $img_url,
                    "active" => boolval($active),
                );

                array_push($shows_arr["records"], $show_item);
            }

            echo json_encode($shows_arr);
        } else {
            http_response_code(404); // not found
            throw new Exception("No shows found.");
        }

    }
} catch (Exception $e) {
    echo json_encode(array("message" => $e->getMessage()));
}
