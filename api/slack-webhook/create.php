<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // <PERSON>lack's payload is tricky
    // it's raw url encoded and has a single tick inside string(s), that we must escape to make valid JSON
    $payload = str_replace("'", "/'", rawurldecode(ltrim(@file_get_contents('php://input'), "payload=")));

    global $slack_bearer_token;
    global $slack_channel;

    // check if payload is valid json
    if ($payload_obj = json_decode($payload)) {

        // TODO: ACL could check user against api db authorized?
        // user
        $user = $payload_obj->user->username;
        $block_id = $payload_obj->actions[0]->block_id;
        $value = $payload_obj->actions[0]->value;
        $stripe_payment_intent = $payload_obj->message->blocks[0]->accessory->value;

        if ($block_id == "payment_authorization") {
            // stripe
            $paymentIntent = $stripe->paymentIntents->retrieve($stripe_payment_intent);
            $amount = $paymentIntent->amount;
            $amount_formatted = number_format((float) $amount / 100, 2, '.', '');

            if ($value == "authorize") {
                // [stripe] Capture funds
                try {
                    $stripe_message = $paymentIntent->capture(['amount_to_capture' => $amount]); // can throw error 
                    // set response message
                    $response_message = "Successfully captured amount: `$$amount_formatted` for payment: `$stripe_payment_intent`.";
                } catch (Exception $e) {
                    $response_message = "Stripe Error: ```" . $e->getMessage() . "```";
                }
            }
            if ($value == "dont_authorize") {
                // [stripe] Capture funds
                try {
                    $stripe_message = $paymentIntent->cancel(); // can throw error 
                    // set response message
                    $response_message = "Okay, we canceled the payment for amount: `$$amount_formatted` for payment: `$stripe_payment_intent`.";
                } catch (Exception $e) {
                    $response_message = "Stripe Error: ```" . $e->getMessage() . "```";
                }
            }
        } else {
            //$response_message = "FAILURE - block_id not = donation_authorization";
        }

        // send confirmation back to slack
        if ($response_message) {
            $data = json_encode(
                array(
                    "channel" => $slack_channel,
                    "blocks" => [
                        array(
                            "type" => "section",
                            "text" => array(
                                "type" => "mrkdwn",
                                "text" => $response_message,
                            )
                        )
                    ]
                )
            );

            // curl opts
            $ch = curl_init(); // init
            curl_setopt($ch, CURLOPT_URL, "https://slack.com/api/chat.postMessage");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_POST, 1);

            $headers = array();
            $headers[] = "Authorization: Bearer $slack_bearer_token";
            $headers[] = "Content-Type: application/json";
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            // execute
            $result = json_decode(curl_exec($ch));

            // if error throw exception
            if (curl_errno($ch)) {
                throw new Exception(curl_error($ch));
            }

            // slack bad reply
            if (!$result->ok) {
                throw new Exception("error:" . $result->error . "\nerror_message: " . print_r($result->errors, 1));
            }

            // close connection
            curl_close($ch);
        } else {
            throw new Exception("Response: $response_message empty!");
        }
    } else { // json invalid
        http_response_code(400);
        throw new Exception('JSON invalid');
    }
} catch (PDOException $e) { // DB Error
    echo json_encode(array(
        "code" => $e->getCode(),
        "message" => $stmt->errorInfo()[2],
    ));
    // send mail to admin
    mail("$admin_email", "SLACK-webhook DB ERROR:", $e->getMessage());
} catch (Exception $e) {
    echo json_encode(array(
        "message" => $e->getMessage(),
    ));
    // send mail to admin
    mail("$admin_email", "SLACK-webhook ERROR:", $e->getMessage());
}
