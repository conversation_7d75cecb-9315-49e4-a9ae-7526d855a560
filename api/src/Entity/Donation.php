<?php
namespace Kpfa\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: "donations")]
class Donation
{
    #[ORM\Id]
    #[ORM\Column(type: "integer")]
    #[ORM\GeneratedValue(strategy: "AUTO")]
    private $id;

    #[ORM\Column(type: "datetime", options: ["default" => "CURRENT_TIMESTAMP"])]
    private $timestamp;

    #[ORM\Column(type: "integer", nullable: true)]
    private $account_id;

    #[ORM\Column(type: "integer")]
    private $donor_id;

    #[ORM\Column(type: "string", length: 32, unique: true)]
    private $transaction_id;

    #[ORM\Column(type: "integer", nullable: true)]
    private $payment_id;

    #[ORM\Column(type: "string", options: ["default" => "Pledge"])]
    private $type = 'Pledge';

    #[ORM\Column(type: "decimal", precision: 13, scale: 2)]
    private $amount;

    #[ORM\Column(type: "string", length: 10)]
    private $installment;

    #[ORM\Column(type: "text", nullable: true)]
    private $comments;

    #[ORM\Column(type: "string", length: 1, nullable: true)]
    private $add_me;

    #[ORM\Column(type: "boolean", options: ["default" => 0])]
    private $read_onair = false;

    #[ORM\Column(type: "string", length: 45, nullable: true)]
    private $ipaddress;

    #[ORM\Column(type: "string", length: 512, nullable: true)]
    private $browser;

    #[ORM\Column(type: "string", length: 225, nullable: true)]
    private $show_name;

    #[ORM\Column(type: "integer", nullable: true)]
    private $program_wp_id;

    #[ORM\Column(type: "string", nullable: true)]
    private $source;

    #[ORM\Column(type: "integer", nullable: true)]
    private $campaign_id;

    #[ORM\Column(type: "boolean", options: ["default" => 0])]
    private $donation_match = false;

    #[ORM\Column(type: "text", nullable: true)]
    private $premiums_cart;

    #[ORM\Column(type: "datetime", nullable: true, options: ["default" => "CURRENT_TIMESTAMP"])]
    private $updated;

    // Getters and setters
    public function getId()
    {
        return $this->id;
    }

    public function getDonorId()
    {
        return $this->donor_id;
    }

    public function setDonorId($donor_id)
    {
        $this->donor_id = $donor_id;
        return $this;
    }

    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount)
    {
        $this->amount = $amount;
        return $this;
    }

    public function getTransactionId()
    {
        return $this->transaction_id;
    }

    public function setTransactionId($transaction_id)
    {
        $this->transaction_id = $transaction_id;
        return $this;
    }

    public function getTimestamp()
    {
        return $this->timestamp;
    }

    public function setTimestamp($timestamp)
    {
        $this->timestamp = $timestamp;
        return $this;
    }

    public function getType()
    {
        return $this->type;
    }

    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }
} 