<?php
namespace Kpfa\Entity;

use <PERSON>trine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: "donors")]
class Donor
{
    #[ORM\Id]
    #[ORM\Column(type: "integer")]
    #[ORM\GeneratedValue(strategy: "AUTO")]
    private $id;

    #[ORM\Column(type: "string", length: 35, nullable: true)]
    private $firstname;

    #[ORM\Column(type: "string", length: 50, nullable: true)]
    private $lastname;

    #[ORM\Column(type: "string", length: 16, nullable: true)]
    private $phone;

    #[ORM\Column(type: "string", length: 320, nullable: true)]
    private $email;

    #[ORM\Column(type: "string", length: 225, nullable: true)]
    private $address1;

    #[ORM\Column(type: "string", length: 225, nullable: true)]
    private $address2;

    #[ORM\Column(type: "string", length: 35, nullable: true)]
    private $partner_firstname;

    #[ORM\Column(type: "string", length: 50, nullable: true)]
    private $partner_lastname;

    #[ORM\Column(type: "string", length: 225, nullable: true)]
    private $city;

    #[ORM\Column(type: "string", length: 225, nullable: true)]
    private $state;

    #[ORM\Column(type: "string", length: 225, options: ["default" => "US"])]
    private $country = 'US';

    #[ORM\Column(type: "string", length: 16, nullable: true)]
    private $postal_code;

    #[ORM\Column(type: "text", nullable: true)]
    private $notes;

    #[ORM\Column(type: "string", options: ["default" => "Individual"])]
    private $type = 'Individual';

    #[ORM\Column(type: "string", nullable: true)]
    private $membership_level;

    #[ORM\Column(type: "boolean", options: ["default" => 0])]
    private $deceased = false;

    #[ORM\Column(type: "boolean", nullable: true)]
    private $donotsolicit;

    #[ORM\Column(type: "string", length: 255, nullable: true)]
    private $stripe_cus_id;

    #[ORM\Column(type: "string", length: 255, nullable: true)]
    private $paypal_user_id;

    #[ORM\Column(type: "integer", nullable: true)]
    private $memsys_id;

    #[ORM\Column(type: "integer", nullable: true)]
    private $allegiance_id;

    #[ORM\Column(type: "datetime")]
    private $date_created;

    #[ORM\Column(type: "datetime")]
    private $date_updated;

    #[ORM\Column(type: "boolean", options: ["default" => 0])]
    private $paperless = false;

    #[ORM\Column(type: "string", length: 64, nullable: true)]
    private $paperless_token;

    // Getters and setters
    public function getId()
    {
        return $this->id;
    }

    public function getFirstname()
    {
        return $this->firstname;
    }

    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;
        return $this;
    }

    public function getLastname()
    {
        return $this->lastname;
    }

    public function setLastname($lastname)
    {
        $this->lastname = $lastname;
        return $this;
    }

    public function getEmail()
    {
        return $this->email;
    }

    public function setEmail($email)
    {
        $this->email = $email;
        return $this;
    }

    public function getFullName()
    {
        return trim($this->firstname . ' ' . $this->lastname);
    }
} 