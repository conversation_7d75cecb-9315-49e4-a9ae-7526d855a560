<?php
namespace Kpfa\Entity;

use Kpfa\Repository\ProgramRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProgramRepository::class)]
#[ORM\Table(name: "programs")]
class Program {
    #[ORM\Id, ORM\GeneratedValue, ORM\Column(type: "integer")]
    private int $id;

    #[ORM\Column(type: "integer", unique: true)]
    private int $wp_id;

    #[ORM\Column(type: "integer")]
    private int $count;

    #[ORM\Column(type: "string")]
    private string $link;

    #[ORM\Column(type: "string")]
    private string $name;

    #[ORM\Column(type: "string")]
    private string $slug;

    #[ORM\Column(type: "string")]
    private string $kpfa_b;

    #[ORM\Column(type: "string")]
    private string $program_category;

    #[ORM\Column(type: "string")]
    private string $show_time;

    #[ORM\Column(type: "string", nullable: true)]
    private ?string $confessor_id;

    #[ORM\Column(type: "string")]
    private string $default_archive_length;

    #[ORM\Column(type: "boolean")]
    private $program_inactive;

    #[ORM\Column(type: "boolean")]
    private bool $downloadable_program;

    #[ORM\Column(type: "json", nullable: true)]
    private array $weeks_that_it_airs;

    #[ORM\Column(type: "json", nullable: true)]
    private array $days;

    //Getters and Setters
    public function getId(): int {
        return $this->id;
    }

    public function getWP_id(): int {
        return $this->wp_id;
    }

    public function setWP_id(int $wp_id): self {
        $this->wp_id = $wp_id;
        return $this;
    }

    public function getCount(): int {
        return $this->count;
    }

    public function setCount(int $count): void {
        $this->count = $count;
    }

    public function getLink(): string {
        return $this->link;
    }

    public function setLink(string $link): void {
        $this->link = $link;
    }

    public function getName(): string {
        return $this->name;
    }

    public function setName(string $name): void {
        $this->name = $name;
    }

    public function getSlug(): string {
        return $this->slug;
    }

    public function setSlug(string $slug): void {
        $this->slug = $slug;
    }

    public function getKpfaB(): string {
        return $this->kpfa_b;
    }

    public function setKpfaB(string $kpfa_b): void {
        $this->kpfa_b = $kpfa_b;
    }

    public function getProgramCategory(): string {
        return $this->program_category;
    }

    public function setProgramCategory(string $program_category): void {
        $this->program_category = $program_category;
    }

    public function getShowTime(): string {
        return $this->show_time;
    }

    public function setShowTime(string $show_time): void {
        $this->show_time = $show_time;
    }

    public function getConfessorId(): string {
        return $this->confessor_id;
    }

    public function setConfessorId(string $confessor_id): void {
        $this->confessor_id = $confessor_id;
    }

    public function getDefaultArchiveLength(): string {
        return $this->default_archive_length;
    }

    public function setDefaultArchiveLength(
        string $default_archive_length,
    ): void {
        $this->default_archive_length = $default_archive_length;
    }

    public function getProgramInactive(): bool {
        return $this->program_inactive;
    }

    public function setProgramInactive(bool $program_inactive): void {
        $this->program_inactive = $program_inactive;
    }

    public function getDownloadableProgram(): bool {
        return $this->downloadable_program;
    }

    public function setDownloadableProgram(bool $downloadable_program): void {
        $this->downloadable_program = $downloadable_program;
    }

    public function getWeeksThatItAirs(): array {
        return $this->weeks_that_it_airs;
    }

    public function setWeeksThatItAirs(array $weeks_that_it_airs): void {
        $this->weeks_that_it_airs = $weeks_that_it_airs;
    }
    /**
     * @return array
     */
    public function getDays(): array {
        return $this->days;
    }

    /**
     * @param array $days
     * @return self
     */
    public function setDays(array $days): self {
        $this->days = $days;
        return $this;
    }
}
