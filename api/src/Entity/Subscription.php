<?php
namespace Kpfa\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: "subscriptions")]
class Subscription
{
    #[ORM\Id]
    #[ORM\Column(type: "string", length: 255)]
    private $id;

    #[ORM\Column(type: "string", length: 255)]
    private $customer_id;

    #[ORM\Column(type: "integer")]
    private $donor_id;

    #[ORM\Column(type: "string")]
    private $processor;

    #[ORM\Column(type: "datetime", options: ["default" => "CURRENT_TIMESTAMP"])]
    private $date_created;

    #[ORM\Column(type: "string", length: 255)]
    private $plan_id;

    #[ORM\Column(type: "string", length: 32, nullable: true)]
    private $transaction_id;

    #[ORM\Column(type: "decimal", precision: 13, scale: 2)]
    private $amount;

    #[ORM\Column(type: "string")]
    private $interval;

    #[ORM\Column(type: "boolean")]
    private $active;

    #[ORM\Column(type: "datetime", nullable: true)]
    private $date_canceled;

    // Getters and setters
    public function getId()
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    public function getDonorId()
    {
        return $this->donor_id;
    }

    public function setDonorId($donor_id)
    {
        $this->donor_id = $donor_id;
        return $this;
    }

    public function getCustomerId()
    {
        return $this->customer_id;
    }

    public function setCustomerId($customer_id)
    {
        $this->customer_id = $customer_id;
        return $this;
    }

    public function getActive()
    {
        return $this->active;
    }

    public function setActive($active)
    {
        $this->active = $active;
        return $this;
    }

    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount)
    {
        $this->amount = $amount;
        return $this;
    }

    public function getProcessor()
    {
        return $this->processor;
    }

    public function setProcessor($processor)
    {
        $this->processor = $processor;
        return $this;
    }
} 