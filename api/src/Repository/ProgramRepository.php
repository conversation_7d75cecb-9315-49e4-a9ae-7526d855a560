<?php
namespace Kpfa\Repository;

use Doctrine\ORM\EntityRepository;
use DateTime;

class ProgramRepository extends EntityRepository {
    private function calculateShowEndTime($showStartTime, $length) {
        $hours = (int) ($showStartTime / 100);
        $minutes = $showStartTime % 100;

        $totalMinutes = $hours * 60 + $minutes + $length;

        $endHours = (int) ($totalMinutes / 60);
        $endMinutes = $totalMinutes % 60;

        $showEndTime = $endHours * 100 + $endMinutes;

        return $showEndTime;
    }

    public function findShowAiring10MinutesAgo(): ?array {
        $currentDateTime = new DateTime();
        $tenMinutesAgo = $currentDateTime->modify("-10 minutes");
        $hour = (int) $tenMinutesAgo->format("Hi"); // Format it as HHmm, e.g., 1430 for 2:30 PM
        $currentDay = strtolower($tenMinutesAgo->format("l")); // e.g., 'friday'
        $currentWeek = (int) $tenMinutesAgo->format("W"); // Week of the year

        // Fetching the show_time, days, and weeks_that_it_airs fields
        $qb = $this->createQueryBuilder("p")
            ->select("p.wp_id, p.name, p.weeks_that_it_airs, p.days")
            ->where("p.program_inactive = 0")
            ->getQuery();

        $results = $qb->getResult();

        foreach ($results as $result) {
            $days = is_string($result["days"])
                ? json_decode($result["days"], true)
                : $result["days"];

            $weeks_that_it_airs = is_string($result["weeks_that_it_airs"])
                ? json_decode($result["weeks_that_it_airs"], true)
                : $result["weeks_that_it_airs"];

            if (!is_array($weeks_that_it_airs) || !is_array($days)) {
                continue;
            }

            if (in_array($currentWeek, $weeks_that_it_airs)) {
                foreach ($days as $day) {
                    if (strtolower($day["day"]) == $currentDay) {
                        $showStartTime = (int) $day["start"];
                        $showEndTime = $this->calculateShowEndTime(
                            $showStartTime,
                            (int) $day["length"],
                        );

                        if ($hour > $showStartTime && $hour <= $showEndTime) {
                            return [
                                "wp_id" => $result["wp_id"],
                                "name" => $result["name"],
                            ];
                        }
                    }
                }
            }
        }

        return null;
    }

    public function searchDate(string $date): array {
        $dateTime = new DateTime($date);
        $currentDay = strtolower($dateTime->format("l")); // e.g., 'friday'
        $currentWeek = (int) $dateTime->format("W"); // Week of the year

        // Fetching the program details where program_inactive is 0
        $qb = $this->createQueryBuilder("p")
            ->select("p.name, p.weeks_that_it_airs, p.days")
            ->where("p.program_inactive = 0")
            ->getQuery();

        $results = $qb->getResult();
        $filteredResults = [];

        foreach ($results as $result) {
            $days = is_string($result["days"])
                ? json_decode($result["days"], true)
                : $result["days"];

            $weeks_that_it_airs = is_string($result["weeks_that_it_airs"])
                ? json_decode($result["weeks_that_it_airs"], true)
                : $result["weeks_that_it_airs"];

            if (!is_array($weeks_that_it_airs) || !is_array($days)) {
                continue;
            }

            if (in_array($currentWeek, $weeks_that_it_airs)) {
                foreach ($days as $day) {
                    if (strtolower($day["day"]) == $currentDay) {
                        $filteredResults[] = [
                            "name" => $result["name"],
                            "weeks_that_it_airs" => $weeks_that_it_airs,
                            "days" => $days,
                        ];
                        break;
                    }
                }
            }
        }

        return $filteredResults;
    }
}
