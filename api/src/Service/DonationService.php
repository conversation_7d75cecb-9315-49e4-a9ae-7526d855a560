<?php
namespace Kpfa\Service;

use Kpfa\Entity\Program;

class DonationService {
    private $entityManager;

    public function __construct($entityManager) {
        $this->entityManager = $entityManager;
    }

    public function findShowAiring10MinutesAgo() {
        $programRepo = $this->entityManager->getRepository(Program::class);
        return $programRepo->findShowAiring10MinutesAgo();
    }
}
