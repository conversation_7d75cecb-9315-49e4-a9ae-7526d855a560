<?php
namespace Kpfa\Service;

use Exception;
use InvalidArgumentException;
use PDO;

// Manually include PhoneNormalization until autoloader is configured
require_once __DIR__ . '/../Utils/PhoneNormalization.php';
use App\Utils\PhoneNormalization;

class DonorService {
    private $db;
    private $donor;
    
    public function __construct($db, $donor) {
        $this->db = $db;
        $this->donor = $donor;
    }
    
    /**
     * Merge secondary donors into a primary donor
     * 
     * @param int $primaryId The ID of the primary donor
     * @param array $secondaryIds Array of secondary donor IDs to merge
     * @return array Result of the merge operation
     * @throws InvalidArgumentException If input parameters are invalid
     * @throws Exception If any part of the merge fails
     */
    public function mergeDonors(int $primaryId, array $secondaryIds) {
        $result = [
            "status" => "success",
            "merged_records" => [
                "donations" => 0,
                "subscriptions" => 0,
                "caller_logs" => 0,
            ],
            "primary_donor_id" => $primaryId,
            "secondary_donor_ids" => $secondaryIds,
            "deleted_donors" => []
        ];
        
        // Validate input parameters
        if ($primaryId <= 0) {
            throw new InvalidArgumentException("Primary donor ID must be a positive integer");
        }
        
        if (empty($secondaryIds)) {
            throw new InvalidArgumentException("No secondary donor IDs provided");
        }
        
        // Check for primary ID in secondary IDs
        if (in_array($primaryId, $secondaryIds)) {
            throw new InvalidArgumentException("Primary donor ID cannot be included in secondary donor IDs");
        }
        
        // Verify primary donor exists
        if ($this->donor->read($primaryId)->rowCount() < 1) {
            throw new Exception("Primary donor (ID: $primaryId) not found");
        }
        
        // Filter out non-existent secondary donors
        $validSecondaryIds = [];
        $skippedIds = [];
        foreach ($secondaryIds as $secondaryId) {
            if (!is_numeric($secondaryId) || $secondaryId <= 0) {
                $skippedIds[] = $secondaryId;
                continue;
            }
            if ($this->donor->read($secondaryId)->rowCount() > 0) {
                $validSecondaryIds[] = $secondaryId;
            } else {
                $skippedIds[] = $secondaryId;
            }
        }
        
        // Begin transaction
        $this->db->beginTransaction();
        
        try {
            foreach ($validSecondaryIds as $secondaryId) {
                
                // Debug check - verify donor has donations before attempting transfer
                $checkQuery = "SELECT COUNT(*) FROM donations WHERE donor_id = ?";
                $checkStmt = $this->db->prepare($checkQuery);
                if (!$checkStmt) {
                    throw new Exception("Failed to prepare query to check donations");
                }
                
                $checkStmt->bindParam(1, $secondaryId, PDO::PARAM_INT);
                if (!$checkStmt->execute()) {
                    $error = $checkStmt->errorInfo();
                    throw new Exception("Failed to check donations: " . ($error[2] ?? "Unknown error"));
                }
                
                $donationCount = $checkStmt->fetchColumn();
                
                try {
                    // Transfer donations
                    $result["merged_records"]["donations"] += $this->transferRecords(
                        "donations", 
                        "donor_id", 
                        $primaryId, 
                        $secondaryId
                    );
                } catch (Exception $e) {
                    throw new Exception("Failed to transfer donations: " . $e->getMessage());
                }

                try {
                    // Transfer subscriptions
                    $result["merged_records"]["subscriptions"] += $this->transferRecords(
                        "subscriptions", 
                        "donor_id", 
                        $primaryId, 
                        $secondaryId
                    );
                } catch (Exception $e) {
                    throw new Exception("Failed to transfer subscriptions: " . $e->getMessage());
                }
                
                try {
                    // Transfer caller logs
                    $result["merged_records"]["caller_logs"] += $this->transferRecords(
                        "caller_log", 
                        "donor_id", 
                        $primaryId, 
                        $secondaryId
                    );
                } catch (Exception $e) {
                    throw new Exception("Failed to transfer caller logs: " . $e->getMessage());
                }

            }
            
            // Create a human-friendly merge summary and append to the primary donor's notes
            $mergeSummary = $this->createMergeSummary($primaryId);
            $this->appendToNotes($primaryId, $mergeSummary);
            $result["merge_summary"] = $mergeSummary;
            
            // Verify and delete secondary donors
            foreach ($validSecondaryIds as $secondaryId) {
                if ($this->verifyNoRecordsAttached($secondaryId)) {
                    // Delete the donor if no records are attached
                    if ($this->deleteDonor($secondaryId)) {
                        $result["deleted_donors"][] = $secondaryId;
                    }
                }
            }
            
            $this->db->commit();
            
            // Make sure merged_records exists and has all required fields
            if (!isset($result["merged_records"])) {
                $result["merged_records"] = [
                    "donations" => 0,
                    "subscriptions" => 0,
                    "caller_logs" => 0
                ];
            }
            
            // Format the response to match the NextJS frontend expectations
            return [
                "success" => true,
                "message" => "Donors merged successfully",
                "details" => [
                    "donationsMoved" => $result["merged_records"]["donations"],
                    "otherRecordsMoved" => $result["merged_records"]["subscriptions"] + $result["merged_records"]["caller_logs"],
                    "donorsMerged" => count($validSecondaryIds),
                    "donorsDeleted" => count($result["deleted_donors"])
                ],
                "primary_donor_id" => $primaryId,
                "secondary_donor_ids" => $validSecondaryIds,
                "deleted_donors" => $result["deleted_donors"],
                "merge_summary" => $result["merge_summary"] ?? ""
            ];
            
        } catch (Exception $e) {
            // Ensure transaction is rolled back
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }
            
            // Add context to the exception
            
            // Return an error response instead of rethrowing
            return [
                "success" => false,
                "message" => "Failed to merge donors",
                "error" => [
                    "code" => "SERVER_ERROR",
                    "details" => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * Create a human-friendly summary of the merge operation
     * 
     * @param int $primaryId The ID of the primary donor
     * @return string Formatted summary text
     */
    private function createMergeSummary(int $primaryId): string {
        $date = date("n/j/y");
        
        $summary = "Merged on $date";
        
        $summary .= ".\n\n";
        
        return $summary;
    }
    
    /**
     * Append text to the notes field of a donor
     * 
     * @param int $donorId The donor ID
     * @param string $text Text to append
     * @return bool Success status
     */
    private function appendToNotes(int $donorId, string $text): bool {
        try {
            // First get the current notes
            $query = "SELECT notes FROM donors WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $donorId, PDO::PARAM_INT);
            $stmt->execute();
            
            $currentNotes = $stmt->fetchColumn();
            
            // Append new text to existing notes with a separator
            $updatedNotes = $currentNotes 
                ? $currentNotes . "\n\n" . $text 
                : $text;
            
            // Update the notes field
            $updateQuery = "UPDATE donors SET notes = ? WHERE id = ?";
            $updateStmt = $this->db->prepare($updateQuery);
            $updateStmt->bindParam(1, $updatedNotes, PDO::PARAM_STR);
            $updateStmt->bindParam(2, $donorId, PDO::PARAM_INT);
            
            return $updateStmt->execute();
        } catch (Exception $e) {
            error_log("Failed to update donor notes: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Helper method to transfer records from one donor to another
     * 
     * @param string $table Table name
     * @param string $donorColumn Column name for donor reference
     * @param int $primaryId Primary donor ID
     * @param int $secondaryId Secondary donor ID
     * @return int Number of records transferred
     * @throws InvalidArgumentException If input parameters are invalid
     * @throws Exception If any part of the transfer fails
     */
    private function transferRecords(string $table, string $donorColumn, int $primaryId, int $secondaryId): int {
        // Validate parameters
        if (empty($table)) {
            throw new InvalidArgumentException("Table name cannot be empty");
        }
        
        if (empty($donorColumn)) {
            throw new InvalidArgumentException("Donor column name cannot be empty");
        }
        
        if ($primaryId <= 0) {
            throw new InvalidArgumentException("Primary donor ID must be a positive integer");
        }
        
        if ($secondaryId <= 0) {
            throw new InvalidArgumentException("Secondary donor ID must be a positive integer");
        }
    
        // Check if table exists
        try {
            $tableCheck = $this->db->query("SHOW TABLES LIKE '$table'");
            if ($tableCheck->rowCount() === 0) {
                throw new Exception("Table '$table' does not exist");
            }
        } catch (Exception $e) {
            error_log("Error checking table existence: " . $e->getMessage());
            throw new Exception("Error verifying table '$table': " . $e->getMessage());
        }
        
        $query = "UPDATE `$table` SET `$donorColumn` = ? WHERE `$donorColumn` = ?";
        
        try {
            $stmt = $this->db->prepare($query);
            if (!$stmt) {
                $error = $this->db->errorInfo();
                throw new Exception("Failed to prepare update statement: " . ($error[2] ?? "Unknown error"));
            }
            
            $stmt->bindParam(1, $primaryId, PDO::PARAM_INT);
            $stmt->bindParam(2, $secondaryId, PDO::PARAM_INT);
            
            $result = $stmt->execute();
            if (!$result) {
                $error = $stmt->errorInfo();
                throw new Exception("Failed to execute update statement: " . ($error[2] ?? "Unknown error"));
            }
            
            $rowCount = $stmt->rowCount();
            
            return $rowCount;
        } catch (Exception $e) {
            throw new Exception("Failed to transfer records from $table: " . $e->getMessage());
        }
    }
    
    /**
     * Verify that a donor has no records attached to it
     * 
     * @param int $donorId The donor ID to check
     * @return bool True if no records are attached, false otherwise
     */
    private function verifyNoRecordsAttached(int $donorId): bool {
        // Tables and columns to check
        $checksToPerform = [
            ["table" => "donations", "column" => "donor_id"],
            ["table" => "subscriptions", "column" => "donor_id"],
            ["table" => "caller_log", "column" => "donor_id"]
        ];
        
        try {
            foreach ($checksToPerform as $check) {
                $query = "SELECT COUNT(*) FROM `{$check['table']}` WHERE `{$check['column']}` = ?";
                $stmt = $this->db->prepare($query);
                
                if (!$stmt) {
                    error_log("Failed to prepare verification query for {$check['table']}");
                    return false;
                }
                
                $stmt->bindParam(1, $donorId, PDO::PARAM_INT);
                
                if (!$stmt->execute()) {
                    error_log("Failed to execute verification query for {$check['table']}");
                    return false;
                }
                
                $count = $stmt->fetchColumn();
                
                if ($count > 0) {
                    return false;
                }
            }
            
            // If we've reached this point, no records were found in any table
            return true;
        } catch (Exception $e) {
            error_log("Error verifying donor records: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a donor from the database
     * 
     * @param int $donorId The donor ID to delete
     * @return bool True if deletion successful, false otherwise
     */
    private function deleteDonor(int $donorId): bool {
        try {
            $query = "DELETE FROM donors WHERE id = ?";
            $stmt = $this->db->prepare($query);
            
            if (!$stmt) {
                error_log("Failed to prepare donor deletion query");
                return false;
            }
            
            $stmt->bindParam(1, $donorId, PDO::PARAM_INT);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Error deleting donor: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find potential duplicate donors by phone number
     * Now uses improved PhoneNormalization for consistency with Keystone
     * 
     * @param string $phoneNumber The phone number to search for
     * @param int|null $excludeDonorId Optional donor ID to exclude from results (useful when checking for duplicates of an existing donor)
     * @param int $limit Maximum number of results to return (default: 10)
     * @return array Array of potential duplicate donors
     * @throws InvalidArgumentException If phone number is invalid
     * @throws Exception If database query fails
     */
    public function findPotentialDuplicatesByPhone(string $phoneNumber, ?int $excludeDonorId = null, int $limit = 10): array {
        // Validate input
        if (empty($phoneNumber)) {
            throw new InvalidArgumentException("Phone number cannot be empty");
        }
        
        if ($limit <= 0 || $limit > 100) {
            throw new InvalidArgumentException("Limit must be between 1 and 100");
        }
        
        // Use improved phone normalization
        $normalizedPhone = PhoneNormalization::normalizePhoneNumber($phoneNumber);
        
        if (!$normalizedPhone) {
            throw new InvalidArgumentException("Phone number could not be normalized");
        }
        
        // Skip if phone is not valid for duplicate detection
        if (!PhoneNormalization::isValidForDuplicateDetection($phoneNumber)) {
            return [
                'success' => true,
                'search_phone' => $phoneNumber,
                'normalized_search_phone' => $normalizedPhone,
                'total_found' => 0,
                'exact_matches' => [],
                'message' => 'Phone number not suitable for duplicate detection'
            ];
        }
        
        try {
            // Get ALL phone numbers for comparison using PhoneNormalization algorithm
            $query = "SELECT 
                        id,
                        firstname,
                        lastname,
                        phone,
                        email,
                        address1,
                        address2,
                        city,
                        state,
                        country,
                        postal_code,
                        type,
                        membership_level,
                        deceased,
                        donotsolicit,
                        date_created,
                        date_updated
                      FROM donors 
                      WHERE phone IS NOT NULL AND phone != ''";
            
            $params = [];
            $types = [];
            
            // Exclude a specific donor if requested
            if ($excludeDonorId !== null) {
                $query .= " AND id != ?";
                $params[] = $excludeDonorId;
                $types[] = PDO::PARAM_INT;
            }
            
            // Order by most recent first
            $query .= " ORDER BY date_updated DESC, id DESC";
            
            $stmt = $this->db->prepare($query);
            if (!$stmt) {
                throw new Exception("Failed to prepare duplicate search query");
            }
            
            // Bind parameters if any
            for ($i = 0; $i < count($params); $i++) {
                $stmt->bindValue($i + 1, $params[$i], $types[$i]);
            }
            
            if (!$stmt->execute()) {
                $error = $stmt->errorInfo();
                throw new Exception("Failed to execute duplicate search query: " . ($error[2] ?? "Unknown error"));
            }
            
            $allDonors = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Use PhoneNormalization to find matching phones
            $matches = [];
            $minConfidence = 85; // Use higher confidence threshold for exact matches
            
            foreach ($allDonors as $donor) {
                if (empty($donor['phone'])) {
                    continue;
                }
                
                $comparison = PhoneNormalization::comparePhoneNumbers($phoneNumber, $donor['phone']);
                
                if ($comparison['isMatch'] && $comparison['confidence'] >= $minConfidence) {
                    $donor['match_confidence'] = $comparison['confidence'];
                    $donor['match_type'] = $comparison['matchType'];
                    $donor['normalized_phone'] = $comparison['normalizedPhone2'];
                    $matches[] = $donor;
                }
            }
            
            // Sort by confidence score (highest first), then by date
            usort($matches, function($a, $b) {
                if ($a['match_confidence'] === $b['match_confidence']) {
                    // If confidence is the same, sort by date_updated (most recent first)
                    return strtotime($b['date_updated'] ?? '0') - strtotime($a['date_updated'] ?? '0');
                }
                return $b['match_confidence'] - $a['match_confidence'];
            });
            
            // Limit results
            $limitedMatches = array_slice($matches, 0, $limit);
            
            // Add formatted display information to each result
            foreach ($limitedMatches as &$donor) {
                // Add formatted display name
                $donor['display_name'] = trim(($donor['firstname'] ?? '') . ' ' . ($donor['lastname'] ?? ''));
                if (empty($donor['display_name'])) {
                    $donor['display_name'] = 'Unknown Name';
                }
                
                // Add formatted phone for display
                $donor['formatted_phone'] = PhoneNormalization::formatPhoneForDisplay($donor['phone'] ?? '');
            }
            
            return [
                'success' => true,
                'search_phone' => $phoneNumber,
                'normalized_search_phone' => $normalizedPhone,
                'total_found' => count($matches),
                'exact_matches' => $limitedMatches,
                'match_algorithm' => 'phone_normalization_v2'
            ];
            
        } catch (Exception $e) {
            error_log("Error in findPotentialDuplicatesByPhone: " . $e->getMessage());
            throw new Exception("Failed to search for duplicate donors: " . $e->getMessage());
        }
    }
    
    /**
     * Determines the correct Keystone API URL based on the server's hostname.
     * This avoids reliance on environment variables and provides a clear,
     * consistent endpoint for each environment.
     *
     * @return string The fully-qualified URL for the Keystone GraphQL endpoint.
     */
    private function getKeystoneUrl(): string {
        $host = $_SERVER['HTTP_HOST'] ?? '';

        if ($host === 'api.staging.kpfa.org') {
            return 'https://keystone.staging.kpfa.org/api/graphql';
        }

        if ($host === 'api.kpfa.org') {
            return 'https://keystone.kpfa.org/api/graphql';
        }

        // Default for local development and other environments
        return 'https://pet-leopard-fully.ngrok-free.app/api/graphql';
    }

    /**
     * Check if a donor is already part of an existing duplicate group in Keystone
     * This helps avoid creating redundant suggestions
     * 
     * @param int $donorId The donor ID to check
     * @return array|null Returns group info if donor is already in a group, null otherwise
     */
    private function checkExistingGroupMembership(int $donorId): ?array {
        $keystoneUrl = $this->getKeystoneUrl();
        
        $query = '
            query CheckDonorGroupMembership($kpfaId: Int!) {
                duplicateDonors(where: { kpfaId: { equals: $kpfaId } }) {
                    id
                    kpfaId
                    group {
                        id
                        name
                        status
                        duplicateDonors {
                            id
                            kpfaId
                            firstname
                            lastname
                        }
                    }
                    primaryGroup {
                        id
                        name
                        status
                    }
                }
            }
        ';

        $variables = [
            'kpfaId' => $donorId
        ];

        try {
            $response = $this->sendGraphQLQuery($keystoneUrl, $query, $variables);
            
            if (isset($response['data']['duplicateDonors']) && !empty($response['data']['duplicateDonors'])) {
                $donor = $response['data']['duplicateDonors'][0];
                
                // Check if donor is part of an active group (not REJECTED or MERGED)
                $group = $donor['group'] ?? $donor['primaryGroup'] ?? null;
                
                if ($group && in_array($group['status'], ['SUGGESTED', 'PENDING', 'READY_TO_MERGE'])) {
                    return [
                        'groupId' => $group['id'],
                        'groupName' => $group['name'],
                        'groupStatus' => $group['status'],
                        'memberCount' => isset($group['duplicateDonors']) ? count($group['duplicateDonors']) : 1
                    ];
                }
            }

            return null;

        } catch (Exception $e) {
            error_log("Error checking existing group membership for donor {$donorId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Enhanced check for duplicates after donor creation and notify Keystone if found
     * This method is called automatically after a new donor is created
     * 
     * Features:
     * - Checks if donor is already part of an existing group
     * - Filters out previously rejected donor pairs
     * - Uses improved PhoneNormalization for consistency with Keystone
     * - Enhanced logging and error handling
     * 
     * @param int $donorId The ID of the newly created donor
     * @return void
     */
    public function checkForDuplicatesAndNotifyKeystone(int $donorId): void {
        try {
            // First, check if donor is already part of an existing group
            $existingGroup = $this->checkExistingGroupMembership($donorId);
            if ($existingGroup) {
                error_log("Duplicate detection: Donor {$donorId} is already part of group '{$existingGroup['groupName']}' ({$existingGroup['groupStatus']}) - skipping duplicate detection");
                return;
            }

            // Get the donor whose phone was updated
            $donor = $this->getDonorById($donorId);
            if (!$donor || empty($donor['phone'])) {
                error_log("Duplicate detection: Donor {$donorId} has no phone number - skipping duplicate detection");
                return;
            }
            
            // Skip if phone is not valid for duplicate detection
            if (!PhoneNormalization::isValidForDuplicateDetection($donor['phone'])) {
                error_log("Duplicate detection: Donor {$donorId} phone '{$donor['phone']}' is not valid for duplicate detection - skipping");
                return;
            }
            
            // Check if we already have a pending suggestion for this donor
            $existingSuggestion = $this->checkExistingSuggestionForDonor($donorId);
            if ($existingSuggestion) {
                error_log("Duplicate detection: Donor {$donorId} already has a pending suggestion (ID: {$existingSuggestion['id']}) - skipping duplicate detection");
                return;
            }
            
            // Use enhanced method to find duplicates
            $duplicates = $this->findPotentialDuplicatesByPhone(
                $donor['phone'], 
                $donorId, // exclude self
                10 // limit
            );
            
                        if (!empty($duplicates['exact_matches'])) {
                try {
                    // Found potential duplicates - try to create suggestion
                    // The createSuggestionInKeystone method will check if group already exists
                    $this->createSuggestionInKeystone($donor, $duplicates['exact_matches']);
                    
                    error_log("Duplicate detection SUCCESS: Created suggestion for donor {$donorId} with " . 
                             count($duplicates['exact_matches']) . " potential duplicates (phone: {$donor['phone']})");
                } catch (Exception $e) {
                    error_log("❌ Duplicate detection FAILED for donor {$donorId}: " . $e->getMessage());
                }
            } else {
                error_log("Duplicate detection: No phone matches found for donor {$donorId} with phone '{$donor['phone']}'");
            }
            
        } catch (Exception $e) {
            // Log error but don't break donor update flow
            error_log("Enhanced duplicate detection failed for donor {$donorId}: " . $e->getMessage());
        }
    }

    /**
     * Filter out donor candidates that have been previously rejected as groups
     * Relies on DuplicateGroup status="REJECTED" instead of RejectedGroupSuggestion table
     * 
     * @param int $triggerDonorId The ID of the donor that triggered the search
     * @param array $candidateDonors Array of potential duplicate donors
     * @return array Filtered array with previously rejected pairs/groups removed
     */
    private function filterPreviouslyRejectedPairs(int $triggerDonorId, array $candidateDonors): array {
        // The checkIfGroupAlreadyExists() method includes REJECTED status in its check,
        // so rejected groups are automatically prevented from being recreated
        return $candidateDonors;
    }







    /**
     * Send GraphQL query to Keystone and get response
     * Enhanced version that returns the response data instead of fire-and-forget
     * 
     * @param string $url
     * @param string $query
     * @param array $variables
     * @return array|null
     */
    private function sendGraphQLQuery(string $url, string $query, array $variables): ?array {
        $data = [
            'query' => $query,
            'variables' => $variables
        ];
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Accept: application/json'
                ],
                'content' => json_encode($data),
                'timeout' => 10 // Allow more time for queries
            ]
        ]);
        
        $result = @file_get_contents($url, false, $context);
        
        if ($result === false) {
            throw new Exception("Failed to send GraphQL query to Keystone");
        }

        $decodedResult = json_decode($result, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response from Keystone: " . json_last_error_msg());
        }

        if (isset($decodedResult['errors'])) {
            throw new Exception("GraphQL errors: " . json_encode($decodedResult['errors']));
        }

        return $decodedResult;
    }

    /**
     * Check if a specific donor pair has been previously rejected
     * Uses DuplicateGroup status="REJECTED" instead of RejectedGroupSuggestion table
     * 
     * @param int $donorId1 First donor ID
     * @param int $donorId2 Second donor ID
     * @return bool True if the pair has been marked as rejected
     */
    public function isDonorPairPreviouslyRejected(int $donorId1, int $donorId2): bool {
        try {
            // Check if a group with these exact donors already exists with REJECTED status
            $allKpfaIds = [min($donorId1, $donorId2), max($donorId1, $donorId2)];
            sort($allKpfaIds);
            
            return $this->checkIfGroupAlreadyExistsWithStatus($allKpfaIds, ['REJECTED']);
        } catch (Exception $e) {
            error_log("Error checking if donor pair {$donorId1},{$donorId2} was previously rejected: " . $e->getMessage());
            return false; // Assume not rejected if we can't check
        }
    }

    /**
     * Get donor by ID
     * 
     * @param int $donorId
     * @return array|null
     */
    private function getDonorById(int $donorId): ?array {
        try {
            $query = "SELECT id, firstname, lastname, phone, email, address1, address2,
                             city, state, country, postal_code, type, membership_level,
                             deceased, donotsolicit, date_created, date_updated
                      FROM donors WHERE id = ?";
            
            $stmt = $this->db->prepare($query);
            if (!$stmt) {
                throw new Exception("Failed to prepare donor lookup query");
            }
            
            $stmt->bindParam(1, $donorId, PDO::PARAM_INT);
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to execute donor lookup query");
            }
            
            $donor = $stmt->fetch(PDO::FETCH_ASSOC);
            return $donor ?: null;
            
        } catch (Exception $e) {
            error_log("Error fetching donor {$donorId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Ensure a donor exists in Keystone DuplicateDonor table
     * Uses the existing createDuplicateDonor mutation pattern from next-admin
     * 
     * @param array $donor The donor data
     * @return bool Success status
     */
    private function ensureDonorExistsInKeystone(array $donor): bool {
        $keystoneUrl = $this->getKeystoneUrl();
        $kpfaId = (int)$donor['id'];

        try {
            // First check if donor already exists
            $checkQuery = '
                query CheckDonor($kpfaId: Int!) {
                    duplicateDonor(where: { kpfaId: $kpfaId }) {
                        id
                        kpfaId
                    }
                }
            ';

            $checkResponse = $this->sendGraphQLQuery($keystoneUrl, $checkQuery, ['kpfaId' => $kpfaId]);
            
            // If donor doesn't exist, create it using the existing createDuplicateDonor mutation
            if (!isset($checkResponse['data']['duplicateDonor'])) {
                $createMutation = '
                    mutation CreateDuplicateDonor(
                        $kpfaId: Int!
                        $firstname: String
                        $lastname: String
                        $phone: String
                        $email: String
                    ) {
                        createDuplicateDonor(data: {
                            kpfaId: $kpfaId
                            firstname: $firstname
                            lastname: $lastname
                            phone: $phone
                            email: $email
                        }) {
                            id
                            kpfaId
                        }
                    }
                ';

                $createVariables = [
                    'kpfaId' => $kpfaId,
                    'firstname' => $donor['firstname'] ?? '',
                    'lastname' => $donor['lastname'] ?? '',
                    'phone' => $donor['phone'] ?? '',
                    'email' => $donor['email'] ?? '',
                ];

                $createResponse = $this->sendGraphQLQuery($keystoneUrl, $createMutation, $createVariables);
                
                if (isset($createResponse['errors'])) {
                    error_log("❌ Failed to create donor {$kpfaId} in Keystone: " . json_encode($createResponse['errors']));
                    return false;
                }

                error_log("✅ Created donor {$kpfaId} in Keystone");
            } else {
                error_log("✅ Donor {$kpfaId} already exists in Keystone");
            }

            return true;

        } catch (Exception $e) {
            error_log("❌ Error ensuring donor {$kpfaId} exists in Keystone: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send suggestion to Keystone via GraphQL
     * Uses the original simple createDuplicateSuggestion mutation after ensuring donors exist
     * 
     * @param array $triggerDonor The donor that triggered the duplicate detection
     * @param array $duplicateDonors Array of potential duplicate donors
     * @return void
     */
    private function createSuggestionInKeystone(array $triggerDonor, array $duplicateDonors): void {
        $keystoneUrl = $this->getKeystoneUrl();
        
        // Step 1: Ensure all donors exist in Keystone using the existing createDuplicateDonor pattern
        error_log("🔄 Ensuring donors exist in Keystone before creating suggestion...");
        
        // Ensure trigger donor exists
        if (!$this->ensureDonorExistsInKeystone($triggerDonor)) {
            error_log("❌ Failed to ensure trigger donor exists, aborting suggestion creation");
            return;
        }

        // Ensure all candidate donors exist
        foreach ($duplicateDonors as $donor) {
            if (!$this->ensureDonorExistsInKeystone($donor)) {
                error_log("❌ Failed to ensure candidate donor {$donor['id']} exists, aborting suggestion creation");
                return;
            }
        }

        // Step 2: Check if a group with these exact donors already exists
        $allKpfaIds = array_merge([(int)$triggerDonor['id']], array_map('intval', array_column($duplicateDonors, 'id')));
        sort($allKpfaIds);
        
        if ($this->checkIfGroupAlreadyExists($allKpfaIds)) {
            error_log("ℹ️  Group with these exact donors already exists - skipping creation");
            return;
        }
        
        // Step 3: Create the duplicate suggestion using the original simple mutation
        $mutation = '
            mutation CreateDuplicateSuggestion(
                $triggerDonorKpfaId: Int!
                $candidateKpfaIds: [Int!]!
                $matchingCriteria: JSON
            ) {
                createDuplicateSuggestion(
                    triggerDonorKpfaId: $triggerDonorKpfaId
                    candidateKpfaIds: $candidateKpfaIds
                    matchingCriteria: $matchingCriteria
                ) {
                    id
                    name
                    status
                }
            }
        ';
        
        // Check if we have additional matching criteria
        $additionalMatches = 0;
        foreach ($duplicateDonors as $duplicate) {
            if (!empty($triggerDonor['email']) && !empty($duplicate['email']) && 
                strtolower($triggerDonor['email']) === strtolower($duplicate['email'])) {
                $additionalMatches++;
            }
            if (!empty($triggerDonor['lastname']) && !empty($duplicate['lastname']) && 
                strtolower($triggerDonor['lastname']) === strtolower($duplicate['lastname'])) {
                $additionalMatches++;
            }
        }
        
        $variables = [
            'triggerDonorKpfaId' => (int)$triggerDonor['id'],
            'candidateKpfaIds' => array_map('intval', array_column($duplicateDonors, 'id')),
            'matchingCriteria' => [
                'matchType' => 'exact_phone',
                'searchPhone' => $triggerDonor['phone'],
                'normalizedPhone' => PhoneNormalization::normalizePhoneNumber($triggerDonor['phone']),
                'totalFound' => count($duplicateDonors),
                'detectedAt' => date('Y-m-d H:i:s'),
                'detectionMethod' => 'real_time_php',
                'additionalMatches' => $additionalMatches,
                'donorDetails' => array_map(function($d) {
                    return [
                        'kpfaId' => (int)$d['id'],
                        'name' => trim(($d['firstname'] ?? '') . ' ' . ($d['lastname'] ?? '')),
                        'phone' => $d['phone'],
                        'email' => $d['email'] ?? null
                    ];
                }, $duplicateDonors)
            ]
        ];
        
        $success = $this->sendGraphQLRequest($keystoneUrl, $mutation, $variables);
        
        if (!$success) {
            error_log("❌ Failed to create duplicate suggestion in Keystone for donor {$triggerDonor['id']}");
            throw new Exception("Failed to create duplicate suggestion in Keystone");
        }
    }

    /**
     * Send GraphQL request to Keystone
     * 
     * @param string $url
     * @param string $query
     * @param array $variables
     * @return bool Returns true on success, false on failure
     */
    private function sendGraphQLRequest(string $url, string $query, array $variables): bool {
        $data = [
            'query' => $query,
            'variables' => $variables
        ];
        
        $jsonData = json_encode($data);
        
        // Log the request details for debugging
        error_log("🔥 DUPLICATE DETECTION: Sending GraphQL request to Keystone");
        error_log("📍 URL: {$url}");
        error_log("🏠 HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'not set'));
        error_log("👤 Trigger Donor ID: " . ($variables['triggerDonorKpfaId'] ?? 'not set'));
        error_log("🎯 Candidate IDs: " . json_encode($variables['candidateKpfaIds'] ?? []));
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Accept: application/json'
                ],
                'content' => $jsonData,
                'timeout' => 10, // Increased timeout for better debugging
                'ignore_errors' => true // This allows us to get error responses
            ]
        ]);
        
        // Make request and capture detailed response
        $result = file_get_contents($url, false, $context);
        
        // Get HTTP response headers for debugging
        $httpResponseHeader = $http_response_header ?? [];
        $httpCode = 'unknown';
        
        if (!empty($httpResponseHeader)) {
            $httpCode = $httpResponseHeader[0] ?? 'unknown';
            error_log("📡 HTTP Response: {$httpCode}");
        }
        
        if ($result === false) {
            error_log("❌ FAILED to send duplicate suggestion to Keystone");
            error_log("🔧 Last error: " . (error_get_last()['message'] ?? 'No error details'));
            
            // Try to get more error details
            $errorDetails = error_get_last();
            if ($errorDetails) {
                error_log("🔍 Error details: " . json_encode($errorDetails));
            }
            return false;
        }
        
        // Parse and log the response for debugging
        $responseData = json_decode($result, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("❌ Invalid JSON response from Keystone: " . json_last_error_msg());
            error_log("📄 Raw response: " . $result);
            return false;
        }
        
        // Check for GraphQL errors
        if (isset($responseData['errors']) && !empty($responseData['errors'])) {
            error_log("❌ GraphQL Errors: " . json_encode($responseData['errors']));
            
            // Check if this is a duplicate name error (which we might want to handle differently)
            foreach ($responseData['errors'] as $error) {
                if (isset($error['extensions']['prisma']['code']) && $error['extensions']['prisma']['code'] === 'P2002') {
                    error_log("⚠️  Duplicate group name constraint - group may already exist");
                }
            }
            return false;
        }
        
        // Check for successful creation
        if (isset($responseData['data']['createDuplicateSuggestion'])) {
            $suggestion = $responseData['data']['createDuplicateSuggestion'];
            error_log("🎉 Created suggestion: ID={$suggestion['id']}, Name={$suggestion['name']}, Status={$suggestion['status']}");
            return true;
        }
        
        error_log("⚠️  Unexpected response structure from Keystone");
        error_log("📄 Response: " . json_encode($responseData));
        return false;
    }

    /**
     * Check if a group with the exact same donor combination already exists
     * 
     * @param array $kpfaIds Sorted array of KPFA IDs
     * @return bool True if group exists, false otherwise
     */
    private function checkIfGroupAlreadyExists(array $kpfaIds): bool {
        // Check for any active group (excluding REJECTED for new suggestion creation)
        return $this->checkIfGroupAlreadyExistsWithStatus($kpfaIds, ["SUGGESTED", "PENDING", "READY_TO_MERGE", "REJECTED"]);
    }

    /**
     * Check if a group with the exact same donor combination already exists with specific statuses
     * 
     * @param array $kpfaIds Sorted array of KPFA IDs
     * @param array $statuses Array of statuses to check for
     * @return bool True if group exists with any of the specified statuses, false otherwise
     */
    private function checkIfGroupAlreadyExistsWithStatus(array $kpfaIds, array $statuses): bool {
        try {
            $keystoneUrl = $this->getKeystoneUrl();

            $query = '
                query CheckExistingGroups($kpfaIds: [Int!]!, $statuses: [String!]!) {
                    duplicateGroups(
                        where: {
                            AND: [
                                { status: { in: $statuses } }
                                { duplicateDonors: { every: { kpfaId: { in: $kpfaIds } } } }
                                { duplicateDonors: { some: {} } }
                            ]
                        }
                    ) {
                        id
                        name
                        status
                        duplicateDonors {
                            kpfaId
                        }
                        primaryDonor {
                            kpfaId
                        }
                    }
                }
            ';

            $variables = ['kpfaIds' => $kpfaIds, 'statuses' => $statuses];
            
            $data = ['query' => $query, 'variables' => $variables];
            $context = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => [
                        'Content-Type: application/json',
                        'Accept: application/json'
                    ],
                    'content' => json_encode($data),
                    'timeout' => 5,
                    'ignore_errors' => true
                ]
            ]);
            
            $result = @file_get_contents($keystoneUrl, false, $context);
            
            if ($result === false) {
                return false; // If we can't check, assume it doesn't exist
            }
            
            $response = json_decode($result, true);
            if (json_last_error() !== JSON_ERROR_NONE || isset($response['errors'])) {
                return false; // If there are errors, assume it doesn't exist
            }
            
            // Check if any existing group has the exact same donor set
            if (isset($response['data']['duplicateGroups'])) {
                foreach ($response['data']['duplicateGroups'] as $group) {
                    $groupKpfaIds = array_merge(
                        array_column($group['duplicateDonors'] ?? [], 'kpfaId'),
                        $group['primaryDonor'] ? [$group['primaryDonor']['kpfaId']] : []
                    );
                    sort($groupKpfaIds);
                    
                    if ($groupKpfaIds === $kpfaIds) {
                        error_log("📋 Found existing group '{$group['name']}' ({$group['status']}) with same donors: " . implode(',', $kpfaIds));
                        return true;
                    }
                }
            }

            return false;

        } catch (Exception $e) {
            error_log("Error checking existing groups: " . $e->getMessage());
            return false; // If we can't check, assume it doesn't exist
        }
    }

    /**
     * Check if donor already has an existing pending suggestion in Keystone
     * 
     * @param int $donorId
     * @return array|null Returns suggestion info if exists, null otherwise
     */
    private function checkExistingSuggestionForDonor(int $donorId): ?array {
        try {
            $keystoneUrl = $this->getKeystoneUrl();

            $query = '
                query CheckExistingSuggestion($kpfaId: Int!) {
                    duplicateGroups(
                        where: {
                            AND: [
                                { status: { equals: "SUGGESTED" } }
                                { 
                                    OR: [
                                        { duplicateDonors: { some: { kpfaId: { equals: $kpfaId } } } }
                                        { primaryDonor: { kpfaId: { equals: $kpfaId } } }
                                    ]
                                }
                            ]
                        }
                    ) {
                        id
                        name
                        status
                        createdAt
                    }
                }
            ';

            $variables = [
                'kpfaId' => $donorId
            ];

            $response = $this->sendGraphQLQuery($keystoneUrl, $query, $variables);
            
            if (isset($response['data']['duplicateGroups']) && !empty($response['data']['duplicateGroups'])) {
                return $response['data']['duplicateGroups'][0]; // Return the first suggestion found
            }

            return null;

        } catch (Exception $e) {
            error_log("Error checking existing suggestions in Keystone: " . $e->getMessage());
            return null; // Don't block processing if check fails
        }
    }
}
