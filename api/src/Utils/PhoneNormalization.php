<?php

namespace App\Utils;

/**
 * Phone number normalization and matching utilities
 * PHP port of the Keystone phone normalization logic
 * Ensures consistency between PHP and Keystone duplicate detection
 */
class PhoneNormalization
{
    /**
     * Normalize a phone number for comparison
     * - Removes all non-numeric characters
     * - Handles US country code (+1)
     * - Returns standardized format for comparison
     */
    public static function normalizePhoneNumber(?string $phone): ?string
    {
        if (empty($phone) || !is_string($phone)) {
            return null;
        }

        // Remove all non-numeric characters
        $digitsOnly = preg_replace('/\D/', '', $phone);
        
        if (empty($digitsOnly)) {
            return null;
        }

        // Handle US numbers with country code
        if (strlen($digitsOnly) === 11 && str_starts_with($digitsOnly, '1')) {
            // Remove the '1' prefix for US numbers
            return substr($digitsOnly, 1);
        }

        // Handle standard 10-digit US numbers
        if (strlen($digitsOnly) === 10) {
            return $digitsOnly;
        }

        // For international numbers or other formats, return as-is
        // but only if they're reasonable length (7-15 digits)
        if (strlen($digitsOnly) >= 7 && strlen($digitsOnly) <= 15) {
            return $digitsOnly;
        }

        // Invalid phone number
        return null;
    }

    /**
     * Compare two phone numbers and determine if they match
     * Returns detailed matching information
     */
    public static function comparePhoneNumbers(?string $phone1, ?string $phone2): array
    {
        $normalized1 = self::normalizePhoneNumber($phone1);
        $normalized2 = self::normalizePhoneNumber($phone2);

        $result = [
            'isMatch' => false,
            'confidence' => 0,
            'normalizedPhone1' => $normalized1 ?? '',
            'normalizedPhone2' => $normalized2 ?? '',
            'matchType' => 'none'
        ];

        // If either phone is null/invalid, no match
        if (!$normalized1 || !$normalized2) {
            return $result;
        }

        // Exact match
        if ($normalized1 === $normalized2) {
            $result['isMatch'] = true;
            $result['confidence'] = 100;
            $result['matchType'] = 'exact';
            return $result;
        }

        // Check if one is a subset of the other (for partial matches)
        $longer = strlen($normalized1) > strlen($normalized2) ? $normalized1 : $normalized2;
        $shorter = strlen($normalized1) > strlen($normalized2) ? $normalized2 : $normalized1;

        // If the shorter number is the last N digits of the longer number
        if (str_ends_with($longer, $shorter) && strlen($shorter) >= 7) {
            $result['isMatch'] = true;
            $result['confidence'] = 85;
            $result['matchType'] = 'without_country_code';
            return $result;
        }

        // Check for partial matches (last 7 digits for US numbers)
        if (strlen($normalized1) >= 7 && strlen($normalized2) >= 7) {
            $last7_1 = substr($normalized1, -7);
            $last7_2 = substr($normalized2, -7);
            
            if ($last7_1 === $last7_2) {
                $result['isMatch'] = true;
                $result['confidence'] = 75;
                $result['matchType'] = 'partial';
                return $result;
            }
        }

        return $result;
    }

    /**
     * Check if a phone number is likely a US number
     */
    public static function isUSPhoneNumber(?string $phone): bool
    {
        $normalized = self::normalizePhoneNumber($phone);
        if (!$normalized) {
            return false;
        }
        
        // US numbers are typically 10 digits
        return strlen($normalized) === 10;
    }

    /**
     * Format a phone number for display
     */
    public static function formatPhoneForDisplay(?string $phone): string
    {
        $normalized = self::normalizePhoneNumber($phone);
        if (!$normalized) {
            return $phone ?? '';
        }

        // Format US numbers as (XXX) XXX-XXXX
        if (strlen($normalized) === 10) {
            return sprintf(
                '(%s) %s-%s',
                substr($normalized, 0, 3),
                substr($normalized, 3, 3),
                substr($normalized, 6)
            );
        }

        // For other formats, just return the normalized version
        return $normalized;
    }

    /**
     * Validate if a phone number is reasonable for duplicate detection
     */
    public static function isValidForDuplicateDetection(?string $phone): bool
    {
        $normalized = self::normalizePhoneNumber($phone);
        if (!$normalized) {
            return false;
        }

        // Must be at least 7 digits
        if (strlen($normalized) < 7) {
            return false;
        }

        // Check for obviously fake numbers
        $fakePatterns = [
            '/^1234567/', // 1234567...
            '/^0000000/', // 0000000...
            '/^1111111/', // 1111111...
            '/^2222222/', // etc.
            '/^3333333/',
            '/^4444444/',
            '/^5555555/',
            '/^6666666/',
            '/^7777777/',
            '/^8888888/',
            '/^9999999/',
            '/^5551212/', // Classic fake number
            '/^8675309/', // Jenny's number
        ];

        foreach ($fakePatterns as $pattern) {
            if (preg_match($pattern, $normalized)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Find potential duplicates using the same algorithm as Keystone
     * This replaces the simple character replacement approach
     */
    public static function findMatchingPhones(array $phoneNumbers, string $targetPhone, int $minConfidence = 75): array
    {
        $matches = [];
        
        foreach ($phoneNumbers as $index => $phone) {
            $comparison = self::comparePhoneNumbers($targetPhone, $phone);
            
            if ($comparison['isMatch'] && $comparison['confidence'] >= $minConfidence) {
                $matches[] = [
                    'index' => $index,
                    'phone' => $phone,
                    'confidence' => $comparison['confidence'],
                    'matchType' => $comparison['matchType'],
                    'normalizedPhone' => $comparison['normalizedPhone2']
                ];
            }
        }

        // Sort by confidence score (highest first)
        usort($matches, function($a, $b) {
            return $b['confidence'] - $a['confidence'];
        });

        return $matches;
    }
} 