<?php
/* Create functions */
// required headers
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
try {
    // CREATE Streamer Stats
    if (!isset($_POST['number']) && !isset($_POST['action'])) {
        //disable verify ssl
        $arrContextOptions = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );

        // KPFA STREAM @ HE
        $src = file_get_contents('https://streams.kpfa.org/status-json.xsl');
        $array = json_decode($src, true);
        $source_arr = isset($array['icestats']['source']) ? $array['icestats']['source'] : [];

        // iterate through $source_arr and filter when server_name contains KPFA
        $new = is_array($source_arr) ? array_filter($source_arr, function ($var) {
            if (isset($var['server_name'])) {
                return (str_contains($var['server_name'], 'KPFA'));
            }
            return false;
        }) : [];

        $total = 0;
        foreach ($new as $key => $value) {
            $total += $value['listeners'];
        }
        
        //  Program info from KPFA.org
        $src2 = file_get_contents('https://kpfa.org/wp-json/wp/v2/episode?per_page=1');
        $array2 = json_decode($src2, true);
        // SHOW
        $show = isset($array2['0']['program_name']) ? $array2['0']['program_name'] : ''; // title of show
        // EPISODE
        $episode = isset($array2['0']['title']['rendered']) ? $array2['0']['title']['rendered'] : ''; // show episode
        // EXCERPT
        //$excerpt = $array2['0']['excerpt']['rendered']; // show excerpt
        // CONTENT
        //$content = $array2['0']['content']['rendered']; // show content
        // ID
        $post_id = isset($array2['0']['id']) ? $array2['0']['id'] : 0; // post id

        // set stats property values
        $stat->stream = "KPFA";
        $stat->streamers = $total;
        $stat->show = $show;
        $stat->episode = $episode;
        $stat->post_id = $post_id;

        // create the stats
        if ($stat->createStreamers()) {
            echo json_encode(array('message' => "stats were created.", "listeners" => intval($total)));
        }

        // if unable to create the stats, tell the user
        else {
            throw new Exception("Unable to create stats.");
        }
    }

    // New IcecastStats
    if (isset($_POST['action']) && !isset($_POST['number'])) {
        header("icecast-auth-user: 1");

        // payload (sanitize and assign)
        if (isset($_POST['client'])) {
            $stat->icecast_id = intval($_POST['client']);
        }
        if (isset($_POST['ip'])) {
            $stat->ip = $_POST['ip'];
        }
        if (isset($_POST['mount'])) {
            $stat->mount = $_POST['mount'];
        }
        if (isset($_POST['agent'])) {
            $stat->agent = $_POST['agent'];
        }
        if (isset($_POST['duration'])) {
            $stat->duration = intval($_POST['duration']);
        }
        if (isset($_POST['sent'])) {
            $stat->sent_bytes = intval($_POST['sent']);
        }
        if (isset($_POST['referer'])) {
            $stat->referer = strip_tags($_POST['referer']);
        }
        if (isset($_POST['server'])) {
            $stat->server = $_POST['server'];
        }
        if (isset($_POST['port'])) {
            $stat->port = intval($_POST['port']);
        }

        // create the stats
        if (isset($_POST['action']) && $_POST['action'] == "listener_add" && $stat->createIcecastStats_join()) {
            echo json_encode(array('message' => "success"));
        }
        if (isset($_POST['action']) && $_POST['action'] == "listener_remove" && $stat->createIcecastStats_leave()) {
            echo json_encode(array('message' => "success"));
        } else {
            echo json_encode(array('message' => "failure"));
        }
    }

    // Create Caller
    if (isset($_POST['number'])) {
        // [Required Section]
        try { // number
            if (!empty($data->number) && is_numeric($data->number)) {
                $number = $data->number;
            } elseif (!empty($_POST['number'])) { // POSTDATA for FREEPBX extensions_custom.conf
                $number = $_POST['number'];
            } else {
                throw new Exception("[number] is required and must be numeric.");
            }
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
            die;
        }
        try { // duration
            if (!empty($data->duration) && is_numeric($data->duration)) {
                $duration = $data->duration;
            } elseif (!empty($_POST['duration'])) { // POSTDATA for FREEPBX extensions_custom.conf
                $duration = $_POST['duration'];
            } else {
                throw new Exception("[duration] is required and must be numeric.");
            }
        } catch (Exception $e) {
            http_response_code(400); // Bad Request
            echo json_encode(array(
                "status" => "error",
                "message" => $e->getMessage(),
            ));
            die;
        }

        // create the stats
        if ($stat->createCaller($number, $duration)) {
            http_response_code(200); // Success
            echo json_encode(array(
                "status" => "success",
                "message" => "Caller was created for: $number",
            ));
            die;
        }
    }
} catch (Exception $e) {
    http_response_code(400); // Bad Request
    echo json_encode(array("message" => $e->getMessage()));
    die;
}
