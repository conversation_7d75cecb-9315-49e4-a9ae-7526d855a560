<?php
// stats
try {
    $resources = array("downloads", "streams", "campaigns", "calls");

    if ($resource) { // check if resource has value
        // check to see if resource contains sub resource
        if (strpos($resource, '/') !== false) {
            // explodable
            $piece = explode("/", $resource);
            $resource = $piece[0]; // reassign resource
            $entity = $piece[1]; // entity
        } else {
            // not explodable
        }
    }

    if ($collection == "stats") {
        // resource check
        if (!in_array($resource, $resources) && $resource !== null) {
            foreach ($resources as $resource) {
                $resource_array["resources"][] =
                    array(
                        "links" => array(
                            "rel" => "self",
                            "href" => "$api_url/$collection/$resource/",
                        ),
                    );
            }
            echo json_encode($resource_array);
            die;
        }
        // resource check
        if (in_array($resource, $resources)) {
            if ($resource == "downloads") {
                // Download Stats (from nginx mirror on archives)
                if (isset($entity) && $entity == "add") {
                    // payload (get from mirror'd request w/ headers)
                    if (isset($_SERVER['REQUEST_TIME'])) {
                        $stat->timestamp = $_SERVER['REQUEST_TIME'];
                    } else {
                        http_response_code(400); // bad request
                        throw new Exception("[REQUEST_TIME] required");
                    }
                    if (isset($_SERVER['HTTP_REFERER'])) {
                        $stat->referer = $_SERVER['HTTP_REFERER'];
                    } else {
                        http_response_code(400); // bad request
                        throw new Exception("[HTTP_REFERER] required");
                    }
                    if (isset($_SERVER['HTTP_USER_AGENT'])) {
                        $stat->useragent = $_SERVER['HTTP_USER_AGENT'];
                    } else {
                        http_response_code(400); // bad request
                        throw new Exception("[HTTP_USER_AGENT] required");
                    }
                    if (isset($_SERVER['HTTP_X_CLIENTIP'])) {
                        $stat->ipaddress = $_SERVER['HTTP_X_CLIENTIP'];
                    } else {
                        http_response_code(400); // bad request
                        throw new Exception("[HTTP_X_CLIENTIP] required");
                    }
                    if (isset($_SERVER['HTTP_X_ORIGINAL_URI'])) {
                        $stat->request = $_SERVER['HTTP_X_ORIGINAL_URI'];
                    } else {
                        http_response_code(400); // bad request
                        throw new Exception("[HTTP_X_ORIGINAL_URI] required");
                    }

                    // Clean up date to search
                    $remove = array("/data/", ".mp3");
                    $mp3_date = str_replace($remove, "", $stat->request); // 20210523-Sun1100

                    // Make a dateTime object from string
                    $dateTime = dateTime::createFromFormat('Ymd-*Hi', "$mp3_date");

                    // convert dateTime to ISO8601 like format for WP: 2021-05-23T10:59:59
                    $before = (clone $dateTime)->modify('+1 second')->format('Y-m-d\TH:i:s');
                    $after = (clone $dateTime)->modify('-1 second')->format('Y-m-d\TH:i:s');

                    // Query wp-api for episode info:
                    $wp_api_data = json_decode(file_get_contents("https://kpfa.org/wp-json/wp/v2/episode?before=$before&after=$after&type=post&subtype=episode"));

                    if (isset($wp_api_data[0]->program_name)) {
                        $stat->show = $wp_api_data[0]->program_name;
                    }
                    if (isset($wp_api_data[0]->title->rendered)) {
                        $stat->episode = $wp_api_data[0]->title->rendered;
                    }
                    if (isset($wp_api_data[0]->id)) {
                        $stat->post_id = $wp_api_data[0]->id;
                    }

                    // create the stats
                    if ($stat->createDownload()) {
                        http_response_code(200); // success
                        echo json_encode(array('message' => "success"));
                    } else {
                        http_response_code(500);
                        echo json_encode(array("message" => $e->getMessage()));
                        mail($admin_email, "Download STAT ERROR:", $e->getMessage());
                    }
                } else {
                    // READ Download Stats by date time
                    if (isset($_GET['start'])) {
                        $stat->datetime_start = $_GET['start'];
                    } else {
                        http_response_code(400); // bad request
                        throw new Exception("[start] is required");
                    }
                    if (isset($_GET['end'])) {
                        $stat->datetime_end = $_GET['end'];
                    } else {
                        http_response_code(400); // bad request
                        throw new Exception("[end] is required");
                    }

                    // READ the Download stats by date time
                    if ($stat->readDownloadbyDateTime()) {
                        if ($stmt->fetchAll()) {
                            echo json_encode($stmt->fetchAll()); // fetch query and return as json
                            die;
                        }
                    }
                }
            }
            if ($resource == "streams") {
                $stmt = $stat->readStreamers();
                $num = $stmt->rowCount();
                // check if more than 0 record found
                if ($num > 0) {
                    // initialize premium_categories array
                    $stats_streamers_arr = array();
                    // retrieve our table contents
                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        // extract row
                        extract($row);
                        // Build the array
                        $stats_streamers_item = array(
                            "timestamp" => $timestamp,
                            "stream" => $stream,
                            "streamers" => intval($streamers),
                            "show" => $show,
                            "episode" => $episode,
                            "post_id" => intval($post_id),
                        );
                        array_push($stats_streamers_arr, $stats_streamers_item);
                    }
                    if (!empty($entity)) {
                        echo json_encode($stats_streamers_item);
                    } else {
                        echo json_encode($stats_streamers_arr);
                    }
                } else {
                    http_response_code(404); // not found
                    throw new Exception("Streaming Stats not found");
                }
            }
            if ($resource == "campaigns") {
                http_response_code(400); // bad request
                throw new Exception("GET campaigns feature coming soon!");
                // 302 to /campaigns?
            }
            if ($resource == "calls") {
                http_response_code(400); // bad request
                throw new Exception("GET calls feature coming soon!");
            }
        }

        // Generic Stat Resource
        if ((!isset($_GET["s"])) && (!isset($resource)) && (!in_array($resource, $resources))) {
            // Initialize array
            $stats_arr = array();

            // Campaigns //

            // Filter based on date time
            if ((isset($_GET["dt"]))) {
                function validateDateTime($date, $format = 'Y-m-d H:i:s') // Function to see if dt is a valid DateTime

                {
                    $d = DateTime::createFromFormat($format, $date);
                    // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
                    return $d && $d->format($format) === $date;
                }

                if (validateDateTime($_GET["dt"])) { // call validateDate function
                    $datetime = date($_GET["dt"]);
                    $date = date('Y-m-d', strtotime($datetime));
                    $stmt = $campaign->readCampaignsByDateTime($datetime); //readLatest
                } else {
                    http_response_code(400); // Bad Request
                    echo json_encode(array("message" => "Please provide a date time in the Y-m-d H:i:s format."));
                    die;
                }
            }
            // Filter based on date
            if ((isset($_GET["d"]))) {

                function validateDate($date, $format = 'Y-m-d') // Function to see if d is a valid DateTime

                {
                    $d = DateTime::createFromFormat($format, $date);
                    // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
                    return $d && $d->format($format) === $date;
                }

                if (validateDate($_GET["d"])) { // call validateDate function
                    $date = date($_GET["d"]);
                    $stmt = $campaign->readCampaignsByDate($date);
                } else {
                    http_response_code(400); // Bad Request
                    echo json_encode(array("message" => "Please provide a date in the Y-m-d format."));
                    die;
                }
            }
            // ReadLatest if no filter set (default)
            if ((!isset($_GET["d"])) && (!isset($_GET["dt"]))) {
                $datetime = date('Y-m-d H:i:s'); // NOW!
                $stmt = $campaign->readCampaignsByDateTime($datetime);
            }

            // count campaign rows
            $num = $stmt->rowCount();

            // CAMPAIGNS //
            // check if more than 0 record found
            if ($num > 0) {
                $campaign_arr = array();
                $stats_arr["campaigns"] = array();
                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);

                    // percent of campaign (pledged)
                    if ($pledged > 0) {
                        $percent = max(number_format($pledged / $goal * 100, 2, '.', ''), 0); // change 2 to # of decimals max. 0 so no neg numbers and no comma
                    } else {
                        $percent = 0;
                    }

                    // how much left togo
                    $togo = max($goal - $pledged, 0);

                    // how much is left / the number of days left = daily goal
                    $datediff = time() - strtotime($end);
                    $diff = round($datediff / (60 * 60 * 24), 2); // round means at 12noon big jump , no round means every few seconds an increase
                    $day_goal = round($togo / -$diff);

                    // day togo
                    $day_togo = max($day_goal - $day_total, 0);

                    if ($day_goal > $togo) {
                        $day_goal = $togo + $day_total;
                        $day_togo = $togo;
                    } // fix for bad math above

                    // day_percent
                    if ($day_goal > 0) {
                        $day_percent = max(number_format($day_total / $day_goal * 100, 2, '.', ''), 0); // change 2 to # of decimals max. 0 so no neg numbers and no comma
                    } else {
                        $day_percent = 0;
                    }

                    // null for hour
                    if (!isset($hour_pledged)) {
                        $hour_pledged = 0;
                    }
                    if (!isset($hour_count)) {
                        $hour_count = 0;
                    }

                    // Build the Campaign array
                    $campaign_item = array(
                        "id" => intval($id),
                        "type" => $type,
                        "name" => $name,
                        "start" => $start,
                        "end" => $end,
                        "goal" => floatval($goal),
                        "pledged" => floatval($pledged),
                        "sustainer_total" => floatval($sustainer_total),
                        "sustainer_count" => floatval($sustainer_count),
                        "paid" => floatval($paid),
                        "percent" => floatval($percent),
                        "pledges" => floatval($count), // number of pledges
                        "day_goal" => floatval(round($day_goal, 2)), // day goal
                        "day_total" => floatval($day_total), // day pledged
                        "day_togo" => floatval(round($day_togo, 2)), // day left to raise
                        "day_percent" => floatval($day_percent), // percent of day (pledged)
                        "hour_pledged" => floatval($hour_pledged), // hour pledged
                        "hour_count" => intval($hour_count), // number of donations in the current hour
                        "togo" => floatval(round($togo, 2)),
                    );
                    // Push the campaign item into the campaign array
                    array_push($campaign_arr, $campaign_item);
                    array_push($stats_arr["campaigns"], $campaign_item);
                } // while

                //echo json_encode($campaign_arr);
            } else {
                //http_response_code(404); // not found
                $stats_arr["campaigns"] = null; //no campaign
            }

            // STREAMERS //
            // query streaming stats
            $stmt4 = $stat->readStreamer();

            // count streamer rows
            $num = $stmt4->rowCount();

            // check if more than 0 record found
            if ($num > 0) {

                // Streamers array
                $stats_arr["streams"] = array();

                // retrieve our table contents
                while ($row = $stmt4->fetch(PDO::FETCH_ASSOC)) {
                    extract($row);

                    $streams_item = array(
                        "stream" => $stream,
                        "timestamp" => $timestamp,
                        "post_id" => intval($post_id),
                        "show" => html_entity_decode(htmlspecialchars_decode($show, ENT_QUOTES)),
                        "episode" => html_entity_decode(htmlspecialchars_decode($episode, ENT_QUOTES)),
                        "episode_url" => "https://kpfa.org/?post_type=episode&p=$post_id",
                        "streamers" => intval($streamers),
                    );

                    array_push($stats_arr["streams"], $streams_item);
                } // end while for $stmt

            } else {
                // db empty pull from icecast srv
                $stats_arr["streams"] = array();
                $streamers = 0;
                // json
                $json = file_get_contents('https://streams.kpfa.org:8443/status-json.xsl');
                $data = json_decode($json, true);

                $source_arr = $data["icestats"]["source"];

                //die(print_r($source_arr));
                foreach ($source_arr as $key => $value) {
                    $streamers += $value["listeners"];
                }
                // Query wp-api for episode info:
                $wp_api_data = json_decode(file_get_contents("https://kpfa.org/wp-json/wp/v2/episode?per_page=1"));

                if (isset($wp_api_data[0]->program_name)) {
                    $stat->show = $wp_api_data[0]->program_name;
                }
                if (isset($wp_api_data[0]->title->rendered)) {
                    $stat->episode = $wp_api_data[0]->title->rendered;
                }
                if (isset($wp_api_data[0]->id)) {
                    $stat->post_id = $wp_api_data[0]->id;
                }

                $streams_item = array(
                    "stream" => "KPFA",
                    "show" => $stat->show,
                    "episode" => $stat->episode,
                    "episode_url" => "https://kpfa.org/?post_type=episode&p=$stat->post_id",
                    "streamers" => intval($streamers),
                );

                array_push($stats_arr["streams"], $streams_item);
            }

            // CALLS //
            // set time out
            $ctx = stream_context_create(array(
                'http' => array(
                    'method' => "GET",
                    'timeout' => 2, //2 Seconds timeout
                    'follow_location' => false,
                ),
                // ipv4 only
                'socket' => array(
                    'bindto' => '0:0',
                ),
            ));

            if (($content = @file_get_contents("https://pbx.kpfa.net/call-status/json.php", false, $ctx)) === false) {
                $e = error_get_last();
                //   throw new Exception($e['message']); // don't fail on error till we fix
            }

            // check to see if we got a response
            if (!empty($content)) {

                // Callers array initialize
                $stats_arr["calls"] = array();
                $callers_arr = json_decode($content, JSON_OBJECT_AS_ARRAY);

                if (!empty($callers_arr)) { // is there something there?
                    // remove  station number and reorder array
                    $callers_arr["numbers"] = array_merge(array_diff($callers_arr["numbers"], array("5108486767")));
                    // set stats_arr callers to callers array
                    $stats_arr["calls"] = $callers_arr;
                } else {
                    $stats_arr["calls"] = null;
                }
            } else {
                $stats_arr["calls"] = null;
            }

            // echo json stats
            echo json_encode($stats_arr);
        }
    }
} catch (Exception $e) {
    // Display any messages
    echo json_encode(array("message" => $e->getMessage()));
    die;
}
