<?php

/**
 * Stripe Webhook Handler: charge.succeeded
 *
 * Processes successful charge events for one-time payments.
 * Subscription payments should be handled by invoice.paid webhook.
 *
 * @param object $event Stripe webhook event object
 * @param PDO $db Database connection
 */

require_once __DIR__ . "/../../utils/handle_ach_status.php";

try {
    // Log webhook processing start
    error_log("[charge.succeeded] Processing charge: " . ($event->data->object->id ?? 'unknown'));

    // Validate required event data
    if (empty($event->data->object)) {
        throw new Exception("Invalid webhook event: missing event data object");
    }

    $charge = $event->data->object;
    $charge_id = $charge->id ?? null;

    if (empty($charge_id)) {
        throw new Exception("Invalid webhook event: missing charge ID");
    }

    // Handle ACH status updates first
    if (handle_ach_status($event, $db)) {
        error_log("[charge.succeeded] ACH status handled for charge: $charge_id");
        exit();
    }

    // Check if payment already exists in our system
    $existingPayment = (new Payment($db))->readStripePayment($charge_id);
    if ($existingPayment->rowCount() > 0) {
        $paymentData = $existingPayment->fetch(PDO::FETCH_ASSOC);
        error_log("[charge.succeeded] Payment already exists for charge: $charge_id");

        http_response_code(200);
        echo json_encode($paymentData, JSON_NUMERIC_CHECK);
        exit();
    }

} catch (Exception $e) {
    error_log("[charge.succeeded] Error in initial processing: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "error" => "Webhook processing failed",
        "message" => $e->getMessage()
    ]);
    exit();
}

// Extract and validate charge data
try {
    $customer_id = $charge->customer ?? null;
    $transaction_id = $charge->metadata->transaction_id ?? null;
    $donor_id = $charge->metadata->donor_id ?? null;

    // Validate required metadata
    if (empty($transaction_id)) {
        error_log("[charge.succeeded] Missing transaction_id for charge: $charge_id");
        http_response_code(200);
        echo json_encode([
            "message" => "No transaction_id found - likely subscription payment, waiting for invoice.paid",
            "charge_id" => $charge_id
        ]);
        exit();
    }

    if (empty($customer_id)) {
        throw new Exception("Missing customer_id for charge: $charge_id");
    }

    // Lookup donation record
    $donationResult = (new Donation($db))->readDonationByTransactionID($transaction_id);
    if ($donationResult->rowCount() === 0) {
        throw new Exception("Donation not found for transaction_id: $transaction_id");
    }

    $donation = $donationResult->fetch(PDO::FETCH_ASSOC);
    error_log("[charge.succeeded] Found donation ID: " . $donation['id'] . " for transaction: $transaction_id");

    // Extract payment details with safe property access
    $payment_data = [
        'payment_id' => $charge_id,
        'customer_id' => $customer_id,
        'donor_id' => $donor_id,
        'amount' => ($charge->amount ?? 0) / 100,
        'amount_refunded' => ($charge->amount_refunded ?? 0) / 100,
        'processor' => 'Stripe',
        'date_created' => $charge->created ?? time(),
        'status' => $charge->status ?? 'unknown'
    ];

    // Extract payment method details safely
    $payment_method_details = $charge->payment_method_details ?? null;
    if ($payment_method_details) {
        $method_type = $payment_method_details->type ?? 'unknown';
        $payment_data['method'] = $method_type;

        // Handle different payment method types
        switch ($method_type) {
            case 'card':
                $card_details = $payment_method_details->card ?? null;
                if ($card_details) {
                    $payment_data['fingerprint'] = $card_details->fingerprint ?? null;
                    $payment_data['card_type'] = $card_details->funding ?? null;
                    $payment_data['last4'] = $card_details->last4 ?? null;
                    $payment_data['brand'] = $card_details->brand ?? null;
                    $payment_data['exp_month'] = $card_details->exp_month ?? null;
                    $payment_data['exp_year'] = $card_details->exp_year ?? null;
                }
                break;

            case 'us_bank_account':
                $bank_details = $payment_method_details->us_bank_account ?? null;
                if ($bank_details) {
                    $payment_data['last4'] = $bank_details->last4 ?? null;
                    $payment_data['fingerprint'] = null;
                    $payment_data['card_type'] = null;
                    $payment_data['brand'] = null;
                    $payment_data['exp_month'] = null;
                    $payment_data['exp_year'] = null;
                }
                break;

            case 'link':
                // Stripe Link payments
                $payment_data['fingerprint'] = null;
                $payment_data['card_type'] = null;
                $payment_data['last4'] = null;
                $payment_data['brand'] = null;
                $payment_data['exp_month'] = null;
                $payment_data['exp_year'] = null;
                break;

            default:
                // Other payment methods
                $payment_data['fingerprint'] = null;
                $payment_data['card_type'] = null;
                $payment_data['last4'] = null;
                $payment_data['brand'] = null;
                $payment_data['exp_month'] = null;
                $payment_data['exp_year'] = null;
        }
    } else {
        // No payment method details available
        $payment_data['method'] = 'unknown';
        $payment_data['fingerprint'] = null;
        $payment_data['card_type'] = null;
        $payment_data['last4'] = null;
        $payment_data['brand'] = null;
        $payment_data['exp_month'] = null;
        $payment_data['exp_year'] = null;
    }

} catch (Exception $e) {
    error_log("[charge.succeeded] Error extracting payment data: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "error" => "Failed to process payment data",
        "message" => $e->getMessage(),
        "charge_id" => $charge_id ?? 'unknown'
    ]);
    exit();
}
// Insert payment record into database
try {
    $db_data = [
        "donation_id" => $donation["id"],
        "customer_id" => $payment_data['customer_id'],
        "payment_id" => $payment_data['payment_id'],
        "amount" => $payment_data['amount'],
        "amount_refunded" => $payment_data['amount_refunded'],
        "method" => $payment_data['method'],
        "processor" => $payment_data['processor'],
        "fingerprint" => $payment_data['fingerprint'],
        "card_type" => $payment_data['card_type'],
        "date_created" => $payment_data['date_created'],
        "last4" => $payment_data['last4'],
        "brand" => $payment_data['brand'],
        "exp_month" => $payment_data['exp_month'],
        "exp_year" => $payment_data['exp_year'],
        "status" => $payment_data['status'],
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO `payments`
                            (donation_id, customer_id, payment_id, amount, amount_refunded, method, processor, fingerprint, card_type, date_created, last4, brand, exp_month, exp_year, `status`)
                            VALUES
                            (:donation_id, :customer_id, :payment_id, :amount, :amount_refunded, :method, :processor, :fingerprint, :card_type, FROM_UNIXTIME(:date_created), LPAD(:last4,4,'0'), :brand, LPAD(:exp_month,2,'0'), :exp_year, :status)");

    $stmt->execute($db_data);
    $last_id = $db->lastInsertId();

    if ($last_id > 0) {
        error_log("[charge.succeeded] Payment record created with ID: $last_id for charge: $charge_id");

        // Read the created payment record
        $createdPayment = (new Payment($db))
            ->readStripePayment($charge_id)
            ->fetch(PDO::FETCH_ASSOC);

        // Send thank you email if donation has email
        if (!empty($donation["email"])) {
            try {
                (new Email($db))->sendMessage("thankyou", $transaction_id);
                error_log("[charge.succeeded] Thank you email sent for transaction: $transaction_id");
            } catch (Exception $e) {
                error_log("[charge.succeeded] Failed to send thank you email for transaction $transaction_id: " . $e->getMessage());
                // Don't fail the webhook for email issues
            }
        }

        // Return success response
        http_response_code(200);
        echo json_encode($createdPayment, JSON_NUMERIC_CHECK);

    } else {
        // INSERT IGNORE may have prevented insertion due to duplicate
        // Check if payment already exists (race condition)
        $existingPayment = (new Payment($db))->readStripePayment($charge_id);
        if ($existingPayment->rowCount() > 0) {
            error_log("[charge.succeeded] Payment already exists (race condition) for charge: $charge_id");
            http_response_code(200);
            echo json_encode($existingPayment->fetch(PDO::FETCH_ASSOC), JSON_NUMERIC_CHECK);
        } else {
            throw new Exception("Failed to insert payment record for charge: $charge_id");
        }
    }

} catch (Exception $e) {
    error_log("[charge.succeeded] Database error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "error" => "Database operation failed",
        "message" => $e->getMessage(),
        "charge_id" => $charge_id,
        "transaction_id" => $transaction_id ?? 'unknown'
    ]);
}
