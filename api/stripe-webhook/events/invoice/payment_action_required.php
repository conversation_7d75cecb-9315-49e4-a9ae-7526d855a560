<?php
require_once __DIR__ . "/../../utils/create_pending_payment.php";
global $stripe;

try {
    $payment = createPendingPayment($db, $stripe, $event, "invoice");

    http_response_code(200);
    echo json_encode($payment, JSON_NUMERIC_CHECK);
} catch (Exception $e) {
    error_log("[invoice.payment_action_required] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(["error" => $e->getMessage()]);
}
