<?php
require_once __DIR__ . "/../../utils/handle_premium_adjustments.php";

try {
    // Retrieve the event object
    $object = $event->data->object;

    // Check if the payment is for the first subscription payment
    $billing_reason = $object->billing_reason ?? null;
    if ($billing_reason !== "subscription_create") {
        http_response_code(200);
        exit();
    }

    // Retrieve transaction_id from metadata
    $transaction_id = $object->lines->data[0]->metadata->transaction_id ?? null;
    if (!$transaction_id) {
        throw new Exception("Transaction ID not found in metadata.");
    }

    // Fetch premium IDs using the new method
    $premium_ids = (new Donation($db))->getPremiumIDsByTransactionID(
        $transaction_id,
    );

    if (empty($premium_ids)) {
        http_response_code(200); // success
        exit();
    }

    // We need this to run for first payments that did not adjust premium quantity
    // via creating pending payments in the invoice.payment_action_required event handler

    if (!$object->lines->data[0]->metadata->premiums_processed) {
        handlePremiumAdjustmentsFromStripe($db, $premium_ids);
    }

    // Success response
    http_response_code(200); // success
} catch (Exception $e) {
    error_log("[invoice.payment_succeeded] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(["error" => $e->getMessage()]);
}
