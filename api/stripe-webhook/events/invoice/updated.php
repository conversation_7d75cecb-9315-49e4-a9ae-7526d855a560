<?php

try {
    // Retrieve the invoice object
    $invoice = $event->data->object;
    $invoice_id = $invoice->id;
    $invoice_status = $invoice->status;

    // This event is informational only
    // Subscription activation is handled by customer.subscription.updated webhook
    // Invoice payment processing is handled by invoice.paid webhook
    
    // Always return success - this event is informational
    http_response_code(200);
    echo json_encode([
        "message" => "Invoice updated event processed successfully",
        "invoice_id" => $invoice_id,
        "status" => $invoice_status
    ]);
    
} catch (Exception $e) {
    error_log("[invoice.updated] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(["error" => $e->getMessage()]);
}
