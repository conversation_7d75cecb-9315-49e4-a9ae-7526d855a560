<?php
require_once __DIR__ . "/../../utils/handle_premium_adjustments.php";

try {
    // Check if the payment is related to a subscription via the `invoice` field
    $invoice_id = $event->data->object->invoice ?? null;

    if ($invoice_id) {
        http_response_code(200);
        echo json_encode([
            "message" => "Skipped subscription-related payment.",
        ]);
        exit();
    }

    // Check if the payment already exists
    $payment_id = $event->data->object->charges->data[0]->id;
    if ((new Payment($db))->readStripePayment($payment_id)->rowCount() > 0) {
        echo json_encode(
            (new Payment($db))
                ->readStripePayment($payment_id)
                ->fetch(PDO::FETCH_ASSOC),
            JSON_NUMERIC_CHECK,
        );
        exit();
    }

    $customer_id = $event->data->object->charges->data[0]->customer; // Customer ID

    // Retrieve transaction_id from metadata
    $transaction_id =
        $event->data->object->charges->data[0]->metadata->transaction_id ??
        null;

    // Handle missing transaction_id
    if (!$transaction_id) {
        error_log(
            "[payment_intent.succeeded] Transaction ID missing in metadata for Payment ID: {$payment_id}.",
        );
        throw new Exception("Transaction ID not found in metadata.");
    }

    $amount = $event->data->object->charges->data[0]->amount / 100; // Amount in dollars

    // Retrieve Premium metadata
    $premium_metadata =
        $event->data->object->charges->data[0]->metadata->Premium ?? "[]";

    // Decode Premium metadata (assumes JSON string)
    $premium_ids = json_decode($premium_metadata, true);

    // Ensure `premium_ids` is an array
    if (!is_array($premium_ids)) {
        error_log(
            "[payment_intent.succeeded] Invalid Premium metadata. Defaulting to an empty array. Metadata: {$premium_metadata}.",
        );
        $premium_ids = []; // Default to an empty array if decoding failed
    }

    // Retrieve the associated donation
    $donation = (new Donation($db))
        ->readDonationByTransactionID($transaction_id)
        ->fetch(PDO::FETCH_ASSOC);
    if (!$donation) {
        error_log(
            "[payment_intent.succeeded] Donation not found for Transaction ID: {$transaction_id}.",
        );
        throw new Exception(
            "Donation not found for transaction ID: {$transaction_id}.",
        );
    }

    // Delegate premium adjustments to the unified function
    if (!$event->data->object->metadata->premiums_processed) {
        handlePremiumAdjustmentsFromStripe($db, $premium_ids);
    }

    // Success response
    http_response_code(200);
    echo json_encode(
        ["message" => "Premiums processed successfully"],
        JSON_NUMERIC_CHECK,
    );
} catch (Exception $e) {
    error_log("[payment_intent.succeeded] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(["error" => $e->getMessage()]);
}
