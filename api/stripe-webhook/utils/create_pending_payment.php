<?php

require_once __DIR__ . '/handle_premium_adjustments.php';

/**
 * Creates a pending payment record from Stripe webhook events
 *
 * @param PDO $db Database connection
 * @param \Stripe\StripeClient $stripe Pre-configured Stripe client
 * @param object $event Stripe webhook event object
 * @param string $event_type Event type ('payment_intent' or 'invoice')
 * @return array Created payment record
 * @throws Exception If operations fail
 */
function createPendingPayment(
    PDO $db,
    \Stripe\StripeClient $stripe,
    object $event,
    string $event_type,
): array {
    // Extract transaction_id from metadata
    $transaction_id = match ($event_type) {
        "payment_intent" => $event->data->object->metadata->transaction_id ?? null,
        "invoice" => $event->data->object->subscription_details->metadata->transaction_id ?? null,
        default => throw new Exception("Invalid event type: $event_type"),
    };

    if (!$transaction_id) {
        throw new Exception("Transaction ID missing in metadata");
    }

    // Retrieve Premium metadata from the appropriate source
    if ($event_type === "payment_intent") {
        $premium_metadata = $event->data->object->metadata->Premium ?? "[]";
    } else {
        $premium_metadata = $event->data->object->subscription_details->metadata->Premium ?? "[]";
    }

    // Decode and validate premium IDs
    $premium_ids = json_decode($premium_metadata, true);
    if (!is_array($premium_ids)) {
        error_log("[createPendingPayment] Invalid Premium metadata. Defaulting to empty array. Metadata: {$premium_metadata}");
        $premium_ids = [];
    }

    // Retrieve associated donation
    $donation = (new Donation($db))
        ->readDonationByTransactionID($transaction_id)
        ->fetch(PDO::FETCH_ASSOC);

    if (!$donation) {
        throw new Exception("Donation not found for transaction: $transaction_id");
    }

    // Common payment data
    $payment_data = [
        "donation_id" => $donation["id"],
        "customer_id" => $event->data->object->customer,
        "amount_refunded" => 0,
        "processor" => "Stripe",
        "method" => "us_bank_account",
        "status" => "pending",
        "fingerprint" => null,
        "card_type" => null,
        "last4" => null,
        "brand" => null,
        "exp_month" => null,
        "exp_year" => null,
    ];

    // Event-specific data extraction
    if ($event_type === "payment_intent") {
        $payment_data += [
            "payment_id" => $event->data->object->id,
            "amount" => $event->data->object->amount / 100,
            "date_created" => $event->data->object->created,
        ];
    } else {
        $payment_data += [
            "payment_id" => $event->data->object->payment_intent,
            "amount" => $event->data->object->amount_due / 100,
            "date_created" => $event->data->object->created,
        ];
    }

    // Insert payment record
    $stmt = $db->prepare("INSERT IGNORE INTO `payments`
        (donation_id, customer_id, payment_id, amount, amount_refunded, method,
         processor, fingerprint, card_type, date_created, last4, brand,
         exp_month, exp_year, `status`)
        VALUES
        (:donation_id, :customer_id, :payment_id, :amount, :amount_refunded,
         :method, :processor, :fingerprint, :card_type, FROM_UNIXTIME(:date_created),
         :last4, :brand, :exp_month, :exp_year, :status)");

    $stmt->execute($payment_data);

    if ($db->lastInsertId() === "0") {
        throw new Exception("Payment insert failed for customer {$payment_data["customer_id"]}");
    }

    // Handle premium adjustments if needed
    if (!empty($premium_ids)) {
        handlePremiumAdjustmentsFromStripe($db, $premium_ids);

        // Update Stripe metadata only for premium-containing payments
        try {
            $metadata = ["premiums_processed" => "true"];

            if ($event_type === "payment_intent") {
                $stripe->paymentIntents->update($payment_data["payment_id"], [
                    "metadata" => $metadata
                ]);
            } else {
                $subscription_id = $event->data->object->subscription;
                $stripe->subscriptions->update($subscription_id, [
                    "metadata" => array_merge(
                        $event->data->object->subscription_details->metadata->toArray(),
                        $metadata
                    )
                ]);
            }
        } catch (\Stripe\Exception\ApiErrorException $e) {
            error_log("Stripe metadata update error: " . $e->getMessage());
            throw new Exception("Failed to update premium tracking in Stripe");
        }
    }

    return (new Payment($db))
        ->readStripePayment($payment_data["payment_id"])
        ->fetch(PDO::FETCH_ASSOC);
}
