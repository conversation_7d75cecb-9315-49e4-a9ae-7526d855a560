<?php

function handle_ach_status($event, $db) {
    // Bail if not ACH
    if (
        $event->data->object->payment_method_details->type !== "us_bank_account"
    ) {
        return false;
    }

    try {
        $payment_intent = $event->data->object->payment_intent;
        $new_charge_id = $event->data->object->id;

        // Check for existing payment
        $payment = (new Payment($db))->readStripePayment($payment_intent);
        if ($payment->rowCount() === 0) {
            error_log("No existing ACH payment found for PI: $payment_intent");
            return false;
        }

        $existing = $payment->fetch(PDO::FETCH_ASSOC);

        // Only update pending payments
        if ($existing["status"] === "pending") {
            (new Payment($db))->update_ach_payment(
                $existing["id"],
                $event->data->object->status,
                $new_charge_id,
            );

            // Return updated record
            echo json_encode(
                (new Payment($db))
                    ->readStripePayment($new_charge_id)
                    ->fetch(PDO::FETCH_ASSOC),
                JSON_NUMERIC_CHECK,
            );
            exit();
        }

        // Already succeeded - return existing
        http_response_code(200);
        echo json_encode($existing, JSON_NUMERIC_CHECK);
        exit();
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            "error" => "Database error",
            "details" => $e->getMessage(),
        ]);
        exit();
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            "error" => "ACH processing failed",
            "details" => $e->getMessage(),
        ]);
        exit();
    }
}
