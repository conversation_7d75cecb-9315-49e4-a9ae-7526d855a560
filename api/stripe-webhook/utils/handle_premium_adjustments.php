<?php
function handlePremiumAdjustmentsFromStripe($db, $premium_ids) {
    try {
        // Skip if no premiums are provided
        if (empty($premium_ids) || !is_array($premium_ids)) {
            return;
        }

        foreach ($premium_ids as $premium_id) {
            // Decrement qty for each premium ID (including repeated IDs)
            $update_query = "UPDATE premiums
                             SET qty = qty - 1
                             WHERE id = :premium_id AND qty > 0";
            $stmt = $db->prepare($update_query);
            $stmt->bindParam(":premium_id", $premium_id, PDO::PARAM_INT);

            if (!$stmt->execute()) {
                error_log(
                    "[handlePremiumAdjustmentsFromStripe] Failed to decrement qty for Premium ID: {$premium_id}. Error: " .
                        json_encode($stmt->errorInfo()),
                );
                throw new Exception(
                    "Failed to decrement qty for Premium ID: {$premium_id}.",
                );
            }
        }
    } catch (Exception $e) {
        error_log(
            "[handlePremiumAdjustmentsFromStripe] Error occurred: " .
                $e->getMessage(),
        );
        throw $e; // Re-throw to handle in the caller
    }
}
