<?php
// CREATE a new subscription
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    // check to see if resource contains sub resource
    if (strpos($resource, '/') !== false) {
        // explodable
        $piece = explode("/", $resource);
        $resource = $piece[0]; // reassign resource
        $entity = $piece[1]; // entity
    } else {
        // not explodable
    }

    // set campaign_id to be deleted
    if (isset($resource)) { // is ID in URL?
        $donor_id = $resource;
    }
    if (isset($data->id)) { // is the id in the body ($data)
        $donor_id = $data->id;
    }
    // Require donor_id
    if (empty($donor_id)) {
        throw new Exception("[donor_id] is required");
    }

    // Require amount
    if (!empty($data->amount)) {
        // set amount to be created
        $amount = $data->amount;
    } else {
        throw new Exception("[amount] is required.");
    }
    // Require transaction_id
    if (!empty($data->transaction_id)) {
        // set transaction_id to be created
        $transaction_id = $data->transaction_id;
    } else {
        throw new Exception("[transaction_id] is required.");
    }
    // Require customer_id
    if (!empty($data->customer_id)) {
        // set customer_id to be created
        $customer_id = $data->customer_id;
    } else {
        throw new Exception("[customer_id] is required.");
    }
    // Create Subscription
    $subscription_obj = $subscription->create($donor_id, $transaction_id, $customer_id, $amount);
    // Fetch and display results
    if (!is_null($subscription_obj)) {
        echo json_encode($subscription_obj), JSON_NUMERIC_CHECK;
    }
} catch (Exception $e) {
    http_response_code(400); // bad request
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
}
