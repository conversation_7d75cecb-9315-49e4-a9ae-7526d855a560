<?php
// SET $subscription_id (parsed from from url)
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    // check to see if resource contains sub resource
    if (strpos($resource, '/') !== false) {
        // explodable
        $piece = explode("/", $resource);
        $resource = $piece[0]; // reassign resource
        $entity = $piece[1]; // entity
    } else {
        // not explodable
    }

    // set campaign_id to be deleted
    if (isset($resource)) { // is subscription_id in URL?
        $subscription_id = $resource;
    }
    if (isset($data->subscription_id)) { // is the subscription_id in the body ($data)
        $subscription_id = $data->subscription_id;
    }
    // Require id
    if (empty($subscription_id)) {
        http_response_code(400); // bad request
        throw new Exception("[subscription_id] is required");
    }

    // DELETE (Cancel) SUBSCRIPTION
    if ($subscription->cancel($subscription_id)) {
        echo json_encode(array(
            "status" => "success",
            "message" => "Subscription $subscription_id was successfully cancelled",
        ));
    }
} catch (Exception $e) {
    echo json_encode(array("message" => $e->getMessage()));
}
