<?php
// READ subscription
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    // check to see if resource contains sub resource
    if (strpos($resource, '/') !== false) {
        // explodable
        $piece = explode("/", $resource);
        $resource = $piece[0]; // reassign resource
        $entity = $piece[1]; // entity
    } else {
        // not explodable
    }

    // set campaign_id to be deleted
    if (isset($resource)) { // is subscription_id in URL?
        $subscription_id = $resource;
    }
    if (isset($data->subscription_id)) { // is the subscription_id in the body ($data)
        $subscription_id = $data->subscription_id;
    }
    // Require id
    if (empty($subscription_id)) {
        http_response_code(400); // bad request
        throw new Exception("[subscription_id] is required");
    }

    // READ subscription
    $stmt = $subscription->read($subscription_id);
    $num = $stmt->rowCount();

    // check if more than 0 record found
    if ($num > 0) {
        // instantiate the subscription array
        $subscriptions_arr = array();
        // retrieve our table contents
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            extract($row);
            // create array
            $subscription_item = array(
                "id" => $id,
                "customer_id" => $customer_id,
                "donor_id" => intval($donor_id),
                "processor" => $processor,
                "date_created" => $date_created,
                "date_canceled" => $date_canceled,
                "plan_id" => $plan_id,
                "transaction_id" => $transaction_id,
                "amount" => floatval($amount),
                "interval" => $interval,
                "active" => boolval($active),
            );
            array_push($subscriptions_arr, $subscription_item); // add subscription item to subscriptions array
        } // end while for $stmt

        echo json_encode($subscriptions_arr);
    } else {
        http_response_code(404); // not found
        throw new Exception("No subscriptions found");
    }
} catch (Exception $e) {
    echo json_encode(array("message" => $e->getMessage()));
}
