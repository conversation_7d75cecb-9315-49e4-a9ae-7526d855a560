<?php
// Set subscription property values
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    // source [not required]
    if (!empty($data->source)) {
        $subscription->source = $data->source;
    }
    // [Required Section]
    // method
    if (!empty($data->method)) {
        $subscription->method = $data->method;
    } else {
        throw new Exception("[method] is required.");
    }
    // id (subscription id)
    if (!empty($resource)) {
        $subscription->id = $resource;
    } else if (!empty($data->id)) {
        $subscription->id = $data->id;
    } else {
        throw new Exception("[id] is required.");
    }
    // amount
    if (!empty($data->amount)) {
        $subscription->amount = $data->amount;
    } else {
        throw new Exception("[amount] is required.");
    }
    // status
    $status_arr = array("succeeded", "pending", "failed");
    if (!empty($data->status) && (in_array($data->status, $status_arr))) { // status [required]
        $subscription->status = $data->status;
    } else {
        throw new Exception("[status] is required and must be one of the following: succeeded, pending, failed");
    }
    // card
    if ($data->method == "card") {
        // action [required]
        if (!empty($data->action)) {
            $subscription->action = $data->action;
        } else {
            throw new Exception("[action] is required.");
        }

        $reason_arr = array("duplicate", "fraudulent", "requested_by_customer");
        if (!empty($data->reason) && (in_array($data->reason, $reason_arr))) { // reason [required]
            $subscription->reason = $data->reason;
        } else {
            throw new Exception("[reason] is required and must be one of the following: duplicate, fraudulent, requested_by_customer");
        }
        if ($data->action == "refund") {

            // UPDATE (REFUND) CHARGE
            if ($subscription->refund_stripe_subscription()) { // (call stripe subscription function) //TODO doesn't exist
                echo json_encode(array(
                    "status" => "success",
                    "message" => "subscription $subscription->id was successfully refunded",
                ));
            } else { // Unable to refund the subscription, tell the user.
                echo json_encode(array(
                    "status" => "error",
                    "message" => $e->getMessage(),
                ));
            }
        } else {
            throw new Exception("[action] must be of type: 'refund'");
        }
    }
} catch (Exception $e) {
    http_response_code(400); // Bad Request
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}

// Send error if incoming JSON invalid
