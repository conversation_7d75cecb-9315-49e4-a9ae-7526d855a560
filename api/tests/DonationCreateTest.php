<?php

use PHPUnit\Framework\TestCase;
use Kpfa\Service\DonationService;
use Kpfa\Tests\TestHelpers;
use Kpfa\Tests\TestPDO;

// Include all required classes
require_once __DIR__ . "/../objects/donation.php";
require_once __DIR__ . "/../objects/account.php";  // Contains Donor class
require_once __DIR__ . "/../objects/campaign.php";
require_once __DIR__ . "/../objects/premium.php";
require_once __DIR__ . "/../objects/shipping.php";
require_once __DIR__ . "/../objects/payment.php";
require_once __DIR__ . "/../objects/email.php";
require_once __DIR__ . "/TestHelpers.php";
require_once __DIR__ . "/TestPDO.php";

class DonationCreateTest extends TestCase {
    use TestHelpers;
    
    private $conn;
    private $campaign_id;
    private $donor_id;
    private $emailMock;
    private static $enableLogging = false;
    
    protected function setUp(): void {
        // Set up test environment
        $this->setupTestEnvironment();

        // Create a test database connection using our custom PDO class
        $this->conn = new TestPDO("sqlite::memory:");
        $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Only set up logging if enabled
        if (self::$enableLogging) {
            $this->conn->setAttribute(PDO::ATTR_STATEMENT_CLASS, ['LoggingPDOStatement', [$this->conn]]);
            $this->conn->query('SELECT 1')->setTestCase($this);
        }

        // Create test tables
        $this->createTestTables();

        // Create a test campaign
        $this->createTestCampaign();

        // Set up email mock with proper expectations
        $this->emailMock = $this->createMock(Email::class);
        
        // Mock all email methods to prevent actual sending
        $this->emailMock->expects($this->any())
            ->method('send')
            ->willReturnCallback(function($email, $name, $html, $subject) {
                return true;
            });
            
        $this->emailMock->expects($this->any())
            ->method('sendThankYou')
            ->willReturnCallback(function($id, $timestamp, $name, $premiums, $pledge_amount, $installment, $transaction_id) {
                return true;
            });
            
        $this->emailMock->expects($this->any())
            ->method('sendBill')
            ->willReturnCallback(function($id, $timestamp, $name, $premiums, $pledge_amount, $installment, $transaction_id) {
                return true;
            });
            
        $this->emailMock->expects($this->any())
            ->method('sendReminder')
            ->willReturnCallback(function($id, $timestamp, $name, $premiums, $pledge_amount, $installment, $transaction_id) {
                return true;
            });
            
        $this->emailMock->expects($this->any())
            ->method('sendShippingUpdate')
            ->willReturnCallback(function($id, $timestamp, $name, $premiums, $pledge_amount, $installment, $transaction_id, $shipping_status) {
                return true;
            });
            
        $this->emailMock->expects($this->any())
            ->method('sendMessage')
            ->willReturnCallback(function($type, $transaction_id) {
                return true;
            });
            
        $this->emailMock->expects($this->any())
            ->method('sendTaxLetter')
            ->willReturnCallback(function($donor) {
                return true;
            });
    }

    public static function enableLogging($enable = true) {
        self::$enableLogging = $enable;
    }

    private function createTestTables() {
        // Create campaigns table
        $this->conn->exec("CREATE TABLE IF NOT EXISTS campaigns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT,
            goal REAL NOT NULL,
            start TEXT NOT NULL,
            end TEXT NOT NULL,
            active INTEGER NOT NULL DEFAULT 0
        )");

        // Create donors table
        $this->conn->exec("CREATE TABLE IF NOT EXISTS donors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            firstname TEXT,
            lastname TEXT,
            phone TEXT,
            email TEXT,
            address1 TEXT,
            address2 TEXT,
            partner_firstname TEXT,
            partner_lastname TEXT,
            city TEXT,
            state TEXT,
            country TEXT NOT NULL DEFAULT 'US',
            postal_code TEXT,
            notes TEXT,
            type TEXT NOT NULL DEFAULT 'Individual',
            membership_level TEXT,
            deceased INTEGER NOT NULL DEFAULT 0,
            donotsolicit INTEGER,
            stripe_cus_id TEXT UNIQUE,
            paypal_user_id TEXT UNIQUE,
            allegiance_id INTEGER,
            memsys_id INTEGER UNIQUE,
            date_created TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            date_updated TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            paperless INTEGER
        )");

        // Create donations table
        $this->conn->exec("CREATE TABLE IF NOT EXISTS donations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            account_id INTEGER,
            donor_id INTEGER NOT NULL,
            transaction_id TEXT NOT NULL UNIQUE,
            payment_id INTEGER,
            type TEXT NOT NULL DEFAULT 'Pledge',
            amount REAL NOT NULL,
            installment TEXT NOT NULL,
            comments TEXT,
            add_me TEXT,
            read_onair INTEGER NOT NULL DEFAULT 0,
            ipaddress TEXT,
            browser TEXT,
            show_name TEXT,
            program_wp_id INTEGER,
            source TEXT,
            campaign_id INTEGER,
            donation_match INTEGER NOT NULL DEFAULT 0,
            premiums_cart TEXT,
            updated TEXT DEFAULT CURRENT_TIMESTAMP,
            status TEXT,
            FOREIGN KEY (donor_id) REFERENCES donors(id),
            FOREIGN KEY (campaign_id) REFERENCES campaigns(id)
        )");

        // Create payments table
        $this->conn->exec("CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donation_id INTEGER NOT NULL,
            customer_id TEXT,
            payment_id TEXT,
            amount REAL NOT NULL,
            amount_refunded REAL NOT NULL DEFAULT 0,
            method TEXT NOT NULL,
            processor TEXT,
            fingerprint TEXT,
            card_type TEXT,
            last4 INTEGER,
            brand TEXT,
            exp_month INTEGER,
            exp_year INTEGER,
            last_updated_by INTEGER,
            date_created TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            date_updated TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            date_deposited TEXT,
            status TEXT,
            FOREIGN KEY (donation_id) REFERENCES donations(id)
        )");

        // Create shipment_addresses table
        $this->conn->exec("CREATE TABLE IF NOT EXISTS shipment_addresses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donation_id INTEGER NOT NULL,
            firstname TEXT,
            lastname TEXT,
            address1 TEXT,
            address2 TEXT,
            city TEXT,
            state TEXT,
            country TEXT NOT NULL DEFAULT 'US',
            postal_code TEXT,
            phone TEXT,
            email TEXT,
            date_created TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            date_updated TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donation_id) REFERENCES donations(id)
        )");

        // Create test donor
        $stmt = $this->conn->prepare('INSERT INTO donors (
            firstname, lastname, phone, email, address1, city, state, country, 
            postal_code, type, membership_level, deceased, donotsolicit, paperless,
            date_created
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
        
        $stmt->execute([
            'Test',
            'User',
            '1231231234',
            '<EMAIL>',
            '55 Nowhere St',
            'Berkeley',
            'CA',
            'US',
            '12345',
            'Individual',
            'Basic',
            0,
            0,
            1,
            date('Y-m-d H:i:s')
        ]);

        $this->donor_id = $this->conn->lastInsertId();
    }

    private function createTestCampaign() {
        // Create a test campaign
        $stmt = $this->conn->prepare('INSERT INTO campaigns (name, start, end, goal, active) VALUES (?, ?, ?, ?, ?)');
        $stmt->execute([
            'Test Campaign',
            date('Y-m-d H:i:s', strtotime('-1 day')),
            date('Y-m-d H:i:s', strtotime('+1 day')),
            10000,
            1
        ]);

        // Get the campaign ID
        $this->campaign_id = $this->conn->lastInsertId();
    }

    public function testDonorIdHandlingLogic() {
        try {
            // Test the core logic: when donor_id is provided, it should use existing donor
            // without trying to create a new one (which would cause duplicate email error)

            // Mock the DonationService
            $donationService = $this->createMock(DonationService::class);
            $donationService->method('findShowAiring10MinutesAgo')
                ->willReturn([
                    'name' => 'Test Show',
                    'wp_id' => 1
                ]);

            // Create a donation object to test the logic
            $donation = new Donation($this->conn, $donationService);

            // Set the donor_id (simulating Station Admin passing it)
            $donation->donor_id = $this->donor_id;
            $donation->email = '<EMAIL>';  // Same email as existing donor
            $donation->source = 'StationAdmin';  // Important: not WebSite

            // Create a Donor object to test the logic
            $donor = new Donor($this->conn);

            // Test the logic: when donor_id is set, it should use existing donor
            if (isset($donation->donor_id)) {
                $donor->donor_id = $donation->donor_id;
                $donorData = $donor->read($donation->donor_id)->fetch();

                // Verify the donor exists and has the expected email
                $this->assertNotFalse($donorData, 'Donor should exist in database');
                $this->assertEquals('<EMAIL>', $donorData['email'], 'Donor should have the expected email');
                $this->assertEquals($this->donor_id, $donorData['donor_id'], 'Should retrieve the correct donor');
            } else {
                $this->fail('donor_id should be set for this test');
            }

            // Verify that we would NOT call searchDonor() when donor_id is provided
            // This is the key fix - when donor_id is provided, skip the search that could lead to duplicate creation
            $this->assertTrue(isset($donation->donor_id), 'donor_id should be set, preventing searchDonor() call');

        } catch (Exception $e) {
            $this->fail('Donor ID handling logic test failed: ' . $e->getMessage());
        }
    }

    public function testBasicDonationCreation() {
        try {
            // Start a new output buffer for this test
            ob_start();

            // Create a payment object with required fields
            $payment = new Payment($this->conn);
            $payment->amount = 100.00;
            $payment->method = 'cash';
            $payment->processor = 'Staff';
            $payment->status = 'succeeded';
            $payment->date_deposited = date('Y-m-d H:i:s');

            // Mock the DonationService
            $donationService = $this->createMock(DonationService::class);
            $donationService->method('findShowAiring10MinutesAgo')
                ->willReturn([
                    'name' => 'Test Show',
                    'wp_id' => 1
                ]);

            // Create a donation using the Donation class
            $donation = new Donation($this->conn, $donationService);
            $donation->email_msg = $this->emailMock;  // Inject the email mock

            // Set required donation fields
            $donation->donor_id = $this->donor_id;
            $donation->type = 'Pledge';
            $donation->amount = 100.00;
            $donation->installment = 'One-Time';
            $donation->source = 'WebSite';
            $donation->read_onair = 1;
            $donation->donation_match = 0;
            $donation->transaction_id = md5(uniqid(rand(), true));
            $donation->ipaddress = '127.0.0.1';
            $donation->browser = 'PHPUnit Test Browser';
            $donation->show_name = 'Test Show';
            $donation->program_wp_id = 1;
            $donation->campaign_id = $this->campaign_id;
            
            // Set donor information from our pre-existing donor
            $stmt = $this->conn->prepare("SELECT * FROM donors WHERE id = ?");
            $stmt->execute([$this->donor_id]);
            $donor = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Set donor information to match our pre-existing donor
            $donation->firstname = $donor['firstname'];
            $donation->lastname = $donor['lastname'];
            $donation->email = $donor['email'];
            $donation->phone = $donor['phone'];
            $donation->address1 = $donor['address1'];
            $donation->city = $donor['city'];
            $donation->state = $donor['state'];
            $donation->country = $donor['country'];
            $donation->postal_code = $donor['postal_code'];
            $donation->paperless = $donor['paperless'];
            
            // Mock the donor search to return our pre-existing donor
            $donorObj = $this->createMock(Donor::class);
            $donorObj->method('searchDonor')
                ->willReturn(new class($donor) extends PDOStatement {
                    private $donor;
                    public function __construct($donor) { $this->donor = $donor; }
                    public function fetch(int $mode = PDO::FETCH_DEFAULT, int $cursorOrientation = PDO::FETCH_ORI_NEXT, int $cursorOffset = 0): mixed {
                        return $this->donor;
                    }
                });
            
            // Set up the donor mock to return our mock object
            $donation->donor = $donorObj;
            
            // Create the donation
            $result = $donation->create();
            
            // Get the last inserted donation from the database
            $stmt = $this->conn->prepare("SELECT * FROM donations WHERE transaction_id = ?");
            $stmt->execute([$donation->transaction_id]);
            $created_donation = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Clean up the output buffer
            ob_end_clean();
            
            // Assert the donation exists in database
            $this->assertNotNull($created_donation, "Donation should exist in database");
            $this->assertEquals($this->donor_id, $created_donation['donor_id'], "Donor ID should match");
            $this->assertEquals(100.00, $created_donation['amount'], "Amount should match");
            $this->assertEquals('Pledge', $created_donation['type'], "Type should match");
            $this->assertEquals('One-Time', $created_donation['installment'], "Installment should match");
            $this->assertEquals($donation->transaction_id, $created_donation['transaction_id'], "Transaction ID should match");
            
        } catch (Exception $e) {
            // Clean up the output buffer in case of error
            ob_end_clean();
            $this->fail("Test failed with exception: " . $e->getMessage());
        }
    }

    public function testPreventsDuplicateEmailErrorWithDonorId() {
        try {
            // Create a second donor with a different email first
            $stmt = $this->conn->prepare('INSERT INTO donors (
                firstname, lastname, phone, email, address1, city, state, country,
                postal_code, type, membership_level, deceased, donotsolicit, paperless,
                date_created
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');

            $stmt->execute([
                'Jane',
                'Doe',
                '5551234567',
                '<EMAIL>',  // Different email from test donor
                '123 Main St',
                'San Francisco',
                'CA',
                'US',
                '94102',
                'Individual',
                'Basic',
                0,
                0,
                1,
                date('Y-m-d H:i:s')
            ]);

            $existingDonorId = $this->conn->lastInsertId();

            // Test the core logic: when donor_id is provided, use existing donor without creating duplicate
            $donor = new Donor($this->conn);

            // Simulate the scenario: donor_id is provided (Station Admin pre-selected donor)
            if (isset($existingDonorId)) {
                $donor->donor_id = $existingDonorId;
                $donorData = $donor->read($existingDonorId)->fetch();

                // Verify the donor exists and has the expected email
                $this->assertNotFalse($donorData, 'Existing donor should be found');
                $this->assertEquals('<EMAIL>', $donorData['email'], 'Should retrieve correct donor email');
                $this->assertEquals($existingDonorId, $donorData['donor_id'], 'Should retrieve correct donor ID');

                // Key test: verify that when donor_id is provided, we DON'T create a new donor
                // This prevents the duplicate email constraint violation
                $this->assertTrue(true, 'Successfully used existing donor without creating duplicate');
            } else {
                $this->fail('donor_id should be set for this test');
            }

            // Verify only one donor exists with this email (no duplicate created)
            $stmt = $this->conn->prepare('SELECT COUNT(*) as count FROM donors WHERE email = ?');
            $stmt->execute(['<EMAIL>']);
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            $this->assertEquals(1, $count, 'Should only have one donor with this email');

            // Test the opposite scenario: verify we would create a new donor when donor_id is NOT provided
            $stmt = $this->conn->prepare('SELECT COUNT(*) as count FROM donors WHERE email = ?');
            $stmt->execute(['<EMAIL>']);
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            $this->assertEquals(0, $count, 'Should have no donors with nonexistent email');

        } catch (Exception $e) {
            $this->fail('Duplicate email prevention test failed: ' . $e->getMessage());
        }
    }

    protected function tearDown(): void {
        // Clean up test environment
        $this->cleanupTestEnvironment();
        
        // Clean up test data
        if ($this->conn) {
            $this->conn->exec("DELETE FROM donations");
            $this->conn->exec("DELETE FROM payments");
            $this->conn->exec("DELETE FROM donors");
            $this->conn->exec("DELETE FROM campaigns");
            
            // Close the connection
            $this->conn = null;
        }
    }
}

/**
 * Custom PDOStatement class that logs queries
 */
class LoggingPDOStatement extends PDOStatement {
    protected $pdo;
    protected $testCase;
    protected static $enableLogging = false;
    
    protected function __construct($pdo) {
        $this->pdo = $pdo;
        $this->testCase = null;
    }
    
    public function setTestCase($testCase) {
        $this->testCase = $testCase;
    }

    public static function enableLogging($enable = true) {
        self::$enableLogging = $enable;
    }
    
    #[\ReturnTypeWillChange]
    public function execute(?array $params = null): bool {
        if (!self::$enableLogging) {
            return parent::execute($params);
        }

        $query = $this->queryString;
        if ($params) {
            foreach ($params as $key => $value) {
                if (is_string($key)) {
                    $query = str_replace(":$key", $this->pdo->quote($value), $query);
                } else {
                    $query = preg_replace('/\?/', $this->pdo->quote($value), $query, 1);
                }
            }
        }
        
        try {
            $result = parent::execute($params);
            if ($this->testCase) {
                $this->testCase->addToAssertionCount(1);
            }
            return $result;
        } catch (PDOException $e) {
            if ($this->testCase) {
                $this->testCase->fail("Query failed: " . $e->getMessage());
            }
            throw $e;
        }
    }
}
