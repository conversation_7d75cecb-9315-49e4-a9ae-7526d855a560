<?php

use PHPUnit\Framework\TestCase;
use Kpfa\Tests\TestHelpers;
use Kpfa\Tests\TestPDO;

// Include required classes
require_once __DIR__ . "/../objects/payment.php";
require_once __DIR__ . "/../objects/donation.php";
require_once __DIR__ . "/../objects/subscription.php";
require_once __DIR__ . "/TestHelpers.php";
require_once __DIR__ . "/TestPDO.php";

/**
 * Integration tests for donation flows
 * 
 * These tests simulate the actual donation scenarios that are manually tested:
 * - [ ] Monthly donations from Station Admin with Existing Donor
 * - [ ] One-Time donations from Station Admin with Existing Donor
 * - [ ] Monthly donations from Station Admin with New Donor
 * - [ ] One-Time donations from Station Admin with New Donor
 * - [ ] Automatic subscription/monthly payments via Stripe webhooks
 * - [ ] Monthly donations from PDA with New Donor
 * - [ ] One-Time donations from PDA with New Donor
 * - [ ] Monthly donations from PDA with Existing Donor
 * - [ ] One-Time donations from PDA with Existing Donor
 */
class DonationFlowTest extends TestCase {
    use TestHelpers;
    
    private $conn;
    private $payment;
    private $donation;
    
    protected function setUp(): void {
        $this->setupTestEnvironment();
        
        // Create test database connection
        $this->conn = new TestPDO("sqlite::memory:");
        $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $this->createTestTables();
        $this->setupTestObjects();
    }
    
    private function setupTestObjects() {
        $this->payment = new Payment($this->conn);
        $this->donation = new Donation($this->conn);
    }
    
    private function createTestTables() {
        // Create comprehensive tables for donation flow testing
        $this->conn->exec("CREATE TABLE IF NOT EXISTS donors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            firstname TEXT,
            lastname TEXT,
            email TEXT,
            phone TEXT,
            address TEXT,
            city TEXT,
            state TEXT,
            zip TEXT,
            stripe_cus_id TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        $this->conn->exec("CREATE TABLE IF NOT EXISTS donations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donor_id INTEGER,
            transaction_id TEXT UNIQUE,
            amount REAL,
            installment TEXT,
            status TEXT,
            source TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donor_id) REFERENCES donors(id)
        )");
        
        $this->conn->exec("CREATE TABLE IF NOT EXISTS subscriptions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donor_id INTEGER,
            donation_id INTEGER,
            subscription_id TEXT,
            status TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donor_id) REFERENCES donors(id),
            FOREIGN KEY (donation_id) REFERENCES donations(id)
        )");
        
        $this->conn->exec("CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donation_id INTEGER,
            payment_id TEXT,
            amount REAL,
            status TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donation_id) REFERENCES donations(id)
        )");
        
        $this->conn->exec("CREATE TABLE IF NOT EXISTS stripe_prices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            amount REAL,
            stripe_price_id TEXT
        )");
    }
    
    /**
     * Test: Monthly donations from Station Admin with Existing Donor
     * 
     * This simulates the flow when a call center agent creates a monthly donation
     * for a donor who already exists in the system with a Stripe customer ID
     */
    public function testMonthlyDonationStationAdminExistingDonor() {
        // Create existing donor with Stripe customer ID
        $donorId = $this->createTestDonor([
            'firstname' => 'John',
            'lastname' => 'Doe',
            'email' => '<EMAIL>',
            'stripe_cus_id' => 'cus_existing123'
        ]);
        
        // Simulate Station Admin monthly donation request
        $paymentData = (object) [
            'donor_id' => $donorId,
            'customer_id' => 'cus_existing123',
            'transaction_id' => 'SA_MONTHLY_' . uniqid(),
            'amount' => 25.00,
            'installment' => 'Monthly',
            'email' => '<EMAIL>',
            'source' => 'Station Admin'
        ];
        
        // Test that the payment object can be created
        $this->assertInstanceOf(Payment::class, $this->payment);
        
        // Verify donor exists and has Stripe customer ID
        $donor = $this->getDonorById($donorId);
        $this->assertEquals('cus_existing123', $donor['stripe_cus_id']);
        $this->assertEquals('<EMAIL>', $donor['email']);
        
        // Test payment data structure
        $this->assertEquals('Monthly', $paymentData->installment);
        $this->assertEquals(25.00, $paymentData->amount);
        $this->assertStringContainsString('SA_MONTHLY_', $paymentData->transaction_id);
        
        $this->assertTrue(true, 'Monthly donation from Station Admin with existing donor setup correctly');
    }
    
    /**
     * Test: One-Time donations from Station Admin with Existing Donor
     */
    public function testOneTimeDonationStationAdminExistingDonor() {
        // Create existing donor
        $donorId = $this->createTestDonor([
            'firstname' => 'Jane',
            'lastname' => 'Smith',
            'email' => '<EMAIL>',
            'stripe_cus_id' => 'cus_existing456'
        ]);
        
        // Simulate Station Admin one-time donation
        $paymentData = (object) [
            'donor_id' => $donorId,
            'customer_id' => 'cus_existing456',
            'transaction_id' => 'SA_ONETIME_' . uniqid(),
            'amount' => 50.00,
            'installment' => 'One-Time',
            'email' => '<EMAIL>',
            'source' => 'Station Admin'
        ];
        
        // Verify setup
        $donor = $this->getDonorById($donorId);
        $this->assertEquals('cus_existing456', $donor['stripe_cus_id']);
        $this->assertEquals('One-Time', $paymentData->installment);
        $this->assertEquals(50.00, $paymentData->amount);
        
        $this->assertTrue(true, 'One-time donation from Station Admin with existing donor setup correctly');
    }
    
    /**
     * Test: Monthly donations from Station Admin with New Donor
     */
    public function testMonthlyDonationStationAdminNewDonor() {
        // Simulate new donor data from Station Admin
        $newDonorData = [
            'firstname' => 'Bob',
            'lastname' => 'Johnson',
            'email' => '<EMAIL>',
            'phone' => '555-0123',
            'address' => '123 Main St',
            'city' => 'Berkeley',
            'state' => 'CA',
            'zip' => '94702'
        ];
        
        // Create new donor (no Stripe customer ID yet)
        $donorId = $this->createTestDonor($newDonorData);
        
        // Simulate Station Admin monthly donation for new donor
        $paymentData = (object) [
            'donor_id' => $donorId,
            'customer_id' => null, // Will be created during payment processing
            'transaction_id' => 'SA_MONTHLY_NEW_' . uniqid(),
            'amount' => 35.00,
            'installment' => 'Monthly',
            'email' => '<EMAIL>',
            'source' => 'Station Admin'
        ];
        
        // Verify new donor setup
        $donor = $this->getDonorById($donorId);
        $this->assertNull($donor['stripe_cus_id']); // Should be null for new donor
        $this->assertEquals('<EMAIL>', $donor['email']);
        $this->assertEquals('Monthly', $paymentData->installment);
        
        $this->assertTrue(true, 'Monthly donation from Station Admin with new donor setup correctly');
    }
    
    /**
     * Test: One-Time donations from Station Admin with New Donor
     */
    public function testOneTimeDonationStationAdminNewDonor() {
        // Create new donor
        $donorId = $this->createTestDonor([
            'firstname' => 'Alice',
            'lastname' => 'Wilson',
            'email' => '<EMAIL>'
        ]);
        
        // Simulate one-time donation for new donor
        $paymentData = (object) [
            'donor_id' => $donorId,
            'customer_id' => null,
            'transaction_id' => 'SA_ONETIME_NEW_' . uniqid(),
            'amount' => 100.00,
            'installment' => 'One-Time',
            'email' => '<EMAIL>',
            'source' => 'Station Admin'
        ];
        
        // Verify setup
        $donor = $this->getDonorById($donorId);
        $this->assertNull($donor['stripe_cus_id']);
        $this->assertEquals('One-Time', $paymentData->installment);
        $this->assertEquals(100.00, $paymentData->amount);
        
        $this->assertTrue(true, 'One-time donation from Station Admin with new donor setup correctly');
    }

    /**
     * Test: Monthly donations from PDA (Public Donation App) with New Donor
     */
    public function testMonthlyDonationPublicDonationAppNewDonor() {
        // Simulate PDA donation data for new donor
        $paymentData = (object) [
            'donor_id' => null, // Will be created during processing
            'customer_id' => null,
            'transaction_id' => 'PDA_MONTHLY_NEW_' . uniqid(),
            'amount' => 15.00,
            'installment' => 'Monthly',
            'email' => '<EMAIL>',
            'firstname' => 'Charlie',
            'lastname' => 'Brown',
            'source' => 'PDA'
        ];

        // Verify PDA donation structure
        $this->assertEquals('Monthly', $paymentData->installment);
        $this->assertEquals(15.00, $paymentData->amount);
        $this->assertStringContainsString('PDA_MONTHLY_NEW_', $paymentData->transaction_id);
        $this->assertEquals('PDA', $paymentData->source);

        $this->assertTrue(true, 'Monthly donation from PDA with new donor setup correctly');
    }

    /**
     * Test: One-Time donations from PDA with New Donor
     */
    public function testOneTimeDonationPublicDonationAppNewDonor() {
        $paymentData = (object) [
            'donor_id' => null,
            'customer_id' => null,
            'transaction_id' => 'PDA_ONETIME_NEW_' . uniqid(),
            'amount' => 75.00,
            'installment' => 'One-Time',
            'email' => '<EMAIL>',
            'firstname' => 'Diana',
            'lastname' => 'Prince',
            'source' => 'PDA'
        ];

        $this->assertEquals('One-Time', $paymentData->installment);
        $this->assertEquals(75.00, $paymentData->amount);
        $this->assertStringContainsString('PDA_ONETIME_NEW_', $paymentData->transaction_id);

        $this->assertTrue(true, 'One-time donation from PDA with new donor setup correctly');
    }

    /**
     * Test: Monthly donations from PDA with Existing Donor
     */
    public function testMonthlyDonationPublicDonationAppExistingDonor() {
        // Create existing donor
        $donorId = $this->createTestDonor([
            'firstname' => 'Eve',
            'lastname' => 'Adams',
            'email' => '<EMAIL>',
            'stripe_cus_id' => 'cus_pda_existing789'
        ]);

        $paymentData = (object) [
            'donor_id' => $donorId,
            'customer_id' => 'cus_pda_existing789',
            'transaction_id' => 'PDA_MONTHLY_EXISTING_' . uniqid(),
            'amount' => 20.00,
            'installment' => 'Monthly',
            'email' => '<EMAIL>',
            'source' => 'PDA'
        ];

        $donor = $this->getDonorById($donorId);
        $this->assertEquals('cus_pda_existing789', $donor['stripe_cus_id']);
        $this->assertEquals('Monthly', $paymentData->installment);

        $this->assertTrue(true, 'Monthly donation from PDA with existing donor setup correctly');
    }

    /**
     * Test: One-Time donations from PDA with Existing Donor
     */
    public function testOneTimeDonationPublicDonationAppExistingDonor() {
        // Create existing donor
        $donorId = $this->createTestDonor([
            'firstname' => 'Frank',
            'lastname' => 'Miller',
            'email' => '<EMAIL>',
            'stripe_cus_id' => 'cus_pda_existing101'
        ]);

        $paymentData = (object) [
            'donor_id' => $donorId,
            'customer_id' => 'cus_pda_existing101',
            'transaction_id' => 'PDA_ONETIME_EXISTING_' . uniqid(),
            'amount' => 125.00,
            'installment' => 'One-Time',
            'email' => '<EMAIL>',
            'source' => 'PDA'
        ];

        $donor = $this->getDonorById($donorId);
        $this->assertEquals('cus_pda_existing101', $donor['stripe_cus_id']);
        $this->assertEquals('One-Time', $paymentData->installment);

        $this->assertTrue(true, 'One-time donation from PDA with existing donor setup correctly');
    }

    /**
     * Test: Automatic subscription/monthly payments via Stripe webhooks
     */
    public function testStripeWebhookSubscriptionPayment() {
        // Create existing subscription
        $donorId = $this->createTestDonor([
            'firstname' => 'Grace',
            'lastname' => 'Hopper',
            'email' => '<EMAIL>',
            'stripe_cus_id' => 'cus_webhook_test'
        ]);

        // Simulate webhook data for subscription payment
        $webhookData = (object) [
            'type' => 'invoice.payment_succeeded',
            'data' => (object) [
                'object' => (object) [
                    'id' => 'in_webhook_test',
                    'customer' => 'cus_webhook_test',
                    'subscription' => 'sub_webhook_test',
                    'amount_paid' => 2500,
                    'status' => 'paid'
                ]
            ]
        ];

        // Verify webhook structure
        $this->assertEquals('invoice.payment_succeeded', $webhookData->type);
        $this->assertEquals('cus_webhook_test', $webhookData->data->object->customer);
        $this->assertEquals('sub_webhook_test', $webhookData->data->object->subscription);
        $this->assertEquals('paid', $webhookData->data->object->status);

        $this->assertTrue(true, 'Stripe webhook subscription payment setup correctly');
    }

    // Helper methods for test setup
    
    private function createTestDonor($data) {
        $stmt = $this->conn->prepare("
            INSERT INTO donors (firstname, lastname, email, phone, address, city, state, zip, stripe_cus_id) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $data['firstname'] ?? null,
            $data['lastname'] ?? null,
            $data['email'] ?? null,
            $data['phone'] ?? null,
            $data['address'] ?? null,
            $data['city'] ?? null,
            $data['state'] ?? null,
            $data['zip'] ?? null,
            $data['stripe_cus_id'] ?? null
        ]);
        
        return $this->conn->lastInsertId();
    }
    
    private function getDonorById($id) {
        $stmt = $this->conn->prepare("SELECT * FROM donors WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    protected function tearDown(): void {
        $this->cleanupTestEnvironment();

        if ($this->conn) {
            $this->conn->exec("DELETE FROM subscriptions");
            $this->conn->exec("DELETE FROM donations");
            $this->conn->exec("DELETE FROM donors");
            $this->conn->exec("DELETE FROM stripe_prices");
            $this->conn = null;
        }
    }
}
