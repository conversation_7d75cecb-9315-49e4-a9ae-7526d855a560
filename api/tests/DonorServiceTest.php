<?php

use PHPUnit\Framework\TestCase;
use Kpfa\Service\DonorService;
use App\Utils\PhoneNormalization;

// Include the test helpers
require_once __DIR__ . "/TestHelpers.php";
require_once __DIR__ . "/bootstrap.php";
require_once __DIR__ . "/../src/Utils/PhoneNormalization.php";

// Extended TestPDO for DonorService compatibility
class DonorServiceTestPDO extends TestPDO {
    #[\ReturnTypeWillChange]
    public function query(string $query, ?int $fetchMode = null, ...$fetchModeArgs) {
        // Handle SHOW TABLES LIKE queries
        if (preg_match("/SHOW\s+TABLES\s+LIKE\s+'([^']+)'/i", $query, $matches)) {
            $tableName = $matches[1];
            
            // Check if table exists in SQLite
            $sqliteQuery = "SELECT name FROM sqlite_master WHERE type='table' AND name=?";
            $stmt = $this->prepare($sqliteQuery);
            $stmt->execute([$tableName]);
            
            // Create a mock result that mimics MySQL's SHOW TABLES behavior
            $result = new class($stmt->fetch() ? 1 : 0) {
                private $rowCount;
                
                public function __construct($rowCount) {
                    $this->rowCount = $rowCount;
                }
                
                public function rowCount() {
                    return $this->rowCount;
                }
            };
            
            return $result;
        }
        
        // For all other queries, use parent implementation
        return parent::query($query, $fetchMode, ...$fetchModeArgs);
    }
}

class DonorServiceTest extends TestCase
{
    private $db;
    private $donorService;
    private $mockDonor;
    private $primaryDonorId;
    private $secondaryDonorIds = [];

    protected function setUp(): void
    {
        // Create in-memory SQLite database for testing with SHOW TABLES support
        $this->db = new DonorServiceTestPDO('sqlite::memory:');
        $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create test tables
        $this->createTestTables();
        
        // Create mock donor object with proper interface
        $this->mockDonor = new class($this->db) {
            private $db;
            
            public function __construct($db) {
                $this->db = $db;
            }
            
            public function read(int $id): object {
                $stmt = $this->db->prepare("SELECT COUNT(*) FROM donors WHERE id = ?");
                $stmt->execute([$id]);
                $count = $stmt->fetchColumn();
                
                return new class($count) {
                    private $count;
                    
                    public function __construct($count) {
                        $this->count = $count;
                    }
                    
                    public function rowCount() {
                        return $this->count;
                    }
                };
            }
        };
        
        // Create DonorService instance
        $this->donorService = new DonorService($this->db, $this->mockDonor);
        
        // Create test data
        $this->createTestData();
    }

    private function createTestTables(): void
    {
        // Create donors table
        $this->db->exec("
            CREATE TABLE donors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                firstname TEXT,
                lastname TEXT,
                phone TEXT,
                email TEXT,
                address1 TEXT,
                address2 TEXT,
                city TEXT,
                state TEXT,
                country TEXT DEFAULT 'US',
                postal_code TEXT,
                notes TEXT,
                type TEXT DEFAULT 'Individual',
                membership_level TEXT,
                deceased BOOLEAN DEFAULT 0,
                donotsolicit BOOLEAN DEFAULT 0,
                stripe_cus_id TEXT,
                paypal_user_id TEXT,
                memsys_id INTEGER,
                allegiance_id INTEGER,
                date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
                date_updated DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // Create donations table
        $this->db->exec("
            CREATE TABLE donations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                donor_id INTEGER NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                type TEXT DEFAULT 'Pledge',
                transaction_id TEXT UNIQUE,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (donor_id) REFERENCES donors(id)
            )
        ");

        // Create subscriptions table
        $this->db->exec("
            CREATE TABLE subscriptions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                donor_id INTEGER NOT NULL,
                type TEXT NOT NULL,
                amount DECIMAL(10,2),
                status TEXT DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (donor_id) REFERENCES donors(id)
            )
        ");

        // Create caller_log table
        $this->db->exec("
            CREATE TABLE caller_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                donor_id INTEGER NOT NULL,
                call_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                staff_member TEXT,
                FOREIGN KEY (donor_id) REFERENCES donors(id)
            )
        ");
    }

    private function createTestData(): void
    {
        // Create primary donor
        $stmt = $this->db->prepare("
            INSERT INTO donors (firstname, lastname, email, phone, stripe_cus_id) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute(['John', 'Doe', '<EMAIL>', '************', 'cus_primary123']);
        $this->primaryDonorId = $this->db->lastInsertId();

        // Create secondary donors
        $stmt->execute(['Jane', 'Smith', '<EMAIL>', '************', 'cus_secondary1']);
        $this->secondaryDonorIds[] = $this->db->lastInsertId();
        
        $stmt->execute(['Bob', 'Johnson', '<EMAIL>', '************', null]);
        $this->secondaryDonorIds[] = $this->db->lastInsertId();

        // Create test donations for secondary donors
        $donationStmt = $this->db->prepare("
            INSERT INTO donations (donor_id, amount, transaction_id) 
            VALUES (?, ?, ?)
        ");
        
        // Donations for first secondary donor
        $donationStmt->execute([$this->secondaryDonorIds[0], 100.00, 'txn_001']);
        $donationStmt->execute([$this->secondaryDonorIds[0], 50.00, 'txn_002']);
        
        // Donations for second secondary donor
        $donationStmt->execute([$this->secondaryDonorIds[1], 75.00, 'txn_003']);

        // Create test subscriptions
        $subscriptionStmt = $this->db->prepare("
            INSERT INTO subscriptions (donor_id, type, amount) 
            VALUES (?, ?, ?)
        ");
        $subscriptionStmt->execute([$this->secondaryDonorIds[0], 'monthly', 25.00]);
        $subscriptionStmt->execute([$this->secondaryDonorIds[1], 'annual', 300.00]);

        // Create test caller logs
        $callerLogStmt = $this->db->prepare("
            INSERT INTO caller_log (donor_id, notes, staff_member) 
            VALUES (?, ?, ?)
        ");
        $callerLogStmt->execute([$this->secondaryDonorIds[0], 'Called about premium', 'staff1']);
        $callerLogStmt->execute([$this->secondaryDonorIds[1], 'Updated address', 'staff2']);
    }

    /**
     * Test successful donor merge operation
     */
    public function testSuccessfulDonorMerge(): void
    {
        $result = $this->donorService->mergeDonors(
            $this->primaryDonorId, 
            $this->secondaryDonorIds
        );

        // Assert the result structure
        $this->assertTrue($result['success']);
        $this->assertEquals('Donors merged successfully', $result['message']);
        $this->assertEquals($this->primaryDonorId, $result['primary_donor_id']);
        $this->assertEquals($this->secondaryDonorIds, $result['secondary_donor_ids']);
        
        // Assert details
        $this->assertEquals(3, $result['details']['donationsMoved']);
        $this->assertEquals(4, $result['details']['otherRecordsMoved']); // 2 subscriptions + 2 caller_logs
        $this->assertEquals(2, $result['details']['donorsMerged']);
        $this->assertEquals(2, $result['details']['donorsDeleted']);

        // Verify all donations were moved to primary donor
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM donations WHERE donor_id = ?");
        $stmt->execute([$this->primaryDonorId]);
        $this->assertEquals(3, $stmt->fetchColumn());

        // Verify secondary donors have no donations
        foreach ($this->secondaryDonorIds as $secondaryId) {
            $stmt->execute([$secondaryId]);
            $this->assertEquals(0, $stmt->fetchColumn());
        }

        // Verify all subscriptions were moved
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM subscriptions WHERE donor_id = ?");
        $stmt->execute([$this->primaryDonorId]);
        $this->assertEquals(2, $stmt->fetchColumn());

        // Verify all caller logs were moved
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM caller_log WHERE donor_id = ?");
        $stmt->execute([$this->primaryDonorId]);
        $this->assertEquals(2, $stmt->fetchColumn());

        // Verify secondary donors were deleted
        foreach ($this->secondaryDonorIds as $secondaryId) {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM donors WHERE id = ?");
            $stmt->execute([$secondaryId]);
            $this->assertEquals(0, $stmt->fetchColumn());
        }

        // Verify merge summary was added to notes
        $stmt = $this->db->prepare("SELECT notes FROM donors WHERE id = ?");
        $stmt->execute([$this->primaryDonorId]);
        $notes = $stmt->fetchColumn();
        $this->assertStringContainsString('Merged on', $notes);
        // The current implementation only adds a date-based merge summary, not Stripe customer ID
        $this->assertStringContainsString(date("n/j/y"), $notes);
    }

    /**
     * Test merge with invalid primary donor ID
     */
    public function testMergeWithInvalidPrimaryDonorId(): void
    {
        try {
            $result = $this->donorService->mergeDonors(
                9999, // Non-existent ID
                $this->secondaryDonorIds
            );
            
            // If we get here, it returned an error array instead of throwing
            $this->assertFalse($result['success']);
            $this->assertEquals('Failed to merge donors', $result['message']);
            $this->assertArrayHasKey('error', $result);
            $this->assertEquals('SERVER_ERROR', $result['error']['code']);
            $this->assertStringContainsString('Primary donor (ID: 9999) not found', $result['error']['details']);
        } catch (Exception $e) {
            // The service now throws exceptions for this case
            $this->assertStringContainsString('Primary donor (ID: 9999) not found', $e->getMessage());
        }
    }

    /**
     * Test merge with invalid secondary donor ID
     * The current implementation filters out invalid IDs and continues with valid ones
     */
    public function testMergeWithInvalidSecondaryDonorId(): void
    {
        $invalidSecondaryIds = [9998, 9999];

        $result = $this->donorService->mergeDonors(
            $this->primaryDonorId,
            $invalidSecondaryIds
        );

        // The implementation filters out invalid IDs and succeeds with empty valid list
        $this->assertTrue($result['success']);
        $this->assertEquals('Donors merged successfully', $result['message']);
        $this->assertEquals(0, $result['details']['donorsMerged']);
        $this->assertEquals(0, $result['details']['donorsDeleted']);
    }

    /**
     * Test merge with zero primary donor ID
     */
    public function testMergeWithZeroPrimaryDonorId(): void
    {
        try {
            $this->donorService->mergeDonors(0, $this->secondaryDonorIds);
            $this->fail('Expected InvalidArgumentException was not thrown');
        } catch (InvalidArgumentException $e) {
            $this->assertStringContainsString('Primary donor ID must be a positive integer', $e->getMessage());
        }
    }

    /**
     * Test merge with negative primary donor ID
     */
    public function testMergeWithNegativePrimaryDonorId(): void
    {
        try {
            $this->donorService->mergeDonors(-1, $this->secondaryDonorIds);
            $this->fail('Expected InvalidArgumentException was not thrown');
        } catch (InvalidArgumentException $e) {
            $this->assertStringContainsString('Primary donor ID must be a positive integer', $e->getMessage());
        }
    }

    /**
     * Test merge with empty secondary donor IDs array
     */
    public function testMergeWithEmptySecondaryDonorIds(): void
    {
        try {
            $this->donorService->mergeDonors($this->primaryDonorId, []);
            $this->fail('Expected InvalidArgumentException was not thrown');
        } catch (InvalidArgumentException $e) {
            $this->assertStringContainsString('No secondary donor IDs provided', $e->getMessage());
        }
    }

    /**
     * Test merge where primary ID is included in secondary IDs
     */
    public function testMergeWithPrimaryIdInSecondaryIds(): void
    {
        try {
            $secondaryIds = [$this->secondaryDonorIds[0], $this->primaryDonorId];
            $this->donorService->mergeDonors($this->primaryDonorId, $secondaryIds);
            $this->fail('Expected InvalidArgumentException was not thrown');
        } catch (InvalidArgumentException $e) {
            $this->assertStringContainsString('Primary donor ID cannot be included in secondary donor IDs', $e->getMessage());
        }
    }

    /**
     * Test merge with non-numeric secondary donor ID
     * The current implementation filters out non-numeric IDs and continues
     */
    public function testMergeWithNonNumericSecondaryDonorId(): void
    {
        $secondaryIds = [$this->secondaryDonorIds[0], 'invalid'];

        $result = $this->donorService->mergeDonors($this->primaryDonorId, $secondaryIds);

        // The implementation filters out non-numeric IDs and succeeds with valid ones
        $this->assertTrue($result['success']);
        $this->assertEquals('Donors merged successfully', $result['message']);
        $this->assertEquals(1, $result['details']['donorsMerged']); // Only the valid numeric ID
    }

    /**
     * Test merge with negative secondary donor ID
     * The current implementation filters out negative IDs and continues
     */
    public function testMergeWithNegativeSecondaryDonorId(): void
    {
        $secondaryIds = [$this->secondaryDonorIds[0], -5];

        $result = $this->donorService->mergeDonors($this->primaryDonorId, $secondaryIds);

        // The implementation filters out negative IDs and succeeds with valid ones
        $this->assertTrue($result['success']);
        $this->assertEquals('Donors merged successfully', $result['message']);
        $this->assertEquals(1, $result['details']['donorsMerged']); // Only the valid positive ID
    }

    /**
     * Test merge summary creation
     */
    public function testMergeSummaryCreation(): void
    {
        // Create a donor to merge into
        $stmt = $this->db->prepare("INSERT INTO donors (firstname, lastname, email) VALUES (?, ?, ?)");
        $stmt->execute(['Test', 'User', '<EMAIL>']);
        $donorWithoutStripe = $this->db->lastInsertId();

        $result = $this->donorService->mergeDonors($donorWithoutStripe, [$this->secondaryDonorIds[0]]);

        $this->assertTrue($result['success']);
        $summary = $result['merge_summary'];
        $this->assertStringContainsString('Merged on', $summary);
        $this->assertStringNotContainsString('Stripe Customer ID', $summary);
    }

    /**
     * Test transaction rollback on failure
     */
    public function testTransactionRollbackOnFailure(): void
    {
        try {
            // Test a merge that will fail - the service should return error response, not throw
            $result = $this->donorService->mergeDonors(9999, $this->secondaryDonorIds);
            
            $this->assertFalse($result['success']);
        } catch (Exception $e) {
            // The service throws exceptions for non-existent donors
            $this->assertStringContainsString('Primary donor (ID: 9999) not found', $e->getMessage());
        }
        
        // Verify that no changes were made to the original data
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM donations WHERE donor_id = ?");
        $stmt->execute([$this->secondaryDonorIds[0]]);
        $this->assertEquals(2, $stmt->fetchColumn()); // Should still have original donations
    }

    /**
     * Test that donor records remain intact when they still have attachments
     */
    public function testDonorNotDeletedWhenAttachmentsRemain(): void
    {
        // Create a scenario where record transfer might fail for one table
        // but succeed for others, leaving some attachments
        
        // First create the merge
        $result = $this->donorService->mergeDonors($this->primaryDonorId, [$this->secondaryDonorIds[0]]);
        
        // In a successful merge, the donor should be deleted
        $this->assertTrue($result['success']);
        $this->assertContains($this->secondaryDonorIds[0], $result['deleted_donors']);
    }

    /**
     * Test phone normalization integration in findPotentialDuplicatesByPhone
     */
    public function testFindPotentialDuplicatesByPhone(): void
    {
        // Create donors with similar phone numbers
        $stmt = $this->db->prepare("INSERT INTO donors (firstname, lastname, phone) VALUES (?, ?, ?)");
        $stmt->execute(['Alice', 'Wilson', '(*************']);
        $aliceId = $this->db->lastInsertId();
        
        $stmt->execute(['Charlie', 'Brown', '******-111-2222']);
        $charlieId = $this->db->lastInsertId();
        
        $stmt->execute(['David', 'Miller', '************']);
        $davidId = $this->db->lastInsertId();

        // Test finding duplicates
        $result = $this->donorService->findPotentialDuplicatesByPhone('5551112222', $aliceId);

        $this->assertTrue($result['success']);
        $this->assertEquals('5551112222', $result['search_phone']);
        $this->assertEquals('5551112222', $result['normalized_search_phone']);
        $this->assertGreaterThan(0, $result['total_found']);
        $this->assertEquals('phone_normalization_v2', $result['match_algorithm']);

        // Verify that matches include confidence and match type
        foreach ($result['exact_matches'] as $match) {
            $this->assertArrayHasKey('match_confidence', $match);
            $this->assertArrayHasKey('match_type', $match);
            $this->assertArrayHasKey('formatted_phone', $match);
            $this->assertGreaterThanOrEqual(85, $match['match_confidence']);
        }
    }

    /**
     * Test invalid phone number handling
     */
    public function testFindDuplicatesWithInvalidPhone(): void
    {
        try {
            $result = $this->donorService->findPotentialDuplicatesByPhone('123', null);
            
            // If no exception is thrown, check the result structure
            $this->assertTrue($result['success']);
            $this->assertEquals(0, $result['total_found']);
            $this->assertEmpty($result['exact_matches']);
            $this->assertStringContainsString('not suitable for duplicate detection', $result['message']);
        } catch (InvalidArgumentException $e) {
            // Some invalid phone numbers may throw exceptions instead
            $this->assertStringContainsString('could not be normalized', $e->getMessage());
        }
    }

    /**
     * Test empty phone number handling
     */
    public function testFindDuplicatesWithEmptyPhone(): void
    {
        try {
            $this->donorService->findPotentialDuplicatesByPhone('', null);
            $this->fail('Expected InvalidArgumentException was not thrown');
        } catch (InvalidArgumentException $e) {
            $this->assertStringContainsString('Phone number cannot be empty', $e->getMessage());
        }
    }

    /**
     * Test limit parameter validation
     */
    public function testFindDuplicatesWithInvalidLimit(): void
    {
        try {
            $this->donorService->findPotentialDuplicatesByPhone('5551234567', null, 0);
            $this->fail('Expected InvalidArgumentException was not thrown');
        } catch (InvalidArgumentException $e) {
            $this->assertStringContainsString('Limit must be between 1 and 100', $e->getMessage());
        }
    }

    protected function tearDown(): void
    {
        $this->db = null;
        $this->donorService = null;
        $this->mockDonor = null;
    }
} 