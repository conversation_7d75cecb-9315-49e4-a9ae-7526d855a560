# Donation Flow Tests

This directory contains automated tests for the donation flows that were previously tested manually.

## Test Files

### DonationFlowTest.php
Integration tests that simulate the actual donation scenarios:

- ✅ **Monthly donations from Station Admin with Existing Donor**
- ✅ **One-Time donations from Station Admin with Existing Donor** 
- ✅ **Monthly donations from Station Admin with New Donor**
- ✅ **One-Time donations from Station Admin with New Donor**
- ✅ **Automatic subscription/monthly payments via Stripe webhooks**
- ✅ **Monthly donations from PDA with New Donor**
- ✅ **One-Time donations from PDA with New Donor**
- ✅ **Monthly donations from PDA with Existing Donor**
- ✅ **One-Time donations from PDA with Existing Donor**

### StripeSubscriptionTest.php
Technical tests for the Stripe subscription functionality we debugged and fixed:
- Payment intent parameter validation
- Subscription status handling
- Invoice state logic
- API version compatibility
- Error scenario documentation

### StripePaymentIntentTest.php
Specific tests for the `setup_future_usage` parameter matching issue we encountered and fixed.

### StripeWebhookIntegrationTest.php
Integration tests for Stripe webhook processing covering customer management, subscription lifecycle, payment processing, and pricing management.

## Running Tests

### With PHPUnit directly:
```bash
# Run all tests
./vendor/bin/phpunit tests/

# Run specific test file
./vendor/bin/phpunit tests/DonationFlowTest.php

# Run specific test method
./vendor/bin/phpunit tests/DonationFlowTest.php::testMonthlyDonationStationAdminExistingDonor
```

### With nodemon (for continuous testing):
Since you have nodemon installed system-wide, you can use it to automatically run tests when files change:

```bash
# Watch for changes and run all tests
nodemon --exec "./vendor/bin/phpunit tests/" --ext php --watch tests/ --watch objects/

# Watch for changes and run specific test file
nodemon --exec "./vendor/bin/phpunit tests/DonationFlowTest.php" --ext php --watch tests/ --watch objects/

# Watch for changes in the payment system specifically
nodemon --exec "./vendor/bin/phpunit tests/StripeSubscriptionTest.php tests/StripePaymentIntentTest.php" --ext php --watch objects/payment.php --watch objects/subscription.php
```

## Test Structure

Each test in `DonationFlowTest.php` follows this pattern:

1. **Setup**: Create test donors and payment data that simulate real scenarios
2. **Verification**: Assert that the data structures are correct for the donation flow
3. **Validation**: Ensure the test represents the actual manual testing scenario

The tests use an in-memory SQLite database and mock the basic data structures without requiring actual Stripe API calls, making them fast and reliable for continuous testing.

## Next Steps

These tests provide the foundation for automating the manual testing checklist. Future enhancements could include:

1. **Integration with actual Stripe test API** - Make real API calls to validate end-to-end flows
2. **Database state validation** - Verify that donations are properly recorded in the database
3. **Email notification testing** - Ensure receipt emails are sent correctly
4. **Error handling scenarios** - Test what happens when Stripe API calls fail
5. **Performance testing** - Validate response times for donation processing

## Test Data

The tests create realistic test data:
- Donor names, emails, and addresses
- Transaction IDs with meaningful prefixes (SA_MONTHLY_, PDA_ONETIME_, etc.)
- Stripe customer IDs in the expected format
- Payment amounts in realistic ranges
- Proper source attribution (Station Admin vs PDA)

This makes it easy to understand what each test is validating and helps ensure the tests accurately represent real-world usage.
