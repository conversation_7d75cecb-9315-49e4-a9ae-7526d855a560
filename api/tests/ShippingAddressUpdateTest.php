<?php

use PHPUnit\Framework\TestCase;

require_once './tests/bootstrap.php';

class ShippingAddressUpdateTest extends TestCase
{
    private $conn;

    protected function setUp(): void
    {
        require_once './tests/TestPDO.php';
        $this->conn = new TestPDO('sqlite::memory:');
        
        // Create minimal test tables
        $this->conn->exec('
            CREATE TABLE donations (
                id INTEGER PRIMARY KEY,
                donor_id INTEGER DEFAULT 1,
                amount DECIMAL(10,2) DEFAULT 100.00,
                type VARCHAR(50) DEFAULT "Pledge",
                installment VARCHAR(50) DEFAULT "One-Time",
                transaction_id VARCHAR(255),
                comments TEXT,
                read_onair BOOLEAN DEFAULT 0,
                donation_match BOOLEAN DEFAULT 0,
                campaign_id INTEGER NULL
            )
        ');
        
        $this->conn->exec('
            CREATE TABLE shipment_addresses (
                id INTEGER PRIMARY KEY,
                donation_id INTEGER UNIQUE,
                firstname <PERSON><PERSON><PERSON><PERSON>(255),
                lastname <PERSON><PERSON><PERSON><PERSON>(255),
                address1 VARCHAR(255),
                address2 VARCHAR(255),
                city VARCHAR(255),
                state VARCHAR(255),
                country VARCHAR(255),
                postal_code VARCHAR(255)
            )
        ');
    }

    protected function tearDown(): void
    {
        // Clean up tables between tests
        $this->conn->exec('DELETE FROM shipment_addresses');
        $this->conn->exec('DELETE FROM donations');
    }

    public function testShippingAddressCheckLogic()
    {
        // Test the core logic of our fix: checking if shipment_addresses exists and creating if needed
        
        // Insert a donation without shipping address
        $result = $this->conn->exec('INSERT INTO donations (id, transaction_id) VALUES (1, "test123")');
        $this->assertNotFalse($result, 'Should successfully insert donation');
        
        // Verify no shipment_addresses record exists
        $stmt = $this->conn->prepare('SELECT COUNT(*) FROM shipment_addresses WHERE donation_id = 1');
        $stmt->execute();
        $this->assertEquals(0, $stmt->fetchColumn(), 'Should start with no shipping address');
        
        // Simulate our fix logic
        $donation_id = 1;
        $shipping_firstname = 'Test';
        $shipping_lastname = 'User';
        $shipping_address1 = '123 Main St';
        $shipping_city = 'Berkeley';
        $shipping_state = 'CA';
        $shipping_country = 'US';
        $shipping_postal_code = '94704';
        
        // Check if shipping data is provided (our condition)
        $has_shipping_data = $shipping_firstname || $shipping_lastname || $shipping_address1 || 
                           $shipping_city || $shipping_state || $shipping_country || $shipping_postal_code;
        
        $this->assertTrue($has_shipping_data, 'Should detect shipping data is provided');
        
        if ($has_shipping_data) {
            // Check if shipment_addresses record exists (our query)
            $check_query = 'SELECT COUNT(*) FROM shipment_addresses WHERE donation_id = ?';
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->execute([$donation_id]);
            $existing_count = $check_stmt->fetchColumn();

            $this->assertEquals(0, $existing_count, 'Should find no existing shipping address');

            if ($existing_count == 0) {
                // Create the missing record (our fix)
                $insert_query = '
                    INSERT INTO shipment_addresses 
                    (donation_id, firstname, lastname, address1, city, state, country, postal_code) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ';
                $insert_stmt = $this->conn->prepare($insert_query);
                $result = $insert_stmt->execute([
                    $donation_id, $shipping_firstname, $shipping_lastname, $shipping_address1,
                    $shipping_city, $shipping_state, $shipping_country, $shipping_postal_code
                ]);
                
                $this->assertTrue($result, 'Should successfully create shipping address record');
                
                // Verify insert worked
                $insertedId = $this->conn->lastInsertId();
                $this->assertGreaterThan(0, $insertedId, 'Should have valid insert ID');
            }
        }
        
        // Verify the record was created
        $stmt = $this->conn->prepare('SELECT * FROM shipment_addresses WHERE donation_id = 1');
        $stmt->execute();
        $address = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertNotFalse($address, 'Shipping address should exist after fix');
        $this->assertEquals('Test', $address['firstname']);
        $this->assertEquals('User', $address['lastname']);
        $this->assertEquals('123 Main St', $address['address1']);
        $this->assertEquals('Berkeley', $address['city']);
        $this->assertEquals('CA', $address['state']);
        $this->assertEquals('US', $address['country']);
        $this->assertEquals('94704', $address['postal_code']);
    }
    
    public function testNoShippingAddressWhenNoShippingData()
    {
        // Test that we don't create shipping addresses when no shipping data is provided
        
        // Insert a donation without shipping address
        $this->conn->exec('INSERT INTO donations (id, transaction_id) VALUES (2, "test456")');
        
        // Simulate no shipping data
        $shipping_firstname = null;
        $shipping_lastname = null;
        $shipping_address1 = null;
        $shipping_city = null;
        $shipping_state = null;
        $shipping_country = null;
        $shipping_postal_code = null;
        
        // Check if shipping data is provided (our condition)
        $has_shipping_data = $shipping_firstname || $shipping_lastname || $shipping_address1 || 
                           $shipping_city || $shipping_state || $shipping_country || $shipping_postal_code;
        
        $this->assertFalse($has_shipping_data, 'Should detect no shipping data is provided');
        
        // Our fix should NOT create a record when no shipping data is provided
        if (!$has_shipping_data) {
            // Do nothing - this is the correct behavior
        }
        
        // Verify no record was created
        $stmt = $this->conn->prepare('SELECT COUNT(*) FROM shipment_addresses WHERE donation_id = 2');
        $stmt->execute();
        $this->assertEquals(0, $stmt->fetchColumn(), 'Should not create shipping address when no data provided');
    }
    
    public function testShippingAddressUpdateWithExistingRecord()
    {
        // Test that existing shipping addresses are updated normally
        
        // Insert donation and existing shipping address
        $this->conn->exec('INSERT INTO donations (id, transaction_id) VALUES (3, "test789")');
        $insertResult = $this->conn->exec('
            INSERT INTO shipment_addresses (donation_id, firstname, lastname, address1, city, state, country, postal_code) 
            VALUES (3, "Old", "Name", "Old Address", "Old City", "CA", "US", "12345")
        ');
        $this->assertNotFalse($insertResult, 'Should successfully insert initial shipping address');
        
        // Verify existing record
        $stmt = $this->conn->prepare('SELECT COUNT(*) FROM shipment_addresses WHERE donation_id = 3');
        $stmt->execute();
        $this->assertEquals(1, $stmt->fetchColumn(), 'Should have existing shipping address');
        
        // Simulate our fix logic with existing record
        $donation_id = 3;
        $has_shipping_data = true; // Has shipping data to update
        
        if ($has_shipping_data) {
            $check_query = 'SELECT COUNT(*) FROM shipment_addresses WHERE donation_id = ?';
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->execute([$donation_id]);
            $existing_count = $check_stmt->fetchColumn();

            $this->assertEquals(1, $existing_count, 'Should find existing shipping address');

            if ($existing_count > 0) {
                // Record exists - normal UPDATE would happen here
                $update_query = '
                    UPDATE shipment_addresses 
                    SET firstname = ?, lastname = ?, address1 = ?, city = ? 
                    WHERE donation_id = ?
                ';
                $update_stmt = $this->conn->prepare($update_query);
                $result = $update_stmt->execute(['New', 'Name', 'New Address', 'New City', $donation_id]);
                
                $this->assertTrue($result, 'Should successfully update existing shipping address');
                
                // Verify update affected a row
                $this->assertEquals(1, $update_stmt->rowCount(), 'Should update exactly one row');
            }
        }
        
        // Verify the record was updated (not duplicated)
        $stmt = $this->conn->prepare('SELECT COUNT(*) FROM shipment_addresses WHERE donation_id = 3');
        $stmt->execute();
        $this->assertEquals(1, $stmt->fetchColumn(), 'Should still have exactly one shipping address');
        
        $stmt = $this->conn->prepare('SELECT * FROM shipment_addresses WHERE donation_id = 3');
        $stmt->execute();
        $address = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals('New', $address['firstname']);
        $this->assertEquals('New Address', $address['address1']);
        $this->assertEquals('New City', $address['city']);
    }
} 