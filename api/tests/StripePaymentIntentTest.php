<?php

use PHPUnit\Framework\TestCase;
use Kpfa\Tests\TestHelpers;

// Include required classes
require_once __DIR__ . "/../objects/payment.php";
require_once __DIR__ . "/TestHelpers.php";

/**
 * Test cases for Stripe Payment Intent parameter matching
 *
 * These tests specifically cover the setup_future_usage parameter
 * matching issue we encountered and fixed
 */
class StripePaymentIntentTest extends TestCase {
    use TestHelpers;

    protected function setUp(): void {
        $this->setupTestEnvironment();
    }
    
    /**
     * Test that payment intent parameters are correctly structured
     *
     * This tests the fix for the parameter mismatch error:
     * "The provided setup_future_usage (null) does not match the expected setup_future_usage (off_session)"
     */
    public function testPaymentIntentParameterStructure() {
        // Test the correct parameter structure for subscription payment intents
        $correctParams = [
            'amount' => 2500,
            'currency' => 'usd',
            'customer' => 'cus_test123',
            'setup_future_usage' => 'off_session', // Critical fix
            'metadata' => [
                'invoice_id' => 'in_test123',
                'subscription_id' => 'sub_test123',
                'transaction_id' => 'txn_test123',
                'donor_id' => 1,
            ],
            'payment_method_types' => ['card', 'us_bank_account'],
        ];

        // Verify all required parameters are present
        $this->assertArrayHasKey('setup_future_usage', $correctParams);
        $this->assertEquals('off_session', $correctParams['setup_future_usage']);

        // Verify metadata structure
        $this->assertArrayHasKey('metadata', $correctParams);
        $this->assertArrayHasKey('invoice_id', $correctParams['metadata']);
        $this->assertArrayHasKey('subscription_id', $correctParams['metadata']);
        $this->assertArrayHasKey('transaction_id', $correctParams['metadata']);
        $this->assertArrayHasKey('donor_id', $correctParams['metadata']);

        // Verify payment method types
        $this->assertArrayHasKey('payment_method_types', $correctParams);
        $this->assertContains('card', $correctParams['payment_method_types']);
        $this->assertContains('us_bank_account', $correctParams['payment_method_types']);

        $this->assertTrue(true, 'Payment intent parameters are correctly structured');
    }
    
    /**
     * Test the specific error message we encountered
     *
     * This documents the error we were getting before the fix
     */
    public function testParameterMismatchError() {
        $errorMessage = 'The provided setup_future_usage (null) does not match the expected setup_future_usage (off_session). Try confirming with a Payment Intent that is configured to use the same parameters as Stripe Elements.';

        // Verify the error message contains the key information
        $this->assertStringContainsString('setup_future_usage', $errorMessage);
        $this->assertStringContainsString('(null)', $errorMessage);
        $this->assertStringContainsString('(off_session)', $errorMessage);
        $this->assertStringContainsString('Stripe Elements', $errorMessage);

        // Test that our fix addresses this specific error
        $fixedParams = ['setup_future_usage' => 'off_session'];
        $this->assertEquals('off_session', $fixedParams['setup_future_usage']);
        $this->assertNotNull($fixedParams['setup_future_usage']);

        $this->assertTrue(true, 'Parameter mismatch error is properly documented and fixed');
    }
    
    /**
     * Test different setup_future_usage values
     */
    public function testSetupFutureUsageValues() {
        // Test valid values for setup_future_usage
        $validValues = ['on_session', 'off_session'];

        $this->assertContains('off_session', $validValues);
        $this->assertContains('on_session', $validValues);

        // Test that 'off_session' is required for subscriptions
        $subscriptionUsage = 'off_session';
        $oneTimeUsage = 'on_session'; // or null for one-time payments

        $this->assertEquals('off_session', $subscriptionUsage);
        $this->assertEquals('on_session', $oneTimeUsage);

        // Test that our fix uses the correct value
        $ourFix = 'off_session';
        $this->assertEquals('off_session', $ourFix);

        $this->assertTrue(true, 'setup_future_usage values are correctly understood');
    }
    
    protected function tearDown(): void {
        $this->cleanupTestEnvironment();
    }
}
