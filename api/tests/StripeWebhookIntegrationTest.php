<?php

use PHPUnit\Framework\TestCase;
use Kpfa\Tests\TestHelpers;
use Kpfa\Tests\TestPDO;

// Include required classes
require_once __DIR__ . "/../objects/payment.php";
require_once __DIR__ . "/../objects/donation.php";
require_once __DIR__ . "/../objects/subscription.php";
require_once __DIR__ . "/../objects/account.php"; // Contains Donor class
require_once __DIR__ . "/TestHelpers.php";
require_once __DIR__ . "/TestPDO.php";

/**
 * Integration tests for Stripe webhook endpoint
 */
class StripeWebhookIntegrationTest extends TestCase {
    use TestHelpers;
    
    private $conn;
    
    protected function setUp(): void {
        // Create test database connection
        $this->conn = new TestPDO("sqlite::memory:");
        $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create test tables
        $this->conn->exec("CREATE TABLE IF NOT EXISTS donors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            first_name TEXT,
            last_name TEXT,
            email TEXT,
            phone TEXT,
            address TEXT,
            city TEXT,
            state TEXT,
            zip TEXT,
            stripe_cus_id TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )");
        
        $this->conn->exec("CREATE TABLE IF NOT EXISTS subscriptions (
            id TEXT PRIMARY KEY,
            processor TEXT,
            customer_id TEXT,
            donor_id INTEGER,
            date_created DATETIME,
            plan_id TEXT,
            transaction_id TEXT,
            amount REAL,
            interval TEXT,
            active INTEGER DEFAULT 1,
            date_canceled DATETIME,
            FOREIGN KEY (donor_id) REFERENCES donors(id)
        )");
        
        $this->conn->exec("CREATE TABLE IF NOT EXISTS donations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donor_id INTEGER,
            amount REAL,
            status TEXT DEFAULT 'pending',
            processor TEXT,
            transaction_id TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donor_id) REFERENCES donors(id)
        )");
        
        $this->conn->exec("CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donation_id INTEGER,
            payment_id TEXT,
            amount REAL,
            status TEXT,
            processor TEXT,
            method TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donation_id) REFERENCES donations(id)
        )");
        
        $this->conn->exec("CREATE TABLE IF NOT EXISTS stripe_prices (
            id TEXT PRIMARY KEY,
            amount INTEGER,
            product_id TEXT
        )");
    }
    
    /**
     * Test customer.created webhook - Updates donor with Stripe customer ID
     */
    public function testCustomerCreatedWebhookUpdatesDatabase() {
        // Create test donor
        $donorId = $this->createTestDonor([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>'
        ]);
        
        // Simulate customer.created webhook processing
        $customerId = 'cus_webhook_test_123';
        $donorEmail = '<EMAIL>';
        
        // This simulates the actual webhook logic from api/stripe-webhook/create.php lines 16-36
        $stmt = $this->conn->prepare("UPDATE `donors`
         SET
             `stripe_cus_id` = :customer_id
         WHERE
            donors.email=:email AND `stripe_cus_id` is NULL;");
        
        $stmt->bindParam(':customer_id', $customerId);
        $stmt->bindParam(':email', $donorEmail);
        $stmt->execute();
        
        // Verify webhook updated the donor record
        $updatedDonor = $this->getDonorById($donorId);
        $this->assertEquals('cus_webhook_test_123', $updatedDonor['stripe_cus_id'], 
            'Webhook should update donor with Stripe customer ID');
        
        // Test idempotency - webhook should be safe to retry
        $stmt = $this->conn->prepare("UPDATE `donors`
         SET
             `stripe_cus_id` = :customer_id
         WHERE
            donors.id=:donor_id AND `stripe_cus_id` is NULL;");
        
        $stmt->bindParam(':customer_id', $customerId);
        $stmt->bindParam(':donor_id', $donorId);
        $stmt->execute();
        
        $retryDonor = $this->getDonorById($donorId);
        $this->assertEquals('cus_webhook_test_123', $retryDonor['stripe_cus_id'], 
            'Webhook should be idempotent - no change on retry due to NULL condition');
    }
    
    /**
     * Test customer.subscription.created webhook - Creates subscription record
     */
    public function testSubscriptionCreatedWebhookCreatesRecord() {
        // Create test donor
        $donorId = $this->createTestDonor([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'stripe_cus_id' => 'cus_test_subscription'
        ]);
        
        // Simulate subscription.created webhook data
        $subscriptionData = [
            'id' => 'sub_webhook_test_456',
            'customer' => 'cus_test_subscription',
            'items' => [
                'data' => [
                    [
                        'price' => [
                            'id' => 'price_monthly_25',
                            'unit_amount' => 2500,
                            'recurring' => ['interval' => 'month']
                        ]
                    ]
                ]
            ],
            'created' => time()
        ];
        
        // This simulates the webhook processing logic from lines 205-246
        $data = [
            ':processor' => 'stripe',
            ':id' => $subscriptionData['id'],
            ':customer_id' => $subscriptionData['customer'],
            ':donor_id' => $donorId,
            ':date_created' => $subscriptionData['created'],
            ':plan_id' => $subscriptionData['items']['data'][0]['price']['id'],
            ':transaction_id' => $subscriptionData['id'],
            ':amount' => $subscriptionData['items']['data'][0]['price']['unit_amount'] / 100,
            ':interval' => $subscriptionData['items']['data'][0]['price']['recurring']['interval'],
            ':active' => 1
        ];
        
        $stmt = $this->conn->prepare("INSERT OR IGNORE INTO `subscriptions`
                            (`processor`, `id`, `customer_id`, `donor_id`, `date_created`, `plan_id`,`transaction_id`, `amount`, `interval`, `active`)
                            VALUES
                            (:processor, :id, :customer_id, :donor_id, datetime(:date_created, 'unixepoch'), :plan_id, :transaction_id, :amount, :interval, :active)");
        $stmt->execute($data);
        
        // Verify subscription was created
        $subscription = $this->getSubscriptionById($subscriptionData['id']);
        $this->assertNotNull($subscription, 'Subscription should be created');
        $this->assertEquals('stripe', $subscription['processor']);
        $this->assertEquals($donorId, $subscription['donor_id']);
        $this->assertEquals(25.00, $subscription['amount']);
        $this->assertEquals('month', $subscription['interval']);
        $this->assertEquals(1, $subscription['active']);
        
        // Test idempotency with INSERT IGNORE
        $stmt = $this->conn->prepare("INSERT OR IGNORE INTO `subscriptions`
                            (`processor`, `id`, `customer_id`, `donor_id`, `date_created`, `plan_id`,`transaction_id`, `amount`, `interval`, `active`)
                            VALUES
                            (:processor, :id, :customer_id, :donor_id, datetime(:date_created, 'unixepoch'), :plan_id, :transaction_id, :amount, :interval, :active)");
        $stmt->execute($data);
        
        // Verify only one subscription exists
        $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM subscriptions WHERE id = ?");
        $stmt->execute([$subscriptionData['id']]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        $this->assertEquals(1, $count['count'], 'INSERT IGNORE should prevent duplicates');
    }
    
    /**
     * Test subscription.updated with incomplete_expired status
     */
    public function testSubscriptionUpdatedIncompleteExpiredCleansUp() {
        // Create test subscription
        $subscriptionId = 'sub_incomplete_test_789';
        $donorId = $this->createTestDonor([
            'first_name' => 'Bob',
            'last_name' => 'Wilson',
            'email' => '<EMAIL>'
        ]);
        
        // Create subscription record
        $stmt = $this->conn->prepare("INSERT INTO subscriptions
            (id, processor, customer_id, donor_id, date_created, plan_id, transaction_id, amount, interval, active)
            VALUES (?, 'stripe', 'cus_test', ?, ?, 'price_test', ?, 15.00, 'month', 1)");
        $stmt->execute([$subscriptionId, $donorId, date('Y-m-d H:i:s'), $subscriptionId]);
        
        // Simulate subscription.updated webhook with incomplete_expired status
        // This tests the cleanup logic from lines 296-317
        $stmt = $this->conn->prepare("UPDATE `subscriptions` SET `active`=0, `date_canceled`=datetime('now') WHERE `id`=:id");
        $stmt->bindParam(':id', $subscriptionId);
        $stmt->execute();
        
        // Verify subscription was deactivated
        $subscription = $this->getSubscriptionById($subscriptionId);
        $this->assertEquals(0, $subscription['active'], 'Incomplete expired subscription should be deactivated');
        $this->assertNotNull($subscription['date_canceled'], 'Cancellation date should be set');
    }

    // Helper methods for database operations
    private function createTestDonor($data) {
        $stmt = $this->conn->prepare("INSERT INTO donors (first_name, last_name, email, phone, address, city, state, zip, stripe_cus_id)
                                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $data['first_name'] ?? null,
            $data['last_name'] ?? null,
            $data['email'] ?? null,
            $data['phone'] ?? null,
            $data['address'] ?? null,
            $data['city'] ?? null,
            $data['state'] ?? null,
            $data['zip'] ?? null,
            $data['stripe_cus_id'] ?? null
        ]);
        return $this->conn->lastInsertId();
    }

    private function getDonorById($id) {
        $stmt = $this->conn->prepare("SELECT * FROM donors WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    private function getSubscriptionById($id) {
        $stmt = $this->conn->prepare("SELECT * FROM subscriptions WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    private function getDonationById($id) {
        $stmt = $this->conn->prepare("SELECT * FROM donations WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    private function getPaymentByTransactionId($transactionId) {
        $stmt = $this->conn->prepare("SELECT * FROM payments WHERE payment_id = ?");
        $stmt->execute([$transactionId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Test price.created webhook - Creates pricing records
     */
    public function testPriceCreatedWebhookCreatesRecord() {
        $priceId = 'price_webhook_test_123';
        $priceData = [
            'id' => $priceId,
            'unit_amount' => 3000,
            'product' => 'prod_monthly_donation'
        ];

        // This simulates the webhook processing logic from lines 699-723
        $stmt = $this->conn->prepare("INSERT OR IGNORE INTO `stripe_prices` (`id`, `amount`, `product_id`) VALUES (:id, :amount, :product_id)");
        $data = [
            ':id' => $priceData['id'],
            ':amount' => $priceData['unit_amount'],
            ':product_id' => $priceData['product']
        ];
        $stmt->execute($data);

        // Verify price was created
        $price = $this->getPriceById($priceId);
        $this->assertNotNull($price, 'Price should be created');
        $this->assertEquals($priceId, $price['id']);
        $this->assertEquals(3000, $price['amount']);
        $this->assertEquals('prod_monthly_donation', $price['product_id']);
    }

    /**
     * Test price.deleted webhook - Removes pricing records
     */
    public function testPriceDeletedWebhookRemovesRecord() {
        $priceId = 'price_delete_test_456';

        // Create price record first
        $stmt = $this->conn->prepare("INSERT INTO stripe_prices (id, amount, product_id) VALUES (?, ?, ?)");
        $stmt->execute([$priceId, 2500, 'prod_test']);

        // Verify price exists
        $price = $this->getPriceById($priceId);
        $this->assertNotNull($price, 'Price should exist before deletion');

        // Simulate price.deleted webhook processing
        // This tests the deletion logic from lines 726-744
        $stmt = $this->conn->prepare("DELETE FROM stripe_prices WHERE id = :id");
        $stmt->bindParam(':id', $priceId);
        $stmt->execute();

        // Verify price was deleted
        $deletedPrice = $this->getPriceById($priceId);
        $this->assertNull($deletedPrice, 'Price should be deleted');
    }

    /**
     * Test customer.subscription.deleted webhook - Deactivates subscriptions
     */
    public function testSubscriptionDeletedWebhookDeactivatesSubscription() {
        // Create test subscription
        $subscriptionId = 'sub_delete_test_789';
        $donorId = $this->createTestDonor([
            'first_name' => 'Alice',
            'last_name' => 'Johnson',
            'email' => '<EMAIL>'
        ]);

        // Create active subscription
        $stmt = $this->conn->prepare("INSERT INTO subscriptions
            (id, processor, customer_id, donor_id, date_created, plan_id, transaction_id, amount, interval, active)
            VALUES (?, 'stripe', 'cus_test_delete', ?, ?, 'price_test', ?, 20.00, 'month', 1)");
        $stmt->execute([$subscriptionId, $donorId, date('Y-m-d H:i:s'), $subscriptionId]);

        // Verify subscription is active
        $subscription = $this->getSubscriptionById($subscriptionId);
        $this->assertEquals(1, $subscription['active'], 'Subscription should be active initially');

        // Simulate subscription.deleted webhook processing
        // This tests the deletion logic from lines 251-276
        $stmt = $this->conn->prepare("UPDATE `subscriptions` SET `active`=0, `date_canceled`=datetime('now') WHERE `id`=:id");
        $stmt->bindParam(':id', $subscriptionId);
        $stmt->execute();

        // Verify subscription was deactivated
        $canceledSubscription = $this->getSubscriptionById($subscriptionId);
        $this->assertEquals(0, $canceledSubscription['active'], 'Subscription should be deactivated');
        $this->assertNotNull($canceledSubscription['date_canceled'], 'Cancellation date should be set');
    }

    /**
     * Test charge.succeeded webhook - Processes successful one-time payments
     */
    public function testChargeSucceededWebhookProcessesPayment() {
        // Create test donation
        $donorId = $this->createTestDonor([
            'first_name' => 'Charlie',
            'last_name' => 'Brown',
            'email' => '<EMAIL>'
        ]);

        $donationId = $this->createTestDonation($donorId, 50.00, 'pending');

        // Simulate charge.succeeded webhook data
        $chargeData = [
            'id' => 'ch_webhook_test_success',
            'amount' => 5000, // $50.00 in cents
            'status' => 'succeeded'
        ];

        // This simulates the payment processing logic
        $stmt = $this->conn->prepare("INSERT OR IGNORE INTO payments
            (donation_id, payment_id, amount, status, processor)
            VALUES (?, ?, ?, ?, 'stripe')");
        $stmt->execute([
            $donationId,
            $chargeData['id'],
            $chargeData['amount'] / 100,
            $chargeData['status']
        ]);

        // Update donation status
        $stmt = $this->conn->prepare("UPDATE donations SET status = 'completed' WHERE id = ?");
        $stmt->execute([$donationId]);

        // Verify payment was recorded
        $payment = $this->getPaymentByTransactionId($chargeData['id']);
        $this->assertNotNull($payment, 'Payment should be recorded');
        $this->assertEquals($donationId, $payment['donation_id']);
        $this->assertEquals(50.00, $payment['amount']);
        $this->assertEquals('succeeded', $payment['status']);

        // Verify donation status was updated
        $donation = $this->getDonationById($donationId);
        $this->assertEquals('completed', $donation['status'], 'Donation status should be updated');
    }

    /**
     * Test invoice.payment_succeeded webhook - Processes subscription payments
     */
    public function testInvoicePaymentSucceededWebhookProcessesSubscriptionPayment() {
        // Create test subscription payment scenario
        $donorId = $this->createTestDonor([
            'first_name' => 'Diana',
            'last_name' => 'Prince',
            'email' => '<EMAIL>'
        ]);

        $subscriptionId = 'sub_invoice_test_999';
        $donationId = $this->createTestDonation($donorId, 30.00, 'pending');

        // Create subscription record
        $stmt = $this->conn->prepare("INSERT INTO subscriptions
            (id, processor, customer_id, donor_id, date_created, plan_id, transaction_id, amount, interval, active)
            VALUES (?, 'stripe', 'cus_invoice_test', ?, ?, 'price_monthly_30', ?, 30.00, 'month', 1)");
        $stmt->execute([$subscriptionId, $donorId, date('Y-m-d H:i:s'), $subscriptionId]);

        // Simulate invoice.payment_succeeded webhook data
        $invoiceData = [
            'id' => 'in_webhook_test_success',
            'subscription' => $subscriptionId,
            'amount_paid' => 3000, // $30.00 in cents
            'status' => 'paid'
        ];

        // This simulates the subscription payment processing logic
        $stmt = $this->conn->prepare("INSERT OR IGNORE INTO payments
            (donation_id, payment_id, amount, status, processor)
            VALUES (?, ?, ?, ?, 'stripe')");
        $stmt->execute([
            $donationId,
            $invoiceData['id'],
            $invoiceData['amount_paid'] / 100,
            'succeeded'
        ]);

        // Update donation status
        $stmt = $this->conn->prepare("UPDATE donations SET status = 'completed' WHERE id = ?");
        $stmt->execute([$donationId]);

        // Verify subscription payment was recorded
        $payment = $this->getPaymentByTransactionId($invoiceData['id']);
        $this->assertNotNull($payment, 'Subscription payment should be recorded');
        $this->assertEquals($donationId, $payment['donation_id']);
        $this->assertEquals(30.00, $payment['amount']);
        $this->assertEquals('succeeded', $payment['status']);

        // Verify donation status was updated
        $donation = $this->getDonationById($donationId);
        $this->assertEquals('completed', $donation['status'], 'Subscription donation status should be updated');
    }

    private function createTestDonation($donorId, $amount, $status = 'pending') {
        $stmt = $this->conn->prepare("INSERT INTO donations (donor_id, amount, status, processor) VALUES (?, ?, ?, 'stripe')");
        $stmt->execute([$donorId, $amount, $status]);
        return $this->conn->lastInsertId();
    }

    private function getPriceById($id) {
        $stmt = $this->conn->prepare("SELECT * FROM stripe_prices WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Test customer.subscription.updated webhook - Activates incomplete subscription
     */
    public function testSubscriptionUpdatedWebhookActivatesIncompleteSubscription() {
        // Create test subscription in incomplete state (active = 0)
        $this->conn->exec("INSERT INTO subscriptions (processor, id, customer_id, donor_id, date_created, plan_id, transaction_id, amount, `interval`, active)
                         VALUES ('Stripe', 'sub_activate123', 'cus_test123', 12345, '2025-01-01 10:00:00', 'price_test123', 'txn_activate123', 5000, 'month', 0)");

        // Simulate the webhook logic from api/stripe-webhook/create.php lines 318-336
        // This simulates when subscription status changes from incomplete to active
        $subscriptionId = 'sub_activate123';

        // Execute the subscription activation logic
        $stmt = $this->conn->prepare("UPDATE `subscriptions`
                                    SET `active` = '1'
                                    WHERE `id` = :subscription_id");
        $stmt->execute(['subscription_id' => $subscriptionId]);

        // Verify subscription was activated in database
        $stmt = $this->conn->prepare("SELECT active FROM subscriptions WHERE id = ?");
        $stmt->execute(['sub_activate123']);
        $subscription = $stmt->fetch(PDO::FETCH_ASSOC);

        $this->assertEquals('1', $subscription['active'],
            'Subscription should be activated when status changes to active');

        // Test idempotency - webhook should be safe to retry
        $stmt = $this->conn->prepare("UPDATE `subscriptions`
                                    SET `active` = '1'
                                    WHERE `id` = :subscription_id");
        $stmt->execute(['subscription_id' => $subscriptionId]);

        $retrySubscription = $this->getSubscriptionById($subscriptionId);
        $this->assertEquals('1', $retrySubscription['active'],
            'Subscription activation should be idempotent');
    }
}
