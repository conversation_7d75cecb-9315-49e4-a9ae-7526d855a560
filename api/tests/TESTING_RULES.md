# Testing Rules and Guidelines

1. **DO NOT MODIFY API CODE**
   - Never modify the actual API implementation
   - All changes must be confined to test files
   - If API issues are found, document them but don't fix them

2. **Test Environment Setup**
   - All test dependencies must be properly mocked
   - Use SQLite for testing database
   - Set up proper test data in setUp()
   - Clean up test data in tearDown()

3. **Test Data Management**
   - Create fresh test data for each test
   - Don't rely on existing data
   - Clean up after each test
   - Use meaningful test data names (e.g., "Test User", "Test Campaign")

4. **Mocking External Services**
   - Mock all external API calls (Stripe, WordPress, etc.)
   - Mock file system operations when needed
   - Mock HTTP requests
   - Document all mocked services

5. **Test Structure**
   - One assertion per test when possible
   - Clear test names that describe what is being tested
   - Use descriptive assertion messages
   - Group related tests in test classes

6. **Error Handling**
   - Test both success and failure cases
   - Verify error messages and codes
   - Test edge cases
   - Test input validation
   - Handle API error responses appropriately in test environment
   - Capture and verify HTTP response codes in test context
   - Don't let API error handling (http_response_code, etc.) break tests

7. **Database Testing**
   - Use transactions when possible
   - Clean up database after tests
   - Don't leave test data in production tables
   - Use separate test database/schema
   - Verify database state after operations
   - Check both success and error conditions

8. **Documentation**
   - Document test prerequisites
   - Document any special setup needed
   - Document known limitations
   - Document workarounds for API issues
   - Document any assumptions about API behavior
   - Note any special handling needed for API responses

9. **Debugging**
   - Use proper logging in tests
   - Add descriptive error messages
   - Include relevant data in failure messages
   - Log SQL queries when needed
   - Capture and log API responses for debugging
   - Use LoggingPDOStatement for database debugging

10. **Maintenance**
    - Keep tests up to date
    - Remove obsolete tests
    - Update test data as needed
    - Document test modifications
    - Update tests when API behavior changes
    - Document any workarounds used in tests

11. **API Response Handling**
    - Mock or capture HTTP response codes
    - Handle API error responses gracefully
    - Don't let API error handling break tests
    - Test both success and error responses
    - Verify response format and content
    - Document expected API responses 