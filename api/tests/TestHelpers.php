<?php

namespace Kpfa\Tests;

trait TestHelpers {
    private $output_buffer = '';
    private $http_response_code = 200;
    private $stmt_error_info = null;
    private $initial_ob_level;

    protected function startOutputBuffering() {
        $this->initial_ob_level = ob_get_level();
        ob_start();
    }

    protected function getOutputAndClean() {
        $output = ob_get_clean();
        $this->output_buffer = $output;
        return $output;
    }

    protected function mockHttpResponse() {
        // Override the http_response_code function in the current namespace
        if (!function_exists(__NAMESPACE__ . '\http_response_code')) {
            function http_response_code($code = null) {
                global $http_response_code;
                if ($code !== null) {
                    $http_response_code = $code;
                }
                return $http_response_code;
            }
        }
    }

    protected function getLastHttpResponseCode() {
        global $http_response_code;
        return $http_response_code;
    }

    protected function getLastJsonResponse() {
        return json_decode($this->output_buffer, true);
    }

    protected function setupTestEnvironment() {
        // Start output buffering
        $this->startOutputBuffering();
        
        // Mock HTTP response code function
        $this->mockHttpResponse();
        
        // Set up error handling
        set_error_handler(function($errno, $errstr, $errfile, $errline) {
            if (strpos($errstr, 'errorInfo') !== false) {
                return true; // Suppress errorInfo errors
            }
            return false; // Let PHP handle other errors
        });
    }

    protected function cleanupTestEnvironment() {
        // Clean up output buffering
        while (ob_get_level() > $this->initial_ob_level) {
            ob_end_clean();
        }
        
        // Restore error handler
        restore_error_handler();
    }
} 