<?php

namespace Kpfa\Tests;

class TestPDO extends \PDO {
    private $lastStmt = null;
    private $lastError = null;
    private static $enableLogging = false;
    private static $queryCount = 0;
    private static $startTime = null;

    // ANSI color codes
    private const COLORS = [
        'green' => "\033[32m",
        'red' => "\033[31m",
        'yellow' => "\033[33m",
        'blue' => "\033[34m",
        'magenta' => "\033[35m",
        'cyan' => "\033[36m",
        'reset' => "\033[0m",
        'bold' => "\033[1m"
    ];

    // Unicode symbols
    private const SYMBOLS = [
        'success' => '✓',
        'error' => '✗',
        'warning' => '⚠',
        'info' => 'ℹ',
        'query' => '⚡',
        'progress' => '▰'
    ];

    public static function enableLogging($enable = true) {
        self::$enableLogging = false; // Disabled for cleaner test output
        if ($enable) {
            self::$startTime = microtime(true);
            self::$queryCount = 0;
            echo self::COLORS['bold'] . "\n🚀 Starting Test Session\n" . self::COLORS['reset'];
            echo str_repeat('─', 50) . "\n\n";
        }
    }

    private function log($message) {
        if (self::$enableLogging) {
            self::$queryCount++;
            $elapsed = microtime(true) - self::$startTime;
            
            // Add progress bar
            $progress = str_repeat(self::SYMBOLS['progress'], min(20, floor($elapsed * 2)));
            $progress = str_pad($progress, 20, '▱');
            
            // Get caller info
            $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
            $caller = $trace[1] ?? null;
            $file = $caller ? basename($caller['file']) : 'unknown';
            $line = $caller ? $caller['line'] : 0;
            
            // Format the message with colors and symbols
            $formattedMessage = sprintf(
                "%s %s %s %s %s %s\n",
                self::COLORS['cyan'] . self::SYMBOLS['query'] . self::COLORS['reset'],
                self::COLORS['yellow'] . sprintf("[%03d]", self::$queryCount) . self::COLORS['reset'],
                self::COLORS['magenta'] . sprintf("[%.2fs]", $elapsed) . self::COLORS['reset'],
                $progress,
                self::COLORS['blue'] . sprintf("[%s:%d]", $file, $line) . self::COLORS['reset'],
                $message
            );
            
            echo $formattedMessage;
        }
    }

    private function logError($message) {
        if (self::$enableLogging) {
            echo sprintf(
                "%s %s %s %s\n",
                self::COLORS['red'] . self::SYMBOLS['error'] . self::COLORS['reset'],
                self::COLORS['red'] . "ERROR:" . self::COLORS['reset'],
                $message,
                self::COLORS['reset']
            );
        }
    }

    private function logSuccess($message) {
        if (self::$enableLogging) {
            echo sprintf(
                "%s %s %s %s\n",
                self::COLORS['green'] . self::SYMBOLS['success'] . self::COLORS['reset'],
                self::COLORS['green'] . "SUCCESS:" . self::COLORS['reset'],
                $message,
                self::COLORS['reset']
            );
        }
    }

    public function __destruct() {
        if (self::$enableLogging && self::$startTime) {
            $elapsed = microtime(true) - self::$startTime;
            echo "\n" . str_repeat('─', 50) . "\n";
            echo sprintf(
                "%s %s %s %s %s\n",
                self::COLORS['bold'] . "🏁 Test Session Complete" . self::COLORS['reset'],
                self::COLORS['cyan'] . sprintf("Queries: %d", self::$queryCount) . self::COLORS['reset'],
                self::COLORS['yellow'] . sprintf("Time: %.2fs", $elapsed) . self::COLORS['reset'],
                self::COLORS['green'] . sprintf("Avg: %.2fms/query", ($elapsed / self::$queryCount) * 1000) . self::COLORS['reset'],
                self::COLORS['reset']
            );
        }
    }

    /**
     * Converts MySQL INSERT ... SET syntax to SQLite INSERT INTO ... VALUES syntax
     */
    private function convertMySQLToSQLite(string $query): string {
        // Convert MySQL backtick quotes to SQLite double quotes, including table prefixes
        $query = preg_replace('/`([^`\.]+)\.`([^`]+)`/', '"$1"."$2"', $query);  // Handle table.column
        $query = preg_replace('/`([^`]+)`/', '"$1"', $query);  // Handle remaining backticks
        
        // Handle string concatenation in column names
        $query = preg_replace('/"([^"]+)"\s*\.\s*"([^"]+)"/', '"$1$2"', $query);
        
        // Convert MySQL NOW() to SQLite datetime('now')
        $query = str_replace('NOW()', "datetime('now')", $query);
        
        // Convert MySQL DATE_SUB to SQLite datetime
        if (preg_match('/DATE_SUB\(([^,]+),\s*INTERVAL\s*\'?(\d+)\'?\s*HOUR\)/i', $query, $matches)) {
            $date = trim($matches[1]);
            $hours = $matches[2];
            $query = str_replace(
                $matches[0],
                "datetime($date, '-$hours hours')",
                $query
            );
        }
        
        // Handle INSERT ... SET syntax
        if (preg_match('/INSERT\s+INTO\s+([^\s]+)\s+SET\s+([^;]+)/is', $query, $matches)) {
            $table = trim($matches[1]);
            $setClause = trim($matches[2]);
            
            // Parse the SET clause to get column names and placeholders
            $pairs = explode(',', $setClause);
            $columns = [];
            $values = [];
            
            foreach ($pairs as $pair) {
                $pair = trim($pair);
                if (empty($pair)) continue;
                
                // Match column=value pattern, handling both quoted and unquoted column names
                if (preg_match('/^\s*"?([^"=\s]+)"?\s*=\s*([^,]+)/i', $pair, $parts)) {
                    $column = trim($parts[1]);  // Column name without quotes
                    $value = trim($parts[2]);  // Placeholder
                    
                    // Skip empty columns
                    if (empty($column)) continue;
                    
                    $columns[] = $column;
                    $values[] = $value;
                }
            }
            
            // Debug: Print columns and values (disabled for cleaner output)
            // if (self::$enableLogging) {
            //     echo "\nColumns:\n";
            //     print_r($columns);
            //     echo "\nValues:\n";
            //     print_r($values);
            // }
            
            // Construct the new SQLite-compatible query
            $columnsStr = implode(', ', $columns);
            $valuesStr = implode(', ', $values);
            
            $sqliteQuery = "INSERT INTO $table ($columnsStr) VALUES ($valuesStr)";
            
            // Debug: Print final query (disabled for cleaner output)
            // $this->log("\nConverted query: " . $sqliteQuery . "\n");
            
            return $sqliteQuery;
        }

        // Handle INSERT INTO ... VALUES syntax with backticks
        if (preg_match('/INSERT\s+INTO\s+([^\s]+)\s*\(([^)]+)\)\s*VALUES\s*\(([^)]+)\)/is', $query, $matches)) {
            $table = trim($matches[1]);
            $columns = array_map('trim', explode(',', $matches[2]));
            $values = array_map('trim', explode(',', $matches[3]));
            
            // Remove backticks from column names
            $columns = array_map(function($col) {
                return preg_replace('/`([^`]+)`/', '"$1"', $col);
            }, $columns);
            
            // Construct the new SQLite-compatible query
            $columnsStr = implode(', ', $columns);
            $valuesStr = implode(', ', $values);
            
            $sqliteQuery = "INSERT INTO $table ($columnsStr) VALUES ($valuesStr)";
            
            // Debug: Print final query (disabled for cleaner output)
            // $this->log("\nConverted query: " . $sqliteQuery . "\n");
            
            return $sqliteQuery;
        }
        
        return $query;
    }

    #[\ReturnTypeWillChange]
    public function prepare(string $query, array $options = []): \PDOStatement|false {
        try {
            // Log the original query (disabled for cleaner output)
            // $this->log("");

            // Convert MySQL backtick quotes to SQLite double quotes
            $query = preg_replace('/`([^`]+)`/', '"$1"', $query);

            // Convert MySQL NOW() to SQLite datetime('now')
            $query = str_replace('NOW()', "datetime('now')", $query);

            // Log the converted query
            $this->log("");

            // Log the prepare call
            $this->log("");

            // Convert MySQL syntax to SQLite if needed
            $sqliteQuery = $this->convertMySQLToSQLite($query);
            
            // Debug: Print query
            $this->log("");
            
            $stmt = parent::prepare($sqliteQuery, $options);
            if ($stmt === false) {
                // Debug: Print error info
                if (self::$enableLogging) {
                    $this->logError("PDO Error Info:\n" . print_r($this->errorInfo(), true));
                    if ($this->lastError) {
                        $this->logError($this->lastError->getMessage());
                    }
                }
                return false;
            }
            $this->lastStmt = $stmt;
            $this->lastError = null;
            $this->logSuccess("Query prepared successfully");
            return $stmt;
        } catch (\PDOException $e) {
            // Debug: Print exception
            if (self::$enableLogging) {
                $this->logError("PDO Exception: " . $e->getMessage());
                $this->logError("Query was: " . $query);
            }
            $this->lastError = $e;
            return false;
        }
    }

    public function lastStmt(): ?\PDOStatement {
        return $this->lastStmt;
    }

    #[\ReturnTypeWillChange]
    public function errorInfo(): array {
        if ($this->lastError) {
            return [
                $this->lastError->getCode(),
                null,
                $this->lastError->getMessage()
            ];
        }
        if ($this->lastStmt) {
            return $this->lastStmt->errorInfo();
        }
        return [null, null, null];
    }

    #[\ReturnTypeWillChange]
    public function exec(string $query): int|false {
        try {
            // Convert MySQL syntax to SQLite if needed
            $sqliteQuery = $this->convertMySQLToSQLite($query);
            
            $result = parent::exec($sqliteQuery);
            if ($result === false) {
                // Debug: Print error info
                if (self::$enableLogging) {
                    $this->logError("PDO Error Info:\n" . print_r($this->errorInfo(), true));
                    if ($this->lastError) {
                        $this->logError($this->lastError->getMessage());
                    }
                }
            } else {
                $this->logSuccess("Query executed successfully");
            }
            return $result;
        } catch (\PDOException $e) {
            // Debug: Print exception
            if (self::$enableLogging) {
                $this->logError("PDO Exception: " . $e->getMessage());
                $this->logError("Query was: " . $query);
            }
            $this->lastError = $e;
            return false;
        }
    }
} 