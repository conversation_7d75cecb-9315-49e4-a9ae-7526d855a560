<?php

// Ensure the SERVER_NAME is globally set for all tests
if (!isset($_SERVER['SERVER_NAME'])) {
    $_SERVER['SERVER_NAME'] = 'localhost';
}

// Load composer autoloader first
require_once __DIR__ . '/../vendor/autoload.php';

// Set up test environment variables
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'PHPUnit Test Browser';

// Create a PDO wrapper that translates MySQL SET syntax to SQLite syntax
class TestPDO extends PDO {
    #[\ReturnTypeWillChange]
    public function prepare(string $query, array $options = []): PDOStatement|false {
        // Convert MySQL date functions to SQLite
        $query = preg_replace(
            '/DATE_SUB\(NOW\(\), INTERVAL \'(\d+)\' HOUR\)/',
            "datetime('now', '-$1 hours')",
            $query
        );
        $query = str_replace('NOW()', "datetime('now')", $query);
        
        // Convert MySQL's "INSERT INTO ... SET" to SQLite's "INSERT INTO ... (cols) VALUES (vals)"
        if (preg_match('/INSERT\s+INTO\s+([^\s]+)\s+SET\s+(.+)/is', $query, $matches)) {
            $table = trim($matches[1]);
            $sets = $matches[2];
            
            // Parse the SET clause
            $pairs = [];
            $current = '';
            $inQuote = false;
            $quoteChar = '';
            $depth = 0;
            
            for ($i = 0; $i < strlen($sets); $i++) {
                $char = $sets[$i];
                
                if ($char === '(' && !$inQuote) {
                    $depth++;
                } elseif ($char === ')' && !$inQuote) {
                    $depth--;
                } elseif (($char === '"' || $char === "'") && ($i === 0 || $sets[$i-1] !== '\\')) {
                    if (!$inQuote) {
                        $inQuote = true;
                        $quoteChar = $char;
                    } elseif ($char === $quoteChar) {
                        $inQuote = false;
                    }
                }
                
                if ($char === ',' && !$inQuote && $depth === 0) {
                    $pairs[] = trim($current);
                    $current = '';
                } else {
                    $current .= $char;
                }
            }
            if ($current !== '') {
                $pairs[] = trim($current);
            }
            
            $columns = [];
            $values = [];
            foreach ($pairs as $pair) {
                if (preg_match('/^([^=]+)=(.+)$/s', trim($pair), $matches)) {
                    $columns[] = trim($matches[1]);
                    $values[] = trim($matches[2]);
                }
            }
            
            // Rebuild the query in SQLite format
            $query = sprintf(
                "INSERT INTO %s (%s) VALUES (%s)",
                $table,
                implode(', ', $columns),
                implode(', ', $values)
            );
        }
        
        return parent::prepare($query, $options);
    }
}

// Create PDO connection for testing
function getTestDatabase() {
    $pdo = new TestPDO('sqlite::memory:');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create test tables
    $pdo->exec('
        CREATE TABLE IF NOT EXISTS donors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            firstname VARCHAR(255),
            lastname VARCHAR(255),
            partner_firstname VARCHAR(255),
            partner_lastname VARCHAR(255),
            email VARCHAR(255),
            phone VARCHAR(255),
            type VARCHAR(255),
            address1 VARCHAR(255),
            address2 VARCHAR(255),
            city VARCHAR(255),
            state VARCHAR(255),
            postal_code VARCHAR(255),
            country VARCHAR(255),
            notes TEXT,
            membership_level VARCHAR(255),
            donotsolicit BOOLEAN,
            deceased BOOLEAN,
            stripe_cus_id VARCHAR(255),
            paypal_user_id VARCHAR(255),
            memsys_id VARCHAR(255),
            allegiance_id VARCHAR(255),
            paperless BOOLEAN,
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ');

    $pdo->exec('
        CREATE TABLE IF NOT EXISTS campaigns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(255),
            description TEXT,
            start DATETIME,
            end DATETIME,
            goal DECIMAL(10,2),
            active BOOLEAN DEFAULT 1,
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ');

    $pdo->exec('
        CREATE TABLE IF NOT EXISTS donations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donor_id INTEGER,
            campaign_id INTEGER,
            account_id INTEGER,
            type VARCHAR(255),
            amount DECIMAL(10,2),
            method VARCHAR(255),
            status VARCHAR(255),
            installment VARCHAR(255),
            comments TEXT,
            add_me BOOLEAN,
            read_onair BOOLEAN,
            transaction_id VARCHAR(255),
            ipaddress VARCHAR(255),
            show_name VARCHAR(255),
            program_wp_id INTEGER,
            source VARCHAR(255),
            browser TEXT,
            donation_match BOOLEAN,
            premiums_cart TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donor_id) REFERENCES donors(id),
            FOREIGN KEY (campaign_id) REFERENCES campaigns(id)
        )
    ');

    $pdo->exec('
        CREATE TABLE IF NOT EXISTS premiums (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(255),
            description TEXT,
            category VARCHAR(255),
            active BOOLEAN DEFAULT 1,
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ');

    $pdo->exec('
        CREATE TABLE IF NOT EXISTS shipments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donation_id INTEGER,
            premium_id INTEGER,
            status VARCHAR(255),
            tracking_number VARCHAR(255),
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donation_id) REFERENCES donations(id),
            FOREIGN KEY (premium_id) REFERENCES premiums(id)
        )
    ');

    $pdo->exec('
        CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donation_id INTEGER,
            customer_id VARCHAR(255),
            payment_id VARCHAR(255),
            amount DECIMAL(10,2),
            amount_refunded DECIMAL(10,2),
            method VARCHAR(255),
            processor VARCHAR(255),
            fingerprint VARCHAR(255),
            card_type VARCHAR(255),
            last4 VARCHAR(4),
            brand VARCHAR(255),
            exp_month INTEGER,
            exp_year INTEGER,
            last_updated_by VARCHAR(255),
            date_deposited DATETIME,
            status VARCHAR(255),
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donation_id) REFERENCES donations(id)
        )
    ');

    $pdo->exec('
        CREATE TABLE IF NOT EXISTS emails (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donation_id INTEGER,
            type VARCHAR(255),
            status VARCHAR(255),
            sent_at DATETIME,
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donation_id) REFERENCES donations(id)
        )
    ');

    $pdo->exec('
        CREATE TABLE IF NOT EXISTS premium_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(255),
            description TEXT,
            active BOOLEAN DEFAULT 1,
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ');

    $pdo->exec('
        CREATE TABLE IF NOT EXISTS subscriptions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donor_id INTEGER,
            donation_id INTEGER,
            status VARCHAR(255),
            subscription_id VARCHAR(255),
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donor_id) REFERENCES donors(id),
            FOREIGN KEY (donation_id) REFERENCES donations(id)
        )
    ');

    $pdo->exec('
        CREATE TABLE IF NOT EXISTS shipment_addresses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            donation_id INTEGER,
            firstname VARCHAR(255),
            lastname VARCHAR(255),
            address1 VARCHAR(255),
            address2 VARCHAR(255),
            city VARCHAR(255),
            state VARCHAR(255),
            postal_code VARCHAR(255),
            country VARCHAR(255),
            date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (donation_id) REFERENCES donations(id)
        )
    ');

    return $pdo;
}

// Set up the global test database connection
$GLOBALS['testDb'] = getTestDatabase();

