<?php
/*
 * Allows Authenticated Admins to create users
 */
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    // check fistname
    if (!empty($data->firstname)) {
        $user->firstname = $data->firstname;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[firstname] is required.");
    }

    // check lastname
    if (!empty($data->lastname)) {
        $user->lastname = $data->lastname;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[lastname] is required.");
    }

    // check email
    if (!empty($data->email)) {
        $user->email = $data->email;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[email] is required.");
    }

    // check password
    if (!empty($data->password)) {
        $user->password = $data->password;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[password] is required.");
    }

    // check access_level
    if (!empty($data->access_level)) {
        $user->access_level = $data->access_level;
    } 

    // call user create function
    if ($user->create()) {
        echo json_encode(array(
            "status" => "success",
            "message" => "User $data->email was created.",
        ));
    }
} catch (Exception $e) {
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
