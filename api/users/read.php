<?php
// users
try {
    // ACL
    $access_levels = array("Admin");
    if (!in_array($access_level, $access_levels)) {
        // Unauthorized access level
        http_response_code(401); // unauthorized
        throw new Exception("[user] is unauthorized.");
    }

    if ($collection == "users") {
        // READ (one) user by ID
        if ((!isset($_GET["s"])) && (isset($resource))) {
            $stmt = $user->read($resource);
            $num = $stmt->rowCount();

            // user found
            if ($num == 1) {
                // retrieve user contents
                $row = $stmt->fetch(PDO::FETCH_ASSOC);
                extract($row);
                $user_item = array(
                    "id" => intval($resource),
                    "email" => $email,
                    "firstname" => $firstname,
                    "lastname" => $lastname,
                    "access_level" => $access_level,
                );
                echo json_encode($user_item);
            } else {
                http_response_code(404); // not found
                throw new Exception("No user found.");
            }
        }

        // READ (all) users
        if ((!isset($_GET["s"])) && (!isset($resource))) {
            $stmt = $user->readUsers();
            $num = $stmt->rowCount();
            // check if more than 0 record found
            if ($num > 0) {
                // create user array
                $user_arr = array();
                // retrieve our table contents
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    // iterate through users and create user item.
                    extract($row);
                    $user_item = array(
                        "id" => intval($id),
                        "email" => $email,
                        "firstname" => $firstname,
                        "lastname" => $lastname,
                        "access_level" => $access_level,
                    );
                    // Push the user item into the user array
                    array_push($user_arr, $user_item);
                } // while
                echo json_encode($user_arr);
            } else {
                http_response_code(404); // not found
                throw new Exception("No users found.");
            }
        }

        // SEARCH users
        if ((isset($_GET["s"])) && (!isset($resource))) {

            // assign search to user class
            $user->email = $_GET["s"];

            // query users
            $stmt = $user->search();
            $num = $stmt->rowCount();

            // user found
            if ($num > 0) {
                // retrieve user contents
                $row = $stmt->fetch(PDO::FETCH_ASSOC);
                extract($row);
                $user_item = array(
                    "id" => intval($id),
                    "email" => $user->email,
                    "firstname" => $firstname,
                    "lastname" => $lastname,
                    "access_level" => $access_level,
                );
                echo json_encode($user_item);
            } else {
                http_response_code(404); // not found
                throw new Exception("No users found.");
            }
        }
    }
} catch (Exception $e) {
    //  display message
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
