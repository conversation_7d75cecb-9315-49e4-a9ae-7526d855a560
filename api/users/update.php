<?php
/*
 * Allows Authenticated Admins to create users
 */
// ACL
$access_levels = array("Admin");
if (!in_array($access_level, $access_levels)) {
    // Unauthorized access level
    http_response_code(401); // unauthorized
    throw new Exception("[user] is unauthorized.");
}
try {
    // check access_level
    if ($decoded->data->access_level !== "Admin") {
        http_response_code(401); // Unauthorized
        throw new Exception("Admin credentials are required.");
    }

    // check fistname
    if (!empty($data->firstname)) {
        $user->firstname = $data->firstname;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[firstname] is required.");
    }

    // check lastname
    if (!empty($data->lastname)) {
        $user->lastname = $data->lastname;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[lastname] is required.");
    }

    // check email
    if (!empty($data->email)) {
        $user->email = $data->email;
    } else {
        http_response_code(400); // Bad Request
        throw new Exception("[email] is required.");
    }

    // check password
    if (!empty($data->password)) {
        $user->password = $data->password;
    }

    // check to see if user exists
    $stmt = $user->search();
    $num = $stmt->rowCount();
    if ($num > 0) {
        http_response_code(400); // Bad Request
        throw new Exception("[email] already exists.");
    }

    // call user create function
    if ($user->update()) {
        echo json_encode(array(
            "status" => "success",
            "message" => "User $data->email was updated.",
        ));
    }
} catch (Exception $e) {
    echo json_encode(array(
        "status" => "error",
        "message" => $e->getMessage(),
    ));
    die;
}
