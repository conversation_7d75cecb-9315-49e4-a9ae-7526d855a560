/*M!999999\- enable the sandbox mode */ 
-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.11.13-MariaD<PERSON>, for debian-linux-gnu (x86_64)
--
-- Host: localhost    Database: api_db
-- ------------------------------------------------------
-- Server version	10.11.13-MariaDB-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `_abandoned_transaction_log`
--

DROP TABLE IF EXISTS `_abandoned_transaction_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `_abandoned_transaction_log` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `action` varchar(255) DEFAULT NULL,
  `table_name` varchar(255) DEFAULT NULL,
  `record_id` varchar(255) DEFAULT NULL,
  `associated_transaction_id` varchar(255) DEFAULT NULL,
  `timestamp` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9575 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `_donation_type_corrections`
--

DROP TABLE IF EXISTS `_donation_type_corrections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `_donation_type_corrections` (
  `correction_id` int(11) NOT NULL AUTO_INCREMENT,
  `donation_id` int(11) NOT NULL,
  `campaign_type` varchar(255) DEFAULT NULL,
  `original_donation_type` varchar(255) NOT NULL,
  `corrected_donation_type` varchar(255) NOT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `installment` varchar(255) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `correction_timestamp` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`correction_id`),
  KEY `donation_id` (`donation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16914 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accounts`
--

DROP TABLE IF EXISTS `accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donor_id` int(11) DEFAULT NULL,
  `volunteer_id` int(11) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `foundation_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_2` (`id`,`donor_id`,`volunteer_id`,`employee_id`,`foundation_id`),
  KEY `id` (`id`,`donor_id`,`volunteer_id`,`employee_id`,`foundation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `caller_log`
--

DROP TABLE IF EXISTS `caller_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `caller_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donor_id` int(11) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `number` bigint(20) NOT NULL,
  `duration` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `donor_id` (`donor_id`),
  CONSTRAINT `donorid_fkib` FOREIGN KEY (`donor_id`) REFERENCES `donors` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=18968 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `campaigns`
--

DROP TABLE IF EXISTS `campaigns`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL COMMENT 'Campaign name',
  `type` enum('marathon','mailer','email','event','general') DEFAULT NULL,
  `goal` decimal(11,2) NOT NULL COMMENT 'Campaign goal',
  `gift_title` tinytext DEFAULT NULL,
  `gift_link` tinytext DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `start` datetime NOT NULL COMMENT 'Start Date',
  `end` datetime NOT NULL COMMENT 'End Date',
  `active` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `donations`
--

DROP TABLE IF EXISTS `donations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `donations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'donation_id',
  `timestamp` datetime NOT NULL DEFAULT current_timestamp(),
  `account_id` int(11) DEFAULT NULL,
  `donor_id` int(11) NOT NULL,
  `transaction_id` varchar(32) NOT NULL,
  `payment_id` int(11) DEFAULT NULL COMMENT 'Allegiance PledgeID',
  `type` enum('Pledge','In-Kind','Vehicle','Bequest','Grant','Contract','Corporate Match','QCD','Donor Advised Fund','Major','Minor','Mailer') NOT NULL DEFAULT 'Pledge',
  `amount` decimal(13,2) NOT NULL,
  `installment` varchar(10) NOT NULL,
  `comments` mediumtext DEFAULT NULL,
  `add_me` char(1) DEFAULT NULL COMMENT 'Marketing & Communication',
  `read_onair` tinyint(1) NOT NULL DEFAULT 0,
  `ipaddress` varchar(45) DEFAULT NULL COMMENT 'ipv4 (15) and ipv6 (45)',
  `browser` varchar(512) DEFAULT NULL,
  `show_name` varchar(225) DEFAULT NULL,
  `program_wp_id` int(11) DEFAULT NULL,
  `source` enum('WebSite','PhoneRoom','StationAdmin','Testing','PaySol','CallCenter','Allegiance') DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `donation_match` tinyint(1) NOT NULL DEFAULT 0,
  `premiums_cart` longtext DEFAULT NULL COMMENT 'Premium Cart at time of donation',
  `updated` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `donor_id` (`donor_id`),
  KEY `campaign_id` (`campaign_id`),
  KEY `timestamp` (`timestamp`),
  CONSTRAINT `donations_campaigns_ibfk_1` FOREIGN KEY (`campaign_id`) REFERENCES `campaigns` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `donations_donors_ibfk_1` FOREIGN KEY (`donor_id`) REFERENCES `donors` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=232835 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `donors`
--

DROP TABLE IF EXISTS `donors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `donors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firstname` varchar(35) DEFAULT NULL,
  `lastname` varchar(50) DEFAULT NULL,
  `phone` varchar(16) DEFAULT NULL COMMENT 'Phone Number (E.164)',
  `email` varchar(320) DEFAULT NULL COMMENT 'email address',
  `address1` varchar(225) DEFAULT NULL,
  `address2` varchar(225) DEFAULT NULL,
  `partner_firstname` varchar(35) DEFAULT NULL,
  `partner_lastname` varchar(50) DEFAULT NULL,
  `city` varchar(225) DEFAULT NULL,
  `state` varchar(225) DEFAULT NULL,
  `country` varchar(225) NOT NULL DEFAULT 'US' COMMENT 'ISO 3166-1 alpha-2',
  `postal_code` varchar(16) DEFAULT NULL,
  `notes` mediumtext DEFAULT NULL COMMENT 'Notes',
  `type` set('Individual','Couple','Foundation','Corporate','Goverment','Trust','Charity','Fund','Test','Fraudulent') NOT NULL DEFAULT 'Individual',
  `membership_level` enum('Basic','Vigilant','Vibrant') DEFAULT NULL,
  `deceased` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'alive or deceased',
  `donotsolicit` tinyint(1) DEFAULT NULL,
  `stripe_cus_id` varchar(255) DEFAULT NULL COMMENT 'Stripe Customer ID',
  `paypal_user_id` varchar(255) DEFAULT NULL COMMENT 'PayPal ID',
  `memsys_id` mediumint(8) unsigned DEFAULT NULL COMMENT 'MemSys ID',
  `allegiance_id` int(11) DEFAULT NULL COMMENT 'Allegiance ID',
  `date_created` datetime NOT NULL DEFAULT current_timestamp(),
  `date_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `paperless` tinyint(1) NOT NULL DEFAULT 0,
  `paperless_token` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `memsys_id` (`memsys_id`),
  UNIQUE KEY `stripe_cus_id` (`stripe_cus_id`),
  UNIQUE KEY `paypal_user_id` (`paypal_user_id`),
  UNIQUE KEY `email_2` (`email`),
  UNIQUE KEY `allegiance_id_2` (`allegiance_id`),
  KEY `email` (`email`),
  KEY `phone` (`phone`),
  KEY `lastname` (`lastname`),
  KEY `allegiance_id` (`allegiance_id`)
) ENGINE=InnoDB AUTO_INCREMENT=86098 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `download_log`
--

DROP TABLE IF EXISTS `download_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `download_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `show` varchar(225) DEFAULT NULL,
  `episode` varchar(512) DEFAULT NULL,
  `post_id` bigint(20) DEFAULT NULL,
  `ipaddress` varchar(45) DEFAULT NULL,
  `referer` varchar(512) DEFAULT NULL,
  `useragent` varchar(512) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `post_id` (`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_msg`
--

DROP TABLE IF EXISTS `email_msg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_msg` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` tinytext DEFAULT NULL,
  `body` mediumtext DEFAULT NULL,
  `date_created` datetime NOT NULL DEFAULT current_timestamp(),
  `date_updated` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `foundations`
--

DROP TABLE IF EXISTS `foundations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `foundations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) DEFAULT NULL,
  `type` set('Family Foundation','Private Foundation','Corporate Foundation','Community Foundation','Federal','State','Local') NOT NULL,
  `name` varchar(255) NOT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `address1` varchar(255) NOT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `city` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `postal_code` varchar(255) NOT NULL,
  `country` varchar(255) NOT NULL,
  `phone` varchar(16) DEFAULT NULL,
  `fax` bigint(20) DEFAULT NULL,
  `email` varchar(320) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `icecast_log`
--

DROP TABLE IF EXISTS `icecast_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `icecast_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `icecast_id` int(11) NOT NULL,
  `datetime_start` datetime NOT NULL,
  `datetime_end` datetime DEFAULT NULL,
  `ipaddress` varchar(45) NOT NULL,
  `country_code` varchar(4) DEFAULT NULL,
  `mount` varchar(60) NOT NULL,
  `duration` int(11) DEFAULT NULL,
  `sent_bytes` int(11) DEFAULT NULL,
  `useragent` varchar(200) DEFAULT NULL,
  `referer` varchar(400) DEFAULT NULL,
  `server` varchar(50) DEFAULT NULL,
  `port` int(11) DEFAULT NULL,
  `user` varchar(20) DEFAULT NULL,
  `pass` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1564 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `kpfa_programs`
--

DROP TABLE IF EXISTS `kpfa_programs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `kpfa_programs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `wp_id` int(11) NOT NULL,
  `count` int(11) NOT NULL,
  `link` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `kpfa_b` varchar(255) NOT NULL,
  `program_category` varchar(255) NOT NULL,
  `show_time` varchar(255) NOT NULL,
  `confessor_id` varchar(255) DEFAULT NULL,
  `default_archive_length` varchar(255) NOT NULL,
  `program_inactive` tinyint(1) NOT NULL,
  `downloadable_program` tinyint(1) NOT NULL,
  `weeks_that_it_airs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`weeks_that_it_airs`)),
  `days` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`days`)),
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_7DC0A5357FEF777C` (`wp_id`)
) ENGINE=InnoDB AUTO_INCREMENT=236 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `applied_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `migration` (`migration`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payments`
--

DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donation_id` int(11) NOT NULL COMMENT 'Foreign Key to donations',
  `customer_id` varchar(255) DEFAULT NULL COMMENT 'stripe_customer_id',
  `payment_id` varchar(255) DEFAULT NULL COMMENT 'stripe charge_id, paypal payments id, check#',
  `amount` decimal(13,2) NOT NULL COMMENT 'Amount',
  `amount_refunded` decimal(13,2) NOT NULL DEFAULT 0.00 COMMENT 'Amount Refunded',
  `method` set('card','check','cash','in-kind','bank_account','us_bank_account','epayment') NOT NULL,
  `processor` enum('Stripe','PayPal','P-check','K-check','Staff','Paya','Venmo','DipJar','ACH') DEFAULT NULL,
  `fingerprint` varchar(30) DEFAULT NULL COMMENT 'stripe_card_fingerprint',
  `card_type` set('credit','debit','prepaid','unknown') DEFAULT NULL,
  `last4` smallint(5) unsigned DEFAULT NULL,
  `brand` set('Amex','Diners Club','Discover','JCB','MasterCard','Visa','UnionPay','Unknown','American Express','Diners') DEFAULT NULL,
  `exp_month` tinyint(4) DEFAULT NULL,
  `exp_year` smallint(5) unsigned DEFAULT NULL,
  `last_updated_by` int(11) DEFAULT NULL COMMENT 'user_id',
  `date_created` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'Creation time',
  `date_updated` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'Record update date and time',
  `date_deposited` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'Date Deposited into Station Bank account',
  `status` set('succeeded','pending','failed') NOT NULL,
  PRIMARY KEY (`id`),
  KEY `donations_id` (`donation_id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `payments_ibfk` FOREIGN KEY (`donation_id`) REFERENCES `donations` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=461990 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `premium_categories`
--

DROP TABLE IF EXISTS `premium_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `premium_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(256) NOT NULL,
  `description` mediumtext NOT NULL,
  `created` datetime NOT NULL DEFAULT current_timestamp(),
  `modified` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `premium_vendors`
--

DROP TABLE IF EXISTS `premium_vendors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `premium_vendors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company` varchar(225) DEFAULT NULL,
  `contact` varchar(225) DEFAULT NULL,
  `website` varchar(225) DEFAULT NULL,
  `address` varchar(225) DEFAULT NULL,
  `city` varchar(225) DEFAULT NULL,
  `state` varchar(225) DEFAULT NULL,
  `zip` varchar(16) DEFAULT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `email` varchar(254) DEFAULT NULL,
  `updated` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='vendor table linked to premiums';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `premiums`
--

DROP TABLE IF EXISTS `premiums`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `premiums` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` mediumtext NOT NULL,
  `img_url` varchar(255) NOT NULL,
  `download_url` varchar(255) DEFAULT NULL,
  `price` decimal(8,2) NOT NULL,
  `cog` int(11) NOT NULL COMMENT 'Cost of Goods',
  `fmv` int(11) NOT NULL COMMENT 'Fair Market Value',
  `qty` int(11) NOT NULL COMMENT 'Quantity',
  `variant_name` set('Size','Color','Delivery') DEFAULT NULL,
  `variation_name` set('Small','Medium','Large','XL','XXL','3XL','4XL','Black','White','Grey','Green Forest','One Size Fits Most','Post Mail','Download','Red','Blue') DEFAULT NULL,
  `parentID` int(11) DEFAULT NULL,
  `category_id` int(11) NOT NULL,
  `created` datetime NOT NULL DEFAULT current_timestamp(),
  `modified` timestamp NOT NULL DEFAULT current_timestamp(),
  `featured` tinyint(1) NOT NULL DEFAULT 0,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `vendor_id` int(11) DEFAULT NULL,
  `vendor_code` varchar(48) DEFAULT NULL,
  `sort_weight` int(11) NOT NULL DEFAULT 50 COMMENT 'sort weight',
  `date_created` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'date_created',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `vendor_id` (`vendor_id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `premiums_ibfk_1` FOREIGN KEY (`vendor_id`) REFERENCES `premium_vendors` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `premiums_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `premium_categories` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=224621 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shipment_addresses`
--

DROP TABLE IF EXISTS `shipment_addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `shipment_addresses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(255) NOT NULL,
  `address1` varchar(255) NOT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `city` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `postal_code` varchar(255) NOT NULL,
  `country` varchar(255) NOT NULL,
  `donation_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `donation_id` (`donation_id`),
  CONSTRAINT `fk_donation_id2` FOREIGN KEY (`donation_id`) REFERENCES `donations` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=85089 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shipments`
--

DROP TABLE IF EXISTS `shipments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `shipments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `donation_id` int(11) NOT NULL,
  `premium_id` int(11) NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT current_timestamp(),
  `quantity` int(11) NOT NULL DEFAULT 1,
  `status` set('Shipped','On Hold','New','Canceled','Returned') NOT NULL DEFAULT 'New',
  `ship_date` date DEFAULT NULL,
  `tracking_number` varchar(35) DEFAULT NULL,
  `updated` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `premium_id` (`premium_id`),
  KEY `donation_id` (`donation_id`),
  KEY `timestamp` (`timestamp`),
  KEY `updated` (`updated`),
  CONSTRAINT `fk_donation_id` FOREIGN KEY (`donation_id`) REFERENCES `donations` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_premium_id` FOREIGN KEY (`premium_id`) REFERENCES `premiums` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=49156 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shows`
--

DROP TABLE IF EXISTS `shows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `shows` (
  `id` smallint(6) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Program Name',
  `hosts` varchar(255) NOT NULL COMMENT 'Program Hosts',
  `description` mediumtext NOT NULL COMMENT 'Program Description',
  `img_url` varchar(255) DEFAULT NULL,
  `starts` time NOT NULL COMMENT 'Starting Time',
  `ends` time NOT NULL COMMENT 'Ending Time',
  `duration` smallint(6) NOT NULL COMMENT 'Length in seconds',
  `days` set('Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday') NOT NULL COMMENT 'Day of the Week',
  `weeks` set('01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','40','41','42','43','44','45','46','47','48','49','50','51','52','53') DEFAULT NULL COMMENT 'Weeks of the Year',
  `dad_id` int(11) NOT NULL COMMENT 'Dad ID',
  `type` set('News & Politics','Music','Culture','Special','Talk') NOT NULL COMMENT 'Program Type',
  `active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Active Program',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=139 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `stream_log`
--

DROP TABLE IF EXISTS `stream_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `stream_log` (
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `stream` varchar(12) NOT NULL COMMENT 'Stream Name',
  `streamers` int(11) NOT NULL COMMENT 'Listener Count',
  `show` varchar(255) DEFAULT NULL,
  `episode` varchar(255) DEFAULT NULL,
  `post_id` bigint(20) DEFAULT NULL,
  UNIQUE KEY `timestamp` (`timestamp`),
  KEY `post_id` (`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `stripe_prices`
--

DROP TABLE IF EXISTS `stripe_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `stripe_prices` (
  `id` varchar(40) NOT NULL,
  `amount` int(11) NOT NULL,
  `product_id` varchar(40) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `amount_2` (`amount`),
  KEY `amount` (`amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subscriptions`
--

DROP TABLE IF EXISTS `subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `subscriptions` (
  `id` varchar(255) NOT NULL COMMENT 'Stripe subscription_id',
  `customer_id` varchar(255) NOT NULL,
  `donor_id` int(11) NOT NULL COMMENT 'kpfa donor_id',
  `processor` enum('Stripe') NOT NULL,
  `date_created` datetime NOT NULL DEFAULT current_timestamp(),
  `plan_id` varchar(255) NOT NULL,
  `transaction_id` varchar(32) DEFAULT NULL,
  `amount` decimal(13,2) NOT NULL,
  `interval` enum('month') NOT NULL,
  `active` tinyint(1) NOT NULL,
  `date_canceled` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `donorid_ibfk` (`donor_id`),
  KEY `transactionid_ibfk` (`transaction_id`),
  CONSTRAINT `customerid_ibfk` FOREIGN KEY (`customer_id`) REFERENCES `payments` (`customer_id`),
  CONSTRAINT `donorid_ibfk` FOREIGN KEY (`donor_id`) REFERENCES `donors` (`id`),
  CONSTRAINT `transactionid_ibfk` FOREIGN KEY (`transaction_id`) REFERENCES `donations` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `upload_kpfa_survey__responses__form_responses_1_20241112214111`
--

DROP TABLE IF EXISTS `upload_kpfa_survey__responses__form_responses_1_20241112214111`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `upload_kpfa_survey__responses__form_responses_1_20241112214111` (
  `_mb_row_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `timestamp` varchar(255) DEFAULT NULL,
  `what_is_your_age_` varchar(255) DEFAULT NULL,
  `what_is_your_gender_` varchar(255) DEFAULT NULL,
  `what_is_your_highest_level_of_education_` varchar(255) DEFAULT NULL,
  `which_topics_are_most_interesting_to_you___select_up_to_three_` varchar(255) DEFAULT NULL,
  `how_often_do_you_listen_to_the_radio_` varchar(255) DEFAULT NULL,
  `what_is_your_annual_income_range___select_the_highest_amount_tha` varchar(255) DEFAULT NULL,
  `how_important_is_it_for_you_that_the_content_you_consume_is_alig` bigint(20) DEFAULT NULL,
  `what_technology_do_you_use_to_listen_to_the_radio__how_often___i` varchar(255) DEFAULT NULL,
  `what_technology_do_you_use_to_listen_to_the_radio___4f6fd099` varchar(255) DEFAULT NULL,
  `what_technology_do_you_use_to_listen_to_the_radio___3868e00f` varchar(255) DEFAULT NULL,
  `are_you_a_current_kpfa__berkeley__ca__listener_` tinyint(1) DEFAULT NULL,
  `would_you_be_interested_in_sharing_kpfa_content_with_your_friend` varchar(255) DEFAULT NULL,
  `what_kind_of_kpfa_content_would_you_be_most_likely_to_share_` varchar(255) DEFAULT NULL,
  `what_improvements_would_make_kpfa_more_relevant_to_you_` text DEFAULT NULL,
  `kpfa_is_entirely_listener_supported__have_you_ever_donated_to_kp` varchar(255) DEFAULT NULL,
  `do_you_listen_to_podcasts_` tinyint(1) DEFAULT NULL,
  `how_often_do_you_listen_to_podcasts___frequency_` varchar(255) DEFAULT NULL,
  `what_is_your_preferred_podcast_length_` varchar(255) DEFAULT NULL,
  `what_types_of_podcasts_do_you_listen_to___check_all_that_apply_` varchar(255) DEFAULT NULL,
  `what_are_some_of_your_favorite_podcasts_` text DEFAULT NULL,
  `do_you_use_kpfa_s_website_` tinyint(1) DEFAULT NULL,
  `what_do_you_currently_use_the_kpfa_org_website_for_` text DEFAULT NULL,
  `what_s_important_to_you_about_kpfa_` text DEFAULT NULL,
  `in_what_ways_do_you_think_kpfa_could_improve_` text DEFAULT NULL,
  `what_do_you_like_most_about_the_website_` varchar(255) DEFAULT NULL,
  `if_you_answered_yes_to_the_previous_question__what_made_you_deci` text DEFAULT NULL,
  `what_could_be_improved_` text DEFAULT NULL,
  `when_visiting_the_site__how_often_are_you_on_your_desktop_or_lap` bigint(20) DEFAULT NULL,
  `when_visiting_the_site__how_often_are_you_on_your_tablet__medium` bigint(20) DEFAULT NULL,
  `when_visiting_the_site__how_often_are_you_on_your_mobile_phone__` bigint(20) DEFAULT NULL,
  `what_is_your_preferred_way_to_visit_the_website_` varchar(255) DEFAULT NULL,
  `what_social_media_apps_do_you_use_` varchar(255) DEFAULT NULL,
  `how_easy_is_it_to_find_what_you_re_looking_for_` varchar(255) DEFAULT NULL,
  `i_am_entering_my_email_address_below_so_kpfa_and_its_research_as` varchar(255) DEFAULT NULL,
  `group` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`_mb_row_id`)
) ENGINE=InnoDB AUTO_INCREMENT=251 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firstname` varchar(32) NOT NULL,
  `lastname` varchar(32) NOT NULL,
  `email` varchar(64) NOT NULL,
  `password` varchar(2048) NOT NULL,
  `access_level` set('Admin','Staff','CallCenter','Donor') NOT NULL DEFAULT 'Staff',
  `created` datetime NOT NULL DEFAULT current_timestamp(),
  `modified` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=124 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='admin and users';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping events for database 'api_db'
--
/*!50106 SET @save_time_zone= @@TIME_ZONE */ ;
/*!50106 DROP EVENT IF EXISTS `Delete Abandoned Donations From Previous Calendar Day or Before` */;
DELIMITER ;;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;;
/*!50003 SET character_set_client  = utf8mb4 */ ;;
/*!50003 SET character_set_results = utf8mb4 */ ;;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;;
/*!50003 SET @saved_time_zone      = @@time_zone */ ;;
/*!50003 SET time_zone             = 'SYSTEM' */ ;;
/*!50106 CREATE*/ /*!50117 DEFINER=`root`@`localhost`*/ /*!50106 EVENT `Delete Abandoned Donations From Previous Calendar Day or Before` ON SCHEDULE EVERY 1 DAY STARTS '2025-05-07 01:00:00' ON COMPLETION PRESERVE ENABLE DO BEGIN
    
    INSERT INTO _abandoned_transaction_log (action, table_name, record_id, associated_transaction_id)
    SELECT 'DELETE', 'shipments', s.id, d1.transaction_id
    FROM shipments s
    INNER JOIN donations d1 ON s.donation_id = d1.id
    LEFT JOIN payments p1 ON d1.id = p1.donation_id
    WHERE p1.id IS NULL
      AND d1.source = 'WebSite'
      AND d1.timestamp < CURDATE();

    DELETE s
    FROM shipments s
    INNER JOIN donations d1 ON s.donation_id = d1.id
    LEFT JOIN payments p1 ON d1.id = p1.donation_id
    WHERE p1.id IS NULL
      AND d1.source = 'WebSite'
      AND d1.timestamp < CURDATE();

    
    INSERT INTO _abandoned_transaction_log (action, table_name, record_id, associated_transaction_id)
    SELECT 'DELETE', 'subscriptions', s.id, s.transaction_id
    FROM subscriptions s
    INNER JOIN donations d1 ON s.transaction_id = d1.transaction_id
    LEFT JOIN payments p1 ON d1.id = p1.donation_id
    WHERE p1.id IS NULL
      AND d1.source = 'WebSite'
      AND d1.timestamp < CURDATE();

    DELETE s
    FROM subscriptions s
    INNER JOIN donations d1 ON s.transaction_id = d1.transaction_id
    LEFT JOIN payments p1 ON d1.id = p1.donation_id
    WHERE p1.id IS NULL
      AND d1.source = 'WebSite'
      AND d1.timestamp < CURDATE();

    
    INSERT INTO _abandoned_transaction_log (action, table_name, record_id, associated_transaction_id)
    SELECT 'DELETE', 'donations', d1.id, d1.transaction_id
    FROM donations d1
    LEFT JOIN payments p1 ON d1.id = p1.donation_id
    WHERE p1.id IS NULL
      AND d1.id NOT IN (SELECT p.donation_id FROM payments p)
      AND d1.source = 'WebSite'
      AND d1.timestamp < CURDATE();

    DELETE d1
    FROM donations d1
    LEFT JOIN payments p1 ON d1.id = p1.donation_id
    WHERE p1.id IS NULL
      AND d1.id NOT IN (SELECT p.donation_id FROM payments p)
      AND d1.source = 'WebSite'
      AND d1.timestamp < CURDATE();
END */ ;;
/*!50003 SET time_zone             = @saved_time_zone */ ;;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;;
/*!50003 SET character_set_client  = @saved_cs_client */ ;;
/*!50003 SET character_set_results = @saved_cs_results */ ;;
/*!50003 SET collation_connection  = @saved_col_connection */ ;;
DELIMITER ;
/*!50106 SET TIME_ZONE= @save_time_zone */ ;

--
-- Dumping routines for database 'api_db'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-14 14:02:46
