# KPFA Knowledge MCP Server

Enhanced MCP (Model Context Protocol) server providing comprehensive domain knowledge for the KPFA donor management system. This server offers structured access to business logic, technical patterns, testing strategies, and implementation guidance.

## Features

### 📚 **Comprehensive Knowledge Base**
- **Business Logic**: Duplicate detection, donor merging, data integrity rules
- **Technical Patterns**: GraphQL conventions, state management, code patterns  
- **UI/UX Guidelines**: Component patterns, styling standards, interaction flows
- **Testing Strategies**: Unit testing, mocking patterns, QA best practices
- **Authentication Patterns**: Auth context, debugging, access control
- **Auto-Suggested Features**: Implementation details for auto-suggested duplicate workflows
- **System Architecture**: Component integration, data flow, technology stack

### 🔍 **Advanced Search & Organization**
- **Categorized Knowledge**: Organized into logical categories with metadata
- **Tag-Based Search**: Find knowledge by relevant tags and keywords
- **Semantic Search**: Intelligent matching across titles, tags, and categories
- **Priority Levels**: High/medium/low priority knowledge items
- **Version Tracking**: Knowledge versioning and last-updated timestamps

### 🛠️ **Interactive Tools**
- `search_knowledge`: Search across all knowledge by keywords, tags, or categories
- `get_by_category`: Retrieve all knowledge items for a specific category
- `get_by_tags`: Find knowledge items matching specific tags

## Knowledge Categories

### 1. **Business Domain** 
Core business logic, rules, and processes for donor duplicate management
- Duplicate Detection Process
- Donor Merging Process

### 2. **Technical Implementation**
Code patterns, technical architecture, and implementation details
- GraphQL Query and Mutation Patterns  
- Auto-Suggested Features Implementation

### 3. **User Interface**
UI patterns, styling guidelines, and user experience standards
- UI/UX Patterns and Components

### 4. **Testing & QA**
Testing strategies, patterns, and quality assurance practices
- Testing Strategy and Patterns
- Authentication Mocking
- Business Logic Testing

### 5. **Authentication & Security**
Authentication patterns, access control, and security considerations
- Authentication Context Setup
- Debugging Authentication Issues
- Testing Authentication Components

### 6. **System Architecture**
Overall system design, component integration, and data flow
- PHP API ↔ Keystone ↔ Next.js Integration
- Data Synchronization Patterns

## Installation & Usage

### Prerequisites
- Node.js 18+ 
- TypeScript 5.0+

### Setup
```bash
cd kpfa-knowledge-mcp
npm install
npm run build
```

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

## MCP Protocol Usage

### Resources
Access knowledge domains directly:
```
knowledge://kpfa/business-logic
knowledge://kpfa/technical-patterns
knowledge://kpfa/ui-patterns
knowledge://kpfa/testing
knowledge://kpfa/auto-suggested-features
knowledge://kpfa/auth-patterns
knowledge://kpfa/system-architecture
knowledge://kpfa/categories
```

### Tools

#### Search Knowledge
```json
{
  "method": "tools/call",
  "params": {
    "name": "search_knowledge",
    "arguments": {
      "query": "authentication testing",
      "includeContent": true
    }
  }
}
```

#### Get By Category
```json
{
  "method": "tools/call", 
  "params": {
    "name": "get_by_category",
    "arguments": {
      "categoryId": "testing-qa",
      "includeContent": false
    }
  }
}
```

#### Get By Tags
```json
{
  "method": "tools/call",
  "params": {
    "name": "get_by_tags", 
    "arguments": {
      "tags": ["graphql", "mutations"],
      "includeContent": true
    }
  }
}
```

## Recent Updates (June 2025)

### ✨ **New Knowledge Areas Added**
- **Testing Patterns**: Comprehensive testing strategies from recent auto-suggested functionality work
- **Authentication Debugging**: Solutions for common auth context issues and testing challenges
- **Auto-Suggested Workflows**: Complete implementation guidance for auto-suggested duplicate management
- **Business Logic Testing**: Patterns for testing complex business rules in isolation

### 🎯 **Enhanced Organization**
- **Metadata System**: Priority levels, versioning, last-updated timestamps
- **Tag-Based Classification**: Granular tagging for precise knowledge discovery
- **Category Hierarchy**: Logical grouping of related knowledge areas
- **Search Scoring**: Intelligent ranking of search results by relevance

### 🔧 **Tool Capabilities**
- **Smart Search**: Weighted scoring system (title > tags > category)
- **Content Resolution**: Dynamic content retrieval from knowledge paths
- **Flexible Filtering**: Search by keywords, categories, or tag combinations
- **Result Limiting**: Top 10 results with match scores for optimal performance

## Knowledge Quality Standards

- **Accuracy**: All knowledge verified through actual implementation experience
- **Recency**: Updated June 18, 2025 with latest patterns and solutions
- **Practicality**: Focus on actionable guidance and real-world problem solving
- **Comprehensiveness**: Covers full development lifecycle from architecture to testing

## Architecture

### Server Components
- **Request Handler**: JSON-RPC 2.0 protocol implementation
- **Knowledge Engine**: Content organization and retrieval system
- **Search System**: Multi-criteria search with scoring
- **Tool Framework**: Interactive knowledge query capabilities

### Knowledge Structure
```
KPFA_KNOWLEDGE
├── businessLogic/
├── technicalPatterns/
├── uiPatterns/
├── codeStyle/
├── businessRules/
├── systemArchitecture/
├── testing/                    # ← New
├── autoSuggestedFeatures/      # ← New  
├── authPatterns/               # ← New
└── categories/                 # ← New
```

## Contributing

When adding new knowledge:
1. Update the appropriate knowledge domain in `src/knowledge.ts`
2. Add corresponding TypeScript types in `src/types.ts`  
3. Update category metadata with proper tags and priority
4. Test search functionality with new content
5. Update this README with new capabilities

## Version History

- **v2.0.0** (June 2025): Enhanced with categorization, search, testing knowledge, auth patterns
- **v1.0.0** (January 2025): Initial release with core business domain knowledge

---

*This MCP server embodies the collective knowledge and experience of building and maintaining the KPFA donor management system, providing instant access to battle-tested patterns and solutions.* 