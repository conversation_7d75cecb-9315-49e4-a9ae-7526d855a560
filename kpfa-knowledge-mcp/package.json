{"name": "kpfa-knowledge-mcp", "version": "2.0.0", "description": "MCP server providing domain knowledge for KPFA donor management system", "main": "dist/server.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "keywords": ["mcp", "kpfa", "donor-management", "knowledge-server"], "author": "KPFA Team", "license": "MIT"}