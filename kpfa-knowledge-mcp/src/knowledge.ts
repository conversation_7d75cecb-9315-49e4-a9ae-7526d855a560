import type { KPFAKnowledge } from './types.js';

export const KPFA_KNOWLEDGE: KPFAKnowledge = {
  // Business Domain Knowledge
  businessLogic: {
    duplicateDetection: {
      process: "Auto-detection via phone number matching → staff review → decision",
      statuses: {
        "SUGGESTED": "Auto-detected, pending staff review",
        "PENDING": "Staff moved to unconfirmed queue for further review", 
        "READY_TO_MERGE": "Staff confirmed as duplicates, ready for bulk merge",
        "MERGED": "Successfully merged into primary donor record",
        "REJECTED": "Staff determined NOT duplicates, creates prevention records"
      },
      autoCreationRules: "Phone number exact matches create auto-suggested duplicate groups",
      rejectionLogic: "Creates DonorPairDecision records to prevent future auto-suggestions for same pairs"
    },
    
    donorMerging: {
      primaryDonorSelection: "Staff manually chooses which donor becomes the primary record",
      dataConsolidation: "Combines donations, contact info, membership data, notes",
      auditTrail: "All decisions tracked with staffMember name and timestamp"
    },
    
    dataIntegrity: {
      kpfaId: "Legacy system primary identifier - immutable and required",
      phoneNormalization: "System normalizes phone formats for comparison",
      pairDecisions: "Prevents duplicate suggestions for previously reviewed pairs"
    }
  },

  // UI/UX Patterns
  uiPatterns: {
    actionButtons: {
      confirm: "Green buttons (bg-green-100, text-green-700) for positive actions",
      reject: "Red buttons (bg-red-100, text-red-700) for negative actions", 
      view: "Blue buttons (bg-blue-100, text-blue-700) for navigation/info",
      neutral: "Gray buttons for secondary actions"
    },
    
    statusBadges: {
      primary: "badge-green for primary donors and confirmed items",
      warning: "badge-yellow for missing data or pending states",
      info: "badge-blue for counts and informational data"
    },
    
    loadingStates: {
      pattern: "processingGroupId state with disabled buttons and 'Processing...' text",
      spinners: "Tailwind animate-spin for loading indicators"
    },
    
    conditionalRendering: {
      autoCreatedGroups: "Only show reject button for groups with autoCreated=true (not manually created groups)",
      emptyGroups: "Show delete button only for groups with no duplicate donors",
      statusSpecific: "Different actions available based on group status (SUGGESTED vs PENDING vs READY_TO_MERGE)"
    }
  },

  // Technical Patterns
  technicalPatterns: {
    graphqlQueries: {
      alwaysInclude: ["id", "autoCreated", "status"],
      auditFields: ["reviewedBy", "reviewedAt", "reviewNotes"],
      relationships: "Include both primaryDonor and duplicateDonors with kpfaId"
    },
    
    mutations: {
      staffTracking: "All decision mutations require staffMember parameter",
      optimisticUpdates: "Use optimisticResponse for immediate UI feedback",
      refetchQueries: "Always refetch related queries after mutations"
    },
    
    stateManagement: {
      processingStates: "Track individual item processing with ID-based state",
      modalStates: "Separate state objects for different modal types",
      searchAndPagination: "URL-synced search with debounced queries"
    }
  },

  // Code Style Preferences  
  codeStyle: {
    components: {
      structure: "Functional components with hooks",
      props: "TypeScript interfaces for all component props",
      styling: "Tailwind CSS classes, avoid inline styles"
    },
    
    mutations: {
      errorHandling: "Try-catch with user-friendly error messages",
      loadingStates: "Button disabled states during processing",
      successFeedback: "Modal confirmations for important actions"
    },
    
    naming: {
      handlers: "handle[Action] for event handlers (handleRejectSuggestion)",
      states: "Descriptive names (processingGroupId, not loading)",
      queries: "UPPERCASE_QUERY naming convention"
    }
  },

  // Business Rules
  businessRules: {
    staffDecisions: {
      requirement: "Staff member name required for all review actions",
      storage: "Staff name saved in localStorage for session persistence", 
      audit: "All decisions logged with timestamp and staff attribution"
    },
    
    groupLifecycle: {
      creation: "Auto-created groups start as SUGGESTED status",
      progression: "SUGGESTED → PENDING → READY_TO_MERGE → MERGED",
      rejection: "Groups can be rejected at SUGGESTED or PENDING status, creates permanent prevention records"
    },
    
    dataValidation: {
      primaryDonor: "Required for merge operations",
      kpfaIds: "Must be valid integers, unique within system",
      phoneNumbers: "Normalized for comparison, displayed with formatting"
    }
  },

  // System Architecture & Data Flow
  systemArchitecture: {
    overview: "Legacy PHP station admin as source of truth with Keystone as specialized duplicate management layer",
    
    dataFlow: {
      donorCreation: "Donors created and managed in legacy PHP station admin → donor data synced to Keystone for duplicate detection purposes",
      duplicateDetection: "Keystone reads donor data from PHP → detects phone matches → creates DuplicateGroup (SUGGESTED) → staff reviews in Next.js → decisions stored in Keystone",
      staffWorkflow: "Staff use Next.js admin UI → make duplicate decisions via GraphQL to Keystone → Keystone manages duplicate groups and rejection records",
      dataSync: "One-way sync from PHP to Keystone for donor data, Keystone handles all duplicate-related data independently"
    },

    componentRoles: {
      legacyPhpApi: "Source of truth for all donor data, handles donations/campaigns/receipts, manages kpfaId assignments, primary donor management interface",
      keystoneBackend: "Specialized duplicate management service, reads donor data from PHP, handles duplicate detection/groups/rejections, GraphQL API for Next.js",
      nextAdminUI: "Modern staff interface specifically for duplicate management workflow, connects only to Keystone GraphQL",
      stationAdminLegacy: "Primary donor management interface, where all donor CRUD operations happen, source of truth for donor records"
    },

    integrationPoints: {
      phpToKeystone: "One-way donor data sync from PHP to Keystone, kpfaId serves as primary key, phone number data for duplicate detection",
      keystoneToNext: "GraphQL queries/mutations for all duplicate management operations, real-time updates for duplicate workflow",
      sharedDatabase: "Keystone reads donor tables from PHP database but manages its own duplicate-specific tables (DuplicateGroup, DonorPairDecision)",
      donorRecords: "PHP remains authoritative for donor data, Keystone only manages duplicate relationships and staff decisions about duplicates"
    }
  },

  // Testing Knowledge (from recent conversation)
  testing: {
    strategy: {
      businessLogic: "Focus on testing business logic separately from complex component rendering",
      componentTesting: "Simple unit tests for UI logic, avoid complex GraphQL component integration tests",
      authenticationMocking: "Mock authentication context completely including checkAuth function and access_level",
      errorHandling: "Test error scenarios and fallback states thoroughly"
    },
    
    patterns: {
      unitTests: "Test pure functions and business logic in isolation using simple input/output assertions",
      integrationTests: "Avoid complex integration tests that require full component rendering with authentication",
      mockingApproach: "Use vi.mock for external dependencies, provide complete mock implementations with all required functions",
      testStructure: "Organize tests by functionality domain (Staff Member Generation, Status Validation, etc.)"
    },
    
    commonIssues: [
      "AuthRequired component showing loading states in tests due to incomplete auth mocks",
      "Missing checkAuth function in authentication context mocks",
      "Missing access_level property in mock user objects", 
      "TypeScript errors from incomplete mock implementations",
      "Complex component tests timing out due to authentication flow"
    ],
    
    bestPractices: [
      "Test business logic separately from UI components",
      "Create comprehensive mocks that include all expected properties and functions",
      "Use TypeScript interfaces to ensure mock completeness",
      "Focus on testing the actual functionality rather than implementation details",
      "Group related tests into descriptive describe blocks"
    ]
  },

  // Auto-Suggested Functionality (from recent work)
  autoSuggestedFeatures: {
    workflow: {
      detection: "System automatically detects potential duplicates based on phone number matches",
      staffReview: "Staff review auto-suggested groups and can accept (move to pending), mark ready, or reject",
      actionTypes: ["Accept Suggestion", "Reject Suggestion", "Mark Ready to Merge"],
      statusProgression: "SUGGESTED → PENDING → READY_TO_MERGE (via staff actions)"
    },
    
    mutations: {
      directGraphQL: "Use GraphQL mutations directly instead of API routes for auto-suggested actions",
      staffMemberTracking: "All mutations require staffMember parameter from useAuth() context",
      errorRecovery: "Proper error handling with user-friendly messages and retry capabilities",
      cacheManagement: "Refetch relevant queries after mutations (suggested groups, main groups, ready groups)"
    },
    
    uiStates: [
      "processingGroupId for tracking which group is being processed",
      "Modal states for success/error feedback",
      "Button disabled states during processing",
      "Loading indicators with descriptive text"
    ],
    
    validationRules: [
      "Only show suggestion actions for groups with status === 'SUGGESTED'",
      "Require staff member authentication and Admin access level",
      "Validate group ID and staff member name before mutation",
      "Handle missing or invalid data gracefully"
    ]
  },

  // Authentication Patterns (from debugging session)
  authPatterns: {
    context: {
      setup: "Custom authentication context (useAuth) instead of NextAuth, provides user object and authentication state",
      mocking: "Must mock complete auth context including user, isAuthenticated, isLoading, checkAuth function",
      userAccess: "User must have access_level: 'Admin' to access duplicate management features",
      sessionManagement: "Authentication state persisted across page loads, checked on component mount"
    },
    
    authRequired: {
      purpose: "AuthRequired component wraps pages to enforce authentication and access control",
      implementation: "Shows loading state while checking auth, redirects non-authenticated users, validates access level",
      testingChallenges: "Component shows loading screen in tests if auth context not properly mocked",
      solutions: [
        "Mock complete useAuth return value including checkAuth function",
        "Set isLoading: false and isAuthenticated: true in mocks",
        "Include access_level: 'Admin' in mock user object",
        "Use vi.fn().mockResolvedValue(true) for checkAuth function"
      ]
    },
    
    commonErrors: [
      "checkAuth is not a function - missing function in auth context mock",
      "User stuck in loading state - isLoading not set to false in mock",
      "Access denied - missing access_level property in mock user",
      "Authentication context not found - useAuth hook not properly mocked"
    ],
    
    debugging: [
      "Check browser console for authentication errors",
      "Verify localStorage has proper staff member data",
      "Ensure mock user object has all required properties",
      "Test authentication flow in isolation from complex components"
    ]
  },

  // Knowledge Categories with Metadata
  categories: [
    {
      id: "business-domain",
      name: "Business Domain",
      description: "Core business logic, rules, and processes for donor duplicate management",
      items: [
        {
          id: "duplicate-detection",
          title: "Duplicate Detection Process",
          content: "businessLogic.duplicateDetection",
          category: "business-domain",
          tags: ["detection", "workflow", "automation"],
          priority: "high",
          lastUpdated: "2025-06-18",
          version: "1.2.0"
        },
        {
          id: "donor-merging", 
          title: "Donor Merging Process",
          content: "businessLogic.donorMerging",
          category: "business-domain", 
          tags: ["merging", "data-consolidation", "audit"],
          priority: "high",
          lastUpdated: "2025-06-18",
          version: "1.1.0"
        }
      ]
    },
    {
      id: "technical-implementation",
      name: "Technical Implementation", 
      description: "Code patterns, technical architecture, and implementation details",
      items: [
        {
          id: "graphql-patterns",
          title: "GraphQL Query and Mutation Patterns",
          content: "technicalPatterns",
          category: "technical-implementation",
          tags: ["graphql", "queries", "mutations", "state-management"],
          priority: "high", 
          lastUpdated: "2025-06-18",
          version: "1.3.0"
        },
        {
          id: "auto-suggested-implementation",
          title: "Auto-Suggested Features Implementation",
          content: "autoSuggestedFeatures",
          category: "technical-implementation",
          tags: ["auto-suggested", "mutations", "ui-states", "workflow"],
          priority: "high",
          lastUpdated: "2025-06-18", 
          version: "2.0.0"
        }
      ]
    },
    {
      id: "user-interface",
      name: "User Interface",
      description: "UI patterns, styling guidelines, and user experience standards", 
      items: [
        {
          id: "ui-patterns",
          title: "UI/UX Patterns and Components",
          content: "uiPatterns",
          category: "user-interface",
          tags: ["ui", "ux", "components", "styling", "tailwind"],
          priority: "medium",
          lastUpdated: "2025-06-18",
          version: "1.1.0"
        }
      ]
    },
    {
      id: "testing-qa",
      name: "Testing & QA",
      description: "Testing strategies, patterns, and quality assurance practices",
      items: [
        {
          id: "testing-strategy",
          title: "Testing Strategy and Patterns", 
          content: "testing",
          category: "testing-qa",
          tags: ["testing", "mocking", "unit-tests", "business-logic"],
          priority: "high",
          lastUpdated: "2025-06-18",
          version: "2.0.0"
        }
      ]
    },
    {
      id: "authentication-security",
      name: "Authentication & Security",
      description: "Authentication patterns, access control, and security considerations",
      items: [
        {
          id: "auth-patterns",
          title: "Authentication Patterns and Debugging",
          content: "authPatterns", 
          category: "authentication-security",
          tags: ["authentication", "access-control", "debugging", "testing"],
          priority: "high",
          lastUpdated: "2025-06-18",
          version: "2.0.0"
        }
      ]
    },
    {
      id: "system-architecture", 
      name: "System Architecture",
      description: "Overall system design, component integration, and data flow",
      items: [
        {
          id: "architecture-overview",
          title: "System Architecture and Data Flow",
          content: "systemArchitecture",
          category: "system-architecture", 
          tags: ["architecture", "data-flow", "integration", "php", "keystone", "nextjs"],
          priority: "high",
          lastUpdated: "2025-06-18",
          version: "1.2.0"
        }
      ]
    }
  ]
}; 