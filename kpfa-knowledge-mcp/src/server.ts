#!/usr/bin/env node

import { KPFA_KNOWLEDGE } from './knowledge.js';

// Simple MCP server implementation for local development
// This provides KPFA domain knowledge via stdout/stdin JSON-RPC

interface MCPRequest {
  id?: string | number;
  method: string;
  params?: any;
}

interface MCPResponse {
  id?: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

class KPFAKnowledgeServer {
  private handleRequest(request: MCPRequest): MCPResponse {
    const { id, method, params } = request;

    try {
      switch (method) {
        case 'initialize':
          return {
            id,
            result: {
              protocolVersion: '2024-11-05',
              capabilities: {
                resources: {},
                tools: {
                  search_knowledge: {
                    description: 'Search across all KPFA knowledge by keywords, tags, or categories'
                  },
                  get_by_category: {
                    description: 'Get all knowledge items for a specific category'
                  },
                  get_by_tags: {
                    description: 'Get knowledge items matching specific tags'
                  }
                },
                prompts: {}
              },
              serverInfo: {
                name: 'kpfa-knowledge-server',
                version: '2.0.0',
                description: 'Enhanced domain knowledge server for KPFA donor management system with categorization and search'
              }
            }
          };

        case 'resources/list':
          return {
            id,
            result: {
              resources: [
                {
                  uri: 'knowledge://kpfa/business-logic',
                  name: 'KPFA Business Logic',
                  description: 'Domain rules for duplicate detection and donor management',
                  mimeType: 'application/json'
                },
                {
                  uri: 'knowledge://kpfa/ui-patterns',
                  name: 'KPFA UI Patterns',
                  description: 'Consistent UI/UX patterns and styling guidelines',
                  mimeType: 'application/json'
                },
                {
                  uri: 'knowledge://kpfa/technical-patterns',
                  name: 'KPFA Technical Patterns',
                  description: 'Code patterns, GraphQL conventions, state management',
                  mimeType: 'application/json'
                },
                {
                  uri: 'knowledge://kpfa/code-style',
                  name: 'KPFA Code Style',
                  description: 'Coding conventions and style preferences',
                  mimeType: 'application/json'
                },
                {
                  uri: 'knowledge://kpfa/business-rules',
                  name: 'KPFA Business Rules',
                  description: 'Staff workflow rules and data validation requirements',
                  mimeType: 'application/json'
                },
                {
                  uri: 'knowledge://kpfa/system-architecture',
                  name: 'KPFA System Architecture',
                  description: 'How PHP API, Keystone backend, and Next.js frontend work together for duplicate management',
                  mimeType: 'application/json'
                },
                {
                  uri: 'knowledge://kpfa/testing',
                  name: 'KPFA Testing Patterns',
                  description: 'Testing strategies, mocking patterns, and QA best practices',
                  mimeType: 'application/json'
                },
                {
                  uri: 'knowledge://kpfa/auto-suggested-features',
                  name: 'KPFA Auto-Suggested Features',
                  description: 'Implementation details for auto-suggested duplicate group functionality',
                  mimeType: 'application/json'
                },
                {
                  uri: 'knowledge://kpfa/auth-patterns',
                  name: 'KPFA Authentication Patterns',
                  description: 'Authentication context, debugging, and testing patterns',
                  mimeType: 'application/json'
                },
                {
                  uri: 'knowledge://kpfa/categories',
                  name: 'KPFA Knowledge Categories',
                  description: 'Organized knowledge categories with metadata and tags',
                  mimeType: 'application/json'
                }
              ]
            }
          };

        case 'resources/read':
          const uri = params?.uri;
          if (!uri) {
            throw new Error('URI parameter is required');
          }

          let content: any;
          switch (uri) {
            case 'knowledge://kpfa/business-logic':
              content = KPFA_KNOWLEDGE.businessLogic;
              break;
            case 'knowledge://kpfa/ui-patterns':
              content = KPFA_KNOWLEDGE.uiPatterns;
              break;
            case 'knowledge://kpfa/technical-patterns':
              content = KPFA_KNOWLEDGE.technicalPatterns;
              break;
            case 'knowledge://kpfa/code-style':
              content = KPFA_KNOWLEDGE.codeStyle;
              break;
            case 'knowledge://kpfa/business-rules':
              content = KPFA_KNOWLEDGE.businessRules;
              break;
            case 'knowledge://kpfa/system-architecture':
              content = KPFA_KNOWLEDGE.systemArchitecture;
              break;
            case 'knowledge://kpfa/testing':
              content = KPFA_KNOWLEDGE.testing;
              break;
            case 'knowledge://kpfa/auto-suggested-features':
              content = KPFA_KNOWLEDGE.autoSuggestedFeatures;
              break;
            case 'knowledge://kpfa/auth-patterns':
              content = KPFA_KNOWLEDGE.authPatterns;
              break;
            case 'knowledge://kpfa/categories':
              content = KPFA_KNOWLEDGE.categories;
              break;
            default:
              throw new Error(`Resource not found: ${uri}`);
          }

          return {
            id,
            result: {
              contents: [{
                uri,
                mimeType: 'application/json',
                text: JSON.stringify(content, null, 2)
              }]
            }
          };

        case 'tools/call':
          const toolName = params?.name;
          const toolArgs = params?.arguments || {};
          
          switch (toolName) {
            case 'search_knowledge':
              return this.handleSearchKnowledge(id, toolArgs);
            case 'get_by_category':
              return this.handleGetByCategory(id, toolArgs);
            case 'get_by_tags':
              return this.handleGetByTags(id, toolArgs);
            default:
              throw new Error(`Unknown tool: ${toolName}`);
          }

        default:
          throw new Error(`Unknown method: ${method}`);
      }
    } catch (error) {
      return {
        id,
        error: {
          code: -32603,
          message: 'Internal error',
          data: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  private handleSearchKnowledge(id: string | number | undefined, args: any): MCPResponse {
    const { query, includeContent = false } = args;
    
    if (!query) {
      throw new Error('Query parameter is required');
    }

    const searchTerm = query.toLowerCase();
    const results: any[] = [];

    // Search through all categories and items
    for (const category of KPFA_KNOWLEDGE.categories) {
      for (const item of category.items) {
        const matchScore = this.calculateMatchScore(item, searchTerm);
        if (matchScore > 0) {
          const result: any = {
            ...item,
            categoryName: category.name,
            matchScore
          };
          
          if (includeContent) {
            result.actualContent = this.getContentByPath(item.content);
          }
          
          results.push(result);
        }
      }
    }

    // Sort by match score (highest first)
    results.sort((a, b) => b.matchScore - a.matchScore);

    return {
      id,
      result: {
        query: searchTerm,
        totalResults: results.length,
        results: results.slice(0, 10) // Limit to top 10 results
      }
    };
  }

  private handleGetByCategory(id: string | number | undefined, args: any): MCPResponse {
    const { categoryId, includeContent = false } = args;
    
    if (!categoryId) {
      throw new Error('CategoryId parameter is required');
    }

    const category = KPFA_KNOWLEDGE.categories.find(cat => cat.id === categoryId);
    if (!category) {
      throw new Error(`Category not found: ${categoryId}`);
    }

    const items = category.items.map(item => {
      const result: any = { ...item };
      if (includeContent) {
        result.actualContent = this.getContentByPath(item.content);
      }
      return result;
    });

    return {
      id,
      result: {
        category: {
          id: category.id,
          name: category.name,
          description: category.description
        },
        items
      }
    };
  }

  private handleGetByTags(id: string | number | undefined, args: any): MCPResponse {
    const { tags, includeContent = false } = args;
    
    if (!tags || !Array.isArray(tags)) {
      throw new Error('Tags parameter is required and must be an array');
    }

    const results: any[] = [];

    for (const category of KPFA_KNOWLEDGE.categories) {
      for (const item of category.items) {
        const hasMatchingTag = tags.some(tag => 
          item.tags.some(itemTag => itemTag.toLowerCase().includes(tag.toLowerCase()))
        );
        
        if (hasMatchingTag) {
          const result: any = {
            ...item,
            categoryName: category.name
          };
          
          if (includeContent) {
            result.actualContent = this.getContentByPath(item.content);
          }
          
          results.push(result);
        }
      }
    }

    return {
      id,
      result: {
        tags,
        totalResults: results.length,
        results
      }
    };
  }

  private calculateMatchScore(item: any, searchTerm: string): number {
    let score = 0;
    
    // Title match (highest weight)
    if (item.title.toLowerCase().includes(searchTerm)) {
      score += 10;
    }
    
    // Tag matches (medium weight)
    for (const tag of item.tags) {
      if (tag.toLowerCase().includes(searchTerm)) {
        score += 5;
      }
    }
    
    // Category match (low weight)
    if (item.category.toLowerCase().includes(searchTerm)) {
      score += 2;
    }
    
    return score;
  }

  private getContentByPath(contentPath: string): any {
    const parts = contentPath.split('.');
    let current: any = KPFA_KNOWLEDGE;
    
    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return null;
      }
    }
    
    return current;
  }

  public start(): void {
    console.error('KPFA Knowledge MCP Server v2.0 starting...');
    
    process.stdin.setEncoding('utf8');
    
    let buffer = '';
    
    process.stdin.on('data', (chunk: string) => {
      buffer += chunk;
      
      // Process complete JSON-RPC messages
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // Keep incomplete line in buffer
      
      for (const line of lines) {
        if (line.trim()) {
          try {
            const request: MCPRequest = JSON.parse(line);
            const response = this.handleRequest(request);
            console.log(JSON.stringify(response));
          } catch (error) {
            console.log(JSON.stringify({
              error: {
                code: -32700,
                message: 'Parse error',
                data: error instanceof Error ? error.message : 'Invalid JSON'
              }
            }));
          }
        }
      }
    });

    process.stdin.on('end', () => {
      console.error('KPFA Knowledge MCP Server stopping...');
      process.exit(0);
    });

    process.on('SIGINT', () => {
      console.error('KPFA Knowledge MCP Server interrupted');
      process.exit(0);
    });

    console.error('KPFA Knowledge MCP Server ready for requests (enhanced with search and categorization)');
  }
}

// Start the server
const server = new KPFAKnowledgeServer();
server.start(); 