// KPFA Domain Types

// Knowledge Organization Types
export interface KnowledgeItem {
  id: string;
  title: string;
  content: any;
  category: string;
  tags: string[];
  priority: 'high' | 'medium' | 'low';
  lastUpdated: string;
  version: string;
}

export interface KnowledgeCategory {
  id: string;
  name: string;
  description: string;
  items: KnowledgeItem[];
}

export interface DuplicateDetectionProcess {
  process: string;
  statuses: Record<string, string>;
  autoCreationRules: string;
  rejectionLogic: string;
}

export interface DonorMerging {
  primaryDonorSelection: string;
  dataConsolidation: string;
  auditTrail: string;
}

export interface DataIntegrity {
  kpfaId: string;
  phoneNormalization: string;
  pairDecisions: string;
}

export interface BusinessLogic {
  duplicateDetection: DuplicateDetectionProcess;
  donorMerging: DonorMerging;
  dataIntegrity: DataIntegrity;
}

export interface ActionButtons {
  confirm: string;
  reject: string;
  view: string;
  neutral: string;
}

export interface StatusBadges {
  primary: string;
  warning: string;
  info: string;
}

export interface LoadingStates {
  pattern: string;
  spinners: string;
}

export interface ConditionalRendering {
  autoCreatedGroups: string;
  emptyGroups: string;
  statusSpecific: string;
}

export interface UIPatterns {
  actionButtons: ActionButtons;
  statusBadges: StatusBadges;
  loadingStates: LoadingStates;
  conditionalRendering: ConditionalRendering;
}

export interface GraphQLQueries {
  alwaysInclude: string[];
  auditFields: string[];
  relationships: string;
}

export interface Mutations {
  staffTracking: string;
  optimisticUpdates: string;
  refetchQueries: string;
}

export interface StateManagement {
  processingStates: string;
  modalStates: string;
  searchAndPagination: string;
}

export interface TechnicalPatterns {
  graphqlQueries: GraphQLQueries;
  mutations: Mutations;
  stateManagement: StateManagement;
}

export interface Components {
  structure: string;
  props: string;
  styling: string;
}

export interface MutationStyle {
  errorHandling: string;
  loadingStates: string;
  successFeedback: string;
}

export interface Naming {
  handlers: string;
  states: string;
  queries: string;
}

export interface CodeStyle {
  components: Components;
  mutations: MutationStyle;
  naming: Naming;
}

export interface StaffDecisions {
  requirement: string;
  storage: string;
  audit: string;
}

export interface GroupLifecycle {
  creation: string;
  progression: string;
  rejection: string;
}

export interface DataValidation {
  primaryDonor: string;
  kpfaIds: string;
  phoneNumbers: string;
}

export interface BusinessRules {
  staffDecisions: StaffDecisions;
  groupLifecycle: GroupLifecycle;
  dataValidation: DataValidation;
}

// New Testing Types
export interface TestingStrategy {
  businessLogic: string;
  componentTesting: string;
  authenticationMocking: string;
  errorHandling: string;
}

export interface TestPatterns {
  unitTests: string;
  integrationTests: string;
  mockingApproach: string;
  testStructure: string;
}

export interface Testing {
  strategy: TestingStrategy;
  patterns: TestPatterns;
  commonIssues: string[];
  bestPractices: string[];
}

// New Auto-Suggested Functionality Types
export interface AutoSuggestedWorkflow {
  detection: string;
  staffReview: string;
  actionTypes: string[];
  statusProgression: string;
}

export interface MutationPatterns {
  directGraphQL: string;
  staffMemberTracking: string;
  errorRecovery: string;
  cacheManagement: string;
}

export interface AutoSuggestedFeatures {
  workflow: AutoSuggestedWorkflow;
  mutations: MutationPatterns;
  uiStates: string[];
  validationRules: string[];
}

// New Authentication Patterns
export interface AuthenticationContext {
  setup: string;
  mocking: string;
  userAccess: string;
  sessionManagement: string;
}

export interface AuthRequired {
  purpose: string;
  implementation: string;
  testingChallenges: string;
  solutions: string[];
}

export interface AuthPatterns {
  context: AuthenticationContext;
  authRequired: AuthRequired;
  commonErrors: string[];
  debugging: string[];
}

export interface KPFAKnowledge {
  businessLogic: BusinessLogic;
  uiPatterns: UIPatterns;
  technicalPatterns: TechnicalPatterns;
  codeStyle: CodeStyle;
  businessRules: BusinessRules;
  systemArchitecture: SystemArchitecture;
  testing: Testing;
  autoSuggestedFeatures: AutoSuggestedFeatures;
  authPatterns: AuthPatterns;
  categories: KnowledgeCategory[];
}

export interface SystemArchitecture {
  overview: string;
  dataFlow: DataFlow;
  componentRoles: ComponentRoles;
  integrationPoints: IntegrationPoints;
}

export interface DataFlow {
  donorCreation: string;
  duplicateDetection: string;
  staffWorkflow: string;
  dataSync: string;
}

export interface ComponentRoles {
  legacyPhpApi: string;
  keystoneBackend: string;
  nextAdminUI: string;
  stationAdminLegacy: string;
}

export interface IntegrationPoints {
  phpToKeystone: string;
  keystoneToNext: string;
  sharedDatabase: string;
  donorRecords: string;
} 