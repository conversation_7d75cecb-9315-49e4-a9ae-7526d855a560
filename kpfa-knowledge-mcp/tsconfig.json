{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "declaration": true, "declarationMap": true, "sourceMap": true, "lib": ["ES2022"], "types": ["node"], "moduleDetection": "force"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": true}}