# Test Mode Setup for Payment Reconciliation

This document explains how to set up test mode for payment reconciliation testing with Stripe test keys and the staging API.

## Automatic Test Mode Detection

The Next.js admin application automatically detects when you're running on localhost and switches to test mode:

- **Localhost (http://localhost:3001)**: Automatically uses `api.staging.kpfa.org`
- **Production domains**: Uses `api.kpfa.org`

## Visual Indicators

When running in test mode, you'll see:
- 🧪 **"TEST MODE"** badge in the navigation bar (yellow, animated)
- Console logs showing environment info and API URLs being used

## Setting Up Stripe Test Keys

1. **Add test keys to `.env.local`**:
   ```bash
   # Stripe Test Keys for localhost development
   STRIPE_SECRET_KEY=sk_test_your_test_key_here
   STRIPE_PUBLISHABLE_KEY=pk_test_your_test_key_here
   ```

2. **Get your test keys from Stripe Dashboard**:
   - Go to [Stripe Dashboard](https://dashboard.stripe.com/test/apikeys)
   - Make sure you're in "Test mode" (toggle in the left sidebar)
   - Copy the "Secret key" and "Publishable key"

## Testing Payment Reconciliation

### Prerequisites
- Next.js app running on localhost (`npm run dev`)
- Stripe test keys configured in `.env.local`
- Access to `api.staging.kpfa.org` (staging Station Admin API)

### Test Workflow
1. **Create test payments** in Stripe test mode
2. **Navigate to Payment Reconciliation** in the Next.js admin
3. **Search for unmatched payments** in the date range
4. **Click "Reconcile"** on eligible payments
5. **Complete the reconciliation workflow**
6. **Verify records** are created in staging database

### What Happens in Test Mode
- ✅ Uses `api.staging.kpfa.org` for Station Admin API calls
- ✅ Uses Stripe test keys for payment data
- ✅ Creates records in staging database
- ✅ Safe to test without affecting production data

### Console Logging
Test mode provides helpful console output:
```
🌍 Environment Info: {
  isTestMode: true,
  hostname: "localhost",
  apiUrl: "https://api.staging.kpfa.org",
  nodeEnv: "development"
}

🧪 Test Mode Features:
  • Using api.staging.kpfa.org for Station Admin API
  • Add STRIPE_SECRET_KEY and STRIPE_PUBLISHABLE_KEY to .env.local for Stripe test keys
  • Payment reconciliation will work with staging database

🔄 Starting payment reconciliation: {
  environment: { ... },
  stripeChargeId: "ch_test_...",
  donorId: 12345,
  donorName: "Test Donor"
}
```

## Production Deployment

When deployed to production domains:
- Automatically uses `api.kpfa.org` (production Station Admin API)
- Uses production Stripe keys from environment variables
- No test mode indicators shown
- Production-safe behavior

## Troubleshooting

### "No Stripe keys configured" errors
- Make sure test keys are added to `.env.local`
- Restart the Next.js development server after adding keys

### "API connection failed" errors
- Verify you have access to `api.staging.kpfa.org`
- Check that your JWT token is valid for staging environment

### "Payment not found" errors
- Ensure you're using the correct Stripe test environment
- Verify the payment exists in your Stripe test dashboard

## Files Modified for Test Mode

- `src/lib/testMode.ts` - Test mode detection utilities
- `src/lib/config.ts` - Automatic API URL switching
- `src/lib/api.ts` - API base URL configuration
- `src/lib/apolloClient.ts` - GraphQL client configuration
- `src/components/Navigation.tsx` - Test mode indicator (existing)
- `src/components/ReconcilePaymentModal.tsx` - Environment logging
