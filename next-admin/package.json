{"name": "next-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:auto-suggested": "vitest run src/__tests__/utils/auto-suggested-actions.test.ts", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "upload": "opennextjs-cloudflare build && opennextjs-cloudflare upload", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts"}, "dependencies": {"@apollo/client": "^3.13.8", "@headlessui/react": "^2.2.4", "@opennextjs/cloudflare": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "graphql": "^16.11.0", "lucide-react": "^0.525.0", "next": "15.3.1", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3.0.0", "@eslint/js": "^8.57.0", "@tailwindcss/postcss": "^4.1.8", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.11.30", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.3.1", "@vitest/ui": "^1.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "^15.3.1", "jsdom": "^24.0.0", "postcss": "^8.4.35", "tailwindcss": "^4.1.8", "typescript": "^5.4.2", "vitest": "^1.3.1", "wrangler": "^4.19.1"}}