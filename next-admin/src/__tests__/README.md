# KPFA Admin Test Suite

This directory contains comprehensive tests for the KPFA Admin Next.js application, covering all major functionalities including authentication, donor management, duplicate detection, and GraphQL operations.

## Test Structure

### Test Types

1. **Unit Tests** - Individual component and function testing
2. **Integration Tests** - Testing interactions between components and services
3. **API Tests** - Testing API routes and GraphQL operations
4. **Hook Tests** - Testing custom React hooks

### Directory Structure

```
__tests__/
├── components/           # Component tests
│   ├── Navigation.test.tsx
│   └── DonorCard.test.tsx
├── pages/               #           Page component tests
│   └── HomePage.test.tsx
├── api/                 # API route tests
│   └── auth.test.ts
├── lib/                 # Library and utility tests
│   ├── utils.test.ts
│   └── hooks/
│       └── usePagination.test.ts
├── integration/         # Integration tests
│   └── apollo-client.test.tsx
├── setup/               # Test utilities and helpers
│   └── test-utils.tsx
└── README.md           # This file
```

## Running Tests

### Basic Commands

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

### Running Specific Tests

```bash
# Run tests for a specific component
npm run test -- Navigation

# Run tests matching a pattern
npm run test -- --grep "authentication"

# Run tests in a specific directory
npm run test -- __tests__/components

# Run remove donor functionality tests
npm run test -- --grep "Remove Donor"

# Run duplicate group detail tests
npm run test -- DuplicateGroupDetail
```

## Test Categories

### 1. Authentication Tests

**Location**: `components/AuthRequired.test.tsx`, `api/auth.test.ts`

Tests cover:
- JWT token validation
- Admin access level enforcement
- Redirect behavior for unauthorized users
- Loading states during authentication
- Error handling for invalid tokens

### 2. Navigation Tests

**Location**: `components/Navigation.test.tsx`

Tests cover:
- User avatar and initials display
- Environment indicator (TEST MODE)
- Navigation links and routing
- Logout functionality
- Responsive behavior

### 3. Donor Management Tests

**Location**: `components/DonorCard.test.tsx`

Tests cover:
- Donor information display
- Phone number formatting
- Address handling (partial/complete)
- Status indicators (deceased, do not solicit)
- External ID display (Stripe, PayPal, etc.)

### 4. Pagination Tests

**Location**: `lib/hooks/usePagination.test.ts`

Tests cover:
- Page navigation (next, previous, goto)
- Items per page configuration
- URL synchronization
- Boundary conditions
- State reset functionality

### 5. GraphQL Integration Tests

**Location**: `integration/apollo-client.test.tsx`

Tests cover:
- Apollo Client configuration
- Query structure validation
- Mutation structure validation
- Authentication header injection
- Error handling configuration

### 6. Auto-Suggested Duplicate Groups Tests

**Location**: `components/DuplicateDonorsClient.test.tsx`, `pages/DuplicateGroupDetail.test.tsx`, `api/auto-suggested-mutations.test.ts`

Tests cover:
- Auto-suggested group display and UI interactions
- "Move to Pending" and "Mark Ready" actions
- Suggestion rejection workflow
- Mark donor pairs as not duplicates functionality
- Staff member tracking in mutations
- Error handling for all suggestion actions
- GraphQL mutation validation and responses
- Loading states and processing indicators

### 7. Remove Donor from Group Tests

**Location**: `pages/DuplicateGroupDetail.test.tsx`, `api/remove-donor-mutations.test.ts`

Tests cover:
- Remove donor button display logic (only for non-merged groups with multiple donors)
- Successful donor removal from duplicate groups
- Primary donor removal and automatic reassignment
- Group status updates when reduced to single donor
- UI state management during removal operations
- Error handling for removal failures
- GraphQL mutation structure and variable validation
- Edge cases (removing primary donors, single donor scenarios)
- Success and error modal displays
- Button styling and accessibility features

## Test Utilities

### Mock Data Factories

```typescript
import { 
  createMockDonor, 
  createMockDuplicateGroup, 
  createMockAuthUser 
} from '@/__tests__/setup/test-utils';

// Create mock donor with defaults
const donor = createMockDonor();

// Create mock donor with overrides
const customDonor = createMockDonor({
  firstname: 'Jane',
  lastname: 'Smith',
  email: '<EMAIL>'
});
```

### Custom Render Function

```typescript
import { render } from '@/__tests__/setup/test-utils';

// Render with all providers (Apollo, Auth, Theme)
render(<MyComponent />, {
  apolloMocks: [mockQuery],
  mockUser: createMockAuthUser()
});
```

### Common Mocks

```typescript
import { 
  mockLocalStorage, 
  mockWindowLocation 
} from '@/__tests__/setup/test-utils';

// Mock localStorage
const localStorage = mockLocalStorage();

// Mock window.location
mockWindowLocation('localhost'); // or 'admin.kpfa.org'
```

## Testing Patterns

### 1. Component Testing Pattern

```typescript
import { render, screen } from '@/__tests__/setup/test-utils';
import { describe, test, expect, vi } from 'vitest';

describe('ComponentName', () => {
  test('renders correctly', () => {
    render(<ComponentName />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
  
  test('handles user interaction', async () => {
    const user = userEvent.setup();
    render(<ComponentName />);
    
    await user.click(screen.getByRole('button'));
    expect(mockFunction).toHaveBeenCalled();
  });
});
```

### 2. GraphQL Testing Pattern

```typescript
import { MockedProvider } from '@apollo/client/testing';
import { render, waitFor } from '@testing-library/react';

const mocks = [{
  request: { query: MY_QUERY },
  result: { data: { field: 'value' } }
}];

test('GraphQL query', async () => {
  render(
    <MockedProvider mocks={mocks}>
      <ComponentWithQuery />
    </MockedProvider>
  );
  
  await waitFor(() => {
    expect(screen.getByText('value')).toBeInTheDocument();
  });
});
```

### 3. Hook Testing Pattern

```typescript
import { renderHook, act } from '@testing-library/react';

test('custom hook behavior', () => {
  const { result } = renderHook(() => useCustomHook());
  
  act(() => {
    result.current.someAction();
  });
  
  expect(result.current.state).toBe('expected');
});
```

## Coverage Goals

The test suite aims for:
- **90%+ line coverage** for business logic
- **100% coverage** for utility functions
- **85%+ coverage** for components
- **95%+ coverage** for API routes

## Best Practices

### 1. Test Organization
- Group related tests in `describe` blocks
- Use descriptive test names that explain the behavior
- Keep tests focused and test one thing at a time

### 2. Mocking Strategy
- Mock external dependencies (APIs, localStorage, etc.)
- Use factory functions for consistent mock data
- Mock at the module level when appropriate

### 3. Async Testing
- Always use `waitFor` for async operations
- Use `findBy*` queries for elements that appear asynchronously
- Handle loading states in tests

### 4. Error Testing
- Test error boundaries and error states
- Test validation and error messages
- Test network failure scenarios

### 5. Accessibility Testing
- Use semantic queries (`getByRole`, `getByLabelText`)
- Test keyboard navigation
- Verify ARIA attributes when relevant

## Debugging Tests

### Common Issues

1. **Hydration Mismatches**: Mock window/document objects properly
2. **Async Operations**: Use `waitFor` and `findBy*` queries
3. **GraphQL Mocks**: Ensure query variables match exactly
4. **Component Dependencies**: Mock all external dependencies

### Debug Commands

```bash
# Run tests with verbose output
npm run test -- --reporter=verbose

# Run a single test file with debugging
npm run test -- --no-coverage Navigation.test.tsx

# Open test UI for interactive debugging
npm run test:ui
```

## Contributing to Tests

When adding new features:

1. **Write tests first** (TDD approach recommended)
2. **Test both happy path and edge cases**
3. **Include error scenarios**
4. **Update this README** if adding new patterns
5. **Maintain or improve coverage**

For the Remove Donor functionality specifically, ensure tests cover:
- UI conditionals (button visibility based on group status and donor count)
- GraphQL mutation variable validation
- Primary donor reassignment logic
- Group status transitions
- Error handling and user feedback

### Adding New Test Files

1. Create test file in appropriate directory
2. Follow naming convention: `ComponentName.test.tsx`
3. Import required testing utilities
4. Add to any relevant test suites
5. Update coverage reports

## Performance Considerations

- Use `vi.fn()` for mock functions instead of jest.fn()
- Minimize DOM queries by storing references
- Use `cleanup` after tests when needed
- Avoid unnecessary re-renders in test setup

## Resources

- [Vitest Documentation](https://vitest.dev/)
- [Testing Library Docs](https://testing-library.com/)
- [Apollo Client Testing](https://www.apollographql.com/docs/react/development-testing/testing/)
- [Next.js Testing Guide](https://nextjs.org/docs/testing) 