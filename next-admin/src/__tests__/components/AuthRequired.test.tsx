import { render, screen, waitFor, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import AuthRequired from '@/components/AuthRequired';
import { useAuth } from '@/lib/authContext';

// Mock auth context
vi.mock('@/lib/authContext', () => ({
  useAuth: vi.fn()
}));

// Mock window.location
const mockLocation = {
  href: '',
  hostname: 'localhost',
  assign: vi.fn()
};

describe('AuthRequired Component', () => {
  const mockCheckAuth = vi.fn();
  const mockLogout = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset location mock
    mockLocation.href = '';
    mockLocation.hostname = 'localhost';
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('shows loading state when authentication is loading', async () => {
    vi.mocked(useAuth).mockReturnValue({
      user: null,
      isLoading: true,
      isAuthenticated: false,
      error: null,
      checkAuth: mockCheckAuth,
      logout: mockLogout
    });

    await act(async () => {
      render(
        <AuthRequired>
          <div>Protected Content</div>
        </AuthRequired>
      );
    });

    expect(screen.getByText('Verifying authentication...')).toBeInTheDocument();
  });

  it('renders children when user is authenticated and has Admin access', async () => {
    vi.mocked(useAuth).mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        firstname: 'Admin',
        lastname: 'User',
        access_level: 'Admin'
      },
      isLoading: false,
      isAuthenticated: true,
      error: null,
      checkAuth: mockCheckAuth,
      logout: mockLogout
    });

    await act(async () => {
      render(
        <AuthRequired>
          <div>Protected Content</div>
        </AuthRequired>
      );
    });

    await waitFor(() => {
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });
  });

  it('calls checkAuth on initial mount', async () => {
    vi.mocked(useAuth).mockReturnValue({
      user: null,
      isLoading: true,
      isAuthenticated: false,
      error: null,
      checkAuth: mockCheckAuth,
      logout: mockLogout
    });

    await act(async () => {
      render(
        <AuthRequired>
          <div>Protected Content</div>
        </AuthRequired>
      );
    });

    expect(mockCheckAuth).toHaveBeenCalledTimes(1);
  });

  it('redirects to admin.kpfa.org with unauthorized error for non-admin users', async () => {
    vi.mocked(useAuth).mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        firstname: 'Regular',
        lastname: 'User',
        access_level: 'User'
      },
      isLoading: false,
      isAuthenticated: true,
      error: null,
      checkAuth: mockCheckAuth,
      logout: mockLogout
    });

    await act(async () => {
      render(
        <AuthRequired>
          <div>Protected Content</div>
        </AuthRequired>
      );
    });

    await waitFor(() => {
      expect(mockLocation.href).toBe('https://admin.kpfa.org?error=unauthorized');
    });
  });

  it('redirects to admin.kpfa.org when not authenticated', async () => {
    vi.mocked(useAuth).mockReturnValue({
      user: null,
      isLoading: false,
      isAuthenticated: false,
      error: null,
      checkAuth: mockCheckAuth,
      logout: mockLogout
    });

    await act(async () => {
      render(
        <AuthRequired>
          <div>Protected Content</div>
        </AuthRequired>
      );
    });

    await waitFor(() => {
      expect(mockLocation.href).toBe('https://admin.kpfa.org');
    });
  });

  it('handles authentication error gracefully', async () => {
    vi.mocked(useAuth).mockReturnValue({
      user: null,
      isLoading: false,
      isAuthenticated: false,
      error: 'Invalid credentials',
      checkAuth: mockCheckAuth,
      logout: mockLogout
    });

    await act(async () => {
      render(
        <AuthRequired>
          <div>Protected Content</div>
        </AuthRequired>
      );
    });

    // Should still redirect since user is not authenticated
    await waitFor(() => {
      expect(mockLocation.href).toBe('https://admin.kpfa.org');
    });
  });
}); 