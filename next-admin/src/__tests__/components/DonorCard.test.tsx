import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { DonorCard } from '@/components/DonorCard';
import { Donor } from '@/types';

const mockDonor: Donor = {
  id: '123',
  kpfaId: 123,
  firstname: '<PERSON>',
  lastname: '<PERSON><PERSON>',
  email: '<EMAIL>',
  phone: '************',
  address1: '123 Main St',
  address2: 'Apt 4B',
  city: 'San Francisco',
  state: 'CA',
  postal_code: '94102',
  country: 'USA'
};

describe('DonorCard', () => {
  it('renders donor information correctly', () => {
    render(
      <DonorCard 
        donor={mockDonor} 
        title="Primary Donor" 
      />
    );

    expect(screen.getByText('Primary Donor')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('ID: 123')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('************')).toBeInTheDocument();
    expect(screen.getByText('123 Main St')).toBeInTheDocument();
    expect(screen.getByText('Apt 4B')).toBeInTheDocument();
    expect(screen.getByText('San Francisco, CA, 94102')).toBeInTheDocument();
  });

  it('shows clear button when editable and onClear provided', () => {
    const onClear = vi.fn();
    render(
      <DonorCard 
        donor={mockDonor} 
        title="Primary Donor" 
        onClear={onClear}
        isEditable={true}
      />
    );

    const clearButton = screen.getByText('Clear');
    expect(clearButton).toBeInTheDocument();
    
    fireEvent.click(clearButton);
    expect(onClear).toHaveBeenCalledTimes(1);
  });

  it('hides clear button when not editable', () => {
    const onClear = vi.fn();
    render(
      <DonorCard 
        donor={mockDonor} 
        title="Primary Donor" 
        onClear={onClear}
        isEditable={false}
      />
    );

    expect(screen.queryByText('Clear')).not.toBeInTheDocument();
  });

  it('handles missing optional fields gracefully', () => {
    const minimalDonor: Donor = {
      id: '456',
      kpfaId: 456,
      firstname: 'Jane',
      lastname: 'Smith'
    };

    render(
      <DonorCard 
        donor={minimalDonor} 
        title="Minimal Donor" 
      />
    );

    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('ID: 456')).toBeInTheDocument();
    expect(screen.queryByText('Email:')).not.toBeInTheDocument();
    expect(screen.queryByText('Phone:')).not.toBeInTheDocument();
    expect(screen.queryByText('Address:')).not.toBeInTheDocument();
  });

  it('shows international country when not USA', () => {
    const internationalDonor: Donor = {
      ...mockDonor,
      country: 'Canada'
    };

    render(
      <DonorCard 
        donor={internationalDonor} 
        title="International Donor" 
      />
    );

    expect(screen.getByText('Canada')).toBeInTheDocument();
  });

  it('returns null when donor is not provided', () => {
    const { container } = render(
      <DonorCard 
        donor={null as any} 
        title="No Donor" 
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('handles partial address information', () => {
    const partialAddressDonor: Donor = {
      id: '789',
      kpfaId: 789,
      firstname: 'Bob',
      lastname: 'Wilson',
      city: 'Oakland',
      state: 'CA'
    };

    render(
      <DonorCard 
        donor={partialAddressDonor} 
        title="Partial Address" 
      />
    );

    expect(screen.getByText('Address:')).toBeInTheDocument();
    expect(screen.getByText('Oakland, CA')).toBeInTheDocument();
  });
}); 