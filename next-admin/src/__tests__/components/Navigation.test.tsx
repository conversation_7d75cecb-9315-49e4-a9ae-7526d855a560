import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { usePathname } from 'next/navigation';
import Navigation from '@/components/Navigation';
import { useAuth } from '@/lib/authContext';

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  usePathname: vi.fn()
}));

// Mock auth context
vi.mock('@/lib/authContext', () => ({
  useAuth: vi.fn()
}));

// Mock window location
Object.defineProperty(window, 'location', {
  value: {
    hostname: 'localhost'
  },
  writable: true
});

describe('Navigation', () => {
  const mockLogout = vi.fn();
  const mockCheckAuth = vi.fn();

  const createMockAuthContext = (overrides: any = {}) => ({
    user: null,
    isLoading: false,
    isAuthenticated: false,
    error: null,
    checkAuth: mockCheckAuth,
    logout: mockLogout,
    ...overrides
  });

  const createMockUser = (overrides: any = {}) => ({
    id: '1',
    email: '<EMAIL>',
    firstname: 'John',
    lastname: 'Doe',
    access_level: 'admin',
    ...overrides
  });

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(usePathname).mockReturnValue('/');
  });

  it('renders KPFA Admin branding', () => {
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext());

    render(<Navigation />);

    expect(screen.getByText('KPFA')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
  });

  it('shows test mode indicator on localhost', () => {
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext());

    render(<Navigation />);

    expect(screen.getByText('TEST MODE')).toBeInTheDocument();
  });

  it('shows navigation links when authenticated', () => {
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext({
      user: createMockUser(),
      isAuthenticated: true
    }));

    render(<Navigation />);

    expect(screen.getByText('Duplicates')).toBeInTheDocument();
    expect(screen.getByText('Merge History')).toBeInTheDocument();
  });

  it('highlights active navigation link', () => {
    vi.mocked(usePathname).mockReturnValue('/duplicate-donors');
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext({
      user: createMockUser(),
      isAuthenticated: true
    }));

    render(<Navigation />);

    const duplicatesLink = screen.getByText('Duplicates');
    expect(duplicatesLink).toHaveClass('text-primary', 'font-medium');
  });

  it('shows user avatar with initials when authenticated', () => {
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext({
      user: createMockUser(),
      isAuthenticated: true
    }));

    render(<Navigation />);

    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  it('shows logout button when authenticated', () => {
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext({
      user: createMockUser(),
      isAuthenticated: true
    }));

    render(<Navigation />);

    const logoutButton = screen.getByText('Logout');
    expect(logoutButton).toBeInTheDocument();

    fireEvent.click(logoutButton);
    expect(mockLogout).toHaveBeenCalledTimes(1);
  });

  it('does not show user controls when not authenticated', () => {
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext());

    render(<Navigation />);

    expect(screen.queryByText('Logout')).not.toBeInTheDocument();
  });

  it('handles user with only first name', () => {
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext({
      user: createMockUser({ lastname: '' }),
      isAuthenticated: true
    }));

    render(<Navigation />);

    expect(screen.getByText('J')).toBeInTheDocument();
  });

  it('handles user with only last name', () => {
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext({
      user: createMockUser({ firstname: '' }),
      isAuthenticated: true
    }));

    render(<Navigation />);

    expect(screen.getByText('D')).toBeInTheDocument();
  });

  it('does not show test mode on production hostname', () => {
    Object.defineProperty(window, 'location', {
      value: {
        hostname: 'admin.kpfa.org'
      },
      writable: true
    });

    vi.mocked(useAuth).mockReturnValue(createMockAuthContext());

    render(<Navigation />);

    expect(screen.queryByText('TEST MODE')).not.toBeInTheDocument();
  });

  it('applies sticky positioning and backdrop blur classes', () => {
    vi.mocked(useAuth).mockReturnValue(createMockAuthContext());

    const { container } = render(<Navigation />);
    const nav = container.querySelector('nav');

    expect(nav).toHaveClass('sticky', 'top-0', 'backdrop-blur-sm');
  });
}); 