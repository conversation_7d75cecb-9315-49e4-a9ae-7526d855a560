import { renderHook, act } from '@testing-library/react';
import { describe, test, expect } from 'vitest';
import { usePagination } from '@/lib/hooks/usePagination';

describe('usePagination hook', () => {
  test('initializes with default values', () => {
    const { result } = renderHook(() => usePagination());
    
    expect(result.current.paginationState.currentPage).toBe(1);
    expect(result.current.paginationState.itemsPerPage).toBe(20);
    expect(result.current.paginationState.skip).toBe(0);
    expect(result.current.paginationState.take).toBe(20);
  });

  test('initializes with custom initial values', () => {
    const { result } = renderHook(() => 
      usePagination({ initialPage: 3, initialItemsPerPage: 25 })
    );
    
    expect(result.current.paginationState.currentPage).toBe(3);
    expect(result.current.paginationState.itemsPerPage).toBe(25);
    expect(result.current.paginationState.skip).toBe(50); // (3-1) * 25
    expect(result.current.paginationState.take).toBe(25);
  });

  test('goToPage updates current page and skip correctly', () => {
    const { result } = renderHook(() => usePagination());
    
    act(() => {
      result.current.goToPage(5);
    });
    
    expect(result.current.paginationState.currentPage).toBe(5);
    expect(result.current.paginationState.skip).toBe(80); // (5-1) * 20
  });

  test('nextPage increments current page', () => {
    const { result } = renderHook(() => usePagination());
    
    act(() => {
      result.current.nextPage();
    });
    
    expect(result.current.paginationState.currentPage).toBe(2);
    expect(result.current.paginationState.skip).toBe(20);
  });

  test('previousPage decrements current page', () => {
    const { result } = renderHook(() => usePagination({ initialPage: 5 }));
    
    act(() => {
      result.current.previousPage();
    });
    
    expect(result.current.paginationState.currentPage).toBe(4);
    expect(result.current.paginationState.skip).toBe(60); // (4-1) * 20
  });

  test('previousPage does not go below page 1', () => {
    const { result } = renderHook(() => usePagination());
    
    act(() => {
      result.current.previousPage();
    });
    
    expect(result.current.paginationState.currentPage).toBe(1);
    expect(result.current.paginationState.skip).toBe(0);
  });

  test('setItemsPerPage updates items per page and resets to page 1', () => {
    const { result } = renderHook(() => usePagination({ initialPage: 5 }));
    
    act(() => {
      result.current.setItemsPerPage(50);
    });
    
    expect(result.current.paginationState.itemsPerPage).toBe(50);
    expect(result.current.paginationState.currentPage).toBe(1);
    expect(result.current.paginationState.skip).toBe(0);
  });

  test('setItemsPerPage respects max limit', () => {
    const { result } = renderHook(() => usePagination({ maxItemsPerPage: 100 }));
    
    act(() => {
      result.current.setItemsPerPage(150);
    });
    
    expect(result.current.paginationState.itemsPerPage).toBe(100);
  });

  test('resetPagination resets to initial values', () => {
    const { result } = renderHook(() => 
      usePagination({ initialPage: 2, initialItemsPerPage: 30 })
    );
    
    // Change the current state
    act(() => {
      result.current.goToPage(10);
      result.current.setItemsPerPage(50);
    });
    
    // Reset
    act(() => {
      result.current.resetPagination();
    });
    
    expect(result.current.paginationState.currentPage).toBe(2);
    expect(result.current.paginationState.itemsPerPage).toBe(30);
  });

  test('paginationInfo calculates correct pagination info', () => {
    const { result } = renderHook(() => usePagination({ initialPage: 3 }));
    
    const info = result.current.paginationInfo(100);
    
    expect(info.currentPage).toBe(3);
    expect(info.itemsPerPage).toBe(20);
    expect(info.totalItems).toBe(100);
    expect(info.hasNextPage).toBe(true);
    expect(info.hasPreviousPage).toBe(true);
  });

  test('paginationInfo handles first page correctly', () => {
    const { result } = renderHook(() => usePagination());
    
    const info = result.current.paginationInfo(100);
    
    expect(info.hasPreviousPage).toBe(false);
    expect(info.hasNextPage).toBe(true);
  });

  test('paginationInfo handles last page correctly', () => {
    const { result } = renderHook(() => usePagination({ initialPage: 5 }));
    
    const info = result.current.paginationInfo(100); // 5 pages total (100/20)
    
    expect(info.hasNextPage).toBe(false);
    expect(info.hasPreviousPage).toBe(true);
  });
}); 