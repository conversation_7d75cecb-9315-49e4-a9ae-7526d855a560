import { describe, test, expect } from 'vitest';
import { cn } from '@/lib/utils';

describe('Utility Functions', () => {
  describe('cn (className merger)', () => {
    test('merges class names correctly', () => {
      const result = cn('bg-red-500', 'text-white', 'p-4');
      expect(result).toBe('bg-red-500 text-white p-4');
    });

    test('handles conditional classes', () => {
      const isActive = true;
      const result = cn('base-class', isActive && 'active-class');
      expect(result).toBe('base-class active-class');
    });

    test('filters out falsy values', () => {
      const result = cn('base-class', false && 'hidden-class', null, undefined, 'visible-class');
      expect(result).toBe('base-class visible-class');
    });

    test('handles empty input', () => {
      const result = cn();
      expect(result).toBe('');
    });

    test('handles conflicting Tailwind classes correctly', () => {
      // This would depend on the actual implementation of cn
      // If using tailwind-merge, it should resolve conflicts
      const result = cn('bg-red-500', 'bg-blue-500');
      // The exact behavior depends on the implementation
      expect(typeof result).toBe('string');
    });
  });
}); 