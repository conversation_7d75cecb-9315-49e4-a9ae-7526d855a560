import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import Home from '@/app/page';
import { useAuth } from '@/lib/authContext';

// Mock AuthRequired component
vi.mock('@/components/AuthRequired', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

// Mock auth context
vi.mock('@/lib/authContext', () => ({
  useAuth: vi.fn()
}));

describe('HomePage', () => {
  beforeEach(() => {
    vi.mocked(useAuth).mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        firstname: 'Admin',
        lastname: 'User',
        access_level: 'admin'
      },
      isLoading: false,
      isAuthenticated: true,
      error: null,
      checkAuth: vi.fn(),
      logout: vi.fn()
    });
  });

  it('renders the dashboard title', () => {
    render(<Home />);
    
    expect(screen.getByText('KPFA Admin Dashboard')).toBeInTheDocument();
  });

  it('renders duplicate donors card', () => {
    render(<Home />);
    
    expect(screen.getByText('Duplicate Donors')).toBeInTheDocument();
    expect(screen.getByText('Review and merge duplicate donor records manually.')).toBeInTheDocument();
    
    const duplicatesLink = screen.getByRole('link', { name: /manage duplicates/i });
    expect(duplicatesLink).toHaveAttribute('href', '/duplicate-donors');
  });

  it('renders merged groups card', () => {
    render(<Home />);
    
    expect(screen.getByText('Merged Groups')).toBeInTheDocument();
    expect(screen.getByText('View history of merged donor groups.')).toBeInTheDocument();
    
    const historyLink = screen.getByRole('link', { name: /view history/i });
    expect(historyLink).toHaveAttribute('href', '/merged-groups');
  });

  it('applies correct styling classes to cards', () => {
    render(<Home />);
    
    const cards = screen.getAllByRole('link');
    
    cards.forEach(card => {
      expect(card.closest('div')).toHaveClass('card-bg', 'rounded-lg', 'shadow-sm');
    });
  });

  it('shows card hover states', () => {
    render(<Home />);
    
    const cardContainers = document.querySelectorAll('.hover\\:shadow-md');
    expect(cardContainers.length).toBeGreaterThan(0);
  });

  it('includes arrow icons in links', () => {
    render(<Home />);
    
    const svgElements = document.querySelectorAll('svg');
    expect(svgElements.length).toBe(2); // One for each card
    
    svgElements.forEach(svg => {
      expect(svg).toHaveAttribute('viewBox', '0 0 24 24');
    });
  });

  it('uses responsive grid layout', () => {
    render(<Home />);
    
    const gridContainer = document.querySelector('.grid');
    expect(gridContainer).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');
  });

  it('applies proper container and padding classes', () => {
    render(<Home />);
    
    const container = document.querySelector('.container');
    expect(container).toHaveClass('mx-auto', 'px-4', 'py-8');
  });
}); 