import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { MockedProvider, MockedResponse } from '@apollo/client/testing';
import { vi } from 'vitest';

// Mock the AuthProvider for tests
const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

// Mock the ThemeProvider for tests  
const MockThemeProvider = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

interface TestProvidersProps {
  children: React.ReactNode;
  apolloMocks?: MockedResponse[];
  mockUser?: {
    id: string;
    email: string;
    firstname: string;
    lastname: string;
    access_level: string;
  } | null;
}

// Test wrapper that includes all necessary providers
function TestProviders({ 
  children, 
  apolloMocks = [],
  mockUser = null 
}: TestProvidersProps) {
  return (
    <MockThemeProvider>
      <MockedProvider mocks={apolloMocks} addTypename={false}>
        <MockAuthProvider>
          {children}
        </MockAuthProvider>
      </MockedProvider>
    </MockThemeProvider>
  );
}

// Custom render function that includes providers
function renderWithProviders(
  ui: React.ReactElement,
  options: Omit<RenderOptions, 'wrapper'> & {
    apolloMocks?: MockedResponse[];
    mockUser?: {
      id: string;
      email: string;
      firstname: string;
      lastname: string;
      access_level: string;
    } | null;
  } = {}
) {
  const { apolloMocks, mockUser, ...renderOptions } = options;
  
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <TestProviders apolloMocks={apolloMocks} mockUser={mockUser}>
        {children}
      </TestProviders>
    );
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock data factories
export const createMockDonor = (overrides = {}) => ({
  id: '1',
  kpfaId: 12345,
  firstname: 'John',
  lastname: 'Doe',
  email: '<EMAIL>',
  phone: '**********',
  address1: '123 Main St',
  city: 'San Francisco',
  state: 'CA',
  postal_code: '94107',
  country: 'US',
  type: 'Individual',
  membership_level: 'Sustainer',
  deceased: false,
  donotsolicit: false,
  paperless: true,
  date_created: '2023-01-15T10:30:00Z',
  date_updated: '2024-01-15T15:45:00Z',
  ...overrides,
});

export const createMockDuplicateGroup = (overrides = {}) => ({
  id: 'group1',
  name: 'John Doe',
  status: 'PENDING',
  primaryDonor: {
    id: 'donor1',
    kpfaId: 12345,
  },
  duplicateDonors: [
    {
      id: 'donor2',
      kpfaId: 67890,
    },
  ],
  createdAt: '2024-01-15T10:30:00Z',
  ...overrides,
});

export const createMockAuthUser = (overrides = {}) => ({
  id: '1',
  email: '<EMAIL>',
  firstname: 'John',
  lastname: 'Admin',
  access_level: 'Admin',
  ...overrides,
});

// Common GraphQL mocks
export const createDuplicateGroupsMock = (groups = [createMockDuplicateGroup()]) => ({
  request: {
    query: require('@apollo/client').gql`
      query GetDuplicateGroups($where: DuplicateGroupWhereInput!, $take: Int!, $skip: Int!) {
        duplicateGroups(where: $where, take: $take, skip: $skip, orderBy: [{ name: asc }]) {
          id
          name
          status
          primaryDonor {
            id
            kpfaId
          }
          duplicateDonors {
            id
            kpfaId
          }
        }
        duplicateGroupsCount(where: $where)
      }
    `,
    variables: {
      where: {},
      take: 20,
      skip: 0,
    },
  },
  result: {
    data: {
      duplicateGroups: groups,
      duplicateGroupsCount: groups.length,
    },
  },
});

// Mock localStorage
export const mockLocalStorage = () => {
  const store: Record<string, string> = {};
  
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
  };
};

// Mock window.location
export const mockWindowLocation = (hostname = 'localhost') => {
  Object.defineProperty(window, 'location', {
    value: {
      hostname,
      href: `https://${hostname}`,
      assign: vi.fn(),
    },
    writable: true,
  });
};

// Async test helper
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

// Re-export everything from testing library
export * from '@testing-library/react';
export { renderWithProviders as render };
export { TestProviders }; 