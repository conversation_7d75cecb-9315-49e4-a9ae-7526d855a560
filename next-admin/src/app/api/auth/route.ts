import { NextRequest, NextResponse } from 'next/server';

// Decodes a JWT token and extracts the payload
function decodeJWT(token: string) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    return null;
  }
}

// Get the appropriate Station Admin API URL based on token issuer
function getStationAdminApiUrl(token: string) {
  // Decode the token to check the issuer
  const payload = decodeJWT(token);
  const issuer = payload?.iss;

  console.log('🔐 Token issuer:', issuer);

  // Use the same API that issued the token
  if (issuer === 'api.kpfa.org') {
    console.log('🔐 Using production API for production token');
    return 'https://api.kpfa.org';
  } else if (issuer === 'api.staging.kpfa.org') {
    console.log('🔐 Using staging API for staging token');
    return 'https://api.staging.kpfa.org';
  } else {
    // Fallback to environment-based detection
    const isDevelopment = process.env.NODE_ENV === 'development';
    const fallbackUrl = isDevelopment ? 'https://api.staging.kpfa.org' : 'https://api.kpfa.org';
    console.log('🔐 Unknown issuer, falling back to:', fallbackUrl);
    return fallbackUrl;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      );
    }

    // Extract the token
    const token = authHeader.substring(7);
    
    // Decode the token to get basic info
    const payload = decodeJWT(token);

    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token format' },
        { status: 401 }
      );
    }

    // Verify the user has Admin access level from token payload
    const accessLevel = payload.data?.access_level;

    if (accessLevel !== 'Admin') {
      return NextResponse.json(
        { error: 'Admin access required', accessLevel },
        { status: 403 }
      );
    }

    // Return the user data from token (like it was working before)
    return NextResponse.json({
      authenticated: true,
      user: {
        id: payload.data.id,
        email: payload.data.email,
        firstname: payload.data.firstname,
        lastname: payload.data.lastname,
        access_level: payload.data.access_level
      }
    });
  } catch (error) {
    console.error('Auth API error:', error);
    return NextResponse.json(
      { error: 'Authentication error' },
      { status: 500 }
    );
  }
} 