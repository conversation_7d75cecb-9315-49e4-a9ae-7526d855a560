import { NextRequest, NextResponse } from 'next/server';

// Environment-aware redirect URL
const getRedirectUrl = (requestHost: string) => {
  // Local development
  if (requestHost.includes('localhost') || requestHost.includes('127.0.0.1')) {
    return 'http://' + requestHost;
  }
  // Ngrok testing
  if (requestHost.includes('ngrok')) {
    return 'https://' + requestHost;
  }
  // Production
  return 'https://' + requestHost;
};

// Simple function to check if a string looks like a JWT
// (not full validation, just basic structure)
function looksLikeJWT(token: string): boolean {
  // JWT typically has three parts separated by dots
  const parts = token.split('.');
  return parts.length === 3;
}

// Decodes a JWT token without validation - just to see what's inside
function decodeJWT(token: string) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    return null;
  }
}

export async function GET(request: NextRequest) {
  // Get the token and redirect from the query string
  const searchParams = request.nextUrl.searchParams;
  const token = searchParams.get('token');
  const redirect = searchParams.get('redirect') || '/';
  
  console.log('Received token request, token present:', !!token);
  console.log('Redirect parameter:', redirect);
  
  if (!token) {
    return NextResponse.json(
      { error: 'Missing token parameter' },
      { status: 400 }
    );
  }

  // Basic validation
  if (!looksLikeJWT(token)) {
    console.error('Invalid token format received');
    return NextResponse.json(
      { error: 'Invalid token format' },
      { status: 400 }
    );
  }

  // Decode the token to see content (for debugging)
  const decodedToken = decodeJWT(token);
  console.log('Decoded token payload:', decodedToken);

  // Sanitize the redirect URL to prevent open redirects
  const safeRedirect = redirect.startsWith('/') ? redirect : '/';

  // Generate the response with a cookie or HTML that sets the token in localStorage
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Authentication Redirect</title>
      <meta charset="utf-8">
      <style>
        body { margin: 0; padding: 0; background: white; }
      </style>
    </head>
    <body>
      <script>
        // Store the token in localStorage
        localStorage.setItem('jwt_token', '${token}');
        console.log('Token stored in localStorage');
        
        // Redirect to the specified page or home immediately
        window.location.href = '${safeRedirect}';
      </script>
    </body>
    </html>
  `;

  return new NextResponse(html, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
} 