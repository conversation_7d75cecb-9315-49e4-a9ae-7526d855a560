import { NextResponse } from 'next/server';
import { getAuthToken } from '@/lib/api';

export async function GET(request: Request) {
  const url = new URL(request.url);
  const donorId = url.searchParams.get('id');
  
  if (!donorId) {
    return NextResponse.json({ error: 'Missing donor ID' }, { status: 400 });
  }
  
  try {
    // Get auth token for legacy API
    const authToken = await getAuthToken();
    
    if (!authToken) {
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }
    
    // Determine the API URL based on environment
    const apiBaseUrl = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test'
      ? 'https://api.staging.kpfa.org' 
      : 'https://api.kpfa.org';
    
    // Fetch donor data from legacy API
    const response = await fetch(`${apiBaseUrl}/accounts/donors/${donorId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    if (!response.ok) {
      return NextResponse.json({ 
        error: `Failed to fetch donor: ${response.status} ${response.statusText}` 
      }, { status: response.status });
    }
    
    // Parse the response
    const responseText = await response.text();
    const data = JSON.parse(responseText);
    
    // Extract donor data
    let donorData;
    if (data.records && data.records.length > 0) {
      donorData = data.records[0];
    } else {
      donorData = data;
    }
    
    // Log Stripe Customer ID if available
    if (donorData.stripe_cus_id) {
      console.log(`Donor ${donorId} has Stripe Customer ID: ${donorData.stripe_cus_id}`);
    }
    
    // Return the donor data
    return NextResponse.json(donorData);
  } catch (error) {
    console.error(`Error fetching donor ${donorId}:`, error);
    return NextResponse.json({ 
      error: 'An error occurred while fetching donor data' 
    }, { status: 500 });
  }
} 