import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getAuthToken } from '@/lib/api';

export async function GET(request: Request) {
  try {
    // Check authentication via Next.js session
    const session = await getServerSession(authOptions);
    
    let isAuthenticated = !!session;
    let authToken: string | null = null;
    
    // Check for Bearer token in Authorization header if session check failed
    if (!isAuthenticated) {
      const authHeader = request.headers.get('authorization');
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        // You might want to add JWT validation here similar to the PHP endpoint
        authToken = token;
        isAuthenticated = true;
      }
    }
    
    // If still not authenticated, try to get auth token via the existing method
    if (!isAuthenticated) {
      try {
        authToken = await getAuthToken();
        isAuthenticated = !!authToken;
      } catch (error) {
        console.error('Failed to get auth token:', error);
      }
    }
    
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse URL and get query parameters
    const url = new URL(request.url);
    const phoneNumber = url.searchParams.get('phone');
    const excludeDonorId = url.searchParams.get('exclude_id');
    const limit = url.searchParams.get('limit') || '10';

    // Validate required parameters
    if (!phoneNumber) {
      return NextResponse.json({
        success: false,
        message: 'Phone number parameter is required',
        error: {
          code: 'MISSING_PARAMETER',
          details: 'Please provide a phone query parameter'
        }
      }, { status: 400 });
    }

    // Determine the API URL based on environment
    const apiBaseUrl = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test'
      ? 'https://api.staging.kpfa.org' 
      : 'https://api.kpfa.org';

    // Build query parameters for the PHP endpoint
    const params = new URLSearchParams();
    params.append('phone', phoneNumber);
    if (excludeDonorId) {
      params.append('exclude_id', excludeDonorId);
    }
    params.append('limit', limit);

    const phpEndpointUrl = `${apiBaseUrl}/accounts/duplicates?${params.toString()}`;

    console.log(`Calling PHP duplicate search endpoint: ${phpEndpointUrl}`);

    // Call the PHP endpoint
    const response = await fetch(phpEndpointUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    // Get the response text
    const responseText = await response.text();
    console.log("PHP duplicate search response status:", response.status);
    console.log("PHP duplicate search response:", responseText.substring(0, 200));

    if (!response.ok) {
      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        errorData = { message: responseText };
      }
      
      return NextResponse.json({
        success: false,
        message: 'Failed to search for duplicates',
        error: errorData
      }, { status: response.status });
    }

    // Parse and return the successful response
    const data = JSON.parse(responseText);
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in duplicate search API route:', error);
    return NextResponse.json({
      success: false,
      message: 'An error occurred while searching for duplicates',
      error: {
        code: 'SERVER_ERROR',
        details: error instanceof Error ? error.message : String(error)
      }
    }, { status: 500 });
  }
} 