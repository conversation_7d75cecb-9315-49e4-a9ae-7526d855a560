import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { KEYSTONE_API_URL, LEGACY_API_URL } from '@/lib/config';

// Define interface for the request body
interface MergeDonorsRequest {
  primaryDonorId: number;
  secondaryDonorIds: number[];
  duplicateGroupId?: string; // Add new parameter to track which group to update
}

// Define merge API response
interface MergeApiResponse {
  status: string;
  success?: boolean;
  merged_records: {
    donations: number;
    subscriptions: number;
    caller_logs: number;
  };
  primary_donor_id: number;
  secondary_donor_ids: number[];
  deleted_donors: number[];
  merge_summary?: string;
  message: string;
  details?: {
    donationsMoved?: number;
    otherRecordsMoved?: number;
    donorsMerged?: number;
    donorsDeleted?: number;
  };
}

// Interface for donor data from legacy API
interface LegacyDonorData {
  id: number;
  firstname?: string;
  lastname?: string;
  phone?: string;
  email?: string;
  address1?: string;
  address2?: string;
  partner_firstname?: string;
  partner_lastname?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  notes?: string;
  type?: string;
  membership_level?: string;
  deceased?: boolean;
  donotsolicit?: boolean;
  stripe_cus_id?: string;
  paypal_user_id?: string;
  memsys_id?: number;
  allegiance_id?: number;
  date_created?: string;
  date_updated?: string;
  paperless?: boolean;
  paperless_token?: string;
  [key: string]: any; // Allow for additional properties from the API
}

// Decodes a JWT token and extracts the payload
function decodeJWT(token: string) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    return null;
  }
}

// Helper function to update a duplicate group status via internal GraphQL
async function updateDuplicateGroupStatus(groupId: string, status: string, userInfo: any) {
  // Determine the GraphQL API URL based on environment
  const apiUrl = KEYSTONE_API_URL;
  
  try {
    // Get user info from provided userInfo object
    let userName = 'Unknown User';
    
    if (userInfo?.firstname && userInfo?.lastname) {
      userName = `${userInfo.firstname} ${userInfo.lastname}`;
    }
    
    // Prepare mutation variables
    const variables: any = {
      id: groupId,
      status: status,
      mergedAt: new Date().toISOString(),
      mergedBy: userName
    };
    
    const query = `
      mutation UpdateDuplicateGroupStatus($id: ID!, $status: String!, $mergedAt: DateTime, $mergedBy: String) {
        updateDuplicateGroup(
          where: { id: $id }, 
          data: { 
            status: $status,
            mergedAt: $mergedAt,
            mergedBy: $mergedBy
          }
        ) {
          id
          status
          mergedAt
          mergedBy
        }
      }
    `;
    
    const response = await fetch(`${apiUrl}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });
    
    const result = await response.json();
    
    if (result.errors) {
      throw new Error(result.errors[0].message);
    }
    
    return result.data;
  } catch (error) {
    throw error;
  }
}

// Helper function to fetch donor data from legacy API
async function fetchDonorData(donorId: number, authToken: string): Promise<LegacyDonorData | null> {
  try {
    // Determine the API URL based on environment
    const apiBaseUrl = LEGACY_API_URL;
    
    console.log(`Fetching donor ${donorId} from legacy API: ${apiBaseUrl}/accounts/donors/${donorId}`);
    
    const response = await fetch(`${apiBaseUrl}/accounts/donors/${donorId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch donor ${donorId}: ${response.status} ${response.statusText}`);
      return null;
    }
    
    // Get the text response first so we can log it
    const responseText = await response.text();
    console.log(`Donor ${donorId} API response:`, responseText.substring(0, 200));
    
    try {
      // Parse the response as JSON
      const data = JSON.parse(responseText);
      
      // If response has records, use the first record
      let donorData: LegacyDonorData;
      
      if (data.records && data.records.length > 0) {
        console.log(`Donor ${donorId} found in records array (${data.records.length} records)`);
        donorData = data.records[0];
      } else {
        console.log(`Donor ${donorId} using direct data response`);
        donorData = data;
      }
      
      // Make sure donorData has the id field
      if (!donorData.id && donorId) {
        donorData.id = donorId;
      }
      
      // Ensure all fields are properly mapped from the API response
      // Handle potential field name differences or transformations here
      if (donorData.donor_id && !donorData.id) {
        donorData.id = Number(donorData.donor_id);
        delete donorData.donor_id;
      }
      
      if (donorData.donor_type && !donorData.type) {
        donorData.type = donorData.donor_type;
        delete donorData.donor_type;
      }
      
      // Log the donor data after processing
      const fieldCount = Object.keys(donorData).filter(key => 
        donorData[key] !== undefined && 
        donorData[key] !== null && 
        donorData[key] !== '').length;
      
      console.log(`Donor ${donorId} data has ${fieldCount} populated fields`);
      
      return donorData;
    } catch (parseError) {
      console.error(`Failed to parse donor ${donorId} JSON response:`, parseError);
      return null;
    }
  } catch (error) {
    console.error(`Exception fetching donor ${donorId}:`, error);
    return null;
  }
}

// Helper function to update a DuplicateDonor with legacy data
async function updateDuplicateDonorWithLegacyData(donorId: number, legacyData: LegacyDonorData) {
  const apiUrl = KEYSTONE_API_URL;
  
  try {
    // First find the DuplicateDonor by kpfaId
    const findQuery = `
      query FindDuplicateDonor($kpfaId: Int!) {
        duplicateDonors(where: { kpfaId: { equals: $kpfaId } }) {
          id
          kpfaId
          firstname
          lastname
          email
        }
      }
    `;
    
    const findResponse = await fetch(`${apiUrl}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: findQuery,
        variables: {
          kpfaId: donorId
        },
      }),
    });
    
    const findResponseText = await findResponse.text();
    if (!findResponse.ok) {
      throw new Error(`GraphQL find-donor query failed: ${findResponse.status} ${findResponseText}`);
    }
    
    const findResult = JSON.parse(findResponseText);
    
    if (findResult.errors) {
      console.error(`Error finding donor ${donorId}:`, findResult.errors);
      return null;
    }
    
    if (!findResult.data?.duplicateDonors?.[0]?.id) {
      console.error(`Duplicate donor not found for kpfaId ${donorId}`);
      return null;
    }
    
    const duplicateDonorId = findResult.data.duplicateDonors[0].id;
    console.log(`Found donor with ID ${duplicateDonorId} for kpfaId ${donorId}`);
    
    // Build mutation dynamically based on available data
    const mutationFields = [];
    const variables: Record<string, any> = { id: duplicateDonorId };
    
    // Log the legacy data for debugging
    console.log(`Processing donor data for ${donorId}:`, JSON.stringify(legacyData, null, 2));
    
    // Set values directly from legacy data, letting GraphQL handle the type conversion
    // Only set fields that are actually defined in the legacy data
    if (legacyData.firstname !== undefined && legacyData.firstname !== null) {
      mutationFields.push('firstname: $firstname');
      variables.firstname = legacyData.firstname;
    }
    
    if (legacyData.lastname !== undefined && legacyData.lastname !== null) {
      mutationFields.push('lastname: $lastname');
      variables.lastname = legacyData.lastname;
    }
    
    if (legacyData.phone !== undefined && legacyData.phone !== null) {
      mutationFields.push('phone: $phone');
      variables.phone = legacyData.phone;
    }
    
    if (legacyData.email !== undefined && legacyData.email !== null) {
      mutationFields.push('email: $email');
      variables.email = legacyData.email;
    }
    
    if (legacyData.address1 !== undefined && legacyData.address1 !== null) {
      mutationFields.push('address1: $address1');
      variables.address1 = legacyData.address1;
    }
    
    if (legacyData.address2 !== undefined && legacyData.address2 !== null) {
      mutationFields.push('address2: $address2');
      variables.address2 = legacyData.address2;
    }
    
    if (legacyData.partner_firstname !== undefined && legacyData.partner_firstname !== null) {
      mutationFields.push('partner_firstname: $partner_firstname');
      variables.partner_firstname = legacyData.partner_firstname;
    }
    
    if (legacyData.partner_lastname !== undefined && legacyData.partner_lastname !== null) {
      mutationFields.push('partner_lastname: $partner_lastname');
      variables.partner_lastname = legacyData.partner_lastname;
    }
    
    if (legacyData.city !== undefined && legacyData.city !== null) {
      mutationFields.push('city: $city');
      variables.city = legacyData.city;
    }
    
    if (legacyData.state !== undefined && legacyData.state !== null) {
      mutationFields.push('state: $state');
      variables.state = legacyData.state;
    }
    
    if (legacyData.country !== undefined && legacyData.country !== null) {
      mutationFields.push('country: $country');
      variables.country = legacyData.country;
    }
    
    if (legacyData.postal_code !== undefined && legacyData.postal_code !== null) {
      mutationFields.push('postal_code: $postal_code');
      variables.postal_code = legacyData.postal_code;
    }
    
    if (legacyData.notes !== undefined && legacyData.notes !== null) {
      mutationFields.push('notes: $notes');
      variables.notes = legacyData.notes;
    }
    
    if (legacyData.type !== undefined && legacyData.type !== null) {
      mutationFields.push('type: $type');
      variables.type = legacyData.type;
    }
    
    if (legacyData.membership_level !== undefined && legacyData.membership_level !== null) {
      mutationFields.push('membership_level: $membership_level');
      variables.membership_level = legacyData.membership_level;
    }
    
    if (legacyData.stripe_cus_id !== undefined && legacyData.stripe_cus_id !== null) {
      mutationFields.push('stripe_cus_id: $stripe_cus_id');
      variables.stripe_cus_id = legacyData.stripe_cus_id;
    }
    
    if (legacyData.paypal_user_id !== undefined && legacyData.paypal_user_id !== null) {
      mutationFields.push('paypal_user_id: $paypal_user_id');
      variables.paypal_user_id = legacyData.paypal_user_id;
    }
    
    if (legacyData.memsys_id !== undefined && legacyData.memsys_id !== null) {
      mutationFields.push('memsys_id: $memsys_id');
      variables.memsys_id = Number(legacyData.memsys_id);
    }
    
    // Handle allegiance_id which may be a number or string
    if (legacyData.allegiance_id !== undefined && legacyData.allegiance_id !== null) {
      mutationFields.push('allegiance_id: $allegiance_id');
      variables.allegiance_id = Number(legacyData.allegiance_id);
    }
    
    // Handle checkbox fields - ensure they're always boolean
    if (legacyData.deceased !== undefined) {
      mutationFields.push('deceased: $deceased');
      variables.deceased = !!legacyData.deceased; // Ensure boolean
    }
    
    if (legacyData.donotsolicit !== undefined) {
      mutationFields.push('donotsolicit: $donotsolicit');
      variables.donotsolicit = !!legacyData.donotsolicit; // Ensure boolean
    }
    
    if (legacyData.paperless !== undefined) {
      mutationFields.push('paperless: $paperless');
      variables.paperless = !!legacyData.paperless; // Ensure boolean
    }
    
    if (legacyData.paperless_token !== undefined && legacyData.paperless_token !== null) {
      mutationFields.push('paperless_token: $paperless_token');
      variables.paperless_token = legacyData.paperless_token;
    }
    
    // Format dates if they exist
    if (legacyData.date_created) {
      try {
        variables.date_created = new Date(legacyData.date_created).toISOString();
        mutationFields.push('date_created: $date_created');
      } catch (e) {
        console.error(`Invalid date format for date_created: ${legacyData.date_created}`);
      }
    }
    
    if (legacyData.date_updated) {
      try {
        variables.date_updated = new Date(legacyData.date_updated).toISOString();
        mutationFields.push('date_updated: $date_updated');
      } catch (e) {
        console.error(`Invalid date format for date_updated: ${legacyData.date_updated}`);
      }
    }
    
    // Only proceed if we have fields to update
    if (mutationFields.length === 0) {
      console.log(`No fields to update for donor ${donorId}`);
      return null;
    }
    
    console.log(`Updating donor ${donorId} with ${mutationFields.length} fields`);
    
    // Add fields to query explicitly for better debugging
    const returnFields = `
      id
      kpfaId
      firstname
      lastname
      email
      phone
      address1
      address2
      city
      state
      country
      postal_code
      notes
      type
      membership_level
      deceased
      donotsolicit
      paperless
    `;
    
    // Build dynamic mutation
    const updateQuery = `
      mutation UpdateDuplicateDonor(
        $id: ID!
        ${Object.keys(variables).filter(k => k !== 'id').map(k => `$${k}: ${getGraphQLType(k)}`).join('\n        ')}
      ) {
        updateDuplicateDonor(
          where: { id: $id }, 
          data: { 
            ${mutationFields.join(',\n            ')}
          }
        ) {
          ${returnFields}
        }
      }
    `;
    
    // Log the full mutation for debugging
    console.log(`GraphQL mutation: ${updateQuery}`);
    console.log(`Variables: ${JSON.stringify(variables, null, 2)}`);
    
    const updateResponse = await fetch(`${apiUrl}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: updateQuery,
        variables: variables,
      }),
    });
    
    const updateResponseText = await updateResponse.text();
    if (!updateResponse.ok) {
      throw new Error(`GraphQL update-donor query failed: ${updateResponse.status} ${updateResponseText}`);
    }

    const updateResult = JSON.parse(updateResponseText);
    
    if (updateResult.errors) {
      console.error('Error updating duplicate donor:', updateResult.errors);
      // Return null instead of throwing, so caller can handle gracefully
      return null;
    }
    
    console.log(`Successfully updated duplicate donor ${donorId} with legacy data:`, JSON.stringify(updateResult.data?.updateDuplicateDonor, null, 2));
    return updateResult.data.updateDuplicateDonor;
  } catch (error) {
    console.error(`Error in updateDuplicateDonorWithLegacyData for donor ${donorId}:`, error);
    return null;
  }
}

// Helper function to determine GraphQL type for each field
function getGraphQLType(fieldName: string): string {
  switch (fieldName) {
    case 'deceased':
    case 'donotsolicit':
    case 'paperless':
      return 'Boolean';
    case 'date_created':
    case 'date_updated':
      return 'DateTime';
    case 'allegiance_id':
    case 'memsys_id':
      return 'Int';
    default:
      return 'String';
  }
}

export async function POST(request: Request) {
  try {
    // Check authentication via Next.js session
    const session = await getServerSession(authOptions);
    
    let isAuthenticated = !!session;
    let authToken: string | null = null;
    let userData: any = null;
    
    // Check for Bearer token in Authorization header if session check failed
    if (!isAuthenticated) {
      const authHeader = request.headers.get('authorization');
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const payload = decodeJWT(token);
        
        // Verify the user has Admin access level
        const accessLevel = payload?.data?.access_level;
        
        if (accessLevel === 'Admin') {
          isAuthenticated = true;
          authToken = token;
          userData = payload?.data; // Store user data from JWT
        }
      }
    } else if (session?.user) {
      // If we have session data, use that
      userData = session.user;
    }
    
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body: MergeDonorsRequest = await request.json();
    const { primaryDonorId, secondaryDonorIds, duplicateGroupId } = body;

    // Validate request
    if (!primaryDonorId) {
      return NextResponse.json(
        { error: 'Primary donor ID is required' },
        { status: 400 }
      );
    }

    if (!secondaryDonorIds || !secondaryDonorIds.length) {
      return NextResponse.json(
        { error: 'At least one secondary donor ID is required' },
        { status: 400 }
      );
    }

    console.log(`Merge requested: Primary=${primaryDonorId}, Secondary=${secondaryDonorIds.join()}`);
    
    // IMPORTANT: First step - Fetch and store donor data from legacy API before merging
    // This ensures we capture the data before potential deletion in the legacy system
    let donorDataSaved = false;
    if (authToken) {
      try {
        // Fetch data for all secondary donors only (skip primary donor)
        console.log("Fetching legacy donor data...");
        
        // Use Promise.all for parallel fetching for better performance
        const secondaryDonorsData = await Promise.all(
          secondaryDonorIds.map(id => fetchDonorData(id, authToken))
        );
        
        const validDonorsData = secondaryDonorsData.filter(d => d !== null);
        console.log(`Got legacy data for ${validDonorsData.length} out of ${secondaryDonorIds.length} donors`);
        
        // Store the secondary donor data in Keystone
        if (validDonorsData.length > 0) {
          // Update secondary donors in parallel for better performance
          console.log("Starting donor data updates in Keystone...");
          
          const updatePromises = [];
          for (let i = 0; i < secondaryDonorIds.length; i++) {
            const donorData = secondaryDonorsData[i];
            if (donorData) {
              try {
                console.log(`Processing donor ${secondaryDonorIds[i]} data...`);
                updatePromises.push(
                  updateDuplicateDonorWithLegacyData(secondaryDonorIds[i], donorData)
                    .then(result => {
                      if (result) {
                        console.log(`✅ Successfully updated donor ${secondaryDonorIds[i]} data`);
                        donorDataSaved = true;
                      } else {
                        console.error(`❌ Failed to update donor ${secondaryDonorIds[i]} data`);
                      }
                    })
                    .catch(error => {
                      console.error(`Error updating donor ${secondaryDonorIds[i]} data:`, error);
                    })
                );
              } catch (error) {
                console.error(`Error processing donor ${secondaryDonorIds[i]} data:`, error);
              }
            } else {
              console.log(`No legacy data found for donor ${secondaryDonorIds[i]}`);
            }
          }
          
          // Wait for all updates to complete
          await Promise.all(updatePromises);
        } else {
          console.log('No legacy data found for any secondary donors');
        }
      } catch (fetchError) {
        console.error("Error fetching or saving donor data:", fetchError);
        // Continue with merge even if data fetching fails
      }
    } else {
      console.log('No auth token available, skipping legacy data fetch');
    }
    
    // Determine the API URL based on environment
    const apiBaseUrl = LEGACY_API_URL;
    
    // Call the API to perform the donor merge
    console.log(`Calling legacy API to merge donors: ${apiBaseUrl}/accounts/donors/${primaryDonorId}`);
    const apiResponse = await fetch(`${apiBaseUrl}/accounts/donors/${primaryDonorId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        merge_with: secondaryDonorIds
      })
    });
    
    // Get the response text
    const responseText = await apiResponse.text();
    console.log("Legacy API merge response:", responseText.substring(0, 200));
    
    // If we didn't save donor data before the merge, try one more time
    // This is a fallback in case the previous attempt failed
    if (!donorDataSaved && authToken && duplicateGroupId) {
      try {
        console.log("Attempting to save donor data after merge as fallback...");
        // Fetch the duplicate group to get donor information
        const apiUrl = KEYSTONE_API_URL;
            
        // Try one more time to fetch and update the data for each donor
        // Use Promise.all for parallel processing
        const fallbackPromises = secondaryDonorIds.map(donorId => 
          fetchDonorData(donorId, authToken)
            .then(donorData => {
              if (donorData) {
                console.log(`Fallback: Got data for donor ${donorId}`);
                return updateDuplicateDonorWithLegacyData(donorId, donorData);
              }
              return null;
            })
            .catch(e => {
              console.error(`Fallback donor data fetch failed for ${donorId}:`, e);
              return null;
            })
        );
        
        await Promise.all(fallbackPromises);
      } catch (error) {
        console.error("Fallback donor data fetch error:", error);
      }
    }
    
    // Check if the response was successful based on HTTP status
    if (!apiResponse.ok) {
      // Try to parse the error response as JSON
      let errorDetails = apiResponse.statusText;
      try {
        if (responseText && responseText.trim()) {
          const errorJson = JSON.parse(responseText);
          errorDetails = errorJson.error || errorJson.message || errorDetails;
          
          const errorResponse = {
            success: false,
            message: errorJson.message || `API responded with status ${apiResponse.status}`,
            error: {
              code: 'API_ERROR',
              details: errorDetails
            }
          };
          
          return NextResponse.json(errorResponse, { status: apiResponse.status });
        }
      } catch (parseError) {
        // If we can't parse the error as JSON, return the raw error
      }
      
      return NextResponse.json({
        success: false,
        message: `API responded with status ${apiResponse.status}`,
        error: {
          code: 'API_ERROR',
          details: errorDetails
        }
      }, { status: apiResponse.status });
    }
    
    // Parse the successful response
    let responseData: MergeApiResponse;
    let formattedResponse: any;
    
    if (responseText && responseText.trim()) {
      responseData = JSON.parse(responseText) as MergeApiResponse;
      
      // Format the response to match the frontend expectations
      formattedResponse = {
        success: responseData.success !== undefined ? responseData.success : responseData.status === 'success',
        message: responseData.message,
        details: {
          donationsMoved: responseData.merged_records?.donations || 0,
          otherRecordsMoved: (responseData.merged_records?.subscriptions || 0) + (responseData.merged_records?.caller_logs || 0),
          donorsMerged: responseData.details?.donorsMerged || responseData.secondary_donor_ids?.length || 0,
          donorsDeleted: responseData.details?.donorsDeleted || responseData.deleted_donors?.length || 0
        },
        primary_donor_id: responseData.primary_donor_id,
        secondary_donor_ids: responseData.secondary_donor_ids,
        deleted_donors: responseData.deleted_donors,
        merge_summary: responseData.merge_summary
      };
      
      // If a duplicateGroupId was provided, update its status
      if (duplicateGroupId) {
        try {
          await updateDuplicateGroupStatus(duplicateGroupId, 'MERGED', userData);
          formattedResponse.groupUpdated = true;
        } catch (error) {
          formattedResponse.groupUpdateError = 'Failed to update group status';
        }
      }
    } else {
      // Empty response, create a generic success response
      formattedResponse = {
        success: true,
        message: 'Merge completed successfully, but no details were returned',
        details: {
          donationsMoved: 0,
          otherRecordsMoved: 0,
          donorsMerged: secondaryDonorIds.length,
          donorsDeleted: 0
        }
      };
    }
    
    return NextResponse.json(formattedResponse);
  } catch (error) {
    console.error('An unexpected error occurred:', error);

    // Default error response
    const errorResponse = {
      success: false,
      message: 'An unexpected error occurred during the merge process',
      error: {
        code: 'INTERNAL_ERROR',
        details: 'There is a problem with the server configuration. Check the server logs for more information.'
      }
    };

    // Add more specific error information if available
    if (error instanceof Error) {
      errorResponse.error.details = error.message;
      // Optionally log the stack trace for debugging in server logs
      console.error(error.stack);
    }

    return NextResponse.json(errorResponse, { status: 500 });
  }
} 