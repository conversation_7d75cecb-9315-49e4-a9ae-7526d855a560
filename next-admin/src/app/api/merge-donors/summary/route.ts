import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { apiFetch } from '@/lib/api';

export async function GET(request: Request) {
  // Check authentication
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Get donor IDs from query parameters
  const url = new URL(request.url);
  const primaryDonorId = url.searchParams.get('primaryDonorId');
  const secondaryDonorId = url.searchParams.get('secondaryDonorId');

  if (!primaryDonorId || !secondaryDonorId) {
    return NextResponse.json(
      { error: 'Both primary and secondary donor IDs are required' },
      { status: 400 }
    );
  }

  try {
    // Fetch summaries for both donors in parallel
    const [primaryData, secondaryData] = await Promise.all([
      fetchDonorSummary(parseInt(primaryDonorId)),
      fetchDonorSummary(parseInt(secondaryDonorId))
    ]);

    return NextResponse.json({
      primary: primaryData,
      secondary: secondaryData
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch donor summaries', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// Helper function to fetch donor summary data
async function fetchDonorSummary(donorId: number) {
  try {
    // Get donations for the donor
    const donations = await apiFetch<any>(`accounts/donations?donor_id=${donorId}&limit=100`);
    
    // Get other related data
    const subscriptions = await apiFetch<any>(`accounts/subscriptions?donor_id=${donorId}&limit=100`)
      .catch(() => ({ records: [] })); // Return empty array if endpoint doesn't exist or errors
    
    // Calculate summary data
    const donationRecords = donations.records || [];
    const totalAmount = donationRecords.reduce(
      (sum: number, donation: any) => sum + (parseFloat(donation.amount) || 0),
      0
    );
    
    // Sort donations by date to get the most recent
    const sortedDonations = [...donationRecords].sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
    
    return {
      donationCount: donationRecords.length,
      totalAmount: totalAmount,
      lastDonationDate: donationRecords.length > 0 ? sortedDonations[0].date : null,
      subscriptionCount: (subscriptions.records || []).length,
      recentDonations: sortedDonations.slice(0, 3).map((d: any) => ({
        id: d.id,
        date: d.date,
        amount: d.amount,
        type: d.type
      }))
    };
  } catch (error) {
    throw error;
  }
} 