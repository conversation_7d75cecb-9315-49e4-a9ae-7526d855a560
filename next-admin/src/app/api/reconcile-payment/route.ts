import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { LEGACY_API_URL } from '@/lib/config';
import { decodeJWT } from '@/lib/jwt';

interface ReconcilePaymentRequest {
  stripeChargeId: string;
  donorId: number;
  comments?: string;
}

export async function POST(request: Request) {
  let isAuthenticated = false;
  let authToken = '';
  let userData: any = null;

  try {
    // Check for session-based authentication first
    const session = await getServerSession(authOptions);
    
    if (session?.user) {
      isAuthenticated = true;
      userData = session.user;
    }
    
    // Check for Bearer token in Authorization header if session check failed
    if (!isAuthenticated) {
      const authHeader = request.headers.get('authorization');
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const payload = decodeJWT(token);
        
        // Verify the user has Admin access level
        const accessLevel = payload?.data?.access_level;
        
        if (accessLevel === 'Admin') {
          isAuthenticated = true;
          authToken = token;
          userData = payload?.data;
        }
      }
    } else if (session?.user) {
      userData = session.user;
    }
    
    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body: ReconcilePaymentRequest = await request.json();
    const { stripeChargeId, donorId, comments } = body;

    // Validate request
    if (!stripeChargeId) {
      return NextResponse.json(
        { error: 'Stripe charge ID is required' },
        { status: 400 }
      );
    }

    if (!donorId) {
      return NextResponse.json(
        { error: 'Donor ID is required' },
        { status: 400 }
      );
    }

    console.log(`Reconciliation requested: Stripe=${stripeChargeId}, Donor=${donorId}`);

    // Call the Station Admin API to perform the reconciliation
    const apiBaseUrl = LEGACY_API_URL;
    console.log(`🌍 Using API: ${apiBaseUrl} (NODE_ENV: ${process.env.NODE_ENV})`);
    console.log(`Calling Station Admin API: ${apiBaseUrl}/apiv1.php?method=POST&collection=reconcile`);
    
    const apiResponse = await fetch(`${apiBaseUrl}/apiv1.php?method=POST&collection=reconcile`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        stripe_charge_id: stripeChargeId,
        donor_id: donorId,
        comments: comments
      })
    });
    
    const responseText = await apiResponse.text();
    console.log("Station Admin API response:", responseText.substring(0, 200));
    
    if (!apiResponse.ok) {
      console.error(`Station Admin API error: ${apiResponse.status} ${responseText}`);
      
      let errorMessage = 'Failed to reconcile payment';
      try {
        const errorData = JSON.parse(responseText);
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        errorMessage = responseText || errorMessage;
      }
      
      return NextResponse.json(
        { 
          error: errorMessage,
          details: `Station Admin API returned ${apiResponse.status}`
        },
        { status: apiResponse.status }
      );
    }
    
    // Parse the successful response
    let formattedResponse;
    try {
      const apiData = JSON.parse(responseText);
      
      if (apiData.status === 'success') {
        formattedResponse = {
          success: true,
          message: 'Payment reconciled successfully',
          details: {
            donationId: apiData.donation_id,
            paymentId: apiData.payment_id,
            transactionId: apiData.transaction_id,
            stripeChargeId: stripeChargeId,
            donorId: donorId
          }
        };
      } else {
        return NextResponse.json(
          { 
            error: apiData.message || 'Reconciliation failed',
            details: apiData
          },
          { status: 400 }
        );
      }
    } catch (parseError) {
      console.error('Failed to parse Station Admin API response:', parseError);
      
      formattedResponse = {
        success: true,
        message: 'Payment reconciled successfully, but response format was unexpected',
        details: {
          stripeChargeId: stripeChargeId,
          donorId: donorId,
          rawResponse: responseText.substring(0, 100)
        }
      };
    }
    
    return NextResponse.json(formattedResponse);
  } catch (error) {
    console.error('An unexpected error occurred:', error);

    const errorResponse = {
      success: false,
      message: 'An unexpected error occurred during the reconciliation process',
      error: {
        code: 'INTERNAL_ERROR',
        details: 'There is a problem with the server configuration. Check the server logs for more information.'
      }
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}
