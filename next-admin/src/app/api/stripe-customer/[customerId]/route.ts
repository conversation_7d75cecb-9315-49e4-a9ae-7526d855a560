import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: Request,
  { params }: { params: { customerId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { customerId } = params;

    if (!customerId) {
      return NextResponse.json({ error: 'Customer ID is required' }, { status: 400 });
    }

    console.log(`🔍 Fetching Stripe customer data for: ${customerId}`);

    // Use GraphQL to fetch customer data from Keystone
    const query = `
      query GetStripeCustomer($customerId: String!) {
        stripeCustomer(customerId: $customerId) {
          id
          email
          name
          metadata {
            donor_id
          }
        }
      }
    `;

    const keystoneUrl = process.env.NEXT_PUBLIC_KEYSTONE_API || 'http://localhost:3000';
    
    const response = await fetch(`${keystoneUrl}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables: { customerId }
      })
    });

    if (!response.ok) {
      throw new Error(`Keystone API error: ${response.status}`);
    }

    const result = await response.json();

    if (result.errors) {
      console.error('GraphQL errors:', result.errors);
      throw new Error('Failed to fetch customer data');
    }

    const customer = result.data?.stripeCustomer;

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 });
    }

    // Extract donor_id from metadata
    const donorId = customer.metadata?.donor_id;

    console.log(`✅ Customer ${customerId} donor_id: ${donorId || 'Not found'}`);

    return NextResponse.json({
      customerId: customer.id,
      email: customer.email,
      name: customer.name,
      donorId: donorId || null
    });

  } catch (error) {
    console.error('Error fetching Stripe customer:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer data' },
      { status: 500 }
    );
  }
}
