import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { chargeId: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { chargeId } = params;

    if (!chargeId) {
      return NextResponse.json({ error: 'Charge ID is required' }, { status: 400 });
    }

    console.log(`🔍 Looking up donor ID for subscription charge: ${chargeId}`);

    // Use GraphQL to get charge details from Keystone
    const chargeQuery = `
      query GetStripeCharge($chargeId: String!) {
        stripeCharge(chargeId: $chargeId) {
          id
          status
          description
          transactionId
          donorId
          donorName
        }
      }
    `;

    const keystoneUrl = process.env.NEXT_PUBLIC_KEYSTONE_API || 'http://localhost:3000';
    
    const chargeResponse = await fetch(`${keystoneUrl}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: chargeQuery,
        variables: { chargeId }
      })
    });

    if (!chargeResponse.ok) {
      throw new Error(`Keystone API error: ${chargeResponse.status}`);
    }

    const chargeResult = await chargeResponse.json();

    if (chargeResult.errors) {
      console.error('GraphQL errors:', chargeResult.errors);
      throw new Error('Failed to fetch charge data');
    }

    const charge = chargeResult.data?.stripeCharge;

    if (!charge) {
      return NextResponse.json({ error: 'Charge not found' }, { status: 404 });
    }

    // If charge already has donor ID in metadata, return it
    if (charge.donorId) {
      console.log(`✅ Found donor ID in charge metadata: ${charge.donorId}`);
      return NextResponse.json({
        donorId: charge.donorId,
        donorName: charge.donorName,
        source: 'charge_metadata'
      });
    }

    // For subscription charges, we need to look up the subscription directly
    // Stripe charges from subscriptions typically have invoice info in description

    // First, try to get the invoice ID from the charge description
    // Common patterns: "Subscription creation for in_xxxxx" or "Subscription update for in_xxxxx"
    let invoiceId: string | null = null;
    let subscriptionId: string | null = null;

    // Check if description contains invoice ID
    const invoiceMatch = charge.description?.match(/in_[a-zA-Z0-9]+/);
    if (invoiceMatch) {
      invoiceId = invoiceMatch[0];
      console.log(`📄 Found invoice ID in description: ${invoiceId}`);
    }

    // Also check if description contains subscription ID directly
    const subscriptionMatch = charge.description?.match(/sub_[a-zA-Z0-9]+/);
    if (subscriptionMatch) {
      subscriptionId = subscriptionMatch[0];
      console.log(`📋 Found subscription ID in description: ${subscriptionId}`);
    }

    // If we have subscription ID directly, skip invoice lookup
    if (subscriptionId) {
      console.log(`🎯 Using subscription ID directly: ${subscriptionId}`);
    } else if (!invoiceId) {
      // If no invoice or subscription ID found, this might not be a subscription charge
      console.log(`⚠️ No invoice or subscription ID found for charge ${chargeId} - may not be a subscription payment`);
      return NextResponse.json({
        error: 'Unable to determine subscription information for this charge',
        details: 'This charge may not be associated with a subscription'
      }, { status: 400 });
    }

    // If we don't have subscription ID yet, get it from invoice
    if (!subscriptionId && invoiceId) {
      const invoiceQuery = `
        query GetStripeInvoice($invoiceId: String!) {
          stripeInvoice(invoiceId: $invoiceId) {
            id
            subscriptionId
            customerId
          }
        }
      `;

      const invoiceResponse = await fetch(`${keystoneUrl}/api/graphql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: invoiceQuery,
          variables: { invoiceId }
        })
      });

      if (!invoiceResponse.ok) {
        throw new Error(`Failed to fetch invoice data: ${invoiceResponse.status}`);
      }

      const invoiceResult = await invoiceResponse.json();

      if (invoiceResult.errors) {
        console.error('Invoice GraphQL errors:', invoiceResult.errors);
        throw new Error('Failed to fetch invoice data');
      }

      const invoice = invoiceResult.data?.stripeInvoice;

      if (!invoice) {
        return NextResponse.json({
          error: 'Invoice not found',
          details: `Invoice ${invoiceId} not found in Stripe`
        }, { status: 404 });
      }

      subscriptionId = invoice.subscriptionId;
      console.log(`📋 Found subscription ID from invoice: ${subscriptionId}`);
    }

    if (!subscriptionId) {
      return NextResponse.json({
        error: 'No subscription ID found',
        details: 'Unable to find subscription associated with this charge'
      }, { status: 400 });
    }

    // Now get subscription data to find donor ID in metadata
    const subscriptionQuery = `
      query GetStripeSubscription($subscriptionId: String!) {
        stripeSubscription(subscriptionId: $subscriptionId) {
          id
          status
          customerId
          metadata {
            donor_id
            transaction_id
          }
        }
      }
    `;

    const subscriptionResponse = await fetch(`${keystoneUrl}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: subscriptionQuery,
        variables: { subscriptionId }
      })
    });

    if (!subscriptionResponse.ok) {
      throw new Error(`Failed to fetch subscription data: ${subscriptionResponse.status}`);
    }

    const subscriptionResult = await subscriptionResponse.json();

    if (subscriptionResult.errors) {
      console.error('Subscription GraphQL errors:', subscriptionResult.errors);
      throw new Error('Failed to fetch subscription data');
    }

    const subscription = subscriptionResult.data?.stripeSubscription;

    if (!subscription) {
      return NextResponse.json({
        error: 'Subscription not found',
        details: `Subscription ${subscriptionId} not found in Stripe`
      }, { status: 404 });
    }

    const donorId = subscription.metadata?.donor_id;
    const transactionId = subscription.metadata?.transaction_id;

    if (!donorId) {
      return NextResponse.json({
        error: 'No donor ID found in subscription metadata',
        details: `Subscription ${subscription.id} does not have donor_id in metadata`
      }, { status: 400 });
    }

    console.log(`✅ Found donor ID in subscription metadata: ${donorId} (transaction_id: ${transactionId})`);

    return NextResponse.json({
      donorId: donorId,
      transactionId: transactionId,
      subscriptionId: subscription.id,
      customerId: subscription.customerId,
      source: 'subscription_metadata'
    });

  } catch (error) {
    console.error('Error looking up subscription donor:', error);
    return NextResponse.json({
      error: 'Failed to lookup donor information',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
