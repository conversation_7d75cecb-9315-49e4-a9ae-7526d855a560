import { NextRequest, NextResponse } from 'next/server';

const KEYSTONE_URL = process.env.KEYSTONE_URL || 'http://localhost:3000/api/graphql';

async function countExistingDonors() {
  const query = `
    query CountDonors {
      duplicateDonorsCount(
        where: { 
          AND: [
            { phone: { not: { equals: "" } } }
            { kpfaId: { not: { equals: null } } }
          ]
        }
      )
    }
  `;

  const response = await fetch(KEYSTONE_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query }),
  });

  const result = await response.json();
  
  if (result.errors) {
    throw new Error(result.errors[0].message);
  }

  return result.data.duplicateDonorsCount;
}

export async function GET() {
  try {
    const count = await countExistingDonors();
    
    return NextResponse.json({
      success: true,
      count,
      message: `Found ${count} existing donors with phone numbers available for duplicate detection. Note: This processes existing data in Keystone that has been synced from the PHP system.`
    });

  } catch (error) {
    console.error('Error counting donors:', error);
    return NextResponse.json(
      { 
        error: 'Failed to count donors', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
} 