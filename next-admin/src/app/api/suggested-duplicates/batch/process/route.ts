import { NextRequest, NextResponse } from 'next/server';

const KEYSTONE_URL = process.env.KEYSTONE_URL || 'http://localhost:3000/api/graphql';

async function fetchExistingDonorBatch(offset: number, batchSize: number) {
  const query = `
    query GetDonorBatch($skip: Int!, $take: Int!) {
      duplicateDonors(
        skip: $skip
        take: $take
        where: { 
          AND: [
            { phone: { not: { equals: "" } } }
            { kpfaId: { not: { equals: null } } }
          ]
        }
        orderBy: { kpfaId: asc }
      ) {
        id
        kpfaId
        firstname
        lastname
        phone
        email
      }
    }
  `;

  const response = await fetch(KEYSTONE_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ 
      query, 
      variables: { skip: offset, take: batchSize } 
    }),
  });

  const result = await response.json();
  
  if (result.errors) {
    throw new Error(result.errors[0].message);
  }

  return result.data.duplicateDonors;
}

async function runDuplicateDetection(kpfaId: number) {
  const mutation = `
    mutation RunDetection($kpfaId: Int!) {
      runDuplicateDetectionForDonor(kpfaId: $kpfaId) {
        hasMatches
        candidates {
          donorId
          kpfaId
          phone
          firstname
          lastname
          confidence
          matchType
        }
        suggestedAction
        existingGroupId
        suggestionId
      }
    }
  `;

  const response = await fetch(KEYSTONE_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ 
      query: mutation, 
      variables: { kpfaId } 
    }),
  });

  const result = await response.json();
  
  if (result.errors) {
    throw new Error(result.errors[0].message);
  }

  return result.data.runDuplicateDetectionForDonor;
}

export async function POST(request: NextRequest) {
  try {
    const { offset, batchSize, minConfidence } = await request.json();

    const stats = {
      processed: 0,
      withMatches: 0,
      suggestionsCreated: 0,
      errors: 0,
      skipped: 0,
    };

    // Fetch batch of existing donors from Keystone
    const donors = await fetchExistingDonorBatch(offset, batchSize);

    // Process each donor
    for (const donor of donors) {
      try {
        // Skip donors without valid phone numbers
        if (!donor.phone || donor.phone.trim() === '') {
          stats.skipped++;
          continue;
        }

        const result = await runDuplicateDetection(donor.kpfaId);
        stats.processed++;

        if (result.hasMatches) {
          stats.withMatches++;
          
          if (result.suggestionId) {
            stats.suggestionsCreated++;
          }
        }

        // Add a small delay to prevent overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        stats.errors++;
        console.error(`Error processing donor ${donor.kpfaId}:`, error);
      }
    }

    return NextResponse.json({
      ...stats,
      message: `Processed ${stats.processed} existing donors, found ${stats.withMatches} with potential matches, created ${stats.suggestionsCreated} suggestions. Note: This processes existing data in Keystone that has been synced from the PHP system.`
    });

  } catch (error) {
    console.error('Batch processing error:', error);
    return NextResponse.json(
      { error: 'Batch processing failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 