import { NextRequest, NextResponse } from 'next/server';
import { KEYSTONE_API_URL } from '@/lib/config';

const GRAPHQL_API_URL = `${KEYSTONE_API_URL}/api/graphql`;

interface DonorData {
  kpfaId: number;
  phone?: string;
  firstname?: string;
  lastname?: string;
  email?: string;
}

interface DuplicateDetectionRequest {
  donor: DonorData;
  autoCreate?: boolean; // Whether to auto-create high-confidence suggestions
}

/**
 * POST /api/suggested-duplicates
 * Analyze a donor for potential duplicates and create suggestions
 */
export async function POST(request: NextRequest) {
  try {
    const body: DuplicateDetectionRequest = await request.json();
    
    if (!body.donor || !body.donor.kpfaId) {
      return NextResponse.json(
        { error: 'Missing required donor data or kpfaId' },
        { status: 400 }
      );
    }

    const { donor } = body;

    // First, ensure the donor exists in the DuplicateDonor table
    await ensureDonorExists(donor);

    // Run duplicate detection
    const detectionResult = await runDuplicateDetection(donor.kpfaId);

    return NextResponse.json({
      success: true,
      donor: {
        kpfaId: donor.kpfaId,
        name: `${donor.firstname || ''} ${donor.lastname || ''}`.trim(),
      },
      analysis: detectionResult,
    });

  } catch (error) {
    console.error('Error in suggested duplicates API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/suggested-duplicates
 * Get pending suggestions for review
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'PENDING';
    const limit = parseInt(searchParams.get('limit') || '20');

    const suggestions = await getSuggestedDuplicates(status, limit);

    return NextResponse.json({
      success: true,
      suggestions,
      count: suggestions.length,
    });

  } catch (error) {
    console.error('Error fetching suggested duplicates:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Ensure a donor exists in the DuplicateDonor table
 */
async function ensureDonorExists(donor: DonorData): Promise<void> {
  const checkQuery = `
    query CheckDonor($kpfaId: Int!) {
      duplicateDonor(where: { kpfaId: $kpfaId }) {
        id
        kpfaId
      }
    }
  `;

  const checkResponse = await fetch(GRAPHQL_API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: checkQuery,
      variables: { kpfaId: donor.kpfaId },
    }),
  });

  const checkResult = await checkResponse.json();

  // If donor doesn't exist, create it
  if (!checkResult.data?.duplicateDonor) {
    const createMutation = `
      mutation CreateDuplicateDonor(
        $kpfaId: Int!
        $firstname: String
        $lastname: String
        $phone: String
        $email: String
      ) {
        createDuplicateDonor(data: {
          kpfaId: $kpfaId
          firstname: $firstname
          lastname: $lastname
          phone: $phone
          email: $email
        }) {
          id
          kpfaId
        }
      }
    `;

    const createResponse = await fetch(GRAPHQL_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: createMutation,
        variables: {
          kpfaId: donor.kpfaId,
          firstname: donor.firstname,
          lastname: donor.lastname,
          phone: donor.phone,
          email: donor.email,
        },
      }),
    });

    const createResult = await createResponse.json();

    if (createResult.errors) {
      throw new Error(`Failed to create donor: ${createResult.errors[0].message}`);
    }
  } else {
    // Update existing donor with latest data
    const updateMutation = `
      mutation UpdateDuplicateDonor(
        $kpfaId: Int!
        $firstname: String
        $lastname: String
        $phone: String
        $email: String
      ) {
        updateDuplicateDonor(
          where: { kpfaId: $kpfaId }
          data: {
            firstname: $firstname
            lastname: $lastname
            phone: $phone
            email: $email
          }
        ) {
          id
          kpfaId
        }
      }
    `;

    await fetch(GRAPHQL_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: updateMutation,
        variables: {
          kpfaId: donor.kpfaId,
          firstname: donor.firstname,
          lastname: donor.lastname,
          phone: donor.phone,
          email: donor.email,
        },
      }),
    });
  }
}

/**
 * Run duplicate detection for a donor
 */
async function runDuplicateDetection(kpfaId: number) {
  const detectionMutation = `
    mutation RunDuplicateDetection($kpfaId: Int!) {
      runDuplicateDetectionForDonor(kpfaId: $kpfaId) {
        hasMatches
        candidates {
          donorId
          kpfaId
          phone
          firstname
          lastname
          confidence
          matchType
        }
        suggestedAction
        existingGroupId
        suggestionId
      }
    }
  `;

  const response = await fetch(GRAPHQL_API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: detectionMutation,
      variables: { kpfaId },
    }),
  });

  const result = await response.json();

  if (result.errors) {
    throw new Error(`Duplicate detection failed: ${result.errors[0].message}`);
  }

  return result.data.runDuplicateDetectionForDonor;
}

/**
 * Get suggested duplicates
 */
async function getSuggestedDuplicates(status: string, limit: number) {
  const query = `
    query GetSuggestedDuplicates($status: String, $limit: Int) {
      getSuggestedDuplicates(status: $status, limit: $limit) {
        id
        name
        status
        suggestionType
        autoCreated
        createdAt
        triggerDonor {
          id
          kpfaId
          firstname
          lastname
          phone
        }
        suggestedDonors {
          id
          kpfaId
          firstname
          lastname
          phone
        }
        existingGroup {
          id
          name
        }
        matchingCriteria
      }
    }
  `;

  const response = await fetch(GRAPHQL_API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query,
      variables: { status, limit },
    }),
  });

  const result = await response.json();

  if (result.errors) {
    throw new Error(`Failed to fetch suggestions: ${result.errors[0].message}`);
  }

  return result.data.getSuggestedDuplicates;
} 