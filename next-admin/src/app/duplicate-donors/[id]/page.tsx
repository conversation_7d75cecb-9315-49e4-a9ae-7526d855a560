'use client';

import { useState, useEffect } from 'react';
import { gql, useQuery, useMutation } from '@apollo/client';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { apiFetch, API_BASE_URL, getAuthToken } from '@/lib/api';
import { useAuth } from '@/lib/authContext';
import AuthRequired from '@/components/AuthRequired';
import { MergeDonorsModal } from '@/components/MergeDonorsModal';
import { LegacyDonorResponse } from '@/types';

// Types definition for this component
interface DonorDetail {
  id: string;
  kpfaId: number;
  details: LegacyDonorResponse | null;
  loading: boolean;
  error: string | null;
  dataSource?: 'api' | 'stored'; // Track whether data came from API or stored in Keystone
}

interface DuplicateGroup {
  id: string;
  name: string;
  status?: string;
  primaryDonor: DuplicateDonor | null;
  duplicateDonors: DuplicateDonor[];
  mergedAt?: string;
  mergedBy?: string;
  mergedDonorsData?: string | object;
}

interface DuplicateDonor {
  id: string;
  kpfaId: number;
  firstname?: string;
  lastname?: string;
  email?: string;
  phone?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  notes?: string;
  type?: string;
  membership_level?: string;
  deceased?: boolean;
  donotsolicit?: boolean;
  stripe_cus_id?: string;
  paypal_user_id?: string;
  memsys_id?: string;
  allegiance_id?: string;
  // ...other properties can be added as needed
}

// GraphQL query for a specific duplicate group
const DUPLICATE_GROUP_QUERY = gql`
  query DuplicateGroup($id: ID!) {
    duplicateGroup(where: { id: $id }) {
      id
      name
      status
      mergedAt
      mergedBy
      mergedDonorsData
      primaryDonor {
        id
        kpfaId
        firstname
        lastname
        phone
        email
        address1
        address2
        partner_firstname
        partner_lastname
        city
        state
        country
        postal_code
        notes
        type
        membership_level
        deceased
        donotsolicit
        stripe_cus_id
        paypal_user_id
        memsys_id
        allegiance_id
        date_created
        date_updated
        paperless
        paperless_token
      }
      duplicateDonors {
        id
        kpfaId
        firstname
        lastname
        phone
        email
        address1
        address2
        partner_firstname
        partner_lastname
        city
        state
        country
        postal_code
        notes
        type
        membership_level
        deceased
        donotsolicit
        stripe_cus_id
        paypal_user_id
        memsys_id
        allegiance_id
        date_created
        date_updated
        paperless
        paperless_token
      }
    }
  }
`;

// GraphQL mutation to update the status of a duplicate group
const UPDATE_DUPLICATE_GROUP_STATUS = gql`
  mutation UpdateDuplicateGroupStatus($id: ID!, $status: String!) {
    updateDuplicateGroup(
      where: { id: $id }
      data: { status: $status }
    ) {
      id
      status
    }
  }
`;

// GraphQL mutations for suggested duplicates
const ACCEPT_SUGGESTION_MUTATION = gql`
  mutation AcceptSuggestion($groupId: ID!, $staffMember: String!, $newStatus: String!) {
    updateDuplicateGroup(
      where: { id: $groupId }
      data: { 
        status: $newStatus
        reviewedBy: $staffMember
      }
    ) {
      id
      status
      reviewedBy
      reviewedAt
    }
  }
`;

const REJECT_SUGGESTION_MUTATION = gql`
  mutation RejectSuggestion($groupId: ID!, $staffMember: String!) {
    rejectSuggestion(groupId: $groupId, staffMember: $staffMember) {
      id
      name
      status
      reviewedBy
      reviewedAt
    }
  }
`;

// GraphQL mutation to remove a donor from a group
const REMOVE_DONOR_FROM_GROUP_MUTATION = gql`
  mutation RemoveDonorFromGroup($groupId: ID!, $donorId: ID!) {
    removeDonorFromGroup(groupId: $groupId, donorId: $donorId) {
      id
      name
      status
      duplicateDonors {
        id
        kpfaId  
        firstname
        lastname
      }
      primaryDonor {
        id
        kpfaId
        firstname
        lastname
      }
    }
  }
`;

// GraphQL mutation for deleting groups with members
const DELETE_GROUP_WITH_MEMBERS_MUTATION = gql`
  mutation DeleteGroupWithMembers($groupId: ID!, $rejectGroupSuggestion: Boolean) {
    deleteGroupWithMembers(groupId: $groupId, rejectGroupSuggestion: $rejectGroupSuggestion) {
      id
      name
      groupRejected
      deletedDonorCount
    }
  }
`;

// Add this helper function after the query definitions
function getDonorProperty(donor: LegacyDonorResponse | null, property: string): any {
  if (!donor) return undefined;
  
  // Direct property access with fallback to handle both formats
  // (legacy API response format and the stored mergedDonorsData format)
  switch (property) {
    case 'firstname': return donor.firstname;
    case 'lastname': return donor.lastname;
    case 'email': return donor.email;
    case 'phone': return donor.phone;
    case 'address1': return donor.address1;
    case 'address2': return donor.address2;
    case 'city': return donor.city;
    case 'state': return donor.state;
    case 'postal_code': return donor.postal_code;
    case 'country': return donor.country;
    case 'notes': return donor.notes;
    case 'membership_level': return donor.membership_level;
    case 'donotsolicit': return typeof donor.donotsolicit === 'boolean' ? donor.donotsolicit : null;
    case 'deceased': return typeof donor.deceased === 'boolean' ? donor.deceased : null;
    case 'type': return donor.type;
    case 'stripe_cus_id': return donor.stripe_cus_id || donor.stripeCusId;
    case 'allegiance_id': {
      const val = (donor as any).allegiance_id || donor.allegianceId;
      return val !== undefined ? Number(val) : undefined;
    }
    case 'memsys_id': {
      const val = (donor as any).memsys_id || donor.memsysId;
      return val !== undefined ? Number(val) : undefined;
    }
    default: return undefined;
  }
}

export default function DuplicateGroupPage() {
  const { user } = useAuth();
  const params = useParams();
  const id = params.id as string;
  const [donorDetails, setDonorDetails] = useState<Record<number, DonorDetail>>({});
  const [isMergeModalOpen, setIsMergeModalOpen] = useState(false);
  
  const { loading: groupLoading, error: groupError, data, refetch } = useQuery(DUPLICATE_GROUP_QUERY, {
    variables: { id },
  });

  const [updateGroupStatus] = useMutation(UPDATE_DUPLICATE_GROUP_STATUS);

  // Add mutations for suggested duplicates
  const [acceptSuggestion] = useMutation(ACCEPT_SUGGESTION_MUTATION);
  const [rejectSuggestion] = useMutation(REJECT_SUGGESTION_MUTATION);
  const [removeDonorFromGroup] = useMutation(REMOVE_DONOR_FROM_GROUP_MUTATION);
  const [deleteGroupWithMembers] = useMutation(DELETE_GROUP_WITH_MEMBERS_MUTATION);

  // Add state for suggested duplicates handling
  const [processingGroupId, setProcessingGroupId] = useState<string | null>(null);
  const [removingDonorId, setRemovingDonorId] = useState<string | null>(null);
  const [deletingGroup, setDeletingGroup] = useState(false);
  const [confirmDeleteModal, setConfirmDeleteModal] = useState<{
    isOpen: boolean;
    groupId: string;
    groupName: string;
    hasMembers: boolean;
    isAutoCreated: boolean;
  }>({
    isOpen: false,
    groupId: "",
    groupName: "",
    hasMembers: false,
    isAutoCreated: false,
  });

  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    type: 'success' | 'error';
    title: string;
    message: string;
  }>({
    isOpen: false,
    type: 'success',
    title: '',
    message: ''
  });

  const duplicateGroup: DuplicateGroup | null = data?.duplicateGroup || null;

  // Function to fetch donor details from API
  const fetchDonorDetails = async (kpfaId: number): Promise<void> => {
    // Update loading state
    setDonorDetails(prev => ({
      ...prev,
      [kpfaId]: {
        ...prev[kpfaId],
        loading: true,
        error: null
      }
    }));

    try {
      // Make a standard API fetch call
      const data = await apiFetch<{ records?: LegacyDonorResponse[], error?: string, message?: string }>(`/accounts/donors/${kpfaId}`);
      
      // Check for API-level errors
      if (data.error || data.message) {
        const errorMsg = data.error || data.message;
        console.error(`API error for donor ${kpfaId}:`, errorMsg);
        throw new Error(errorMsg);
      }

      // Check if records exists and is not empty
      if (!data.records || !data.records.length) {
        console.error(`No records found in response for donor ${kpfaId}:`, data);
        
        // Special case: the endpoint might have returned a successful HTTP status
        // but with empty content or missing the expected 'records' structure
        if (Object.keys(data as object).length === 0) {
          throw new Error(`Donor ID ${kpfaId} returned an empty response.`);
        } else {
          throw new Error(`Donor ID ${kpfaId} was not found in the database or returned no records`);
        }
      }

      // Log the Stripe customer ID if available
      const stripeCusId = data.records[0].stripe_cus_id || data.records[0].stripeCusId;

      // Update the donor details state on success
      setDonorDetails(prev => ({
        ...prev,
        [kpfaId]: {
          id: kpfaId.toString(),
          kpfaId: kpfaId,
          details: data.records?.[0] || null,
          loading: false,
          error: null,
          dataSource: 'api'
        }
      }));
    } catch (error: any) {
      console.error(`Error fetching donor details for ID ${kpfaId}:`, error);
      
      // Update state with error and re-throw for upstream handling
      setDonorDetails(prev => ({
        ...prev,
        [kpfaId]: {
          id: kpfaId.toString(),
          kpfaId: kpfaId,
          details: null,
          loading: false,
          error: error.message || 'Failed to fetch donor details'
        }
      }));
      
      // Re-throw the error so the catch block in the useEffect can handle it
      throw error;
    }
  };

  // Load donor details when the group data is loaded
  useEffect(() => {
    if (duplicateGroup) {
      // Initialize donor details state with loading status
      const initialDonorDetails: Record<number, DonorDetail> = {};
      
      // For all groups, fetch primary donor data from the API
      // (since primary donors continue to exist and get updated in the legacy system)
      if (duplicateGroup.primaryDonor) {
        initialDonorDetails[duplicateGroup.primaryDonor.kpfaId] = {
          id: duplicateGroup.primaryDonor.id,
          kpfaId: duplicateGroup.primaryDonor.kpfaId,
          details: null,
          loading: true,
          error: null
        };
        
        // Always fetch primary donor from the external API
        fetchDonorDetails(duplicateGroup.primaryDonor.kpfaId);
      }
      
      // For duplicate donors, always try to fetch from API first
      // If API returns error (donor doesn't exist because it was merged), 
      // then fall back to stored data from duplicateDonor record
      duplicateGroup.duplicateDonors.forEach(donor => {
        // Skip if it's the primary donor (already handled above)
        if (duplicateGroup.primaryDonor && donor.kpfaId === duplicateGroup.primaryDonor.kpfaId) {
          return;
        }
        
        // Initialize with loading state and attempt to fetch from API
        initialDonorDetails[donor.kpfaId] = {
          id: donor.id,
          kpfaId: donor.kpfaId,
          details: null,
          loading: true,
          error: null
        };
        
        // Store the donor record so we can fall back to it if API fetch fails
        const storedDonor = donor;
        
        // Try to fetch from API, with fallback to stored data on error
        fetchDonorDetails(donor.kpfaId).catch(() => {
          // If API fetch fails, use stored data from duplicateDonor record
          const donorAsLegacyResponse: LegacyDonorResponse = {
            id: storedDonor.kpfaId,
            firstname: storedDonor.firstname || '',
            lastname: storedDonor.lastname || '',
            email: storedDonor.email || '',
            phone: storedDonor.phone || '',
            address1: storedDonor.address1 || '',
            address2: storedDonor.address2 || '',
            city: storedDonor.city || '',
            state: storedDonor.state || '',
            country: storedDonor.country || '',
            postal_code: storedDonor.postal_code || '',
            notes: storedDonor.notes || '',
            type: storedDonor.type || '',
            membership_level: storedDonor.membership_level || '',
            deceased: storedDonor.deceased || false,
            donotsolicit: storedDonor.donotsolicit || false,
            stripe_cus_id: storedDonor.stripe_cus_id || '',
            stripeCusId: storedDonor.stripe_cus_id || '',
            allegianceId: storedDonor.allegiance_id ? Number(storedDonor.allegiance_id) : undefined,
            memsysId: storedDonor.memsys_id ? Number(storedDonor.memsys_id) : undefined
          };
          
          setDonorDetails(prev => ({
            ...prev,
            [storedDonor.kpfaId]: {
              id: storedDonor.id,
              kpfaId: storedDonor.kpfaId, 
              details: donorAsLegacyResponse,
              loading: false,
              error: null,
              dataSource: 'stored'
            }
          }));
        });
      });
      
      setDonorDetails(initialDonorDetails);
    }
  }, [duplicateGroup]);

  const handleMergeComplete = () => {
    setIsMergeModalOpen(false);
    // Refetch the data to update the UI after merge
    setTimeout(() => {
      refetch();
    }, 1000); // Small delay to allow backend operations to complete
  };

  // Extract duplicate donors for the merge modal - only include unmerged donors
  const duplicateDonorsForModal = duplicateGroup?.duplicateDonors
    .filter(donor => {
      // Exclude the primary donor
      if (duplicateGroup.primaryDonor && donor.kpfaId === duplicateGroup.primaryDonor.kpfaId) {
        return false;
      }
      
      // Only include donors that come from the API (not stored/merged)
      const donorDetail = donorDetails[donor.kpfaId];
      return donorDetail?.dataSource === 'api' && !donorDetail?.error;
    })
    .map(donor => {
      const details = donorDetails[donor.kpfaId];
      return {
        id: donor.id,
        kpfaId: donor.kpfaId,
        details: details?.details || null
      };
    }) || [];

  // Check if there are any unmerged duplicates to determine if merge button should be shown
  // For non-merged groups, show merge button if there are any duplicate donors (excluding primary)
  // For merged groups, only show if there are donors that still exist in the API
  const hasUnmergedDuplicates = duplicateGroup?.status === "MERGED" 
    ? duplicateDonorsForModal.length > 0 
    : (duplicateGroup?.duplicateDonors?.filter(donor => 
        duplicateGroup.primaryDonor && donor.kpfaId !== duplicateGroup.primaryDonor.kpfaId
      ).length || 0) > 0;

  // Get primary donor details for the merge modal
  const primaryDonorId = duplicateGroup?.primaryDonor?.kpfaId || 0;
  const primaryDonorDetails = donorDetails[primaryDonorId]?.details || null;

  // Handler functions for suggested duplicates
  const handleAcceptSuggestion = async (moveToReady: boolean = false) => {
    if (!duplicateGroup?.id) return;

    setProcessingGroupId(duplicateGroup.id);
    
    // Get staff member name from user context
    const staffMember = user?.firstname && user?.lastname 
      ? `${user.firstname} ${user.lastname}` 
      : 'Unknown Staff';

    try {
      await acceptSuggestion({
        variables: {
          groupId: duplicateGroup.id,
          staffMember,
          newStatus: moveToReady ? 'READY_TO_MERGE' : 'PENDING'
        }
      });

      await refetch(); // Refresh the data
      setModalState({
        isOpen: true,
        type: 'success',
        title: 'Suggestion Accepted',
        message: `The duplicate suggestion has been accepted and moved to ${moveToReady ? 'Ready to Merge' : 'Pending'}.`
      });
    } catch (error) {
      console.error('Error accepting suggestion:', error);
      setModalState({
        isOpen: true,
        type: 'error',
        title: 'Error',
        message: 'Error accepting suggestion. Please try again.'
      });
    } finally {
      setProcessingGroupId(null);
    }
  };

  const handleRejectSuggestion = async () => {
    if (!duplicateGroup?.id) return;

    setProcessingGroupId(duplicateGroup.id);
    
    // Get staff member name from user context
    const staffMember = user?.firstname && user?.lastname 
      ? `${user.firstname} ${user.lastname}` 
      : 'Unknown Staff';

    try {
      await rejectSuggestion({
        variables: {
          groupId: duplicateGroup.id,
          staffMember
        }
      });

      setModalState({
        isOpen: true,
        type: 'success',
        title: 'Suggestion Rejected',
        message: 'The duplicate suggestion has been successfully rejected. Redirecting to duplicate groups page...'
      });
      
      // Redirect to main duplicate groups page after successful rejection
      // since the group is likely removed or hidden after rejection
      setTimeout(() => {
        window.location.href = '/duplicate-donors';
      }, 2000);
    } catch (error) {
      console.error('Error rejecting suggestion:', error);
      setModalState({
        isOpen: true,
        type: 'error',
        title: 'Rejection Failed',
        message: error instanceof Error ? error.message : 'Failed to reject suggestion. Please try again.'
      });
    } finally {
      setProcessingGroupId(null);
    }
  };

  // Handle status change of the duplicate group
  const handleStatusChange = async (newStatus: string) => {
    try {
      await updateGroupStatus({
        variables: {
          id: duplicateGroup?.id,
          status: newStatus,
        },
      });

      // Refetch the data to update the UI
      refetch();
    } catch (error) {
      console.error('Error updating group status:', error);
    }
  };

  const handleRemoveDonor = async (donorId: string, donorName: string, kpfaId: number) => {
    if (!duplicateGroup) return;

    try {
      setRemovingDonorId(donorId);
      
      await removeDonorFromGroup({
        variables: {
          groupId: duplicateGroup.id,
          donorId: donorId,
        },
      });

      // Refresh the data
      refetch();
      
      setModalState({
        isOpen: true,
        type: 'success',
        title: 'Donor Removed',
        message: `${donorName} (ID: ${kpfaId}) has been successfully removed from the group.`
      });
    } catch (error: any) {
      console.error('Error removing donor from group:', error);
      setModalState({
        isOpen: true,
        type: 'error',
        title: 'Removal Failed',
        message: error.message || 'Failed to remove donor from group. Please try again.'
      });
    } finally {
      setRemovingDonorId(null);
    }
  };

  const handleDeleteGroup = async () => {
    if (!duplicateGroup) return;
    
    const hasMembers = duplicateGroup.duplicateDonors.length > 0 || !!duplicateGroup.primaryDonor;
    const isAutoCreated = duplicateGroup.status === 'SUGGESTED' || false; // Assuming auto-created groups are suggested
    
    setConfirmDeleteModal({
      isOpen: true,
      groupId: duplicateGroup.id,
      groupName: duplicateGroup.name,
      hasMembers,
      isAutoCreated,
    });
    

  };

  const confirmDeleteGroup = async () => {
    const { groupId, hasMembers } = confirmDeleteModal;
    setDeletingGroup(true);
    setConfirmDeleteModal({ 
      isOpen: false, 
      groupId: "", 
      groupName: "", 
      hasMembers: false, 
      isAutoCreated: false 
    });
    
    try {
      const result = await deleteGroupWithMembers({
        variables: { 
          groupId, 
          rejectGroupSuggestion: confirmDeleteModal.isAutoCreated 
        },
      });

      // Show success message with details
      const deletedData = result.data?.deleteGroupWithMembers;
      let message = `Group "${deletedData?.name}" has been deleted along with ${deletedData?.deletedDonorCount} duplicate donor records.`;
      
      if (deletedData?.groupRejected) {
        message += ' The group suggestion has also been rejected to prevent future suggestions.';
      }

      setModalState({
        isOpen: true,
        type: 'success',
        title: 'Group Deleted Successfully',
        message
      });

      // Navigate back to the main page after a short delay
      setTimeout(() => {
        window.location.href = '/duplicate-donors';
      }, 2000);
    } catch (err) {
      console.error("Error deleting group:", err);
      setModalState({
        isOpen: true,
        type: 'error',
        title: 'Delete Failed',
        message: err instanceof Error ? err.message : 'Failed to delete group. Please try again.'
      });
    } finally {
      setDeletingGroup(false);
    }
  };

  const closeDeleteModal = () => {
    setConfirmDeleteModal({ 
      isOpen: false, 
      groupId: "", 
      groupName: "", 
      hasMembers: false, 
      isAutoCreated: false 
    });

  };

  // Get all donor IDs for comparison, with primary donor first
  const allDonorIds = (() => {
    const ids = Object.keys(donorDetails).map(Number);
    
    // If there's a primary donor, ensure it comes first
    if (duplicateGroup?.primaryDonor?.kpfaId) {
      const primaryId = duplicateGroup.primaryDonor.kpfaId;
      return [
        primaryId,
        ...ids.filter(id => id !== primaryId)
      ];
    }
    
    return ids;
  })();

  // Handle loading and error states outside the main return
  if (groupLoading) return (
    <AuthRequired>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full"></div>
        </div>
      </div>
    </AuthRequired>
  );

  if (groupError) return (
    <AuthRequired>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="bg-red-50 border-l-4 border-red-400 p-4 my-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">Error loading group: {groupError?.message || "Unknown error"}</p>
            </div>
          </div>
        </div>
      </div>
    </AuthRequired>
  );

  if (!duplicateGroup) return (
    <AuthRequired>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 w-full text-center">
          <svg className="w-12 h-12 mx-auto text-yellow-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
          <h2 className="text-lg font-semibold text-yellow-700 mb-2">Duplicate Group Not Found</h2>
          <p className="text-sm text-yellow-600 mb-4">The requested duplicate donor group could not be found.</p>
          <Link href="/duplicate-donors" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            Return to Duplicate Donors
          </Link>
        </div>
      </div>
    </AuthRequired>
  );

  return (
    <AuthRequired>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-primary">{duplicateGroup.name}</h1>
            <div className="flex items-center mt-1">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mr-2 
                ${duplicateGroup.status === "PENDING" ? "bg-yellow-100 text-yellow-800" : ""}
                ${duplicateGroup.status === "READY_TO_MERGE" ? "bg-green-100 text-green-800" : ""}
                ${duplicateGroup.status === "MERGED" ? "bg-blue-100 text-blue-800" : ""}
                ${duplicateGroup.status === "SUGGESTED" ? "bg-purple-100 text-purple-800" : ""}
              `}>
                {duplicateGroup.status === "PENDING" && "Unconfirmed"}
                {duplicateGroup.status === "READY_TO_MERGE" && "Confirmed"}
                {duplicateGroup.status === "MERGED" && "Merged"}
                {duplicateGroup.status === "SUGGESTED" && "Auto-Suggested"}
              </span>
              
              {duplicateGroup.status === "MERGED" && (
                <span className="text-sm text-gray-600">
                  {duplicateGroup.mergedAt && (
                    <>Merged on {new Date(duplicateGroup.mergedAt).toLocaleDateString()}</>
                  )}
                  {duplicateGroup.mergedBy && (
                    <> by {duplicateGroup.mergedBy}</>
                  )}
                </span>
              )}
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Link
              href="/duplicate-donors"
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back
            </Link>
            
            {/* Delete Group Button */}
            <button
              onClick={handleDeleteGroup}
              disabled={deletingGroup}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {deletingGroup ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Deleting...
                </>
              ) : (
                <>
                  <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Delete Group
                </>
              )}
            </button>
            
            {(duplicateGroup.status !== "MERGED" || hasUnmergedDuplicates) && (
              <div className="flex space-x-2">
                {duplicateGroup.status === "SUGGESTED" ? (
                  // Actions for auto-suggested groups
                  <>
                    <button
                      onClick={() => handleAcceptSuggestion(false)}
                      disabled={processingGroupId === duplicateGroup.id}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
                    >
                      {processingGroupId === duplicateGroup.id ? 'Processing...' : (
                        <>
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Mark Pending
                        </>
                      )}
                    </button>
                    
                    <button
                      onClick={() => handleAcceptSuggestion(true)}
                      disabled={processingGroupId === duplicateGroup.id || duplicateGroup.duplicateDonors.length <= 1}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors disabled:opacity-50 disabled:hover:bg-green-100"
                    >
                      {processingGroupId === duplicateGroup.id ? 'Processing...' : (
                        <>
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          Mark Ready
                        </>
                      )}
                    </button>
                    
                    <button
                      onClick={() => handleRejectSuggestion()}
                      disabled={processingGroupId === duplicateGroup.id}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors disabled:opacity-50"
                    >
                      {processingGroupId === duplicateGroup.id ? 'Processing...' : (
                        <>
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                          Reject
                        </>
                      )}
                    </button>

                    {/* Keep merge functionality available for auto-suggested groups too */}
                    {hasUnmergedDuplicates && (
                      <button
                        onClick={() => setIsMergeModalOpen(true)}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        disabled={!hasUnmergedDuplicates}
                      >
                        <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                        </svg>
                        Merge Group
                      </button>
                    )}
                  </>
                ) : duplicateGroup.status === "PENDING" ? (
                  <>
                    <button
                      onClick={() => handleStatusChange("READY_TO_MERGE")}
                      disabled={duplicateGroup.duplicateDonors.length <= 1}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-green-600"
                    >
                      <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Mark as Ready to Merge
                    </button>
                    {hasUnmergedDuplicates && (
                      <button
                        onClick={() => setIsMergeModalOpen(true)}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        disabled={!hasUnmergedDuplicates}
                      >
                        <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                        </svg>
                        Merge Group
                      </button>
                    )}
                  </>
                ) : (
                  <>
                    {duplicateGroup.status !== "MERGED" && (
                      <button
                        onClick={() => handleStatusChange("PENDING")}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
                      >
                        <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        Mark as Unconfirmed
                      </button>
                    )}
                    {hasUnmergedDuplicates && (
                      <button
                        onClick={() => setIsMergeModalOpen(true)}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        disabled={!hasUnmergedDuplicates}
                      >
                        <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                        </svg>
                        {duplicateGroup.status === "MERGED" ? "Merge New Duplicates" : "Merge Group"}
                      </button>
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </div>

        {duplicateGroup.status === "MERGED" && (
          <div className="card-bg rounded-lg shadow-sm border overflow-hidden transition-theme mb-6">
            <div className="px-6 py-4 bg-green-50 border-b border-green-200">
              <h2 className="text-lg font-semibold text-green-800">Merge Information</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-semibold text-gray-500 mb-2">Merge Date</h3>
                  <p className="text-base text-gray-900">
                    {duplicateGroup.mergedAt ? new Date(duplicateGroup.mergedAt).toLocaleString() : "Unknown"}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-gray-500 mb-2">Merged By</h3>
                  <p className="text-base text-gray-900">
                    {duplicateGroup.mergedBy && duplicateGroup.mergedBy !== "System" ? duplicateGroup.mergedBy : "Unknown User"}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-gray-500 mb-2">Donors Merged</h3>
                  <p className="text-base text-gray-900">
                    {duplicateGroup.duplicateDonors.length - (duplicateGroup.primaryDonor ? 1 : 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="card-bg rounded-lg shadow-sm border overflow-hidden transition-theme mb-6">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-800">Donor Comparison</h2>
            <p className="text-sm text-gray-700">Compare details between the primary donor and duplicates.</p>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Field
                  </th>
                  {allDonorIds.map(donorId => (
                    <th key={donorId} scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {duplicateGroup.primaryDonor?.kpfaId === donorId ? (
                        <span className="flex items-center">
                          <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                          Primary ({donorId})
                        </span>
                      ) : (
                        <span className="flex items-center">
                          {donorDetails[donorId]?.dataSource === 'stored' ? (
                            <>
                              <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                              Merged ({donorId})
                            </>
                          ) : (
                            <>
                              <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                              Duplicate ({donorId})
                            </>
                          )}
                        </span>
                      )}
                      {donorDetails[donorId]?.loading && (
                        <span className="ml-2 inline-block">
                          <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </span>
                      )}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {[
                  { key: 'firstname', label: 'First Name' },
                  { key: 'lastname', label: 'Last Name' },
                  { key: 'email', label: 'Email' },
                  { key: 'phone', label: 'Phone' },
                  { key: 'address1', label: 'Address 1' },
                  { key: 'address2', label: 'Address 2' },
                  { key: 'city', label: 'City' },
                  { key: 'state', label: 'State' },
                  { key: 'postal_code', label: 'Postal Code' },
                  { key: 'country', label: 'Country' },
                  { key: 'membership_level', label: 'Membership Level' },
                  { key: 'type', label: 'Donor Type' },
                  { key: 'notes', label: 'Notes' },
                  { key: 'donotsolicit', label: 'Do Not Solicit' },
                  { key: 'deceased', label: 'Deceased' },
                  { key: 'stripe_cus_id', label: 'Stripe Customer ID' },
                  { key: 'allegiance_id', label: 'Allegiance ID' },
                  { key: 'memsys_id', label: 'Memsys ID' }
                ].map(({ key, label }) => (
                  <tr key={key} className="hover-bg transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800">
                      {label}
                    </td>
                    {allDonorIds.map(donorId => {
                      const donor = donorDetails[donorId];
                      
                      if (donor?.loading) {
                        return (
                          <td key={donorId} className="px-6 py-4 whitespace-nowrap">
                            <div className="animate-pulse">
                              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            </div>
                          </td>
                        );
                      }
                      
                      if (donor?.error) {
                        return (
                          <td key={donorId} className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-600">
                              {key === 'firstname' ? (
                                <div className="flex flex-col">
                                  {donor.dataSource !== 'stored' ? (
                                    <>
                                      <span>Error loading donor</span>
                                      <span className="text-xs opacity-75">No donor found.</span>
                                    </>
                                  ) : (
                                    <>
                                      <span>Merged Donor</span>
                                      <span className="text-xs opacity-75">Data stored in Keystone</span>
                                    </>
                                  )}
                                </div>
                              ) : '-'}
                            </div>
                          </td>
                        );
                      }
                      
                      const value = getDonorProperty(donor?.details, key);
                      return (
                        <td key={donorId} className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm">
                            {value === undefined || value === null ? (
                              <span className="text-muted">-</span>
                            ) : key === 'stripe_cus_id' && value ? (
                              <a 
                                href={`https://dashboard.stripe.com/customers/${value}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 underline"
                              >
                                {value}
                              </a>
                            ) : typeof value === 'boolean' ? (
                              <span className={value ? 'text-green-600' : 'text-red-600'}>
                                {value ? 'Yes' : 'No'}
                              </span>
                            ) : (
                              value
                            )}
                          </div>
                        </td>
                      );
                    })}
                  </tr>
                ))}
                
                {/* Add Remove buttons row only for non-merged groups and if there are multiple donors */}
                {duplicateGroup.status !== "MERGED" && allDonorIds.length > 1 && (
                  <tr className="bg-red-50 hover:bg-red-100 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-800">
                      Remove from Group
                    </td>
                    {allDonorIds.map(donorId => {
                      const donor = donorDetails[donorId];
                      const duplicateDonor = duplicateGroup.duplicateDonors?.find(d => d.kpfaId === donorId);
                      const isDuplicateDonor = !!duplicateDonor;
                      const isPrimaryDonor = duplicateGroup.primaryDonor?.kpfaId === donorId;
                      
                      // Don't show remove button if this donor can't be found or doesn't exist in the group
                      if (!isDuplicateDonor && !isPrimaryDonor) {
                        return (
                          <td key={donorId} className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-400">N/A</span>
                          </td>
                        );
                      }

                      const donorName = donor?.details ? 
                        `${getDonorProperty(donor.details, 'firstname') || ''} ${getDonorProperty(donor.details, 'lastname') || ''}`.trim() ||
                        `Donor #${donorId}` :
                        `Donor #${donorId}`;

                      return (
                        <td key={donorId} className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => handleRemoveDonor(duplicateDonor?.id || duplicateGroup.primaryDonor?.id || '', donorName, donorId)}
                            disabled={removingDonorId === (duplicateDonor?.id || duplicateGroup.primaryDonor?.id)}
                            className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                            title={`Remove ${donorName} from this duplicate group`}
                          >
                            {removingDonorId === (duplicateDonor?.id || duplicateGroup.primaryDonor?.id) ? (
                              <>
                                <svg className="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Removing...
                              </>
                            ) : (
                              <>
                                <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                Remove
                              </>
                            )}
                          </button>
                        </td>
                      );
                    })}
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Delete Group Confirmation Modal */}
        {confirmDeleteModal.isOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4 text-red-600">
                Delete Group
              </h3>
              
              <div className="mb-4">
                <p className="text-gray-700 mb-3">
                  Delete "{confirmDeleteModal.groupName}"?
                </p>
                
                {confirmDeleteModal.hasMembers && (
                  <p className="text-sm text-gray-600">
                    This removes the duplicate tracking records in Keystone only. Station Admin donor data remains untouched.
                  </p>
                )}


              </div>
              
              <div className="flex gap-3 justify-end">
                <button
                  onClick={closeDeleteModal}
                  disabled={deletingGroup}
                  className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 cursor-pointer disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDeleteGroup}
                  disabled={deletingGroup}
                  className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed flex items-center"
                >
                  {deletingGroup ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    `Delete Group${confirmDeleteModal.hasMembers ? ' & Members' : ''}`
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Merge Donors Modal */}
        <MergeDonorsModal
          isOpen={isMergeModalOpen}
          onClose={handleMergeComplete}
          primaryDonorId={primaryDonorId}
          primaryDonorDetails={primaryDonorDetails}
          duplicateDonors={duplicateDonorsForModal}
          batchMode={true}
          allGroups={[{
            id: duplicateGroup.id,
            primaryDonorId: primaryDonorId,
            primaryDonorName: primaryDonorDetails ? 
              `${getDonorProperty(primaryDonorDetails, 'firstname') || ''} ${getDonorProperty(primaryDonorDetails, 'lastname') || ''}` : 
              `Donor #${primaryDonorId}`,
            groupName: duplicateGroup.name,
            duplicateCount: duplicateDonorsForModal.length,
            duplicateDonors: duplicateDonorsForModal.map(d => ({ kpfaId: d.kpfaId }))
          }]}
        />

        {/* Success/Error Modal for Suggested Duplicates */}
        {modalState.isOpen && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full ${
                  modalState.type === 'success' ? 'bg-green-100' : 'bg-red-100'
                }`}>
                  {modalState.type === 'success' ? (
                    <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  ) : (
                    <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  )}
                </div>
                <h3 className={`text-lg leading-6 font-medium ${
                  modalState.type === 'success' ? 'text-green-900' : 'text-red-900'
                } mt-2`}>
                  {modalState.title}
                </h3>
                <div className={`mt-2 px-7 py-3 ${
                  modalState.type === 'success' ? 'text-green-700' : 'text-red-700'
                }`}>
                  <p className="text-sm">{modalState.message}</p>
                </div>
                <div className="items-center px-4 py-3">
                  <button
                    onClick={() => setModalState({ ...modalState, isOpen: false })}
                    className={`px-4 py-2 ${
                      modalState.type === 'success' 
                        ? 'bg-green-500 hover:bg-green-700' 
                        : 'bg-red-500 hover:bg-red-700'
                    } text-white text-base font-medium rounded-md w-full shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      modalState.type === 'success' ? 'focus:ring-green-500' : 'focus:ring-red-500'
                    }`}
                  >
                    OK
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AuthRequired>
  );
} 