"use client";

import React, { useState } from "react";
import { gql, useQuery, useMutation, useApolloClient } from "@apollo/client";
import Link from "next/link";
import { usePagination } from "@/lib/hooks/usePagination";
import { useSearch } from "@/lib/hooks/useSearch";
import { useAuth } from "@/lib/authContext";
import AuthRequired from "@/components/AuthRequired";
import { MergeDonorsModal } from "@/components/MergeDonorsModal";
import SearchInput from "@/components/ui/SearchInput";
import ConfirmationModal from "@/components/ui/ConfirmationModal";
import Pagination from "@/components/ui/Pagination";


// Simple Button component
interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
  variant?: "primary" | "secondary" | "outline";
  size?: "sm" | "md" | "lg";
  className?: string;
}

function Button({
  children,
  onClick,
  disabled = false,
  isLoading = false,
  variant = "primary",
  size = "md",
  className = "",
}: ButtonProps) {
  const baseStyle =
    "inline-flex items-center justify-center font-medium rounded-md focus:outline-none transition-colors";

  const variantStyles = {
    primary: "bg-blue-600 hover:bg-blue-700 text-white",
    secondary: "bg-gray-200 hover:bg-gray-300 text-gray-800",
    outline:
      "bg-transparent border border-gray-300 hover:bg-gray-50 text-gray-700",
  };

  const sizeStyles = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2",
    lg: "px-6 py-3 text-lg",
  };

  const style = `${baseStyle} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`;

  return (
    <button
      onClick={onClick}
      disabled={disabled || isLoading}
      className={style}
      style={{
        opacity: disabled ? 0.6 : 1,
        cursor: disabled ? "not-allowed" : "pointer",
      }}
    >
      {isLoading ? (
        <span className="flex items-center">
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Loading...
        </span>
      ) : (
        children
      )}
    </button>
  );
}

// Types definition
interface DuplicateDonor {
  id: string;
  kpfaId: number;
}

interface DuplicateGroup {
  id: string;
  name: string;
  primaryDonor: {
    id: string;
    kpfaId: number;
  } | null;
  duplicateDonors: {
    id: string;
    kpfaId: number;
  }[];
  status: string;
  mergedAt?: string;
  mergedBy?: string;
  autoCreated?: boolean;
}

// GraphQL query for duplicate donors with pagination
const DUPLICATE_GROUPS_QUERY = gql`
  query GetDuplicateGroups($where: DuplicateGroupWhereInput!, $take: Int!, $skip: Int!) {
    duplicateGroups(where: $where, take: $take, skip: $skip, orderBy: [{ name: asc }]) {
      id
      name
      primaryDonor {
        id
        kpfaId
      }
      duplicateDonors {
        id
        kpfaId
      }
      status
      mergedAt
      mergedBy
      autoCreated
    }
    duplicateGroupsCount(where: $where)
  }
`;

// GraphQL query for ready to merge groups (kept separate for bulk merge functionality)
const READY_TO_MERGE_GROUPS_QUERY = gql`
  query GetReadyToMergeGroups {
    duplicateGroups(where: { status: { equals: "READY_TO_MERGE" } }) {
      id
      name
      primaryDonor {
        id
        kpfaId
      }
      duplicateDonors {
        id
        kpfaId
      }
      status
      mergedAt
      mergedBy
    }
  }
`;

// GraphQL mutation for updating group status
const UPDATE_GROUP_STATUS_MUTATION = gql`
  mutation UpdateDuplicateGroupStatus($id: ID!, $status: String!) {
    updateDuplicateGroup(where: { id: $id }, data: { status: $status }) {
      id
      status
    }
  }
`;

// GraphQL mutation for deleting empty duplicate groups
const DELETE_DUPLICATE_GROUP_MUTATION = gql`
  mutation DeleteDuplicateGroup($id: ID!) {
    deleteDuplicateGroup(where: { id: $id }) {
      id
      name
    }
  }
`;

// GraphQL mutation for deleting groups with members
const DELETE_GROUP_WITH_MEMBERS_MUTATION = gql`
  mutation DeleteGroupWithMembers($groupId: ID!, $rejectGroupSuggestion: Boolean) {
    deleteGroupWithMembers(groupId: $groupId, rejectGroupSuggestion: $rejectGroupSuggestion) {
      id
      name
      groupRejected
      deletedDonorCount
    }
  }
`;

// GraphQL query for suggested duplicate groups
const SUGGESTED_DUPLICATE_GROUPS_QUERY = gql`
  query GetSuggestedDuplicateGroups($where: DuplicateGroupWhereInput!, $take: Int!, $skip: Int!) {
    duplicateGroups(
      where: $where
      take: $take
      skip: $skip
      orderBy: [{ createdAt: desc }]
    ) {
      id
      name
      status
      createdAt
      primaryDonor {
        id
        kpfaId
        firstname
        lastname
        phone
      }
      duplicateDonors {
        id
        kpfaId
        firstname
        lastname
        phone
      }
    }
    duplicateGroupsCount(where: $where)
  }
`;

// GraphQL mutations for suggested duplicates
const ACCEPT_SUGGESTION_MUTATION = gql`
  mutation AcceptSuggestion($groupId: ID!, $staffMember: String!, $newStatus: String!) {
    updateDuplicateGroup(
      where: { id: $groupId }
      data: { 
        status: $newStatus
        reviewedBy: $staffMember
      }
    ) {
      id
      status
      reviewedBy
    }
  }
`;

const REJECT_SUGGESTION_MUTATION = gql`
  mutation RejectSuggestion($groupId: ID!, $staffMember: String!) {
    rejectSuggestion(groupId: $groupId, staffMember: $staffMember) {
      id
      name
      status
      reviewedBy
    }
  }
`;

const MARK_DONORS_NOT_DUPLICATES_MUTATION = gql`
  mutation MarkDonorsAsNotDuplicates($kpfaIds: [Int!]!, $staffMember: String!) {
    markDonorsAsNotDuplicates(kpfaIds: $kpfaIds, staffMember: $staffMember) {
      id
      name
      status
      reviewedBy
    }
  }
`;

export default function DuplicateDonorsClient() {
  const { user } = useAuth();
  const apolloClient = useApolloClient();
  const [updateStatus] = useMutation(UPDATE_GROUP_STATUS_MUTATION);
  const [deleteGroup] = useMutation(DELETE_DUPLICATE_GROUP_MUTATION);
  const [deleteGroupWithMembers] = useMutation(DELETE_GROUP_WITH_MEMBERS_MUTATION);
  const [mergeResult, setMergeResult] = useState<any>(null);
  const [isMergeModalOpen, setIsMergeModalOpen] = useState(false);
  const [selectedGroups, setSelectedGroups] = useState<DuplicateGroup[]>([]);
  const [deletingGroupId, setDeletingGroupId] = useState<string | null>(null);
  const [confirmDeleteModal, setConfirmDeleteModal] = useState<{
    isOpen: boolean;
    groupId: string;
    groupName: string;
    hasMembers: boolean;
    isAutoCreated: boolean;
  }>({
    isOpen: false,
    groupId: "",
    groupName: "",
    hasMembers: false,
    isAutoCreated: false,
  });
  
  // State for advanced delete options


  // Search setup with URL synchronization
  const search = useSearch({
    urlSync: false,
    urlParamName: 'search',
    debounceMs: 300,
  });

  // Pagination setup with URL synchronization
  const pagination = usePagination({
    initialItemsPerPage: 20,
    maxItemsPerPage: 100,
    urlSync: false,
    scrollToTopOnPageChange: true,
  });

  // Suggested duplicates pagination
  const suggestedPagination = usePagination({
    initialItemsPerPage: 10,
    maxItemsPerPage: 50,
    urlSync: false,
    scrollToTopOnPageChange: true,
    scrollTargetSelector: '.suggestions-section',
  });

  // Search setup for suggested duplicates
  const suggestedSearch = useSearch({
    urlSync: false,
    urlParamName: 'suggestedSearch',
    debounceMs: 300,
  });

  // New state for suggested duplicates
  const [processingGroupId, setProcessingGroupId] = useState<string | null>(null);
  const [showSuggestedSection, setShowSuggestedSection] = useState(true);

  // Add modal state for success/failure messages
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    type: 'success' | 'error';
    title: string;
    message: string;
  }>({
    isOpen: false,
    type: 'success',
    title: '',
    message: ''
  });

  // Track if we've ever received data to prevent skeleton flash
  const [hasEverReceivedData, setHasEverReceivedData] = React.useState(false);
  
  // Add state tracking for suggested duplicates data reception
  const [hasEverReceivedSuggestedData, setHasEverReceivedSuggestedData] = React.useState(false);

  // Add states for manual pair marking modal
  const [isPairModalOpen, setIsPairModalOpen] = useState(false);
  const [pairKpfaId1, setPairKpfaId1] = useState<number>(0);
  const [pairKpfaId2, setPairKpfaId2] = useState<number>(0);

  // Build the where clause for filtering - simplified to always show PENDING status
  const whereClause = React.useMemo(() => {
    const conditions: any[] = [];

    // Add search condition if search term exists
    if (search.debouncedQuery.trim()) {
      const searchTerm = search.debouncedQuery.toLowerCase();
      const numericSearch = parseInt(searchTerm);
      
      const orConditions: any[] = [
        { name: { contains: searchTerm, mode: "insensitive" } }
      ];

      // Only search by kpfaId for reasonable donor ID numbers (under 1 million)
      // This excludes phone numbers and other large numbers from being treated as donor IDs
      if (!isNaN(numericSearch) && numericSearch > 0 && numericSearch < 1000000) {
        orConditions.push(
          { primaryDonor: { kpfaId: { equals: numericSearch } } },
          { duplicateDonors: { some: { kpfaId: { equals: numericSearch } } } }
        );
      }

      conditions.push({ OR: orConditions });
    }

    // Always filter to PENDING status for Unconfirmed Duplicate Groups
    conditions.push({ status: { equals: "PENDING" } });

    return conditions.length > 0 ? { AND: conditions } : { status: { equals: "PENDING" } };
  }, [search.debouncedQuery]);

  // Build the where clause for suggested duplicates
  const suggestedWhereClause = React.useMemo(() => {
    const where: any = {
      status: { equals: "SUGGESTED" }
    };

    if (suggestedSearch.debouncedQuery.trim()) {
      const searchTerm = suggestedSearch.debouncedQuery.toLowerCase();
      const numericSearch = parseInt(searchTerm);
      
      const orConditions: any[] = [
        { name: { contains: searchTerm, mode: "insensitive" } },
        // Search in donor names and phone numbers for suggested duplicates
        { primaryDonor: { firstname: { contains: searchTerm, mode: "insensitive" } } },
        { primaryDonor: { lastname: { contains: searchTerm, mode: "insensitive" } } },
        { primaryDonor: { phone: { contains: searchTerm, mode: "insensitive" } } },
        { duplicateDonors: { some: { firstname: { contains: searchTerm, mode: "insensitive" } } } },
        { duplicateDonors: { some: { lastname: { contains: searchTerm, mode: "insensitive" } } } },
        { duplicateDonors: { some: { phone: { contains: searchTerm, mode: "insensitive" } } } }
      ];

      // Only search by kpfaId for reasonable donor ID numbers (under 1 million)
      // This excludes phone numbers from being treated as donor IDs
      if (!isNaN(numericSearch) && numericSearch > 0 && numericSearch < 1000000) {
        orConditions.push(
          { primaryDonor: { kpfaId: { equals: numericSearch } } },
          { duplicateDonors: { some: { kpfaId: { equals: numericSearch } } } }
        );
      }

      where.OR = orConditions;
    }

    return where;
  }, [suggestedSearch.debouncedQuery]);

  // Memoize query variables to prevent unnecessary re-renders
  const queryVariables = React.useMemo(() => {
    const vars = {
      where: whereClause,
      take: pagination.paginationState.take,
      skip: pagination.paginationState.skip,
    };
    return vars;
  }, [whereClause, pagination.paginationState.take, pagination.paginationState.skip]);

  // Memoize suggested duplicates query variables
  const suggestedQueryVariables = React.useMemo(() => {
    const vars = {
      where: suggestedWhereClause,
      take: suggestedPagination.paginationState.take,
      skip: suggestedPagination.paginationState.skip,
    };
    return vars;
  }, [suggestedWhereClause, suggestedPagination.paginationState.take, suggestedPagination.paginationState.skip]);

  // Main query with pagination
  const { loading, error, data, refetch } = useQuery(DUPLICATE_GROUPS_QUERY, {
    variables: queryVariables,
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
    onError: (error) => {
      console.error('❌ Query error:', error);
    },
    onCompleted: (data) => {
      console.log('✅ Main query completed:', data);
    },
  });

  // Separate query for ready to merge groups
  const { data: readyToMergeData, loading: readyToMergeLoading, error: readyToMergeError } = useQuery(READY_TO_MERGE_GROUPS_QUERY, {
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
    onError: (error) => {
      console.error('❌ Ready to merge query error:', error);
    },
    onCompleted: (data) => {
      console.log('✅ Ready to merge query completed:', data);
    },
  });

  // Query for suggested duplicate groups
  const {
    data: suggestedData,
    loading: suggestedLoading,
    error: suggestedError,
    refetch: refetchSuggested
  } = useQuery(SUGGESTED_DUPLICATE_GROUPS_QUERY, {
    variables: suggestedQueryVariables,
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
    onError: (error) => {
      console.error('❌ Suggested query error:', error);
    },
    onCompleted: (data) => {
      console.log('✅ Suggested query completed:', data);
    },
  });

  // Mutations for suggested duplicates
  const [acceptSuggestion] = useMutation(ACCEPT_SUGGESTION_MUTATION);
  const [rejectSuggestion] = useMutation(REJECT_SUGGESTION_MUTATION);
  const [markDonorsNotDuplicates] = useMutation(MARK_DONORS_NOT_DUPLICATES_MUTATION);

  const duplicateGroups: DuplicateGroup[] = data?.duplicateGroups || [];
  const totalItems = data?.duplicateGroupsCount || 0;
  const readyToMergeGroups: DuplicateGroup[] = readyToMergeData?.duplicateGroups || [];

  // Extract suggested duplicates data
  const suggestedGroups: DuplicateGroup[] = suggestedData?.duplicateGroups || [];
  const suggestedTotalItems = suggestedData?.duplicateGroupsCount || 0;

  React.useEffect(() => {
    if (data) {
      setHasEverReceivedData(true);
    }
  }, [data]);

  // Add effect for suggested duplicates data tracking
  React.useEffect(() => {
    if (suggestedData) {
      setHasEverReceivedSuggestedData(true);
    }
  }, [suggestedData]);

  // Handle search changes
  const handleSearchChange = React.useCallback((value: string) => {
    // Update search query and reset pagination when search changes
    search.setSearchQuery(value);
    
    // Reset pagination when search changes (immediately for responsive feel)
    if (value !== search.searchQuery) {
      pagination.resetPagination();
    }
  }, [search, pagination]);

  // Handle suggested duplicates search changes
  const handleSuggestedSearchChange = React.useCallback((value: string) => {
    // Update search query and reset pagination when search changes
    suggestedSearch.setSearchQuery(value);
    
    // Reset pagination when search changes (immediately for responsive feel)
    if (value !== suggestedSearch.searchQuery) {
      suggestedPagination.resetPagination();
    }
  }, [suggestedSearch, suggestedPagination]);

  // Handle clearing suggested search
  const handleClearSuggestedSearch = React.useCallback(() => {
    suggestedSearch.setSearchQuery('');
    // Reset pagination immediately for clear action
    suggestedPagination.resetPagination();
  }, [suggestedSearch, suggestedPagination]);

  // Only show skeleton on the very first load, never during updates
  const isInitialLoading = loading && !hasEverReceivedData;

  // Add initial loading state for suggested duplicates
  const isSuggestedInitialLoading = suggestedLoading && !hasEverReceivedSuggestedData;

  const handleStatusChange = async (groupId: string, newStatus: string) => {
    try {
      await updateStatus({
        variables: { id: groupId, status: newStatus },
        optimisticResponse: {
          updateDuplicateGroup: {
            id: groupId,
            status: newStatus,
            __typename: 'DuplicateGroup'
          }
        },
        // Refetch both queries to ensure cache consistency
        refetchQueries: [
          { query: DUPLICATE_GROUPS_QUERY, variables: queryVariables },
          { query: READY_TO_MERGE_GROUPS_QUERY }
        ],
        awaitRefetchQueries: false
      });
    } catch (error) {
      console.error("Error updating status:", error);
    }
  };

  const handleDeleteGroup = async (group: DuplicateGroup) => {
    const hasMembers = group.duplicateDonors.length > 0 || !!group.primaryDonor;
    const isAutoCreated = group.autoCreated || false;
    
    setConfirmDeleteModal({
      isOpen: true,
      groupId: group.id,
      groupName: group.name,
      hasMembers,
      isAutoCreated,
    });
    

  };

  const confirmDeleteGroup = async () => {
    const { groupId, hasMembers } = confirmDeleteModal;
    setDeletingGroupId(groupId);
    setConfirmDeleteModal({ 
      isOpen: false, 
      groupId: "", 
      groupName: "", 
      hasMembers: false, 
      isAutoCreated: false 
    });
    
    try {
      if (hasMembers) {
        // Use the new mutation for groups with members
        // Automatically reject auto-created group suggestions to prevent re-suggestions
        const result = await deleteGroupWithMembers({
          variables: { 
            groupId, 
            rejectGroupSuggestion: confirmDeleteModal.isAutoCreated 
          },
          // Refetch all relevant queries
          refetchQueries: [
            { query: DUPLICATE_GROUPS_QUERY, variables: queryVariables },
            { query: READY_TO_MERGE_GROUPS_QUERY },
            { query: SUGGESTED_DUPLICATE_GROUPS_QUERY, variables: suggestedQueryVariables }
          ],
          awaitRefetchQueries: false
        });

        // Show success message with details
        const deletedData = result.data?.deleteGroupWithMembers;
        let message = `Group "${deletedData?.name}" has been deleted along with ${deletedData?.deletedDonorCount} duplicate donor records.`;
        
        if (deletedData?.groupRejected) {
          message += ' The group suggestion has also been rejected to prevent future suggestions.';
        }

        setModalState({
          isOpen: true,
          type: 'success',
          title: 'Group Deleted Successfully',
          message
        });
      } else {
        // Use the original mutation for empty groups
        await deleteGroup({
          variables: { id: groupId },
          refetchQueries: [
            { query: DUPLICATE_GROUPS_QUERY, variables: queryVariables },
            { query: READY_TO_MERGE_GROUPS_QUERY }
          ],
          awaitRefetchQueries: false
        });

        setModalState({
          isOpen: true,
          type: 'success',
          title: 'Empty Group Deleted',
          message: 'The empty duplicate group has been successfully deleted.'
        });
      }
    } catch (err) {
      console.error("Error deleting group:", err);
      setModalState({
        isOpen: true,
        type: 'error',
        title: 'Delete Failed',
        message: err instanceof Error ? err.message : 'Failed to delete group. Please try again.'
      });
    } finally {
      setDeletingGroupId(null);
    }
  };

  const closeDeleteModal = () => {
    setConfirmDeleteModal({ 
      isOpen: false, 
      groupId: "", 
      groupName: "", 
      hasMembers: false, 
      isAutoCreated: false 
    });

  };

  const handleBulkMerge = () => {
    // Use readyToMergeGroups instead of filtering duplicateGroups
    if (readyToMergeGroups.length === 0) {
      alert("No groups are marked as ready to merge");
      return;
    }

    // Filter out groups without primary donors
    const validGroups = readyToMergeGroups.filter((group) => group.primaryDonor);

    if (validGroups.length === 0) {
      alert("No groups with primary donors set");
      return;
    }

    // Set the selected groups and open the modal with batch mode
    setSelectedGroups(validGroups);
    setIsMergeModalOpen(true);
  };

  const handleMergeModalClose = () => {
    // Reset state
    setIsMergeModalOpen(false);
    setSelectedGroups([]);
    
    // Refetch both queries to ensure we have the latest data after merge operations
    
    // Refetch the main duplicate groups query
    refetch();
    
    // Refetch the ready-to-merge groups query using Apollo client
    apolloClient.refetchQueries({
      include: [READY_TO_MERGE_GROUPS_QUERY]
    });
    
    // Also invalidate the merged groups cache to refresh that page
    // Use both query name and operation name for better cache invalidation
    apolloClient.refetchQueries({
      include: ['MergedDuplicateGroups']
    });
    
    // Also try to invalidate by clearing the cache for duplicate groups
    apolloClient.cache.evict({ fieldName: 'duplicateGroups' });
    apolloClient.cache.gc();
    
    // Trigger merged groups page refresh if it's available
    if (typeof window !== 'undefined' && (window as any).refreshMergedGroups) {
      (window as any).refreshMergedGroups();
    }
    
    // Clear any merge result state
    setMergeResult(null);
  };

  // New functions for suggested duplicates
  const handleAcceptSuggestion = async (
    groupId: string,
    moveToReady: boolean = false
  ) => {
    setProcessingGroupId(groupId);
    
    // Get staff member name from user context
    const staffMember = user?.firstname && user?.lastname 
      ? `${user.firstname} ${user.lastname}` 
      : 'Unknown Staff';

    try {
      await acceptSuggestion({
        variables: {
          groupId,
          staffMember,
          newStatus: moveToReady ? 'READY_TO_MERGE' : 'PENDING'
        },
        // Refetch both queries to ensure cache consistency
        refetchQueries: [
          { query: SUGGESTED_DUPLICATE_GROUPS_QUERY, variables: suggestedQueryVariables },
          { query: DUPLICATE_GROUPS_QUERY, variables: queryVariables },
          ...(moveToReady ? [{ query: READY_TO_MERGE_GROUPS_QUERY }] : [])
        ],
        awaitRefetchQueries: false
      });

      setModalState({
        isOpen: true,
        type: 'success',
        title: 'Suggestion Accepted',
        message: `The duplicate suggestion has been accepted and moved to ${moveToReady ? 'Ready to Merge' : 'Pending'}.`
      });
    } catch (error) {
      console.error('Error accepting suggestion:', error);
      setModalState({
        isOpen: true,
        type: 'error',
        title: 'Error',
        message: 'Error accepting suggestion. Please try again.'
      });
    } finally {
      setProcessingGroupId(null);
    }
  };

  const handleRejectSuggestion = async (groupId: string) => {
    setProcessingGroupId(groupId);
    
    // Get staff member name from user context
    const staffMember = user?.firstname && user?.lastname 
      ? `${user.firstname} ${user.lastname}` 
      : 'Unknown Staff';

    try {
      await rejectSuggestion({
        variables: {
          groupId,
          staffMember
        },
        // Refetch both queries to ensure cache consistency
        refetchQueries: [
          { query: SUGGESTED_DUPLICATE_GROUPS_QUERY, variables: suggestedQueryVariables },
          { query: DUPLICATE_GROUPS_QUERY, variables: queryVariables }
        ],
        awaitRefetchQueries: false
      });

      setModalState({
        isOpen: true,
        type: 'success',
        title: 'Suggestion Rejected',
        message: 'The duplicate suggestion has been successfully rejected.'
      });
    } catch (error) {
      console.error('Error rejecting suggestion:', error);
      setModalState({
        isOpen: true,
        type: 'error',
        title: 'Rejection Failed',
        message: error instanceof Error ? error.message : 'Failed to reject suggestion. Please try again.'
      });
    } finally {
      setProcessingGroupId(null);
    }
  };

  const handleMarkPairNotDuplicates = async () => {
    if (!pairKpfaId1 || !pairKpfaId2) {
      setModalState({
        isOpen: true,
        type: 'error',
        title: 'Missing Information',
        message: 'Please provide both KPFA IDs.'
      });
      return;
    }

    // Get staff member name from user context
    const staffMember = user?.firstname && user?.lastname 
      ? `${user.firstname} ${user.lastname}` 
      : 'Unknown Staff';

    try {
      await markDonorsNotDuplicates({
        variables: {
          kpfaIds: [pairKpfaId1, pairKpfaId2],
          staffMember
        }
      });

      setModalState({
        isOpen: true,
        type: 'success',
        title: 'Donors Marked as NOT Duplicates',
        message: 'The donors have been successfully marked as NOT duplicates.'
      });
      
      setIsPairModalOpen(false);
      setPairKpfaId1(0);
      setPairKpfaId2(0);
    } catch (error) {
      console.error('Error marking donors as not duplicates:', error);
      setModalState({
        isOpen: true,
        type: 'error',
        title: 'Operation Failed',
        message: error instanceof Error ? error.message : 'Failed to mark donors as not duplicates. Please try again.'
      });
    }
  };

  const formatDonorName = (donor: any) => {
    if (!donor) return 'Unknown';
    const name = `${donor.firstname || ''} ${donor.lastname || ''}`.trim();
    return name || `Donor ${donor.kpfaId}`;
  };

  const formatPhoneForDisplay = (phone: string) => {
    if (!phone) return '';
    // Simple formatting for display
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  };

  const getSuggestionTypeLabel = (type: string) => {
    switch (type) {
      case 'NEW_GROUP': return 'New Group';
      case 'ADD_TO_GROUP': return 'Add to Existing';
      default: return type || 'Unknown';
    }
  };

  // Create a skeleton/placeholder table instead of hiding everything
  const renderSkeletonTable = () => (
    <div className="card-bg rounded-lg shadow-sm border overflow-hidden transition-theme">
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <div className="h-6 bg-gray-200 rounded w-64 mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 animate-pulse"></div>
          </div>
          <div className="w-full sm:w-1/3">
            <div className="flex space-x-2">
              <div className="h-10 bg-gray-200 rounded flex-1 animate-pulse"></div>
              <div className="h-10 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {[...Array(4)].map((_, i) => (
                <th key={i} className="px-6 py-3">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {[...Array(5)].map((_, i) => (
              <tr key={i}>
                {[...Array(4)].map((_, j) => (
                  <td key={j} className="px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Debug logging
  React.useEffect(() => {
    console.log('🔍 Query Variables:', queryVariables);
    console.log('🔍 Loading states:', { loading, readyToMergeLoading, suggestedLoading });
    console.log('🔍 Data states:', { 
      duplicateGroups: duplicateGroups.length, 
      readyToMergeGroups: readyToMergeGroups.length, 
      suggestedGroups: suggestedGroups.length 
    });
    console.log('🔍 Errors:', { error, readyToMergeError, suggestedError });
  }, [queryVariables, loading, readyToMergeLoading, suggestedLoading, duplicateGroups, readyToMergeGroups, suggestedGroups, error, readyToMergeError, suggestedError]);

  if (error)
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] max-w-lg mx-auto px-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 w-full text-center">
          <svg
            className="w-12 h-12 mx-auto text-red-500 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <h2 className="text-lg font-semibold text-red-700 mb-2">
            Error loading duplicate donor groups
          </h2>
          <p className="text-sm text-red-600 mb-4">
            {error.message}
          </p>
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Return to Home
          </Link>
        </div>
      </div>
    );

  return (
    <AuthRequired>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        
        <header className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold mb-2 text-primary transition-theme">
                Duplicate Donor Groups
              </h1>
              <p className="text-secondary transition-theme">
                Manage and view duplicate donor records in the system
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={handleBulkMerge}
                disabled={readyToMergeGroups.length === 0}
              >
                {readyToMergeGroups.length > 0 ? `Merge ${readyToMergeGroups.length} Group${readyToMergeGroups.length !== 1 ? "s" : ""}` : "Merge Groups"}
              </Button>
            </div>
          </div>
        </header>

        {readyToMergeError && (
          <div className="card-bg rounded-lg shadow-sm border overflow-hidden transition-theme mb-6 bg-red-50">
            <div className="px-6 py-4 bg-red-50 border-b border-red-200">
              <h2 className="text-lg font-semibold text-red-700 mb-2">
                Error loading confirmed duplicate groups
              </h2>
              <p className="text-sm text-red-600">
                {readyToMergeError.message}
              </p>
            </div>
          </div>
        )}

        {readyToMergeLoading && !readyToMergeData && (
          <div className="card-bg rounded-lg shadow-sm border overflow-hidden transition-theme mb-6">
            <div className="px-6 py-4 bg-green-50 border-b border-green-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-lg font-semibold text-green-800">
                    Confirmed
                  </h2>
                  <p className="text-sm text-green-800">
                    Loading confirmed duplicate groups...
                  </p>
                </div>
              </div>
            </div>
            <div className="p-8 text-center">
              <div className="inline-flex items-center space-x-2 text-gray-500">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-500"></div>
                <span>Loading...</span>
              </div>
            </div>
          </div>
        )}

        {readyToMergeGroups.length > 0 && (
          <div className="card-bg rounded-lg shadow-sm border overflow-hidden transition-theme mb-6">
            <div className="px-6 py-4 bg-green-50 border-b border-green-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-lg font-semibold text-green-800">
                    Confirmed
                  </h2>
                  <p className="text-sm text-green-800">
                    {readyToMergeGroups.length} group
                    {readyToMergeGroups.length !== 1 ? "s" : ""} ready for bulk
                    merge
                  </p>
                </div>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Group Name
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Primary Donor
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Duplicates
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {readyToMergeGroups.map((group) => (
                    <tr key={group.id} className="hover-bg transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-primary">
                          {group.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm">
                          {group.primaryDonor ? (
                            <span className="badge badge-green">
                              {group.primaryDonor.kpfaId || "N/A"}
                            </span>
                          ) : (
                            <span className="badge badge-yellow">Not Set</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm">
                          <span className="badge badge-blue">
                            {group.duplicateDonors.length} records
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-2">
                          <Link
                            href={`/duplicate-donors/${group.id}`}
                            className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                          >
                            <svg
                              className="w-4 h-4 mr-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              ></path>
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              ></path>
                            </svg>
                            View Details
                          </Link>
                          <button
                            onClick={() =>
                              handleStatusChange(group.id, "PENDING")
                            }
                            className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors cursor-pointer"
                          >
                            <svg
                              className="w-4 h-4 mr-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M6 18L18 6M6 6l12 12"
                              ></path>
                            </svg>
                            Remove
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {mergeResult && (
          <div
            className={`card-bg rounded-lg shadow-sm border overflow-hidden transition-theme mb-6 ${!mergeResult.success ? "bg-red-50" : ""}`}
          >
            <div
              className={`px-6 py-4 ${
                mergeResult.success
                  ? "bg-blue-50 border-b border-blue-200"
                  : "bg-red-50 border-b border-red-200"
              }`}
            >
              <h2
                className={`text-lg font-semibold ${
                  mergeResult.success
                    ? "text-blue-700"
                    : "text-red-700"
                }`}
              >
                {mergeResult.success ? "Merge Results" : "Merge Failed"}
              </h2>
              {mergeResult.success ? (
                <div className="mt-2 text-sm text-blue-600">
                  <p>
                    Successfully merged {mergeResult.details.totalGroups} groups
                  </p>
                </div>
              ) : (
                <div className="mt-2 text-sm text-red-600">
                  <p>{mergeResult.message}</p>
                  {mergeResult.error && (
                    <p className="mt-1 font-mono text-xs bg-red-100 p-2 rounded">
                      {mergeResult.error.details || mergeResult.error.code}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Persistent Header with Search - Always rendered */}
        <div className="card-bg rounded-t-lg shadow-sm border-b border-gray-200 transition-theme mb-0">
          <div className="px-6 py-4 bg-gray-50">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
              <div>
                <h2 className="text-lg font-semibold text-gray-800">
                  Unconfirmed Duplicate Groups
                </h2>
                <p className="text-sm text-gray-700">
                  Manage and review unconfirmed duplicate donor groups in the system
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
                <SearchInput 
                  value={search.searchQuery} 
                  onChange={handleSearchChange} 
                  placeholder="Search by name or donor ID..." 
                />
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area - SearchInput stays above */}
        {isInitialLoading ? (
          renderSkeletonTable()
        ) : duplicateGroups.length === 0 && !loading ? (
          <div className="card-bg rounded-b-lg shadow-sm border border-t-0 overflow-hidden transition-theme mb-8">
            <div className="p-8 text-center">
              <svg
                className="w-12 h-12 mx-auto text-gray-400 mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                ></path>
              </svg>
              {search.debouncedQuery.trim() ? (
                <div>
                  <p className="text-secondary mb-2">
                    No duplicate donor groups found matching your search.
                  </p>
                  <p className="text-sm text-gray-500">
                    Try adjusting your search terms or clearing the search to see all groups.
                  </p>
                </div>
              ) : (
                <p className="text-secondary">
                  No duplicate donor groups found in the system.
                </p>
              )}
            </div>
          </div>
        ) : duplicateGroups.length === 0 && loading ? (
          <div className="card-bg rounded-b-lg shadow-sm border border-t-0 overflow-hidden transition-theme relative mb-8">
            <div className="p-8 text-center">
              <div className="inline-flex items-center space-x-2 text-gray-500">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                <span>Searching...</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="card-bg rounded-b-lg shadow-sm border border-t-0 overflow-hidden transition-theme relative mb-8">
            {/* Subtle loading overlay for search/pagination updates */}
            {loading && data && (
              <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10">
                <div className="bg-white rounded-lg shadow-lg p-3 flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                  <span className="text-sm text-gray-700">Updating...</span>
                </div>
              </div>
            )}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Group Name
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Primary Donor
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Duplicates
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {duplicateGroups.map((group) => (
                    <tr key={group.id} className={`hover-bg transition-colors ${group.status === "MERGED" ? "bg-blue-50" : ""}`}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-primary">
                          {group.name}
                        </div>
                        {group.status === "MERGED" && group.mergedAt && (
                          <div className="text-xs text-gray-500">
                            Merged on {new Date(group.mergedAt).toLocaleDateString()}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {group.primaryDonor ? (
                          <div className="text-sm">
                            <span className="badge badge-green">
                              {group.primaryDonor.kpfaId || "N/A"}
                            </span>
                          </div>
                        ) : (
                          <span className="badge badge-yellow">Not Set</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm">
                          <span className="badge badge-blue">
                            {group.duplicateDonors.length} records
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-2">
                          <Link
                            href={`/duplicate-donors/${group.id}`}
                            className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                          >
                            <svg
                              className="w-4 h-4 mr-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              ></path>
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              ></path>
                            </svg>
                            View Details
                          </Link>
                          {group.status !== "MERGED" && (
                            group.status === "PENDING" ? (
                              <>
                                <button
                                  onClick={() => handleStatusChange(group.id, "READY_TO_MERGE")}
                                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors cursor-pointer"
                                >
                                  <svg
                                    className="w-4 h-4 mr-1"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M5 13l4 4L19 7"
                                    ></path>
                                  </svg>
                                  Confirm
                                </button>
                                
                                {/* For auto-created groups, only show reject button to prevent re-suggestions */}
                                {group.autoCreated ? (
                                  <button
                                    onClick={() => handleRejectSuggestion(group.id)}
                                    disabled={processingGroupId === group.id}
                                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
                                  >
                                    <svg
                                      className="w-4 h-4 mr-1"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M6 18L18 6M6 6l12 12"
                                      ></path>
                                    </svg>
                                    {processingGroupId === group.id ? 'Processing...' : 'Reject'}
                                  </button>
                                ) : (
                                  /* For manually created groups, show delete button */
                                  <button
                                    onClick={() => handleDeleteGroup(group)}
                                    disabled={deletingGroupId === group.id}
                                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                                  >
                                    {deletingGroupId === group.id ? (
                                      <svg
                                        className="w-4 h-4 mr-1 animate-spin"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                      >
                                        <circle
                                          className="opacity-25"
                                          cx="12"
                                          cy="12"
                                          r="10"
                                          stroke="currentColor"
                                          strokeWidth="4"
                                        ></circle>
                                        <path
                                          className="opacity-75"
                                          fill="currentColor"
                                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                        ></path>
                                      </svg>
                                    ) : (
                                      <svg
                                        className="w-4 h-4 mr-1"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                        ></path>
                                      </svg>
                                    )}
                                    {deletingGroupId === group.id ? "Deleting..." : "Delete Group"}
                                  </button>
                                )}
                              </>
                            ) : (
                              <button
                                onClick={() => handleStatusChange(group.id, "PENDING")}
                                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors cursor-pointer"
                              >
                                <svg
                                  className="w-4 h-4 mr-1"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M6 18L18 6M6 6l12 12"
                                  ></path>
                                </svg>
                                Remove
                              </button>
                            )
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {duplicateGroups.length > 0 && (
                <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 text-sm text-gray-700">
                  <span className="font-medium">{duplicateGroups.length}</span> of {totalItems} groups displayed
                </div>
              )}
            </div>
          </div>
        )}

        {/* Pagination for main duplicate groups */}
        {totalItems > 0 && (
          <div className="mb-8">
            <Pagination
              paginationInfo={pagination.paginationInfo(totalItems)}
              onPageChange={(page) => {
                pagination.goToPage(page);
              }}
              onItemsPerPageChange={(itemsPerPage) => {
                pagination.setItemsPerPage(itemsPerPage);
              }}
              showItemsPerPageSelector={true}
              itemsPerPageOptions={[10, 20, 50, 100]}
              className="bg-white p-4 rounded-lg border-gray-200 border"
            />
          </div>
        )}

        {/* Suggested Duplicates Section - moved below main section */}
        <div className="mb-12 suggestions-section">
          <div className="card-bg rounded-lg shadow-sm border overflow-hidden transition-theme">
            <div className="px-8 py-6 bg-gradient-to-r from-purple-50 to-blue-50 border-b border-purple-200">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h2 className="text-lg font-semibold text-purple-800 flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    Suggested Duplicate Groups
                    <span className="ml-2 px-2 py-1 bg-purple-200 text-purple-800 text-xs rounded-full">
                      {isSuggestedInitialLoading ? '...' : suggestedTotalItems} pending
                    </span>
                  </h2>
                  <p className="text-sm text-purple-700 mt-2">
                    Automatically detected phone number duplicates for staff review
                  </p>
                </div>
                <button
                  onClick={() => setShowSuggestedSection(!showSuggestedSection)}
                  className="text-purple-600 hover:text-purple-800 transition-colors cursor-pointer"
                >
                  {showSuggestedSection ? 'Hide' : 'Show'}
                </button>
              </div>
              
              {/* Search input for suggested duplicates */}
              {showSuggestedSection && (
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <SearchInput
                      value={suggestedSearch.searchQuery}
                      onChange={handleSuggestedSearchChange}
                      placeholder="Search suggested duplicates by name, phone, or donor ID..."
                      className="w-full"
                    />
                  </div>
                </div>
              )}
            </div>

            {showSuggestedSection && (
              <div className="p-8 relative">
                {/* Subtle loading overlay for search/pagination updates */}
                {suggestedLoading && suggestedData && (
                  <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10">
                    <div className="bg-white rounded-lg shadow-lg p-3 flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500"></div>
                      <span className="text-sm text-gray-700">Updating...</span>
                    </div>
                  </div>
                )}

                {isSuggestedInitialLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                    <span className="ml-2 text-gray-600">Loading suggestions...</span>
                  </div>
                ) : suggestedError ? (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                    <p className="text-red-700">Error loading suggestions: {suggestedError.message}</p>
                  </div>
                ) : suggestedGroups.length === 0 ? (
                  <div className="text-center py-12">
                    <svg
                      className="w-12 h-12 mx-auto text-gray-400 mb-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                      />
                    </svg>
                    <p className="text-gray-500 mb-2">No suggested duplicate groups found</p>
                    <p className="text-sm text-gray-400">
                      {suggestedSearch.searchQuery.trim() 
                        ? 'Try adjusting your search terms or clear the search to see all suggestions.'
                        : 'The system will automatically detect and suggest potential duplicates based on phone numbers.'}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                      <div className="px-6 py-4 bg-gray-50 border-b border-gray-100">
                        <p className="text-sm font-medium text-gray-700">Phone number duplicates</p>
                      </div>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                              >
                                Suggestion
                              </th>
                              <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                              >
                                Primary Donor
                              </th>
                              <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                              >
                                Duplicates
                              </th>
                              <th
                                scope="col"
                                className="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                              >
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {suggestedGroups.map((group: any) => (
                              <tr key={group.id} className="hover:bg-gray-50 transition-colors">
                                <td className="px-6 py-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {group.primaryDonor?.phone ? formatPhoneForDisplay(group.primaryDonor.phone) : 'Phone number'}
                                  </div>
                                </td>
                                <td className="px-6 py-4">
                                  {group.primaryDonor ? (
                                    <div className="text-sm">
                                      <span className="badge badge-green">
                                        {group.primaryDonor.kpfaId || "N/A"}
                                      </span>
                                    </div>
                                  ) : (
                                    <span className="text-xs text-gray-500 italic">No primary donor</span>
                                  )}
                                </td>
                                <td className="px-6 py-4">
                                  <div className="text-sm">
                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                      {group.duplicateDonors.length} records
                                    </span>
                                  </div>
                                </td>
                                <td className="px-6 py-4">
                                  <div className="flex flex-wrap gap-2">
                                    <button
                                      onClick={() => handleAcceptSuggestion(group.id, false)}
                                      disabled={processingGroupId === group.id}
                                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
                                    >
                                      {processingGroupId === group.id ? 'Processing...' : 'Move to Pending'}
                                    </button>
                                    
                                    <button
                                      onClick={() => handleAcceptSuggestion(group.id, true)}
                                      disabled={processingGroupId === group.id}
                                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
                                    >
                                      {processingGroupId === group.id ? 'Processing...' : 'Mark Ready to Merge'}
                                    </button>
                                    
                                    <button
                                      onClick={() => handleRejectSuggestion(group.id)}
                                      disabled={processingGroupId === group.id}
                                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
                                    >
                                      {processingGroupId === group.id ? 'Processing...' : 'Reject'}
                                    </button>

                                    <Link
                                      href={`/duplicate-donors/${group.id}`}
                                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors cursor-pointer"
                                    >
                                      <svg
                                        className="w-4 h-4 mr-1"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                        ></path>
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                        ></path>
                                      </svg>
                                      View Details
                                    </Link>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                      {suggestedGroups.length > 0 && (
                        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 text-sm text-gray-700">
                          <span className="font-medium">{suggestedGroups.length}</span> of {suggestedTotalItems} suggested groups displayed
                        </div>
                      )}
                    </div>

                    {/* Pagination for suggested duplicates */}
                    {suggestedTotalItems > 0 && (
                      <div className="mt-6">
                        <Pagination
                          paginationInfo={suggestedPagination.paginationInfo(suggestedTotalItems)}
                          onPageChange={(page) => {
                            suggestedPagination.goToPage(page);
                          }}
                          onItemsPerPageChange={(itemsPerPage) => {
                            suggestedPagination.setItemsPerPage(itemsPerPage);
                          }}
                          showItemsPerPageSelector={true}
                          itemsPerPageOptions={[5, 10, 20, 50]}
                          className="bg-purple-50 p-4 rounded-lg border border-purple-200"
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {selectedGroups.length > 0 && isMergeModalOpen && (
          <MergeDonorsModal
            isOpen={isMergeModalOpen}
            onClose={handleMergeModalClose}
            primaryDonorId={
              selectedGroups[0].primaryDonor?.kpfaId || 0
            }
            primaryDonorDetails={null}
            duplicateDonors={
              selectedGroups[0].duplicateDonors
                .filter(
                  (d) =>
                    d.kpfaId !==
                    selectedGroups[0].primaryDonor?.kpfaId,
                )
                .map((d) => ({
                  id: d.id,
                  kpfaId: d.kpfaId,
                  details: null,
                }))
            }
            batchMode={true}
            allGroups={selectedGroups.map(group => {
              const primaryDonorId = group.primaryDonor?.kpfaId || 0;
              const duplicates = group.duplicateDonors.filter(d => d.kpfaId !== primaryDonorId);
              
              return {
                id: group.id,
                primaryDonorId,
                primaryDonorName: `Donor #${primaryDonorId}`,
                groupName: group.name,
                duplicateCount: duplicates.length,
                duplicateDonors: duplicates.map(d => ({ kpfaId: d.kpfaId }))
              };
            })}
          />
        )}

        {/* Delete Group Confirmation Modal */}
        {confirmDeleteModal.isOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4 text-red-600">
                Delete Group
              </h3>
              
              <div className="mb-4">
                <p className="text-gray-700 mb-3">
                  Delete "{confirmDeleteModal.groupName}"?
                </p>
                
                {confirmDeleteModal.hasMembers && (
                  <p className="text-sm text-gray-600">
                    This removes the duplicate tracking records in Keystone only. Station Admin donor data remains untouched.
                  </p>
                )}
              </div>
              
              <div className="flex gap-3 justify-end">
                <button
                  onClick={closeDeleteModal}
                  disabled={deletingGroupId === confirmDeleteModal.groupId}
                  className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 cursor-pointer disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDeleteGroup}
                  disabled={deletingGroupId === confirmDeleteModal.groupId}
                  className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed flex items-center"
                >
                  {deletingGroupId === confirmDeleteModal.groupId ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    `Delete Group${confirmDeleteModal.hasMembers ? ' & Members' : ''}`
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Success/Failure Modal */}
        <ConfirmationModal
          isOpen={modalState.isOpen}
          onClose={() => setModalState({ ...modalState, isOpen: false })}
          onConfirm={() => setModalState({ ...modalState, isOpen: false })}
          title={modalState.title}
          message={modalState.message}
          confirmText="OK"
          cancelText=""
          isDangerous={modalState.type === 'error'}
          type={modalState.type}
        />

        {/* Manual Pair Marking Modal */}
        {isPairModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Mark Donor Pair as NOT Duplicates</h3>
              <p className="text-gray-600 mb-4">
                Manually mark two donors as NOT duplicates to prevent future suggestions.
              </p>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    First Donor KPFA ID:
                  </label>
                  <input
                    type="number"
                    value={pairKpfaId1 || ''}
                    onChange={(e) => setPairKpfaId1(parseInt(e.target.value) || 0)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., 12345"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Second Donor KPFA ID:
                  </label>
                  <input
                    type="number"
                    value={pairKpfaId2 || ''}
                    onChange={(e) => setPairKpfaId2(parseInt(e.target.value) || 0)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., 67890"
                  />
                </div>
              </div>
              
              <div className="flex gap-3 justify-end mt-6">
                <button
                  onClick={() => {
                    setIsPairModalOpen(false);
                    setPairKpfaId1(0);
                    setPairKpfaId2(0);
                  }}
                  className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 cursor-pointer"
                >
                  Cancel
                </button>
                <button
                  onClick={handleMarkPairNotDuplicates}
                  disabled={!pairKpfaId1 || !pairKpfaId2}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
                >
                  Mark as NOT Duplicates
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AuthRequired>
  );
}

