import { gql } from '@apollo/client';

interface DuplicateDonor {
  id: string;
  kpfaId: number;
}

interface TestGroup {
  id: string;
  name: string;
  primaryDonor: {
    id: string;
    kpfaId: number;
  };
  duplicateDonors: DuplicateDonor[];
  status: string;
}

// Query to get a test group
const GET_TEST_GROUP = gql`
  query GetTestGroup {
    duplicateGroups(where: { status: { equals: "READY_TO_MERGE" } }, first: 1) {
      id
      name
      primaryDonor {
        id
        kpfaId
      }
      duplicateDonors {
        id
        kpfaId
      }
      status
    }
  }
`;

async function runTest() {
  try {
    // First, get a test group
    console.log('Fetching test group...');
    const response = await fetch('/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: GET_TEST_GROUP.loc?.source.body
      })
    });

    const result = await response.json();
    const testGroup = result.data?.duplicateGroups[0] as TestGroup;

    if (!testGroup) {
      throw new Error('No test group found. Please ensure there is at least one group marked as READY_TO_MERGE.');
    }

    console.log('Found test group:', testGroup);

    // Now run the merge test
    console.log('\nRunning merge test...');
    const mergeResponse = await fetch('/api/merge-donors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        primaryDonorId: testGroup.primaryDonor.kpfaId,
        secondaryDonorIds: testGroup.duplicateDonors.map((d: DuplicateDonor) => d.kpfaId)
      })
    });

    const mergeResult = await mergeResponse.json();
    console.log('Merge result:', mergeResult);

    if (!mergeResponse.ok) {
      throw new Error(mergeResult.error || mergeResult.message || 'Failed to merge donors');
    }

    // Update the group status
    console.log('\nUpdating group status...');
    const updateResponse = await fetch('/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: `
          mutation UpdateDuplicateGroupStatus($id: ID!, $status: String!) {
            updateDuplicateGroup(where: { id: $id }, data: { status: $status }) {
              id
              status
            }
          }
        `,
        variables: {
          id: testGroup.id,
          status: 'MERGED'
        }
      })
    });

    const updateResult = await updateResponse.json();
    console.log('Status update result:', updateResult);

    console.log('\nTest completed successfully!');
    console.log('Group:', testGroup.name);
    console.log('Primary donor:', testGroup.primaryDonor.kpfaId);
    console.log('Duplicates merged:', testGroup.duplicateDonors.length);
    console.log('Merge result:', mergeResult);
    console.log('Final status:', updateResult.data?.updateDuplicateGroup?.status);

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
runTest(); 