'use client';

import { useState } from 'react';
import { DuplicateSearchPanel } from '@/components/DuplicateSearchPanel';
import { useAuth } from '@/lib/authContext';

interface SelectedDonor {
  id: number;
  display_name: string;
  phone: string;
  email: string;
  formatted_phone: string;
}

export default function DuplicateSearchPage() {
  const { isAuthenticated } = useAuth();
  const [selectedDonor, setSelectedDonor] = useState<SelectedDonor | null>(null);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold mb-4">Authentication Required</h1>
          <p className="text-gray-600">Please log in to access the duplicate search feature.</p>
        </div>
      </div>
    );
  }

  const handleDonorSelect = (donor: any) => {
    setSelectedDonor({
      id: donor.id,
      display_name: donor.display_name,
      phone: donor.phone,
      email: donor.email,
      formatted_phone: donor.formatted_phone
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Exact Phone Number Duplicate Search</h1>
          <p className="mt-2 text-gray-600">
            Search for donors with exactly matching phone numbers (ignoring formatting). This helps identify existing donors before creating new records.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Search Panel */}
          <div className="lg:col-span-2">
            <DuplicateSearchPanel 
              onDonorSelect={handleDonorSelect}
              className="h-fit"
            />
          </div>

          {/* Selected Donor Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h3 className="text-lg font-semibold mb-4">Selected Donor</h3>
              
              {selectedDonor ? (
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Name</label>
                    <p className="text-lg font-semibold">{selectedDonor.display_name}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Donor ID</label>
                    <p className="text-gray-900">{selectedDonor.id}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                    <p className="text-gray-900">{selectedDonor.formatted_phone || selectedDonor.phone || 'N/A'}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <p className="text-gray-900">{selectedDonor.email || 'N/A'}</p>
                  </div>

                  <div className="pt-4 border-t">
                    <h4 className="font-medium text-gray-900 mb-2">Actions</h4>
                    <div className="space-y-2">
                      <button
                        onClick={() => window.open(`/donors/${selectedDonor.id}`, '_blank')}
                        className="w-full px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        View Donor Details
                      </button>
                      
                      <button
                        onClick={() => alert(`This would initiate a merge process with donor ${selectedDonor.id}`)}
                        className="w-full px-3 py-2 text-sm bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
                      >
                        Initiate Merge Process
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>No donor selected</p>
                  <p className="text-sm mt-2">Click on a search result to view details</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="mt-12 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">How to Use</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-2">🔍 Search Process</h4>
              <ol className="list-decimal list-inside space-y-1">
                <li>Enter a phone number in any format</li>
                <li>Click "Search" or press Enter</li>
                <li>Review potential matches</li>
                <li>Click on a result to select it</li>
              </ol>
            </div>
            <div>
              <h4 className="font-medium mb-2">📞 Phone Number Formats</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>(*************</li>
                <li>************</li>
                <li>5551234567</li>
                <li>****** 123 4567</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-yellow-100 rounded border border-yellow-300">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> This feature searches the legacy database directly for immediate results. 
              For bulk duplicate detection or more advanced matching criteria, use the existing duplicate management tools.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 