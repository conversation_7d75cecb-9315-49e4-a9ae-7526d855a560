@import "tailwindcss";

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #111827;
  --card-bg: #ffffff;
  --nav-bg: #ffffff;
  --table-bg: #ffffff;
  --table-header-bg: #f9fafb;
  --hover-bg: #f3f4f6;
  --border-color: #e5e7eb;
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-muted: #4b5563;

  /* Badge/pill colors with better contrast */
  --badge-green-bg: #dcfce7;
  --badge-green-text: #166534;
  --badge-yellow-bg: #fef9c3;
  --badge-yellow-text: #854d0e;
  --badge-blue-bg: #dbeafe;
  --badge-blue-text: #1e40af;

  /* shadcn/ui CSS variables */
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 262.1 83.3% 57.8%;
  --radius: 0.5rem;
  --primary: 262.1 83.3% 57.8%;
  --primary-foreground: 210 20% 98%;
  --secondary: 220 14.3% 95.9%;
  --secondary-foreground: 220.9 39.3% 11%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 20% 98%;
  --muted: 220 14.3% 95.9%;
  --muted-foreground: 220 8.9% 46.1%;
  --accent: 220 14.3% 95.9%;
  --accent-foreground: 220.9 39.3% 11%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans, Arial, Helvetica, sans-serif);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Add custom styles for transitions */
.transition-theme {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Component-specific utility classes */
.card-bg {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.nav-bg {
  background-color: var(--nav-bg);
  border-color: var(--border-color);
}

.table-bg {
  background-color: var(--table-bg);
}

.table-header-bg {
  background-color: var(--table-header-bg);
}

.hover-bg:hover {
  background-color: var(--hover-bg);
}

/* Text utility classes */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

/* Badge/pill utility classes */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-green {
  background-color: var(--badge-green-bg);
  color: var(--badge-green-text);
}

.badge-yellow {
  background-color: var(--badge-yellow-bg);
  color: var(--badge-yellow-text);
}

.badge-blue {
  background-color: var(--badge-blue-bg);
  color: var(--badge-blue-text);
}

/* Select and status styles */
.select-status {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
  transition: background-color 0.3s, color 0.3s;
  border-width: 1px;
}

.status-label {
  color: var(--text-primary);
  font-weight: 500;
  padding: 0.15rem 0.75rem;
  border-radius: 0.5rem;
  background: var(--table-header-bg);
  font-size: 0.85rem;
}
