'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/authContext';
import { Donor } from '@/types';
import { DonorSearch } from '@/components/DonorSearch';
import { DonorCard } from '@/components/DonorCard';
import { ConfirmationDialog } from '@/components/ConfirmationDialog';
import { MergeSummary } from '@/components/MergeSummary';

export default function MergeDonorsPage() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [primaryDonor, setPrimaryDonor] = useState<Donor | null>(null);
  const [secondaryDonor, setSecondaryDonor] = useState<Donor | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const handleMerge = async () => {
    if (!primaryDonor || !secondaryDonor) {
      setError('Please select both primary and secondary donors');
      return;
    }

    // Open the confirmation dialog
    setIsConfirmDialogOpen(true);
  };

  const performMerge = async () => {
    if (!primaryDonor || !secondaryDonor) return;

    setIsProcessing(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch('/api/merge-donors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          primaryDonorId: primaryDonor.id,
          secondaryDonorId: secondaryDonor.id,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to merge donors');
      }

      // Show success message
      setSuccessMessage(
        `Donors merged successfully! ${result.details?.donationsMoved || 0} donations were moved.`
      );
      
      // Clear the secondary donor since it's now been merged
      setSecondaryDonor(null);
      
      // Close confirmation dialog
      setIsConfirmDialogOpen(false);
      
      // Redirect to duplicate donors page after a delay
      setTimeout(() => {
        router.push('/duplicate-donors?success=true');
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during the merge');
      setIsConfirmDialogOpen(false);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Merge Donor Accounts</h1>
      
      {successMessage && (
        <div className="mb-6 p-4 bg-green-100 text-green-800 rounded">
          {successMessage}
        </div>
      )}
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-lg font-semibold mb-4">Primary Donor (Keep)</h2>
            {primaryDonor ? (
              <DonorCard 
                donor={primaryDonor} 
                title="Primary Donor" 
                onClear={() => setPrimaryDonor(null)}
              />
            ) : (
              <DonorSearch 
                onSelectDonor={setPrimaryDonor}
                label="Search for Primary Donor"
                placeholder="Enter ID or name of donor to keep"
              />
            )}
          </div>

          <div>
            <h2 className="text-lg font-semibold mb-4">Secondary Donor (Merge)</h2>
            {secondaryDonor ? (
              <DonorCard 
                donor={secondaryDonor} 
                title="Secondary Donor" 
                onClear={() => setSecondaryDonor(null)}
              />
            ) : (
              <DonorSearch 
                onSelectDonor={setSecondaryDonor}
                label="Search for Secondary Donor"
                placeholder="Enter ID or name of donor to merge"
              />
            )}
          </div>
        </div>

        {primaryDonor && secondaryDonor && (
          <div className="mt-8">
            <MergeSummary primaryDonor={primaryDonor} secondaryDonor={secondaryDonor} />
          </div>
        )}

        {error && (
          <div className="mt-4 p-4 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded">
            {error}
          </div>
        )}

        <div className="mt-6">
          <button
            onClick={handleMerge}
            disabled={!primaryDonor || !secondaryDonor || isProcessing}
            className="px-6 py-2 bg-primary text-white rounded disabled:opacity-50"
          >
            {isProcessing ? 'Processing...' : 'Merge Donors'}
          </button>
        </div>
      </div>
      
      <ConfirmationDialog
        isOpen={isConfirmDialogOpen}
        onClose={() => setIsConfirmDialogOpen(false)}
        onConfirm={performMerge}
        title="Confirm Donor Merge"
        message={
          <div>
            <p className="mb-4">
              You are about to merge donor <strong>{secondaryDonor?.firstname} {secondaryDonor?.lastname}</strong> (ID: {secondaryDonor?.id}) 
              into <strong>{primaryDonor?.firstname} {primaryDonor?.lastname}</strong> (ID: {primaryDonor?.id}).
            </p>
            <p className="mb-4">
              This will move all donations and related records from the secondary donor to the primary donor, 
              and delete the secondary donor account. This action cannot be undone.
            </p>
          </div>
        }
        verificationText="Type the primary donor's full name to confirm:"
        requiredVerificationText={primaryDonor ? `${primaryDonor.firstname} ${primaryDonor.lastname}` : undefined}
        confirmText="Merge Donors"
        cancelText="Cancel"
      />
    </div>
  );
} 