"use client";

import React from "react";
import { useQuery, gql } from "@apollo/client";
import Link from "next/link";
import { useState } from "react";
import AuthRequired from "@/components/AuthRequired";
import SearchInput from "@/components/ui/SearchInput";
import Pagination from "@/components/ui/Pagination";
import { usePagination } from "@/lib/hooks/usePagination";
import { useSearch } from "@/lib/hooks/useSearch";

// TypeScript interfaces
interface DuplicateDonor {
  id: string;
  kpfaId: number;
}

interface MergedGroup {
  id: string;
  name: string;
  status: string;
  mergedAt: string;
  mergedBy?: string;
  mergedDonorsData?: string;
  primaryDonor?: {
    kpfaId: number;
    firstname?: string;
    lastname?: string;
  };
  duplicateDonors: DuplicateDonor[];
}

// GraphQL query to fetch merged duplicate groups with pagination
const MERGED_GROUPS_QUERY = gql`
  query MergedDuplicateGroups($where: DuplicateGroupWhereInput!, $take: Int!, $skip: Int!, $orderBy: [DuplicateGroupOrderByInput!]!) {
    duplicateGroups(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
      id
      name
      status
      mergedAt
      mergedBy
      mergedDonorsData
      primaryDonor {
        kpfaId
        firstname
        lastname
      }
      duplicateDonors {
        id
        kpfaId
      }
    }
    duplicateGroupsCount(where: $where)
  }
`;

export default function MergedGroupsClient() {
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Search setup with URL synchronization
  const search = useSearch({
    urlSync: false,
    urlParamName: 'search',
    debounceMs: 300,
  });

  // Pagination setup with URL synchronization
  const pagination = usePagination({
    initialItemsPerPage: 20,
    maxItemsPerPage: 100,
    urlSync: false,
    scrollToTopOnPageChange: true,
  });

  // Build the where clause for filtering
  const whereClause = React.useMemo(() => {
    const where: any = {
      status: { equals: "MERGED" }
    };

    if (search.debouncedQuery.trim()) {
      const searchTerm = search.debouncedQuery.toLowerCase();
      const numericSearch = parseInt(searchTerm);
      
      // Start with text-based searches
      const orConditions: any[] = [
        { name: { contains: searchTerm, mode: "insensitive" } },
        { mergedBy: { contains: searchTerm, mode: "insensitive" } }
      ];

      // Only search by kpfaId for reasonable donor ID numbers (under 1 million)
      // This excludes phone numbers and other large numbers from being treated as donor IDs
      if (!isNaN(numericSearch) && numericSearch > 0 && numericSearch < 1000000) {
        orConditions.push(
          { primaryDonor: { kpfaId: { equals: numericSearch } } },
          { duplicateDonors: { some: { kpfaId: { equals: numericSearch } } } }
        );
      }

      // Add name search for primary donor if it looks like a name (non-numeric)
      if (isNaN(Number(searchTerm))) {
        orConditions.push(
          { primaryDonor: { firstname: { contains: searchTerm, mode: "insensitive" } } },
          { primaryDonor: { lastname: { contains: searchTerm, mode: "insensitive" } } }
        );
      }

      where.OR = orConditions;
    }

    return where;
  }, [search.debouncedQuery]);

  // Build order by clause
  const orderByClause = React.useMemo(() => {
    return [{ mergedAt: sortOrder }];
  }, [sortOrder]);

  // Memoize query variables to prevent unnecessary re-renders
  const queryVariables = React.useMemo(() => {
    const vars = {
      where: whereClause,
      take: pagination.paginationState.take,
      skip: pagination.paginationState.skip,
      orderBy: orderByClause,
    };
    return vars;
  }, [whereClause, pagination.paginationState.take, pagination.paginationState.skip, orderByClause]);

  // Main query with pagination
  const { loading, error, data, refetch } = useQuery(MERGED_GROUPS_QUERY, {
    variables: queryVariables,
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
    onError: (error) => {
      console.error('❌ Merged groups query error:', error);
    },
  });

  const mergedGroups: MergedGroup[] = data?.duplicateGroups || [];
  const totalItems = data?.duplicateGroupsCount || 0;

  // Track if we've ever received data to prevent skeleton flash
  const [hasEverReceivedData, setHasEverReceivedData] = React.useState(false);
  
  React.useEffect(() => {
    if (data) {
      setHasEverReceivedData(true);
    }
  }, [data]);

  // Handle search changes
  const handleSearchChange = React.useCallback((value: string) => {
    // Update search query and reset pagination when search changes
    search.setSearchQuery(value);
    
    // Reset pagination when search changes (immediately for responsive feel)
    if (value !== search.searchQuery) {
      pagination.resetPagination();
    }
  }, [search, pagination]);

  // Handle clearing search
  const handleClearSearch = React.useCallback(() => {
    search.setSearchQuery('');
    // Reset pagination immediately for clear action
    pagination.resetPagination();
  }, [search, pagination]);

  // Handle sort order changes
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    pagination.resetPagination(); // Reset to first page when sorting
  };

  // Only show skeleton on the very first load, never during updates
  const isInitialLoading = loading && !hasEverReceivedData;
  const isUpdating = false; // Remove the updating indicator to prevent flash

  // Format time using built-in methods instead of date-fns
  const formatMergeTime = (dateString: string) => {
    if (!dateString) return "Unknown";
    
    const date = new Date(dateString);
    const now = new Date();
    
    // Get difference in milliseconds
    const diffMs = now.getTime() - date.getTime();
    
    // Convert to minutes, hours, days
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    // Format relative time
    let timeAgo;
    if (diffMins < 1) {
      timeAgo = 'just now';
    } else if (diffMins < 60) {
      timeAgo = `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
      timeAgo = `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
      timeAgo = `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      // If older than a week, just show the date
      timeAgo = new Intl.DateTimeFormat('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: 'numeric'
      }).format(date);
    }
    
    // Format full date for tooltip
    const fullDate = new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date);
    
    return (
      <span title={fullDate}>
        {timeAgo}
      </span>
    );
  };

  if (isInitialLoading)
    return (
      <div className="flex items-center justify-center min-h-[70vh]">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 border-4 border-t-blue-500 border-blue-200 rounded-full animate-spin mb-4"></div>
          <div className="text-secondary">Loading merged groups...</div>
        </div>
      </div>
    );

  if (error)
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] max-w-lg mx-auto px-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 w-full text-center">
          <svg
            className="w-12 h-12 mx-auto text-red-500 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <h2 className="text-lg font-semibold text-red-700 mb-2">
            Error loading merged groups
          </h2>
          <p className="text-sm text-red-600 mb-4">{error.message}</p>
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Return to Home
          </Link>
        </div>
      </div>
    );

  return (
    <AuthRequired>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Subtle loading indicator for updates */}
        {isUpdating && (
          <div className="fixed top-0 left-0 right-0 z-50">
            <div className="h-1 bg-blue-200">
              <div className="h-1 bg-blue-500 animate-pulse w-full"></div>
            </div>
          </div>
        )}
        
        <header className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold mb-2 text-primary transition-theme">
                Merged Donor Groups
              </h1>
              <p className="text-secondary transition-theme">
                View history of previously merged duplicate donor groups
              </p>
            </div>
          </div>
        </header>

        {/* Persistent Header with Search - Always rendered */}
        <div className="card-bg rounded-t-lg shadow-sm border-b border-gray-200 transition-theme mb-0">
          <div className="px-6 py-4 bg-gray-50">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
              <div>
                <h2 className="text-lg font-semibold text-gray-800">
                  Merge History
                </h2>
                <p className="text-sm text-gray-700">
                  Search and browse previously merged duplicate donor groups
                </p>
              </div>
              <div className="w-full sm:w-1/3">
                <SearchInput 
                  value={search.searchQuery} 
                  onChange={handleSearchChange} 
                  placeholder="Search by name, donor ID, or merged by..." 
                />
              </div>
            </div>
          </div>
        </div>

        {mergedGroups.length === 0 && !loading ? (
          <div className="card-bg rounded-b-lg shadow-sm border border-t-0 overflow-hidden transition-theme">
            <div className="p-8 text-center">
              <svg
                className="w-12 h-12 mx-auto text-gray-400 mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                ></path>
              </svg>
              {search.debouncedQuery.trim() ? (
                <div>
                  <h2 className="text-xl font-medium text-gray-900 mb-2">
                    No Merged Groups Found
                  </h2>
                  <p className="text-gray-500 mb-4">
                    No merged groups match your search criteria.
                  </p>
                  <button 
                    onClick={handleClearSearch}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Clear search
                  </button>
                </div>
              ) : (
                <div>
                  <h2 className="text-xl font-medium text-gray-900 mb-2">
                    No Merged Groups Found
                  </h2>
                  <p className="text-gray-500 mb-4">
                    There are no merged duplicate groups to display.
                  </p>
                  <Link
                    href="/duplicate-donors"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Go to Duplicate Donors
                  </Link>
                </div>
              )}
            </div>
          </div>
        ) : mergedGroups.length === 0 && loading ? (
          <div className="card-bg rounded-b-lg shadow-sm border border-t-0 overflow-hidden transition-theme">
            <div className="p-8 text-center">
              <div className="inline-flex items-center space-x-2 text-gray-500">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                <span>Searching...</span>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto card-bg rounded-b-lg shadow-sm border border-t-0 mb-4 transition-theme relative">
              {/* Subtle loading overlay for search/pagination updates */}
              {loading && data && (
                <div className="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center z-10">
                  <div className="bg-white rounded-lg shadow-lg p-3 flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    <span className="text-sm text-gray-700">Updating...</span>
                  </div>
                </div>
              )}
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Group Name
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Primary Donor
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Records Merged
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={toggleSortOrder}
                    >
                      <div className="flex items-center">
                        Merged {sortOrder === "desc" ? "↓" : "↑"}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Merged By
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {mergedGroups.map((group) => (
                    <tr key={group.id} className="hover-bg transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {group.name || "Unnamed Group"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {group.primaryDonor ? (
                            <>
                              <span className="font-medium">
                                #{group.primaryDonor.kpfaId}
                              </span>
                              {group.primaryDonor.firstname &&
                                group.primaryDonor.lastname && (
                                  <span className="text-gray-500 ml-2">
                                    {group.primaryDonor.firstname}{" "}
                                    {group.primaryDonor.lastname}
                                  </span>
                                )}
                            </>
                          ) : (
                            <span className="text-gray-500 italic">
                              No primary donor
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {group.duplicateDonors.length} records
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {formatMergeTime(group.mergedAt)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {group.mergedBy || "System"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Link
                          href={`/duplicate-donors/${group.id}`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          View Details
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {mergedGroups.length > 0 && (
              <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 text-sm text-gray-700">
                <span className="font-medium">{mergedGroups.length}</span> of {totalItems} groups displayed
              </div>
            )}
          </>
        )}

        {/* Pagination - outside conditional blocks so it's always available when there are total items */}
        {totalItems > 0 && (
          <div className="mt-6">
            <Pagination
              paginationInfo={pagination.paginationInfo(totalItems)}
              onPageChange={(page) => {
                pagination.goToPage(page);
              }}
              onItemsPerPageChange={(itemsPerPage) => {
                pagination.setItemsPerPage(itemsPerPage);
              }}
              showItemsPerPageSelector={true}
              itemsPerPageOptions={[10, 20, 50, 100]}
              className="bg-white p-4 rounded-lg border-gray-200 border"
            />
          </div>
        )}
      </div>
    </AuthRequired>
  );
} 