import { Suspense } from 'react';
import MergedGroupsClient from './page-client';

export default function MergedGroupsPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-[70vh]">
      <div className="animate-pulse flex flex-col items-center">
        <div className="h-12 w-12 border-4 border-t-blue-500 border-blue-200 rounded-full animate-spin mb-4"></div>
        <div className="text-gray-600">Loading...</div>
      </div>
    </div>}>
      <MergedGroupsClient />
    </Suspense>
  );
} 