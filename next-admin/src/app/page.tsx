'use client';

import Link from "next/link";
import AuthRequired from '@/components/AuthRequired';

export default function Home() {
  return (
    <AuthRequired>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-primary mb-6 transition-theme">KPFA Admin Dashboard</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="card-bg rounded-lg shadow-sm border p-6 hover:shadow-md transition-all transition-theme">
            <h2 className="text-xl font-semibold text-primary mb-2">Duplicate Donors</h2>
            <p className="text-secondary mb-4">Review and merge duplicate donor records manually.</p>
            <Link 
              href="/duplicate-donors" 
              className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              <span>Manage Duplicates</span>
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </Link>
          </div>
          
          <div className="card-bg rounded-lg shadow-sm border p-6 hover:shadow-md transition-all transition-theme">
            <h2 className="text-xl font-semibold text-primary mb-2">Merged Groups</h2>
            <p className="text-secondary mb-4">View history of merged donor groups.</p>
            <Link 
              href="/merged-groups" 
              className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              <span>View History</span>
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
              </svg>
            </Link>
          </div>
          
          {/* Additional cards for future admin features */}
        </div>
      </div>
    </AuthRequired>
  );
}
