'use client';

import { useState } from 'react';
import AuthRequired from '@/components/AuthRequired';
import { StripeCustomerSearch } from '@/components/StripeCustomerSearch';
import { StripeCustomerList } from '@/components/StripeCustomerList';
import { StripeCustomerDetail } from '@/components/StripeCustomerDetail';

import { DonorReconciliation } from '@/components/DonorReconciliation';
import { PaymentReconciliation } from '@/components/PaymentReconciliation';
import { StripeCustomer } from '@/types';

export default function StripeDonorsClient() {
  const [customers, setCustomers] = useState<StripeCustomer[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'reconciliation' | 'payment-reconciliation' | 'stripe-search'>('reconciliation');

  const handleCustomersFound = (foundCustomers: StripeCustomer[]) => {
    setCustomers(foundCustomers);
    setError(null);
    setHasSearched(true);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setCustomers([]);
    setHasSearched(true);
  };

  const handleViewDetails = (customer: StripeCustomer) => {
    setSelectedCustomerId(customer.id);
  };

  const handleCloseDetails = () => {
    setSelectedCustomerId(null);
  };

  return (
    <AuthRequired>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Stripe Reconciliation Dashboard
          </h1>
          <p className="text-gray-600">
            Reconcile discrepancies between Station Admin and Stripe data - compare donor accounts and detect unmatched payments
          </p>
        </div>



        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('reconciliation')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'reconciliation'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Donor Reconciliation
              </button>
              <button
                onClick={() => setActiveTab('payment-reconciliation')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'payment-reconciliation'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Payment Reconciliation
              </button>
              <button
                onClick={() => setActiveTab('stripe-search')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'stripe-search'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Stripe Customer Search
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'reconciliation' && <DonorReconciliation />}

        {activeTab === 'payment-reconciliation' && <PaymentReconciliation />}

        {activeTab === 'stripe-search' && (
          <>
            {/* Search Section */}
            <div className="mb-6">
              <StripeCustomerSearch
                onCustomersFound={handleCustomersFound}
                onError={handleError}
              />
            </div>
          </>
        )}

        {activeTab === 'stripe-search' && (
          <>
            {/* Error Display */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            )}

            {/* Results Section */}
            {hasSearched && (
              <StripeCustomerList
                customers={customers}
                onViewDetails={handleViewDetails}
              />
            )}

            {/* Initial State */}
            {!hasSearched && !error && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-center py-12 text-gray-500">
                  <p>Search for Stripe customers to begin reconciliation</p>
                  <p className="text-sm mt-2">
                    Find customers by email address or Stripe customer ID to compare with Station Admin data
                  </p>
                </div>
              </div>
            )}
          </>
        )}

        {/* Customer Detail Modal */}
        {selectedCustomerId && (
          <StripeCustomerDetail
            customerId={selectedCustomerId}
            onClose={handleCloseDetails}
          />
        )}
      </div>
    </AuthRequired>
  );
}
