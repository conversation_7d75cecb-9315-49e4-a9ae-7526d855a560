'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/authContext';
import { Spinner } from '@/components/ui/spinner';

interface AuthRequiredProps {
  children: React.ReactNode;
}

// Helper to determine if in development environment
const isDevelopment = () => 
  typeof window !== 'undefined' && 
  (window.location.hostname === 'localhost' || 
   window.location.hostname === '127.0.0.1' ||
   window.location.hostname === 'pet-leopard-fully.ngrok-free.app');

// Helper to get the appropriate StationAdmin URL based on environment
const getStationAdminUrl = () => {
  // If we're using ngrok for testing
  if (typeof window !== 'undefined' && window.location.hostname === 'pet-leopard-fully.ngrok-free.app') {
    return 'https://pet-leopard-fully.ngrok-free.app';
  }
  // Otherwise use the production domain
  return 'https://admin.kpfa.org';
};

export default function AuthRequired({
  children
}: AuthRequiredProps) {
  const { user, isLoading, isAuthenticated, checkAuth } = useAuth();
  const router = useRouter();
  const [initialCheck, setInitialCheck] = useState(false);
  const [hasUrlToken, setHasUrlToken] = useState(false);
  
  useEffect(() => {
    // Check for URL token first, then run auth check
    if (!initialCheck) {
      const runAuthCheck = async () => {
        // Check if there's a token in URL parameters
        if (typeof window !== 'undefined') {
          const urlParams = new URLSearchParams(window.location.search);
          const urlToken = urlParams.get('token');
          setHasUrlToken(!!urlToken);

          if (urlToken) {
            // Give extra time for auth context to process URL token
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        await checkAuth();
        setInitialCheck(true);
      };

      runAuthCheck();
    }
  }, [checkAuth, initialCheck]);

  useEffect(() => {
    // Only proceed with redirects if we've done the initial check and no URL token processing
    if (initialCheck && !isLoading) {
      // If not authenticated, redirect to login page
      // BUT: Don't redirect immediately if we had a URL token - give more time for processing
      if (!isAuthenticated) {
        if (hasUrlToken) {
          // Give additional time for URL token processing
          setTimeout(() => {
            if (!isAuthenticated) {
              window.location.href = getStationAdminUrl();
            }
          }, 2000);
        } else {
          window.location.href = getStationAdminUrl();
        }
        return;
      }

      // If authenticated but doesn't have Admin access level
      if (isAuthenticated && user && user.access_level !== 'Admin') {
        console.error('Access denied: Admin permission required');
        // Redirect to StationAdmin with unauthorized message
        window.location.href = getStationAdminUrl() + '?error=unauthorized';
      }
    }
  }, [isLoading, isAuthenticated, initialCheck, user, hasUrlToken]);

  // Show loading state - use a subtle overlay instead of full screen replacement
  if (isLoading || !initialCheck) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="fixed inset-0 bg-white bg-opacity-75 flex justify-center items-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 flex items-center space-x-3">
            <Spinner size="sm" />
            <span className="text-gray-700">Verifying authentication...</span>
          </div>
        </div>
        {/* Show a light skeleton of the main layout */}
        <div className="container mx-auto px-4 py-8 max-w-6xl opacity-30">
          <div className="h-8 bg-gray-200 rounded w-64 mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-96 mb-8 animate-pulse"></div>
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="h-4 bg-gray-200 rounded w-full mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  // If authenticated and has Admin access, show children
  if (isAuthenticated && user && user.access_level === 'Admin') {
    return <>{children}</>;
  }

  // This should not be visible, but as a fallback:
  return (
    <div className="flex justify-center items-center h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Administrator Access Required</h1>
        <p className="mt-2">You must be logged in as an Administrator to access this application.</p>
        <button 
          className="mt-4 px-4 py-2 bg-primary text-white rounded"
          onClick={() => window.location.href = getStationAdminUrl() + '/login'}
        >
          Log In
        </button>
      </div>
    </div>
  );
} 