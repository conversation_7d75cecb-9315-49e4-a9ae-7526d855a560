'use client';

import React, { useState } from 'react';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: React.ReactNode;
  confirmText?: string;
  cancelText?: string;
  verificationText?: string;
  requiredVerificationText?: string;
}

export function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  verificationText,
  requiredVerificationText
}: ConfirmationDialogProps) {
  const [inputValue, setInputValue] = useState('');
  const isVerificationValid = !requiredVerificationText || 
    inputValue.toLowerCase() === requiredVerificationText.toLowerCase();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md">
        <div className="mb-4">
          <h3 className="text-lg font-bold">{title}</h3>
        </div>
        
        <div className="mb-6">
          {typeof message === 'string' ? <p>{message}</p> : message}
        </div>
        
        {verificationText && (
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">
              {verificationText}
            </label>
            <input
              type="text"
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              className="w-full p-2 border rounded"
              placeholder="Type to confirm"
            />
          </div>
        )}
        
        <div className="flex justify-end space-x-2">
          <button 
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded"
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            disabled={!isVerificationValid}
            className="px-4 py-2 bg-red-600 text-white rounded disabled:opacity-50"
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
} 