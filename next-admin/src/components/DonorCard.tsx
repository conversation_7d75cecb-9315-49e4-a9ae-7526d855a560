'use client';

import { Donor } from '@/types';

interface DonorCardProps {
  donor: Don<PERSON>;
  title: string;
  onClear?: () => void;
  isEditable?: boolean;
}

export function DonorCard({ donor, title, onClear, isEditable = true }: DonorCardProps) {
  if (!donor) return null;

  return (
    <div className="border rounded p-4 bg-white">
      <div className="flex justify-between items-start mb-2">
        <h3 className="text-lg font-semibold">{title}</h3>
        {isEditable && onClear && (
          <button
            onClick={onClear}
            className="text-sm text-red-600 hover:text-red-800"
          >
            Clear
          </button>
        )}
      </div>
      
      <div className="space-y-2">
        <p className="font-medium text-lg">
          {donor.firstname} {donor.lastname}
        </p>
        <p className="text-sm text-gray-600">
          ID: {donor.id}
        </p>
        
        {donor.email && (
          <p className="text-sm">
            <span className="text-gray-500">Email: </span>
            {donor.email}
          </p>
        )}
        
        {donor.phone && (
          <p className="text-sm">
            <span className="text-gray-500">Phone: </span>
            {donor.phone}
          </p>
        )}
        
        {(donor.address1 || donor.city || donor.state) && (
          <div className="text-sm">
            <p className="text-gray-500 mb-1">Address:</p>
            {donor.address1 && <p>{donor.address1}</p>}
            {donor.address2 && <p>{donor.address2}</p>}
            <p>
              {[
                donor.city, 
                donor.state, 
                donor.postal_code
              ].filter(Boolean).join(', ')}
            </p>
            {donor.country && donor.country !== 'USA' && <p>{donor.country}</p>}
          </div>
        )}
      </div>
    </div>
  );
} 