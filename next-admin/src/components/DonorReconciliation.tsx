'use client';

import { useState, useCallback } from 'react';
import { useLazyQuery, gql } from '@apollo/client';
import { searchDonors } from '@/lib/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tooltip } from '@/components/ui/tooltip';
import { ChevronDown, ChevronRight, Download, Copy, CheckCircle, AlertTriangle, XCircle, Clock, ExternalLink } from 'lucide-react';
import { StripeCustomer } from '@/types';

// GraphQL query to find Stripe customer by email
const FIND_STRIPE_CUSTOMER_BY_EMAIL = gql`
  query FindStripeCustomerByEmail($email: String!) {
    stripeCustomerByEmail(email: $email) {
      id
      email
      name
      phone
      created
      currency
      balance
      description
      metadata
      address {
        line1
        line2
        city
        state
        postal_code
        country
      }
    }
  }
`;

interface StationAdminDonor {
  id: number;
  firstname?: string;
  lastname?: string;
  email?: string;
  phone?: string;
  stripe_cus_id?: string;
  [key: string]: any;
}

interface ReconciliationResult {
  saDonor: StationAdminDonor;
  stripeCustomer: StripeCustomer | null;
  discrepancies: string[];
  hasStripeAccount: boolean;
  multipleStripeAccounts?: StripeCustomer[];
}

export function DonorReconciliation() {
  const [searchQuery, setSearchQuery] = useState('');
  const [reconciliationResults, setReconciliationResults] = useState<ReconciliationResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isHowToOpen, setIsHowToOpen] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<'all' | 'perfect' | 'discrepancies' | 'no-stripe'>('all');

  const [findStripeCustomer] = useLazyQuery(FIND_STRIPE_CUSTOMER_BY_EMAIL);

  // Copy to clipboard utility
  const copyToClipboard = useCallback(async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  }, []);

  // Export results to CSV
  const exportResults = useCallback(() => {
    const resultsToExport = getFilteredResults();
    if (resultsToExport.length === 0) return;

    const headers = ['Donor Name', 'Email', 'Status', 'Discrepancies Count', 'Stripe Customer ID', 'Discrepancies'];
    const rows = resultsToExport.map(result => [
      `${result.saDonor.firstname || ''} ${result.saDonor.lastname || ''}`.trim(),
      result.saDonor.email || '',
      result.discrepancies.length === 0 ? 'Data Matches' : 'Has Discrepancies',
      result.discrepancies.length.toString(),
      result.stripeCustomer?.id || 'No Stripe Account',
      result.discrepancies.join('; ')
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `donor-reconciliation-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [reconciliationResults, statusFilter]);

  // Add to recent searches
  const addToRecentSearches = useCallback((query: string) => {
    setRecentSearches(prev => {
      const filtered = prev.filter(q => q !== query);
      return [query, ...filtered].slice(0, 5); // Keep last 5 searches
    });
  }, []);

  // Get status icon and styling
  const getStatusInfo = (result: ReconciliationResult) => {
    if (!result.hasStripeAccount) {
      return {
        icon: <XCircle className="h-4 w-4" />,
        color: 'text-gray-500',
        bgColor: 'bg-gray-100',
        status: 'No Stripe Account'
      };
    }

    if (result.discrepancies.length === 0) {
      return {
        icon: <CheckCircle className="h-4 w-4" />,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        status: 'Data Matches'
      };
    }

    return {
      icon: <AlertTriangle className="h-4 w-4" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      status: `${result.discrepancies.length} Discrepancies`
    };
  };

  const findDiscrepancies = (saDonor: StationAdminDonor, stripeCustomer: StripeCustomer): string[] => {
    const discrepancies: string[] = [];

    // Check donor_id metadata linkage (most important for reconciliation)
    const stripeDonorId = stripeCustomer.metadata?.donor_id;
    if (stripeDonorId && stripeDonorId !== saDonor.id.toString()) {
      discrepancies.push(`🔗 Donor ID metadata mismatch: Stripe has donor_id="${stripeDonorId}" but SA ID is "${saDonor.id}"`);
    } else if (!stripeDonorId) {
      discrepancies.push(`🔗 Missing donor_id in Stripe metadata - this Stripe customer is not properly linked to SA donor ${saDonor.id}`);
    }

    // Compare email (critical for matching)
    if (saDonor.email && stripeCustomer.email) {
      if (saDonor.email.toLowerCase() !== stripeCustomer.email.toLowerCase()) {
        discrepancies.push(`📧 Email mismatch: SA="${saDonor.email}" vs Stripe="${stripeCustomer.email}"`);
      }
    } else if (saDonor.email && !stripeCustomer.email) {
      discrepancies.push(`📧 SA has email "${saDonor.email}" but Stripe customer has no email`);
    } else if (!saDonor.email && stripeCustomer.email) {
      discrepancies.push(`📧 Stripe has email "${stripeCustomer.email}" but SA donor has no email`);
    }

    // Compare name (important for verification)
    const saFullName = `${saDonor.firstname || ''} ${saDonor.lastname || ''}`.trim();
    if (saFullName && stripeCustomer.name) {
      // Normalize names for comparison (handle case differences, extra spaces)
      const normalizedSaName = saFullName.toLowerCase().replace(/\s+/g, ' ');
      const normalizedStripeName = stripeCustomer.name.toLowerCase().replace(/\s+/g, ' ');

      if (normalizedSaName !== normalizedStripeName) {
        discrepancies.push(`👤 Name mismatch: SA="${saFullName}" vs Stripe="${stripeCustomer.name}"`);
      }
    } else if (saFullName && !stripeCustomer.name) {
      discrepancies.push(`👤 SA has name "${saFullName}" but Stripe customer has no name`);
    } else if (!saFullName && stripeCustomer.name) {
      discrepancies.push(`👤 Stripe has name "${stripeCustomer.name}" but SA donor has incomplete name data`);
    }

    // Compare phone (useful for verification)
    if (saDonor.phone && stripeCustomer.phone) {
      // Normalize phone numbers for comparison (remove formatting)
      const normalizedSaPhone = saDonor.phone.replace(/\D/g, '');
      const normalizedStripePhone = stripeCustomer.phone.replace(/\D/g, '');

      if (normalizedSaPhone !== normalizedStripePhone) {
        discrepancies.push(`📞 Phone mismatch: SA="${saDonor.phone}" vs Stripe="${stripeCustomer.phone}"`);
      }
    } else if (saDonor.phone && !stripeCustomer.phone) {
      discrepancies.push(`📞 SA has phone "${saDonor.phone}" but Stripe customer has no phone`);
    }

    // Compare address fields (billing address comparison)
    if (stripeCustomer.address) {
      // Address Line 1
      if (saDonor.address1 && stripeCustomer.address.line1) {
        const normalizedSaAddress1 = saDonor.address1.toLowerCase().trim();
        const normalizedStripeAddress1 = stripeCustomer.address.line1.toLowerCase().trim();
        if (normalizedSaAddress1 !== normalizedStripeAddress1) {
          discrepancies.push(`🏠 Address Line 1 mismatch: SA="${saDonor.address1}" vs Stripe="${stripeCustomer.address.line1}"`);
        }
      } else if (saDonor.address1 && !stripeCustomer.address.line1) {
        discrepancies.push(`🏠 SA has address1 "${saDonor.address1}" but Stripe has no address line 1`);
      } else if (!saDonor.address1 && stripeCustomer.address.line1) {
        discrepancies.push(`🏠 Stripe has address line 1 "${stripeCustomer.address.line1}" but SA has no address1`);
      }

      // Address Line 2
      if (saDonor.address2 && stripeCustomer.address.line2) {
        const normalizedSaAddress2 = saDonor.address2.toLowerCase().trim();
        const normalizedStripeAddress2 = stripeCustomer.address.line2.toLowerCase().trim();
        if (normalizedSaAddress2 !== normalizedStripeAddress2) {
          discrepancies.push(`🏠 Address Line 2 mismatch: SA="${saDonor.address2}" vs Stripe="${stripeCustomer.address.line2}"`);
        }
      } else if (saDonor.address2 && !stripeCustomer.address.line2) {
        discrepancies.push(`🏠 SA has address2 "${saDonor.address2}" but Stripe has no address line 2`);
      } else if (!saDonor.address2 && stripeCustomer.address.line2) {
        discrepancies.push(`🏠 Stripe has address line 2 "${stripeCustomer.address.line2}" but SA has no address2`);
      }

      // City
      if (saDonor.city && stripeCustomer.address.city) {
        const normalizedSaCity = saDonor.city.toLowerCase().trim();
        const normalizedStripeCity = stripeCustomer.address.city.toLowerCase().trim();
        if (normalizedSaCity !== normalizedStripeCity) {
          discrepancies.push(`🏙️ City mismatch: SA="${saDonor.city}" vs Stripe="${stripeCustomer.address.city}"`);
        }
      } else if (saDonor.city && !stripeCustomer.address.city) {
        discrepancies.push(`🏙️ SA has city "${saDonor.city}" but Stripe has no city`);
      } else if (!saDonor.city && stripeCustomer.address.city) {
        discrepancies.push(`🏙️ Stripe has city "${stripeCustomer.address.city}" but SA has no city`);
      }

      // State
      if (saDonor.state && stripeCustomer.address.state) {
        const normalizedSaState = saDonor.state.toLowerCase().trim();
        const normalizedStripeState = stripeCustomer.address.state.toLowerCase().trim();
        if (normalizedSaState !== normalizedStripeState) {
          discrepancies.push(`🗺️ State mismatch: SA="${saDonor.state}" vs Stripe="${stripeCustomer.address.state}"`);
        }
      } else if (saDonor.state && !stripeCustomer.address.state) {
        discrepancies.push(`🗺️ SA has state "${saDonor.state}" but Stripe has no state`);
      } else if (!saDonor.state && stripeCustomer.address.state) {
        discrepancies.push(`🗺️ Stripe has state "${stripeCustomer.address.state}" but SA has no state`);
      }

      // Postal Code
      if (saDonor.postal_code && stripeCustomer.address.postal_code) {
        // Normalize postal codes (remove spaces, hyphens for comparison)
        const normalizedSaPostal = saDonor.postal_code.replace(/[\s-]/g, '').toLowerCase();
        const normalizedStripePostal = stripeCustomer.address.postal_code.replace(/[\s-]/g, '').toLowerCase();
        if (normalizedSaPostal !== normalizedStripePostal) {
          discrepancies.push(`📮 Postal code mismatch: SA="${saDonor.postal_code}" vs Stripe="${stripeCustomer.address.postal_code}"`);
        }
      } else if (saDonor.postal_code && !stripeCustomer.address.postal_code) {
        discrepancies.push(`📮 SA has postal code "${saDonor.postal_code}" but Stripe has no postal code`);
      } else if (!saDonor.postal_code && stripeCustomer.address.postal_code) {
        discrepancies.push(`📮 Stripe has postal code "${stripeCustomer.address.postal_code}" but SA has no postal code`);
      }

      // Country
      if (saDonor.country && stripeCustomer.address.country) {
        const normalizedSaCountry = saDonor.country.toLowerCase().trim();
        const normalizedStripeCountry = stripeCustomer.address.country.toLowerCase().trim();
        if (normalizedSaCountry !== normalizedStripeCountry) {
          discrepancies.push(`🌍 Country mismatch: SA="${saDonor.country}" vs Stripe="${stripeCustomer.address.country}"`);
        }
      } else if (saDonor.country && !stripeCustomer.address.country) {
        discrepancies.push(`🌍 SA has country "${saDonor.country}" but Stripe has no country`);
      } else if (!saDonor.country && stripeCustomer.address.country) {
        discrepancies.push(`🌍 Stripe has country "${stripeCustomer.address.country}" but SA has no country`);
      }
    } else if (saDonor.address1 || saDonor.city || saDonor.state || saDonor.postal_code || saDonor.country) {
      discrepancies.push(`🏠 SA has address information but Stripe customer has no billing address`);
    }

    // Check stored Stripe customer ID in SA (legacy linkage method)
    if (saDonor.stripe_cus_id) {
      if (saDonor.stripe_cus_id !== stripeCustomer.id) {
        discrepancies.push(`🆔 Stored Stripe ID mismatch: SA has "${saDonor.stripe_cus_id}" but found customer "${stripeCustomer.id}"`);
      }
    } else {
      discrepancies.push(`🆔 SA donor has no stored Stripe customer ID - should be updated to "${stripeCustomer.id}"`);
    }

    return discrepancies;
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setError('Please enter a search term');
      return;
    }

    setIsSearching(true);
    setError(null);
    setReconciliationResults([]);
    addToRecentSearches(searchQuery.trim());

    try {
      // Search Station Admin for donors
      const saDonors = await searchDonors(searchQuery);
      
      if (saDonors.length === 0) {
        setError('No donors found in Station Admin');
        setIsSearching(false);
        return;
      }

      const results: ReconciliationResult[] = [];

      // For each SA donor, try to find corresponding Stripe customer(s)
      for (const saDonor of saDonors) {
        let stripeCustomer: StripeCustomer | null = null;
        let multipleStripeAccounts: StripeCustomer[] = [];

        if (saDonor.email) {
          try {
            const { data } = await findStripeCustomer({
              variables: { email: saDonor.email }
            });

            if (data?.stripeCustomerByEmail) {
              const customers = data.stripeCustomerByEmail;

              // Filter customers that have matching donor_id metadata (these are the "linked duplicates")
              const linkedCustomers = customers.filter((customer: StripeCustomer) =>
                customer.metadata?.donor_id === saDonor.id.toString()
              );

              if (linkedCustomers.length > 0) {
                // Use linked customers (these are properly connected to this SA donor)
                multipleStripeAccounts = linkedCustomers;
                stripeCustomer = linkedCustomers[0]; // Use first linked one for comparison
              } else if (customers.length > 0) {
                // Found customers by email but no donor_id metadata match
                // These might be unlinked accounts that should be connected
                multipleStripeAccounts = customers;
                stripeCustomer = customers[0]; // Use first one for comparison
              }
            }
          } catch (stripeError) {
            console.error('Error searching Stripe for donor:', saDonor.email, stripeError);
          }
        }

        const discrepancies = stripeCustomer ? findDiscrepancies(saDonor, stripeCustomer) : [];
        
        results.push({
          saDonor,
          stripeCustomer,
          discrepancies,
          hasStripeAccount: !!stripeCustomer,
          multipleStripeAccounts: multipleStripeAccounts.length > 1 ? multipleStripeAccounts : undefined
        });
      }

      setReconciliationResults(results);
    } catch (error) {
      console.error('Search error:', error);
      setError(error instanceof Error ? error.message : 'Search failed');
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const getFilteredResults = () => {
    if (statusFilter === 'all') return reconciliationResults;

    return reconciliationResults.filter(result => {
      switch (statusFilter) {
        case 'perfect':
          return result.hasStripeAccount && result.discrepancies.length === 0;
        case 'discrepancies':
          return result.hasStripeAccount && result.discrepancies.length > 0;
        case 'no-stripe':
          return !result.hasStripeAccount;
        default:
          return true;
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Search Section */}
      <Card>
        <CardHeader>
          <CardTitle>Donor Reconciliation</CardTitle>
          <p className="text-sm text-gray-600">Compare Station Admin donors with Stripe customer data</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Input
              type="text"
              placeholder="Search SA donor by name, email, or donor ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              className="flex-1"
              disabled={isSearching}
            />
            <Button
              onClick={handleSearch}
              disabled={isSearching || !searchQuery.trim()}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2 cursor-pointer disabled:cursor-not-allowed disabled:opacity-50 transition-colors"
            >
              {isSearching ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Searching...
                </>
              ) : (
                <>
                  <span className="mr-2">🔍</span>
                  Find Discrepancies
                </>
              )}
            </Button>
            {reconciliationResults.length > 0 && (
              <Tooltip content="Export reconciliation results as CSV file for reporting and follow-up actions">
                <Button
                  onClick={exportResults}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export CSV
                </Button>
              </Tooltip>
            )}
          </div>

          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm text-gray-600 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Recent searches:
              </p>
              <div className="flex flex-wrap gap-2">
                {recentSearches.map((search, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setSearchQuery(search)}
                    className="text-xs h-7 px-2"
                  >
                    {search}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* How-To Guide */}
          <div className="bg-gray-50 p-4 rounded-lg border">
            <details className="group">
              <summary className="cursor-pointer font-medium text-gray-800 hover:text-gray-900 flex items-center">
                <span className="mr-2">📖</span>
                How-To Guide: Understanding Reconciliation (WIP - Draft)
                <span className="ml-2 text-gray-500 group-open:rotate-90 transition-transform">▶</span>
              </summary>
              <div className="mt-4 space-y-4 text-sm text-gray-700">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">🔄 Data Flow & Search Process</h4>
                  <ol className="list-decimal list-inside space-y-1 ml-4">
                    <li>Search Station Admin (SA) database for donors matching your query</li>
                    <li>For each SA donor found, search Stripe by email address</li>
                    <li>Compare SA donor data with Stripe customer data</li>
                    <li>Identify discrepancies and linking issues</li>
                  </ol>
                </div>

                <div>
                  <h4 className="font-medium text-gray-800 mb-2">✅ What "Data Matches" Means</h4>
                  <p className="mb-2">A donor shows "Data Matches" when ALL of these conditions are met:</p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li><strong>Email:</strong> SA email exactly matches Stripe email (case-insensitive)</li>
                    <li><strong>Name:</strong> SA firstname + lastname matches Stripe name (normalized for spacing/case)</li>
                    <li><strong>Phone:</strong> SA phone matches Stripe phone (numbers only, formatting ignored)</li>
                    <li><strong>Address:</strong> SA address fields match Stripe billing address:
                      <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                        <li>Address Line 1 & 2 (normalized for case/spacing)</li>
                        <li>City (normalized for case/spacing)</li>
                        <li>State/Province (normalized for case/spacing)</li>
                        <li>Postal/ZIP Code (ignores formatting like spaces and hyphens)</li>
                        <li>Country (normalized for case/spacing)</li>
                      </ul>
                    </li>
                    <li><strong>Donor ID Link:</strong> Stripe metadata contains correct SA donor_id</li>
                    <li><strong>Customer ID:</strong> SA has correct Stripe customer ID stored</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-gray-800 mb-2">🚨 Common Discrepancies</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li><strong>🔗 Missing donor_id metadata:</strong> Stripe customer not properly linked to SA donor</li>
                    <li><strong>📧 Email mismatch:</strong> Different emails in SA vs Stripe (typos, updates)</li>
                    <li><strong>👤 Name differences:</strong> Formatting differences, nicknames, or data entry errors</li>
                    <li><strong>📞 Phone mismatches:</strong> Different phone numbers or formatting</li>
                    <li><strong>🏠 Address discrepancies:</strong> Different billing addresses, missing address data, or formatting differences</li>
                    <li><strong>🆔 Wrong stored Stripe ID:</strong> SA has incorrect or missing Stripe customer ID</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-gray-800 mb-2">⚠️ Duplicate Types Explained</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-orange-50 p-3 rounded border-l-4 border-orange-400">
                      <h5 className="font-medium text-orange-800">Linked Duplicates</h5>
                      <p className="text-orange-700 text-xs mt-1">
                        Same SA donor has multiple Stripe accounts (same donor_id metadata).
                        Happens when someone creates multiple Stripe accounts during different donations.
                      </p>
                    </div>
                    <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
                      <h5 className="font-medium text-blue-800">Individual Duplicates</h5>
                      <p className="text-blue-700 text-xs mt-1">
                        Same person has multiple SA donor records.
                        Different issue - handled by separate SA donor deduplication tools.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 p-3 rounded border-l-4 border-yellow-400">
                  <p className="text-yellow-800 text-xs">
                    <strong>Note:</strong> This is a read-only research dashboard.
                    Use findings to manually update records in Station Admin or contact tech support for bulk fixes.
                  </p>
                </div>
              </div>
            </details>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Results */}
      {reconciliationResults.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-gray-900">
              Reconciliation Analysis ({reconciliationResults.length} SA donors)
            </h3>
            <div className="text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded">
              Read-only research dashboard
            </div>
          </div>

          {/* Summary Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {(() => {
              const withStripe = reconciliationResults.filter(r => r.hasStripeAccount).length;
              const withDiscrepancies = reconciliationResults.filter(r => r.discrepancies.length > 0).length;
              const perfect = reconciliationResults.filter(r => r.hasStripeAccount && r.discrepancies.length === 0).length;
              const noStripe = reconciliationResults.length - withStripe;

              return (
                <>
                  <Tooltip content="Click to filter: Donors with Stripe accounts where all data fields match perfectly">
                    <Card
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        statusFilter === 'perfect'
                          ? 'bg-green-100 border-green-300 ring-2 ring-green-400'
                          : 'bg-green-50 border-green-200 hover:bg-green-100'
                      }`}
                      onClick={() => setStatusFilter(statusFilter === 'perfect' ? 'all' : 'perfect')}
                    >
                      <CardContent className="p-4 text-center">
                        <div className="flex items-center justify-center mb-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        </div>
                        <div className="text-2xl font-bold text-green-700">{perfect}</div>
                        <div className="text-sm text-green-600">Perfect Matches</div>
                      </CardContent>
                    </Card>
                  </Tooltip>
                  <Tooltip content="Click to filter: Donors with Stripe accounts but mismatched data">
                    <Card
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        statusFilter === 'discrepancies'
                          ? 'bg-orange-100 border-orange-300 ring-2 ring-orange-400'
                          : 'bg-orange-50 border-orange-200 hover:bg-orange-100'
                      }`}
                      onClick={() => setStatusFilter(statusFilter === 'discrepancies' ? 'all' : 'discrepancies')}
                    >
                      <CardContent className="p-4 text-center">
                        <div className="flex items-center justify-center mb-2">
                          <AlertTriangle className="h-5 w-5 text-orange-600" />
                        </div>
                        <div className="text-2xl font-bold text-orange-700">{withDiscrepancies}</div>
                        <div className="text-sm text-orange-600">With Discrepancies</div>
                      </CardContent>
                    </Card>
                  </Tooltip>
                  <Tooltip content="Click to filter: Station Admin donors without Stripe customer accounts">
                    <Card
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        statusFilter === 'no-stripe'
                          ? 'bg-gray-100 border-gray-300 ring-2 ring-gray-400'
                          : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                      }`}
                      onClick={() => setStatusFilter(statusFilter === 'no-stripe' ? 'all' : 'no-stripe')}
                    >
                      <CardContent className="p-4 text-center">
                        <div className="flex items-center justify-center mb-2">
                          <XCircle className="h-5 w-5 text-gray-600" />
                        </div>
                        <div className="text-2xl font-bold text-gray-700">{noStripe}</div>
                        <div className="text-sm text-gray-600">No Stripe Account</div>
                      </CardContent>
                    </Card>
                  </Tooltip>
                  <Tooltip content="Click to show all: Total number of Station Admin donors analyzed">
                    <Card
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        statusFilter === 'all'
                          ? 'bg-blue-100 border-blue-300 ring-2 ring-blue-400'
                          : 'bg-blue-50 border-blue-200 hover:bg-blue-100'
                      }`}
                      onClick={() => setStatusFilter('all')}
                    >
                      <CardContent className="p-4 text-center">
                        <div className="flex items-center justify-center mb-2">
                          <span className="text-blue-600 font-bold text-lg">Σ</span>
                        </div>
                        <div className="text-2xl font-bold text-blue-700">{reconciliationResults.length}</div>
                        <div className="text-sm text-blue-600">Total Donors</div>
                      </CardContent>
                    </Card>
                  </Tooltip>
                </>
              );
            })()}
          </div>

          {/* Filter Status */}
          {statusFilter !== 'all' && (
            <div className="flex items-center justify-between bg-blue-50 p-3 rounded border">
              <div className="flex items-center gap-2">
                <span className="text-sm text-blue-700">
                  Showing {getFilteredResults().length} of {reconciliationResults.length} donors
                  {statusFilter === 'perfect' && ' with perfect matches'}
                  {statusFilter === 'discrepancies' && ' with discrepancies'}
                  {statusFilter === 'no-stripe' && ' without Stripe accounts'}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStatusFilter('all')}
                className="text-blue-700 border-blue-300 hover:bg-blue-100"
              >
                Clear Filter
              </Button>
            </div>
          )}

          {getFilteredResults().map((result, index) => (
            <Card key={index} className={`border-l-4 ${
              result.discrepancies.length > 0
                ? 'border-l-red-500 bg-red-50'
                : result.hasStripeAccount
                ? 'border-l-green-500 bg-green-50'
                : 'border-l-gray-500 bg-gray-50'
            }`}>
              <CardHeader>
                <CardTitle className="text-lg flex items-center justify-between">
                  <div>
                    <span className="text-gray-900">
                      {result.saDonor.firstname} {result.saDonor.lastname}
                    </span>
                    <div className="text-sm font-normal text-gray-600 mt-1">
                      <span className="font-mono bg-gray-100 px-2 py-1 rounded">SA ID: {result.saDonor.id}</span>
                      {result.saDonor.email && (
                        <span className="ml-2">{result.saDonor.email}</span>
                      )}
                    </div>
                  </div>
                  <div className="text-right flex flex-col items-end gap-2">
                    {(() => {
                      const statusInfo = getStatusInfo(result);
                      const tooltipContent = statusInfo.status === 'Perfect Match'
                        ? 'All data fields match between Station Admin and Stripe'
                        : statusInfo.status === 'Has Discrepancies'
                        ? 'Some data fields differ between Station Admin and Stripe'
                        : 'No corresponding Stripe customer account found';
                      return (
                        <Tooltip content={tooltipContent}>
                          <Badge className={`${statusInfo.bgColor} ${statusInfo.color} flex items-center gap-2 px-3 py-2`}>
                            {statusInfo.icon}
                            {statusInfo.status}
                          </Badge>
                        </Tooltip>
                      );
                    })()}
                    {result.multipleStripeAccounts && (
                      <Tooltip content="This donor has multiple Stripe customer accounts linked to the same email address">
                        <Badge variant="outline" className="bg-orange-100 text-orange-800 text-xs">
                          {result.multipleStripeAccounts.length} Linked Stripe Accounts
                        </Badge>
                      </Tooltip>
                    )}
                    {/* Quick Actions */}
                    <div className="flex gap-1 mt-1">
                      {result.saDonor.email && (
                        <Tooltip content="Copy email address to clipboard">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(result.saDonor.email!, 'Email')}
                            className="h-7 px-2 text-xs"
                          >
                            {copiedText === 'Email' ? <CheckCircle className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                          </Button>
                        </Tooltip>
                      )}
                      {result.stripeCustomer?.id && (
                        <Tooltip content="Open Stripe customer page in new tab">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(`https://dashboard.stripe.com/customers/${result.stripeCustomer!.id}`, '_blank')}
                            className="h-7 px-2 text-xs"
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </Tooltip>
                      )}
                    </div>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Discrepancies Alert */}
                {result.discrepancies.length > 0 && (
                  <div className="mb-6 p-4 bg-red-100 border border-red-300 rounded-lg">
                    <h5 className="font-medium text-red-800 mb-3 flex items-center">
                      <span className="mr-2">🚨</span>
                      Data Discrepancies Detected
                    </h5>
                    <ul className="space-y-2">
                      {result.discrepancies.map((discrepancy, idx) => (
                        <li key={idx} className="text-sm text-red-700 bg-red-50 p-2 rounded flex items-start">
                          <span className="mr-2 mt-0.5 text-red-500">•</span>
                          {discrepancy}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Multiple Stripe Accounts Warning */}
                {result.multipleStripeAccounts && result.multipleStripeAccounts.length > 1 && (
                  <div className="mb-6 p-4 bg-orange-100 border border-orange-300 rounded-lg">
                    <h5 className="font-medium text-orange-800 mb-2 flex items-center">
                      <span className="mr-2">⚠️</span>
                      Linked Duplicate Stripe Accounts Detected
                    </h5>
                    <p className="text-orange-700 text-sm mb-3">
                      This SA donor has {result.multipleStripeAccounts.length} Stripe customer accounts with the same donor_id metadata.
                      This indicates the same person created multiple Stripe accounts during different donation sessions.
                    </p>
                    <div className="text-xs text-orange-600">
                      <strong>Note:</strong> This is different from "individual duplicates" where the same person has multiple SA donor records.
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Station Admin Data */}
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-900 mb-3 flex items-center">
                      <span className="mr-2">🏢</span>
                      Station Admin Record
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="font-medium text-blue-800">Email:</span>
                        <span className="text-blue-700">{result.saDonor.email || 'Not provided'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-blue-800">Phone:</span>
                        <span className="text-blue-700">{result.saDonor.phone || 'Not provided'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-blue-800">Stored Stripe ID:</span>
                        <span className="text-blue-700 font-mono text-xs">{result.saDonor.stripe_cus_id || 'None stored'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-blue-800">Member Type:</span>
                        <span className="text-blue-700">{result.saDonor.type || 'Unknown'}</span>
                      </div>
                    </div>
                  </div>

                  {/* Stripe Data */}
                  <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <h4 className="font-medium text-purple-900 mb-3 flex items-center">
                      <span className="mr-2">💳</span>
                      Stripe Customer Data
                    </h4>
                    {result.stripeCustomer ? (
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="font-medium text-purple-800">Email:</span>
                          <span className="text-purple-700">{result.stripeCustomer.email || 'Not provided'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium text-purple-800">Name:</span>
                          <span className="text-purple-700">{result.stripeCustomer.name || 'Not provided'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium text-purple-800">Phone:</span>
                          <span className="text-purple-700">{result.stripeCustomer.phone || 'Not provided'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium text-purple-800">Customer ID:</span>
                          <span className="text-purple-700 font-mono text-xs">{result.stripeCustomer.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium text-purple-800">Created:</span>
                          <span className="text-purple-700">{new Date(result.stripeCustomer.created * 1000).toLocaleDateString()}</span>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-purple-600 text-sm">No Stripe customer found</p>
                        <p className="text-purple-500 text-xs mt-1">This donor may not have made online donations</p>
                      </div>
                    )}
                  </div>
                </div>


              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
