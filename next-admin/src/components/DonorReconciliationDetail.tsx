'use client';

import { useQuery, gql } from '@apollo/client';
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { StripeCustomerReport } from '@/types';

// GraphQL query for comprehensive Stripe customer report
const GET_STRIPE_CUSTOMER_REPORT = gql`
  query GetStripeCustomerReport($customerId: String!) {
    stripeCustomerReport(customerId: $customerId) {
      customer {
        id
        email
        name
        phone
        created
        currency
        balance
        description
        metadata
      }
      paymentMethods {
        id
        type
        created
        card {
          brand
          last4
          expMonth
          expYear
        }
      }
      subscriptions {
        id
        status
        currentPeriodStart
        currentPeriodEnd
        amount
        currency
        interval
        intervalCount
      }
      charges {
        id
        amount
        currency
        status
        created
        description
        receiptUrl
        paymentMethod
      }
      totalSpent
      lastPaymentDate
    }
  }
`;

interface StationAdminDonor {
  id: number;
  firstname?: string;
  lastname?: string;
  email?: string;
  phone?: string;
  stripe_cus_id?: string;
  address1?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  membership_level?: string;
  date_created?: string;
  [key: string]: any;
}

interface DonorReconciliationDetailProps {
  saDonor: StationAdminDonor;
  stripeCustomerId: string;
  onClose: () => void;
  onUpdateStripeId?: (donorId: number, stripeCustomerId: string) => void;
}

export function DonorReconciliationDetail({ 
  saDonor, 
  stripeCustomerId, 
  onClose,
  onUpdateStripeId 
}: DonorReconciliationDetailProps) {
  const { data, loading, error } = useQuery(GET_STRIPE_CUSTOMER_REPORT, {
    variables: { customerId: stripeCustomerId },
    errorPolicy: 'all',
  });

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const handleUpdateStripeId = () => {
    if (onUpdateStripeId) {
      onUpdateStripeId(saDonor.id, stripeCustomerId);
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-6xl max-h-[90vh] overflow-auto">
          <CardContent className="p-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3">Loading reconciliation details...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-6xl max-h-[90vh] overflow-auto">
          <CardHeader>
            <CardTitle className="flex justify-between items-center">
              Error Loading Stripe Data
              <Button variant="outline" onClick={onClose}>Close</Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">Failed to load Stripe customer details: {error.message}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const report: StripeCustomerReport = data?.stripeCustomerReport;
  if (!report) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-6xl max-h-[90vh] overflow-auto">
          <CardHeader>
            <CardTitle className="flex justify-between items-center">
              Stripe Customer Not Found
              <Button variant="outline" onClick={onClose}>Close</Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>No Stripe customer found with ID: {stripeCustomerId}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { customer, totalSpent, charges } = report;
  const saFullName = `${saDonor.firstname || ''} ${saDonor.lastname || ''}`.trim();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-auto">
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl">Donor Reconciliation Details</h2>
              <p className="text-sm text-gray-600">
                Station Admin ID: {saDonor.id} • Stripe ID: {customer.id}
              </p>
            </div>
            <Button variant="outline" onClick={onClose}>Close</Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Comparison Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Station Admin Data */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Station Admin Data</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">Name</label>
                  <p className="font-medium">{saFullName || 'No name'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Email</label>
                  <p>{saDonor.email || 'No email'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Phone</label>
                  <p>{saDonor.phone || 'No phone'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Address</label>
                  <p>{saDonor.address1 ? `${saDonor.address1}, ${saDonor.city || ''} ${saDonor.state || ''} ${saDonor.postal_code || ''}`.trim() : 'No address'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Membership Level</label>
                  <p>{saDonor.membership_level || 'None'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Stored Stripe ID</label>
                  <p className={saDonor.stripe_cus_id !== customer.id ? 'text-red-600 font-medium' : ''}>
                    {saDonor.stripe_cus_id || 'None'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Created</label>
                  <p>{saDonor.date_created ? new Date(saDonor.date_created).toLocaleDateString() : 'Unknown'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Stripe Data */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Stripe Data</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">Name</label>
                  <p className={customer.name !== saFullName && saFullName ? 'text-yellow-600 font-medium' : 'font-medium'}>
                    {customer.name || 'No name'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Email</label>
                  <p className={customer.email !== saDonor.email && saDonor.email ? 'text-yellow-600 font-medium' : ''}>
                    {customer.email || 'No email'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Phone</label>
                  <p className={customer.phone !== saDonor.phone && saDonor.phone ? 'text-yellow-600 font-medium' : ''}>
                    {customer.phone || 'No phone'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Customer ID</label>
                  <p className="font-mono text-sm">{customer.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Total Spent</label>
                  <p className="font-medium text-green-600">
                    {formatCurrency(totalSpent * 100, customer.currency)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Successful Payments</label>
                  <p>{charges.filter(c => c.status === 'succeeded').length}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Created</label>
                  <p>{formatDate(customer.created)}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          {saDonor.stripe_cus_id !== customer.id && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-blue-900">Update Station Admin Record</h4>
                    <p className="text-sm text-blue-700">
                      The Stripe customer ID in Station Admin doesn't match. Update it to link these accounts?
                    </p>
                  </div>
                  <Button 
                    onClick={handleUpdateStripeId}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Update Stripe ID
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Discrepancies Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Reconciliation Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {customer.email === saDonor.email ? (
                  <p className="text-green-600">✓ Email addresses match</p>
                ) : (
                  <p className="text-yellow-600">⚠ Email addresses differ</p>
                )}
                
                {customer.name === saFullName ? (
                  <p className="text-green-600">✓ Names match</p>
                ) : (
                  <p className="text-yellow-600">⚠ Names differ</p>
                )}
                
                {customer.phone === saDonor.phone ? (
                  <p className="text-green-600">✓ Phone numbers match</p>
                ) : (
                  <p className="text-yellow-600">⚠ Phone numbers differ</p>
                )}
                
                {saDonor.stripe_cus_id === customer.id ? (
                  <p className="text-green-600">✓ Stripe customer ID is correctly stored</p>
                ) : (
                  <p className="text-red-600">✗ Stripe customer ID needs to be updated</p>
                )}
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
}
