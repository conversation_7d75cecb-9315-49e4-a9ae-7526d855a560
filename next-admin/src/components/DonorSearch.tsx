'use client';

import { useState } from 'react';
import { searchDonors } from '@/lib/api';
import { Donor } from '@/types';

interface DonorSearchProps {
  onSelectDonor: (donor: Donor) => void;
  placeholder?: string;
  buttonLabel?: string;
  label?: string;
}

export function DonorSearch({
  onSelectDonor,
  placeholder = 'Search donor ID or name',
  buttonLabel = 'Search',
  label = 'Search for Donor'
}: DonorSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Donor[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setError('Please enter a search term');
      return;
    }

    setIsSearching(true);
    setError(null);

    try {
      const results = await searchDonors(searchQuery);
      setSearchResults(results as Donor[]);
      
      if (results.length === 0) {
        setError('No donors found matching your search');
      }
    } catch (err) {
      console.error('Error searching donors:', err);
      setError('Failed to search for donors');
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label htmlFor="donor-search" className="block text-sm font-medium mb-1">
          {label}
        </label>
        <div className="flex items-center space-x-2">
          <input
            id="donor-search"
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="flex-1 p-2 border rounded"
            disabled={isSearching}
          />
          <button
            onClick={handleSearch}
            disabled={isSearching}
            className="px-4 py-2 bg-primary text-white rounded disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
          >
            {isSearching ? 'Searching...' : buttonLabel}
          </button>
        </div>
      </div>

      {error && (
        <div className="p-2 text-sm text-red-700 bg-red-100 rounded">
          {error}
        </div>
      )}

      {searchResults.length > 0 && (
        <div className="border rounded divide-y">
          {searchResults.map((donor) => (
            <div
              key={donor.id}
              className="p-3 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
              onClick={() => onSelectDonor(donor)}
            >
              <p className="font-medium">{donor.firstname} {donor.lastname}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                ID: {donor.id}
                {donor.email && ` • ${donor.email}`}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 