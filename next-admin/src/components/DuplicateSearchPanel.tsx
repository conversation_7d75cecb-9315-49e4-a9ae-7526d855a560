'use client';

import { useState } from 'react';
import { searchDuplicatesByPhone } from '@/lib/api';
import { Spinner } from '@/components/ui/spinner';

interface PotentialDuplicate {
  id: number;
  display_name: string;
  firstname: string;
  lastname: string;
  phone: string;
  formatted_phone: string;
  email: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  type: string;
  membership_level: string;
  deceased: boolean;
  donotsolicit: boolean;
  date_created: string;
  date_updated: string;
}

interface DuplicateSearchResult {
  success: boolean;
  search_phone: string;
  clean_search_phone: string;
  total_found: number;
  exact_matches: PotentialDuplicate[];
}

interface DuplicateSearchPanelProps {
  onDonorSelect?: (donor: PotentialDuplicate) => void;
  excludeDonorId?: number;
  className?: string;
}

export function DuplicateSearchPanel({ 
  onDonorSelect, 
  excludeDonorId, 
  className = '' 
}: DuplicateSearchPanelProps) {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<DuplicateSearchResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async () => {
    if (!phoneNumber.trim()) {
      setError('Please enter a phone number');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSearchResult(null);
    setHasSearched(false);

    try {
      const result = await searchDuplicatesByPhone(phoneNumber.trim(), excludeDonorId, 20);
      setSearchResult(result);
      setHasSearched(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while searching');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const formatAddress = (donor: PotentialDuplicate) => {
    const parts = [
      donor.address1,
      donor.address2,
      donor.city,
      donor.state,
      donor.postal_code
    ].filter(Boolean);
    
    return parts.length > 0 ? parts.join(', ') : 'No address';
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-4">Search for Exact Phone Number Matches</h3>
        
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter phone number (e.g., ************)"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          />
          <button
            onClick={handleSearch}
            disabled={isLoading || !phoneNumber.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? <Spinner size="sm" className="mr-2" /> : null}
            Search
          </button>
        </div>

        {excludeDonorId && (
          <p className="text-sm text-gray-600 mb-2">
            Excluding donor ID: {excludeDonorId}
          </p>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <Spinner size="lg" />
          <span className="ml-2 text-gray-600">Searching for exact matches...</span>
        </div>
      )}

      {searchResult && (
        <div className="space-y-4">
          <div className="bg-gray-50 p-3 rounded-md">
            <p className="text-sm text-gray-600">
              Searched for: <span className="font-mono">{searchResult.search_phone}</span>
              {' '}(cleaned: <span className="font-mono">{searchResult.clean_search_phone}</span>)
            </p>
            <p className="text-sm text-gray-600">
              Found {searchResult.total_found} exact match{searchResult.total_found !== 1 ? 'es' : ''}
            </p>
          </div>

          {searchResult.exact_matches.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p className="text-lg">🎉 No exact matches found!</p>
              <p className="text-sm">This phone number doesn't exactly match any existing donors.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {searchResult.exact_matches.map((donor) => (
                <div
                  key={donor.id}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => onDonorSelect?.(donor)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-semibold text-lg">
                        {donor.display_name}
                        {donor.deceased && (
                          <span className="ml-2 text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                            Deceased
                          </span>
                        )}
                      </h4>
                      <p className="text-sm text-gray-600">ID: {donor.id}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div>
                      <p className="text-gray-600">
                        <span className="font-medium">Phone:</span> {donor.formatted_phone || donor.phone || 'N/A'}
                      </p>
                      <p className="text-gray-600">
                        <span className="font-medium">Email:</span> {donor.email || 'N/A'}
                      </p>
                      <p className="text-gray-600">
                        <span className="font-medium">Type:</span> {donor.type || 'N/A'}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">
                        <span className="font-medium">Address:</span> {formatAddress(donor)}
                      </p>
                      <p className="text-gray-600">
                        <span className="font-medium">Updated:</span> {new Date(donor.date_updated).toLocaleDateString()}
                      </p>
                      {donor.membership_level && (
                        <p className="text-gray-600">
                          <span className="font-medium">Member:</span> {donor.membership_level}
                        </p>
                      )}
                    </div>
                  </div>

                  {donor.donotsolicit && (
                    <div className="mt-2">
                      <span className="inline-block px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded">
                        Do Not Solicit
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {hasSearched && !searchResult && !isLoading && !error && (
        <div className="text-center py-8 text-gray-500">
          <p>No results found. Try a different phone number.</p>
        </div>
      )}
    </div>
  );
} 