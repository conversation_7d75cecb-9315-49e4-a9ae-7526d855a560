"use client";

import { useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { LegacyDonorResponse } from "@/types";
import { getAuthToken } from "@/lib/api";

interface MergeDonorsModalProps {
  isOpen: boolean;
  onClose: () => void;
  primaryDonorId: number;
  primaryDonorDetails: LegacyDonorResponse | null;
  duplicateDonors: {
    id: string;
    kpfaId: number;
    details: LegacyDonorResponse | null;
  }[];
  groupName?: string;
  isBulkOperation?: boolean;
  currentGroupIndex?: number;
  totalGroups?: number;
  allGroups?: Array<{
    id: string;
    primaryDonorId: number;
    primaryDonorName?: string;
    groupName?: string;
    duplicateCount: number;
    duplicateDonors?: Array<{ kpfaId: number }>;
  }>;
  batchMode?: boolean;
}

interface MergeResult {
  success: boolean;
  message: string;
  details?: {
    secondaryDonors?: number[];
    deletedDonors?: number[];
    mergeSummary?: string;
    skippedDonorIds?: number[];
    donorsMerged?: number;
    donorsDeleted?: number;
  };
  error?: {
    details: string;
    code?: string;
  };
  deleted_donors?: number[];
  merge_summary?: string;
  secondary_donor_ids?: number[];
}

export function MergeDonorsModal({
  isOpen,
  onClose,
  primaryDonorId,
  primaryDonorDetails,
  duplicateDonors,
  groupName,
  isBulkOperation = false,
  currentGroupIndex = 0,
  totalGroups = 0,
  allGroups = [],
  batchMode = false,
}: MergeDonorsModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<MergeResult | null>(null);

  // Get primary donor name for display
  const primaryDonorName = primaryDonorDetails
    ? `${primaryDonorDetails.firstname} ${primaryDonorDetails.lastname}`
    : "";

  const handleMerge = async () => {
    setIsProcessing(true);
    setError(null);

    // Get auth token for API requests
    const authToken = getAuthToken();
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Accept: "application/json",
    };

    // Add authorization header if token exists
    if (authToken) {
      headers["Authorization"] = `Bearer ${authToken}`;
    }

    try {
      if (batchMode && allGroups && allGroups.length > 0) {
        // Process all groups in sequence
        let successCount = 0;
        let failCount = 0;
                  // Process merge
        let allDeletedDonors: number[] = [];
        let skippedDonorIds: number[] = [];

        // Execute them in sequence to avoid overwhelming the server
        for (const group of allGroups) {
          if (!group.primaryDonorId || !group.duplicateDonors) continue;

          try {
            // Get the duplicate donor IDs for this group
            const secondaryDonorIds = group.duplicateDonors.map(
              (d) => d.kpfaId,
            );

            if (secondaryDonorIds.length === 0) continue;

            // Call the API for each group
            const response = await fetch("/api/merge-donors", {
              method: "POST",
              headers,
              body: JSON.stringify({
                primaryDonorId: group.primaryDonorId,
                secondaryDonorIds: secondaryDonorIds,
                duplicateGroupId: group.id,
              }),
            });

            // Check for errors that mention specific donor IDs
            const data = await response.json();

            if (!response.ok) {
              // Check if the error is due to a donor not found
              const errorMsg = data.error?.details || data.message || "";
              const donorNotFoundMatch = errorMsg.match(
                /donor (\d+): "No donor found"/i,
              );

              if (donorNotFoundMatch && donorNotFoundMatch[1]) {
                // Extract the donor ID mentioned in the error
                const missingDonorId = parseInt(donorNotFoundMatch[1], 10);
                console.warn(
                  `Skipping missing donor with ID: ${missingDonorId}`,
                );

                // Add to the list of skipped donors
                skippedDonorIds.push(missingDonorId);

                // Remove the missing donor ID from the list and retry
                const validSecondaryDonorIds = secondaryDonorIds.filter(
                  (id) => id !== missingDonorId,
                );

                // Only retry if there are still valid donors to merge
                if (validSecondaryDonorIds.length > 0) {
                  console.log(
                    `Retrying merge with filtered donor list: ${validSecondaryDonorIds.join(", ")}`,
                  );

                  // Retry the merge with filtered donor list
                  const retryResponse = await fetch("/api/merge-donors", {
                    method: "POST",
                    headers,
                    body: JSON.stringify({
                      primaryDonorId: group.primaryDonorId,
                      secondaryDonorIds: validSecondaryDonorIds,
                      duplicateGroupId: group.id,
                    }),
                  });

                  const retryData = await retryResponse.json();

                  if (retryResponse.ok) {
                    successCount++;
                    // Retry was successful

                    if (retryData.details?.deletedDonors) {
                      allDeletedDonors = [
                        ...allDeletedDonors,
                        ...retryData.details.deletedDonors,
                      ];
                    }
                    continue;
                  }
                }
              }

              failCount++;
              continue;
            }

            successCount++;
                              // Successful merge

            if (data.details?.deletedDonors) {
              allDeletedDonors = [
                ...allDeletedDonors,
                ...data.details.deletedDonors,
              ];
            }
          } catch (err) {
            failCount++;
          }
        }

        // Set a composite result
        setResult({
          success: successCount > 0,
          message: `Completed merging ${successCount} of ${allGroups.length} groups`,
          details: {
            
            skippedDonorIds: skippedDonorIds,
            donorsMerged: allGroups.flatMap(
              (g) => g.duplicateDonors?.map((d) => d.kpfaId) || [],
            ).length,
            donorsDeleted: allDeletedDonors.length,
          },
          error:
            failCount > 0
              ? {
                  details: `Failed to merge ${failCount} groups${skippedDonorIds.length > 0 ? `. Skipped missing donors: ${skippedDonorIds.join(", ")}` : ""}`,
                }
              : undefined,
          deleted_donors: allDeletedDonors,
          secondary_donor_ids: allGroups.flatMap(
            (g) => g.duplicateDonors?.map((d) => d.kpfaId) || [],
          ),
        });
      } else {
        // Original single group merge logic
        const response = await fetch("/api/merge-donors", {
          method: "POST",
          headers,
          body: JSON.stringify({
            primaryDonorId,
            secondaryDonorIds: duplicateDonors.map((d) => d.kpfaId),
            duplicateGroupId: duplicateDonors[0]?.id?.split('_')[0],
          }),
        });

        const data = await response.json();

        if (!response.ok) {
          // Check if the error is due to a donor not found
          const errorMsg = data.error?.details || data.message || "";
          const donorNotFoundMatch = errorMsg.match(
            /donor (\d+): "No donor found"/i,
          );

          if (donorNotFoundMatch && donorNotFoundMatch[1]) {
            // Extract the donor ID mentioned in the error
            const missingDonorId = parseInt(donorNotFoundMatch[1], 10);

            // Remove the missing donor ID from the list and retry
            const validSecondaryDonorIds = duplicateDonors
              .map((d) => d.kpfaId)
              .filter((id) => id !== missingDonorId);

            // Only retry if there are still valid donors to merge
            if (validSecondaryDonorIds.length > 0) {
              // Retry the merge with filtered donor list
              const retryResponse = await fetch("/api/merge-donors", {
                method: "POST",
                headers,
                body: JSON.stringify({
                  primaryDonorId,
                  secondaryDonorIds: validSecondaryDonorIds,
                  duplicateGroupId: duplicateDonors[0]?.id?.split('_')[0],
                }),
              });

              const retryData = await retryResponse.json();

              if (retryResponse.ok) {
                setResult({
                  success: retryData.success,
                  message:
                    retryData.message ||
                    "Donors merged successfully! (Some missing donors were skipped)",
                  details: {
                    ...retryData.details,
                    skippedDonorIds: [missingDonorId],
                    donorsMerged: validSecondaryDonorIds.length,
                  },
                  error: retryData.error,
                  deleted_donors: retryData.deleted_donors,
                  merge_summary: retryData.merge_summary,
                  secondary_donor_ids: retryData.secondary_donor_ids || [],
                });
                return;
              }
            }
          }

          throw new Error(
            data.error?.details || data.message || "Failed to merge donors",
          );
        }

        setResult({
          success: data.success,
          message: data.message || "Donors merged successfully!",
          details: data.details,
          error: data.error
            ? {
                details: data.error.details,
                code: data.error.code,
              }
            : undefined,
          deleted_donors: data.deleted_donors,
          merge_summary: data.merge_summary,
          secondary_donor_ids: data.secondary_donor_ids || [],
        });
      }
    } catch (err: any) {
      setError(err.message || "An error occurred while merging donors");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    // Reset state when modal is closed
    setError(null);
    setResult(null);
    onClose();
  };

  const renderContent = () => {
    if (result) {
      return (
        <>
          <Dialog.Title
            as="h3"
            className="text-lg font-semibold leading-6 text-primary mb-4"
          >
            {result.success ? "Merge Complete" : "Merge Failed"}
            {!batchMode && isBulkOperation && totalGroups > 0 && (
              <span className="ml-2 text-sm font-normal text-gray-500">
                Group {currentGroupIndex + 1} of {totalGroups}
              </span>
            )}
          </Dialog.Title>

          <div className="mb-6">
            {result.success ? (
              <>
                {batchMode ? (
                  // Vastly simplified batch mode success view
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <div className="flex items-start">
                      <div className="bg-green-100 rounded-full p-2 mr-3">
                        <svg
                          className="h-6 w-6 text-green-600"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M5 13l4 4L19 7"
                          ></path>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-green-800 text-base">
                          Merge Successful
                        </h4>
                        <p className="text-sm text-green-700 mt-1">
                          {result.message ||
                            `Successfully merged donor groups.`}
                        </p>
                      </div>
                    </div>

                    {result.details?.skippedDonorIds &&
                      result.details.skippedDonorIds.length > 0 && (
                        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                          <h5 className="text-sm font-medium text-yellow-800">
                            Skipped Missing Donors
                          </h5>
                          <p className="mt-1 text-sm text-yellow-700">
                            The following donor records were skipped because
                            they couldn't be found in the database:
                          </p>
                          <div className="mt-2 max-h-40 overflow-y-auto">
                            <ul className="text-xs text-yellow-600 list-disc ml-5 space-y-1">
                              {result.details.skippedDonorIds.map((id) => (
                                <li key={id}>Donor #{id}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                  </div>
                ) : (
                  // Original single donor merge success view
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <div className="flex items-start">
                      <div className="bg-green-100 rounded-full p-2 mr-3">
                        <svg
                          className="h-6 w-6 text-green-600"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M5 13l4 4L19 7"
                          ></path>
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-green-800 text-base">
                          Merge Successful
                        </h4>
                        <p className="text-sm text-green-700 mt-1">
                          {`Successfully merged ${result.secondary_donor_ids?.length || 0} donor records into primary donor #${primaryDonorId}.`}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div className="bg-white rounded-lg p-3 shadow-sm border border-green-100">
                        <div className="flex justify-between">
                          <h5 className="text-sm font-medium text-gray-500">
                            Donors Merged
                          </h5>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {`${result.details?.donorsMerged || result.secondary_donor_ids?.length || 0} donors merged`}
                          </span>
                        </div>

                        <p className="mt-2 text-3xl font-bold text-gray-900">
                          {result.details?.donorsMerged || result.secondary_donor_ids?.length || 0}
                        </p>
                      </div>

                      <div className="bg-white rounded-lg p-3 shadow-sm border border-green-100">
                        <div className="flex justify-between">
                          <h5 className="text-sm font-medium text-gray-500">
                            Deleted Records
                          </h5>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {`${result.details?.donorsMerged || result.secondary_donor_ids?.length || 0} donors merged`}
                          </span>
                        </div>

                        <p className="mt-2 text-3xl font-bold text-gray-900">
                          {result.details?.deletedDonors?.length || result.deleted_donors?.length || 0}
                        </p>
                      </div>
                    </div>

                    {result.merge_summary && (
                      <div className="mt-4 border-t border-green-200 pt-3">
                        <h5 className="text-sm font-medium text-green-800">
                          Merge Summary
                        </h5>
                        <pre className="mt-2 text-xs text-green-700 whitespace-pre-wrap font-mono bg-green-50 p-2 rounded overflow-auto max-h-48">
                          {result.merge_summary}
                        </pre>
                      </div>
                    )}
                  </div>
                )}
              </>
            ) : (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <div className="bg-red-100 rounded-full p-2 mr-3">
                    <svg
                      className="h-6 w-6 text-red-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      ></path>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-red-800 text-base">
                      Merge Failed
                    </h4>
                    <p className="text-sm text-red-700 mt-1">
                      {batchMode
                        ? result.message ||
                          "There was an error merging some donor groups."
                        : "There was an error merging the donor records."}
                    </p>
                  </div>
                </div>
                <p className="text-xs text-red-600 mt-2">
                  {result.error?.details || "Unknown error occurred"}
                </p>
              </div>
            )}
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
              onClick={handleClose}
            >
              Close
            </button>
          </div>
        </>
      );
    }

    if (batchMode && allGroups && allGroups.length > 0) {
      return (
        <>
          <Dialog.Title
            as="h3"
            className="text-lg font-semibold leading-6 text-primary mb-4"
          >
            Confirm Batch Merge
          </Dialog.Title>

          <div className="mb-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-start mb-2">
                <svg
                  className="h-5 w-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <div>
                  <span className="font-semibold text-yellow-800 block mb-1">
                    You are about to merge {allGroups.length} donor groups
                  </span>
                  <p className="text-sm text-yellow-700">This action will:</p>
                  <ul className="text-sm text-yellow-700 list-disc ml-5 mt-1 space-y-1">
                    <li>Keep all information from each primary donor</li>
                    <li>
                      Transfer all donations and related records from the
                      duplicate donors to their respective primary donors
                    </li>
                    <li>
                      Delete the duplicate donor records after successful
                      transfer
                    </li>
                  </ul>
                  <p className="text-sm font-semibold text-yellow-800 mt-2">
                    This action cannot be easily undone.
                  </p>
                </div>
              </div>
            </div>

            <div className="max-h-80 overflow-y-auto border border-gray-200 rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Group Name
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Primary Donor
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Duplicates
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {allGroups.map((group, idx) => (
                    <tr key={idx}>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                        {group.groupName || `Group ${idx + 1}`}
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                        {group.primaryDonorName || group.primaryDonorId}
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                        {group.duplicateCount}{" "}
                        {group.duplicateCount === 1 ? "donor" : "donors"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 rounded-lg text-sm">
              {error}
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer disabled:cursor-not-allowed"
              onClick={handleClose}
              disabled={isProcessing}
            >
              Cancel
            </button>
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
              onClick={handleMerge}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Merging All Donors...
                </>
              ) : (
                "Merge All Groups"
              )}
            </button>
          </div>
        </>
      );
    }

    return (
      <>
        <Dialog.Title
          as="h3"
          className="text-lg font-semibold leading-6 text-primary mb-4"
        >
          Confirm Merge
          {isBulkOperation && totalGroups > 0 && (
            <span className="ml-2 text-sm font-normal text-gray-500">
              Group {currentGroupIndex + 1} of {totalGroups}
            </span>
          )}
        </Dialog.Title>

        <div className="mb-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <div className="flex items-start mb-2">
              <svg
                className="h-5 w-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              <div>
                {groupName && (
                  <span className="font-semibold text-yellow-800 block mb-1">
                    {groupName}
                  </span>
                )}
                <span className="font-semibold text-yellow-800 block mb-1">
                  You are about to merge {duplicateDonors.length} donor
                  {duplicateDonors.length !== 1 ? "s" : ""}
                </span>
                <p className="text-sm text-yellow-700">This action will:</p>
                <ul className="text-sm text-yellow-700 list-disc ml-5 mt-1 space-y-1">
                  <li>
                    Keep all information from the primary donor (
                    {primaryDonorName})
                  </li>
                  <li>
                    Transfer all donations and related records from the
                    duplicate donors to the primary donor
                  </li>
                  <li>
                    Delete the duplicate donor records after successful transfer
                  </li>
                </ul>
                <p className="text-sm font-semibold text-yellow-800 mt-2">
                  This action cannot be easily undone.
                </p>
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-800 rounded-lg text-sm">
            {error}
          </div>
        )}

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer disabled:cursor-not-allowed"
            onClick={handleClose}
            disabled={isProcessing}
          >
            Cancel
          </button>
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 cursor-pointer disabled:cursor-not-allowed"
            onClick={handleMerge}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Merging Donors...
              </>
            ) : (
              "Merge Donors"
            )}
          </button>
        </div>
      </>
    );
  };

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                {renderContent()}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}

