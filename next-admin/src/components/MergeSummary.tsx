'use client';

import { Donor } from '@/types';
import { useState, useEffect } from 'react';

interface MergeSummaryProps {
  primaryDonor: Donor;
  secondaryDonor: Donor;
}

interface DonorSummary {
  donationCount: number;
  totalAmount: number;
  lastDonationDate: string | null;
  subscriptionCount: number;
  recentDonations: {
    id: number;
    date: string;
    amount: string;
    type: string;
  }[];
}

export function MergeSummary({ primaryDonor, secondaryDonor }: MergeSummaryProps) {
  const [primarySummary, setPrimarySummary] = useState<DonorSummary | null>(null);
  const [secondarySummary, setSecondarySummary] = useState<DonorSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDonorSummaries = async () => {
      if (!primaryDonor || !secondaryDonor) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Use the new summary API endpoint
        const response = await fetch(
          `/api/merge-donors/summary?primaryDonorId=${primaryDonor.id}&secondaryDonorId=${secondaryDonor.id}`
        );
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to load donor summaries');
        }
        
        const data = await response.json();
        setPrimarySummary(data.primary);
        setSecondarySummary(data.secondary);
      } catch (err) {
        console.error('Error fetching donor summaries:', err);
        setError('Failed to load donor summaries. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDonorSummaries();
  }, [primaryDonor, secondaryDonor]);

  if (isLoading) {
    return <div className="text-center py-4">Loading donor summaries...</div>;
  }

  if (error) {
    return <div className="text-red-600 py-4">{error}</div>;
  }

  return (
    <div className="border rounded p-4 bg-gray-50 dark:bg-gray-800/50">
      <h3 className="text-lg font-semibold mb-4">Merge Summary</h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
        <div>
          <h4 className="font-medium">Primary Donor (Keep)</h4>
          <p className="text-sm">{primaryDonor.firstname} {primaryDonor.lastname} (ID: {primaryDonor.id})</p>
          {primarySummary && (
            <div className="mt-2 text-sm">
              <p>Donations: {primarySummary.donationCount}</p>
              <p>Total Amount: ${primarySummary.totalAmount.toFixed(2)}</p>
              {primarySummary.lastDonationDate && (
                <p>Last Donation: {new Date(primarySummary.lastDonationDate).toLocaleDateString()}</p>
              )}
              {primarySummary.subscriptionCount > 0 && (
                <p>Subscriptions: {primarySummary.subscriptionCount}</p>
              )}
            </div>
          )}
        </div>
        
        <div>
          <h4 className="font-medium">Secondary Donor (Merge)</h4>
          <p className="text-sm">{secondaryDonor.firstname} {secondaryDonor.lastname} (ID: {secondaryDonor.id})</p>
          {secondarySummary && (
            <div className="mt-2 text-sm">
              <p>Donations: {secondarySummary.donationCount}</p>
              <p>Total Amount: ${secondarySummary.totalAmount.toFixed(2)}</p>
              {secondarySummary.lastDonationDate && (
                <p>Last Donation: {new Date(secondarySummary.lastDonationDate).toLocaleDateString()}</p>
              )}
              {secondarySummary.subscriptionCount > 0 && (
                <p>Subscriptions: {secondarySummary.subscriptionCount}</p>
              )}
            </div>
          )}
        </div>
      </div>
      
      {secondarySummary && secondarySummary.recentDonations.length > 0 && (
        <div className="mb-4">
          <h4 className="font-medium mb-2">Recent Donations to be Merged</h4>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="bg-gray-100 dark:bg-gray-700">
                  <th className="p-2 text-left">Date</th>
                  <th className="p-2 text-left">Amount</th>
                  <th className="p-2 text-left">Type</th>
                </tr>
              </thead>
              <tbody>
                {secondarySummary.recentDonations.map((donation) => (
                  <tr key={donation.id} className="border-b dark:border-gray-700">
                    <td className="p-2">{new Date(donation.date).toLocaleDateString()}</td>
                    <td className="p-2">${parseFloat(donation.amount).toFixed(2)}</td>
                    <td className="p-2">{donation.type}</td>
                  </tr>
                ))}
                {secondarySummary.donationCount > secondarySummary.recentDonations.length && (
                  <tr>
                    <td colSpan={3} className="p-2 text-center italic">
                      + {secondarySummary.donationCount - secondarySummary.recentDonations.length} more donations
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
      
      <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-900 rounded text-sm">
        <p className="font-medium text-yellow-800 dark:text-yellow-200">After merge:</p>
        <ul className="list-disc list-inside mt-1 text-yellow-700 dark:text-yellow-300 space-y-1">
          <li>All donations from the secondary donor will be moved to the primary donor</li>
          <li>The primary donor's information (name, contact details, etc.) will be preserved</li>
          {secondarySummary && secondarySummary.subscriptionCount > 0 && (
            <li>All subscriptions from the secondary donor will be updated to the primary donor</li>
          )}
          <li>The secondary donor account will be deleted after successful merge</li>
          <li>This action cannot be undone</li>
        </ul>
      </div>
    </div>
  );
} 