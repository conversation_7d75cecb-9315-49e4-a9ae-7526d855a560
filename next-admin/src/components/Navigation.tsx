'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/lib/authContext';
import { useState, useEffect } from 'react';

export default function Navigation() {
  const pathname = usePathname();
  const { user, isAuthenticated, logout } = useAuth();
  const [isTestEnvironment, setIsTestEnvironment] = useState(false);
  
  // Check test environment on client side only to avoid hydration mismatch
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isTest = window.location.hostname === 'localhost' || 
                     window.location.hostname === '127.0.0.1' ||
                     window.location.hostname === 'pet-leopard-fully.ngrok-free.app';
      setIsTestEnvironment(isTest);
    }
  }, []);
  
  // Generate user initials for avatar
  const getUserInitials = () => {
    if (!user) return '';
    const firstInitial = user.firstname ? user.firstname[0] : '';
    const lastInitial = user.lastname ? user.lastname[0] : '';
    return (firstInitial + lastInitial).toUpperCase();
  };

  return (
    <nav className="sticky top-0 z-10 w-full border-b bg-background transition-theme backdrop-blur-sm bg-opacity-95">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <span className="font-bold text-xl mr-2 text-primary transition-theme">KPFA</span>
              <span className="text-md font-medium text-secondary transition-theme">Admin</span>
              {isTestEnvironment && (
                <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-yellow-500 text-black rounded-full animate-pulse">
                  TEST MODE
                </span>
              )}
            </Link>
            <div className="hidden md:flex ml-8 space-x-4">
              <Link
                href="/stripe-donors"
                className={`nav-link ${pathname.startsWith('/stripe-donors') ? 'text-primary font-medium' : 'text-secondary hover:text-primary'} transition-theme`}
              >
                Stripe Donors
              </Link>
              <Link
                href="/duplicate-donors"
                className={`nav-link ${pathname.startsWith('/duplicate-donors') ? 'text-primary font-medium' : 'text-secondary hover:text-primary'} transition-theme`}
              >
                Duplicates
              </Link>
              <Link
                href="/merged-groups"
                className={`nav-link ${pathname.startsWith('/merged-groups') ? 'text-primary font-medium' : 'text-secondary hover:text-primary'} transition-theme`}
              >
                Merge History
              </Link>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {isAuthenticated && user ? (
              <div className="flex items-center">
                <div className="flex items-center mr-3">
                  <div className="w-9 h-9 rounded-full bg-gray-100 text-gray-700 flex items-center justify-center text-base font-medium border border-gray-200 shadow-sm">
                    {getUserInitials()}
                  </div>
                </div>
                <button 
                  onClick={logout}
                  className="text-sm text-secondary hover:text-primary cursor-pointer"
                >
                  Logout
                </button>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </nav>
  );
} 