'use client';

import React, { useState } from 'react';
import { useLazyQuery, gql } from '@apollo/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, ExternalLink, Calendar, DollarSign, CreditCard, Loader2, Download, Filter, HelpCircle, Clock, BarChart3 } from 'lucide-react';
import { stripeDashboardApiFetch, searchDonors } from '@/lib/api';
import { ReconcilePaymentModal } from './ReconcilePaymentModal';
import { useAuth } from '@/lib/authContext';

// Simple tooltip component
const Tooltip = ({ children, content }: { children: React.ReactNode; content: string }) => {
  return (
    <div className="group relative inline-block">
      {children}
      <div className="invisible group-hover:visible absolute z-10 w-64 p-2 mt-1 text-sm text-white bg-gray-900 rounded-lg shadow-lg -translate-x-1/2 left-1/2">
        {content}
        <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
      </div>
    </div>
  );
};

// GraphQL query for payment reconciliation
const STRIPE_PAYMENT_RECONCILIATION = gql`
  query StripePaymentReconciliation($input: StripePaymentReconciliationInput!) {
    stripePaymentReconciliation(input: $input) {
      stripePayments {
        id
        amount
        currency
        status
        created
        description
        receiptUrl
        paymentMethod
        transactionId
        donorId
        donorName
        premiums
        hasPremiums
      }
      unmatchedPayments {
        id
        amount
        currency
        status
        created
        description
        receiptUrl
        paymentMethod
        transactionId
        donorId
        donorName
        premiums
        hasPremiums
      }
      totalStripePayments
      totalUnmatchedPayments
      dateRange
    }
  }
`;

interface StripePayment {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  description: string;
  receiptUrl: string;
  paymentMethod: string;
  transactionId?: string;
  donorId?: string;
  donorName?: string;
  premiums: string[];
  hasPremiums: boolean;
  isSubscription?: boolean;
}

interface StationAdminPayment {
  id: number;
  payment_id: string;
  customer_id: string;
  amount: number;
  status: string;
  date_created: string;
  method: string;
  donation_id: number;
}

interface StationAdminDonation {
  id: number;
  transaction_id: string;
  donor_id: number;
  amount: number;
  timestamp: string;
  type: string;
  source: string;
}

interface Premium {
  id: number;
  name: string;
  description: string;
  price: number;
  active: boolean;
}

export function PaymentReconciliation() {
  const { checkAuth, isAuthenticated, isLoading: authLoading, user } = useAuth();

  // Helper to redirect to Station Admin for authentication
  const redirectToAuth = () => {
    // Use the same logic as AuthRequired component
    const getStationAdminUrl = () => {
      if (typeof window !== 'undefined' && window.location.hostname === 'pet-leopard-fully.ngrok-free.app') {
        return 'https://pet-leopard-fully.ngrok-free.app';
      }
      return 'https://admin.kpfa.org';
    };

    window.location.href = getStationAdminUrl();
  };

  const [startDate, setStartDate] = useState(() => {
    const date = new Date();
    date.setDate(date.getDate() - 7); // Default to last 7 days
    return date.toISOString().split('T')[0];
  });

  const [endDate, setEndDate] = useState(() => {
    const date = new Date();
    return date.toISOString().split('T')[0];
  });

  const [stationAdminPayments, setStationAdminPayments] = useState<StationAdminPayment[]>([]);
  const [stationAdminDonations, setStationAdminDonations] = useState<StationAdminDonation[]>([]);
  const [unmatchedPayments, setUnmatchedPayments] = useState<StripePayment[]>([]);
  const [isLoadingSA, setIsLoadingSA] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [premiumsOnlyMode, setPremiumsOnlyMode] = useState(false);
  const [progressStage, setProgressStage] = useState<'idle' | 'loading' | 'complete'>('idle');
  const [premiums, setPremiums] = useState<Premium[]>([]);
  const [premiumsLoaded, setPremiumsLoaded] = useState(false);

  // Modal state
  const [isReconcileModalOpen, setIsReconcileModalOpen] = useState(false);
  const [selectedPaymentForReconcile, setSelectedPaymentForReconcile] = useState<StripePayment | null>(null);

  // GraphQL lazy query for Stripe payments - better for manual triggering
  const [fetchStripePayments, { data: stripeData, loading: stripeLoading }] = useLazyQuery(
    STRIPE_PAYMENT_RECONCILIATION
  );

  // Fetch premiums on component mount
  React.useEffect(() => {
    fetchPremiums();
  }, []);

  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDateRangeInfo = () => {
    if (!startDate || !endDate) return { days: 0, isLongRange: false };

    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const isLongRange = days > 30;

    return { days, isLongRange };
  };

  // Quick date range selection functions
  const setDateRange = (months: number) => {
    const end = new Date();
    const start = new Date();
    start.setMonth(start.getMonth() - months);

    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
  };

  const setCurrentMonth = () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1);
    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
  };

  const setPreviousMonth = () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const end = new Date(now.getFullYear(), now.getMonth(), 0);

    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
  };

  const setSpecificMonth = (monthIndex: number) => {
    const now = new Date();
    const start = new Date(now.getFullYear(), monthIndex, 1);
    const end = new Date(now.getFullYear(), monthIndex + 1, 0);

    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
  };

  // Fetch premiums for name lookup
  const fetchPremiums = async () => {
    if (premiumsLoaded) return;

    try {
      console.log('Fetching ALL premiums (including inactive) for name lookup...');
      // Fetch all premiums without the active=yes filter to include inactive premiums
      // that might still appear in Stripe metadata
      const response = await stripeDashboardApiFetch<{ records: any[] }>('/premiums');

      const formattedPremiums: Premium[] = [];

      (response.records || []).forEach((premium: any) => {
        // Add the main premium
        formattedPremiums.push({
          id: premium.id,
          name: premium.name,
          description: premium.description,
          price: premium.price,
          active: premium.active,
        });

        // Add any variants (like t-shirt sizes)
        if (premium.variants && premium.variants.variations) {
          premium.variants.variations.forEach((variant: any) => {
            formattedPremiums.push({
              id: variant.id,
              name: `${premium.name} - ${variant.name}`, // e.g., "T-Shirt - Medium"
              description: premium.description,
              price: premium.price,
              active: premium.active,
            });
          });
        }
      });

      setPremiums(formattedPremiums);
      setPremiumsLoaded(true);

      const mainPremiums = response.records?.length || 0;
      const totalPremiums = formattedPremiums.length;
      console.log(`Loaded ${totalPremiums} premiums for lookup (${mainPremiums} main + ${totalPremiums - mainPremiums} variants)`);

      // Debug: Check if the missing premium IDs are in our fetched data
      const testIds = ['224526', '224525', '224524', '224523'];
      console.log('🔍 Testing specific premium IDs (including variants):');
      testIds.forEach(id => {
        const found = formattedPremiums.find(p => p.id.toString() === id);
        if (found) {
          console.log(`✅ Found premium ${id}: "${found.name}"`);
        } else {
          console.log(`❌ Missing premium ${id} in API response`);
        }
      });

      // Show a sample of what we got, including some variants
      console.log('📋 Sample of fetched premiums (including variants):',
        formattedPremiums.slice(0, 10).map((p: Premium) => `${p.id}: ${p.name}`));
    } catch (error) {
      console.error('Error fetching premiums:', error);
      // Don't fail the whole component if premiums can't be loaded
    }
  };

  // Create premium lookup function
  const getPremiumName = (premiumId: string): string => {
    const premium = premiums.find(p => p.id.toString() === premiumId.toString());
    return premium ? premium.name : `Premium #${premiumId}`;
  };

  // Get premium names for an array of IDs
  const getPremiumNames = (premiumIds: string[]): string[] => {
    console.log(`🎁 Getting names for premium IDs:`, premiumIds);
    return premiumIds.map(id => getPremiumName(id));
  };

  // Get premium details including active status
  const getPremiumDetails = (premiumId: string): { name: string; active: boolean } => {
    const premium = premiums.find(p => p.id.toString() === premiumId.toString());
    return premium
      ? { name: premium.name, active: premium.active }
      : { name: `Premium #${premiumId}`, active: false };
  };

  // Get premium names with inactive status for display
  const getPremiumNamesWithStatus = (premiumIds: string[]): string[] => {
    return premiumIds.map(id => {
      const details = getPremiumDetails(id);
      return details.active ? details.name : `${details.name} (INACTIVE)`;
    });
  };

  // Check if any premiums in a payment are inactive
  const hasInactivePremiums = (premiumIds: string[]): boolean => {
    return premiumIds.some(id => {
      const details = getPremiumDetails(id);
      return !details.active;
    });
  };

  // Check if payment can be reconciled
  const canReconcilePayment = (payment: StripePayment): boolean => {
    // Must be succeeded
    if (payment.status !== 'succeeded') return false;

    // We'll handle donor ID lookup in the modal for both one-time and subscription payments
    // No need to check donor ID here - let users try to reconcile and handle it in the modal

    // If has premiums, all must be active
    if (payment.hasPremiums && hasInactivePremiums(payment.premiums)) return false;

    return true;
  };

  // Get reconcile button tooltip text
  const getReconcileTooltip = (payment: StripePayment): string => {
    if (payment.status !== 'succeeded') return 'Payment must be succeeded to reconcile';
    if (payment.hasPremiums && hasInactivePremiums(payment.premiums)) return 'Cannot reconcile - has inactive premiums';

    // Provide helpful tooltips based on payment type and available data
    if (payment.isSubscription === true) {
      return 'Reconcile subscription payment (will lookup donor from customer)';
    } else if (payment.donorId) {
      return 'Reconcile this payment';
    } else {
      return 'Reconcile payment (will lookup donor information)';
    }
  };

  // Modal handlers
  const handleOpenReconcileModal = (payment: StripePayment) => {
    setSelectedPaymentForReconcile(payment);
    setIsReconcileModalOpen(true);
  };

  const handleCloseReconcileModal = () => {
    setIsReconcileModalOpen(false);
    setSelectedPaymentForReconcile(null);
  };

  const handleReconcileSuccess = () => {
    // Refresh the data to show updated state
    handleReconcile();
    handleCloseReconcileModal();
  };

  const exportToCSV = () => {
    // If in premiums-only mode, we already have filtered data
    // If not, export all unmatched payments
    const filteredPayments = unmatchedPayments;

    const csvData = [
      // Header row
      ['Date', 'Name', 'Donor ID', 'Amount', 'Premium IDs', 'Premium Names', 'Stripe Charge ID', 'Description', 'Payment Method'],
      // Data rows
      ...filteredPayments.map((payment: StripePayment) => [
        new Date(payment.created * 1000).toLocaleDateString(),
        payment.donorName || '',
        payment.donorId || '',
        formatCurrency(payment.amount),
        payment.premiums.join(', '),
        getPremiumNamesWithStatus(payment.premiums).join(', '),
        payment.id,
        payment.description || '',
        payment.paymentMethod || ''
      ])
    ];

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `unmatched-payments-${startDate}-to-${endDate}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const fetchStationAdminData = async (stripeChargeIds?: string[]) => {
    setIsLoadingSA(true);
    setError(null);

    try {
      // Fetch both payments and donations for comprehensive reconciliation
      console.log('Fetching Station Admin payments and donations...');

      // If we have specific Stripe charge IDs, we could optimize the query
      // For now, we'll still fetch all and filter, but this could be enhanced

      // Fix timezone and date boundary issues:
      // 1. Stripe API converts dates to UTC timestamps, which can include payments from previous day in PST
      // 2. Station Admin API uses PST date comparison on datetime fields
      // 3. When endDate is '2025-01-31', Station Admin interprets as '2025-01-31 00:00:00 PST'
      //    but payments later in the day (e.g., 11:34 PM) won't match
      //
      // Solution: Expand Station Admin date range to account for timezone differences
      // - Start one day earlier to catch payments that might appear in Stripe due to UTC conversion
      // - End one day later to capture full end date
      const adjustedStartDate = new Date(startDate);
      adjustedStartDate.setDate(adjustedStartDate.getDate() - 1);
      const adjustedStartDateStr = adjustedStartDate.toISOString().split('T')[0];

      const adjustedEndDate = new Date(endDate);
      adjustedEndDate.setDate(adjustedEndDate.getDate() + 1);
      const adjustedEndDateStr = adjustedEndDate.toISOString().split('T')[0];

      console.log(`Date range adjustment for timezone consistency:`);
      console.log(`  Original: ${startDate} to ${endDate}`);
      console.log(`  Station Admin: ${adjustedStartDateStr} to ${adjustedEndDateStr}`);
      console.log(`  (Expanded to handle PST/UTC timezone differences)`);

      const [paymentsResponse, donationsResponse] = await Promise.all([
        stripeDashboardApiFetch<{ records: any[] }>(
          `/payments?start=${adjustedStartDateStr}&end=${adjustedEndDateStr}&status=succeeded`
        ),
        stripeDashboardApiFetch<{ records: any[] }>(
          `/donations?start=${adjustedStartDateStr}&end=${adjustedEndDateStr}`
        )
      ]);

      // Format payments for reconciliation - filter to only Stripe payments within original date range
      const formattedPayments = (paymentsResponse.records || [])
        .filter((payment: any) => {
          if (!payment.payment_id || payment.processor !== 'Stripe') return false;

          // Filter to original date range since we expanded the query range for timezone handling
          const paymentDate = new Date(payment.date_created).toISOString().split('T')[0];
          return paymentDate >= startDate && paymentDate <= endDate;
        })
        .map((payment: any) => ({
          id: payment.id,
          payment_id: payment.payment_id, // Stripe charge ID
          customer_id: payment.customer_id, // Stripe customer ID
          amount: parseFloat(payment.amount) * 100, // Convert to cents for comparison with Stripe
          status: payment.status,
          date_created: payment.date_created,
          method: payment.method,
          donation_id: payment.donation_id,
          processor: payment.processor,
        }));

      // Format donations for reconciliation
      // Only include donations that have corresponding Stripe payments (by donation_id relationship)
      const stripePaymentDonationIds = new Set(formattedPayments.map(p => p.donation_id));

      const formattedDonations = (donationsResponse.records || [])
        .filter((donation: any) => {
          if (!donation.transaction_id || !stripePaymentDonationIds.has(donation.id)) return false;

          // Filter to original date range since we expanded the query range for timezone handling
          const donationDate = new Date(donation.timestamp).toISOString().split('T')[0];
          return donationDate >= startDate && donationDate <= endDate;
        })
        .map((donation: any) => ({
          id: donation.id,
          transaction_id: donation.transaction_id, // Stripe Payment Intent ID
          donor_id: donation.donor_id,
          amount: parseFloat(donation.amount) * 100, // Convert to cents
          timestamp: donation.timestamp,
          type: donation.type,
          source: donation.source,
        }));

      console.log('=== STATION ADMIN DATA DEBUG ===');
      console.log('Date range used:', startDate, 'to', endDate);
      console.log('Total payments fetched:', paymentsResponse.records?.length || 0);
      console.log('Stripe payments filtered:', formattedPayments.length);
      console.log('Total donations fetched:', donationsResponse.records?.length || 0);
      console.log('Donations with transaction_id:', (donationsResponse.records || []).filter(d => d.transaction_id).length);
      console.log('Donations linked to Stripe payments:', formattedDonations.length);

      // Debug donation sources
      const donationSources = [...new Set((donationsResponse.records || []).map(d => d.source))];
      console.log('All donation sources found:', donationSources);

      // Debug payment processors
      const paymentProcessors = [...new Set((paymentsResponse.records || []).map(p => p.processor))];
      console.log('All payment processors found:', paymentProcessors);

      // Debug date ranges in the actual data
      const paymentDates = (paymentsResponse.records || []).map(p => p.date_created).slice(0, 5);
      console.log('Sample payment dates from SA:', paymentDates);

      // Check if SA is respecting date range
      const paymentsInRange = (paymentsResponse.records || []).filter(p => {
        const paymentDate = new Date(p.date_created).toISOString().split('T')[0];
        return paymentDate >= startDate && paymentDate <= endDate;
      });
      console.log('SA payments actually in date range:', paymentsInRange.length, 'vs total:', paymentsResponse.records?.length);

      // Check for duplicate payment_ids in Station Admin
      const paymentIds = (paymentsResponse.records || []).map(p => p.payment_id).filter(Boolean);
      const uniquePaymentIds = new Set(paymentIds);
      console.log('SA payment_ids: total =', paymentIds.length, 'unique =', uniquePaymentIds.size);
      if (paymentIds.length !== uniquePaymentIds.size) {
        console.warn('⚠️ DUPLICATE PAYMENT IDS FOUND IN STATION ADMIN!');
        const duplicates = paymentIds.filter((id, index) => paymentIds.indexOf(id) !== index);
        console.log('Duplicate payment_ids:', [...new Set(duplicates)]);
      }

      // Check for suspicious patterns
      const stripePaymentIds = paymentIds.filter(id => id.startsWith('ch_') || id.startsWith('py_'));
      console.log('Stripe-format payment_ids in SA:', stripePaymentIds.length, 'vs total Stripe payments:', paymentIds.length);

      setStationAdminPayments(formattedPayments);
      setStationAdminDonations(formattedDonations);
    } catch (error) {
      console.error('Error fetching Station Admin data:', error);

      // Handle authentication errors specifically
      if (error instanceof Error && error.message.includes('Authentication failed')) {
        setError('Station Admin authentication failed. Redirecting to login...');
        setTimeout(() => {
          redirectToAuth();
        }, 2000);
      } else {
        setError(error instanceof Error ? error.message : 'Failed to fetch Station Admin data. Please check your Station Admin authentication.');
      }
    } finally {
      setIsLoadingSA(false);
    }
  };

  const performReconciliation = async () => {
    setError(null);
    setProgressStage('loading');

    try {
      console.log('Fetching Stripe payments...');

      // Apply timezone-aware date adjustment to Stripe query as well
      // Stripe API converts dates to UTC, so we need to ensure consistency
      // Use the same date range as Station Admin to get matching results
      const stripeStartDate = new Date(startDate);
      stripeStartDate.setDate(stripeStartDate.getDate() - 1);
      const stripeStartDateStr = stripeStartDate.toISOString().split('T')[0];

      const stripeEndDate = new Date(endDate);
      stripeEndDate.setDate(stripeEndDate.getDate() + 1);
      const stripeEndDateStr = stripeEndDate.toISOString().split('T')[0];

      console.log(`Stripe query date adjustment:`);
      console.log(`  User selected: ${startDate} to ${endDate}`);
      console.log(`  Stripe query: ${stripeStartDateStr} to ${stripeEndDateStr}`);

      const stripeResult = await fetchStripePayments({
        variables: {
          input: {
            startDate: stripeStartDateStr,
            endDate: stripeEndDateStr,
            premiumsOnly: premiumsOnlyMode
          }
        }
      });

      await fetchStationAdminData();

      console.log('Processing and matching data...');
      // Small delay to show completion
      setTimeout(() => {
        setProgressStage('complete');
        setTimeout(() => setProgressStage('idle'), 1000);
      }, 300);

    } catch (error) {
      console.error('Reconciliation error:', error);

      // Handle authentication errors specifically
      if (error instanceof Error && error.message.includes('Authentication failed')) {
        setError('Station Admin authentication failed. Redirecting to login...');
        setTimeout(() => {
          redirectToAuth();
        }, 2000);
      } else {
        setError(error instanceof Error ? error.message : 'Reconciliation failed. Please check your Station Admin authentication.');
      }
      setProgressStage('idle');
    }
  };

  // Calculate unmatched payments when data changes - check both payments AND donations
  React.useEffect(() => {
    console.log('useEffect triggered');
    console.log('stripeData:', stripeData?.stripePaymentReconciliation);
    console.log('stationAdminPayments:', stationAdminPayments.length);
    console.log('stationAdminDonations:', stationAdminDonations.length);

    if (stripeData?.stripePaymentReconciliation?.stripePayments &&
        (stationAdminPayments.length > 0 || stationAdminDonations.length > 0)) {

      const stripePayments = stripeData.stripePaymentReconciliation.stripePayments;

      // Filter Stripe payments to only include those within the user-selected date range
      // Since we expanded the Stripe query for timezone handling, we need to filter back
      // IMPORTANT: Stripe timestamps are in UTC, but we need to filter by Pacific Time
      // to match what the user sees in the UI
      const filteredStripePayments = stripePayments.filter((stripePayment: StripePayment) => {
        if (stripePayment.status !== 'succeeded') return false;

        // Convert Stripe UTC timestamp to Pacific Time for accurate filtering
        const paymentDate = new Date(stripePayment.created * 1000);
        const pacificTimeStr = paymentDate.toLocaleDateString('en-CA', {
          timeZone: 'America/Los_Angeles'
        }); // Returns YYYY-MM-DD format in Pacific Time

        return pacificTimeStr >= startDate && pacificTimeStr <= endDate;
      });

      console.log(`Stripe payments filtered: ${stripePayments.length} -> ${filteredStripePayments.length} (within ${startDate} to ${endDate})`);

      // Create Sets for fast lookup
      const saPaymentIds = new Set(stationAdminPayments.map(p => p.payment_id));
      const saDonationTransactionIds = new Set(stationAdminDonations.map(d => d.transaction_id));

      // Debug the matching process
      console.log('Enhanced Matching Debug:');
      console.log('Total Stripe payments (raw):', stripePayments.length);
      console.log('Filtered Stripe payments (in date range):', filteredStripePayments.length);
      console.log('Station Admin payments with payment_id:', saPaymentIds.size);
      console.log('Station Admin donations with transaction_id:', saDonationTransactionIds.size);
      console.log('Sample SA payment_ids:', Array.from(saPaymentIds).slice(0, 5));
      console.log('Sample SA transaction_ids:', Array.from(saDonationTransactionIds).slice(0, 5));
      console.log('Sample filtered Stripe charge IDs:', filteredStripePayments.slice(0, 5).map((p: StripePayment) => p.id));

      // Debug specific payment matching for troubleshooting
      const debugChargeId = 'ch_3QcMPtDkFAcougr42LNqKtpm'; // Arthur Aravena example
      const debugTransactionId = '2da9587a2beb0ca0285246065e78b4c9';
      console.log(`Debug specific payment: ${debugChargeId}`);
      console.log('- Found in SA payments:', saPaymentIds.has(debugChargeId));
      console.log('- Found in SA donations:', saDonationTransactionIds.has(debugTransactionId));
      console.log('- In filtered Stripe results:', filteredStripePayments.some((p: StripePayment) => p.id === debugChargeId));

      // Find Stripe payments that don't have matching Station Admin records
      // A Stripe payment is considered "matched" if:
      // 1. It has a corresponding payment record (payment_id matches charge ID), OR
      // 2. It has a corresponding donation record (transaction_id from Stripe metadata matches SA donation)
      const unmatched = filteredStripePayments.filter((stripePayment: StripePayment) => {
        // Check if charge ID exists in payments table
        const hasPaymentRecord = saPaymentIds.has(stripePayment.id);

        // Check if Stripe metadata transaction_id exists in donations table
        const hasDonationRecord = stripePayment.transactionId &&
          saDonationTransactionIds.has(stripePayment.transactionId);

        const hasMatch = hasPaymentRecord || hasDonationRecord;

        return !hasMatch;
      });

      console.log('Unmatched payments (no payment OR donation record):', unmatched.length);
      console.log('Sample unmatched IDs:', unmatched.slice(0, 3).map((p: StripePayment) => p.id));
      console.log('🔍 DEBUGGING UNMATCHED PAYMENTS:');
      console.log('- Unmatched payment dates (UTC vs PST):', unmatched.slice(0, 3).map((p: StripePayment) => {
        const date = new Date(p.created * 1000);
        const utcDate = date.toISOString().split('T')[0];
        const pstDate = date.toLocaleDateString('en-CA', { timeZone: 'America/Los_Angeles' });
        return `${p.id}: UTC=${utcDate}, PST=${pstDate}`;
      }));
      console.log('- Expected date range (PST):', `${startDate} to ${endDate}`);
      setUnmatchedPayments(unmatched);
    } else if (stripeData?.stripePaymentReconciliation?.stripePayments &&
               stationAdminPayments.length === 0 && stationAdminDonations.length === 0) {
      // If we have Stripe data but no Station Admin data, all Stripe payments are unmatched
      const stripePayments = stripeData.stripePaymentReconciliation.stripePayments;

      // Apply the same date filtering here as well
      // IMPORTANT: Use Pacific Time conversion to match UI display
      const filteredStripePayments = stripePayments.filter((stripePayment: StripePayment) => {
        if (stripePayment.status !== 'succeeded') return false;

        // Convert Stripe UTC timestamp to Pacific Time for accurate filtering
        const paymentDate = new Date(stripePayment.created * 1000);
        const pacificTimeStr = paymentDate.toLocaleDateString('en-CA', {
          timeZone: 'America/Los_Angeles'
        }); // Returns YYYY-MM-DD format in Pacific Time

        return pacificTimeStr >= startDate && pacificTimeStr <= endDate;
      });

      console.log('No SA data, setting filtered Stripe as unmatched:', filteredStripePayments.length);
      setUnmatchedPayments(filteredStripePayments);
    }
  }, [stripeData, stationAdminPayments, stationAdminDonations]);

  const isLoading = stripeLoading || isLoadingSA || progressStage !== 'idle';
  const totalUnmatched = unmatchedPayments.length;
  const totalUnmatchedAmount = unmatchedPayments.reduce((sum, payment) => sum + payment.amount, 0);

  // Show auth warning but don't block the interface completely
  const showAuthWarning = !isAuthenticated || !user || user.access_level !== 'Admin';
  const showAuthLoading = authLoading;

  return (
    <div className="space-y-6">
      {/* Auth Status Warning (non-blocking) */}
      {showAuthLoading && (
        <Card>
          <CardContent className="p-4 bg-blue-50 border-blue-200">
            <div className="flex items-center">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600 mr-3" />
              <div>
                <h4 className="text-blue-800 font-medium">Verifying Station Admin Authentication</h4>
                <p className="text-blue-700 text-sm">Please wait while we validate your credentials...</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {showAuthWarning && !showAuthLoading && (
        <Card>
          <CardContent className="p-4 bg-yellow-50 border-yellow-200">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3 mt-0.5" />
              <div>
                <h4 className="text-yellow-800 font-medium">Station Admin Authentication Required</h4>
                <p className="text-yellow-700 text-sm mb-2">
                  You must be logged in to Station Admin with Administrator privileges to use reconciliation features.
                </p>
                <Button
                  onClick={redirectToAuth}
                  size="sm"
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  Log in to Station Admin
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Date Range Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Payment Reconciliation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1 text-gray-700">Start Date</label>
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                disabled={isLoading}
                className="cursor-pointer hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200
                         transition-all duration-150 ease-out disabled:cursor-not-allowed"
              />
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1 text-gray-700">End Date</label>
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                disabled={isLoading}
                className="cursor-pointer hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200
                         transition-all duration-150 ease-out disabled:cursor-not-allowed"
              />
            </div>
            <Button
              onClick={performReconciliation}
              disabled={isLoading || !startDate || !endDate || showAuthWarning}
              className="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800
                         text-white font-semibold rounded-lg shadow-lg hover:shadow-xl
                         transform hover:scale-[1.02] transition-all duration-200 ease-out
                         disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
                         focus:ring-4 focus:ring-blue-300 focus:outline-none cursor-pointer"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Analyzing Payments...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Run Reconciliation</span>
                </div>
              )}
            </Button>
          </div>

          {/* Month Selection */}
          <div className="flex flex-wrap items-center gap-2 p-3 bg-gray-50 rounded-lg border">
            <span className="text-sm font-medium text-gray-700">{new Date().getFullYear()}:</span>

            {/* Current Year Months */}
            {(() => {
              const currentMonth = new Date().getMonth(); // 0-based (0 = January)
              const monthNames = [
                'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
              ];

              // Only show months up to and including the current month
              return monthNames.slice(0, currentMonth + 1).map((month, index) => (
                <Button
                  key={month}
                  onClick={() => setSpecificMonth(index)}
                  variant="outline"
                  size="sm"
                  className="text-xs cursor-pointer hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700
                           transition-all duration-150 ease-out hover:shadow-md transform hover:scale-105"
                >
                  {month}
                </Button>
              ));
            })()}

            {/* Separator */}
            <div className="h-4 w-px bg-gray-300 mx-2"></div>

            {/* Premium Mode Toggle */}
            <div className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-150 cursor-pointer">
              <input
                type="checkbox"
                id="premiums-only"
                checked={premiumsOnlyMode}
                onChange={(e) => setPremiumsOnlyMode(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2 cursor-pointer
                           hover:border-blue-400 transition-colors duration-150"
              />
              <label htmlFor="premiums-only" className="text-sm font-medium text-gray-700 cursor-pointer
                                                        hover:text-blue-700 transition-colors duration-150">
                Premium mode
              </label>
              <Tooltip content="When enabled, only fetches and displays Stripe payments that have premiums (thank-you gifts). This is much faster and more efficient for premium fulfillment workflows.">
                <HelpCircle className="h-3 w-3 text-gray-400 hover:text-blue-500 transition-colors duration-150 cursor-help" />
              </Tooltip>
            </div>
          </div>



          {/* Date Range Warning - Only show for long ranges */}
          {(() => {
            const { days, isLongRange } = getDateRangeInfo();
            if (!isLongRange) return null;

            return (
              <div className="p-4 rounded-lg border bg-amber-50 border-amber-200 text-amber-800">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-amber-600" />
                  <span className="font-medium">
                    Long date range ({days} days)
                  </span>
                </div>
                <p className="text-sm mt-2">
                  ⚠️ <strong>This query may take several minutes to complete.</strong>
                  Consider using "Premium-only" mode for faster results if you're focusing on premium fulfillment.
                </p>
              </div>
            );
          })()}

          {/* Filter and Export Controls */}
          {unmatchedPayments.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  {unmatchedPayments.length} unmatched payment{unmatchedPayments.length !== 1 ? 's' : ''}
                  {premiumsOnlyMode && ' with premiums'}
                </span>
              </div>
              <Tooltip content="Download a CSV file containing all unmatched payment details including Stripe charge IDs, amounts, dates, transaction IDs, donor IDs, and premium information. Useful for manual reconciliation and premium fulfillment workflows.">
                <Button
                  onClick={exportToCSV}
                  variant="outline"
                  className="flex items-center space-x-2 cursor-pointer hover:bg-green-50 hover:border-green-300
                           hover:text-green-700 transition-all duration-150 ease-out hover:shadow-md
                           transform hover:scale-105"
                >
                  <Download className="h-4 w-4" />
                  <span>Export CSV</span>
                </Button>
              </Tooltip>
            </div>
          )}

          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-red-800 font-medium mb-1">
                    {error.includes('Authentication failed') ? 'Authentication Error' : 'Error'}
                  </h4>
                  <p className="text-red-700 text-sm">{error}</p>
                  {error.includes('Authentication failed') && (
                    <p className="text-red-600 text-xs mt-2">
                      You will be redirected to Station Admin to log in again.
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Progress Indicator */}
      {progressStage === 'loading' && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <div className="text-center">
                <p className="text-sm text-gray-600">Loading payment data...</p>
                {/* Continuous progress bar */}
                <div className="w-48 bg-gray-200 rounded-full h-1 mt-2 overflow-hidden">
                  <div
                    className="bg-blue-500 h-1 rounded-full"
                    style={{
                      width: '5%',
                      animation: 'progress-infinite 60s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards'
                    }}
                  ></div>
                </div>
                <style dangerouslySetInnerHTML={{
                  __html: `
                    @keyframes progress-infinite {
                      0% { width: 5%; }
                      10% { width: 25%; }
                      20% { width: 40%; }
                      30% { width: 52%; }
                      40% { width: 62%; }
                      50% { width: 70%; }
                      60% { width: 76%; }
                      70% { width: 81%; }
                      80% { width: 85%; }
                      90% { width: 88%; }
                      100% { width: 90%; }
                    }
                  `
                }} />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {progressStage === 'complete' && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-center space-x-2 text-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">Analysis complete</span>
            </div>
          </CardContent>
        </Card>
      )}



      {/* Unmatched Payments List */}
      {unmatchedPayments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Unmatched Stripe Payments ({unmatchedPayments.length})
              {premiumsOnlyMode && <Badge variant="outline">Premium Mode</Badge>}
            </CardTitle>
            <p className="text-sm text-gray-600">
              These Stripe payments don't have corresponding payment or donation records in Station Admin
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {unmatchedPayments.map((payment) => (
                <div key={payment.id} className="border rounded-lg p-4 bg-red-50">
                  <div className="flex justify-between items-start">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="destructive">Unmatched</Badge>
                        {payment.hasPremiums && <Badge variant="secondary">Has Premiums</Badge>}
                        {payment.hasPremiums && hasInactivePremiums(payment.premiums) && (
                          <Badge variant="outline" className="border-orange-500 text-orange-700 bg-orange-50">
                            Inactive Premium
                          </Badge>
                        )}
                        <span className="font-mono text-sm">{payment.id}</span>
                      </div>
                      <p className="text-sm text-gray-600">{payment.description}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>{formatDate(payment.created)}</span>
                        <span>Method: {payment.paymentMethod}</span>
                        {payment.transactionId && <span>TX: {payment.transactionId}</span>}
                        {payment.donorId && <span>Donor: {payment.donorId}</span>}
                      </div>
                      {payment.hasPremiums && payment.premiums.length > 0 && (
                        <div className="text-sm space-y-1">
                          <div className="text-purple-600">
                            <strong>Premium IDs:</strong> {payment.premiums.join(', ')}
                          </div>
                          <div className="text-purple-700">
                            <strong>Premium Names:</strong> {getPremiumNamesWithStatus(payment.premiums).join(', ')}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="text-right space-y-2">
                      <p className="text-lg font-semibold">
                        {formatCurrency(payment.amount, payment.currency)}
                      </p>
                      {payment.receiptUrl && (
                        <div>
                          <a
                            href={payment.receiptUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800
                                     cursor-pointer hover:bg-blue-50 px-2 py-1 rounded transition-all duration-150
                                     ease-out hover:shadow-sm transform hover:scale-105"
                          >
                            View Receipt <ExternalLink className="h-3 w-3" />
                          </a>
                        </div>
                      )}
                      <div className="flex justify-end">
                        {canReconcilePayment(payment) ? (
                          <Tooltip content={getReconcileTooltip(payment)}>
                            <Button
                              onClick={() => handleOpenReconcileModal(payment)}
                              size="sm"
                              className="bg-green-600 hover:bg-green-700 text-white cursor-pointer"
                            >
                              Reconcile
                            </Button>
                          </Tooltip>
                        ) : (
                          <Tooltip content={getReconcileTooltip(payment)}>
                            <Button
                              disabled
                              size="sm"
                              className="bg-gray-300 text-gray-500 cursor-not-allowed"
                            >
                              Reconcile
                            </Button>
                          </Tooltip>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Results State */}
      {!isLoading && stripeData?.stripePaymentReconciliation && unmatchedPayments.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-green-600 mb-4">
              <DollarSign className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-green-800 mb-2">All Payments Matched!</h3>
            <p className="text-gray-600">
              All Stripe payments for the selected date range have corresponding records in Station Admin.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Reconcile Payment Modal */}
      {selectedPaymentForReconcile && (
        <ReconcilePaymentModal
          isOpen={isReconcileModalOpen}
          onClose={handleCloseReconcileModal}
          payment={selectedPaymentForReconcile}
          premiums={premiums}
          onReconcileSuccess={handleReconcileSuccess}
        />
      )}
    </div>
  );
}
