'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { getEnvironmentInfo } from '@/lib/testMode';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CreditCard, DollarSign, Calendar, Package, Loader2, CheckCircle, XCircle } from 'lucide-react';

interface StripePayment {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  description: string;
  receiptUrl: string;
  paymentMethod: string;
  transactionId?: string;
  donorId?: string;
  donorName?: string;
  premiums: string[];
  hasPremiums: boolean;
  isSubscription?: boolean;
}



interface Premium {
  id: number;
  name: string;
  description: string;
  price: number;
  active: boolean;
}

interface ReconcilePaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  payment: StripePayment;
  premiums: Premium[];
  onReconcileSuccess: () => void;
}

type ModalStep = 'confirmation' | 'processing' | 'success' | 'error';

export function ReconcilePaymentModal({
  isOpen,
  onClose,
  payment,
  premiums,
  onReconcileSuccess
}: ReconcilePaymentModalProps) {
  const [currentStep, setCurrentStep] = useState<ModalStep>('confirmation');
  const [comments, setComments] = useState('');
  const [isReconciling, setIsReconciling] = useState(false);
  const [isLoadingCustomer, setIsLoadingCustomer] = useState(false);
  const [customerDonorId, setCustomerDonorId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [reconcileResult, setReconcileResult] = useState<any>(null);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep('confirmation');
      setComments('');
      setError(null);
      setReconcileResult(null);
      setCustomerDonorId(null);
      setIsLoadingCustomer(false);
    }
  }, [isOpen]);

  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Check if payment has donor ID (backend will validate donor exists)
  const canReconcile = () => {
    // For one-time payments: check payment metadata
    if (payment.isSubscription !== true) {
      return !!payment.donorId;
    }

    // For subscription payments: check if we've loaded customer donor ID
    return !!customerDonorId;
  };

  // Check if we need to show a warning about missing donor info
  const needsDonorLookup = () => {
    if (payment.isSubscription === true) {
      return !customerDonorId;
    }
    return !payment.donorId;
  };

  // Get the donor ID to use for reconciliation
  const getDonorId = () => {
    if (payment.isSubscription === true) {
      return customerDonorId;
    }
    return payment.donorId;
  };

  // Fetch customer data to get donor ID for subscription payments
  const fetchCustomerDonorId = async () => {
    setIsLoadingCustomer(true);
    setError(null);

    try {
      // Use the new subscription donor lookup endpoint
      const response = await fetch(`/api/stripe-subscription-donor/${payment.id}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch donor information');
      }

      if (result.donorId) {
        setCustomerDonorId(result.donorId);
        console.log(`✅ Found donor ID for subscription payment: ${result.donorId} (source: ${result.source})`);
      } else {
        setError('Unable to find donor ID for this subscription payment');
      }
    } catch (err) {
      console.error('Error fetching subscription donor data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch donor information');
    } finally {
      setIsLoadingCustomer(false);
    }
  };

  const handleReconcile = async () => {
    // For subscription payments, fetch customer donor ID first if not already loaded
    if (payment.isSubscription === true && !customerDonorId) {
      await fetchCustomerDonorId();
      return; // Let user click reconcile again after customer data is loaded
    }

    if (!canReconcile()) {
      setError('Payment missing donor ID');
      return;
    }

    setCurrentStep('processing');
    setIsReconciling(true);
    setError(null);

    // Log environment info for debugging
    const envInfo = getEnvironmentInfo();
    const donorId = getDonorId();
    console.log('🔄 Starting payment reconciliation:', {
      environment: envInfo,
      stripeChargeId: payment.id,
      donorId: donorId,
      donorName: payment.donorName || 'Not provided',
      isSubscription: payment.isSubscription === true,
      donorSource: payment.isSubscription === true ? 'customer_metadata' : 'payment_metadata'
    });

    try {
      const response = await fetch('/api/reconcile-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stripeChargeId: payment.id,
          donorId: parseInt(donorId!),
          comments: comments.trim() || undefined
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Reconciliation failed');
      }

      setReconcileResult(result);
      setCurrentStep('success');

      // Call success callback after a short delay to show success state
      setTimeout(() => {
        onReconcileSuccess();
      }, 2000);

    } catch (err) {
      console.error('Reconciliation error:', err);
      setError(err instanceof Error ? err.message : 'Reconciliation failed');
      setCurrentStep('error');
    } finally {
      setIsReconciling(false);
    }
  };

  const handleClose = () => {
    if (currentStep === 'processing') {
      return; // Don't allow closing during processing
    }
    onClose();
  };

  const handleBackToConfirmation = () => {
    setCurrentStep('confirmation');
    setError(null);
  };

  // Get premium details for display
  const getPaymentPremiums = () => {
    if (!payment.hasPremiums || !payment.premiums.length) return [];
    
    return payment.premiums.map(premiumId => {
      const premium = premiums.find(p => p.id.toString() === premiumId);
      return premium || { id: parseInt(premiumId), name: `Premium ${premiumId}`, active: false };
    });
  };

  const paymentPremiums = getPaymentPremiums();
  const hasInactivePremiums = paymentPremiums.some(p => !p.active);

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 mb-4">
                  Reconcile Stripe Payment
                </Dialog.Title>

                {/* Payment Details Section */}
                <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Payment Details
                  </h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Charge ID:</span>
                      <span className="ml-2 font-mono">{payment.id}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Amount:</span>
                      <span className="ml-2 font-semibold">{formatCurrency(payment.amount, payment.currency)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Date:</span>
                      <span className="ml-2">{formatDate(payment.created)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Method:</span>
                      <span className="ml-2">{payment.paymentMethod}</span>
                    </div>
                    {payment.description && (
                      <div className="col-span-2">
                        <span className="text-gray-600">Description:</span>
                        <span className="ml-2">{payment.description}</span>
                      </div>
                    )}
                  </div>

                  {/* Premium Information */}
                  {payment.hasPremiums && paymentPremiums.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-blue-200">
                      <h5 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                        <Package className="h-4 w-4" />
                        Premiums ({paymentPremiums.length})
                      </h5>
                      <div className="flex flex-wrap gap-2">
                        {paymentPremiums.map((premium) => (
                          <Badge
                            key={premium.id}
                            variant={premium.active ? "default" : "destructive"}
                            className="text-xs"
                          >
                            {premium.name}
                            {!premium.active && " (INACTIVE)"}
                          </Badge>
                        ))}
                      </div>
                      {hasInactivePremiums && (
                        <div className="mt-2 p-2 bg-orange-100 border border-orange-300 rounded text-sm text-orange-800">
                          <AlertTriangle className="h-4 w-4 inline mr-1" />
                          Warning: This payment contains inactive premiums
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Step Content */}
                {currentStep === 'confirmation' && (
                  <ConfirmationStep
                    payment={payment}
                    comments={comments}
                    setComments={setComments}
                    paymentPremiums={paymentPremiums}
                    canReconcile={canReconcile()}
                    customerDonorId={customerDonorId}
                    isLoadingCustomer={isLoadingCustomer}
                    error={error}
                    onReconcile={handleReconcile}
                    onClose={handleClose}
                  />
                )}

                {currentStep === 'processing' && (
                  <ProcessingStep />
                )}

                {currentStep === 'success' && (
                  <SuccessStep
                    result={reconcileResult}
                    onClose={handleClose}
                  />
                )}

                {currentStep === 'error' && (
                  <ErrorStep
                    error={error!}
                    onRetry={handleBackToConfirmation}
                    onClose={handleClose}
                  />
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}

// Step Components

interface ConfirmationStepProps {
  payment: StripePayment;
  comments: string;
  setComments: (comments: string) => void;
  paymentPremiums: Premium[];
  canReconcile: boolean;
  customerDonorId: string | null;
  isLoadingCustomer: boolean;
  error: string | null;
  onReconcile: () => void;
  onClose: () => void;
}

function ConfirmationStep({
  payment,
  comments,
  setComments,
  paymentPremiums,
  canReconcile,
  customerDonorId,
  isLoadingCustomer,
  error,
  onReconcile,
  onClose
}: ConfirmationStepProps) {
  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  // Check if we need to show a warning about missing donor info
  const needsDonorLookup = () => {
    if (payment.isSubscription === true) {
      return !customerDonorId;
    }
    return !payment.donorId;
  };

  return (
    <div className="space-y-4">
      <div>
        <h4 className="font-medium text-gray-900 mb-2">
          Confirm Payment Reconciliation
        </h4>
        <p className="text-sm text-gray-600 mb-4">
          This will create Station Admin records for the following Stripe payment:
        </p>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center">
              <XCircle className="h-4 w-4 text-red-500 mr-2" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          </div>
        )}

        {/* Missing Donor ID Info */}
        {needsDonorLookup() && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm text-blue-800">
                {payment.isSubscription === true
                  ? 'This subscription payment requires donor lookup from Stripe subscription metadata.'
                  : 'This payment is missing donor information in Stripe metadata.'
                }
              </span>
            </div>
          </div>
        )}

        {/* Reconciliation Summary */}
        <div className="space-y-4">
          {/* Stripe Payment Summary */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <h5 className="font-medium text-blue-900 mb-2">Stripe Payment</h5>
            <div className="text-sm space-y-1">
              <div>Charge ID: <span className="font-mono">{payment.id}</span></div>
              <div>Amount: <span className="font-semibold">{formatCurrency(payment.amount, payment.currency)}</span></div>
              {payment.description && <div>Description: {payment.description}</div>}
            </div>
          </div>

          {/* Donor Information */}
          {payment.isSubscription === true ? (
            // Subscription payment - show customer lookup status
            <div className={`p-3 border rounded-md ${
              customerDonorId
                ? 'bg-green-50 border-green-200'
                : isLoadingCustomer
                  ? 'bg-blue-50 border-blue-200'
                  : 'bg-gray-50 border-gray-200'
            }`}>
              <h5 className={`font-medium mb-2 ${
                customerDonorId
                  ? 'text-green-900'
                  : isLoadingCustomer
                    ? 'text-blue-900'
                    : 'text-gray-900'
              }`}>
                Station Admin Donor {isLoadingCustomer && '(Loading...)'}
              </h5>
              <div className="text-sm space-y-1">
                {customerDonorId ? (
                  <>
                    <div>Donor ID: <span className="font-mono">{customerDonorId}</span></div>
                    <div className="text-xs text-green-700">Found in Stripe subscription metadata</div>
                  </>
                ) : isLoadingCustomer ? (
                  <div className="flex items-center text-blue-700">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Looking up: charge → subscription → donor ID in metadata...
                  </div>
                ) : (
                  <div className="text-gray-600">
                    Click "Lookup Donor ID" to find donor ID in subscription metadata
                  </div>
                )}
              </div>
            </div>
          ) : canReconcile ? (
            // One-time payment with donor ID
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <h5 className="font-medium text-green-900 mb-2">Station Admin Donor</h5>
              <div className="text-sm space-y-1">
                <div>Donor ID: <span className="font-mono">{payment.donorId}</span></div>
                {payment.donorName && <div>Name: <span className="font-semibold">{payment.donorName}</span></div>}
                <div className="text-xs text-green-700">From Stripe payment metadata</div>
              </div>
            </div>
          ) : (
            // One-time payment missing donor ID
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <h5 className="font-medium text-yellow-900 mb-2">Donor Information</h5>
              <div className="text-sm text-yellow-800">
                No donor ID available in Stripe payment metadata. This payment cannot be automatically reconciled.
              </div>
              <div className="text-xs text-yellow-700 mt-1">
                Manual reconciliation may be required through Station Admin interface.
              </div>
            </div>
          )}

          {/* Premium Summary */}
          {paymentPremiums.length > 0 && (
            <div className="p-3 bg-purple-50 border border-purple-200 rounded-md">
              <h5 className="font-medium text-purple-900 mb-2">Premiums ({paymentPremiums.length})</h5>
              <div className="flex flex-wrap gap-2">
                {paymentPremiums.map((premium) => (
                  <Badge
                    key={premium.id}
                    variant={premium.active ? "default" : "destructive"}
                    className="text-xs"
                  >
                    {premium.name}
                    {!premium.active && " (INACTIVE)"}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Comments Field */}
          <div>
            <label htmlFor="reconcile-comments" className="block text-sm font-medium text-gray-700 mb-2">
              Comments (Optional)
            </label>
            <textarea
              id="reconcile-comments"
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              placeholder="Add any notes about this reconciliation..."
              className="w-full p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
            />
            <p className="text-xs text-gray-500 mt-1">
              These comments will be added to the donation record for audit purposes.
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button
          onClick={onClose}
          variant="outline"
          className="cursor-pointer"
        >
          Cancel
        </Button>
        <Button
          onClick={onReconcile}
          disabled={(!canReconcile && !isLoadingCustomer) || (payment.isSubscription !== true && !payment.donorId)}
          className="bg-green-600 hover:bg-green-700 text-white cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
        >
          {isLoadingCustomer ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Loading Customer...
            </>
          ) : payment.isSubscription === true && !customerDonorId ? (
            'Lookup Donor ID'
          ) : !canReconcile && payment.isSubscription !== true ? (
            'Cannot Reconcile'
          ) : (
            'Reconcile Payment'
          )}
        </Button>
      </div>
    </div>
  );
}

function ProcessingStep() {
  return (
    <div className="text-center py-8">
      <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
      <h4 className="font-medium text-gray-900 mb-2">Processing Reconciliation</h4>
      <p className="text-sm text-gray-600">
        Please wait while we reconcile the payment with Station Admin...
      </p>
    </div>
  );
}

interface SuccessStepProps {
  result: any;
  onClose: () => void;
}

function SuccessStep({ result, onClose }: SuccessStepProps) {
  return (
    <div className="text-center py-8">
      <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
      <h4 className="font-medium text-gray-900 mb-2">Reconciliation Successful!</h4>
      <p className="text-sm text-gray-600 mb-6">
        The Stripe payment has been successfully reconciled with Station Admin.
      </p>

      {result && (
        <div className="text-left bg-green-50 border border-green-200 rounded-md p-4 mb-6">
          <h5 className="font-medium text-green-900 mb-2">Created Records:</h5>
          <div className="text-sm text-green-800 space-y-1">
            {result.donation_id && <div>Donation ID: {result.donation_id}</div>}
            {result.payment_id && <div>Payment ID: {result.payment_id}</div>}
            {result.transaction_id && <div>Transaction ID: {result.transaction_id}</div>}
            {result.shipments_created && (
              <div>
                Shipments Created: {result.shipments_created}
                {result.premium_ids && (
                  <span className="text-xs text-green-700 ml-1">
                    (Premium IDs: {result.premium_ids.join(', ')})
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      <Button
        onClick={onClose}
        className="bg-green-600 hover:bg-green-700 text-white cursor-pointer"
      >
        Close
      </Button>
    </div>
  );
}

interface ErrorStepProps {
  error: string;
  onRetry: () => void;
  onClose: () => void;
}

function ErrorStep({ error, onRetry, onClose }: ErrorStepProps) {
  return (
    <div className="text-center py-8">
      <XCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
      <h4 className="font-medium text-gray-900 mb-2">Reconciliation Failed</h4>
      <p className="text-sm text-gray-600 mb-4">
        An error occurred while reconciling the payment:
      </p>

      <div className="text-left bg-red-50 border border-red-200 rounded-md p-4 mb-6">
        <div className="text-sm text-red-800">{error}</div>
      </div>

      <div className="flex justify-center space-x-3">
        <Button
          onClick={onClose}
          variant="outline"
          className="cursor-pointer"
        >
          Close
        </Button>
        <Button
          onClick={onRetry}
          className="bg-blue-600 hover:bg-blue-700 text-white cursor-pointer"
        >
          Try Again
        </Button>
      </div>
    </div>
  );
}
