'use client';

import { StripeCustomer } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface StripeCustomerCardProps {
  customer: StripeCustomer;
  onViewDetails: (customer: StripeCustomer) => void;
}

export function StripeCustomerCard({ customer, onViewDetails }: StripeCustomerCardProps) {
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString();
  };

  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center justify-between">
          <span>{customer.name || customer.email || 'Unnamed Customer'}</span>
          <span className="text-sm font-normal text-muted-foreground">
            {customer.id}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-muted-foreground">Email:</span>
            <p className="truncate">{customer.email || 'No email'}</p>
          </div>
          <div>
            <span className="font-medium text-muted-foreground">Phone:</span>
            <p>{customer.phone || 'No phone'}</p>
          </div>
          <div>
            <span className="font-medium text-muted-foreground">Created:</span>
            <p>{formatDate(customer.created)}</p>
          </div>
          <div>
            <span className="font-medium text-muted-foreground">Balance:</span>
            <p className={customer.balance > 0 ? 'text-green-600' : customer.balance < 0 ? 'text-red-600' : ''}>
              {formatCurrency(customer.balance, customer.currency)}
            </p>
          </div>
        </div>
        
        {customer.description && (
          <div>
            <span className="font-medium text-muted-foreground text-sm">Description:</span>
            <p className="text-sm text-gray-600 mt-1">
              {customer.description}
            </p>
          </div>
        )}

        <div className="flex justify-end pt-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => onViewDetails(customer)}
          >
            View Details
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
