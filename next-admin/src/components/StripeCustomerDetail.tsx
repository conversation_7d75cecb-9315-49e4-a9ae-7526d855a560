'use client';

import { useQuery, gql } from '@apollo/client';
import { StripeCustomerReport, StripeCustomer } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

// GraphQL query for comprehensive customer report
const GET_STRIPE_CUSTOMER_REPORT = gql`
  query GetStripeCustomerReport($customerId: String!) {
    stripeCustomerReport(customerId: $customerId) {
      customer {
        id
        email
        name
        phone
        created
        currency
        balance
        description
        metadata
      }
      paymentMethods {
        id
        type
        created
        card {
          brand
          last4
          expMonth
          expYear
        }
      }
      subscriptions {
        id
        status
        currentPeriodStart
        currentPeriodEnd
        amount
        currency
        interval
        intervalCount
      }
      charges {
        id
        amount
        currency
        status
        created
        description
        receiptUrl
        paymentMethod
      }
      totalSpent
      lastPaymentDate
    }
  }
`;

interface StripeCustomerDetailProps {
  customerId: string;
  onClose: () => void;
}

export function StripeCustomerDetail({ customerId, onClose }: StripeCustomerDetailProps) {
  const { data, loading, error } = useQuery(GET_STRIPE_CUSTOMER_REPORT, {
    variables: { customerId },
    errorPolicy: 'all',
  });

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
      case 'active':
        return 'text-green-600 bg-green-50';
      case 'pending':
      case 'trialing':
        return 'text-yellow-600 bg-yellow-50';
      case 'failed':
      case 'canceled':
      case 'incomplete':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-4xl max-h-[90vh] overflow-auto">
          <CardContent className="p-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3">Loading customer details...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-4xl max-h-[90vh] overflow-auto">
          <CardHeader>
            <CardTitle className="flex justify-between items-center">
              Error Loading Customer
              <Button variant="outline" onClick={onClose}>Close</Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">Failed to load customer details: {error.message}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const report: StripeCustomerReport = data?.stripeCustomerReport;
  if (!report) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-4xl max-h-[90vh] overflow-auto">
          <CardHeader>
            <CardTitle className="flex justify-between items-center">
              Customer Not Found
              <Button variant="outline" onClick={onClose}>Close</Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>No customer found with ID: {customerId}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { customer, paymentMethods, subscriptions, charges, totalSpent, lastPaymentDate } = report;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-auto">
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl">{customer.name || customer.email || 'Unnamed Customer'}</h2>
              <p className="text-sm text-muted-foreground">{customer.id}</p>
            </div>
            <Button variant="outline" onClick={onClose}>Close</Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Customer Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(totalSpent * 100, customer.currency)}
                </div>
                <p className="text-sm text-muted-foreground">Total Spent</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">
                  {charges.filter(c => c.status === 'succeeded').length}
                </div>
                <p className="text-sm text-muted-foreground">Successful Payments</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">
                  {subscriptions.filter(s => ['active', 'trialing'].includes(s.status)).length}
                </div>
                <p className="text-sm text-muted-foreground">Active Subscriptions</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">
                  {paymentMethods.length}
                </div>
                <p className="text-sm text-muted-foreground">Payment Methods</p>
              </CardContent>
            </Card>
          </div>

          {/* Customer Details */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email</label>
                  <p>{customer.email || 'No email'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Phone</label>
                  <p>{customer.phone || 'No phone'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created</label>
                  <p>{formatDate(customer.created)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Balance</label>
                  <p className={customer.balance > 0 ? 'text-green-600' : customer.balance < 0 ? 'text-red-600' : ''}>
                    {formatCurrency(customer.balance, customer.currency)}
                  </p>
                </div>
                {customer.description && (
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p>{customer.description}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          {paymentMethods.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {paymentMethods.map((pm) => (
                    <div key={pm.id} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <span className="font-medium">{pm.type.toUpperCase()}</span>
                        {pm.card && (
                          <span className="ml-2 text-muted-foreground">
                            {pm.card.brand?.toUpperCase()} •••• {pm.card.last4} 
                            ({pm.card.expMonth}/{pm.card.expYear})
                          </span>
                        )}
                      </div>
                      <span className="text-sm text-muted-foreground">
                        Added {formatDate(pm.created)}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Subscriptions */}
          {subscriptions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Subscriptions</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Status</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Interval</TableHead>
                      <TableHead>Current Period</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {subscriptions.map((sub) => (
                      <TableRow key={sub.id}>
                        <TableCell>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(sub.status)}`}>
                            {sub.status}
                          </span>
                        </TableCell>
                        <TableCell>{formatCurrency(sub.amount, sub.currency)}</TableCell>
                        <TableCell>
                          Every {sub.intervalCount} {sub.interval}
                          {sub.intervalCount > 1 ? 's' : ''}
                        </TableCell>
                        <TableCell>
                          {formatDate(sub.currentPeriodStart)} - {formatDate(sub.currentPeriodEnd)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* Recent Charges */}
          {charges.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recent Payments ({charges.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Receipt</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {charges.slice(0, 10).map((charge) => (
                      <TableRow key={charge.id}>
                        <TableCell>{formatDate(charge.created)}</TableCell>
                        <TableCell>{formatCurrency(charge.amount, charge.currency)}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(charge.status)}`}>
                            {charge.status}
                          </span>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {charge.description || 'No description'}
                        </TableCell>
                        <TableCell>
                          {charge.receiptUrl && (
                            <a 
                              href={charge.receiptUrl} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-primary hover:underline text-sm"
                            >
                              View Receipt
                            </a>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
