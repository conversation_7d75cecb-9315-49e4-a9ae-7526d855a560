'use client';

import { StripeCustomer } from '@/types';
import { StripeCustomerCard } from './StripeCustomerCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface StripeCustomerListProps {
  customers: StripeCustomer[];
  loading?: boolean;
  onViewDetails: (customer: StripeCustomer) => void;
}

export function StripeCustomerList({ customers, loading, onViewDetails }: StripeCustomerListProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Search Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-muted-foreground">Searching customers...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (customers.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Search Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-muted-foreground">
            <p>No customers found</p>
            <p className="text-sm mt-2">
              Try searching with a different email address or customer ID
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          Search Results ({customers.length} customer{customers.length !== 1 ? 's' : ''})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {customers.map((customer) => (
            <StripeCustomerCard
              key={customer.id}
              customer={customer}
              onViewDetails={onViewDetails}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
