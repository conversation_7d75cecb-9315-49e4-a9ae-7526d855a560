'use client';

import { useState } from 'react';
import { useLazyQuery, gql } from '@apollo/client';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { StripeCustomer } from '@/types';

// GraphQL queries for Stripe customer search
const SEARCH_STRIPE_CUSTOMERS_BY_EMAIL = gql`
  query SearchStripeCustomersByEmail($email: String!) {
    stripeCustomerByEmail(email: $email) {
      id
      email
      name
      phone
      created
      currency
      balance
      description
      metadata
    }
  }
`;

const GET_STRIPE_CUSTOMER = gql`
  query GetStripeCustomer($customerId: String!) {
    stripeCustomer(customerId: $customerId) {
      id
      email
      name
      phone
      created
      currency
      balance
      description
      metadata
    }
  }
`;

interface StripeCustomerSearchProps {
  onCustomersFound: (customers: StripeCustomer[]) => void;
  onError: (error: string) => void;
}

export function StripeCustomerSearch({ onCustomersFound, onError }: StripeCustomerSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState<'email' | 'id'>('email');

  const [searchByEmail, { loading: emailLoading }] = useLazyQuery(SEARCH_STRIPE_CUSTOMERS_BY_EMAIL, {
    onCompleted: (data) => {
      if (data.stripeCustomerByEmail) {
        onCustomersFound(data.stripeCustomerByEmail);
      } else {
        onCustomersFound([]);
      }
    },
    onError: (error) => {
      console.error('Error searching customers by email:', error);
      onError(`Failed to search customers: ${error.message}`);
    },
  });

  const [searchById, { loading: idLoading }] = useLazyQuery(GET_STRIPE_CUSTOMER, {
    onCompleted: (data) => {
      if (data.stripeCustomer) {
        onCustomersFound([data.stripeCustomer]);
      } else {
        onCustomersFound([]);
      }
    },
    onError: (error) => {
      console.error('Error searching customer by ID:', error);
      onError(`Failed to find customer: ${error.message}`);
    },
  });

  const validateInput = (query: string, type: 'email' | 'id'): string | null => {
    if (!query.trim()) {
      return 'Please enter a search term';
    }

    if (type === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(query.trim())) {
        return 'Please enter a valid email address';
      }
    } else if (type === 'id') {
      if (!query.trim().startsWith('cus_')) {
        return 'Stripe customer IDs must start with "cus_"';
      }
      if (query.trim().length < 10) {
        return 'Customer ID appears to be too short';
      }
    }

    return null;
  };

  const handleSearch = () => {
    const validationError = validateInput(searchQuery, searchType);
    if (validationError) {
      onError(validationError);
      return;
    }

    if (searchType === 'email') {
      searchByEmail({ variables: { email: searchQuery.trim() } });
    } else {
      searchById({ variables: { customerId: searchQuery.trim() } });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const isLoading = emailLoading || idLoading;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Search Stripe Customers</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <select
            value={searchType}
            onChange={(e) => setSearchType(e.target.value as 'email' | 'id')}
            className="px-3 py-2 border border-input rounded-md bg-background text-sm"
          >
            <option value="email">Email</option>
            <option value="id">Customer ID</option>
          </select>
          <Input
            type="text"
            placeholder={searchType === 'email' ? 'Enter email address...' : 'Enter customer ID...'}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1"
            disabled={isLoading}
          />
          <Button 
            onClick={handleSearch}
            disabled={isLoading || !searchQuery.trim()}
          >
            {isLoading ? 'Searching...' : 'Search'}
          </Button>
        </div>
        
        <div className="text-sm text-muted-foreground">
          {searchType === 'email' 
            ? 'Search for customers by their email address'
            : 'Search for a specific customer by their Stripe customer ID (starts with "cus_")'
          }
        </div>
      </CardContent>
    </Card>
  );
}
