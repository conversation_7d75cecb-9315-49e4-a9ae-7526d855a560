import React, { useRef, useEffect, useLayoutEffect, useCallback } from 'react';

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export default function SearchInput({ 
  value, 
  onChange, 
  placeholder = "Search...",
  className = ""
}: SearchInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const shouldMaintainFocusRef = useRef<boolean>(false);
  const lastCursorPositionRef = useRef<number>(0);
  const focusTimeoutRef = useRef<any>(null);

  // Aggressive focus restoration on every render
  useLayoutEffect(() => {
    if (shouldMaintainFocusRef.current && inputRef.current && document.activeElement !== inputRef.current) {
      // Clear any existing timeout
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current);
      }
      
      focusTimeoutRef.current = setTimeout(() => {
        const input = inputRef.current;
        if (input && shouldMaintainFocusRef.current) {
          input.focus();
          const cursorPos = lastCursorPositionRef.current;
          if (cursorPos >= 0 && cursorPos <= input.value.length) {
            input.setSelectionRange(cursorPos, cursorPos);
          }
        }
      }, 0);
    }
  });

  // Also restore focus when value changes (search results loading)
  useEffect(() => {
    if (shouldMaintainFocusRef.current && inputRef.current && document.activeElement !== inputRef.current) {
      // Multiple attempts with increasing delays to handle various timing scenarios
      const restoreFocus = () => {
        const input = inputRef.current;
        if (input && shouldMaintainFocusRef.current) {
          input.focus();
          const cursorPos = lastCursorPositionRef.current;
          if (cursorPos >= 0 && cursorPos <= input.value.length) {
            input.setSelectionRange(cursorPos, cursorPos);
          }
        }
      };

      setTimeout(restoreFocus, 0);
      setTimeout(restoreFocus, 10);
      setTimeout(restoreFocus, 100);
    }
  }, [value]);

  const handleFocus = useCallback(() => {
    shouldMaintainFocusRef.current = true;
  }, []);

  const handleBlur = useCallback(() => {
    // Give a longer delay to handle Apollo's re-rendering
    setTimeout(() => {
      if (inputRef.current && document.activeElement !== inputRef.current) {
        shouldMaintainFocusRef.current = false;
      }
    }, 200);
  }, []);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
    shouldMaintainFocusRef.current = true;
  }, []);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const target = e.target as HTMLInputElement;
    lastCursorPositionRef.current = target.selectionStart || 0;
    shouldMaintainFocusRef.current = true;
    onChange(e.target.value);
  }, [onChange]);

  const handleMouseUp = useCallback(() => {
    if (inputRef.current) {
      lastCursorPositionRef.current = inputRef.current.selectionStart || 0;
    }
  }, []);

  const handleClearClick = useCallback(() => {
    shouldMaintainFocusRef.current = false;
    if (focusTimeoutRef.current) {
      clearTimeout(focusTimeoutRef.current);
    }
    onChange('');
  }, [onChange]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={`relative ${className}`}>
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg 
          className="h-5 w-5 text-gray-400" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 20 20" 
          fill="currentColor"
        >
          <path 
            fillRule="evenodd" 
            d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" 
            clipRule="evenodd" 
          />
        </svg>
      </div>
      <input
        ref={inputRef}
        type="text"
        className={`block w-full pl-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-theme ${value ? 'pr-10' : 'pr-3'}`}
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onMouseUp={handleMouseUp}
      />
      {value && (
        <button
          type="button"
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500 cursor-pointer"
          onClick={handleClearClick}
        >
          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </button>
      )}
    </div>
  );
} 