import * as React from "react"
import { cn } from "@/lib/utils"

interface TooltipProps {
  content: string
  children: React.ReactNode
  side?: 'top' | 'bottom' | 'left' | 'right'
  className?: string
}

export function Tooltip({ content, children, side = 'top', className }: TooltipProps) {
  const [isVisible, setIsVisible] = React.useState(false)
  const [position, setPosition] = React.useState({ x: 0, y: 0 })
  const triggerRef = React.useRef<HTMLDivElement>(null)

  const handleMouseEnter = (e: React.MouseEvent) => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect()
      let x = 0
      let y = 0

      switch (side) {
        case 'top':
          x = rect.left + rect.width / 2
          y = rect.top - 8
          break
        case 'bottom':
          x = rect.left + rect.width / 2
          y = rect.bottom + 8
          break
        case 'left':
          x = rect.left - 8
          y = rect.top + rect.height / 2
          break
        case 'right':
          x = rect.right + 8
          y = rect.top + rect.height / 2
          break
      }

      setPosition({ x, y })
      setIsVisible(true)
    }
  }

  const handleMouseLeave = () => {
    setIsVisible(false)
  }

  const getTooltipClasses = () => {
    const baseClasses = "fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none transition-opacity duration-200"
    const sideClasses = {
      top: "transform -translate-x-1/2 -translate-y-full",
      bottom: "transform -translate-x-1/2",
      left: "transform -translate-x-full -translate-y-1/2",
      right: "transform -translate-y-1/2"
    }
    
    return cn(
      baseClasses,
      sideClasses[side],
      isVisible ? "opacity-100" : "opacity-0 pointer-events-none",
      className
    )
  }

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="inline-block"
      >
        {children}
      </div>
      
      {isVisible && (
        <div
          className={getTooltipClasses()}
          style={{
            left: position.x,
            top: position.y,
          }}
        >
          {content}
          {/* Arrow */}
          <div
            className={cn(
              "absolute w-2 h-2 bg-gray-900 transform rotate-45",
              side === 'top' && "top-full left-1/2 -translate-x-1/2 -translate-y-1/2",
              side === 'bottom' && "bottom-full left-1/2 -translate-x-1/2 translate-y-1/2",
              side === 'left' && "left-full top-1/2 -translate-x-1/2 -translate-y-1/2",
              side === 'right' && "right-full top-1/2 translate-x-1/2 -translate-y-1/2"
            )}
          />
        </div>
      )}
    </>
  )
}
