'use client';

import { getApiBaseUrl } from './testMode';

/**
 * API utilities for making authenticated requests to the legacy API
 */

// Base URL for the legacy API - automatically use staging on localhost
const API_BASE_URL = getApiBaseUrl();

// Base URL for Stripe dashboard - use same environment as API for consistent data
const STRIPE_DASHBOARD_API_BASE_URL = process.env.NODE_ENV === 'development'
  ? 'https://api.staging.kpfa.org'
  : 'https://api.kpfa.org';

// Get authenticated token - matches pattern used throughout the app
export const getAuthToken = (): string | null => {
  // Get the JWT token from localStorage - same pattern as authContext and apolloClient
  return typeof window !== 'undefined' ? localStorage.getItem('jwt_token') : null;
};

// Add the API_BASE_URL to exports so it can be used in other parts of the app
export { API_BASE_URL, STRIPE_DASHBOARD_API_BASE_URL };

interface FetchOptions extends RequestInit {
  skipAuth?: boolean;
}

/**
 * Fetch wrapper that automatically adds authentication headers
 */
export async function apiFetch<T>(
  endpoint: string, 
  options: FetchOptions = {}
): Promise<T> {
  const { skipAuth = false, headers = {}, ...rest } = options;
  
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}/${endpoint.startsWith('/') ? endpoint.slice(1) : endpoint}`;
  
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...(headers as Record<string, string>),
  };
  
  // Add authentication token if available and not skipping auth
  if (!skipAuth) {
    const token = getAuthToken();
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`;
    }
  }
  
  try {
    const response = await fetch(url, {
      ...rest,
      headers: requestHeaders,
    });
    
    // Get the response text first
    const responseText = await response.text();
    
    // Try to parse the response as JSON if there's content
    let parsedResponse: any = null;
    if (responseText && responseText.trim() !== '') {
      try {
        parsedResponse = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse API response as JSON:', parseError);
        throw new Error(`API returned invalid JSON: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
      }
    }
    
    // Handle error responses
    if (!response.ok) {
      // Handle unauthorized errors specially
      if (response.status === 401 || response.status === 403) {
        // Clear the token if it's invalid
        if (typeof window !== 'undefined') {
          localStorage.removeItem('jwt_token');
        }
        throw new Error(parsedResponse?.message || 'Authentication failed. Please log in again.');
      }
      
      // Handle other error responses
      const errorMessage = parsedResponse?.message || `API request failed with status ${response.status}`;
      const errorDetails = parsedResponse?.error?.details || errorMessage;
      
      throw new Error(errorDetails);
    }
    
    // Return parsed response or empty object for empty responses
    return (parsedResponse || {}) as T;
    
  } catch (error: any) {
    console.error('API request failed:', error);
    
    // Enhance error message for network errors
    if (error.message === 'Failed to fetch') {
      throw new Error(`Unable to connect to the API. Please check your connection and try again. (URL: ${url})`);
    }
    
    throw error;
  }
}

/**
 * Fetch wrapper specifically for Stripe dashboard that always uses production API
 */
export async function stripeDashboardApiFetch<T>(
  endpoint: string,
  options: FetchOptions = {}
): Promise<T> {
  const { skipAuth = false, headers = {}, ...rest } = options;

  const url = endpoint.startsWith('http') ? endpoint : `${STRIPE_DASHBOARD_API_BASE_URL}/${endpoint.startsWith('/') ? endpoint.slice(1) : endpoint}`;

  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...(headers as Record<string, string>),
  };

  // Add authentication token if available and not skipping auth
  if (!skipAuth) {
    const token = getAuthToken();
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`;
    }
  }

  try {
    const response = await fetch(url, {
      ...rest,
      headers: requestHeaders,
    });

    // Get the response text first
    const responseText = await response.text();

    // Try to parse the response as JSON if there's content
    let parsedResponse: any = null;
    if (responseText && responseText.trim() !== '') {
      try {
        parsedResponse = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse API response as JSON:', parseError);
        throw new Error(`API returned invalid JSON: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
      }
    }

    // Handle error responses
    if (!response.ok) {
      // Handle unauthorized errors specially
      if (response.status === 401 || response.status === 403) {
        // Clear the token if it's invalid
        if (typeof window !== 'undefined') {
          localStorage.removeItem('jwt_token');
        }
        throw new Error(parsedResponse?.message || 'Authentication failed. Please log in again.');
      }

      // Handle other error responses
      const errorMessage = parsedResponse?.message || `API request failed with status ${response.status}`;
      const errorDetails = parsedResponse?.error?.details || errorMessage;

      throw new Error(errorDetails);
    }

    // Return parsed response or empty object for empty responses
    return (parsedResponse || {}) as T;

  } catch (error: any) {
    console.error('Stripe dashboard API request failed:', error);

    // Enhance error message for network errors
    if (error.message === 'Failed to fetch') {
      throw new Error(`Unable to connect to the API. Please check your connection and try again. (URL: ${url})`);
    }

    throw error;
  }
}

/**
 * Check if the user is authenticated with the API
 */
export async function checkApiAuth(): Promise<boolean> {
  try {
    // Try to make a simple authenticated request to check token validity
    // Use an endpoint that matches the API structure we're accessing
    await apiFetch('/accounts/donors?limit=1');
    return true;
  } catch (error) {
    return false;
  }
}

interface DonorSearchResponse {
  records: any[];
}

/**
 * Search for donors using various criteria - uses production API for Stripe dashboard
 */
export async function searchDonors(query: string): Promise<any[]> {
  try {
    // Use the direct path pattern like other API calls: /accounts/donors?s=query
    const response = await stripeDashboardApiFetch<DonorSearchResponse>(`/accounts/donors?s=${encodeURIComponent(query)}`);
    return response.records || [];
  } catch (error) {
    console.error('Error searching donors:', error);
    throw error;
  }
}

/**
 * Get donor details by ID - uses production API for Stripe dashboard
 */
export async function getDonorDetails(donorId: number): Promise<any> {
  try {
    const response = await stripeDashboardApiFetch<any>(`/accounts/donors/${donorId}`);
    return response;
  } catch (error) {
    console.error('Error getting donor details:', error);
    throw error;
  }
}

interface DuplicateSearchResponse {
  success: boolean;
  search_phone: string;
  clean_search_phone: string;
  total_found: number;
  exact_matches: Array<{
    id: number;
    display_name: string;
    firstname: string;
    lastname: string;
    phone: string;
    formatted_phone: string;
    email: string;
    address1: string;
    address2: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    type: string;
    membership_level: string;
    deceased: boolean;
    donotsolicit: boolean;
    date_created: string;
    date_updated: string;
  }>;
}

/**
 * Search for potential duplicate donors by phone number
 */
export async function searchDuplicatesByPhone(
  phoneNumber: string, 
  excludeDonorId?: number, 
  limit: number = 10
): Promise<DuplicateSearchResponse> {
  try {
    // Build query parameters
    const params = new URLSearchParams();
    params.append('phone', phoneNumber);
    if (excludeDonorId) {
      params.append('exclude_id', excludeDonorId.toString());
    }
    params.append('limit', limit.toString());

    // Use the NextJS API route which proxies to the PHP endpoint
    const response = await fetch(`/api/donors/duplicates?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        // Auth will be handled by the NextJS API route
      },
    });

    const responseText = await response.text();
    
    if (!response.ok) {
      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        errorData = { message: responseText };
      }
      
      throw new Error(errorData.message || `Request failed with status ${response.status}`);
    }

    const data = JSON.parse(responseText) as DuplicateSearchResponse;
    return data;
  } catch (error) {
    console.error('Error searching for duplicate donors:', error);
    throw error;
  }
}

// Generic API request function
export async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  // Get JWT token from localStorage
  const token = typeof window !== 'undefined' ? localStorage.getItem('jwt_token') : null;
  
  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }
  
  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    
    // Handle different response types
    const contentType = response.headers.get('content-type');
    let data: any;
    
    if (contentType && contentType.includes('application/json')) {
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('Failed to parse API response as JSON:', parseError);
        throw new Error('Invalid JSON response from server');
      }
    } else {
      data = await response.text();
    }
    
    if (!response.ok) {
      // Handle API errors
      const errorMessage = typeof data === 'object' && data.error 
        ? data.error 
        : `API request failed: ${response.status} ${response.statusText}`;
      
      throw new Error(errorMessage);
    }
    
    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
} 