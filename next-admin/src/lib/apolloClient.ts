import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { isTestMode } from './testMode';

// Automatically detect test mode when running on localhost
const testMode = isTestMode();

/*
// Previous logic for reference when implementing the user toggle:
let isTestMode = process.env.NEXT_PUBLIC_TEST_MODE === 'true';

// This is temporary logic that will be replaced by a user-facing toggle.
// For now, we treat next.kpfa.org as a test environment.
if (typeof window !== 'undefined' && window.location.hostname === 'next.kpfa.org') {
  isTestMode = true;
}
*/

// Use environment variable for API URL if available, otherwise use mode-based URL
const KEYSTONE_URL = process.env.NEXT_PUBLIC_KEYSTONE_API ||
                    (testMode
                      ? 'https://keystone.staging.kpfa.org'
                      : 'https://keystone.kpfa.org');

const API_URL = `${KEYSTONE_URL}/api/graphql`;

const httpLink = createHttpLink({
  uri: API_URL,
  credentials: 'include',  // Include cookies for authentication if needed
});

// Auth link that adds the JWT token to headers
const authLink = setContext((_, { headers }) => {
  // Get the JWT token from localStorage
  const token = typeof window !== 'undefined' ? localStorage.getItem('jwt_token') : null;
  
  // Return the headers to the context
  return {
    headers: {
      ...headers,
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
    }
  };
});

// Error link for handling GraphQL and network errors
const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors)
    graphQLErrors.forEach(({ message, locations, path }) =>
      console.error(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`,
      ),
    );
  if (networkError) console.error(`[Network error]: ${networkError}`);
});

const client = new ApolloClient({
  link: from([errorLink, authLink, httpLink]),
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          duplicateGroups: {
            // Enable proper caching for paginated queries
            keyArgs: ["where", "orderBy"],
            merge(existing, incoming, { args }) {
              // Always return incoming data for pagination (replace, don't append)
              // This ensures proper pagination behavior instead of appending
              return incoming;
            },
          },
        },
      },
    },
  }),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-first',
      errorPolicy: 'all',
    },
    query: {
      fetchPolicy: 'cache-first',
      errorPolicy: 'all',
    },
  },
});

export { testMode as isTestMode };
export default client;