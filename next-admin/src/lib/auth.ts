import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { JWT } from 'next-auth/jwt';

// Extend the types to include our custom fields
declare module 'next-auth' {
  interface User {
    accessToken?: string;
  }
  
  interface Session {
    accessToken?: string;
    user?: {
      name?: string;
      email?: string;
    }
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    accessToken?: string;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        token: { label: "Token", type: "text" }
      },
      async authorize(credentials) {
        if (!credentials?.token) {
          return null;
        }

        try {
          const response = await fetch(`${process.env.LEGACY_API_URL}/api/verify-token`, {
            headers: {
              'Authorization': `Bearer ${credentials.token}`
            }
          });

          if (!response.ok) {
            return null;
          }

          const user = await response.json();
          return {
            id: user.id,
            name: `${user.firstname} ${user.lastname}`,
            email: user.email,
            accessToken: credentials.token,
          };
        } catch (error) {
          return null;
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.accessToken = user.accessToken;
        token.name = user.name;
        token.email = user.email;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.accessToken = token.accessToken;
        if (token.name) {
          session.user = session.user || {};
          session.user.name = token.name;
        }
        if (token.email) {
          session.user = session.user || {};
          session.user.email = token.email;
        }
      }
      return session;
    }
  },
  pages: {
    signIn: '/login',
  },
}; 