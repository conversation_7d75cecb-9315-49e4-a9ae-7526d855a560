'use client';

import { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';

type User = {
  id: string;
  email: string;
  firstname: string;
  lastname: string;
  access_level: string;
};

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  checkAuth: () => Promise<boolean>;
  logout: () => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Helper to determine if in development environment
const isDevelopment = () => 
  typeof window !== 'undefined' && 
  (window.location.hostname === 'localhost' || 
   window.location.hostname === '127.0.0.1' ||
   window.location.hostname === 'pet-leopard-fully.ngrok-free.app');

// Helper to extract station admin domain
const getStationAdminDomain = () => {
  // If we're using ngrok for testing, use the StationAdmin on ngrok
  if (typeof window !== 'undefined' && window.location.hostname === 'pet-leopard-fully.ngrok-free.app') {
    return 'pet-leopard-fully.ngrok-free.app';
  }
  // Otherwise use the production domain for StationAdmin
  return 'admin.kpfa.org';
};

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkAuth = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      // Try to get token from localStorage
      const token = typeof window !== 'undefined' ? localStorage.getItem('jwt_token') : null;

      if (!token) {
        setIsAuthenticated(false);
        setUser(null);
        setIsLoading(false);
        return false;
      }

      // Verify token with our API endpoint
      const response = await fetch('/api/auth', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();

        // If access level is insufficient, show appropriate error
        if (response.status === 403) {
          const errorMsg = `Access denied: Admin permission required. Your access level: ${errorData.accessLevel || 'Unknown'}`;
          setError(errorMsg);
        } else {
          const errorMsg = errorData.error || 'Authentication failed';
          setError(errorMsg);
        }

        setIsAuthenticated(false);
        setUser(null);
        setIsLoading(false);
        return false;
      }

      // If verified, get user data from the response
      const data = await response.json();

      if (data.authenticated && data.user) {
        setUser(data.user);
        setIsAuthenticated(true);
        setIsLoading(false);
        return true;
      } else {
        setIsAuthenticated(false);
        setUser(null);
        setIsLoading(false);
        return false;
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setError('Failed to authenticate');
      setIsAuthenticated(false);
      setUser(null);
      setIsLoading(false);
      return false;
    }
  }, []);

  const logout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('jwt_token');
    }
    setUser(null);
    setIsAuthenticated(false);
    
    // Redirect to StationAdmin logout
    window.location.href = `${getStationAdminDomain().startsWith('http') ? '' : 'https://'}${getStationAdminDomain()}/logout`;
  };

  useEffect(() => {
    // Check for JWT token in URL parameters first, then run auth check
    const initializeAuth = async () => {
      // Check if there's a token in the URL parameters
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const urlToken = urlParams.get('token');

        if (urlToken) {
          localStorage.setItem('jwt_token', urlToken);

          // Remove token from URL for security
          const newUrl = new URL(window.location.href);
          newUrl.searchParams.delete('token');
          window.history.replaceState({}, '', newUrl.toString());

          // Give a small delay to ensure localStorage is updated
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      // Now run the auth check
      await checkAuth();
    };
    initializeAuth();
  }, [checkAuth]);

  return (
    <AuthContext.Provider value={{ user, isLoading, isAuthenticated, error, checkAuth, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 