import { useState, useCallback, useEffect } from 'react';
import { PaginationInfo } from '@/components/ui/Pagination';
import { useUrlState } from './useUrlState';

export interface PaginationConfig {
  initialPage?: number;
  initialItemsPerPage?: number;
  maxItemsPerPage?: number;
  urlSync?: boolean; // New option to sync with URL
  urlPrefix?: string; // Prefix for URL parameters (e.g., 'page_', 'items_')
  scrollToTopOnPageChange?: boolean; // Auto-scroll to top when page changes
  scrollTargetSelector?: string; // CSS selector for scroll target (defaults to window)
}

export interface PaginationState {
  currentPage: number;
  itemsPerPage: number;
  skip: number; // For Keystone queries
  take: number; // For Keystone queries
}

export interface PaginationHookResult {
  paginationState: PaginationState;
  paginationInfo: (totalItems: number, hasNextPage?: boolean) => PaginationInfo;
  goToPage: (page: number) => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  resetPagination: () => void;
  nextPage: () => void;
  previousPage: () => void;
}

export function usePagination(config: PaginationConfig = {}): PaginationHookResult {
  const {
    initialPage = 1,
    initialItemsPerPage = 20,
    maxItemsPerPage = 100,
    urlSync = false,
    urlPrefix = '',
    scrollToTopOnPageChange = false,
    scrollTargetSelector,
  } = config;

  // Always call useUrlState to maintain hook order, but only use it when urlSync is true
  const urlState = useUrlState({
    defaultValues: urlSync ? {
      [`${urlPrefix}page`]: String(initialPage),
      [`${urlPrefix}itemsPerPage`]: String(initialItemsPerPage),
    } : {},
  });

  // Local state (always available, used when URL sync is disabled)
  const [localCurrentPage, setLocalCurrentPage] = useState(initialPage);
  const [localItemsPerPage, setLocalItemsPerPageState] = useState(initialItemsPerPage);

  // Determine current values based on URL sync setting
  const currentPage = urlSync
    ? Math.max(1, urlState.getNumericParam(`${urlPrefix}page`, initialPage))
    : localCurrentPage;
  
  const itemsPerPage = urlSync
    ? Math.max(1, Math.min(urlState.getNumericParam(`${urlPrefix}itemsPerPage`, initialItemsPerPage), maxItemsPerPage))
    : localItemsPerPage;

  const paginationState: PaginationState = {
    currentPage,
    itemsPerPage,
    skip: (currentPage - 1) * itemsPerPage,
    take: Math.min(itemsPerPage, maxItemsPerPage),
  };

  // Helper function to scroll to target
  const scrollToTarget = useCallback(() => {
    if (!scrollToTopOnPageChange) return;
    
    if (scrollTargetSelector) {
      const target = document.querySelector(scrollTargetSelector);
      if (target) {
        target.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    } else {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [scrollToTopOnPageChange, scrollTargetSelector]);

  // Create stable references with proper dependencies
  const goToPage = useCallback((page: number) => {
    const validPage = Math.max(1, page);
    const pageChanged = validPage !== currentPage;
    
    if (urlSync) {
      urlState.updateUrlState({ [`${urlPrefix}page`]: validPage });
    } else {
      setLocalCurrentPage(validPage);
    }
    
    // Scroll to target if page actually changed
    if (pageChanged) {
      // Small delay to ensure DOM has updated
      setTimeout(scrollToTarget, 100);
    }
  }, [urlSync, urlPrefix, urlState, setLocalCurrentPage, currentPage, scrollToTarget]); // Include urlState when needed

  const setItemsPerPage = useCallback((newItemsPerPage: number) => {
    const clampedItemsPerPage = Math.min(Math.max(1, newItemsPerPage), maxItemsPerPage);
    
    if (urlSync) {
      // Reset to first page when changing items per page
      urlState.updateUrlState({ 
        [`${urlPrefix}itemsPerPage`]: clampedItemsPerPage,
        [`${urlPrefix}page`]: 1
      });
    } else {
      setLocalItemsPerPageState(clampedItemsPerPage);
      setLocalCurrentPage(1);
    }
  }, [maxItemsPerPage, urlSync, urlPrefix, urlState, setLocalItemsPerPageState, setLocalCurrentPage]); // Include urlState when needed

  const resetPagination = useCallback(() => {
    if (urlSync) {
      urlState.updateUrlState({
        [`${urlPrefix}page`]: initialPage,
        [`${urlPrefix}itemsPerPage`]: initialItemsPerPage
      });
    } else {
      // Force immediate state updates
      setLocalCurrentPage(initialPage);
      setLocalItemsPerPageState(initialItemsPerPage);
    }
  }, [initialPage, initialItemsPerPage, urlSync, urlPrefix, urlState, setLocalCurrentPage, setLocalItemsPerPageState]); // Include urlState when needed

  const nextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [goToPage, currentPage]);

  const previousPage = useCallback(() => {
    goToPage(Math.max(1, currentPage - 1));
  }, [goToPage, currentPage]);

  const paginationInfo = useCallback((totalItems: number, hasNextPage?: boolean): PaginationInfo => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    
    const info = {
      currentPage,
      itemsPerPage,
      totalItems,
      hasNextPage: hasNextPage !== undefined ? hasNextPage : currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
    };
    
    return info;
  }, [currentPage, itemsPerPage]);

  // Effect to ensure the pagination state is valid when totalItems change
  useEffect(() => {
    // This effect can be used to validate pagination state if needed
    // For now, it's empty but can be extended for complex validation
  }, [currentPage, itemsPerPage, maxItemsPerPage]);

  return {
    paginationState,
    paginationInfo,
    goToPage,
    setItemsPerPage,
    resetPagination,
    nextPage,
    previousPage,
  };
} 