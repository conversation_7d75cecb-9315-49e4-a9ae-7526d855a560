import { useCallback, useEffect, useState } from 'react';
import { useUrlState } from './useUrlState';

export interface SearchConfig {
  initialQuery?: string;
  urlSync?: boolean;
  urlParamName?: string;
  debounceMs?: number;
  onSearchChange?: (query: string) => void;
}

export interface SearchHookResult {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  clearSearch: () => void;
  debouncedQuery: string;
}

export function useSearch(config: SearchConfig = {}): SearchHookResult {
  const {
    initialQuery = '',
    urlSync = false,
    urlParamName = 'search',
    debounceMs = 300,
    onSearchChange,
  } = config;

  // URL state management (only if urlSync is enabled)
  const urlState = useUrlState({
    defaultValues: urlSync ? {
      [urlParamName]: initialQuery,
    } : {},
  });

  // Local state (fallback when URL sync is disabled)
  const [localSearchQuery, setLocalSearchQuery] = useState(initialQuery);
  const [debouncedQuery, setDebouncedQuery] = useState(initialQuery);

  // Determine current search query based on URL sync setting
  const searchQuery = urlSync 
    ? urlState.getParam(urlParamName, initialQuery)
    : localSearchQuery;

  // Effect to handle debouncing
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, debounceMs);

    return () => {
      clearTimeout(handler);
    };
  }, [searchQuery, debounceMs]);

  // Debug effect to track debounced query changes
  useEffect(() => {
    // Only log when there's an actual change and it's not empty
    if (debouncedQuery !== searchQuery) {
      // This effect runs when debounced query updates
    }
  }, [debouncedQuery, searchQuery]);

  const setSearchQuery = useCallback((query: string) => {
    if (urlSync) {
      urlState.updateUrlState({ [`${urlParamName}`]: query });
    } else {
      setLocalSearchQuery(query);
    }
  }, [urlSync, urlParamName, urlState, setLocalSearchQuery]);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
  }, [setSearchQuery]);

  return {
    searchQuery,
    setSearchQuery,
    clearSearch,
    debouncedQuery,
  };
} 