import { useCallback, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export interface UrlStateOptions {
  defaultValues?: Record<string, string>;
  debounceMs?: number;
}

export function useUrlState(options: UrlStateOptions = {}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { defaultValues = {} } = options;

  // Get current URL parameters
  const urlState = useMemo(() => {
    const state: Record<string, string> = { ...defaultValues };
    
    // Override with current URL params
    searchParams.forEach((value, key) => {
      state[key] = value;
    });
    
    return state;
  }, [searchParams, defaultValues]);

  // Update URL with new state
  const updateUrlState = useCallback((newState: Record<string, string | number | null>) => {
    const params = new URLSearchParams(searchParams);
    
    // Update or remove parameters
    Object.entries(newState).forEach(([key, value]) => {
      if (value === null || value === '' || value === undefined) {
        params.delete(key);
      } else {
        params.set(key, String(value));
      }
    });
    
    // Build new URL - only include pathname, not full location
    const currentPath = window.location.pathname;
    const newUrl = params.toString() ? `${currentPath}?${params.toString()}` : currentPath;
    
    // Use replace to avoid adding to browser history for every state change
    // Add a small delay to prevent race conditions
    setTimeout(() => {
      router.replace(newUrl, { scroll: false });
    }, 0);
  }, [router, searchParams]);

  // Helper to get a specific parameter with fallback
  const getParam = useCallback((key: string, fallback: string = '') => {
    return urlState[key] || fallback;
  }, [urlState]);

  // Helper to get a numeric parameter
  const getNumericParam = useCallback((key: string, fallback: number = 0) => {
    const value = urlState[key];
    const parsed = value ? parseInt(value, 10) : fallback;
    return isNaN(parsed) ? fallback : parsed;
  }, [urlState]);

  return {
    urlState,
    updateUrlState,
    getParam,
    getNumericParam,
  };
} 