/**
 * JWT utility functions for token handling
 */

interface JWTPayload {
  data?: {
    access_level?: string;
    user_id?: number;
    email?: string;
    [key: string]: any;
  };
  exp?: number;
  iat?: number;
  [key: string]: any;
}

/**
 * Decode a JWT token without verification
 * Note: This only decodes the payload, it does NOT verify the signature
 * Use this only for extracting claims from trusted tokens
 */
export function decodeJWT(token: string): JWTPayload | null {
  try {
    // JWT tokens have 3 parts separated by dots: header.payload.signature
    const parts = token.split('.');
    
    if (parts.length !== 3) {
      console.error('Invalid JWT format: token must have 3 parts');
      return null;
    }

    // Decode the payload (second part)
    const payload = parts[1];
    
    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    
    // Decode from base64url to string
    const decodedPayload = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));
    
    // Parse JSON
    const parsedPayload: JWTPayload = JSON.parse(decodedPayload);
    
    return parsedPayload;
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

/**
 * Check if a JWT token is expired
 */
export function isTokenExpired(token: string): boolean {
  const payload = decodeJWT(token);
  
  if (!payload || !payload.exp) {
    return true; // Consider invalid tokens as expired
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  return payload.exp < currentTime;
}

/**
 * Extract user data from JWT token
 */
export function getUserFromToken(token: string): JWTPayload['data'] | null {
  const payload = decodeJWT(token);
  return payload?.data || null;
}

/**
 * Check if user has admin access level from JWT token
 */
export function hasAdminAccess(token: string): boolean {
  const userData = getUserFromToken(token);
  return userData?.access_level === 'Admin';
}
