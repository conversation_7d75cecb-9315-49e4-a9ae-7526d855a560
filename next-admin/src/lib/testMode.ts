/**
 * Test mode utilities for automatic localhost detection
 */

// Detect if we're running on localhost for automatic test mode
export const isTestMode = () => {
  // Client-side detection
  if (typeof window !== 'undefined') {
    return window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1';
  }

  // Server-side detection - use development mode as proxy for localhost
  return process.env.NODE_ENV === 'development';
};

// Get the appropriate API base URL based on environment
export const getApiBaseUrl = () => {
  const testMode = isTestMode();
  const url = testMode ? 'https://api.staging.kpfa.org' : 'https://api.kpfa.org';
  
  if (testMode) {
    console.log('🧪 Test Mode Active: Using staging API:', url);
  }
  
  return url;
};

// Get environment info for debugging
export const getEnvironmentInfo = () => {
  const testMode = isTestMode();

  if (typeof window === 'undefined') {
    return {
      isTestMode: testMode,
      hostname: 'server-side',
      apiUrl: getApiBaseUrl(),
      nodeEnv: process.env.NODE_ENV
    };
  }

  return {
    isTestMode: testMode,
    hostname: window.location.hostname,
    apiUrl: getApiBaseUrl(),
    nodeEnv: process.env.NODE_ENV
  };
};

// Log environment info on startup
if (typeof window !== 'undefined') {
  const envInfo = getEnvironmentInfo();
  console.log('🌍 Environment Info:', envInfo);
  
  if (envInfo.isTestMode) {
    console.log('🧪 Test Mode Features:');
    console.log('  • Using api.staging.kpfa.org for Station Admin API');
    console.log('  • Add STRIPE_SECRET_KEY and STRIPE_PUBLISHABLE_KEY to .env.local for Stripe test keys');
    console.log('  • Payment reconciliation will work with staging database');
  }
}
