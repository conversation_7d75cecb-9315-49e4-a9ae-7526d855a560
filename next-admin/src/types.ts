export interface Donor {
  id: string;
  kpfaId: number;
  firstname?: string;
  lastname?: string;
  email?: string;
  phone?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  partner_firstname?: string;
  partner_lastname?: string;
  notes?: string;
  type?: string;
  membership_level?: string;
  deceased?: boolean;
  donotsolicit?: boolean;
  stripe_cus_id?: string;
  paypal_user_id?: string;
  memsys_id?: number;
  allegiance_id?: number;
  date_created?: string;
  date_updated?: string;
  paperless?: boolean;
  paperless_token?: string;
}

export interface LegacyDonorResponse {
  id: number;
  firstname?: string;
  lastname?: string;
  email?: string;
  phone?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  notes?: string;
  membership_level?: string;
  donotsolicit?: boolean;
  deceased?: boolean;
  type?: string;
  allegianceId?: number;
  memsysId?: number;
  stripeCusId?: string;
  stripe_cus_id?: string;
  error?: string;
}

export interface LegacyApiResponse {
  records: LegacyDonorResponse[];
  error?: string;
  status?: string;
  message?: string;
}

export interface MergeResult {
  success: boolean;
  message: string;
  details?: {
    donationsMoved: number;
    otherRecordsMoved?: number;
  };
  error?: {
    code: string;
    details: string;
  };
}



export interface MarkDonorsAsNotDuplicatesResult {
  id: string;
  groupSignature: string;
  rejectedBy: string;
  rejectedAt: string;
  originalGroupName?: string;
}

// Stripe Customer Types
export interface StripeCustomer {
  id: string;
  email?: string;
  name?: string;
  phone?: string;
  created: number;
  currency?: string;
  balance: number;
  description?: string;
  metadata: Record<string, string>;
  address?: StripeAddress;
}

export interface StripeAddress {
  line1?: string;
  line2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
}

export interface StripeCard {
  brand?: string;
  last4?: string;
  expMonth?: number;
  expYear?: number;
}

export interface StripePaymentMethod {
  id: string;
  type: string;
  card?: StripeCard;
  created: number;
}

export interface StripeSubscription {
  id: string;
  status: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  amount: number;
  currency: string;
  interval: string;
  intervalCount: number;
}

export interface StripeCharge {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  description?: string;
  receiptUrl?: string;
  paymentMethod?: string;
}

export interface StripeCustomerReport {
  customer: StripeCustomer;
  paymentMethods: StripePaymentMethod[];
  subscriptions: StripeSubscription[];
  charges: StripeCharge[];
  totalSpent: number;
  lastPaymentDate?: number;
}

export interface StripeCustomerSearchInput {
  email?: string;
  name?: string;
  limit?: number;
}

export interface StripeHealthCheck {
  status: string;
  message: string;
  timestamp: string;
}

