{"version": 3, "file": "static/css/main.05d39e81.css", "mappings": "AAAA,KACE,+FAAqE,CACrE,iBACF,CAEA,SAGE,yBACF,CAEA,EACE,gBACF,CAEA,MACE,iBACF,CAEA,YACE,4HACwD,CACxD,cACF,CAQA,iCACE,kHAEF,CAEA,SAIE,kBAAmB,CAHnB,cAAe,CAEf,iBAAkB,CADlB,eAGF,CAEA,gBACE,YACF,CAEA,gDACE,4BACE,gBACF,CACF,CAEA,yBACE,YACE,cACF,CACA,yBASE,qBAAuB,CAGvB,4BAA6B,CAN7B,YAAa,CADb,YAAa,CAEb,MAAO,CAIP,eAAgB,CARhB,sBAAuB,CAWvB,gBAAiB,CAbjB,cAAe,CAOf,oBAAsB,CAJtB,SAWF,CACA,kCAIE,QAAW,CAHX,WAAe,CACf,cAAe,CACf,oBAEF,CACA,8BAIE,kBAAmB,CAHnB,YAAa,CACb,kBAAmB,CACnB,sBAEF,CACA,mGAIE,gBAAiB,CADjB,yBAA2B,CAE3B,iBACF,CACA,gCACE,aACF,CACF;;AAEA;;;;;EAKE,CACF,MACE,cAAe,CACf,gBAAiB,CACjB,gBAAiB,CACjB,cAAe,CACf,aAAc,CACd,gBAAiB,CACjB,gBAAiB,CACjB,eAAgB,CAChB,cAAe,CACf,cAAe,CACf,YAAa,CACb,cAAe,CACf,mBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,cAAe,CACf,iBAAkB,CAClB,gBAAiB,CACjB,eAAgB,CAChB,cAAe,CACf,iBAAkB,CAClB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,sBAAuB,CACvB,8NAEyD,CACzD,oIAEF,CAEA,iBAGE,qBACF,CAEA,KAGE,6BAA8B,CAC9B,yCAA6C,CAH7C,sBAAuB,CACvB,gBAGF,CAEA,sEAUE,aACF,CAEA,KAUE,qBAAsB,CAFtB,aAAc,CANd,iMAEuC,CACvC,cAAe,CACf,eAAgB,CAChB,eAAgB,CANhB,QAAS,CAQT,eAEF,CAEA,sBACE,mBACF,CAEA,GACE,kBAAuB,CACvB,QAAS,CACT,gBACF,CAEA,kBAOE,mBAAqB,CADrB,YAEF,CAEA,EAEE,kBAAmB,CADnB,YAEF,CAEA,sCAKE,eAAgB,CADhB,WAAY,CAFZ,yBAA0B,CAC1B,wCAAiC,CAAjC,gCAAiC,CAGjC,qCAA8B,CAA9B,6BACF,CAEA,QAEE,iBAAkB,CAClB,mBACF,CAEA,iBALE,kBAUF,CALA,SAGE,YAEF,CAEA,wBAIE,eACF,CAEA,GACE,eACF,CAEA,GACE,mBAAqB,CACrB,aACF,CAEA,WACE,eACF,CAEA,SAEE,kBACF,CAEA,MACE,aACF,CAEA,QAGE,aAAc,CACd,aAAc,CAFd,iBAAkB,CAGlB,sBACF,CAEA,IACE,aACF,CAEA,IACE,SACF,CAEA,EAGE,wBAA6B,CAF7B,aAAc,CACd,oBAEF,CACA,QACE,aAAc,CACd,yBACF,CAMA,sGAEE,aAAc,CACd,oBACF,CACA,oCACE,SACF,CAEA,kBAIE,kHAC6C,CAC7C,aACF,CAEA,IAEE,kBAAmB,CADnB,YAAa,CAEb,aACF,CAEA,OACE,eACF,CAEA,IAEE,iBACF,CAEA,QAJE,qBAOF,CAHA,IACE,eAEF,CAEA,MACE,wBACF,CAEA,QAKE,mBAAoB,CAFpB,aAAc,CADd,qBAAuB,CADvB,kBAAoB,CAGpB,eAEF,CAEA,GACE,kBACF,CAEA,MACE,oBAAqB,CACrB,mBACF,CAEA,OACE,eACF,CAEA,aACE,kBAAmB,CACnB,yCACF,CAEA,sCAME,mBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CAHpB,QAIF,CAEA,aAEE,gBACF,CAEA,cAEE,mBACF,CAEA,gDAIE,yBACF,CAEA,wHAKE,iBAAkB,CADlB,SAEF,CAEA,uCAEE,qBAAsB,CACtB,SACF,CAEA,+EAIE,0BACF,CAEA,SACE,aAAc,CACd,eACF,CAEA,SAIE,QAAS,CADT,QAAS,CAFT,WAAY,CACZ,SAGF,CAEA,OAQE,aAAc,CAPd,aAAc,CAKd,gBAAiB,CACjB,mBAAoB,CAFpB,mBAAqB,CAFrB,cAAe,CACf,SAAU,CAKV,kBAAmB,CAPnB,UAQF,CAEA,SACE,sBACF,CAEA,kFAEE,WACF,CAEA,cAEE,uBAAwB,CADxB,mBAEF,CAEA,yCACE,uBACF,CAEA,6BAEE,yBAA0B,CAD1B,YAEF,CAEA,OACE,oBACF,CAEA,QAEE,cAAe,CADf,iBAEF,CAEA,SACE,YACF,CAEA,SACE,sBACF,CAEA,0CAgBE,aAAc,CAHd,mBAAoB,CACpB,eAAgB,CAChB,eAAgB,CAHhB,mBAKF,CAEA,OAEE,gBACF,CAEA,OAEE,cACF,CAEA,OAEE,iBACF,CAEA,OAEE,gBACF,CAEA,OAEE,iBACF,CAEA,OAEE,cACF,CAEA,MACE,iBAAkB,CAClB,eACF,CAEA,WACE,cAGF,CAEA,sBAJE,eAAgB,CAChB,eAOF,CAJA,WACE,gBAGF,CAEA,WACE,gBAGF,CAEA,sBAJE,eAAgB,CAChB,eAOF,CAJA,WACE,gBAGF,CAEA,GAGE,QAAS,CACT,8BAAwC,CAFxC,kBAAmB,CADnB,eAIF,CAEA,aAEE,aAAc,CACd,eACF,CAEA,WAGE,wBAAyB,CADzB,YAEF,CAOA,4BAEE,eAAgB,CADhB,cAEF,CAEA,kBACE,oBACF,CACA,mCACE,kBACF,CAEA,YACE,aAAc,CACd,wBACF,CAEA,YAEE,iBAAkB,CADlB,kBAEF,CAEA,mBAGE,aAAc,CAFd,aAAc,CACd,aAEF,CACA,0BACE,oBACF,CAOA,0BAHE,WAAY,CADZ,cAWF,CAPA,eAEE,qBAAsB,CACtB,wBAAyB,CACzB,oBAAsB,CAHtB,cAMF,CAEA,QACE,oBACF,CAEA,YAEE,aAAc,CADd,mBAEF,CAEA,gBAEE,aAAc,CADd,aAEF,CAEA,KAEE,aAAc,CADd,eAAgB,CAEhB,qBACF,CACA,OACE,aACF,CAEA,IAIE,wBAAyB,CACzB,mBAAqB,CAFrB,UAAW,CADX,eAAgB,CADhB,mBAKF,CACA,QAEE,cAAe,CACf,eAAgB,CAFhB,SAGF,CAEA,IAGE,aAAc,CAFd,aAAc,CACd,eAEF,CACA,SAEE,aAAc,CADd,iBAAkB,CAElB,iBACF,CAEA,gBACE,gBAAiB,CACjB,iBACF,CAEA,WAKE,gBAAiB,CADjB,iBAAkB,CADlB,iBAAkB,CADlB,kBAAmB,CADnB,UAKF,CACA,yBACE,WACE,eACF,CACF,CACA,yBACE,WACE,eACF,CACF,CACA,yBACE,WACE,eACF,CACF,CACA,0BACE,WACE,gBACF,CACF,CAEA,iBAKE,gBAAiB,CADjB,iBAAkB,CADlB,iBAAkB,CADlB,kBAAmB,CADnB,UAKF,CAEA,KACE,YAAa,CACb,cAAe,CAEf,iBAAkB,CADlB,kBAEF,CAEA,YAEE,aAAc,CADd,cAEF,CACA,2CAGE,cAAe,CADf,eAEF,CAEA,sqBAyEE,iBAAkB,CADlB,kBAAmB,CAFnB,iBAAkB,CAClB,UAGF,CAEA,KACE,YAAa,CACb,WAAY,CACZ,cACF,CAEA,UACE,aAAc,CAEd,cAAe,CADf,UAEF,CAEA,OACE,iBAAkB,CAClB,kBACF,CAEA,OACE,kBAAmB,CACnB,mBACF,CAEA,OACE,YAAa,CACb,aACF,CAEA,OACE,kBAAmB,CACnB,mBACF,CAEA,OACE,kBAAmB,CACnB,mBACF,CAEA,OACE,YAAa,CACb,aACF,CAEA,OACE,kBAAmB,CACnB,mBACF,CAEA,OACE,kBAAmB,CACnB,mBACF,CAEA,OACE,YAAa,CACb,aACF,CAEA,QACE,kBAAmB,CACnB,mBACF,CAEA,QACE,kBAAmB,CACnB,mBACF,CAEA,QACE,aAAc,CACd,cACF,CAEA,aACE,QACF,CAEA,YACE,QACF,CAEA,SACE,OACF,CAEA,SACE,OACF,CAEA,SACE,OACF,CAEA,SACE,OACF,CAEA,SACE,OACF,CAEA,SACE,OACF,CAEA,SACE,OACF,CAEA,SACE,OACF,CAEA,SACE,OACF,CAEA,SACE,OACF,CAEA,UACE,QACF,CAEA,UACE,QACF,CAEA,UACE,QACF,CAEA,UACE,oBACF,CAEA,UACE,qBACF,CAEA,UACE,eACF,CAEA,UACE,qBACF,CAEA,UACE,qBACF,CAEA,UACE,eACF,CAEA,UACE,qBACF,CAEA,UACE,qBACF,CAEA,UACE,eACF,CAEA,WACE,qBACF,CAEA,WACE,qBACF,CAEA,yBACE,QACE,YAAa,CACb,WAAY,CACZ,cACF,CACA,aACE,aAAc,CAEd,cAAe,CADf,UAEF,CACA,UACE,iBAAkB,CAClB,kBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,WACE,kBAAmB,CACnB,mBACF,CACA,WACE,kBAAmB,CACnB,mBACF,CACA,WACE,aAAc,CACd,cACF,CACA,gBACE,QACF,CACA,eACE,QACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,aACE,QACF,CACA,aACE,QACF,CACA,aACE,QACF,CACA,aACE,aACF,CACA,aACE,oBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,aACE,qBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,aACE,qBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,cACE,qBACF,CACA,cACE,qBACF,CACF,CAEA,yBACE,QACE,YAAa,CACb,WAAY,CACZ,cACF,CACA,aACE,aAAc,CAEd,cAAe,CADf,UAEF,CACA,UACE,iBAAkB,CAClB,kBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,WACE,kBAAmB,CACnB,mBACF,CACA,WACE,kBAAmB,CACnB,mBACF,CACA,WACE,aAAc,CACd,cACF,CACA,gBACE,QACF,CACA,eACE,QACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,aACE,QACF,CACA,aACE,QACF,CACA,aACE,QACF,CACA,aACE,aACF,CACA,aACE,oBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,aACE,qBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,aACE,qBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,cACE,qBACF,CACA,cACE,qBACF,CACF,CAEA,yBACE,QACE,YAAa,CACb,WAAY,CACZ,cACF,CACA,aACE,aAAc,CAEd,cAAe,CADf,UAEF,CACA,UACE,iBAAkB,CAClB,kBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,WACE,kBAAmB,CACnB,mBACF,CACA,WACE,kBAAmB,CACnB,mBACF,CACA,WACE,aAAc,CACd,cACF,CACA,gBACE,QACF,CACA,eACE,QACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,aACE,QACF,CACA,aACE,QACF,CACA,aACE,QACF,CACA,aACE,aACF,CACA,aACE,oBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,aACE,qBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,aACE,qBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,cACE,qBACF,CACA,cACE,qBACF,CACF,CAEA,0BACE,QACE,YAAa,CACb,WAAY,CACZ,cACF,CACA,aACE,aAAc,CAEd,cAAe,CADf,UAEF,CACA,UACE,iBAAkB,CAClB,kBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,kBAAmB,CACnB,mBACF,CACA,UACE,YAAa,CACb,aACF,CACA,WACE,kBAAmB,CACnB,mBACF,CACA,WACE,kBAAmB,CACnB,mBACF,CACA,WACE,aAAc,CACd,cACF,CACA,gBACE,QACF,CACA,eACE,QACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,YACE,OACF,CACA,aACE,QACF,CACA,aACE,QACF,CACA,aACE,QACF,CACA,aACE,aACF,CACA,aACE,oBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,aACE,qBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,aACE,qBACF,CACA,aACE,qBACF,CACA,aACE,eACF,CACA,cACE,qBACF,CACA,cACE,qBACF,CACF,CAEA,OAGE,wBAA6B,CAD7B,kBAAmB,CADnB,UAGF,CACA,oBAIE,4BAA6B,CAF7B,cAAgB,CAChB,kBAEF,CACA,gBAEE,+BAAgC,CADhC,qBAEF,CACA,mBACE,4BACF,CACA,cACE,qBACF,CAEA,0BAEE,aACF,CAKA,sDAEE,wBACF,CACA,kDAEE,uBACF,CAEA,mGAIE,QACF,CAEA,yCACE,0BACF,CAEA,4BACE,iCACF,CAEA,mDAGE,wBACF,CAEA,uFAIE,oBACF,CAKA,4GAEE,wBACF,CAEA,yDAGE,wBACF,CAEA,+FAIE,oBACF,CAKA,kHAEE,wBACF,CAEA,mDAGE,wBACF,CAEA,uFAIE,oBACF,CAKA,4GAEE,wBACF,CAEA,0CAGE,wBACF,CAEA,2EAIE,oBACF,CAKA,mGAEE,wBACF,CAEA,mDAGE,wBACF,CAEA,uFAIE,oBACF,CAKA,4GAEE,wBACF,CAEA,gDAGE,wBACF,CAEA,mFAIE,oBACF,CAKA,yGAEE,wBACF,CAEA,6CAGE,wBACF,CAEA,+EAIE,oBACF,CAKA,sGAEE,wBACF,CAEA,0CAGE,wBACF,CAEA,2EAIE,oBACF,CAKA,mGAEE,wBACF,CAWA,yJAEE,iCACF,CAEA,sBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,uBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CAEA,YAEE,wBAAyB,CADzB,UAEF,CACA,mDAGE,oBACF,CACA,2BACE,QACF,CACA,oDACE,0BACF,CACA,uCACE,qCACF,CAEA,4BACE,qBAIE,gCAAiC,CACjC,2CAA4C,CAJ5C,aAAc,CAEd,eAAgB,CADhB,UAIF,CACA,qCACE,QACF,CACF,CAEA,4BACE,qBAIE,gCAAiC,CACjC,2CAA4C,CAJ5C,aAAc,CAEd,eAAgB,CADhB,UAIF,CACA,qCACE,QACF,CACF,CAEA,4BACE,qBAIE,gCAAiC,CACjC,2CAA4C,CAJ5C,aAAc,CAEd,eAAgB,CADhB,UAIF,CACA,qCACE,QACF,CACF,CAEA,6BACE,qBAIE,gCAAiC,CACjC,2CAA4C,CAJ5C,aAAc,CAEd,eAAgB,CADhB,UAIF,CACA,qCACE,QACF,CACF,CAEA,kBAIE,gCAAiC,CACjC,2CAA4C,CAJ5C,aAAc,CAEd,eAAgB,CADhB,UAIF,CACA,kCACE,QACF,CAEA,cAUE,2BAA4B,CAD5B,qBAAsB,CAEtB,wBAAyB,CACzB,oBAAsB,CAJtB,aAAc,CAPd,aAAc,CAId,cAAe,CACf,eAAgB,CAHhB,0BAA2B,CAI3B,eAAgB,CAHhB,sBAAyB,CASzB,oEAAwE,CAXxE,UAYF,CACA,kDACE,cACE,eACF,CACF,CACA,0BACE,wBAA6B,CAC7B,QACF,CACA,oBAEE,qBAAsB,CACtB,oBAAqB,CAErB,gCAA+C,CAJ/C,aAAc,CAGd,SAEF,CACA,2BACE,aAAc,CACd,SACF,CACA,+CAEE,wBAAyB,CACzB,SACF,CAEA,qCAEE,qBAAsB,CADtB,aAEF,CAEA,uCAEE,aAAc,CACd,UACF,CAEA,gBAIE,iBAAkB,CAClB,eAAgB,CAFhB,eAAgB,CADhB,kCAAoC,CADpC,+BAKF,CAEA,mBAGE,iBAAkB,CAClB,eAAgB,CAFhB,gCAAkC,CADlC,6BAIF,CAEA,mBAGE,iBAAmB,CACnB,eAAgB,CAFhB,iCAAmC,CADnC,8BAIF,CAEA,wBAQE,wBAA6B,CAE7B,kBAAmB,CAAnB,kBAAmB,CAHnB,aAAc,CANd,aAAc,CAKd,eAAgB,CADhB,eAAgB,CADhB,sBAAwB,CADxB,mBAAqB,CADrB,UASF,CACA,gFAGE,cAAe,CADf,eAEF,CAEA,iBAKE,mBAAqB,CAFrB,iBAAmB,CAFnB,4BAA6B,CAG7B,eAAgB,CAFhB,oBAIF,CAEA,iBAKE,mBAAqB,CAFrB,iBAAkB,CAFlB,2BAA4B,CAG5B,eAAgB,CAFhB,kBAIF,CAOA,8EACE,WACF,CAEA,YACE,kBACF,CAEA,WACE,aAAc,CACd,iBACF,CAEA,UACE,YAAa,CACb,cAAe,CAEf,gBAAiB,CADjB,iBAEF,CACA,uCAGE,gBAAiB,CADjB,iBAEF,CAEA,YAEE,aAAc,CACd,oBAAqB,CAFrB,iBAGF,CAEA,kBAGE,oBAAqB,CADrB,gBAAkB,CADlB,iBAGF,CACA,6CACE,aACF,CAEA,kBACE,eACF,CAEA,mBAEE,kBAAmB,CADnB,mBAAoB,CAGpB,mBAAqB,CADrB,cAEF,CACA,qCAIE,aAAc,CADd,qBAAuB,CADvB,YAAa,CADb,eAIF,CAEA,gBAKE,aAAc,CAJd,YAAa,CAGb,aAAc,CADd,iBAAmB,CADnB,UAIF,CAEA,eAWE,0BAAwC,CACxC,oBAAsB,CAFtB,UAAW,CANX,YAAa,CAIb,iBAAmB,CACnB,eAAgB,CAFhB,gBAAkB,CAFlB,cAAe,CACf,oBAAuB,CALvB,iBAAkB,CAClB,QAAS,CACT,SAUF,CAEA,0DAOE,wQAA4P,CAF5P,yCAAmD,CADnD,2BAA4B,CAE5B,iCAAoD,CAJpD,oBAAqB,CACrB,qBAKF,CACA,sEAEE,oBAAqB,CACrB,gCACF,CACA,kLAIE,aACF,CAEA,0EAGE,+CAAkE,CADlE,qBAEF,CAEA,4DAIE,kgBAGoD,CALpD,oBAAqB,CACrB,uBAKF,CACA,wEAEE,oBAAqB,CACrB,gCACF,CAQA,4XAIE,aACF,CAEA,sGAEE,aACF,CAEA,kMAIE,aACF,CAEA,sHAEE,aACF,CACA,oIAEE,oBACF,CAEA,kNAIE,aACF,CAEA,oJAGE,wBAAyB,CADzB,oBAEF,CAEA,gJAEE,gCACF,CAOA,sRAEE,oBACF,CAEA,sMAIE,aACF,CAEA,sHAEE,oBAAqB,CACrB,gCACF,CAEA,kBAKE,aAAc,CAJd,YAAa,CAGb,aAAc,CADd,iBAAmB,CADnB,UAIF,CAEA,iBAWE,0BAAwC,CACxC,oBAAsB,CAFtB,UAAW,CANX,YAAa,CAIb,iBAAmB,CACnB,eAAgB,CAFhB,gBAAkB,CAFlB,cAAe,CACf,oBAAuB,CALvB,iBAAkB,CAClB,QAAS,CACT,SAUF,CAEA,8DAOE,mTAAsS,CAFtS,yCAAmD,CADnD,2BAA4B,CAE5B,iCAAoD,CAJpD,oBAAqB,CACrB,qBAKF,CACA,0EAEE,oBAAqB,CACrB,gCACF,CACA,kMAIE,aACF,CAEA,8EAGE,+CAAkE,CADlE,qBAEF,CAEA,gEAIE,6iBAGoD,CALpD,oBAAqB,CACrB,uBAKF,CACA,4EAEE,oBAAqB,CACrB,gCACF,CAQA,4ZAIE,aACF,CAEA,0GAEE,aACF,CAEA,kNAIE,aACF,CAEA,0HAEE,aACF,CACA,wIAEE,oBACF,CAEA,kOAIE,aACF,CAEA,wJAGE,wBAAyB,CADzB,oBAEF,CAEA,oJAEE,gCACF,CAOA,8RAEE,oBACF,CAEA,sNAIE,aACF,CAEA,0HAEE,oBAAqB,CACrB,gCACF,CAEA,aAGE,kBAAmB,CAFnB,YAAa,CACb,kBAEF,CACA,yBACE,UACF,CACA,yBACE,mBAGE,sBAEF,CACA,4CAJE,kBAAmB,CADnB,YAAa,CAGb,eAQF,CANA,yBAEE,aAAc,CACd,kBAGF,CACA,2BACE,oBAAqB,CAErB,qBAAsB,CADtB,UAEF,CACA,qCACE,oBACF,CACA,sDAEE,UACF,CACA,yBAEE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CAEvB,cAAe,CADf,UAEF,CACA,+BAIE,aAAc,CADd,mBAAqB,CADrB,YAAa,CADb,iBAIF,CACA,6BACE,kBAAmB,CACnB,sBACF,CACA,mCACE,eACF,CACF,CAEA,KAOE,wBAA6B,CAC7B,sBAA6B,CAI7B,oBAAsB,CATtB,aAAc,CAFd,oBAAqB,CASrB,cAAe,CARf,eAAgB,CAShB,eAAgB,CAFhB,sBAAyB,CALzB,iBAAkB,CASlB,6HAC8D,CAR9D,wBAAiB,CAAjB,gBAAiB,CADjB,qBAUF,CACA,kDACE,KACE,eACF,CACF,CACA,WACE,aAAc,CACd,oBACF,CACA,sBAGE,gCAA+C,CAD/C,SAEF,CACA,4BAEE,WACF,CACA,mCACE,cACF,CAEA,uCAEE,mBACF,CAEA,aAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,mBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,sCAEE,gCACF,CACA,4CAGE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,uIAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,yJAGE,gCACF,CAEA,eAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,qBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,0CAEE,gCACF,CACA,gDAGE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,6IAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,+JAGE,gCACF,CAEA,aAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,mBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,sCAEE,gCACF,CACA,4CAGE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,uIAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,yJAGE,gCACF,CAEA,UAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,gBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,gCAEE,gCACF,CACA,sCAGE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,8HAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,gJAGE,gCACF,CAEA,aAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,mBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,sCAEE,gCACF,CACA,4CAGE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,uIAIE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,yJAGE,gCACF,CAEA,YAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,kBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,oCAEE,gCACF,CACA,0CAGE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,oIAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,sJAGE,gCACF,CAEA,WAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,iBAEE,wBAA2B,CAC3B,oBAAqB,CAFrB,aAGF,CACA,kCAEE,gCACF,CACA,wCAGE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,iIAIE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,mJAGE,gCACF,CAEA,UAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,gBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,gCAEE,gCACF,CACA,sCAGE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,8HAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,gJAGE,gCACF,CAEA,qBAEE,oBAAqB,CADrB,aAEF,CACA,2BAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,sDAEE,gCACF,CACA,4DAGE,wBAA6B,CAD7B,aAEF,CACA,+JAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,iLAGE,gCACF,CAEA,uBAEE,oBAAqB,CADrB,aAEF,CACA,6BAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,0DAEE,gCACF,CACA,gEAGE,wBAA6B,CAD7B,aAEF,CACA,qKAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,uLAGE,gCACF,CAEA,qBAEE,oBAAqB,CADrB,aAEF,CACA,2BAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,sDAEE,gCACF,CACA,4DAGE,wBAA6B,CAD7B,aAEF,CACA,+JAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,iLAGE,gCACF,CAEA,kBAEE,oBAAqB,CADrB,aAEF,CACA,wBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,gDAEE,gCACF,CACA,sDAGE,wBAA6B,CAD7B,aAEF,CACA,sJAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,wKAGE,gCACF,CAEA,qBAEE,oBAAqB,CADrB,aAEF,CACA,2BAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,sDAEE,gCACF,CACA,4DAGE,wBAA6B,CAD7B,aAEF,CACA,+JAIE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,iLAGE,gCACF,CAEA,oBAEE,oBAAqB,CADrB,aAEF,CACA,0BAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,oDAEE,gCACF,CACA,0DAGE,wBAA6B,CAD7B,aAEF,CACA,4JAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,8KAGE,gCACF,CAEA,mBAEE,oBAAqB,CADrB,aAEF,CACA,yBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,kDAEE,gCACF,CACA,wDAGE,wBAA6B,CAD7B,aAEF,CACA,yJAIE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,2KAGE,gCACF,CAEA,kBAEE,oBAAqB,CADrB,aAEF,CACA,wBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,gDAEE,gCACF,CACA,sDAGE,wBAA6B,CAD7B,aAEF,CACA,sJAIE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,wKAGE,gCACF,CAEA,UAEE,aAAc,CADd,eAEF,CACA,gBACE,aAAc,CACd,yBACF,CACA,gCAGE,eAAgB,CADhB,yBAEF,CACA,sCAEE,aAAc,CACd,mBACF,CAEA,2BAKE,mBAAqB,CAFrB,iBAAkB,CAClB,eAAgB,CAFhB,kBAIF,CAEA,2BAKE,mBAAqB,CAFrB,iBAAmB,CACnB,eAAgB,CAFhB,oBAIF,CAEA,WACE,aAAc,CACd,UACF,CACA,sBACE,gBACF,CAEA,sFAGE,UACF,CAEA,MACE,8BACF,CACA,kDACE,MACE,eACF,CACF,CACA,iBACE,SACF,CAEA,qBACE,YACF,CAEA,YAEE,QAAS,CACT,eAAgB,CAFhB,iBAAkB,CAGlB,2BACF,CACA,kDACE,YACE,eACF,CACF,CAEA,uCAIE,iBACF,CAEA,uBAOE,eAAgB,CAChB,4BAAoC,CAFpC,6BAAqC,CADrC,qBAAuB,CADvB,UAAW,CAHX,oBAAqB,CACrB,kBAAoB,CACpB,qBAMF,CAEA,6BACE,aACF,CAEA,eAeE,2BAA4B,CAD5B,qBAAsB,CAEtB,0BAAqC,CACrC,oBAAsB,CANtB,aAAc,CANd,YAAa,CACb,UAAW,CAIX,cAAe,CAPf,MAAO,CAUP,eAAgB,CAJhB,kBAAoB,CAFpB,eAAgB,CAChB,eAAiB,CAPjB,iBAAkB,CAWlB,eAAgB,CAVhB,QAAS,CAET,YAcF,CAEA,qBAEE,SAAU,CADV,OAEF,CAEA,yBACE,wBAEE,SAAU,CADV,OAEF,CACF,CAEA,yBACE,wBAEE,SAAU,CADV,OAEF,CACF,CAEA,yBACE,wBAEE,SAAU,CADV,OAEF,CACF,CAEA,0BACE,wBAEE,SAAU,CADV,OAEF,CACF,CAEA,oBAEE,MAAO,CADP,UAEF,CAEA,yBACE,uBAEE,MAAO,CADP,UAEF,CACF,CAEA,yBACE,uBAEE,MAAO,CADP,UAEF,CACF,CAEA,yBACE,uBAEE,MAAO,CADP,UAEF,CACF,CAEA,0BACE,uBAEE,MAAO,CADP,UAEF,CACF,CAEA,uBAEE,WAAY,CAEZ,qBAAuB,CADvB,YAAa,CAFb,QAIF,CAEA,+BAOE,wBAA0B,CAC1B,4BAAoC,CAFpC,6BAAqC,CADrC,YAAa,CADb,UAAW,CAHX,oBAAqB,CACrB,kBAAoB,CACpB,qBAMF,CAEA,qCACE,aACF,CAEA,0BAGE,SAAU,CAEV,mBAAqB,CADrB,YAAa,CAFb,UAAW,CADX,KAKF,CAEA,kCAOE,8BAAsC,CACtC,sBAAwB,CAFxB,cAAe,CADf,2BAAmC,CADnC,UAAW,CAHX,oBAAqB,CACrB,kBAAoB,CACpB,qBAMF,CAEA,wCACE,aACF,CAEA,kCACE,gBACF,CAEA,yBAGE,SAAU,CAEV,oBAAsB,CADtB,YAAa,CAFb,UAAW,CADX,KAKF,CAEA,iCAIE,UAAW,CAHX,oBAAqB,CAOrB,YAAa,CANb,kBAAoB,CACpB,qBAEF,CAMA,kCAOE,8BAAsC,CADtC,uBAAyB,CADzB,2BAAmC,CADnC,UAAW,CAHX,oBAAqB,CACrB,mBAAqB,CACrB,qBAKF,CAEA,uCACE,aACF,CAEA,kCACE,gBACF,CAEA,0IAKE,WAAY,CADZ,UAEF,CAEA,kBAIE,4BAA6B,CAH7B,QAAS,CACT,cAAgB,CAChB,eAEF,CAEA,eASE,wBAA6B,CAC7B,QAAS,CANT,UAAW,CAEX,aAAc,CALd,aAAc,CAId,eAAgB,CAFhB,qBAAuB,CAIvB,kBAAmB,CACnB,kBAAmB,CANnB,UASF,CACA,2BACE,yCAA2C,CAC3C,0CACF,CACA,0BAEE,4CAA8C,CAD9C,6CAEF,CACA,0CAIE,wBAAyB,CAFzB,aAAc,CACd,oBAEF,CACA,4CAIE,wBAAyB,CAFzB,UAAW,CACX,oBAEF,CACA,gDAIE,wBAA6B,CAF7B,aAAc,CACd,mBAEF,CAEA,oBACE,aACF,CAEA,iBAKE,aAAc,CAJd,aAAc,CAGd,iBAAmB,CADnB,eAAgB,CADhB,oBAAsB,CAItB,kBACF,CAEA,oBAGE,aAAc,CAFd,aAAc,CACd,qBAEF,CAEA,+BAGE,mBAAoB,CADpB,iBAAkB,CAElB,qBACF,CACA,yCAGE,aAAc,CADd,iBAEF,CAKA,wNAME,SACF,CAEA,aACE,YAAa,CACb,cAAe,CACf,0BACF,CACA,0BACE,UACF,CAEA,0EAEE,gBACF,CAEA,mGAGE,4BAA6B,CAD7B,yBAEF,CAEA,+EAGE,2BAA4B,CAD5B,wBAEF,CAEA,uBAEE,qBAAuB,CADvB,sBAEF,CACA,0GAGE,aACF,CACA,wCACE,cACF,CAEA,yEAGE,oBAAsB,CADtB,qBAEF,CAEA,yEAGE,mBAAqB,CADrB,oBAEF,CAEA,oBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,sBACF,CACA,wDAEE,UACF,CACA,4FAEE,eACF,CACA,qHAGE,2BAA4B,CAD5B,4BAEF,CACA,iGAEE,wBAAyB,CACzB,yBACF,CAEA,yDAEE,eACF,CACA,gMAKE,kBAAsB,CACtB,mBAAoB,CAFpB,iBAGF,CAEA,aAIE,mBAAoB,CAFpB,YAAa,CACb,cAAe,CAFf,iBAAkB,CAIlB,UACF,CACA,sHAKE,aAAc,CAEd,eAAgB,CAHhB,iBAAkB,CAElB,QAEF,CACA,0gBAYE,gBACF,CACA,yIAGE,SACF,CACA,mDACE,SACF,CACA,yFAGE,4BAA6B,CAD7B,yBAEF,CACA,2FAGE,2BAA4B,CAD5B,wBAEF,CACA,0BAEE,kBAAmB,CADnB,YAEF,CACA,kIAGE,4BAA6B,CAD7B,yBAEF,CACA,+DAEE,2BAA4B,CAD5B,wBAEF,CAEA,yCAEE,YACF,CACA,mDAEE,iBAAkB,CAClB,SACF,CACA,+DAEE,SACF,CACA,4VAQE,gBACF,CAEA,qBACE,iBACF,CAEA,oBACE,gBACF,CAEA,kBAEE,kBAAmB,CASnB,wBAAyB,CACzB,wBAAyB,CACzB,oBAAsB,CALtB,aAAc,CAPd,YAAa,CAIb,cAAe,CACf,eAAgB,CAChB,eAAgB,CAHhB,eAAgB,CADhB,sBAAyB,CAMzB,iBAAkB,CAClB,kBAIF,CACA,2EAEE,YACF,CAEA,2EAEE,2BACF,CAEA,6PASE,mBAAqB,CAFrB,iBAAkB,CAClB,eAAgB,CAFhB,kBAIF,CAEA,2EAEE,4BACF,CAEA,6PASE,mBAAqB,CAFrB,iBAAmB,CACnB,eAAgB,CAFhB,oBAIF,CAEA,8DAEE,qBACF,CAEA,6XAOE,4BAA6B,CAD7B,yBAEF,CAEA,+WAOE,2BAA4B,CAD5B,wBAEF,CAEA,gBAEE,aAAc,CACd,iBAAkB,CAClB,mBAAoB,CAHpB,iBAIF,CAEA,uBACE,mBAAoB,CACpB,iBACF,CAEA,sBAGE,SAAU,CAFV,iBAAkB,CAClB,UAEF,CACA,2DAGE,wBAAyB,CADzB,oBAAqB,CADrB,UAGF,CACA,yDACE,gCACF,CACA,uEACE,oBACF,CACA,yEAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CACA,qDACE,aACF,CACA,4DACE,wBACF,CAEA,sBAEE,eAAgB,CADhB,iBAAkB,CAElB,kBACF,CACA,6BASE,qBAAsB,CACtB,wBAAyB,CAHzB,mBAIF,CACA,yDAJE,UAAW,CAJX,aAAc,CAEd,WAAY,CAHZ,YAAa,CAFb,iBAAkB,CAClB,UAAY,CAGZ,UAkBF,CAXA,4BASE,uBAAkC,CADlC,2BAA4B,CAE5B,uBACF,CAEA,8CACE,oBACF,CAEA,2EACE,yNACF,CAEA,kFAEE,wBAAyB,CADzB,oBAEF,CAEA,iFACE,uKACF,CAMA,gLACE,0BACF,CAEA,2CACE,iBACF,CAEA,wEACE,oKACF,CAEA,kFACE,0BACF,CAEA,eACE,oBACF,CACA,4CAIE,mBAAqB,CAHrB,aAAc,CAEd,kBAAmB,CADnB,aAGF,CACA,2CAKE,wBAAyB,CACzB,mBAAqB,CAFrB,uBAAwB,CAFxB,yBAA0B,CAD1B,sBAAwB,CAMxB,iIAC8D,CAL9D,sBAMF,CACA,kDACE,2CACE,eACF,CACF,CACA,yEACE,qBAAsB,CACtB,4BACF,CACA,mFACE,0BACF,CAEA,eAcE,uBAAgB,CAAhB,eAAgB,CALhB,yNACyC,CACzC,qBAAsB,CACtB,wBAAyB,CACzB,oBAAsB,CANtB,aAAc,CANd,oBAAqB,CAIrB,eAAgB,CAFhB,0BAA2B,CAG3B,eAAgB,CAFhB,sCAA0C,CAI1C,qBAAsB,CANtB,UAaF,CACA,qBACE,oBAAqB,CAErB,gCAAiD,CADjD,SAEF,CACA,gCAEE,qBAAsB,CADtB,aAEF,CACA,8DAIE,qBAAsB,CAFtB,WAAY,CACZ,oBAEF,CACA,wBAEE,wBAAyB,CADzB,aAEF,CACA,2BACE,SACF,CAEA,kBAKE,iBAAmB,CAJnB,4BAA6B,CAE7B,qBAAuB,CACvB,kBAAoB,CAFpB,kBAIF,CAEA,kBAKE,iBAAkB,CAJlB,2BAA4B,CAE5B,oBAAsB,CACtB,iBAAkB,CAFlB,iBAIF,CAEA,aAEE,oBAAqB,CAGrB,eACF,CAEA,gCAJE,0BAA2B,CAH3B,iBAAkB,CAElB,UAYF,CAPA,mBAKE,QAAS,CACT,SAAU,CAJV,SAKF,CACA,4CACE,oBAAqB,CACrB,gCACF,CACA,+CACE,wBACF,CACA,qDACE,gBACF,CACA,yDACE,yBACF,CAEA,mBAWE,qBAAsB,CACtB,wBAAyB,CACzB,oBAAsB,CALtB,eAAgB,CAFhB,0BAA2B,CAF3B,MAAO,CACP,SASF,CACA,4CALE,aAAc,CADd,eAAgB,CAFhB,sBAAyB,CANzB,iBAAkB,CAElB,OAAQ,CADR,KA4BF,CAfA,yBAYE,wBAAyB,CACzB,mBAAoB,CACpB,+BAAkC,CAVlC,QAAS,CAOT,gBAAiB,CALjB,aAAc,CACd,cAAe,CAFf,SAUF,CAEA,cAKE,uBAAgB,CAAhB,eAAgB,CADhB,wBAA6B,CAF7B,aAA2B,CAC3B,SAAU,CAFV,UAKF,CACA,oBACE,YACF,CACA,0CACE,+CACF,CACA,sCACE,+CACF,CACA,+BACE,+CACF,CACA,gCACE,QACF,CACA,oCASE,uBAAgB,CAAhB,eAAgB,CALhB,wBAAyB,CACzB,QAAS,CACT,kBAAmB,CAJnB,WAAY,CACZ,kBAAoB,CAIpB,8GAC8B,CAD9B,sGAC8B,CAP9B,UASF,CACA,kDACE,oCACE,uBAAgB,CAAhB,eACF,CACF,CACA,2CACE,wBACF,CACA,6CAKE,wBAAyB,CACzB,kBAAyB,CACzB,kBAAmB,CAJnB,WAAkB,CAClB,cAAe,CAFf,YAAc,CADd,UAOF,CACA,gCAQE,eAAgB,CALhB,wBAAyB,CACzB,QAAS,CACT,kBAAmB,CAHnB,WAAY,CAIZ,2GAC8B,CAD9B,sGAC8B,CAN9B,UAQF,CACA,kDACE,gCACE,oBAAgB,CAAhB,eACF,CACF,CACA,uCACE,wBACF,CACA,gCAKE,wBAAyB,CACzB,kBAAyB,CACzB,kBAAmB,CAJnB,WAAkB,CAClB,cAAe,CAFf,YAAc,CADd,UAOF,CACA,yBAWE,eAAgB,CALhB,wBAAyB,CACzB,QAAS,CACT,kBAAmB,CANnB,WAAY,CAGZ,iBAAmB,CADnB,kBAAoB,CADpB,YAAa,CAMb,0GAC8B,CAD9B,sGAC8B,CAT9B,UAWF,CACA,kDACE,yBACE,mBAAgB,CAAhB,eACF,CACF,CACA,gCACE,wBACF,CACA,yBAKE,wBAA6B,CAC7B,kBAAyB,CACzB,kBAAoB,CAJpB,WAAkB,CAClB,cAAe,CAFf,YAAc,CADd,UAOF,CAKA,4DAHE,wBAAyB,CACzB,kBAMF,CAJA,8BACE,iBAGF,CACA,6CACE,wBACF,CACA,sDACE,cACF,CACA,yCACE,wBACF,CACA,yCACE,cACF,CACA,kCACE,wBACF,CAEA,+DAGE,sGAEF,CACA,kDACE,+DAGE,eACF,CACF,CAEA,KACE,YAAa,CACb,cAAe,CAGf,eAAgB,CADhB,eAAgB,CADhB,cAGF,CAEA,UACE,aAAc,CACd,kBACF,CACA,gCAEE,oBACF,CACA,mBACE,aAAc,CAEd,cAAe,CADf,mBAEF,CAEA,UACE,+BACF,CACA,oBACE,kBACF,CACA,oBACE,sBAA6B,CAC7B,6BAA+B,CAC/B,8BACF,CACA,oDAEE,oCACF,CACA,6BAEE,wBAA6B,CAC7B,kBAAyB,CAFzB,aAGF,CACA,8DAGE,qBAAsB,CACtB,iCAAkC,CAFlC,aAGF,CACA,yBAEE,wBAAyB,CACzB,yBAA0B,CAF1B,eAGF,CAEA,qBACE,oBACF,CAEA,uDAGE,wBAAyB,CADzB,UAEF,CAEA,oBACE,aAAc,CACd,iBACF,CAEA,yBACE,YAAa,CACb,WAAY,CACZ,iBACF,CAEA,uBACE,YACF,CAEA,qBACE,aACF,CAEA,QAME,kBAAoB,CALpB,iBAMF,CACA,oDAJE,kBAAmB,CAFnB,YAAa,CACb,cAAe,CAEf,6BASF,CAEA,cACE,oBAAqB,CAIrB,iBAAkB,CAClB,mBAAoB,CAFpB,iBAAkB,CADlB,uBAAyB,CADzB,oBAAsB,CAKtB,kBACF,CACA,wCAEE,oBACF,CAEA,YACE,YAAa,CACb,qBAAsB,CAGtB,eAAgB,CADhB,eAAgB,CADhB,cAGF,CACA,sBAEE,cAAe,CADf,eAEF,CACA,2BAEE,UAAW,CADX,eAEF,CAEA,aACE,oBAAqB,CAErB,oBAAsB,CADtB,iBAEF,CAEA,iBAGE,kBAAmB,CAFnB,eAAgB,CAChB,WAEF,CAEA,gBAIE,wBAA6B,CAC7B,sBAA6B,CAC7B,oBAAsB,CAJtB,iBAAkB,CAClB,aAAc,CAFd,qBAMF,CACA,4CAEE,oBACF,CACA,8CACE,cACF,CAEA,qBAME,wBAAmC,CACnC,yBAA0B,CAF1B,UAAW,CAJX,oBAAqB,CAErB,YAAa,CACb,qBAAsB,CAFtB,WAMF,CAEA,4BACE,gEAGE,cAAe,CADf,eAEF,CACF,CAEA,yBACE,kBACE,oBAAqB,CACrB,0BACF,CACA,8BACE,kBACF,CACA,6CACE,iBACF,CACA,wCAEE,kBAAoB,CADpB,mBAEF,CACA,gEAEE,gBACF,CACA,mCACE,sBAAwB,CACxB,eACF,CACA,kCACE,YACF,CACF,CAEA,4BACE,gEAGE,cAAe,CADf,eAEF,CACF,CAEA,yBACE,kBACE,oBAAqB,CACrB,0BACF,CACA,8BACE,kBACF,CACA,6CACE,iBACF,CACA,wCAEE,kBAAoB,CADpB,mBAEF,CACA,gEAEE,gBACF,CACA,mCACE,sBAAwB,CACxB,eACF,CACA,kCACE,YACF,CACF,CAEA,4BACE,gEAGE,cAAe,CADf,eAEF,CACF,CAEA,yBACE,kBACE,oBAAqB,CACrB,0BACF,CACA,8BACE,kBACF,CACA,6CACE,iBACF,CACA,wCAEE,kBAAoB,CADpB,mBAEF,CACA,gEAEE,gBACF,CACA,mCACE,sBAAwB,CACxB,eACF,CACA,kCACE,YACF,CACF,CAEA,6BACE,gEAGE,cAAe,CADf,eAEF,CACF,CAEA,0BACE,kBACE,oBAAqB,CACrB,0BACF,CACA,8BACE,kBACF,CACA,6CACE,iBACF,CACA,wCAEE,kBAAoB,CADpB,mBAEF,CACA,gEAEE,gBACF,CACA,mCACE,sBAAwB,CACxB,eACF,CACA,kCACE,YACF,CACF,CAEA,eACE,oBAAqB,CACrB,0BACF,CACA,0DAGE,cAAe,CADf,eAEF,CACA,2BACE,kBACF,CACA,0CACE,iBACF,CACA,qCAEE,kBAAoB,CADpB,mBAEF,CACA,0DAEE,gBACF,CACA,gCACE,sBAAwB,CACxB,eACF,CACA,+BACE,YACF,CAKA,gGAEE,eACF,CAEA,oCACE,eACF,CACA,oFAEE,eACF,CACA,6CACE,eACF,CAEA,0KAIE,eACF,CAEA,8BAEE,sBAAgC,CADhC,eAEF,CAEA,mCACE,qQACF,CAEA,2BACE,eACF,CAIA,mGAEE,eACF,CAKA,6FAEE,UACF,CAEA,mCACE,eACF,CACA,kFAEE,eACF,CACA,4CACE,eACF,CAEA,sKAIE,UACF,CAEA,6BAEE,sBAAsC,CADtC,eAEF,CAEA,kCACE,2QACF,CAEA,0BACE,eACF,CAIA,gGAEE,UACF,CAEA,MAKE,oBAAqB,CAErB,uBAA2B,CAD3B,qBAAsB,CAEtB,iCAAsC,CACtC,oBAAsB,CAPtB,YAAa,CACb,qBAAsB,CACtB,WAAY,CAHZ,iBASF,CACA,SAEE,aAAc,CADd,cAEF,CACA,2DACE,6BAA+B,CAC/B,8BACF,CACA,yDAEE,gCAAkC,CADlC,iCAEF,CAEA,WACE,aAAc,CACd,eACF,CAEA,YACE,oBACF,CAEA,eACE,mBAEF,CAEA,qCAHE,eAKF,CAEA,iBACE,oBACF,CAEA,sBACE,mBACF,CAEA,aAIE,0BAAqC,CACrC,wCAA6C,CAF7C,aAAc,CADd,eAAgB,CADhB,sBAKF,CACA,yBACE,uDACF,CACA,sDACE,YACF,CAEA,aAEE,0BAAqC,CACrC,qCAA0C,CAF1C,sBAGF,CACA,wBACE,uDACF,CAEA,kBAIE,eAAgB,CAFhB,qBAGF,CAEA,qCAJE,oBAAsB,CAFtB,qBASF,CAEA,kBAIE,QAAS,CACT,MAAO,CACP,eAAgB,CALhB,iBAAkB,CAElB,OAAQ,CADR,KAKF,CAEA,UAEE,gCAAkC,CADlC,UAEF,CAEA,cAEE,yCAA2C,CAC3C,0CAA4C,CAF5C,UAGF,CAEA,iBAGE,4CAA8C,CAD9C,6CAA+C,CAD/C,UAGF,CAEA,WACE,YAAa,CACb,qBACF,CACA,iBACE,kBACF,CACA,yBACE,WACE,kBAAmB,CAEnB,iBAAkB,CADlB,kBAEF,CACA,iBACE,YAAa,CACb,QAAY,CACZ,qBAAsB,CAEtB,eAAgB,CAChB,gBAAiB,CAFjB,iBAGF,CACF,CAEA,YACE,YAAa,CACb,qBACF,CACA,kBACE,kBACF,CACA,yBACE,YACE,kBACF,CACA,kBACE,QAAY,CACZ,eACF,CACA,wBAEE,aAAc,CADd,aAEF,CACA,8BAEE,4BAA6B,CAD7B,yBAEF,CACA,uFAEE,yBACF,CACA,0FAEE,4BACF,CACA,6BAEE,2BAA4B,CAD5B,wBAEF,CACA,qFAEE,wBACF,CACA,wFAEE,2BACF,CACA,6BACE,oBACF,CACA,qFAEE,6BAA+B,CAC/B,8BACF,CACA,wFAGE,gCAAkC,CADlC,iCAEF,CAIA,uZAIE,eACF,CACF,CAEA,oBACE,oBACF,CAEA,yBACE,cACE,cAAe,CACf,kBAAmB,CACnB,SAAU,CACV,QACF,CACA,oBACE,oBAAqB,CACrB,UACF,CACF,CAEA,iBACE,eACF,CACA,8DACE,eACF,CACA,wDACE,eAAgB,CAChB,eACF,CACA,+BACE,eAAgB,CAEhB,2BAA4B,CAD5B,4BAEF,CACA,8BACE,wBAAyB,CACzB,yBACF,CACA,8BACE,kBACF,CAEA,YAME,wBAAyB,CACzB,oBAAsB,CANtB,YAAa,CACb,cAAe,CAGf,eAAgB,CADhB,kBAAmB,CADnB,mBAKF,CAEA,kCACE,kBACF,CACA,yCAGE,aAAc,CACd,WAAY,CAHZ,oBAAqB,CACrB,mBAGF,CAEA,+CACE,yBAA0B,CAI1B,oBAHF,CAMA,wBACE,aACF,CAEA,YAIE,oBAAsB,CAHtB,YAAa,CAEb,eAAgB,CADhB,cAGF,CAEA,WAOE,qBAAsB,CACtB,wBAAyB,CAFzB,aAAc,CAJd,aAAc,CAGd,gBAAiB,CADjB,gBAAiB,CADjB,oBAAuB,CAFvB,iBAQF,CACA,iBAIE,wBAAyB,CACzB,oBAAqB,CAHrB,aAAc,CACd,oBAAqB,CAFrB,SAKF,CACA,iBAGE,gCAA+C,CAD/C,SAAU,CADV,SAGF,CACA,yCACE,cACF,CAEA,kCAGE,gCAAkC,CADlC,6BAA+B,CAD/B,aAGF,CAEA,iCAEE,iCAAmC,CADnC,8BAEF,CAEA,6BAGE,wBAAyB,CACzB,oBAAqB,CAFrB,UAAW,CADX,SAIF,CAEA,+BAIE,qBAAsB,CACtB,oBAAqB,CAJrB,aAAc,CAEd,WAAY,CADZ,mBAIF,CAEA,0BAEE,iBAAkB,CAClB,eAAgB,CAFhB,qBAGF,CAEA,iDAEE,+BAAiC,CADjC,4BAEF,CAEA,gDAEE,gCAAkC,CADlC,6BAEF,CAEA,0BAEE,iBAAmB,CACnB,eAAgB,CAFhB,oBAGF,CAEA,iDAEE,+BAAiC,CADjC,4BAEF,CAEA,gDAEE,gCAAkC,CADlC,6BAEF,CAEA,OASE,oBAAsB,CARtB,oBAAqB,CAErB,aAAc,CACd,eAAgB,CAChB,aAAc,CAHd,kBAAqB,CAIrB,iBAAkB,CAElB,sBAAwB,CADxB,kBAGF,CACA,4BAEE,oBACF,CACA,aACE,YACF,CAEA,YACE,iBAAkB,CAClB,QACF,CAEA,YAGE,mBAAoB,CADpB,iBAAmB,CADnB,kBAGF,CAEA,eAEE,wBAAyB,CADzB,UAEF,CACA,4CAGE,wBAAyB,CADzB,UAEF,CAEA,iBAEE,wBAAyB,CADzB,UAEF,CACA,gDAGE,wBAAyB,CADzB,UAEF,CAEA,eAEE,wBAAyB,CADzB,UAEF,CACA,4CAGE,wBAAyB,CADzB,UAEF,CAEA,YAEE,wBAAyB,CADzB,UAEF,CACA,sCAGE,wBAAyB,CADzB,UAEF,CAEA,eAEE,wBAAyB,CADzB,aAEF,CACA,4CAGE,wBAAyB,CADzB,aAEF,CAEA,cAEE,wBAAyB,CADzB,UAEF,CACA,0CAGE,wBAAyB,CADzB,UAEF,CAEA,aAEE,wBAAyB,CADzB,aAEF,CACA,wCAGE,wBAAyB,CADzB,aAEF,CAEA,YAEE,wBAAyB,CADzB,UAEF,CACA,sCAGE,wBAAyB,CADzB,UAEF,CAEA,WAGE,wBAAyB,CACzB,mBAAqB,CAFrB,kBAAmB,CADnB,iBAIF,CACA,yBACE,WACE,iBACF,CACF,CAEA,iBAGE,eAAgB,CADhB,cAAe,CADf,eAGF,CAEA,OAIE,sBAA6B,CAC7B,oBAAsB,CAFtB,kBAAmB,CADnB,sBAAwB,CADxB,iBAKF,CAEA,eACE,aACF,CAEA,YACE,eACF,CAEA,mBACE,kBACF,CACA,0BAKE,aAAc,CADd,sBAAwB,CAHxB,iBAAkB,CAElB,OAAQ,CADR,KAIF,CAEA,eAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,kBACE,wBACF,CACA,2BACE,aACF,CAEA,iBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,oBACE,wBACF,CACA,6BACE,aACF,CAEA,eAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,kBACE,wBACF,CACA,2BACE,aACF,CAEA,YAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,eACE,wBACF,CACA,wBACE,aACF,CAEA,eAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,kBACE,wBACF,CACA,2BACE,aACF,CAEA,cAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,iBACE,wBACF,CACA,0BACE,aACF,CAEA,aAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,gBACE,wBACF,CACA,yBACE,aACF,CAEA,YAEE,wBAAyB,CACzB,oBAAqB,CAFrB,aAGF,CACA,eACE,wBACF,CACA,wBACE,aACF,CAEA,gCACE,GACE,0BACF,CACA,GACE,uBACF,CACF,CAEA,UAKE,wBAAyB,CACzB,oBAAsB,CALtB,YAAa,CAGb,gBAAkB,CAFlB,WAAY,CACZ,eAIF,CAEA,cAOE,wBAAyB,CAHzB,UAAW,CAHX,YAAa,CACb,qBAAsB,CACtB,sBAAuB,CAEvB,iBAAkB,CAGlB,yBAA2B,CAF3B,kBAGF,CACA,kDACE,cACE,eACF,CACF,CAEA,sBACE,+GASC,CACD,yBACF,CAEA,uBACE,iDACF,CAEA,OAEE,sBAAuB,CADvB,YAEF,CAEA,YACE,QACF,CAEA,YACE,YAAa,CACb,qBAAsB,CAEtB,eAAgB,CADhB,cAEF,CAEA,wBAEE,aAAc,CACd,kBAAmB,CAFnB,UAGF,CACA,4DAIE,wBAAyB,CAFzB,aAAc,CACd,oBAEF,CACA,+BAEE,wBAAyB,CADzB,aAEF,CAEA,iBAKE,qBAAsB,CACtB,iCAAsC,CAJtC,aAAc,CAEd,kBAAmB,CADnB,sBAAwB,CAFxB,iBAMF,CACA,6BACE,6BAA+B,CAC/B,8BACF,CACA,4BAGE,gCAAkC,CADlC,iCAAmC,CADnC,eAGF,CACA,8CAGE,oBAAqB,CADrB,SAEF,CACA,oDAIE,qBAAsB,CAFtB,aAAc,CACd,mBAEF,CACA,wBAGE,wBAAyB,CACzB,oBAAqB,CAFrB,UAAW,CADX,SAIF,CAEA,mCAEE,aAAc,CACd,eAAgB,CAFhB,cAGF,CACA,8CACE,kBACF,CAEA,2DACE,YACF,CAEA,yDAEE,eAAgB,CADhB,eAEF,CAEA,yBAEE,wBAAyB,CADzB,aAEF,CACA,4GAGE,wBAAyB,CADzB,aAEF,CACA,uDAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,2BAEE,wBAAyB,CADzB,aAEF,CACA,gHAGE,wBAAyB,CADzB,aAEF,CACA,yDAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,yBAEE,wBAAyB,CADzB,aAEF,CACA,4GAGE,wBAAyB,CADzB,aAEF,CACA,uDAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,sBAEE,wBAAyB,CADzB,aAEF,CACA,sGAGE,wBAAyB,CADzB,aAEF,CACA,oDAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,yBAEE,wBAAyB,CADzB,aAEF,CACA,4GAGE,wBAAyB,CADzB,aAEF,CACA,uDAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,wBAEE,wBAAyB,CADzB,aAEF,CACA,0GAGE,wBAAyB,CADzB,aAEF,CACA,sDAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,uBAEE,wBAAyB,CADzB,aAEF,CACA,wGAGE,wBAAyB,CADzB,aAEF,CACA,qDAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,sBAEE,wBAAyB,CADzB,aAEF,CACA,sGAGE,wBAAyB,CADzB,aAEF,CACA,oDAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,OAKE,UAAW,CAJX,WAAY,CACZ,gBAAiB,CACjB,eAAgB,CAChB,aAAc,CAGd,UAAY,CADZ,wBAEF,CACA,aACE,UAAW,CACX,oBACF,CACA,qCACE,cACF,CACA,sFAEE,WACF,CAEA,aAIE,uBAAgB,CAAhB,eAAgB,CAFhB,wBAA6B,CAC7B,QAAS,CAFT,SAIF,CAEA,iBACE,mBACF,CAEA,OASE,kCAA2B,CAA3B,0BAA2B,CAJ3B,2BAA4B,CAD5B,0BAA2C,CAE3C,0BAAoC,CACpC,oBAAsB,CACtB,oCAAgD,CALhD,iBAAmB,CAFnB,eAAgB,CAShB,SAAU,CARV,eASF,CACA,wBACE,oBACF,CACA,eACE,SACF,CACA,YACE,aAAc,CACd,SACF,CACA,YACE,YACF,CAEA,cAEE,kBAAmB,CAInB,2BAA4B,CAD5B,0BAA2C,CAE3C,iCAA4C,CAH5C,aAAc,CAHd,YAAa,CAEb,qBAKF,CAEA,YACE,cACF,CAEA,YACE,eACF,CACA,mBACE,iBAAkB,CAClB,eACF,CAEA,OAKE,YAAa,CAEb,WAAY,CAJZ,MAAO,CAMP,SAAU,CADV,eAAgB,CAPhB,cAAe,CACf,KAAM,CAIN,UAAW,CAFX,YAMF,CAEA,cAGE,YAAc,CACd,mBAAoB,CAHpB,iBAAkB,CAClB,UAGF,CACA,0BAEE,2BAA8B,CAD9B,iCAEF,CACA,kDACE,0BACE,eACF,CACF,CACA,0BACE,cACF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAEb,4BACF,CACA,8BAGE,UAAW,CAFX,aAAc,CACd,yBAEF,CAEA,eAOE,2BAA4B,CAD5B,qBAAsB,CAEtB,sBAAoC,CACpC,mBAAqB,CAPrB,YAAa,CACb,qBAAsB,CAOtB,SAAU,CALV,mBAAoB,CAJpB,iBAAkB,CAGlB,UAOF,CAEA,gBAOE,qBAAsB,CADtB,YAAa,CAHb,MAAO,CAFP,cAAe,CACf,KAAM,CAGN,WAAY,CADZ,YAIF,CACA,qBACE,SACF,CACA,qBACE,UACF,CAEA,cAEE,sBAAuB,CAGvB,+BAAgC,CAChC,4BAA8B,CAC9B,6BAA+B,CAN/B,YAAa,CAEb,6BAA8B,CAC9B,YAIF,CACA,qBAEE,6BAA8B,CAD9B,YAEF,CAEA,aAEE,eAAgB,CADhB,eAEF,CAEA,YAEE,aAAc,CACd,YAAa,CAFb,iBAGF,CAEA,cAEE,kBAAmB,CAKnB,+BAAiC,CADjC,gCAAkC,CADlC,4BAA6B,CAJ7B,YAAa,CAEb,wBAAyB,CACzB,YAIF,CACA,iCACE,kBACF,CACA,gCACE,mBACF,CAEA,yBAIE,WAAY,CACZ,eAAgB,CAJhB,iBAAkB,CAClB,WAAY,CACZ,UAGF,CAEA,yBACE,cAEE,mBAAoB,CADpB,eAEF,CACA,uBACE,8BACF,CACA,8BACE,2BACF,CACA,UACE,eACF,CACF,CAEA,yBACE,oBAEE,eACF,CACF,CAEA,0BACE,UACE,gBACF,CACF,CAEA,SAsBE,oBAAqB,CAnBrB,aAAc,CAEd,iMAEuC,CAcvC,iBAAmB,CAbnB,iBAAkB,CAClB,eAAgB,CAOhB,qBAAsB,CAItB,eAAgB,CAVhB,eAAgB,CANhB,QAAS,CAmBT,SAAU,CAtBV,iBAAkB,CAUlB,eAAgB,CAChB,gBAAiB,CACjB,oBAAqB,CACrB,gBAAiB,CACjB,mBAAoB,CAIpB,kBAAmB,CAFnB,iBAAkB,CAClB,mBAAoB,CAhBpB,YAsBF,CACA,cACE,UACF,CACA,gBAEE,aAAc,CAEd,YAAc,CAHd,iBAAkB,CAElB,WAEF,CACA,uBAGE,kBAAyB,CACzB,kBAAmB,CAFnB,UAAW,CADX,iBAIF,CAEA,mDAEE,eACF,CACA,iEAEE,QACF,CACA,+EAIE,qBAAsB,CADtB,0BAA6B,CAD7B,KAGF,CAEA,uDAEE,eACF,CACA,qEAIE,YAAc,CAFd,MAAO,CACP,WAEF,CACA,mFAIE,uBAAwB,CADxB,gCAAoC,CADpC,OAGF,CAEA,yDAEE,eACF,CACA,uEAEE,KACF,CACA,qFAIE,wBAAyB,CADzB,0BAA6B,CAD7B,QAGF,CAEA,qDAEE,eACF,CACA,mEAIE,YAAc,CAFd,OAAQ,CACR,WAEF,CACA,iFAIE,sBAAuB,CADvB,gCAAoC,CADpC,MAGF,CAEA,eAKE,qBAAsB,CACtB,oBAAsB,CAHtB,UAAW,CAFX,eAAgB,CAChB,oBAAuB,CAEvB,iBAGF,CAEA,SAwBE,oBAAqB,CAErB,2BAA4B,CAD5B,qBAAsB,CAEtB,sBAAoC,CACpC,mBAAqB,CArBrB,iMAEuC,CAcvC,iBAAmB,CAbnB,iBAAkB,CAClB,eAAgB,CARhB,MAAO,CAeP,qBAAsB,CAItB,eAAgB,CAVhB,eAAgB,CANhB,eAAgB,CAOhB,eAAgB,CAChB,gBAAiB,CACjB,oBAAqB,CACrB,gBAAiB,CACjB,mBAAoB,CAfpB,KAAM,CAmBN,kBAAmB,CAFnB,iBAAkB,CAClB,mBAAoB,CAhBpB,YAyBF,CACA,yBAzBE,aAAc,CAJd,iBAmCF,CANA,gBAIE,YAAc,CACd,cAAgB,CAFhB,UAGF,CACA,6CAKE,kBAAyB,CACzB,kBAAmB,CAFnB,UAAW,CADX,aAAc,CADd,iBAKF,CAEA,mDAEE,mBACF,CACA,iEAEE,0BACF,CACA,4JAIE,0BACF,CACA,+EAGE,0BAAqC,CADrC,QAEF,CAEA,6EAGE,qBAAsB,CADtB,UAEF,CAEA,uDAEE,iBACF,CACA,qEAIE,WAAY,CAFZ,wBAA+B,CAG/B,cAAgB,CAFhB,WAGF,CACA,oKAIE,gCACF,CACA,mFAGE,4BAAuC,CADvC,MAEF,CAEA,iFAGE,uBAAwB,CADxB,QAEF,CAEA,yDAEE,gBACF,CACA,uEAEE,uBACF,CACA,wKAIE,0BACF,CACA,qFAGE,6BAAwC,CADxC,KAEF,CAEA,mFAGE,wBAAyB,CADzB,OAEF,CACA,uGASE,+BAAgC,CADhC,UAAW,CAHX,aAAc,CADd,QAAS,CAGT,kBAAoB,CALpB,iBAAkB,CAClB,KAAM,CAGN,UAIF,CAEA,qDAEE,kBACF,CACA,mEAIE,WAAY,CACZ,cAAgB,CAHhB,yBAAgC,CAChC,WAGF,CACA,gKAIE,gCACF,CACA,iFAGE,2BAAsC,CADtC,OAEF,CAEA,+EAGE,sBAAuB,CADvB,SAEF,CAEA,gBAKE,wBAAyB,CACzB,+BAAgC,CAChC,wCAA0C,CAC1C,yCAA2C,CAJ3C,aAAc,CADd,cAAe,CADf,eAAgB,CADhB,oBAQF,CACA,sBACE,YACF,CAEA,cAEE,aAAc,CADd,oBAEF,CAEA,UACE,iBACF,CAEA,wBACE,kBACF,CAEA,gBAGE,eAAgB,CAFhB,iBAAkB,CAClB,UAEF,CACA,sBAEE,UAAW,CACX,UAAW,CAFX,aAGF,CAEA,eAME,kCAA2B,CAA3B,0BAA2B,CAJ3B,YAAa,CACb,UAAW,CAEX,kBAAmB,CAJnB,iBAAkB,CAMlB,oCAAsC,CAHtC,UAIF,CACA,kDACE,eACE,eACF,CACF,CAEA,8DAGE,aACF,CAEA,yEAEE,0BACF,CAEA,yEAEE,2BACF,CAEA,8BACE,SAAU,CAEV,cAAe,CADf,2BAEF,CAEA,kJAIE,SAAU,CADV,SAEF,CAEA,qFAGE,SAAU,CACV,yBAA2B,CAF3B,SAGF,CACA,kDACE,qFAEE,eACF,CACF,CAEA,8CAOE,kBAAmB,CAHnB,QAAS,CAMT,UAAW,CAJX,YAAa,CAEb,sBAAuB,CAIvB,UAAY,CAVZ,iBAAkB,CASlB,iBAAkB,CARlB,KAAM,CAUN,4BAA8B,CAJ9B,SAAU,CAJV,SASF,CACA,kDACE,8CAEE,eACF,CACF,CACA,oHAIE,UAAW,CAGX,UAAY,CADZ,SAAU,CADV,oBAGF,CAEA,uBACE,MACF,CAEA,uBACE,OACF,CAEA,wDAKE,8BAA+C,CAC/C,yBAA0B,CAJ1B,oBAAqB,CAErB,WAAY,CADZ,UAIF,CAEA,4BACE,6MACF,CAEA,4BACE,8MACF,CAEA,qBAGE,QAAS,CAGT,YAAa,CACb,sBAAuB,CAHvB,MAAO,CAOP,eAAgB,CADhB,eAAgB,CADhB,gBAAiB,CADjB,cAAe,CAPf,iBAAkB,CAClB,OAAQ,CAGR,UAOF,CACA,wBAUE,2BAA4B,CAD5B,qBAAsB,CAGtB,8BAAqC,CADrC,2BAAkC,CAVlC,kBAAuB,CAOvB,cAAe,CANf,aAAc,CAEd,UAAW,CAEX,eAAgB,CADhB,gBAAiB,CAQjB,UAAY,CANZ,kBAAmB,CAOnB,2BAA6B,CAX7B,UAYF,CACA,kDACE,wBACE,eACF,CACF,CACA,6BACE,SACF,CAEA,kBAGE,WAAY,CAKZ,UAAW,CAJX,QAAS,CAGT,mBAAoB,CADpB,gBAAiB,CALjB,iBAAkB,CAClB,SAAU,CAOV,iBAAkB,CAJlB,UAKF,CAEA,0BACE,GACE,uBACF,CACF,CAEA,gBAQE,6CAA+C,CAF/C,kBAA+B,CAC/B,iBAAkB,CADlB,8BAA+B,CAL/B,oBAAqB,CAErB,WAAY,CACZ,0BAA2B,CAF3B,UAOF,CAEA,mBAGE,iBAAmB,CADnB,WAAY,CADZ,UAGF,CAEA,wBACE,GACE,kBACF,CACA,IACE,SACF,CACF,CAEA,cAQE,2CAA6C,CAH7C,6BAA8B,CAC9B,iBAAkB,CALlB,oBAAqB,CAErB,WAAY,CAIZ,SAAU,CAHV,0BAA2B,CAF3B,UAOF,CAEA,iBAEE,WAAY,CADZ,UAEF,CAEA,gBACE,gCACF,CAEA,WACE,4BACF,CAEA,cACE,+BACF,CAEA,cACE,+BACF,CAEA,mBACE,oCACF,CAEA,gBACE,iCACF,CAEA,YACE,kCACF,CAEA,sFAIE,kCACF,CAEA,cACE,kCACF,CAEA,8FAIE,kCACF,CAEA,YACE,kCACF,CAEA,sFAIE,kCACF,CAEA,SACE,kCACF,CAEA,0EAIE,kCACF,CAEA,YACE,kCACF,CAEA,sFAIE,kCACF,CAEA,WACE,kCACF,CAEA,kFAIE,kCACF,CAEA,UACE,kCACF,CAEA,8EAIE,kCACF,CAEA,SACE,kCACF,CAEA,0EAIE,kCACF,CAEA,UACE,+BACF,CAEA,gBACE,kCACF,CAEA,QACE,kCACF,CAEA,YACE,sCACF,CAEA,cACE,wCACF,CAEA,eACE,yCACF,CAEA,aACE,uCACF,CAEA,UACE,kBACF,CAEA,cACE,sBACF,CAEA,gBACE,wBACF,CAEA,iBACE,yBACF,CAEA,eACE,uBACF,CAEA,gBACE,8BACF,CAEA,kBACE,8BACF,CAEA,gBACE,8BACF,CAEA,aACE,8BACF,CAEA,gBACE,8BACF,CAEA,eACE,8BACF,CAEA,cACE,8BACF,CAEA,aACE,8BACF,CAEA,cACE,2BACF,CAEA,SACE,8BACF,CAEA,aACE,uCAEF,CAEA,4BAHE,wCAMF,CAEA,+BAHE,2CAMF,CAEA,8BAHE,0CAMF,CAHA,cACE,uCAEF,CAEA,gBACE,2BACF,CAEA,cACE,6BACF,CAEA,WACE,yBACF,CAEA,gBAEE,UAAW,CACX,UAAW,CAFX,aAGF,CAEA,QACE,sBACF,CAEA,UACE,wBACF,CAEA,gBACE,8BACF,CAEA,SACE,uBACF,CAEA,SACE,uBACF,CAEA,aACE,2BACF,CAEA,cACE,4BACF,CAEA,QACE,sBACF,CAEA,eACE,6BACF,CAEA,yBACE,WACE,sBACF,CACA,aACE,wBACF,CACA,mBACE,8BACF,CACA,YACE,uBACF,CACA,YACE,uBACF,CACA,gBACE,2BACF,CACA,iBACE,4BACF,CACA,WACE,sBACF,CACA,kBACE,6BACF,CACF,CAEA,yBACE,WACE,sBACF,CACA,aACE,wBACF,CACA,mBACE,8BACF,CACA,YACE,uBACF,CACA,YACE,uBACF,CACA,gBACE,2BACF,CACA,iBACE,4BACF,CACA,WACE,sBACF,CACA,kBACE,6BACF,CACF,CAEA,yBACE,WACE,sBACF,CACA,aACE,wBACF,CACA,mBACE,8BACF,CACA,YACE,uBACF,CACA,YACE,uBACF,CACA,gBACE,2BACF,CACA,iBACE,4BACF,CACA,WACE,sBACF,CACA,kBACE,6BACF,CACF,CAEA,0BACE,WACE,sBACF,CACA,aACE,wBACF,CACA,mBACE,8BACF,CACA,YACE,uBACF,CACA,YACE,uBACF,CACA,gBACE,2BACF,CACA,iBACE,4BACF,CACA,WACE,sBACF,CACA,kBACE,6BACF,CACF,CAEA,aACE,cACE,sBACF,CACA,gBACE,wBACF,CACA,sBACE,8BACF,CACA,eACE,uBACF,CACA,eACE,uBACF,CACA,mBACE,2BACF,CACA,oBACE,4BACF,CACA,cACE,sBACF,CACA,qBACE,6BACF,CACF,CAEA,kBAEE,aAAc,CAGd,eAAgB,CADhB,SAAU,CAHV,iBAAkB,CAElB,UAGF,CACA,yBAEE,UAAW,CADX,aAEF,CACA,2IAWE,QAAS,CAJT,QAAS,CAGT,WAAY,CAFZ,MAAO,CAHP,iBAAkB,CAClB,KAAM,CAGN,UAGF,CAEA,+BACE,qBACF,CAEA,+BACE,kBACF,CAEA,8BACE,sBACF,CAEA,8BACE,gBACF,CAEA,UACE,4BACF,CAEA,aACE,+BACF,CAEA,kBACE,oCACF,CAEA,qBACE,uCACF,CAEA,WACE,wBACF,CAEA,aACE,0BACF,CAEA,mBACE,gCACF,CAEA,WACE,uBACF,CAEA,aACE,qBACF,CAEA,aACE,qBACF,CAEA,eACE,uBACF,CAEA,eACE,uBACF,CAEA,uBACE,oCACF,CAEA,qBACE,kCACF,CAEA,wBACE,gCACF,CAEA,yBACE,uCACF,CAEA,wBACE,sCACF,CAEA,mBACE,gCACF,CAEA,iBACE,8BACF,CAEA,oBACE,4BACF,CAEA,sBACE,8BACF,CAEA,qBACE,6BACF,CAEA,qBACE,kCACF,CAEA,mBACE,gCACF,CAEA,sBACE,8BACF,CAEA,uBACE,qCACF,CAEA,sBACE,oCACF,CAEA,uBACE,+BACF,CAEA,iBACE,yBACF,CAEA,kBACE,+BACF,CAEA,gBACE,6BACF,CAEA,mBACE,2BACF,CAEA,qBACE,6BACF,CAEA,oBACE,4BACF,CAEA,yBACE,aACE,4BACF,CACA,gBACE,+BACF,CACA,qBACE,oCACF,CACA,wBACE,uCACF,CACA,cACE,wBACF,CACA,gBACE,0BACF,CACA,sBACE,gCACF,CACA,cACE,uBACF,CACA,gBACE,qBACF,CACA,gBACE,qBACF,CACA,kBACE,uBACF,CACA,kBACE,uBACF,CACA,0BACE,oCACF,CACA,wBACE,kCACF,CACA,2BACE,gCACF,CACA,4BACE,uCACF,CACA,2BACE,sCACF,CACA,sBACE,gCACF,CACA,oBACE,8BACF,CACA,uBACE,4BACF,CACA,yBACE,8BACF,CACA,wBACE,6BACF,CACA,wBACE,kCACF,CACA,sBACE,gCACF,CACA,yBACE,8BACF,CACA,0BACE,qCACF,CACA,yBACE,oCACF,CACA,0BACE,+BACF,CACA,oBACE,yBACF,CACA,qBACE,+BACF,CACA,mBACE,6BACF,CACA,sBACE,2BACF,CACA,wBACE,6BACF,CACA,uBACE,4BACF,CACF,CAEA,yBACE,aACE,4BACF,CACA,gBACE,+BACF,CACA,qBACE,oCACF,CACA,wBACE,uCACF,CACA,cACE,wBACF,CACA,gBACE,0BACF,CACA,sBACE,gCACF,CACA,cACE,uBACF,CACA,gBACE,qBACF,CACA,gBACE,qBACF,CACA,kBACE,uBACF,CACA,kBACE,uBACF,CACA,0BACE,oCACF,CACA,wBACE,kCACF,CACA,2BACE,gCACF,CACA,4BACE,uCACF,CACA,2BACE,sCACF,CACA,sBACE,gCACF,CACA,oBACE,8BACF,CACA,uBACE,4BACF,CACA,yBACE,8BACF,CACA,wBACE,6BACF,CACA,wBACE,kCACF,CACA,sBACE,gCACF,CACA,yBACE,8BACF,CACA,0BACE,qCACF,CACA,yBACE,oCACF,CACA,0BACE,+BACF,CACA,oBACE,yBACF,CACA,qBACE,+BACF,CACA,mBACE,6BACF,CACA,sBACE,2BACF,CACA,wBACE,6BACF,CACA,uBACE,4BACF,CACF,CAEA,yBACE,aACE,4BACF,CACA,gBACE,+BACF,CACA,qBACE,oCACF,CACA,wBACE,uCACF,CACA,cACE,wBACF,CACA,gBACE,0BACF,CACA,sBACE,gCACF,CACA,cACE,uBACF,CACA,gBACE,qBACF,CACA,gBACE,qBACF,CACA,kBACE,uBACF,CACA,kBACE,uBACF,CACA,0BACE,oCACF,CACA,wBACE,kCACF,CACA,2BACE,gCACF,CACA,4BACE,uCACF,CACA,2BACE,sCACF,CACA,sBACE,gCACF,CACA,oBACE,8BACF,CACA,uBACE,4BACF,CACA,yBACE,8BACF,CACA,wBACE,6BACF,CACA,wBACE,kCACF,CACA,sBACE,gCACF,CACA,yBACE,8BACF,CACA,0BACE,qCACF,CACA,yBACE,oCACF,CACA,0BACE,+BACF,CACA,oBACE,yBACF,CACA,qBACE,+BACF,CACA,mBACE,6BACF,CACA,sBACE,2BACF,CACA,wBACE,6BACF,CACA,uBACE,4BACF,CACF,CAEA,0BACE,aACE,4BACF,CACA,gBACE,+BACF,CACA,qBACE,oCACF,CACA,wBACE,uCACF,CACA,cACE,wBACF,CACA,gBACE,0BACF,CACA,sBACE,gCACF,CACA,cACE,uBACF,CACA,gBACE,qBACF,CACA,gBACE,qBACF,CACA,kBACE,uBACF,CACA,kBACE,uBACF,CACA,0BACE,oCACF,CACA,wBACE,kCACF,CACA,2BACE,gCACF,CACA,4BACE,uCACF,CACA,2BACE,sCACF,CACA,sBACE,gCACF,CACA,oBACE,8BACF,CACA,uBACE,4BACF,CACA,yBACE,8BACF,CACA,wBACE,6BACF,CACA,wBACE,kCACF,CACA,sBACE,gCACF,CACA,yBACE,8BACF,CACA,0BACE,qCACF,CACA,yBACE,oCACF,CACA,0BACE,+BACF,CACA,oBACE,yBACF,CACA,qBACE,+BACF,CACA,mBACE,6BACF,CACA,sBACE,2BACF,CACA,wBACE,6BACF,CACA,uBACE,4BACF,CACF,CAEA,YACE,oBACF,CAEA,aACE,qBACF,CAEA,YACE,oBACF,CAEA,yBACE,eACE,oBACF,CACA,gBACE,qBACF,CACA,eACE,oBACF,CACF,CAEA,yBACE,eACE,oBACF,CACA,gBACE,qBACF,CACA,eACE,oBACF,CACF,CAEA,yBACE,eACE,oBACF,CACA,gBACE,qBACF,CACA,eACE,oBACF,CACF,CAEA,0BACE,eACE,oBACF,CACA,gBACE,qBACF,CACA,eACE,oBACF,CACF,CAEA,eACE,uBACF,CAEA,iBACE,yBACF,CAEA,iBACE,yBACF,CAEA,mBACE,2BACF,CAEA,mBACE,2BACF,CAEA,gBACE,wBACF,CAEA,iBACE,iCAA2B,CAA3B,yBACF,CAEA,WAEE,KAIF,CAEA,yBAJE,MAAO,CAHP,cAAe,CAEf,OAAQ,CAER,YASF,CANA,cAGE,QAGF,CAEA,2DACE,YACE,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,YACF,CACF,CAEA,SAME,kBAAsB,CAEtB,QAAS,CALT,UAAW,CAEX,eAAgB,CADhB,SAAU,CAHV,iBAAkB,CAMlB,kBAAmB,CALnB,SAOF,CAEA,mDAME,SAAU,CAFV,WAAY,CACZ,gBAAiB,CAHjB,eAAgB,CAKhB,kBAAmB,CAJnB,UAKF,CAEA,WACE,sDACF,CAEA,QACE,2CACF,CAEA,WACE,iDACF,CAEA,aACE,yBACF,CAEA,MACE,mBACF,CAEA,MACE,mBACF,CAEA,MACE,mBACF,CAEA,OACE,oBACF,CAEA,QACE,oBACF,CAEA,MACE,oBACF,CAEA,MACE,oBACF,CAEA,MACE,oBACF,CAEA,OACE,qBACF,CAEA,QACE,qBACF,CAEA,QACE,wBACF,CAEA,QACE,yBACF,CAEA,YACE,yBACF,CAEA,YACE,0BACF,CAEA,QACE,qBACF,CAEA,QACE,sBACF,CAEA,KACE,kBACF,CAEA,YAEE,sBACF,CAEA,YAEE,wBACF,CAEA,YAEE,yBACF,CAEA,YAEE,uBACF,CAEA,KACE,uBACF,CAEA,YAEE,2BACF,CAEA,YAEE,6BACF,CAEA,YAEE,8BACF,CAEA,YAEE,4BACF,CAEA,KACE,sBACF,CAEA,YAEE,0BACF,CAEA,YAEE,4BACF,CAEA,YAEE,6BACF,CAEA,YAEE,2BACF,CAEA,KACE,qBACF,CAEA,YAEE,yBACF,CAEA,YAEE,2BACF,CAEA,YAEE,4BACF,CAEA,YAEE,0BACF,CAEA,KACE,uBACF,CAEA,YAEE,2BACF,CAEA,YAEE,6BACF,CAEA,YAEE,8BACF,CAEA,YAEE,4BACF,CAEA,KACE,qBACF,CAEA,YAEE,yBACF,CAEA,YAEE,2BACF,CAEA,YAEE,4BACF,CAEA,YAEE,0BACF,CAEA,KACE,mBACF,CAEA,YAEE,uBACF,CAEA,YAEE,yBACF,CAEA,YAEE,0BACF,CAEA,YAEE,wBACF,CAEA,KACE,wBACF,CAEA,YAEE,4BACF,CAEA,YAEE,8BACF,CAEA,YAEE,+BACF,CAEA,YAEE,6BACF,CAEA,KACE,uBACF,CAEA,YAEE,2BACF,CAEA,YAEE,6BACF,CAEA,YAEE,8BACF,CAEA,YAEE,4BACF,CAEA,KACE,sBACF,CAEA,YAEE,0BACF,CAEA,YAEE,4BACF,CAEA,YAEE,6BACF,CAEA,YAEE,2BACF,CAEA,KACE,wBACF,CAEA,YAEE,4BACF,CAEA,YAEE,8BACF,CAEA,YAEE,+BACF,CAEA,YAEE,6BACF,CAEA,KACE,sBACF,CAEA,YAEE,0BACF,CAEA,YAEE,4BACF,CAEA,YAEE,6BACF,CAEA,YAEE,2BACF,CAEA,MACE,wBACF,CAEA,cAEE,4BACF,CAEA,cAEE,8BACF,CAEA,cAEE,+BACF,CAEA,cAEE,6BACF,CAEA,MACE,uBACF,CAEA,cAEE,2BACF,CAEA,cAEE,6BACF,CAEA,cAEE,8BACF,CAEA,cAEE,4BACF,CAEA,MACE,sBACF,CAEA,cAEE,0BACF,CAEA,cAEE,4BACF,CAEA,cAEE,6BACF,CAEA,cAEE,2BACF,CAEA,MACE,wBACF,CAEA,cAEE,4BACF,CAEA,cAEE,8BACF,CAEA,cAEE,+BACF,CAEA,cAEE,6BACF,CAEA,MACE,sBACF,CAEA,cAEE,0BACF,CAEA,cAEE,4BACF,CAEA,cAEE,6BACF,CAEA,cAEE,2BACF,CAEA,QACE,qBACF,CAEA,kBAEE,yBACF,CAEA,kBAEE,2BACF,CAEA,kBAEE,4BACF,CAEA,kBAEE,0BACF,CAEA,yBACE,QACE,kBACF,CACA,kBAEE,sBACF,CACA,kBAEE,wBACF,CACA,kBAEE,yBACF,CACA,kBAEE,uBACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,QACE,qBACF,CACA,kBAEE,yBACF,CACA,kBAEE,2BACF,CACA,kBAEE,4BACF,CACA,kBAEE,0BACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,qBACF,CACA,kBAEE,yBACF,CACA,kBAEE,2BACF,CACA,kBAEE,4BACF,CACA,kBAEE,0BACF,CACA,QACE,mBACF,CACA,kBAEE,uBACF,CACA,kBAEE,yBACF,CACA,kBAEE,0BACF,CACA,kBAEE,wBACF,CACA,QACE,wBACF,CACA,kBAEE,4BACF,CACA,kBAEE,8BACF,CACA,kBAEE,+BACF,CACA,kBAEE,6BACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,QACE,wBACF,CACA,kBAEE,4BACF,CACA,kBAEE,8BACF,CACA,kBAEE,+BACF,CACA,kBAEE,6BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,SACE,wBACF,CACA,oBAEE,4BACF,CACA,oBAEE,8BACF,CACA,oBAEE,+BACF,CACA,oBAEE,6BACF,CACA,SACE,uBACF,CACA,oBAEE,2BACF,CACA,oBAEE,6BACF,CACA,oBAEE,8BACF,CACA,oBAEE,4BACF,CACA,SACE,sBACF,CACA,oBAEE,0BACF,CACA,oBAEE,4BACF,CACA,oBAEE,6BACF,CACA,oBAEE,2BACF,CACA,SACE,wBACF,CACA,oBAEE,4BACF,CACA,oBAEE,8BACF,CACA,oBAEE,+BACF,CACA,oBAEE,6BACF,CACA,SACE,sBACF,CACA,oBAEE,0BACF,CACA,oBAEE,4BACF,CACA,oBAEE,6BACF,CACA,oBAEE,2BACF,CACA,WACE,qBACF,CACA,wBAEE,yBACF,CACA,wBAEE,2BACF,CACA,wBAEE,4BACF,CACA,wBAEE,0BACF,CACF,CAEA,yBACE,QACE,kBACF,CACA,kBAEE,sBACF,CACA,kBAEE,wBACF,CACA,kBAEE,yBACF,CACA,kBAEE,uBACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,QACE,qBACF,CACA,kBAEE,yBACF,CACA,kBAEE,2BACF,CACA,kBAEE,4BACF,CACA,kBAEE,0BACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,qBACF,CACA,kBAEE,yBACF,CACA,kBAEE,2BACF,CACA,kBAEE,4BACF,CACA,kBAEE,0BACF,CACA,QACE,mBACF,CACA,kBAEE,uBACF,CACA,kBAEE,yBACF,CACA,kBAEE,0BACF,CACA,kBAEE,wBACF,CACA,QACE,wBACF,CACA,kBAEE,4BACF,CACA,kBAEE,8BACF,CACA,kBAEE,+BACF,CACA,kBAEE,6BACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,QACE,wBACF,CACA,kBAEE,4BACF,CACA,kBAEE,8BACF,CACA,kBAEE,+BACF,CACA,kBAEE,6BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,SACE,wBACF,CACA,oBAEE,4BACF,CACA,oBAEE,8BACF,CACA,oBAEE,+BACF,CACA,oBAEE,6BACF,CACA,SACE,uBACF,CACA,oBAEE,2BACF,CACA,oBAEE,6BACF,CACA,oBAEE,8BACF,CACA,oBAEE,4BACF,CACA,SACE,sBACF,CACA,oBAEE,0BACF,CACA,oBAEE,4BACF,CACA,oBAEE,6BACF,CACA,oBAEE,2BACF,CACA,SACE,wBACF,CACA,oBAEE,4BACF,CACA,oBAEE,8BACF,CACA,oBAEE,+BACF,CACA,oBAEE,6BACF,CACA,SACE,sBACF,CACA,oBAEE,0BACF,CACA,oBAEE,4BACF,CACA,oBAEE,6BACF,CACA,oBAEE,2BACF,CACA,WACE,qBACF,CACA,wBAEE,yBACF,CACA,wBAEE,2BACF,CACA,wBAEE,4BACF,CACA,wBAEE,0BACF,CACF,CAEA,yBACE,QACE,kBACF,CACA,kBAEE,sBACF,CACA,kBAEE,wBACF,CACA,kBAEE,yBACF,CACA,kBAEE,uBACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,QACE,qBACF,CACA,kBAEE,yBACF,CACA,kBAEE,2BACF,CACA,kBAEE,4BACF,CACA,kBAEE,0BACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,qBACF,CACA,kBAEE,yBACF,CACA,kBAEE,2BACF,CACA,kBAEE,4BACF,CACA,kBAEE,0BACF,CACA,QACE,mBACF,CACA,kBAEE,uBACF,CACA,kBAEE,yBACF,CACA,kBAEE,0BACF,CACA,kBAEE,wBACF,CACA,QACE,wBACF,CACA,kBAEE,4BACF,CACA,kBAEE,8BACF,CACA,kBAEE,+BACF,CACA,kBAEE,6BACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,QACE,wBACF,CACA,kBAEE,4BACF,CACA,kBAEE,8BACF,CACA,kBAEE,+BACF,CACA,kBAEE,6BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,SACE,wBACF,CACA,oBAEE,4BACF,CACA,oBAEE,8BACF,CACA,oBAEE,+BACF,CACA,oBAEE,6BACF,CACA,SACE,uBACF,CACA,oBAEE,2BACF,CACA,oBAEE,6BACF,CACA,oBAEE,8BACF,CACA,oBAEE,4BACF,CACA,SACE,sBACF,CACA,oBAEE,0BACF,CACA,oBAEE,4BACF,CACA,oBAEE,6BACF,CACA,oBAEE,2BACF,CACA,SACE,wBACF,CACA,oBAEE,4BACF,CACA,oBAEE,8BACF,CACA,oBAEE,+BACF,CACA,oBAEE,6BACF,CACA,SACE,sBACF,CACA,oBAEE,0BACF,CACA,oBAEE,4BACF,CACA,oBAEE,6BACF,CACA,oBAEE,2BACF,CACA,WACE,qBACF,CACA,wBAEE,yBACF,CACA,wBAEE,2BACF,CACA,wBAEE,4BACF,CACA,wBAEE,0BACF,CACF,CAEA,0BACE,QACE,kBACF,CACA,kBAEE,sBACF,CACA,kBAEE,wBACF,CACA,kBAEE,yBACF,CACA,kBAEE,uBACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,QACE,qBACF,CACA,kBAEE,yBACF,CACA,kBAEE,2BACF,CACA,kBAEE,4BACF,CACA,kBAEE,0BACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,qBACF,CACA,kBAEE,yBACF,CACA,kBAEE,2BACF,CACA,kBAEE,4BACF,CACA,kBAEE,0BACF,CACA,QACE,mBACF,CACA,kBAEE,uBACF,CACA,kBAEE,yBACF,CACA,kBAEE,0BACF,CACA,kBAEE,wBACF,CACA,QACE,wBACF,CACA,kBAEE,4BACF,CACA,kBAEE,8BACF,CACA,kBAEE,+BACF,CACA,kBAEE,6BACF,CACA,QACE,uBACF,CACA,kBAEE,2BACF,CACA,kBAEE,6BACF,CACA,kBAEE,8BACF,CACA,kBAEE,4BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,QACE,wBACF,CACA,kBAEE,4BACF,CACA,kBAEE,8BACF,CACA,kBAEE,+BACF,CACA,kBAEE,6BACF,CACA,QACE,sBACF,CACA,kBAEE,0BACF,CACA,kBAEE,4BACF,CACA,kBAEE,6BACF,CACA,kBAEE,2BACF,CACA,SACE,wBACF,CACA,oBAEE,4BACF,CACA,oBAEE,8BACF,CACA,oBAEE,+BACF,CACA,oBAEE,6BACF,CACA,SACE,uBACF,CACA,oBAEE,2BACF,CACA,oBAEE,6BACF,CACA,oBAEE,8BACF,CACA,oBAEE,4BACF,CACA,SACE,sBACF,CACA,oBAEE,0BACF,CACA,oBAEE,4BACF,CACA,oBAEE,6BACF,CACA,oBAEE,2BACF,CACA,SACE,wBACF,CACA,oBAEE,4BACF,CACA,oBAEE,8BACF,CACA,oBAEE,+BACF,CACA,oBAEE,6BACF,CACA,SACE,sBACF,CACA,oBAEE,0BACF,CACA,oBAEE,4BACF,CACA,oBAEE,6BACF,CACA,oBAEE,2BACF,CACA,WACE,qBACF,CACA,wBAEE,yBACF,CACA,wBAEE,2BACF,CACA,wBAEE,4BACF,CACA,wBAEE,0BACF,CACF,CAEA,gBACE,kHAEF,CAEA,cACE,4BACF,CAEA,WACE,4BACF,CAEA,aACE,4BACF,CAEA,eACE,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAEA,WACE,yBACF,CAEA,YACE,0BACF,CAEA,aACE,2BACF,CAEA,yBACE,cACE,yBACF,CACA,eACE,0BACF,CACA,gBACE,2BACF,CACF,CAEA,yBACE,cACE,yBACF,CACA,eACE,0BACF,CACA,gBACE,2BACF,CACF,CAEA,yBACE,cACE,yBACF,CACA,eACE,0BACF,CACA,gBACE,2BACF,CACF,CAEA,0BACE,cACE,yBACF,CACA,eACE,0BACF,CACA,gBACE,2BACF,CACF,CAEA,gBACE,kCACF,CAEA,gBACE,kCACF,CAEA,iBACE,mCACF,CAEA,mBACE,yBACF,CAEA,qBACE,6BACF,CAEA,oBACE,yBACF,CAEA,kBACE,yBACF,CAEA,oBACE,4BACF,CAEA,aACE,2BACF,CAEA,YACE,oBACF,CAEA,cACE,uBACF,CAEA,0CAEE,uBACF,CAEA,gBACE,uBACF,CAEA,8CAEE,uBACF,CAEA,cACE,uBACF,CAEA,0CAEE,uBACF,CAEA,WACE,uBACF,CAEA,oCAEE,uBACF,CAEA,cACE,uBACF,CAEA,0CAEE,uBACF,CAEA,aACE,uBACF,CAEA,wCAEE,uBACF,CAEA,YACE,uBACF,CAEA,sCAEE,sBACF,CAEA,WACE,uBACF,CAEA,oCAEE,uBACF,CAEA,WACE,uBACF,CAEA,YACE,uBACF,CAEA,eACE,yBACF,CAEA,eACE,yBACF,CAEA,WAIE,wBAA6B,CAC7B,QAAS,CAHT,WAAkB,CADlB,UAAW,CAEX,gBAGF,CAEA,sBACE,8BACF,CAEA,YACE,uBACF,CAEA,SACE,4BACF,CAEA,WACE,2BACF,CAEA,aACE,iBAIE,yBAA2B,CAD3B,0BAEF,CACA,YACE,yBACF,CACA,kBACE,4BACF,CACA,IACE,8BACF,CACA,eAEE,wBAAyB,CACzB,uBACF,CACA,MACE,0BACF,CACA,OAEE,uBACF,CACA,QAGE,SAAU,CACV,QACF,CACA,MAEE,sBACF,CACA,MACE,OACF,CAIA,gBACE,yBACF,CACA,QACE,YACF,CACA,OACE,qBACF,CACA,OACE,kCACF,CACA,oBAEE,+BACF,CACA,sCAEE,kCACF,CACA,YACE,aACF,CACA,2EAIE,oBACF,CACA,sBAEE,oBAAqB,CADrB,aAEF,CACF,CCpqSA,kBACE,YAAa,CACb,cACF,CAOA,gEAIE,aAAc,CADd,kBAAmB,CADnB,iBAGF,CAEA,oBAGE,kBAAmB,CAFnB,sBAAwB,CACxB,aAAc,CAEd,iBAAkB,CAClB,iBACF,CAEA,0BACE,kBACF,CAEA,0BAGE,eAAgB,CADhB,gBAAiB,CADjB,iBAGF,CAEA,uCAIE,wBAAoC,CAKpC,qBAAoC,CADpC,iBAAkB,CAPlB,aAAc,CACd,eAAgB,CAYhB,WAAY,CAPZ,iBAAkB,CAFlB,cAAe,CAKf,iBAAkB,CAElB,UAAW,CATX,iBAAkB,CAQlB,OAAQ,CAER,SAAU,CAEV,SACF,CAEA,yCACE,uCACE,SACF,CACF,CAEA,uCACE,oBAAsB,CACtB,cAEF,CAEA,gCACE,wBACF,CAEA,kDACE,cACF,CCvEA,0BAGE,wBAAyB,CADzB,kBAAmB,CADnB,eAGF,CAEA,wCAEE,kBAAmB,CADnB,YAAa,CAEb,qBAAsB,CAGtB,sBAAuB,CADvB,gBAAiB,CADjB,gBAGF,CAEA,2CACE,gBAAiB,CACjB,2BACF,CAEA,wCACE,aACF,CAEA,qCACE,gBAAiB,CACjB,eAAgB,CAChB,8BACF,CAEA,0CACE,eACF,CAEA,wCAIE,mBAAoB,CADpB,gBAAiB,CAEjB,SAAU,CAHV,iBAAkB,CADlB,UAKF,CAIA,oBAEE,kBAAmB,CADnB,eAEF,CACA,oBACE,gBACF,CClDA,qBAGE,kBAAmB,CAFnB,YAAa,CAGb,qBAAsB,CAFtB,UAGF,CAEA,yBAEE,kBAAmB,CADnB,WAEF,CAEA,QAIE,uBAAwB,CACxB,+BAAgC,CAJhC,qBAAsB,CACtB,6BAA8B,CAK9B,qCAAsC,CACtC,6CAA8C,CAE9C,4BACF,CAEA,kBACE,GAEE,SAAU,CADV,kBAEF,CACA,IACE,oBACF,CACA,IAEE,SAAU,CADV,mBAEF,CACA,GAEE,SAAU,CADV,kBAEF,CACF,CAoBA,kCACE,mBACF,CAEA,yBACE,mBACF,CACA,4BAEE,oBACF,CACA,uBAEE,oBACF,CACA,mBAEE,oBACF", "sources": ["styles/main.css", "styles/amount-selector.css", "styles/premiums.css", "styles/submit-modal.css"], "sourcesContent": ["body {\n  background: url(../images/bg_verylight.jpg) no-repeat top right fixed;\n  overflow-x: hidden;\n}\n\nh1,\nh3,\nh4 {\n  font-weight: 700 !important;\n}\n\np {\n  font-size: 1.2rem;\n}\n\n#root {\n  position: relative;\n}\n\n#summary h1 {\n  font-family: \"ff-magda-clean-mono-web-pro\", SFMono-Regular, Menlo, Monaco, Consolas,\n    \"Liberation Mono\", \"Courier New\", monospace !important;\n  font-size: 4rem;\n}\n\n.btn,\n.nav-pills .nav-link {\n  font-family: \"ff-magda-clean-mono-web-pro\", SFMono-Regular, Menlo, Monaco, Consolas,\n    \"Liberation Mono\", \"Courier New\", monospace;\n}\n\n.badge {\n  font-family: \"ff-magda-clean-mono-web-pro\", SFMono-Regular, Menlo, Monaco, Consolas,\n    \"Liberation Mono\", \"Courier New\", monospace;\n}\n\n#summary {\n  max-height: 80%;\n  overflow-y: auto;\n  overflow-x: hidden;\n  margin-bottom: 1rem;\n}\n\n#summary-expand {\n  display: none;\n}\n\n@media (max-width: 1138px) and (min-width: 769px) {\n  .inpage-summary #summary h1 {\n    font-size: 2.2rem;\n  }\n}\n\n@media (max-width: 768px) {\n  #summary h1 {\n    font-size: 3rem;\n  }\n  .inpage-summary #summary {\n    position: fixed;\n    /* max-height: 80%; */\n    overflow: hidden hidden;\n    z-index: 2;\n    height: 105px;\n    bottom: -20px;\n    left: 0;\n    width: 100% !important;\n    background-color: white;\n    padding: 20px;\n    margin-bottom: 0;\n    border-top: 1px solid #fc2a00;\n    padding-right: 5px;\n    padding-left: 5px;\n  }\n  .inpage-summary #summary.expanded {\n    height: initial;\n    max-height: 55%;\n    overflow: hidden auto;\n    bottom: 0px;\n  }\n  .inpage-summary #summary-text {\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    align-items: center;\n  }\n  .inpage-summary #summary-text h1,\n  .inpage-summary #summary-text h4,\n  .inpage-summary #summary-text h5 {\n    margin-bottom: 0 !important;\n    font-size: 1.2rem;\n    margin-right: 10px;\n  }\n  .inpage-summary #summary-expand {\n    display: block;\n  }\n}\n\n/*!\n * Bootstrap v4.2.1 (https://getbootstrap.com/)\n * Copyright 2011-2018 The Bootstrap Authors\n * Copyright 2011-2018 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n:root {\n  --blue: #007bff;\n  --indigo: #6610f2;\n  --purple: #6f42c1;\n  --pink: #e83e8c;\n  --red: #dc3545;\n  --orange: #fd7e14;\n  --yellow: #ffc107;\n  --green: #28a745;\n  --teal: #20c997;\n  --cyan: #17a2b8;\n  --white: #fff;\n  --gray: #6c757d;\n  --gray-dark: #343a40;\n  --primary: #fc2a00;\n  --secondary: #6c757d;\n  --success: #28a745;\n  --info: #17a2b8;\n  --warning: #fcaf17;\n  --danger: #dc3545;\n  --light: #e6e6e6;\n  --dark: #343a40;\n  --breakpoint-xs: 0;\n  --breakpoint-sm: 576px;\n  --breakpoint-md: 768px;\n  --breakpoint-lg: 992px;\n  --breakpoint-xl: 1200px;\n  --font-family-sans-serif: \"ff-super-grotesk-web-pro\", -apple-system, BlinkMacSystemFont,\n    \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\",\n    \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --font-family-monospace: \"ff-magda-clean-mono-web-pro\", SFMono-Regular, Menlo, Monaco, Consolas,\n    \"Liberation Mono\", \"Courier New\", monospace;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\nhtml {\n  font-family: sans-serif;\n  line-height: 1.15;\n  -webkit-text-size-adjust: 100%;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n\narticle,\naside,\nfigcaption,\nfigure,\nfooter,\nheader,\nhgroup,\nmain,\nnav,\nsection {\n  display: block;\n}\n\nbody {\n  margin: 0;\n  font-family: \"ff-super-grotesk-web-pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\n    \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\",\n    \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #212529;\n  text-align: left;\n  background-color: #fff;\n}\n\n[tabindex=\"-1\"]:focus {\n  outline: 0 !important;\n}\n\nhr {\n  box-sizing: content-box;\n  height: 0;\n  overflow: visible;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  margin-top: 0;\n  margin-bottom: 0.5rem;\n}\n\np {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nabbr[title],\nabbr[data-original-title] {\n  text-decoration: underline;\n  text-decoration: underline dotted;\n  cursor: help;\n  border-bottom: 0;\n  text-decoration-skip-ink: none;\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: 700;\n}\n\ndd {\n  margin-bottom: 0.5rem;\n  margin-left: 0;\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\nsmall {\n  font-size: 80%;\n}\n\nsub,\nsup {\n  position: relative;\n  font-size: 75%;\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\na {\n  color: #fc2a00;\n  text-decoration: none;\n  background-color: transparent;\n}\na:hover {\n  color: #b01d00;\n  text-decoration: underline;\n}\n\na:not([href]):not([tabindex]) {\n  color: inherit;\n  text-decoration: none;\n}\na:not([href]):not([tabindex]):hover,\na:not([href]):not([tabindex]):focus {\n  color: inherit;\n  text-decoration: none;\n}\na:not([href]):not([tabindex]):focus {\n  outline: 0;\n}\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: \"ff-magda-clean-mono-web-pro\", SFMono-Regular, Menlo, Monaco, Consolas,\n    \"Liberation Mono\", \"Courier New\", monospace;\n  font-size: 1em;\n}\n\npre {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  overflow: auto;\n}\n\nfigure {\n  margin: 0 0 1rem;\n}\n\nimg {\n  vertical-align: middle;\n  border-style: none;\n}\n\nsvg {\n  overflow: hidden;\n  vertical-align: middle;\n}\n\ntable {\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  color: #6c757d;\n  text-align: left;\n  caption-side: bottom;\n}\n\nth {\n  text-align: inherit;\n}\n\nlabel {\n  display: inline-block;\n  margin-bottom: 0.5rem;\n}\n\nbutton {\n  border-radius: 0;\n}\n\nbutton:focus {\n  outline: 1px dotted;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n\nbutton,\ninput {\n  overflow: visible;\n}\n\nbutton,\nselect {\n  text-transform: none;\n}\n\nbutton,\n[type=\"button\"],\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button;\n}\n\nbutton::-moz-focus-inner,\n[type=\"button\"]::-moz-focus-inner,\n[type=\"reset\"]::-moz-focus-inner,\n[type=\"submit\"]::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  box-sizing: border-box;\n  padding: 0;\n}\n\ninput[type=\"date\"],\ninput[type=\"time\"],\ninput[type=\"datetime-local\"],\ninput[type=\"month\"] {\n  -webkit-appearance: listbox;\n}\n\ntextarea {\n  overflow: auto;\n  resize: vertical;\n}\n\nfieldset {\n  min-width: 0;\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%;\n  padding: 0;\n  margin-bottom: 0.5rem;\n  font-size: 1.5rem;\n  line-height: inherit;\n  color: inherit;\n  white-space: normal;\n}\n\nprogress {\n  vertical-align: baseline;\n}\n\n[type=\"number\"]::-webkit-inner-spin-button,\n[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n[type=\"search\"] {\n  outline-offset: -2px;\n  -webkit-appearance: none;\n}\n\n[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n::-webkit-file-upload-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\n\noutput {\n  display: inline-block;\n}\n\nsummary {\n  display: list-item;\n  cursor: pointer;\n}\n\ntemplate {\n  display: none;\n}\n\n[hidden] {\n  display: none !important;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\n.h1,\n.h2,\n.h3,\n.h4,\n.h5,\n.h6 {\n  margin-bottom: 0.5rem;\n  font-family: inherit;\n  font-weight: 500;\n  line-height: 1.2;\n  color: inherit;\n}\n\nh1,\n.h1 {\n  font-size: 2.5rem;\n}\n\nh2,\n.h2 {\n  font-size: 2rem;\n}\n\nh3,\n.h3 {\n  font-size: 1.75rem;\n}\n\nh4,\n.h4 {\n  font-size: 1.5rem;\n}\n\nh5,\n.h5 {\n  font-size: 1.25rem;\n}\n\nh6,\n.h6 {\n  font-size: 1rem;\n}\n\n.lead {\n  font-size: 1.25rem;\n  font-weight: 300;\n}\n\n.display-1 {\n  font-size: 6rem;\n  font-weight: 300;\n  line-height: 1.2;\n}\n\n.display-2 {\n  font-size: 5.5rem;\n  font-weight: 300;\n  line-height: 1.2;\n}\n\n.display-3 {\n  font-size: 4.5rem;\n  font-weight: 300;\n  line-height: 1.2;\n}\n\n.display-4 {\n  font-size: 3.5rem;\n  font-weight: 300;\n  line-height: 1.2;\n}\n\nhr {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n  border: 0;\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\n}\n\nsmall,\n.small {\n  font-size: 80%;\n  font-weight: 400;\n}\n\nmark,\n.mark {\n  padding: 0.2em;\n  background-color: #fcf8e3;\n}\n\n.list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n\n.list-inline {\n  padding-left: 0;\n  list-style: none;\n}\n\n.list-inline-item {\n  display: inline-block;\n}\n.list-inline-item:not(:last-child) {\n  margin-right: 0.5rem;\n}\n\n.initialism {\n  font-size: 90%;\n  text-transform: uppercase;\n}\n\n.blockquote {\n  margin-bottom: 1rem;\n  font-size: 1.25rem;\n}\n\n.blockquote-footer {\n  display: block;\n  font-size: 80%;\n  color: #6c757d;\n}\n.blockquote-footer::before {\n  content: \"\\2014\\00A0\";\n}\n\n.img-fluid {\n  max-width: 100%;\n  height: auto;\n}\n\n.img-thumbnail {\n  padding: 0.25rem;\n  background-color: #fff;\n  border: 1px solid #dee2e6;\n  border-radius: 0.25rem;\n  max-width: 100%;\n  height: auto;\n}\n\n.figure {\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: 0.5rem;\n  line-height: 1;\n}\n\n.figure-caption {\n  font-size: 90%;\n  color: #6c757d;\n}\n\ncode {\n  font-size: 87.5%;\n  color: #e83e8c;\n  word-break: break-word;\n}\na > code {\n  color: inherit;\n}\n\nkbd {\n  padding: 0.2rem 0.4rem;\n  font-size: 87.5%;\n  color: #fff;\n  background-color: #212529;\n  border-radius: 0.2rem;\n}\nkbd kbd {\n  padding: 0;\n  font-size: 100%;\n  font-weight: 700;\n}\n\npre {\n  display: block;\n  font-size: 87.5%;\n  color: #212529;\n}\npre code {\n  font-size: inherit;\n  color: inherit;\n  word-break: normal;\n}\n\n.pre-scrollable {\n  max-height: 340px;\n  overflow-y: scroll;\n}\n\n.container {\n  width: 100%;\n  padding-right: 15px;\n  padding-left: 15px;\n  margin-right: auto;\n  margin-left: auto;\n}\n@media (min-width: 576px) {\n  .container {\n    max-width: 540px;\n  }\n}\n@media (min-width: 768px) {\n  .container {\n    max-width: 720px;\n  }\n}\n@media (min-width: 992px) {\n  .container {\n    max-width: 960px;\n  }\n}\n@media (min-width: 1200px) {\n  .container {\n    max-width: 1140px;\n  }\n}\n\n.container-fluid {\n  width: 100%;\n  padding-right: 15px;\n  padding-left: 15px;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n.row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -15px;\n  margin-left: -15px;\n}\n\n.no-gutters {\n  margin-right: 0;\n  margin-left: 0;\n}\n.no-gutters > .col,\n.no-gutters > [class*=\"col-\"] {\n  padding-right: 0;\n  padding-left: 0;\n}\n\n.col-1,\n.col-2,\n.col-3,\n.col-4,\n.col-5,\n.col-6,\n.col-7,\n.col-8,\n.col-9,\n.col-10,\n.col-11,\n.col-12,\n.col,\n.col-auto,\n.col-sm-1,\n.col-sm-2,\n.col-sm-3,\n.col-sm-4,\n.col-sm-5,\n.col-sm-6,\n.col-sm-7,\n.col-sm-8,\n.col-sm-9,\n.col-sm-10,\n.col-sm-11,\n.col-sm-12,\n.col-sm,\n.col-sm-auto,\n.col-md-1,\n.col-md-2,\n.col-md-3,\n.col-md-4,\n.col-md-5,\n.col-md-6,\n.col-md-7,\n.col-md-8,\n.col-md-9,\n.col-md-10,\n.col-md-11,\n.col-md-12,\n.col-md,\n.col-md-auto,\n.col-lg-1,\n.col-lg-2,\n.col-lg-3,\n.col-lg-4,\n.col-lg-5,\n.col-lg-6,\n.col-lg-7,\n.col-lg-8,\n.col-lg-9,\n.col-lg-10,\n.col-lg-11,\n.col-lg-12,\n.col-lg,\n.col-lg-auto,\n.col-xl-1,\n.col-xl-2,\n.col-xl-3,\n.col-xl-4,\n.col-xl-5,\n.col-xl-6,\n.col-xl-7,\n.col-xl-8,\n.col-xl-9,\n.col-xl-10,\n.col-xl-11,\n.col-xl-12,\n.col-xl,\n.col-xl-auto {\n  position: relative;\n  width: 100%;\n  padding-right: 15px;\n  padding-left: 15px;\n}\n\n.col {\n  flex-basis: 0;\n  flex-grow: 1;\n  max-width: 100%;\n}\n\n.col-auto {\n  flex: 0 0 auto;\n  width: auto;\n  max-width: 100%;\n}\n\n.col-1 {\n  flex: 0 0 8.33333%;\n  max-width: 8.33333%;\n}\n\n.col-2 {\n  flex: 0 0 16.66667%;\n  max-width: 16.66667%;\n}\n\n.col-3 {\n  flex: 0 0 25%;\n  max-width: 25%;\n}\n\n.col-4 {\n  flex: 0 0 33.33333%;\n  max-width: 33.33333%;\n}\n\n.col-5 {\n  flex: 0 0 41.66667%;\n  max-width: 41.66667%;\n}\n\n.col-6 {\n  flex: 0 0 50%;\n  max-width: 50%;\n}\n\n.col-7 {\n  flex: 0 0 58.33333%;\n  max-width: 58.33333%;\n}\n\n.col-8 {\n  flex: 0 0 66.66667%;\n  max-width: 66.66667%;\n}\n\n.col-9 {\n  flex: 0 0 75%;\n  max-width: 75%;\n}\n\n.col-10 {\n  flex: 0 0 83.33333%;\n  max-width: 83.33333%;\n}\n\n.col-11 {\n  flex: 0 0 91.66667%;\n  max-width: 91.66667%;\n}\n\n.col-12 {\n  flex: 0 0 100%;\n  max-width: 100%;\n}\n\n.order-first {\n  order: -1;\n}\n\n.order-last {\n  order: 13;\n}\n\n.order-0 {\n  order: 0;\n}\n\n.order-1 {\n  order: 1;\n}\n\n.order-2 {\n  order: 2;\n}\n\n.order-3 {\n  order: 3;\n}\n\n.order-4 {\n  order: 4;\n}\n\n.order-5 {\n  order: 5;\n}\n\n.order-6 {\n  order: 6;\n}\n\n.order-7 {\n  order: 7;\n}\n\n.order-8 {\n  order: 8;\n}\n\n.order-9 {\n  order: 9;\n}\n\n.order-10 {\n  order: 10;\n}\n\n.order-11 {\n  order: 11;\n}\n\n.order-12 {\n  order: 12;\n}\n\n.offset-1 {\n  margin-left: 8.33333%;\n}\n\n.offset-2 {\n  margin-left: 16.66667%;\n}\n\n.offset-3 {\n  margin-left: 25%;\n}\n\n.offset-4 {\n  margin-left: 33.33333%;\n}\n\n.offset-5 {\n  margin-left: 41.66667%;\n}\n\n.offset-6 {\n  margin-left: 50%;\n}\n\n.offset-7 {\n  margin-left: 58.33333%;\n}\n\n.offset-8 {\n  margin-left: 66.66667%;\n}\n\n.offset-9 {\n  margin-left: 75%;\n}\n\n.offset-10 {\n  margin-left: 83.33333%;\n}\n\n.offset-11 {\n  margin-left: 91.66667%;\n}\n\n@media (min-width: 576px) {\n  .col-sm {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-sm-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%;\n  }\n  .col-sm-1 {\n    flex: 0 0 8.33333%;\n    max-width: 8.33333%;\n  }\n  .col-sm-2 {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%;\n  }\n  .col-sm-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-sm-4 {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%;\n  }\n  .col-sm-5 {\n    flex: 0 0 41.66667%;\n    max-width: 41.66667%;\n  }\n  .col-sm-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-sm-7 {\n    flex: 0 0 58.33333%;\n    max-width: 58.33333%;\n  }\n  .col-sm-8 {\n    flex: 0 0 66.66667%;\n    max-width: 66.66667%;\n  }\n  .col-sm-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-sm-10 {\n    flex: 0 0 83.33333%;\n    max-width: 83.33333%;\n  }\n  .col-sm-11 {\n    flex: 0 0 91.66667%;\n    max-width: 91.66667%;\n  }\n  .col-sm-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-sm-first {\n    order: -1;\n  }\n  .order-sm-last {\n    order: 13;\n  }\n  .order-sm-0 {\n    order: 0;\n  }\n  .order-sm-1 {\n    order: 1;\n  }\n  .order-sm-2 {\n    order: 2;\n  }\n  .order-sm-3 {\n    order: 3;\n  }\n  .order-sm-4 {\n    order: 4;\n  }\n  .order-sm-5 {\n    order: 5;\n  }\n  .order-sm-6 {\n    order: 6;\n  }\n  .order-sm-7 {\n    order: 7;\n  }\n  .order-sm-8 {\n    order: 8;\n  }\n  .order-sm-9 {\n    order: 9;\n  }\n  .order-sm-10 {\n    order: 10;\n  }\n  .order-sm-11 {\n    order: 11;\n  }\n  .order-sm-12 {\n    order: 12;\n  }\n  .offset-sm-0 {\n    margin-left: 0;\n  }\n  .offset-sm-1 {\n    margin-left: 8.33333%;\n  }\n  .offset-sm-2 {\n    margin-left: 16.66667%;\n  }\n  .offset-sm-3 {\n    margin-left: 25%;\n  }\n  .offset-sm-4 {\n    margin-left: 33.33333%;\n  }\n  .offset-sm-5 {\n    margin-left: 41.66667%;\n  }\n  .offset-sm-6 {\n    margin-left: 50%;\n  }\n  .offset-sm-7 {\n    margin-left: 58.33333%;\n  }\n  .offset-sm-8 {\n    margin-left: 66.66667%;\n  }\n  .offset-sm-9 {\n    margin-left: 75%;\n  }\n  .offset-sm-10 {\n    margin-left: 83.33333%;\n  }\n  .offset-sm-11 {\n    margin-left: 91.66667%;\n  }\n}\n\n@media (min-width: 768px) {\n  .col-md {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-md-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%;\n  }\n  .col-md-1 {\n    flex: 0 0 8.33333%;\n    max-width: 8.33333%;\n  }\n  .col-md-2 {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%;\n  }\n  .col-md-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-md-4 {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%;\n  }\n  .col-md-5 {\n    flex: 0 0 41.66667%;\n    max-width: 41.66667%;\n  }\n  .col-md-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-md-7 {\n    flex: 0 0 58.33333%;\n    max-width: 58.33333%;\n  }\n  .col-md-8 {\n    flex: 0 0 66.66667%;\n    max-width: 66.66667%;\n  }\n  .col-md-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-md-10 {\n    flex: 0 0 83.33333%;\n    max-width: 83.33333%;\n  }\n  .col-md-11 {\n    flex: 0 0 91.66667%;\n    max-width: 91.66667%;\n  }\n  .col-md-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-md-first {\n    order: -1;\n  }\n  .order-md-last {\n    order: 13;\n  }\n  .order-md-0 {\n    order: 0;\n  }\n  .order-md-1 {\n    order: 1;\n  }\n  .order-md-2 {\n    order: 2;\n  }\n  .order-md-3 {\n    order: 3;\n  }\n  .order-md-4 {\n    order: 4;\n  }\n  .order-md-5 {\n    order: 5;\n  }\n  .order-md-6 {\n    order: 6;\n  }\n  .order-md-7 {\n    order: 7;\n  }\n  .order-md-8 {\n    order: 8;\n  }\n  .order-md-9 {\n    order: 9;\n  }\n  .order-md-10 {\n    order: 10;\n  }\n  .order-md-11 {\n    order: 11;\n  }\n  .order-md-12 {\n    order: 12;\n  }\n  .offset-md-0 {\n    margin-left: 0;\n  }\n  .offset-md-1 {\n    margin-left: 8.33333%;\n  }\n  .offset-md-2 {\n    margin-left: 16.66667%;\n  }\n  .offset-md-3 {\n    margin-left: 25%;\n  }\n  .offset-md-4 {\n    margin-left: 33.33333%;\n  }\n  .offset-md-5 {\n    margin-left: 41.66667%;\n  }\n  .offset-md-6 {\n    margin-left: 50%;\n  }\n  .offset-md-7 {\n    margin-left: 58.33333%;\n  }\n  .offset-md-8 {\n    margin-left: 66.66667%;\n  }\n  .offset-md-9 {\n    margin-left: 75%;\n  }\n  .offset-md-10 {\n    margin-left: 83.33333%;\n  }\n  .offset-md-11 {\n    margin-left: 91.66667%;\n  }\n}\n\n@media (min-width: 992px) {\n  .col-lg {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-lg-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%;\n  }\n  .col-lg-1 {\n    flex: 0 0 8.33333%;\n    max-width: 8.33333%;\n  }\n  .col-lg-2 {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%;\n  }\n  .col-lg-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-lg-4 {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%;\n  }\n  .col-lg-5 {\n    flex: 0 0 41.66667%;\n    max-width: 41.66667%;\n  }\n  .col-lg-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-lg-7 {\n    flex: 0 0 58.33333%;\n    max-width: 58.33333%;\n  }\n  .col-lg-8 {\n    flex: 0 0 66.66667%;\n    max-width: 66.66667%;\n  }\n  .col-lg-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-lg-10 {\n    flex: 0 0 83.33333%;\n    max-width: 83.33333%;\n  }\n  .col-lg-11 {\n    flex: 0 0 91.66667%;\n    max-width: 91.66667%;\n  }\n  .col-lg-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-lg-first {\n    order: -1;\n  }\n  .order-lg-last {\n    order: 13;\n  }\n  .order-lg-0 {\n    order: 0;\n  }\n  .order-lg-1 {\n    order: 1;\n  }\n  .order-lg-2 {\n    order: 2;\n  }\n  .order-lg-3 {\n    order: 3;\n  }\n  .order-lg-4 {\n    order: 4;\n  }\n  .order-lg-5 {\n    order: 5;\n  }\n  .order-lg-6 {\n    order: 6;\n  }\n  .order-lg-7 {\n    order: 7;\n  }\n  .order-lg-8 {\n    order: 8;\n  }\n  .order-lg-9 {\n    order: 9;\n  }\n  .order-lg-10 {\n    order: 10;\n  }\n  .order-lg-11 {\n    order: 11;\n  }\n  .order-lg-12 {\n    order: 12;\n  }\n  .offset-lg-0 {\n    margin-left: 0;\n  }\n  .offset-lg-1 {\n    margin-left: 8.33333%;\n  }\n  .offset-lg-2 {\n    margin-left: 16.66667%;\n  }\n  .offset-lg-3 {\n    margin-left: 25%;\n  }\n  .offset-lg-4 {\n    margin-left: 33.33333%;\n  }\n  .offset-lg-5 {\n    margin-left: 41.66667%;\n  }\n  .offset-lg-6 {\n    margin-left: 50%;\n  }\n  .offset-lg-7 {\n    margin-left: 58.33333%;\n  }\n  .offset-lg-8 {\n    margin-left: 66.66667%;\n  }\n  .offset-lg-9 {\n    margin-left: 75%;\n  }\n  .offset-lg-10 {\n    margin-left: 83.33333%;\n  }\n  .offset-lg-11 {\n    margin-left: 91.66667%;\n  }\n}\n\n@media (min-width: 1200px) {\n  .col-xl {\n    flex-basis: 0;\n    flex-grow: 1;\n    max-width: 100%;\n  }\n  .col-xl-auto {\n    flex: 0 0 auto;\n    width: auto;\n    max-width: 100%;\n  }\n  .col-xl-1 {\n    flex: 0 0 8.33333%;\n    max-width: 8.33333%;\n  }\n  .col-xl-2 {\n    flex: 0 0 16.66667%;\n    max-width: 16.66667%;\n  }\n  .col-xl-3 {\n    flex: 0 0 25%;\n    max-width: 25%;\n  }\n  .col-xl-4 {\n    flex: 0 0 33.33333%;\n    max-width: 33.33333%;\n  }\n  .col-xl-5 {\n    flex: 0 0 41.66667%;\n    max-width: 41.66667%;\n  }\n  .col-xl-6 {\n    flex: 0 0 50%;\n    max-width: 50%;\n  }\n  .col-xl-7 {\n    flex: 0 0 58.33333%;\n    max-width: 58.33333%;\n  }\n  .col-xl-8 {\n    flex: 0 0 66.66667%;\n    max-width: 66.66667%;\n  }\n  .col-xl-9 {\n    flex: 0 0 75%;\n    max-width: 75%;\n  }\n  .col-xl-10 {\n    flex: 0 0 83.33333%;\n    max-width: 83.33333%;\n  }\n  .col-xl-11 {\n    flex: 0 0 91.66667%;\n    max-width: 91.66667%;\n  }\n  .col-xl-12 {\n    flex: 0 0 100%;\n    max-width: 100%;\n  }\n  .order-xl-first {\n    order: -1;\n  }\n  .order-xl-last {\n    order: 13;\n  }\n  .order-xl-0 {\n    order: 0;\n  }\n  .order-xl-1 {\n    order: 1;\n  }\n  .order-xl-2 {\n    order: 2;\n  }\n  .order-xl-3 {\n    order: 3;\n  }\n  .order-xl-4 {\n    order: 4;\n  }\n  .order-xl-5 {\n    order: 5;\n  }\n  .order-xl-6 {\n    order: 6;\n  }\n  .order-xl-7 {\n    order: 7;\n  }\n  .order-xl-8 {\n    order: 8;\n  }\n  .order-xl-9 {\n    order: 9;\n  }\n  .order-xl-10 {\n    order: 10;\n  }\n  .order-xl-11 {\n    order: 11;\n  }\n  .order-xl-12 {\n    order: 12;\n  }\n  .offset-xl-0 {\n    margin-left: 0;\n  }\n  .offset-xl-1 {\n    margin-left: 8.33333%;\n  }\n  .offset-xl-2 {\n    margin-left: 16.66667%;\n  }\n  .offset-xl-3 {\n    margin-left: 25%;\n  }\n  .offset-xl-4 {\n    margin-left: 33.33333%;\n  }\n  .offset-xl-5 {\n    margin-left: 41.66667%;\n  }\n  .offset-xl-6 {\n    margin-left: 50%;\n  }\n  .offset-xl-7 {\n    margin-left: 58.33333%;\n  }\n  .offset-xl-8 {\n    margin-left: 66.66667%;\n  }\n  .offset-xl-9 {\n    margin-left: 75%;\n  }\n  .offset-xl-10 {\n    margin-left: 83.33333%;\n  }\n  .offset-xl-11 {\n    margin-left: 91.66667%;\n  }\n}\n\n.table {\n  width: 100%;\n  margin-bottom: 1rem;\n  background-color: transparent;\n}\n.table th,\n.table td {\n  padding: 0.75rem;\n  vertical-align: top;\n  border-top: 1px solid #dee2e6;\n}\n.table thead th {\n  vertical-align: bottom;\n  border-bottom: 2px solid #dee2e6;\n}\n.table tbody + tbody {\n  border-top: 2px solid #dee2e6;\n}\n.table .table {\n  background-color: #fff;\n}\n\n.table-sm th,\n.table-sm td {\n  padding: 0.3rem;\n}\n\n.table-bordered {\n  border: 1px solid #dee2e6;\n}\n.table-bordered th,\n.table-bordered td {\n  border: 1px solid #dee2e6;\n}\n.table-bordered thead th,\n.table-bordered thead td {\n  border-bottom-width: 2px;\n}\n\n.table-borderless th,\n.table-borderless td,\n.table-borderless thead th,\n.table-borderless tbody + tbody {\n  border: 0;\n}\n\n.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.table-hover tbody tr:hover {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.table-primary,\n.table-primary > th,\n.table-primary > td {\n  background-color: #fec3b8;\n}\n\n.table-primary th,\n.table-primary td,\n.table-primary thead th,\n.table-primary tbody + tbody {\n  border-color: #fd907a;\n}\n\n.table-hover .table-primary:hover {\n  background-color: #feae9f;\n}\n.table-hover .table-primary:hover > td,\n.table-hover .table-primary:hover > th {\n  background-color: #feae9f;\n}\n\n.table-secondary,\n.table-secondary > th,\n.table-secondary > td {\n  background-color: #d6d8db;\n}\n\n.table-secondary th,\n.table-secondary td,\n.table-secondary thead th,\n.table-secondary tbody + tbody {\n  border-color: #b3b7bb;\n}\n\n.table-hover .table-secondary:hover {\n  background-color: #c8cbcf;\n}\n.table-hover .table-secondary:hover > td,\n.table-hover .table-secondary:hover > th {\n  background-color: #c8cbcf;\n}\n\n.table-success,\n.table-success > th,\n.table-success > td {\n  background-color: #c3e6cb;\n}\n\n.table-success th,\n.table-success td,\n.table-success thead th,\n.table-success tbody + tbody {\n  border-color: #8fd19e;\n}\n\n.table-hover .table-success:hover {\n  background-color: #b1dfbb;\n}\n.table-hover .table-success:hover > td,\n.table-hover .table-success:hover > th {\n  background-color: #b1dfbb;\n}\n\n.table-info,\n.table-info > th,\n.table-info > td {\n  background-color: #bee5eb;\n}\n\n.table-info th,\n.table-info td,\n.table-info thead th,\n.table-info tbody + tbody {\n  border-color: #86cfda;\n}\n\n.table-hover .table-info:hover {\n  background-color: #abdde5;\n}\n.table-hover .table-info:hover > td,\n.table-hover .table-info:hover > th {\n  background-color: #abdde5;\n}\n\n.table-warning,\n.table-warning > th,\n.table-warning > td {\n  background-color: #fee9be;\n}\n\n.table-warning th,\n.table-warning td,\n.table-warning thead th,\n.table-warning tbody + tbody {\n  border-color: #fdd586;\n}\n\n.table-hover .table-warning:hover {\n  background-color: #fee1a5;\n}\n.table-hover .table-warning:hover > td,\n.table-hover .table-warning:hover > th {\n  background-color: #fee1a5;\n}\n\n.table-danger,\n.table-danger > th,\n.table-danger > td {\n  background-color: #f5c6cb;\n}\n\n.table-danger th,\n.table-danger td,\n.table-danger thead th,\n.table-danger tbody + tbody {\n  border-color: #ed969e;\n}\n\n.table-hover .table-danger:hover {\n  background-color: #f1b0b7;\n}\n.table-hover .table-danger:hover > td,\n.table-hover .table-danger:hover > th {\n  background-color: #f1b0b7;\n}\n\n.table-light,\n.table-light > th,\n.table-light > td {\n  background-color: #f8f8f8;\n}\n\n.table-light th,\n.table-light td,\n.table-light thead th,\n.table-light tbody + tbody {\n  border-color: #f2f2f2;\n}\n\n.table-hover .table-light:hover {\n  background-color: #ebebeb;\n}\n.table-hover .table-light:hover > td,\n.table-hover .table-light:hover > th {\n  background-color: #ebebeb;\n}\n\n.table-dark,\n.table-dark > th,\n.table-dark > td {\n  background-color: #c6c8ca;\n}\n\n.table-dark th,\n.table-dark td,\n.table-dark thead th,\n.table-dark tbody + tbody {\n  border-color: #95999c;\n}\n\n.table-hover .table-dark:hover {\n  background-color: #b9bbbe;\n}\n.table-hover .table-dark:hover > td,\n.table-hover .table-dark:hover > th {\n  background-color: #b9bbbe;\n}\n\n.table-active,\n.table-active > th,\n.table-active > td {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.table-hover .table-active:hover {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n.table-hover .table-active:hover > td,\n.table-hover .table-active:hover > th {\n  background-color: rgba(0, 0, 0, 0.075);\n}\n\n.table .thead-dark th {\n  color: #fff;\n  background-color: #212529;\n  border-color: #32383e;\n}\n\n.table .thead-light th {\n  color: #495057;\n  background-color: #e9ecef;\n  border-color: #dee2e6;\n}\n\n.table-dark {\n  color: #fff;\n  background-color: #212529;\n}\n.table-dark th,\n.table-dark td,\n.table-dark thead th {\n  border-color: #32383e;\n}\n.table-dark.table-bordered {\n  border: 0;\n}\n.table-dark.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n.table-dark.table-hover tbody tr:hover {\n  background-color: rgba(255, 255, 255, 0.075);\n}\n\n@media (max-width: 575.98px) {\n  .table-responsive-sm {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n    -ms-overflow-style: -ms-autohiding-scrollbar;\n  }\n  .table-responsive-sm > .table-bordered {\n    border: 0;\n  }\n}\n\n@media (max-width: 767.98px) {\n  .table-responsive-md {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n    -ms-overflow-style: -ms-autohiding-scrollbar;\n  }\n  .table-responsive-md > .table-bordered {\n    border: 0;\n  }\n}\n\n@media (max-width: 991.98px) {\n  .table-responsive-lg {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n    -ms-overflow-style: -ms-autohiding-scrollbar;\n  }\n  .table-responsive-lg > .table-bordered {\n    border: 0;\n  }\n}\n\n@media (max-width: 1199.98px) {\n  .table-responsive-xl {\n    display: block;\n    width: 100%;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n    -ms-overflow-style: -ms-autohiding-scrollbar;\n  }\n  .table-responsive-xl > .table-bordered {\n    border: 0;\n  }\n}\n\n.table-responsive {\n  display: block;\n  width: 100%;\n  overflow-x: auto;\n  -webkit-overflow-scrolling: touch;\n  -ms-overflow-style: -ms-autohiding-scrollbar;\n}\n.table-responsive > .table-bordered {\n  border: 0;\n}\n\n.form-control {\n  display: block;\n  width: 100%;\n  height: calc(2.25rem + 2px);\n  padding: 0.375rem 0.75rem;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .form-control {\n    transition: none;\n  }\n}\n.form-control::-ms-expand {\n  background-color: transparent;\n  border: 0;\n}\n.form-control:focus {\n  color: #495057;\n  background-color: #fff;\n  border-color: #ff927d;\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(252, 42, 0, 0.25);\n}\n.form-control::placeholder {\n  color: #6c757d;\n  opacity: 1;\n}\n.form-control:disabled,\n.form-control[readonly] {\n  background-color: #e9ecef;\n  opacity: 1;\n}\n\nselect.form-control:focus::-ms-value {\n  color: #495057;\n  background-color: #fff;\n}\n\n.form-control-file,\n.form-control-range {\n  display: block;\n  width: 100%;\n}\n\n.col-form-label {\n  padding-top: calc(0.375rem + 1px);\n  padding-bottom: calc(0.375rem + 1px);\n  margin-bottom: 0;\n  font-size: inherit;\n  line-height: 1.5;\n}\n\n.col-form-label-lg {\n  padding-top: calc(0.5rem + 1px);\n  padding-bottom: calc(0.5rem + 1px);\n  font-size: 1.25rem;\n  line-height: 1.5;\n}\n\n.col-form-label-sm {\n  padding-top: calc(0.25rem + 1px);\n  padding-bottom: calc(0.25rem + 1px);\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n  margin-bottom: 0;\n  line-height: 1.5;\n  color: #212529;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: 1px 0;\n}\n.form-control-plaintext.form-control-sm,\n.form-control-plaintext.form-control-lg {\n  padding-right: 0;\n  padding-left: 0;\n}\n\n.form-control-sm {\n  height: calc(1.8125rem + 2px);\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem;\n}\n\n.form-control-lg {\n  height: calc(2.875rem + 2px);\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem;\n}\n\nselect.form-control[size],\nselect.form-control[multiple] {\n  height: auto;\n}\n\ntextarea.form-control {\n  height: auto;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-text {\n  display: block;\n  margin-top: 0.25rem;\n}\n\n.form-row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -5px;\n  margin-left: -5px;\n}\n.form-row > .col,\n.form-row > [class*=\"col-\"] {\n  padding-right: 5px;\n  padding-left: 5px;\n}\n\n.form-check {\n  position: relative;\n  display: block;\n  padding-left: 1.25rem;\n}\n\n.form-check-input {\n  position: absolute;\n  margin-top: 0.3rem;\n  margin-left: -1.25rem;\n}\n.form-check-input:disabled ~ .form-check-label {\n  color: #6c757d;\n}\n\n.form-check-label {\n  margin-bottom: 0;\n}\n\n.form-check-inline {\n  display: inline-flex;\n  align-items: center;\n  padding-left: 0;\n  margin-right: 0.75rem;\n}\n.form-check-inline .form-check-input {\n  position: static;\n  margin-top: 0;\n  margin-right: 0.3125rem;\n  margin-left: 0;\n}\n\n.valid-feedback {\n  display: none;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 80%;\n  color: #28a745;\n}\n\n.valid-tooltip {\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n  display: none;\n  max-width: 100%;\n  padding: 0.25rem 0.5rem;\n  margin-top: 0.1rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #fff;\n  background-color: rgba(40, 167, 69, 0.9);\n  border-radius: 0.25rem;\n}\n\n.was-validated .form-control:valid,\n.form-control.is-valid {\n  border-color: #28a745;\n  padding-right: 2.25rem;\n  background-repeat: no-repeat;\n  background-position: center right calc(2.25rem / 4);\n  background-size: calc(2.25rem / 2) calc(2.25rem / 2);\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\");\n}\n.was-validated .form-control:valid:focus,\n.form-control.is-valid:focus {\n  border-color: #28a745;\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n.was-validated .form-control:valid ~ .valid-feedback,\n.was-validated .form-control:valid ~ .valid-tooltip,\n.form-control.is-valid ~ .valid-feedback,\n.form-control.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated textarea.form-control:valid,\ntextarea.form-control.is-valid {\n  padding-right: 2.25rem;\n  background-position: top calc(2.25rem / 4) right calc(2.25rem / 4);\n}\n\n.was-validated .custom-select:valid,\n.custom-select.is-valid {\n  border-color: #28a745;\n  padding-right: 3.4375rem;\n  background: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\")\n      no-repeat right 0.75rem center/8px 10px,\n    url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\")\n      no-repeat center right 1.75rem/1.125rem 1.125rem;\n}\n.was-validated .custom-select:valid:focus,\n.custom-select.is-valid:focus {\n  border-color: #28a745;\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n.was-validated .custom-select:valid ~ .valid-feedback,\n.was-validated .custom-select:valid ~ .valid-tooltip,\n.custom-select.is-valid ~ .valid-feedback,\n.custom-select.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated .form-control-file:valid ~ .valid-feedback,\n.was-validated .form-control-file:valid ~ .valid-tooltip,\n.form-control-file.is-valid ~ .valid-feedback,\n.form-control-file.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated .form-check-input:valid ~ .form-check-label,\n.form-check-input.is-valid ~ .form-check-label {\n  color: #28a745;\n}\n\n.was-validated .form-check-input:valid ~ .valid-feedback,\n.was-validated .form-check-input:valid ~ .valid-tooltip,\n.form-check-input.is-valid ~ .valid-feedback,\n.form-check-input.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated .custom-control-input:valid ~ .custom-control-label,\n.custom-control-input.is-valid ~ .custom-control-label {\n  color: #28a745;\n}\n.was-validated .custom-control-input:valid ~ .custom-control-label::before,\n.custom-control-input.is-valid ~ .custom-control-label::before {\n  border-color: #28a745;\n}\n\n.was-validated .custom-control-input:valid ~ .valid-feedback,\n.was-validated .custom-control-input:valid ~ .valid-tooltip,\n.custom-control-input.is-valid ~ .valid-feedback,\n.custom-control-input.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before,\n.custom-control-input.is-valid:checked ~ .custom-control-label::before {\n  border-color: #34ce57;\n  background-color: #34ce57;\n}\n\n.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before,\n.custom-control-input.is-valid:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before,\n.custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #28a745;\n}\n\n.was-validated .custom-file-input:valid ~ .custom-file-label,\n.custom-file-input.is-valid ~ .custom-file-label {\n  border-color: #28a745;\n}\n\n.was-validated .custom-file-input:valid ~ .valid-feedback,\n.was-validated .custom-file-input:valid ~ .valid-tooltip,\n.custom-file-input.is-valid ~ .valid-feedback,\n.custom-file-input.is-valid ~ .valid-tooltip {\n  display: block;\n}\n\n.was-validated .custom-file-input:valid:focus ~ .custom-file-label,\n.custom-file-input.is-valid:focus ~ .custom-file-label {\n  border-color: #28a745;\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.invalid-feedback {\n  display: none;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 80%;\n  color: #dc3545;\n}\n\n.invalid-tooltip {\n  position: absolute;\n  top: 100%;\n  z-index: 5;\n  display: none;\n  max-width: 100%;\n  padding: 0.25rem 0.5rem;\n  margin-top: 0.1rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #fff;\n  background-color: rgba(220, 53, 69, 0.9);\n  border-radius: 0.25rem;\n}\n\n.was-validated .form-control:invalid,\n.form-control.is-invalid {\n  border-color: #dc3545;\n  padding-right: 2.25rem;\n  background-repeat: no-repeat;\n  background-position: center right calc(2.25rem / 4);\n  background-size: calc(2.25rem / 2) calc(2.25rem / 2);\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23d9534f' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E\");\n}\n.was-validated .form-control:invalid:focus,\n.form-control.is-invalid:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n.was-validated .form-control:invalid ~ .invalid-feedback,\n.was-validated .form-control:invalid ~ .invalid-tooltip,\n.form-control.is-invalid ~ .invalid-feedback,\n.form-control.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated textarea.form-control:invalid,\ntextarea.form-control.is-invalid {\n  padding-right: 2.25rem;\n  background-position: top calc(2.25rem / 4) right calc(2.25rem / 4);\n}\n\n.was-validated .custom-select:invalid,\n.custom-select.is-invalid {\n  border-color: #dc3545;\n  padding-right: 3.4375rem;\n  background: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\")\n      no-repeat right 0.75rem center/8px 10px,\n    url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23d9534f' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E\")\n      no-repeat center right 1.75rem/1.125rem 1.125rem;\n}\n.was-validated .custom-select:invalid:focus,\n.custom-select.is-invalid:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n.was-validated .custom-select:invalid ~ .invalid-feedback,\n.was-validated .custom-select:invalid ~ .invalid-tooltip,\n.custom-select.is-invalid ~ .invalid-feedback,\n.custom-select.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .form-control-file:invalid ~ .invalid-feedback,\n.was-validated .form-control-file:invalid ~ .invalid-tooltip,\n.form-control-file.is-invalid ~ .invalid-feedback,\n.form-control-file.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .form-check-input:invalid ~ .form-check-label,\n.form-check-input.is-invalid ~ .form-check-label {\n  color: #dc3545;\n}\n\n.was-validated .form-check-input:invalid ~ .invalid-feedback,\n.was-validated .form-check-input:invalid ~ .invalid-tooltip,\n.form-check-input.is-invalid ~ .invalid-feedback,\n.form-check-input.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .custom-control-input:invalid ~ .custom-control-label,\n.custom-control-input.is-invalid ~ .custom-control-label {\n  color: #dc3545;\n}\n.was-validated .custom-control-input:invalid ~ .custom-control-label::before,\n.custom-control-input.is-invalid ~ .custom-control-label::before {\n  border-color: #dc3545;\n}\n\n.was-validated .custom-control-input:invalid ~ .invalid-feedback,\n.was-validated .custom-control-input:invalid ~ .invalid-tooltip,\n.custom-control-input.is-invalid ~ .invalid-feedback,\n.custom-control-input.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before,\n.custom-control-input.is-invalid:checked ~ .custom-control-label::before {\n  border-color: #e4606d;\n  background-color: #e4606d;\n}\n\n.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before,\n.custom-control-input.is-invalid:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before,\n.custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #dc3545;\n}\n\n.was-validated .custom-file-input:invalid ~ .custom-file-label,\n.custom-file-input.is-invalid ~ .custom-file-label {\n  border-color: #dc3545;\n}\n\n.was-validated .custom-file-input:invalid ~ .invalid-feedback,\n.was-validated .custom-file-input:invalid ~ .invalid-tooltip,\n.custom-file-input.is-invalid ~ .invalid-feedback,\n.custom-file-input.is-invalid ~ .invalid-tooltip {\n  display: block;\n}\n\n.was-validated .custom-file-input:invalid:focus ~ .custom-file-label,\n.custom-file-input.is-invalid:focus ~ .custom-file-label {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.form-inline {\n  display: flex;\n  flex-flow: row wrap;\n  align-items: center;\n}\n.form-inline .form-check {\n  width: 100%;\n}\n@media (min-width: 576px) {\n  .form-inline label {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 0;\n  }\n  .form-inline .form-group {\n    display: flex;\n    flex: 0 0 auto;\n    flex-flow: row wrap;\n    align-items: center;\n    margin-bottom: 0;\n  }\n  .form-inline .form-control {\n    display: inline-block;\n    width: auto;\n    vertical-align: middle;\n  }\n  .form-inline .form-control-plaintext {\n    display: inline-block;\n  }\n  .form-inline .input-group,\n  .form-inline .custom-select {\n    width: auto;\n  }\n  .form-inline .form-check {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: auto;\n    padding-left: 0;\n  }\n  .form-inline .form-check-input {\n    position: relative;\n    margin-top: 0;\n    margin-right: 0.25rem;\n    margin-left: 0;\n  }\n  .form-inline .custom-control {\n    align-items: center;\n    justify-content: center;\n  }\n  .form-inline .custom-control-label {\n    margin-bottom: 0;\n  }\n}\n\n.btn {\n  display: inline-block;\n  font-weight: 400;\n  color: #212529;\n  text-align: center;\n  vertical-align: middle;\n  user-select: none;\n  background-color: transparent;\n  border: 1px solid transparent;\n  padding: 0.375rem 0.75rem;\n  font-size: 1rem;\n  line-height: 1.5;\n  border-radius: 0.25rem;\n  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,\n    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .btn {\n    transition: none;\n  }\n}\n.btn:hover {\n  color: #212529;\n  text-decoration: none;\n}\n.btn:focus,\n.btn.focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(252, 42, 0, 0.25);\n}\n.btn.disabled,\n.btn:disabled {\n  opacity: 0.65;\n}\n.btn:not(:disabled):not(.disabled) {\n  cursor: pointer;\n}\n\na.btn.disabled,\nfieldset:disabled a.btn {\n  pointer-events: none;\n}\n\n.btn-primary {\n  color: #fff;\n  background-color: #fc2a00;\n  border-color: #fc2a00;\n}\n.btn-primary:hover {\n  color: #fff;\n  background-color: #d62400;\n  border-color: #c92200;\n}\n.btn-primary:focus,\n.btn-primary.focus {\n  box-shadow: 0 0 0 0.2rem rgba(252, 74, 38, 0.5);\n}\n.btn-primary.disabled,\n.btn-primary:disabled {\n  color: #fff;\n  background-color: #fc2a00;\n  border-color: #fc2a00;\n}\n.btn-primary:not(:disabled):not(.disabled):active,\n.btn-primary:not(:disabled):not(.disabled).active,\n.show > .btn-primary.dropdown-toggle {\n  color: #fff;\n  background-color: #c92200;\n  border-color: #bc1f00;\n}\n.btn-primary:not(:disabled):not(.disabled):active:focus,\n.btn-primary:not(:disabled):not(.disabled).active:focus,\n.show > .btn-primary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(252, 74, 38, 0.5);\n}\n\n.btn-secondary {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-secondary:hover {\n  color: #fff;\n  background-color: #5a6268;\n  border-color: #545b62;\n}\n.btn-secondary:focus,\n.btn-secondary.focus {\n  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);\n}\n.btn-secondary.disabled,\n.btn-secondary:disabled {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-secondary:not(:disabled):not(.disabled):active,\n.btn-secondary:not(:disabled):not(.disabled).active,\n.show > .btn-secondary.dropdown-toggle {\n  color: #fff;\n  background-color: #545b62;\n  border-color: #4e555b;\n}\n.btn-secondary:not(:disabled):not(.disabled):active:focus,\n.btn-secondary:not(:disabled):not(.disabled).active:focus,\n.show > .btn-secondary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);\n}\n\n.btn-success {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n.btn-success:hover {\n  color: #fff;\n  background-color: #218838;\n  border-color: #1e7e34;\n}\n.btn-success:focus,\n.btn-success.focus {\n  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);\n}\n.btn-success.disabled,\n.btn-success:disabled {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n.btn-success:not(:disabled):not(.disabled):active,\n.btn-success:not(:disabled):not(.disabled).active,\n.show > .btn-success.dropdown-toggle {\n  color: #fff;\n  background-color: #1e7e34;\n  border-color: #1c7430;\n}\n.btn-success:not(:disabled):not(.disabled):active:focus,\n.btn-success:not(:disabled):not(.disabled).active:focus,\n.show > .btn-success.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);\n}\n\n.btn-info {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-info:hover {\n  color: #fff;\n  background-color: #138496;\n  border-color: #117a8b;\n}\n.btn-info:focus,\n.btn-info.focus {\n  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);\n}\n.btn-info.disabled,\n.btn-info:disabled {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-info:not(:disabled):not(.disabled):active,\n.btn-info:not(:disabled):not(.disabled).active,\n.show > .btn-info.dropdown-toggle {\n  color: #fff;\n  background-color: #117a8b;\n  border-color: #10707f;\n}\n.btn-info:not(:disabled):not(.disabled):active:focus,\n.btn-info:not(:disabled):not(.disabled).active:focus,\n.show > .btn-info.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);\n}\n\n.btn-warning {\n  color: #212529;\n  background-color: #fcaf17;\n  border-color: #fcaf17;\n}\n.btn-warning:hover {\n  color: #212529;\n  background-color: #ea9c03;\n  border-color: #dd9403;\n}\n.btn-warning:focus,\n.btn-warning.focus {\n  box-shadow: 0 0 0 0.2rem rgba(219, 154, 26, 0.5);\n}\n.btn-warning.disabled,\n.btn-warning:disabled {\n  color: #212529;\n  background-color: #fcaf17;\n  border-color: #fcaf17;\n}\n.btn-warning:not(:disabled):not(.disabled):active,\n.btn-warning:not(:disabled):not(.disabled).active,\n.show > .btn-warning.dropdown-toggle {\n  color: #212529;\n  background-color: #dd9403;\n  border-color: #d18b03;\n}\n.btn-warning:not(:disabled):not(.disabled):active:focus,\n.btn-warning:not(:disabled):not(.disabled).active:focus,\n.show > .btn-warning.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(219, 154, 26, 0.5);\n}\n\n.btn-danger {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-danger:hover {\n  color: #fff;\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n.btn-danger:focus,\n.btn-danger.focus {\n  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);\n}\n.btn-danger.disabled,\n.btn-danger:disabled {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-danger:not(:disabled):not(.disabled):active,\n.btn-danger:not(:disabled):not(.disabled).active,\n.show > .btn-danger.dropdown-toggle {\n  color: #fff;\n  background-color: #bd2130;\n  border-color: #b21f2d;\n}\n.btn-danger:not(:disabled):not(.disabled):active:focus,\n.btn-danger:not(:disabled):not(.disabled).active:focus,\n.show > .btn-danger.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);\n}\n\n.btn-light {\n  color: #212529;\n  background-color: #e6e6e6;\n  border-color: #e6e6e6;\n}\n.btn-light:hover {\n  color: #212529;\n  background-color: lightgray;\n  border-color: #cdcdcd;\n}\n.btn-light:focus,\n.btn-light.focus {\n  box-shadow: 0 0 0 0.2rem rgba(200, 201, 202, 0.5);\n}\n.btn-light.disabled,\n.btn-light:disabled {\n  color: #212529;\n  background-color: #e6e6e6;\n  border-color: #e6e6e6;\n}\n.btn-light:not(:disabled):not(.disabled):active,\n.btn-light:not(:disabled):not(.disabled).active,\n.show > .btn-light.dropdown-toggle {\n  color: #212529;\n  background-color: #cdcdcd;\n  border-color: #c6c6c6;\n}\n.btn-light:not(:disabled):not(.disabled):active:focus,\n.btn-light:not(:disabled):not(.disabled).active:focus,\n.show > .btn-light.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(200, 201, 202, 0.5);\n}\n\n.btn-dark {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n.btn-dark:hover {\n  color: #fff;\n  background-color: #23272b;\n  border-color: #1d2124;\n}\n.btn-dark:focus,\n.btn-dark.focus {\n  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);\n}\n.btn-dark.disabled,\n.btn-dark:disabled {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n.btn-dark:not(:disabled):not(.disabled):active,\n.btn-dark:not(:disabled):not(.disabled).active,\n.show > .btn-dark.dropdown-toggle {\n  color: #fff;\n  background-color: #1d2124;\n  border-color: #171a1d;\n}\n.btn-dark:not(:disabled):not(.disabled):active:focus,\n.btn-dark:not(:disabled):not(.disabled).active:focus,\n.show > .btn-dark.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);\n}\n\n.btn-outline-primary {\n  color: #fc2a00;\n  border-color: #fc2a00;\n}\n.btn-outline-primary:hover {\n  color: #fff;\n  background-color: #fc2a00;\n  border-color: #fc2a00;\n}\n.btn-outline-primary:focus,\n.btn-outline-primary.focus {\n  box-shadow: 0 0 0 0.2rem rgba(252, 42, 0, 0.5);\n}\n.btn-outline-primary.disabled,\n.btn-outline-primary:disabled {\n  color: #fc2a00;\n  background-color: transparent;\n}\n.btn-outline-primary:not(:disabled):not(.disabled):active,\n.btn-outline-primary:not(:disabled):not(.disabled).active,\n.show > .btn-outline-primary.dropdown-toggle {\n  color: #fff;\n  background-color: #fc2a00;\n  border-color: #fc2a00;\n}\n.btn-outline-primary:not(:disabled):not(.disabled):active:focus,\n.btn-outline-primary:not(:disabled):not(.disabled).active:focus,\n.show > .btn-outline-primary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(252, 42, 0, 0.5);\n}\n\n.btn-outline-secondary {\n  color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-outline-secondary:hover {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-outline-secondary:focus,\n.btn-outline-secondary.focus {\n  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);\n}\n.btn-outline-secondary.disabled,\n.btn-outline-secondary:disabled {\n  color: #6c757d;\n  background-color: transparent;\n}\n.btn-outline-secondary:not(:disabled):not(.disabled):active,\n.btn-outline-secondary:not(:disabled):not(.disabled).active,\n.show > .btn-outline-secondary.dropdown-toggle {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,\n.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,\n.show > .btn-outline-secondary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);\n}\n\n.btn-outline-success {\n  color: #28a745;\n  border-color: #28a745;\n}\n.btn-outline-success:hover {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n.btn-outline-success:focus,\n.btn-outline-success.focus {\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);\n}\n.btn-outline-success.disabled,\n.btn-outline-success:disabled {\n  color: #28a745;\n  background-color: transparent;\n}\n.btn-outline-success:not(:disabled):not(.disabled):active,\n.btn-outline-success:not(:disabled):not(.disabled).active,\n.show > .btn-outline-success.dropdown-toggle {\n  color: #fff;\n  background-color: #28a745;\n  border-color: #28a745;\n}\n.btn-outline-success:not(:disabled):not(.disabled):active:focus,\n.btn-outline-success:not(:disabled):not(.disabled).active:focus,\n.show > .btn-outline-success.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);\n}\n\n.btn-outline-info {\n  color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-outline-info:hover {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-outline-info:focus,\n.btn-outline-info.focus {\n  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);\n}\n.btn-outline-info.disabled,\n.btn-outline-info:disabled {\n  color: #17a2b8;\n  background-color: transparent;\n}\n.btn-outline-info:not(:disabled):not(.disabled):active,\n.btn-outline-info:not(:disabled):not(.disabled).active,\n.show > .btn-outline-info.dropdown-toggle {\n  color: #fff;\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n.btn-outline-info:not(:disabled):not(.disabled):active:focus,\n.btn-outline-info:not(:disabled):not(.disabled).active:focus,\n.show > .btn-outline-info.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);\n}\n\n.btn-outline-warning {\n  color: #fcaf17;\n  border-color: #fcaf17;\n}\n.btn-outline-warning:hover {\n  color: #212529;\n  background-color: #fcaf17;\n  border-color: #fcaf17;\n}\n.btn-outline-warning:focus,\n.btn-outline-warning.focus {\n  box-shadow: 0 0 0 0.2rem rgba(252, 175, 23, 0.5);\n}\n.btn-outline-warning.disabled,\n.btn-outline-warning:disabled {\n  color: #fcaf17;\n  background-color: transparent;\n}\n.btn-outline-warning:not(:disabled):not(.disabled):active,\n.btn-outline-warning:not(:disabled):not(.disabled).active,\n.show > .btn-outline-warning.dropdown-toggle {\n  color: #212529;\n  background-color: #fcaf17;\n  border-color: #fcaf17;\n}\n.btn-outline-warning:not(:disabled):not(.disabled):active:focus,\n.btn-outline-warning:not(:disabled):not(.disabled).active:focus,\n.show > .btn-outline-warning.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(252, 175, 23, 0.5);\n}\n\n.btn-outline-danger {\n  color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-outline-danger:hover {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-outline-danger:focus,\n.btn-outline-danger.focus {\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);\n}\n.btn-outline-danger.disabled,\n.btn-outline-danger:disabled {\n  color: #dc3545;\n  background-color: transparent;\n}\n.btn-outline-danger:not(:disabled):not(.disabled):active,\n.btn-outline-danger:not(:disabled):not(.disabled).active,\n.show > .btn-outline-danger.dropdown-toggle {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n.btn-outline-danger:not(:disabled):not(.disabled):active:focus,\n.btn-outline-danger:not(:disabled):not(.disabled).active:focus,\n.show > .btn-outline-danger.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);\n}\n\n.btn-outline-light {\n  color: #e6e6e6;\n  border-color: #e6e6e6;\n}\n.btn-outline-light:hover {\n  color: #212529;\n  background-color: #e6e6e6;\n  border-color: #e6e6e6;\n}\n.btn-outline-light:focus,\n.btn-outline-light.focus {\n  box-shadow: 0 0 0 0.2rem rgba(230, 230, 230, 0.5);\n}\n.btn-outline-light.disabled,\n.btn-outline-light:disabled {\n  color: #e6e6e6;\n  background-color: transparent;\n}\n.btn-outline-light:not(:disabled):not(.disabled):active,\n.btn-outline-light:not(:disabled):not(.disabled).active,\n.show > .btn-outline-light.dropdown-toggle {\n  color: #212529;\n  background-color: #e6e6e6;\n  border-color: #e6e6e6;\n}\n.btn-outline-light:not(:disabled):not(.disabled):active:focus,\n.btn-outline-light:not(:disabled):not(.disabled).active:focus,\n.show > .btn-outline-light.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(230, 230, 230, 0.5);\n}\n\n.btn-outline-dark {\n  color: #343a40;\n  border-color: #343a40;\n}\n.btn-outline-dark:hover {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n.btn-outline-dark:focus,\n.btn-outline-dark.focus {\n  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);\n}\n.btn-outline-dark.disabled,\n.btn-outline-dark:disabled {\n  color: #343a40;\n  background-color: transparent;\n}\n.btn-outline-dark:not(:disabled):not(.disabled):active,\n.btn-outline-dark:not(:disabled):not(.disabled).active,\n.show > .btn-outline-dark.dropdown-toggle {\n  color: #fff;\n  background-color: #343a40;\n  border-color: #343a40;\n}\n.btn-outline-dark:not(:disabled):not(.disabled):active:focus,\n.btn-outline-dark:not(:disabled):not(.disabled).active:focus,\n.show > .btn-outline-dark.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);\n}\n\n.btn-link {\n  font-weight: 400;\n  color: #fc2a00;\n}\n.btn-link:hover {\n  color: #b01d00;\n  text-decoration: underline;\n}\n.btn-link:focus,\n.btn-link.focus {\n  text-decoration: underline;\n  box-shadow: none;\n}\n.btn-link:disabled,\n.btn-link.disabled {\n  color: #6c757d;\n  pointer-events: none;\n}\n\n.btn-lg,\n.btn-group-lg > .btn {\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem;\n}\n\n.btn-sm,\n.btn-group-sm > .btn {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem;\n}\n\n.btn-block {\n  display: block;\n  width: 100%;\n}\n.btn-block + .btn-block {\n  margin-top: 0.5rem;\n}\n\ninput[type=\"submit\"].btn-block,\ninput[type=\"reset\"].btn-block,\ninput[type=\"button\"].btn-block {\n  width: 100%;\n}\n\n.fade {\n  transition: opacity 0.15s linear;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .fade {\n    transition: none;\n  }\n}\n.fade:not(.show) {\n  opacity: 0;\n}\n\n.collapse:not(.show) {\n  display: none;\n}\n\n.collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition: height 0.35s ease;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .collapsing {\n    transition: none;\n  }\n}\n\n.dropup,\n.dropright,\n.dropdown,\n.dropleft {\n  position: relative;\n}\n\n.dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0.3em solid;\n  border-right: 0.3em solid transparent;\n  border-bottom: 0;\n  border-left: 0.3em solid transparent;\n}\n\n.dropdown-toggle:empty::after {\n  margin-left: 0;\n}\n\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: 1000;\n  display: none;\n  float: left;\n  min-width: 10rem;\n  padding: 0.5rem 0;\n  margin: 0.125rem 0 0;\n  font-size: 1rem;\n  color: #212529;\n  text-align: left;\n  list-style: none;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0.25rem;\n}\n\n.dropdown-menu-right {\n  right: 0;\n  left: auto;\n}\n\n@media (min-width: 576px) {\n  .dropdown-menu-sm-right {\n    right: 0;\n    left: auto;\n  }\n}\n\n@media (min-width: 768px) {\n  .dropdown-menu-md-right {\n    right: 0;\n    left: auto;\n  }\n}\n\n@media (min-width: 992px) {\n  .dropdown-menu-lg-right {\n    right: 0;\n    left: auto;\n  }\n}\n\n@media (min-width: 1200px) {\n  .dropdown-menu-xl-right {\n    right: 0;\n    left: auto;\n  }\n}\n\n.dropdown-menu-left {\n  right: auto;\n  left: 0;\n}\n\n@media (min-width: 576px) {\n  .dropdown-menu-sm-left {\n    right: auto;\n    left: 0;\n  }\n}\n\n@media (min-width: 768px) {\n  .dropdown-menu-md-left {\n    right: auto;\n    left: 0;\n  }\n}\n\n@media (min-width: 992px) {\n  .dropdown-menu-lg-left {\n    right: auto;\n    left: 0;\n  }\n}\n\n@media (min-width: 1200px) {\n  .dropdown-menu-xl-left {\n    right: auto;\n    left: 0;\n  }\n}\n\n.dropup .dropdown-menu {\n  top: auto;\n  bottom: 100%;\n  margin-top: 0;\n  margin-bottom: 0.125rem;\n}\n\n.dropup .dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0;\n  border-right: 0.3em solid transparent;\n  border-bottom: 0.3em solid;\n  border-left: 0.3em solid transparent;\n}\n\n.dropup .dropdown-toggle:empty::after {\n  margin-left: 0;\n}\n\n.dropright .dropdown-menu {\n  top: 0;\n  right: auto;\n  left: 100%;\n  margin-top: 0;\n  margin-left: 0.125rem;\n}\n\n.dropright .dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0.3em solid transparent;\n  border-right: 0;\n  border-bottom: 0.3em solid transparent;\n  border-left: 0.3em solid;\n}\n\n.dropright .dropdown-toggle:empty::after {\n  margin-left: 0;\n}\n\n.dropright .dropdown-toggle::after {\n  vertical-align: 0;\n}\n\n.dropleft .dropdown-menu {\n  top: 0;\n  right: 100%;\n  left: auto;\n  margin-top: 0;\n  margin-right: 0.125rem;\n}\n\n.dropleft .dropdown-toggle::after {\n  display: inline-block;\n  margin-left: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n}\n\n.dropleft .dropdown-toggle::after {\n  display: none;\n}\n\n.dropleft .dropdown-toggle::before {\n  display: inline-block;\n  margin-right: 0.255em;\n  vertical-align: 0.255em;\n  content: \"\";\n  border-top: 0.3em solid transparent;\n  border-right: 0.3em solid;\n  border-bottom: 0.3em solid transparent;\n}\n\n.dropleft .dropdown-toggle:empty::after {\n  margin-left: 0;\n}\n\n.dropleft .dropdown-toggle::before {\n  vertical-align: 0;\n}\n\n.dropdown-menu[x-placement^=\"top\"],\n.dropdown-menu[x-placement^=\"right\"],\n.dropdown-menu[x-placement^=\"bottom\"],\n.dropdown-menu[x-placement^=\"left\"] {\n  right: auto;\n  bottom: auto;\n}\n\n.dropdown-divider {\n  height: 0;\n  margin: 0.5rem 0;\n  overflow: hidden;\n  border-top: 1px solid #e9ecef;\n}\n\n.dropdown-item {\n  display: block;\n  width: 100%;\n  padding: 0.25rem 1.5rem;\n  clear: both;\n  font-weight: 400;\n  color: #212529;\n  text-align: inherit;\n  white-space: nowrap;\n  background-color: transparent;\n  border: 0;\n}\n.dropdown-item:first-child {\n  border-top-left-radius: calc(0.25rem - 1px);\n  border-top-right-radius: calc(0.25rem - 1px);\n}\n.dropdown-item:last-child {\n  border-bottom-right-radius: calc(0.25rem - 1px);\n  border-bottom-left-radius: calc(0.25rem - 1px);\n}\n.dropdown-item:hover,\n.dropdown-item:focus {\n  color: #16181b;\n  text-decoration: none;\n  background-color: #f8f9fa;\n}\n.dropdown-item.active,\n.dropdown-item:active {\n  color: #fff;\n  text-decoration: none;\n  background-color: #fc2a00;\n}\n.dropdown-item.disabled,\n.dropdown-item:disabled {\n  color: #6c757d;\n  pointer-events: none;\n  background-color: transparent;\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n.dropdown-header {\n  display: block;\n  padding: 0.5rem 1.5rem;\n  margin-bottom: 0;\n  font-size: 0.875rem;\n  color: #6c757d;\n  white-space: nowrap;\n}\n\n.dropdown-item-text {\n  display: block;\n  padding: 0.25rem 1.5rem;\n  color: #212529;\n}\n\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle;\n}\n.btn-group > .btn,\n.btn-group-vertical > .btn {\n  position: relative;\n  flex: 1 1 auto;\n}\n.btn-group > .btn:hover,\n.btn-group-vertical > .btn:hover {\n  z-index: 1;\n}\n.btn-group > .btn:focus,\n.btn-group > .btn:active,\n.btn-group > .btn.active,\n.btn-group-vertical > .btn:focus,\n.btn-group-vertical > .btn:active,\n.btn-group-vertical > .btn.active {\n  z-index: 1;\n}\n\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n}\n.btn-toolbar .input-group {\n  width: auto;\n}\n\n.btn-group > .btn:not(:first-child),\n.btn-group > .btn-group:not(:first-child) {\n  margin-left: -1px;\n}\n\n.btn-group > .btn:not(:last-child):not(.dropdown-toggle),\n.btn-group > .btn-group:not(:last-child) > .btn {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.btn-group > .btn:not(:first-child),\n.btn-group > .btn-group:not(:first-child) > .btn {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.dropdown-toggle-split {\n  padding-right: 0.5625rem;\n  padding-left: 0.5625rem;\n}\n.dropdown-toggle-split::after,\n.dropup .dropdown-toggle-split::after,\n.dropright .dropdown-toggle-split::after {\n  margin-left: 0;\n}\n.dropleft .dropdown-toggle-split::before {\n  margin-right: 0;\n}\n\n.btn-sm + .dropdown-toggle-split,\n.btn-group-sm > .btn + .dropdown-toggle-split {\n  padding-right: 0.375rem;\n  padding-left: 0.375rem;\n}\n\n.btn-lg + .dropdown-toggle-split,\n.btn-group-lg > .btn + .dropdown-toggle-split {\n  padding-right: 0.75rem;\n  padding-left: 0.75rem;\n}\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n}\n.btn-group-vertical > .btn,\n.btn-group-vertical > .btn-group {\n  width: 100%;\n}\n.btn-group-vertical > .btn:not(:first-child),\n.btn-group-vertical > .btn-group:not(:first-child) {\n  margin-top: -1px;\n}\n.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),\n.btn-group-vertical > .btn-group:not(:last-child) > .btn {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.btn-group-vertical > .btn:not(:first-child),\n.btn-group-vertical > .btn-group:not(:first-child) > .btn {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.btn-group-toggle > .btn,\n.btn-group-toggle > .btn-group > .btn {\n  margin-bottom: 0;\n}\n.btn-group-toggle > .btn input[type=\"radio\"],\n.btn-group-toggle > .btn input[type=\"checkbox\"],\n.btn-group-toggle > .btn-group > .btn input[type=\"radio\"],\n.btn-group-toggle > .btn-group > .btn input[type=\"checkbox\"] {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n}\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: stretch;\n  width: 100%;\n}\n.input-group > .form-control,\n.input-group > .form-control-plaintext,\n.input-group > .custom-select,\n.input-group > .custom-file {\n  position: relative;\n  flex: 1 1 auto;\n  width: 1%;\n  margin-bottom: 0;\n}\n.input-group > .form-control + .form-control,\n.input-group > .form-control + .custom-select,\n.input-group > .form-control + .custom-file,\n.input-group > .form-control-plaintext + .form-control,\n.input-group > .form-control-plaintext + .custom-select,\n.input-group > .form-control-plaintext + .custom-file,\n.input-group > .custom-select + .form-control,\n.input-group > .custom-select + .custom-select,\n.input-group > .custom-select + .custom-file,\n.input-group > .custom-file + .form-control,\n.input-group > .custom-file + .custom-select,\n.input-group > .custom-file + .custom-file {\n  margin-left: -1px;\n}\n.input-group > .form-control:focus,\n.input-group > .custom-select:focus,\n.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label {\n  z-index: 3;\n}\n.input-group > .custom-file .custom-file-input:focus {\n  z-index: 4;\n}\n.input-group > .form-control:not(:last-child),\n.input-group > .custom-select:not(:last-child) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.input-group > .form-control:not(:first-child),\n.input-group > .custom-select:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.input-group > .custom-file {\n  display: flex;\n  align-items: center;\n}\n.input-group > .custom-file:not(:last-child) .custom-file-label,\n.input-group > .custom-file:not(:last-child) .custom-file-label::after {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.input-group > .custom-file:not(:first-child) .custom-file-label {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.input-group-prepend,\n.input-group-append {\n  display: flex;\n}\n.input-group-prepend .btn,\n.input-group-append .btn {\n  position: relative;\n  z-index: 2;\n}\n.input-group-prepend .btn:focus,\n.input-group-append .btn:focus {\n  z-index: 3;\n}\n.input-group-prepend .btn + .btn,\n.input-group-prepend .btn + .input-group-text,\n.input-group-prepend .input-group-text + .input-group-text,\n.input-group-prepend .input-group-text + .btn,\n.input-group-append .btn + .btn,\n.input-group-append .btn + .input-group-text,\n.input-group-append .input-group-text + .input-group-text,\n.input-group-append .input-group-text + .btn {\n  margin-left: -1px;\n}\n\n.input-group-prepend {\n  margin-right: -1px;\n}\n\n.input-group-append {\n  margin-left: -1px;\n}\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: 0.375rem 0.75rem;\n  margin-bottom: 0;\n  font-size: 1rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  text-align: center;\n  white-space: nowrap;\n  background-color: #e9ecef;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n}\n.input-group-text input[type=\"radio\"],\n.input-group-text input[type=\"checkbox\"] {\n  margin-top: 0;\n}\n\n.input-group-lg > .form-control:not(textarea),\n.input-group-lg > .custom-select {\n  height: calc(2.875rem + 2px);\n}\n\n.input-group-lg > .form-control,\n.input-group-lg > .custom-select,\n.input-group-lg > .input-group-prepend > .input-group-text,\n.input-group-lg > .input-group-append > .input-group-text,\n.input-group-lg > .input-group-prepend > .btn,\n.input-group-lg > .input-group-append > .btn {\n  padding: 0.5rem 1rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n  border-radius: 0.3rem;\n}\n\n.input-group-sm > .form-control:not(textarea),\n.input-group-sm > .custom-select {\n  height: calc(1.8125rem + 2px);\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .custom-select,\n.input-group-sm > .input-group-prepend > .input-group-text,\n.input-group-sm > .input-group-append > .input-group-text,\n.input-group-sm > .input-group-prepend > .btn,\n.input-group-sm > .input-group-append > .btn {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 0.2rem;\n}\n\n.input-group-lg > .custom-select,\n.input-group-sm > .custom-select {\n  padding-right: 1.75rem;\n}\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.custom-control {\n  position: relative;\n  display: block;\n  min-height: 1.5rem;\n  padding-left: 1.5rem;\n}\n\n.custom-control-inline {\n  display: inline-flex;\n  margin-right: 1rem;\n}\n\n.custom-control-input {\n  position: absolute;\n  z-index: -1;\n  opacity: 0;\n}\n.custom-control-input:checked ~ .custom-control-label::before {\n  color: #fff;\n  border-color: #fc2a00;\n  background-color: #fc2a00;\n}\n.custom-control-input:focus ~ .custom-control-label::before {\n  box-shadow: 0 0 0 0.2rem rgba(252, 42, 0, 0.25);\n}\n.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {\n  border-color: #ff927d;\n}\n.custom-control-input:not(:disabled):active ~ .custom-control-label::before {\n  color: #fff;\n  background-color: #ffbdb0;\n  border-color: #ffbdb0;\n}\n.custom-control-input:disabled ~ .custom-control-label {\n  color: #6c757d;\n}\n.custom-control-input:disabled ~ .custom-control-label::before {\n  background-color: #e9ecef;\n}\n\n.custom-control-label {\n  position: relative;\n  margin-bottom: 0;\n  vertical-align: top;\n}\n.custom-control-label::before {\n  position: absolute;\n  top: 0.25rem;\n  left: -1.5rem;\n  display: block;\n  width: 1rem;\n  height: 1rem;\n  pointer-events: none;\n  content: \"\";\n  background-color: #fff;\n  border: #adb5bd solid 1px;\n}\n.custom-control-label::after {\n  position: absolute;\n  top: 0.25rem;\n  left: -1.5rem;\n  display: block;\n  width: 1rem;\n  height: 1rem;\n  content: \"\";\n  background-repeat: no-repeat;\n  background-position: center center;\n  background-size: 50% 50%;\n}\n\n.custom-checkbox .custom-control-label::before {\n  border-radius: 0.25rem;\n}\n\n.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e\");\n}\n\n.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {\n  border-color: #fc2a00;\n  background-color: #fc2a00;\n}\n\n.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e\");\n}\n\n.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {\n  background-color: rgba(252, 42, 0, 0.5);\n}\n\n.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {\n  background-color: rgba(252, 42, 0, 0.5);\n}\n\n.custom-radio .custom-control-label::before {\n  border-radius: 50%;\n}\n\n.custom-radio .custom-control-input:checked ~ .custom-control-label::after {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e\");\n}\n\n.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {\n  background-color: rgba(252, 42, 0, 0.5);\n}\n\n.custom-switch {\n  padding-left: 2.25rem;\n}\n.custom-switch .custom-control-label::before {\n  left: -2.25rem;\n  width: 1.75rem;\n  pointer-events: all;\n  border-radius: 0.5rem;\n}\n.custom-switch .custom-control-label::after {\n  top: calc(0.25rem + 2px);\n  left: calc(-2.25rem + 2px);\n  width: calc(1rem - 4px);\n  height: calc(1rem - 4px);\n  background-color: #adb5bd;\n  border-radius: 0.5rem;\n  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out,\n    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .custom-switch .custom-control-label::after {\n    transition: none;\n  }\n}\n.custom-switch .custom-control-input:checked ~ .custom-control-label::after {\n  background-color: #fff;\n  transform: translateX(0.75rem);\n}\n.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {\n  background-color: rgba(252, 42, 0, 0.5);\n}\n\n.custom-select {\n  display: inline-block;\n  width: 100%;\n  height: calc(2.25rem + 2px);\n  padding: 0.375rem 1.75rem 0.375rem 0.75rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  vertical-align: middle;\n  background: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\")\n    no-repeat right 0.75rem center/8px 10px;\n  background-color: #fff;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n  appearance: none;\n}\n.custom-select:focus {\n  border-color: #ff927d;\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(255, 146, 125, 0.5);\n}\n.custom-select:focus::-ms-value {\n  color: #495057;\n  background-color: #fff;\n}\n.custom-select[multiple],\n.custom-select[size]:not([size=\"1\"]) {\n  height: auto;\n  padding-right: 0.75rem;\n  background-image: none;\n}\n.custom-select:disabled {\n  color: #6c757d;\n  background-color: #e9ecef;\n}\n.custom-select::-ms-expand {\n  opacity: 0;\n}\n\n.custom-select-sm {\n  height: calc(1.8125rem + 2px);\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n  padding-left: 0.5rem;\n  font-size: 0.875rem;\n}\n\n.custom-select-lg {\n  height: calc(2.875rem + 2px);\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  padding-left: 1rem;\n  font-size: 1.25rem;\n}\n\n.custom-file {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  height: calc(2.25rem + 2px);\n  margin-bottom: 0;\n}\n\n.custom-file-input {\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  height: calc(2.25rem + 2px);\n  margin: 0;\n  opacity: 0;\n}\n.custom-file-input:focus ~ .custom-file-label {\n  border-color: #ff927d;\n  box-shadow: 0 0 0 0.2rem rgba(252, 42, 0, 0.25);\n}\n.custom-file-input:disabled ~ .custom-file-label {\n  background-color: #e9ecef;\n}\n.custom-file-input:lang(en) ~ .custom-file-label::after {\n  content: \"Browse\";\n}\n.custom-file-input ~ .custom-file-label[data-browse]::after {\n  content: attr(data-browse);\n}\n\n.custom-file-label {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1;\n  height: calc(2.25rem + 2px);\n  padding: 0.375rem 0.75rem;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #495057;\n  background-color: #fff;\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n}\n.custom-file-label::after {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 3;\n  display: block;\n  height: 2.25rem;\n  padding: 0.375rem 0.75rem;\n  line-height: 1.5;\n  color: #495057;\n  content: \"Browse\";\n  background-color: #e9ecef;\n  border-left: inherit;\n  border-radius: 0 0.25rem 0.25rem 0;\n}\n\n.custom-range {\n  width: 100%;\n  height: calc(1rem + 0.4rem);\n  padding: 0;\n  background-color: transparent;\n  appearance: none;\n}\n.custom-range:focus {\n  outline: none;\n}\n.custom-range:focus::-webkit-slider-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(252, 42, 0, 0.25);\n}\n.custom-range:focus::-moz-range-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(252, 42, 0, 0.25);\n}\n.custom-range:focus::-ms-thumb {\n  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(252, 42, 0, 0.25);\n}\n.custom-range::-moz-focus-outer {\n  border: 0;\n}\n.custom-range::-webkit-slider-thumb {\n  width: 1rem;\n  height: 1rem;\n  margin-top: -0.25rem;\n  background-color: #fc2a00;\n  border: 0;\n  border-radius: 1rem;\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,\n    box-shadow 0.15s ease-in-out;\n  appearance: none;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .custom-range::-webkit-slider-thumb {\n    transition: none;\n  }\n}\n.custom-range::-webkit-slider-thumb:active {\n  background-color: #ffbdb0;\n}\n.custom-range::-webkit-slider-runnable-track {\n  width: 100%;\n  height: 0.5rem;\n  color: transparent;\n  cursor: pointer;\n  background-color: #dee2e6;\n  border-color: transparent;\n  border-radius: 1rem;\n}\n.custom-range::-moz-range-thumb {\n  width: 1rem;\n  height: 1rem;\n  background-color: #fc2a00;\n  border: 0;\n  border-radius: 1rem;\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,\n    box-shadow 0.15s ease-in-out;\n  appearance: none;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .custom-range::-moz-range-thumb {\n    transition: none;\n  }\n}\n.custom-range::-moz-range-thumb:active {\n  background-color: #ffbdb0;\n}\n.custom-range::-moz-range-track {\n  width: 100%;\n  height: 0.5rem;\n  color: transparent;\n  cursor: pointer;\n  background-color: #dee2e6;\n  border-color: transparent;\n  border-radius: 1rem;\n}\n.custom-range::-ms-thumb {\n  width: 1rem;\n  height: 1rem;\n  margin-top: 0;\n  margin-right: 0.2rem;\n  margin-left: 0.2rem;\n  background-color: #fc2a00;\n  border: 0;\n  border-radius: 1rem;\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,\n    box-shadow 0.15s ease-in-out;\n  appearance: none;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .custom-range::-ms-thumb {\n    transition: none;\n  }\n}\n.custom-range::-ms-thumb:active {\n  background-color: #ffbdb0;\n}\n.custom-range::-ms-track {\n  width: 100%;\n  height: 0.5rem;\n  color: transparent;\n  cursor: pointer;\n  background-color: transparent;\n  border-color: transparent;\n  border-width: 0.5rem;\n}\n.custom-range::-ms-fill-lower {\n  background-color: #dee2e6;\n  border-radius: 1rem;\n}\n.custom-range::-ms-fill-upper {\n  margin-right: 15px;\n  background-color: #dee2e6;\n  border-radius: 1rem;\n}\n.custom-range:disabled::-webkit-slider-thumb {\n  background-color: #adb5bd;\n}\n.custom-range:disabled::-webkit-slider-runnable-track {\n  cursor: default;\n}\n.custom-range:disabled::-moz-range-thumb {\n  background-color: #adb5bd;\n}\n.custom-range:disabled::-moz-range-track {\n  cursor: default;\n}\n.custom-range:disabled::-ms-thumb {\n  background-color: #adb5bd;\n}\n\n.custom-control-label::before,\n.custom-file-label,\n.custom-select {\n  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,\n    box-shadow 0.15s ease-in-out;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .custom-control-label::before,\n  .custom-file-label,\n  .custom-select {\n    transition: none;\n  }\n}\n\n.nav {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: 0.5rem 1rem;\n}\n.nav-link:hover,\n.nav-link:focus {\n  text-decoration: none;\n}\n.nav-link.disabled {\n  color: #6c757d;\n  pointer-events: none;\n  cursor: default;\n}\n\n.nav-tabs {\n  border-bottom: 1px solid #dee2e6;\n}\n.nav-tabs .nav-item {\n  margin-bottom: -1px;\n}\n.nav-tabs .nav-link {\n  border: 1px solid transparent;\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n.nav-tabs .nav-link:hover,\n.nav-tabs .nav-link:focus {\n  border-color: #e9ecef #e9ecef #dee2e6;\n}\n.nav-tabs .nav-link.disabled {\n  color: #6c757d;\n  background-color: transparent;\n  border-color: transparent;\n}\n.nav-tabs .nav-link.active,\n.nav-tabs .nav-item.show .nav-link {\n  color: #495057;\n  background-color: #fff;\n  border-color: #dee2e6 #dee2e6 #fff;\n}\n.nav-tabs .dropdown-menu {\n  margin-top: -1px;\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.nav-pills .nav-link {\n  border-radius: 0.25rem;\n}\n\n.nav-pills .nav-link.active,\n.nav-pills .show > .nav-link {\n  color: #fff;\n  background-color: #fc2a00;\n}\n\n.nav-fill .nav-item {\n  flex: 1 1 auto;\n  text-align: center;\n}\n\n.nav-justified .nav-item {\n  flex-basis: 0;\n  flex-grow: 1;\n  text-align: center;\n}\n\n.tab-content > .tab-pane {\n  display: none;\n}\n\n.tab-content > .active {\n  display: block;\n}\n\n.navbar {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0.5rem 1rem;\n}\n.navbar > .container,\n.navbar > .container-fluid {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.navbar-brand {\n  display: inline-block;\n  padding-top: 0.3125rem;\n  padding-bottom: 0.3125rem;\n  margin-right: 1rem;\n  font-size: 1.25rem;\n  line-height: inherit;\n  white-space: nowrap;\n}\n.navbar-brand:hover,\n.navbar-brand:focus {\n  text-decoration: none;\n}\n\n.navbar-nav {\n  display: flex;\n  flex-direction: column;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n.navbar-nav .nav-link {\n  padding-right: 0;\n  padding-left: 0;\n}\n.navbar-nav .dropdown-menu {\n  position: static;\n  float: none;\n}\n\n.navbar-text {\n  display: inline-block;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  align-items: center;\n}\n\n.navbar-toggler {\n  padding: 0.25rem 0.75rem;\n  font-size: 1.25rem;\n  line-height: 1;\n  background-color: transparent;\n  border: 1px solid transparent;\n  border-radius: 0.25rem;\n}\n.navbar-toggler:hover,\n.navbar-toggler:focus {\n  text-decoration: none;\n}\n.navbar-toggler:not(:disabled):not(.disabled) {\n  cursor: pointer;\n}\n\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  content: \"\";\n  background: no-repeat center center;\n  background-size: 100% 100%;\n}\n\n@media (max-width: 575.98px) {\n  .navbar-expand-sm > .container,\n  .navbar-expand-sm > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n@media (min-width: 576px) {\n  .navbar-expand-sm {\n    flex-flow: row nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-sm .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-sm .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-sm .navbar-nav .nav-link {\n    padding-right: 0.5rem;\n    padding-left: 0.5rem;\n  }\n  .navbar-expand-sm > .container,\n  .navbar-expand-sm > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-sm .navbar-collapse {\n    display: flex !important;\n    flex-basis: auto;\n  }\n  .navbar-expand-sm .navbar-toggler {\n    display: none;\n  }\n}\n\n@media (max-width: 767.98px) {\n  .navbar-expand-md > .container,\n  .navbar-expand-md > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n@media (min-width: 768px) {\n  .navbar-expand-md {\n    flex-flow: row nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-md .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-md .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-md .navbar-nav .nav-link {\n    padding-right: 0.5rem;\n    padding-left: 0.5rem;\n  }\n  .navbar-expand-md > .container,\n  .navbar-expand-md > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-md .navbar-collapse {\n    display: flex !important;\n    flex-basis: auto;\n  }\n  .navbar-expand-md .navbar-toggler {\n    display: none;\n  }\n}\n\n@media (max-width: 991.98px) {\n  .navbar-expand-lg > .container,\n  .navbar-expand-lg > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n@media (min-width: 992px) {\n  .navbar-expand-lg {\n    flex-flow: row nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-lg .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-lg .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-lg .navbar-nav .nav-link {\n    padding-right: 0.5rem;\n    padding-left: 0.5rem;\n  }\n  .navbar-expand-lg > .container,\n  .navbar-expand-lg > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-lg .navbar-collapse {\n    display: flex !important;\n    flex-basis: auto;\n  }\n  .navbar-expand-lg .navbar-toggler {\n    display: none;\n  }\n}\n\n@media (max-width: 1199.98px) {\n  .navbar-expand-xl > .container,\n  .navbar-expand-xl > .container-fluid {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n@media (min-width: 1200px) {\n  .navbar-expand-xl {\n    flex-flow: row nowrap;\n    justify-content: flex-start;\n  }\n  .navbar-expand-xl .navbar-nav {\n    flex-direction: row;\n  }\n  .navbar-expand-xl .navbar-nav .dropdown-menu {\n    position: absolute;\n  }\n  .navbar-expand-xl .navbar-nav .nav-link {\n    padding-right: 0.5rem;\n    padding-left: 0.5rem;\n  }\n  .navbar-expand-xl > .container,\n  .navbar-expand-xl > .container-fluid {\n    flex-wrap: nowrap;\n  }\n  .navbar-expand-xl .navbar-collapse {\n    display: flex !important;\n    flex-basis: auto;\n  }\n  .navbar-expand-xl .navbar-toggler {\n    display: none;\n  }\n}\n\n.navbar-expand {\n  flex-flow: row nowrap;\n  justify-content: flex-start;\n}\n.navbar-expand > .container,\n.navbar-expand > .container-fluid {\n  padding-right: 0;\n  padding-left: 0;\n}\n.navbar-expand .navbar-nav {\n  flex-direction: row;\n}\n.navbar-expand .navbar-nav .dropdown-menu {\n  position: absolute;\n}\n.navbar-expand .navbar-nav .nav-link {\n  padding-right: 0.5rem;\n  padding-left: 0.5rem;\n}\n.navbar-expand > .container,\n.navbar-expand > .container-fluid {\n  flex-wrap: nowrap;\n}\n.navbar-expand .navbar-collapse {\n  display: flex !important;\n  flex-basis: auto;\n}\n.navbar-expand .navbar-toggler {\n  display: none;\n}\n\n.navbar-light .navbar-brand {\n  color: rgba(0, 0, 0, 0.9);\n}\n.navbar-light .navbar-brand:hover,\n.navbar-light .navbar-brand:focus {\n  color: rgba(0, 0, 0, 0.9);\n}\n\n.navbar-light .navbar-nav .nav-link {\n  color: rgba(0, 0, 0, 0.5);\n}\n.navbar-light .navbar-nav .nav-link:hover,\n.navbar-light .navbar-nav .nav-link:focus {\n  color: rgba(0, 0, 0, 0.7);\n}\n.navbar-light .navbar-nav .nav-link.disabled {\n  color: rgba(0, 0, 0, 0.3);\n}\n\n.navbar-light .navbar-nav .show > .nav-link,\n.navbar-light .navbar-nav .active > .nav-link,\n.navbar-light .navbar-nav .nav-link.show,\n.navbar-light .navbar-nav .nav-link.active {\n  color: rgba(0, 0, 0, 0.9);\n}\n\n.navbar-light .navbar-toggler {\n  color: rgba(0, 0, 0, 0.5);\n  border-color: rgba(0, 0, 0, 0.1);\n}\n\n.navbar-light .navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\");\n}\n\n.navbar-light .navbar-text {\n  color: rgba(0, 0, 0, 0.5);\n}\n.navbar-light .navbar-text a {\n  color: rgba(0, 0, 0, 0.9);\n}\n.navbar-light .navbar-text a:hover,\n.navbar-light .navbar-text a:focus {\n  color: rgba(0, 0, 0, 0.9);\n}\n\n.navbar-dark .navbar-brand {\n  color: #fff;\n}\n.navbar-dark .navbar-brand:hover,\n.navbar-dark .navbar-brand:focus {\n  color: #fff;\n}\n\n.navbar-dark .navbar-nav .nav-link {\n  color: rgba(255, 255, 255, 0.5);\n}\n.navbar-dark .navbar-nav .nav-link:hover,\n.navbar-dark .navbar-nav .nav-link:focus {\n  color: rgba(255, 255, 255, 0.75);\n}\n.navbar-dark .navbar-nav .nav-link.disabled {\n  color: rgba(255, 255, 255, 0.25);\n}\n\n.navbar-dark .navbar-nav .show > .nav-link,\n.navbar-dark .navbar-nav .active > .nav-link,\n.navbar-dark .navbar-nav .nav-link.show,\n.navbar-dark .navbar-nav .nav-link.active {\n  color: #fff;\n}\n\n.navbar-dark .navbar-toggler {\n  color: rgba(255, 255, 255, 0.5);\n  border-color: rgba(255, 255, 255, 0.1);\n}\n\n.navbar-dark .navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\");\n}\n\n.navbar-dark .navbar-text {\n  color: rgba(255, 255, 255, 0.5);\n}\n.navbar-dark .navbar-text a {\n  color: #fff;\n}\n.navbar-dark .navbar-text a:hover,\n.navbar-dark .navbar-text a:focus {\n  color: #fff;\n}\n\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0;\n  word-wrap: break-word;\n  background-color: #fff;\n  background-clip: border-box;\n  border: 1px solid rgba(0, 0, 0, 0.125);\n  border-radius: 0.25rem;\n}\n.card > hr {\n  margin-right: 0;\n  margin-left: 0;\n}\n.card > .list-group:first-child .list-group-item:first-child {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n.card > .list-group:last-child .list-group-item:last-child {\n  border-bottom-right-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n\n.card-body {\n  flex: 1 1 auto;\n  padding: 1.25rem;\n}\n\n.card-title {\n  margin-bottom: 0.75rem;\n}\n\n.card-subtitle {\n  margin-top: -0.375rem;\n  margin-bottom: 0;\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link:hover {\n  text-decoration: none;\n}\n\n.card-link + .card-link {\n  margin-left: 1.25rem;\n}\n\n.card-header {\n  padding: 0.75rem 1.25rem;\n  margin-bottom: 0;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0.03);\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n}\n.card-header:first-child {\n  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;\n}\n.card-header + .list-group .list-group-item:first-child {\n  border-top: 0;\n}\n\n.card-footer {\n  padding: 0.75rem 1.25rem;\n  background-color: rgba(0, 0, 0, 0.03);\n  border-top: 1px solid rgba(0, 0, 0, 0.125);\n}\n.card-footer:last-child {\n  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);\n}\n\n.card-header-tabs {\n  margin-right: -0.625rem;\n  margin-bottom: -0.75rem;\n  margin-left: -0.625rem;\n  border-bottom: 0;\n}\n\n.card-header-pills {\n  margin-right: -0.625rem;\n  margin-left: -0.625rem;\n}\n\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: 1.25rem;\n}\n\n.card-img {\n  width: 100%;\n  border-radius: calc(0.25rem - 1px);\n}\n\n.card-img-top {\n  width: 100%;\n  border-top-left-radius: calc(0.25rem - 1px);\n  border-top-right-radius: calc(0.25rem - 1px);\n}\n\n.card-img-bottom {\n  width: 100%;\n  border-bottom-right-radius: calc(0.25rem - 1px);\n  border-bottom-left-radius: calc(0.25rem - 1px);\n}\n\n.card-deck {\n  display: flex;\n  flex-direction: column;\n}\n.card-deck .card {\n  margin-bottom: 15px;\n}\n@media (min-width: 576px) {\n  .card-deck {\n    flex-flow: row wrap;\n    margin-right: -15px;\n    margin-left: -15px;\n  }\n  .card-deck .card {\n    display: flex;\n    flex: 1 0 0%;\n    flex-direction: column;\n    margin-right: 15px;\n    margin-bottom: 0;\n    margin-left: 15px;\n  }\n}\n\n.card-group {\n  display: flex;\n  flex-direction: column;\n}\n.card-group > .card {\n  margin-bottom: 15px;\n}\n@media (min-width: 576px) {\n  .card-group {\n    flex-flow: row wrap;\n  }\n  .card-group > .card {\n    flex: 1 0 0%;\n    margin-bottom: 0;\n  }\n  .card-group > .card + .card {\n    margin-left: 0;\n    border-left: 0;\n  }\n  .card-group > .card:first-child {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n  .card-group > .card:first-child .card-img-top,\n  .card-group > .card:first-child .card-header {\n    border-top-right-radius: 0;\n  }\n  .card-group > .card:first-child .card-img-bottom,\n  .card-group > .card:first-child .card-footer {\n    border-bottom-right-radius: 0;\n  }\n  .card-group > .card:last-child {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n  .card-group > .card:last-child .card-img-top,\n  .card-group > .card:last-child .card-header {\n    border-top-left-radius: 0;\n  }\n  .card-group > .card:last-child .card-img-bottom,\n  .card-group > .card:last-child .card-footer {\n    border-bottom-left-radius: 0;\n  }\n  .card-group > .card:only-child {\n    border-radius: 0.25rem;\n  }\n  .card-group > .card:only-child .card-img-top,\n  .card-group > .card:only-child .card-header {\n    border-top-left-radius: 0.25rem;\n    border-top-right-radius: 0.25rem;\n  }\n  .card-group > .card:only-child .card-img-bottom,\n  .card-group > .card:only-child .card-footer {\n    border-bottom-right-radius: 0.25rem;\n    border-bottom-left-radius: 0.25rem;\n  }\n  .card-group > .card:not(:first-child):not(:last-child):not(:only-child) {\n    border-radius: 0;\n  }\n  .card-group > .card:not(:first-child):not(:last-child):not(:only-child) .card-img-top,\n  .card-group > .card:not(:first-child):not(:last-child):not(:only-child) .card-img-bottom,\n  .card-group > .card:not(:first-child):not(:last-child):not(:only-child) .card-header,\n  .card-group > .card:not(:first-child):not(:last-child):not(:only-child) .card-footer {\n    border-radius: 0;\n  }\n}\n\n.card-columns .card {\n  margin-bottom: 0.75rem;\n}\n\n@media (min-width: 576px) {\n  .card-columns {\n    column-count: 3;\n    column-gap: 1.25rem;\n    orphans: 1;\n    widows: 1;\n  }\n  .card-columns .card {\n    display: inline-block;\n    width: 100%;\n  }\n}\n\n.accordion .card {\n  overflow: hidden;\n}\n.accordion .card:not(:first-of-type) .card-header:first-child {\n  border-radius: 0;\n}\n.accordion .card:not(:first-of-type):not(:last-of-type) {\n  border-bottom: 0;\n  border-radius: 0;\n}\n.accordion .card:first-of-type {\n  border-bottom: 0;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.accordion .card:last-of-type {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.accordion .card .card-header {\n  margin-bottom: -1px;\n}\n\n.breadcrumb {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 0.75rem 1rem;\n  margin-bottom: 1rem;\n  list-style: none;\n  background-color: #e9ecef;\n  border-radius: 0.25rem;\n}\n\n.breadcrumb-item + .breadcrumb-item {\n  padding-left: 0.5rem;\n}\n.breadcrumb-item + .breadcrumb-item::before {\n  display: inline-block;\n  padding-right: 0.5rem;\n  color: #6c757d;\n  content: \"/\";\n}\n\n.breadcrumb-item + .breadcrumb-item:hover::before {\n  text-decoration: underline;\n}\n\n.breadcrumb-item + .breadcrumb-item:hover::before {\n  text-decoration: none;\n}\n\n.breadcrumb-item.active {\n  color: #6c757d;\n}\n\n.pagination {\n  display: flex;\n  padding-left: 0;\n  list-style: none;\n  border-radius: 0.25rem;\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  padding: 0.5rem 0.75rem;\n  margin-left: -1px;\n  line-height: 1.25;\n  color: #fc2a00;\n  background-color: #fff;\n  border: 1px solid #dee2e6;\n}\n.page-link:hover {\n  z-index: 2;\n  color: #b01d00;\n  text-decoration: none;\n  background-color: #e9ecef;\n  border-color: #dee2e6;\n}\n.page-link:focus {\n  z-index: 2;\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(252, 42, 0, 0.25);\n}\n.page-link:not(:disabled):not(.disabled) {\n  cursor: pointer;\n}\n\n.page-item:first-child .page-link {\n  margin-left: 0;\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n\n.page-item:last-child .page-link {\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n}\n\n.page-item.active .page-link {\n  z-index: 1;\n  color: #fff;\n  background-color: #fc2a00;\n  border-color: #fc2a00;\n}\n\n.page-item.disabled .page-link {\n  color: #6c757d;\n  pointer-events: none;\n  cursor: auto;\n  background-color: #fff;\n  border-color: #dee2e6;\n}\n\n.pagination-lg .page-link {\n  padding: 0.75rem 1.5rem;\n  font-size: 1.25rem;\n  line-height: 1.5;\n}\n\n.pagination-lg .page-item:first-child .page-link {\n  border-top-left-radius: 0.3rem;\n  border-bottom-left-radius: 0.3rem;\n}\n\n.pagination-lg .page-item:last-child .page-link {\n  border-top-right-radius: 0.3rem;\n  border-bottom-right-radius: 0.3rem;\n}\n\n.pagination-sm .page-link {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.pagination-sm .page-item:first-child .page-link {\n  border-top-left-radius: 0.2rem;\n  border-bottom-left-radius: 0.2rem;\n}\n\n.pagination-sm .page-item:last-child .page-link {\n  border-top-right-radius: 0.2rem;\n  border-bottom-right-radius: 0.2rem;\n}\n\n.badge {\n  display: inline-block;\n  padding: 0.25em 0.4em;\n  font-size: 75%;\n  font-weight: 700;\n  line-height: 1;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  border-radius: 0.25rem;\n}\na.badge:hover,\na.badge:focus {\n  text-decoration: none;\n}\n.badge:empty {\n  display: none;\n}\n\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n\n.badge-pill {\n  padding-right: 0.6em;\n  padding-left: 0.6em;\n  border-radius: 10rem;\n}\n\n.badge-primary {\n  color: #fff;\n  background-color: #fc2a00;\n}\na.badge-primary:hover,\na.badge-primary:focus {\n  color: #fff;\n  background-color: #c92200;\n}\n\n.badge-secondary {\n  color: #fff;\n  background-color: #6c757d;\n}\na.badge-secondary:hover,\na.badge-secondary:focus {\n  color: #fff;\n  background-color: #545b62;\n}\n\n.badge-success {\n  color: #fff;\n  background-color: #28a745;\n}\na.badge-success:hover,\na.badge-success:focus {\n  color: #fff;\n  background-color: #1e7e34;\n}\n\n.badge-info {\n  color: #fff;\n  background-color: #17a2b8;\n}\na.badge-info:hover,\na.badge-info:focus {\n  color: #fff;\n  background-color: #117a8b;\n}\n\n.badge-warning {\n  color: #212529;\n  background-color: #fcaf17;\n}\na.badge-warning:hover,\na.badge-warning:focus {\n  color: #212529;\n  background-color: #dd9403;\n}\n\n.badge-danger {\n  color: #fff;\n  background-color: #dc3545;\n}\na.badge-danger:hover,\na.badge-danger:focus {\n  color: #fff;\n  background-color: #bd2130;\n}\n\n.badge-light {\n  color: #212529;\n  background-color: #e6e6e6;\n}\na.badge-light:hover,\na.badge-light:focus {\n  color: #212529;\n  background-color: #cdcdcd;\n}\n\n.badge-dark {\n  color: #fff;\n  background-color: #343a40;\n}\na.badge-dark:hover,\na.badge-dark:focus {\n  color: #fff;\n  background-color: #1d2124;\n}\n\n.jumbotron {\n  padding: 2rem 1rem;\n  margin-bottom: 2rem;\n  background-color: #e9ecef;\n  border-radius: 0.3rem;\n}\n@media (min-width: 576px) {\n  .jumbotron {\n    padding: 4rem 2rem;\n  }\n}\n\n.jumbotron-fluid {\n  padding-right: 0;\n  padding-left: 0;\n  border-radius: 0;\n}\n\n.alert {\n  position: relative;\n  padding: 0.75rem 1.25rem;\n  margin-bottom: 1rem;\n  border: 1px solid transparent;\n  border-radius: 0.25rem;\n}\n\n.alert-heading {\n  color: inherit;\n}\n\n.alert-link {\n  font-weight: 700;\n}\n\n.alert-dismissible {\n  padding-right: 4rem;\n}\n.alert-dismissible .close {\n  position: absolute;\n  top: 0;\n  right: 0;\n  padding: 0.75rem 1.25rem;\n  color: inherit;\n}\n\n.alert-primary {\n  color: #831600;\n  background-color: #fed4cc;\n  border-color: #fec3b8;\n}\n.alert-primary hr {\n  border-top-color: #feae9f;\n}\n.alert-primary .alert-link {\n  color: #500d00;\n}\n\n.alert-secondary {\n  color: #383d41;\n  background-color: #e2e3e5;\n  border-color: #d6d8db;\n}\n.alert-secondary hr {\n  border-top-color: #c8cbcf;\n}\n.alert-secondary .alert-link {\n  color: #202326;\n}\n\n.alert-success {\n  color: #155724;\n  background-color: #d4edda;\n  border-color: #c3e6cb;\n}\n.alert-success hr {\n  border-top-color: #b1dfbb;\n}\n.alert-success .alert-link {\n  color: #0b2e13;\n}\n\n.alert-info {\n  color: #0c5460;\n  background-color: #d1ecf1;\n  border-color: #bee5eb;\n}\n.alert-info hr {\n  border-top-color: #abdde5;\n}\n.alert-info .alert-link {\n  color: #062c33;\n}\n\n.alert-warning {\n  color: #835b0c;\n  background-color: #feefd1;\n  border-color: #fee9be;\n}\n.alert-warning hr {\n  border-top-color: #fee1a5;\n}\n.alert-warning .alert-link {\n  color: #543b08;\n}\n\n.alert-danger {\n  color: #721c24;\n  background-color: #f8d7da;\n  border-color: #f5c6cb;\n}\n.alert-danger hr {\n  border-top-color: #f1b0b7;\n}\n.alert-danger .alert-link {\n  color: #491217;\n}\n\n.alert-light {\n  color: #787878;\n  background-color: #fafafa;\n  border-color: #f8f8f8;\n}\n.alert-light hr {\n  border-top-color: #ebebeb;\n}\n.alert-light .alert-link {\n  color: #5f5f5f;\n}\n\n.alert-dark {\n  color: #1b1e21;\n  background-color: #d6d8d9;\n  border-color: #c6c8ca;\n}\n.alert-dark hr {\n  border-top-color: #b9bbbe;\n}\n.alert-dark .alert-link {\n  color: #040505;\n}\n\n@keyframes progress-bar-stripes {\n  from {\n    background-position: 1rem 0;\n  }\n  to {\n    background-position: 0 0;\n  }\n}\n\n.progress {\n  display: flex;\n  height: 1rem;\n  overflow: hidden;\n  font-size: 0.75rem;\n  background-color: #e9ecef;\n  border-radius: 0.25rem;\n}\n\n.progress-bar {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  color: #fff;\n  text-align: center;\n  white-space: nowrap;\n  background-color: #fc2a00;\n  transition: width 0.6s ease;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .progress-bar {\n    transition: none;\n  }\n}\n\n.progress-bar-striped {\n  background-image: linear-gradient(\n    45deg,\n    rgba(255, 255, 255, 0.15) 25%,\n    transparent 25%,\n    transparent 50%,\n    rgba(255, 255, 255, 0.15) 50%,\n    rgba(255, 255, 255, 0.15) 75%,\n    transparent 75%,\n    transparent\n  );\n  background-size: 1rem 1rem;\n}\n\n.progress-bar-animated {\n  animation: progress-bar-stripes 1s linear infinite;\n}\n\n.media {\n  display: flex;\n  align-items: flex-start;\n}\n\n.media-body {\n  flex: 1;\n}\n\n.list-group {\n  display: flex;\n  flex-direction: column;\n  padding-left: 0;\n  margin-bottom: 0;\n}\n\n.list-group-item-action {\n  width: 100%;\n  color: #495057;\n  text-align: inherit;\n}\n.list-group-item-action:hover,\n.list-group-item-action:focus {\n  color: #495057;\n  text-decoration: none;\n  background-color: #f8f9fa;\n}\n.list-group-item-action:active {\n  color: #212529;\n  background-color: #e9ecef;\n}\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: 0.75rem 1.25rem;\n  margin-bottom: -1px;\n  background-color: #fff;\n  border: 1px solid rgba(0, 0, 0, 0.125);\n}\n.list-group-item:first-child {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n.list-group-item:last-child {\n  margin-bottom: 0;\n  border-bottom-right-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n.list-group-item:hover,\n.list-group-item:focus {\n  z-index: 1;\n  text-decoration: none;\n}\n.list-group-item.disabled,\n.list-group-item:disabled {\n  color: #6c757d;\n  pointer-events: none;\n  background-color: #fff;\n}\n.list-group-item.active {\n  z-index: 2;\n  color: #fff;\n  background-color: #fc2a00;\n  border-color: #fc2a00;\n}\n\n.list-group-flush .list-group-item {\n  border-right: 0;\n  border-left: 0;\n  border-radius: 0;\n}\n.list-group-flush .list-group-item:last-child {\n  margin-bottom: -1px;\n}\n\n.list-group-flush:first-child .list-group-item:first-child {\n  border-top: 0;\n}\n\n.list-group-flush:last-child .list-group-item:last-child {\n  margin-bottom: 0;\n  border-bottom: 0;\n}\n\n.list-group-item-primary {\n  color: #831600;\n  background-color: #fec3b8;\n}\n.list-group-item-primary.list-group-item-action:hover,\n.list-group-item-primary.list-group-item-action:focus {\n  color: #831600;\n  background-color: #feae9f;\n}\n.list-group-item-primary.list-group-item-action.active {\n  color: #fff;\n  background-color: #831600;\n  border-color: #831600;\n}\n\n.list-group-item-secondary {\n  color: #383d41;\n  background-color: #d6d8db;\n}\n.list-group-item-secondary.list-group-item-action:hover,\n.list-group-item-secondary.list-group-item-action:focus {\n  color: #383d41;\n  background-color: #c8cbcf;\n}\n.list-group-item-secondary.list-group-item-action.active {\n  color: #fff;\n  background-color: #383d41;\n  border-color: #383d41;\n}\n\n.list-group-item-success {\n  color: #155724;\n  background-color: #c3e6cb;\n}\n.list-group-item-success.list-group-item-action:hover,\n.list-group-item-success.list-group-item-action:focus {\n  color: #155724;\n  background-color: #b1dfbb;\n}\n.list-group-item-success.list-group-item-action.active {\n  color: #fff;\n  background-color: #155724;\n  border-color: #155724;\n}\n\n.list-group-item-info {\n  color: #0c5460;\n  background-color: #bee5eb;\n}\n.list-group-item-info.list-group-item-action:hover,\n.list-group-item-info.list-group-item-action:focus {\n  color: #0c5460;\n  background-color: #abdde5;\n}\n.list-group-item-info.list-group-item-action.active {\n  color: #fff;\n  background-color: #0c5460;\n  border-color: #0c5460;\n}\n\n.list-group-item-warning {\n  color: #835b0c;\n  background-color: #fee9be;\n}\n.list-group-item-warning.list-group-item-action:hover,\n.list-group-item-warning.list-group-item-action:focus {\n  color: #835b0c;\n  background-color: #fee1a5;\n}\n.list-group-item-warning.list-group-item-action.active {\n  color: #fff;\n  background-color: #835b0c;\n  border-color: #835b0c;\n}\n\n.list-group-item-danger {\n  color: #721c24;\n  background-color: #f5c6cb;\n}\n.list-group-item-danger.list-group-item-action:hover,\n.list-group-item-danger.list-group-item-action:focus {\n  color: #721c24;\n  background-color: #f1b0b7;\n}\n.list-group-item-danger.list-group-item-action.active {\n  color: #fff;\n  background-color: #721c24;\n  border-color: #721c24;\n}\n\n.list-group-item-light {\n  color: #787878;\n  background-color: #f8f8f8;\n}\n.list-group-item-light.list-group-item-action:hover,\n.list-group-item-light.list-group-item-action:focus {\n  color: #787878;\n  background-color: #ebebeb;\n}\n.list-group-item-light.list-group-item-action.active {\n  color: #fff;\n  background-color: #787878;\n  border-color: #787878;\n}\n\n.list-group-item-dark {\n  color: #1b1e21;\n  background-color: #c6c8ca;\n}\n.list-group-item-dark.list-group-item-action:hover,\n.list-group-item-dark.list-group-item-action:focus {\n  color: #1b1e21;\n  background-color: #b9bbbe;\n}\n.list-group-item-dark.list-group-item-action.active {\n  color: #fff;\n  background-color: #1b1e21;\n  border-color: #1b1e21;\n}\n\n.close {\n  float: right;\n  font-size: 1.5rem;\n  font-weight: 700;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: 0.5;\n}\n.close:hover {\n  color: #000;\n  text-decoration: none;\n}\n.close:not(:disabled):not(.disabled) {\n  cursor: pointer;\n}\n.close:not(:disabled):not(.disabled):hover,\n.close:not(:disabled):not(.disabled):focus {\n  opacity: 0.75;\n}\n\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n  appearance: none;\n}\n\na.close.disabled {\n  pointer-events: none;\n}\n\n.toast {\n  max-width: 350px;\n  overflow: hidden;\n  font-size: 0.875rem;\n  background-color: rgba(255, 255, 255, 0.85);\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  border-radius: 0.25rem;\n  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n  opacity: 0;\n}\n.toast:not(:last-child) {\n  margin-bottom: 0.75rem;\n}\n.toast.showing {\n  opacity: 1;\n}\n.toast.show {\n  display: block;\n  opacity: 1;\n}\n.toast.hide {\n  display: none;\n}\n\n.toast-header {\n  display: flex;\n  align-items: center;\n  padding: 0.25rem 0.75rem;\n  color: #6c757d;\n  background-color: rgba(255, 255, 255, 0.85);\n  background-clip: padding-box;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.toast-body {\n  padding: 0.75rem;\n}\n\n.modal-open {\n  overflow: hidden;\n}\n.modal-open .modal {\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 1050;\n  display: none;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  outline: 0;\n}\n\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: 0.5rem;\n  pointer-events: none;\n}\n.modal.fade .modal-dialog {\n  transition: transform 0.3s ease-out;\n  transform: translate(0, -50px);\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .modal.fade .modal-dialog {\n    transition: none;\n  }\n}\n.modal.show .modal-dialog {\n  transform: none;\n}\n\n.modal-dialog-centered {\n  display: flex;\n  align-items: center;\n  min-height: calc(100% - (0.5rem * 2));\n}\n.modal-dialog-centered::before {\n  display: block;\n  height: calc(100vh - (0.5rem * 2));\n  content: \"\";\n}\n\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  pointer-events: auto;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 0.3rem;\n  outline: 0;\n}\n\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 1040;\n  width: 100vw;\n  height: 100vh;\n  background-color: #000;\n}\n.modal-backdrop.fade {\n  opacity: 0;\n}\n.modal-backdrop.show {\n  opacity: 0.5;\n}\n\n.modal-header {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  padding: 1rem 1rem;\n  border-bottom: 1px solid #e9ecef;\n  border-top-left-radius: 0.3rem;\n  border-top-right-radius: 0.3rem;\n}\n.modal-header .close {\n  padding: 1rem 1rem;\n  margin: -1rem -1rem -1rem auto;\n}\n\n.modal-title {\n  margin-bottom: 0;\n  line-height: 1.5;\n}\n\n.modal-body {\n  position: relative;\n  flex: 1 1 auto;\n  padding: 1rem;\n}\n\n.modal-footer {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  padding: 1rem;\n  border-top: 1px solid #e9ecef;\n  border-bottom-right-radius: 0.3rem;\n  border-bottom-left-radius: 0.3rem;\n}\n.modal-footer > :not(:first-child) {\n  margin-left: 0.25rem;\n}\n.modal-footer > :not(:last-child) {\n  margin-right: 0.25rem;\n}\n\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n@media (min-width: 576px) {\n  .modal-dialog {\n    max-width: 500px;\n    margin: 1.75rem auto;\n  }\n  .modal-dialog-centered {\n    min-height: calc(100% - (1.75rem * 2));\n  }\n  .modal-dialog-centered::before {\n    height: calc(100vh - (1.75rem * 2));\n  }\n  .modal-sm {\n    max-width: 300px;\n  }\n}\n\n@media (min-width: 992px) {\n  .modal-lg,\n  .modal-xl {\n    max-width: 800px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .modal-xl {\n    max-width: 1140px;\n  }\n}\n\n.tooltip {\n  position: absolute;\n  z-index: 1070;\n  display: block;\n  margin: 0;\n  font-family: \"ff-super-grotesk-web-pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\n    \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\",\n    \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-style: normal;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  font-size: 0.875rem;\n  word-wrap: break-word;\n  opacity: 0;\n}\n.tooltip.show {\n  opacity: 0.9;\n}\n.tooltip .arrow {\n  position: absolute;\n  display: block;\n  width: 0.8rem;\n  height: 0.4rem;\n}\n.tooltip .arrow::before {\n  position: absolute;\n  content: \"\";\n  border-color: transparent;\n  border-style: solid;\n}\n\n.bs-tooltip-top,\n.bs-tooltip-auto[x-placement^=\"top\"] {\n  padding: 0.4rem 0;\n}\n.bs-tooltip-top .arrow,\n.bs-tooltip-auto[x-placement^=\"top\"] .arrow {\n  bottom: 0;\n}\n.bs-tooltip-top .arrow::before,\n.bs-tooltip-auto[x-placement^=\"top\"] .arrow::before {\n  top: 0;\n  border-width: 0.4rem 0.4rem 0;\n  border-top-color: #000;\n}\n\n.bs-tooltip-right,\n.bs-tooltip-auto[x-placement^=\"right\"] {\n  padding: 0 0.4rem;\n}\n.bs-tooltip-right .arrow,\n.bs-tooltip-auto[x-placement^=\"right\"] .arrow {\n  left: 0;\n  width: 0.4rem;\n  height: 0.8rem;\n}\n.bs-tooltip-right .arrow::before,\n.bs-tooltip-auto[x-placement^=\"right\"] .arrow::before {\n  right: 0;\n  border-width: 0.4rem 0.4rem 0.4rem 0;\n  border-right-color: #000;\n}\n\n.bs-tooltip-bottom,\n.bs-tooltip-auto[x-placement^=\"bottom\"] {\n  padding: 0.4rem 0;\n}\n.bs-tooltip-bottom .arrow,\n.bs-tooltip-auto[x-placement^=\"bottom\"] .arrow {\n  top: 0;\n}\n.bs-tooltip-bottom .arrow::before,\n.bs-tooltip-auto[x-placement^=\"bottom\"] .arrow::before {\n  bottom: 0;\n  border-width: 0 0.4rem 0.4rem;\n  border-bottom-color: #000;\n}\n\n.bs-tooltip-left,\n.bs-tooltip-auto[x-placement^=\"left\"] {\n  padding: 0 0.4rem;\n}\n.bs-tooltip-left .arrow,\n.bs-tooltip-auto[x-placement^=\"left\"] .arrow {\n  right: 0;\n  width: 0.4rem;\n  height: 0.8rem;\n}\n.bs-tooltip-left .arrow::before,\n.bs-tooltip-auto[x-placement^=\"left\"] .arrow::before {\n  left: 0;\n  border-width: 0.4rem 0 0.4rem 0.4rem;\n  border-left-color: #000;\n}\n\n.tooltip-inner {\n  max-width: 200px;\n  padding: 0.25rem 0.5rem;\n  color: #fff;\n  text-align: center;\n  background-color: #000;\n  border-radius: 0.25rem;\n}\n\n.popover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 1060;\n  display: block;\n  max-width: 276px;\n  font-family: \"ff-super-grotesk-web-pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\n    \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\",\n    \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-style: normal;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  font-size: 0.875rem;\n  word-wrap: break-word;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  border-radius: 0.3rem;\n}\n.popover .arrow {\n  position: absolute;\n  display: block;\n  width: 1rem;\n  height: 0.5rem;\n  margin: 0 0.3rem;\n}\n.popover .arrow::before,\n.popover .arrow::after {\n  position: absolute;\n  display: block;\n  content: \"\";\n  border-color: transparent;\n  border-style: solid;\n}\n\n.bs-popover-top,\n.bs-popover-auto[x-placement^=\"top\"] {\n  margin-bottom: 0.5rem;\n}\n.bs-popover-top .arrow,\n.bs-popover-auto[x-placement^=\"top\"] .arrow {\n  bottom: calc((0.5rem + 1px) * -1);\n}\n.bs-popover-top .arrow::before,\n.bs-popover-auto[x-placement^=\"top\"] .arrow::before,\n.bs-popover-top .arrow::after,\n.bs-popover-auto[x-placement^=\"top\"] .arrow::after {\n  border-width: 0.5rem 0.5rem 0;\n}\n.bs-popover-top .arrow::before,\n.bs-popover-auto[x-placement^=\"top\"] .arrow::before {\n  bottom: 0;\n  border-top-color: rgba(0, 0, 0, 0.25);\n}\n\n.bs-popover-top .arrow::after,\n.bs-popover-auto[x-placement^=\"top\"] .arrow::after {\n  bottom: 1px;\n  border-top-color: #fff;\n}\n\n.bs-popover-right,\n.bs-popover-auto[x-placement^=\"right\"] {\n  margin-left: 0.5rem;\n}\n.bs-popover-right .arrow,\n.bs-popover-auto[x-placement^=\"right\"] .arrow {\n  left: calc((0.5rem + 1px) * -1);\n  width: 0.5rem;\n  height: 1rem;\n  margin: 0.3rem 0;\n}\n.bs-popover-right .arrow::before,\n.bs-popover-auto[x-placement^=\"right\"] .arrow::before,\n.bs-popover-right .arrow::after,\n.bs-popover-auto[x-placement^=\"right\"] .arrow::after {\n  border-width: 0.5rem 0.5rem 0.5rem 0;\n}\n.bs-popover-right .arrow::before,\n.bs-popover-auto[x-placement^=\"right\"] .arrow::before {\n  left: 0;\n  border-right-color: rgba(0, 0, 0, 0.25);\n}\n\n.bs-popover-right .arrow::after,\n.bs-popover-auto[x-placement^=\"right\"] .arrow::after {\n  left: 1px;\n  border-right-color: #fff;\n}\n\n.bs-popover-bottom,\n.bs-popover-auto[x-placement^=\"bottom\"] {\n  margin-top: 0.5rem;\n}\n.bs-popover-bottom .arrow,\n.bs-popover-auto[x-placement^=\"bottom\"] .arrow {\n  top: calc((0.5rem + 1px) * -1);\n}\n.bs-popover-bottom .arrow::before,\n.bs-popover-auto[x-placement^=\"bottom\"] .arrow::before,\n.bs-popover-bottom .arrow::after,\n.bs-popover-auto[x-placement^=\"bottom\"] .arrow::after {\n  border-width: 0 0.5rem 0.5rem 0.5rem;\n}\n.bs-popover-bottom .arrow::before,\n.bs-popover-auto[x-placement^=\"bottom\"] .arrow::before {\n  top: 0;\n  border-bottom-color: rgba(0, 0, 0, 0.25);\n}\n\n.bs-popover-bottom .arrow::after,\n.bs-popover-auto[x-placement^=\"bottom\"] .arrow::after {\n  top: 1px;\n  border-bottom-color: #fff;\n}\n.bs-popover-bottom .popover-header::before,\n.bs-popover-auto[x-placement^=\"bottom\"] .popover-header::before {\n  position: absolute;\n  top: 0;\n  left: 50%;\n  display: block;\n  width: 1rem;\n  margin-left: -0.5rem;\n  content: \"\";\n  border-bottom: 1px solid #f7f7f7;\n}\n\n.bs-popover-left,\n.bs-popover-auto[x-placement^=\"left\"] {\n  margin-right: 0.5rem;\n}\n.bs-popover-left .arrow,\n.bs-popover-auto[x-placement^=\"left\"] .arrow {\n  right: calc((0.5rem + 1px) * -1);\n  width: 0.5rem;\n  height: 1rem;\n  margin: 0.3rem 0;\n}\n.bs-popover-left .arrow::before,\n.bs-popover-auto[x-placement^=\"left\"] .arrow::before,\n.bs-popover-left .arrow::after,\n.bs-popover-auto[x-placement^=\"left\"] .arrow::after {\n  border-width: 0.5rem 0 0.5rem 0.5rem;\n}\n.bs-popover-left .arrow::before,\n.bs-popover-auto[x-placement^=\"left\"] .arrow::before {\n  right: 0;\n  border-left-color: rgba(0, 0, 0, 0.25);\n}\n\n.bs-popover-left .arrow::after,\n.bs-popover-auto[x-placement^=\"left\"] .arrow::after {\n  right: 1px;\n  border-left-color: #fff;\n}\n\n.popover-header {\n  padding: 0.5rem 0.75rem;\n  margin-bottom: 0;\n  font-size: 1rem;\n  color: inherit;\n  background-color: #f7f7f7;\n  border-bottom: 1px solid #ebebeb;\n  border-top-left-radius: calc(0.3rem - 1px);\n  border-top-right-radius: calc(0.3rem - 1px);\n}\n.popover-header:empty {\n  display: none;\n}\n\n.popover-body {\n  padding: 0.5rem 0.75rem;\n  color: #212529;\n}\n\n.carousel {\n  position: relative;\n}\n\n.carousel.pointer-event {\n  touch-action: pan-y;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n}\n.carousel-inner::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  float: left;\n  width: 100%;\n  margin-right: -100%;\n  backface-visibility: hidden;\n  transition: transform 0.6s ease-in-out;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .carousel-item {\n    transition: none;\n  }\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n.carousel-item-next:not(.carousel-item-left),\n.active.carousel-item-right {\n  transform: translateX(100%);\n}\n\n.carousel-item-prev:not(.carousel-item-right),\n.active.carousel-item-left {\n  transform: translateX(-100%);\n}\n\n.carousel-fade .carousel-item {\n  opacity: 0;\n  transition-property: opacity;\n  transform: none;\n}\n\n.carousel-fade .carousel-item.active,\n.carousel-fade .carousel-item-next.carousel-item-left,\n.carousel-fade .carousel-item-prev.carousel-item-right {\n  z-index: 1;\n  opacity: 1;\n}\n\n.carousel-fade .active.carousel-item-left,\n.carousel-fade .active.carousel-item-right {\n  z-index: 0;\n  opacity: 0;\n  transition: 0s 0.6s opacity;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .carousel-fade .active.carousel-item-left,\n  .carousel-fade .active.carousel-item-right {\n    transition: none;\n  }\n}\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  z-index: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 15%;\n  color: #fff;\n  text-align: center;\n  opacity: 0.5;\n  transition: opacity 0.15s ease;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .carousel-control-prev,\n  .carousel-control-next {\n    transition: none;\n  }\n}\n.carousel-control-prev:hover,\n.carousel-control-prev:focus,\n.carousel-control-next:hover,\n.carousel-control-next:focus {\n  color: #fff;\n  text-decoration: none;\n  outline: 0;\n  opacity: 0.9;\n}\n\n.carousel-control-prev {\n  left: 0;\n}\n\n.carousel-control-next {\n  right: 0;\n}\n\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  background: transparent no-repeat center center;\n  background-size: 100% 100%;\n}\n\n.carousel-control-prev-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e\");\n}\n\n.carousel-control-next-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e\");\n}\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 15;\n  display: flex;\n  justify-content: center;\n  padding-left: 0;\n  margin-right: 15%;\n  margin-left: 15%;\n  list-style: none;\n}\n.carousel-indicators li {\n  box-sizing: content-box;\n  flex: 0 1 auto;\n  width: 30px;\n  height: 3px;\n  margin-right: 3px;\n  margin-left: 3px;\n  text-indent: -999px;\n  cursor: pointer;\n  background-color: #fff;\n  background-clip: padding-box;\n  border-top: 10px solid transparent;\n  border-bottom: 10px solid transparent;\n  opacity: 0.5;\n  transition: opacity 0.6s ease;\n}\n@media screen and (prefers-reduced-motion: reduce) {\n  .carousel-indicators li {\n    transition: none;\n  }\n}\n.carousel-indicators .active {\n  opacity: 1;\n}\n\n.carousel-caption {\n  position: absolute;\n  right: 15%;\n  bottom: 20px;\n  left: 15%;\n  z-index: 10;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  color: #fff;\n  text-align: center;\n}\n\n@keyframes spinner-border {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.spinner-border {\n  display: inline-block;\n  width: 2rem;\n  height: 2rem;\n  vertical-align: text-bottom;\n  border: 0.25em solid currentColor;\n  border-right-color: transparent;\n  border-radius: 50%;\n  animation: spinner-border 0.75s linear infinite;\n}\n\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n  border-width: 0.2em;\n}\n\n@keyframes spinner-grow {\n  0% {\n    transform: scale(0);\n  }\n  50% {\n    opacity: 1;\n  }\n}\n\n.spinner-grow {\n  display: inline-block;\n  width: 2rem;\n  height: 2rem;\n  vertical-align: text-bottom;\n  background-color: currentColor;\n  border-radius: 50%;\n  opacity: 0;\n  animation: spinner-grow 0.75s linear infinite;\n}\n\n.spinner-grow-sm {\n  width: 1rem;\n  height: 1rem;\n}\n\n.align-baseline {\n  vertical-align: baseline !important;\n}\n\n.align-top {\n  vertical-align: top !important;\n}\n\n.align-middle {\n  vertical-align: middle !important;\n}\n\n.align-bottom {\n  vertical-align: bottom !important;\n}\n\n.align-text-bottom {\n  vertical-align: text-bottom !important;\n}\n\n.align-text-top {\n  vertical-align: text-top !important;\n}\n\n.bg-primary {\n  background-color: #fc2a00 !important;\n}\n\na.bg-primary:hover,\na.bg-primary:focus,\nbutton.bg-primary:hover,\nbutton.bg-primary:focus {\n  background-color: #c92200 !important;\n}\n\n.bg-secondary {\n  background-color: #6c757d !important;\n}\n\na.bg-secondary:hover,\na.bg-secondary:focus,\nbutton.bg-secondary:hover,\nbutton.bg-secondary:focus {\n  background-color: #545b62 !important;\n}\n\n.bg-success {\n  background-color: #28a745 !important;\n}\n\na.bg-success:hover,\na.bg-success:focus,\nbutton.bg-success:hover,\nbutton.bg-success:focus {\n  background-color: #1e7e34 !important;\n}\n\n.bg-info {\n  background-color: #17a2b8 !important;\n}\n\na.bg-info:hover,\na.bg-info:focus,\nbutton.bg-info:hover,\nbutton.bg-info:focus {\n  background-color: #117a8b !important;\n}\n\n.bg-warning {\n  background-color: #fcaf17 !important;\n}\n\na.bg-warning:hover,\na.bg-warning:focus,\nbutton.bg-warning:hover,\nbutton.bg-warning:focus {\n  background-color: #dd9403 !important;\n}\n\n.bg-danger {\n  background-color: #dc3545 !important;\n}\n\na.bg-danger:hover,\na.bg-danger:focus,\nbutton.bg-danger:hover,\nbutton.bg-danger:focus {\n  background-color: #bd2130 !important;\n}\n\n.bg-light {\n  background-color: #e6e6e6 !important;\n}\n\na.bg-light:hover,\na.bg-light:focus,\nbutton.bg-light:hover,\nbutton.bg-light:focus {\n  background-color: #cdcdcd !important;\n}\n\n.bg-dark {\n  background-color: #343a40 !important;\n}\n\na.bg-dark:hover,\na.bg-dark:focus,\nbutton.bg-dark:hover,\nbutton.bg-dark:focus {\n  background-color: #1d2124 !important;\n}\n\n.bg-white {\n  background-color: #fff !important;\n}\n\n.bg-transparent {\n  background-color: transparent !important;\n}\n\n.border {\n  border: 1px solid #dee2e6 !important;\n}\n\n.border-top {\n  border-top: 1px solid #dee2e6 !important;\n}\n\n.border-right {\n  border-right: 1px solid #dee2e6 !important;\n}\n\n.border-bottom {\n  border-bottom: 1px solid #dee2e6 !important;\n}\n\n.border-left {\n  border-left: 1px solid #dee2e6 !important;\n}\n\n.border-0 {\n  border: 0 !important;\n}\n\n.border-top-0 {\n  border-top: 0 !important;\n}\n\n.border-right-0 {\n  border-right: 0 !important;\n}\n\n.border-bottom-0 {\n  border-bottom: 0 !important;\n}\n\n.border-left-0 {\n  border-left: 0 !important;\n}\n\n.border-primary {\n  border-color: #fc2a00 !important;\n}\n\n.border-secondary {\n  border-color: #6c757d !important;\n}\n\n.border-success {\n  border-color: #28a745 !important;\n}\n\n.border-info {\n  border-color: #17a2b8 !important;\n}\n\n.border-warning {\n  border-color: #fcaf17 !important;\n}\n\n.border-danger {\n  border-color: #dc3545 !important;\n}\n\n.border-light {\n  border-color: #e6e6e6 !important;\n}\n\n.border-dark {\n  border-color: #343a40 !important;\n}\n\n.border-white {\n  border-color: #fff !important;\n}\n\n.rounded {\n  border-radius: 0.25rem !important;\n}\n\n.rounded-top {\n  border-top-left-radius: 0.25rem !important;\n  border-top-right-radius: 0.25rem !important;\n}\n\n.rounded-right {\n  border-top-right-radius: 0.25rem !important;\n  border-bottom-right-radius: 0.25rem !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: 0.25rem !important;\n  border-bottom-left-radius: 0.25rem !important;\n}\n\n.rounded-left {\n  border-top-left-radius: 0.25rem !important;\n  border-bottom-left-radius: 0.25rem !important;\n}\n\n.rounded-circle {\n  border-radius: 50% !important;\n}\n\n.rounded-pill {\n  border-radius: 50rem !important;\n}\n\n.rounded-0 {\n  border-radius: 0 !important;\n}\n\n.clearfix::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.d-none {\n  display: none !important;\n}\n\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n@media (min-width: 576px) {\n  .d-sm-none {\n    display: none !important;\n  }\n  .d-sm-inline {\n    display: inline !important;\n  }\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n  .d-sm-block {\n    display: block !important;\n  }\n  .d-sm-table {\n    display: table !important;\n  }\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n  .d-sm-flex {\n    display: flex !important;\n  }\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .d-md-none {\n    display: none !important;\n  }\n  .d-md-inline {\n    display: inline !important;\n  }\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n  .d-md-block {\n    display: block !important;\n  }\n  .d-md-table {\n    display: table !important;\n  }\n  .d-md-table-row {\n    display: table-row !important;\n  }\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n  .d-md-flex {\n    display: flex !important;\n  }\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .d-lg-none {\n    display: none !important;\n  }\n  .d-lg-inline {\n    display: inline !important;\n  }\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n  .d-lg-block {\n    display: block !important;\n  }\n  .d-lg-table {\n    display: table !important;\n  }\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n  .d-lg-flex {\n    display: flex !important;\n  }\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .d-xl-none {\n    display: none !important;\n  }\n  .d-xl-inline {\n    display: inline !important;\n  }\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xl-block {\n    display: block !important;\n  }\n  .d-xl-table {\n    display: table !important;\n  }\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xl-flex {\n    display: flex !important;\n  }\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n}\n\n@media print {\n  .d-print-none {\n    display: none !important;\n  }\n  .d-print-inline {\n    display: inline !important;\n  }\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n  .d-print-block {\n    display: block !important;\n  }\n  .d-print-table {\n    display: table !important;\n  }\n  .d-print-table-row {\n    display: table-row !important;\n  }\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n  .d-print-flex {\n    display: flex !important;\n  }\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n}\n\n.embed-responsive {\n  position: relative;\n  display: block;\n  width: 100%;\n  padding: 0;\n  overflow: hidden;\n}\n.embed-responsive::before {\n  display: block;\n  content: \"\";\n}\n.embed-responsive .embed-responsive-item,\n.embed-responsive iframe,\n.embed-responsive embed,\n.embed-responsive object,\n.embed-responsive video {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  border: 0;\n}\n\n.embed-responsive-21by9::before {\n  padding-top: 42.85714%;\n}\n\n.embed-responsive-16by9::before {\n  padding-top: 56.25%;\n}\n\n.embed-responsive-3by4::before {\n  padding-top: 133.33333%;\n}\n\n.embed-responsive-1by1::before {\n  padding-top: 100%;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n@media (min-width: 576px) {\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n  .align-items-md-center {\n    align-items: center !important;\n  }\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n  .align-content-md-center {\n    align-content: center !important;\n  }\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n  .align-self-md-center {\n    align-self: center !important;\n  }\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n}\n\n.float-left {\n  float: left !important;\n}\n\n.float-right {\n  float: right !important;\n}\n\n.float-none {\n  float: none !important;\n}\n\n@media (min-width: 576px) {\n  .float-sm-left {\n    float: left !important;\n  }\n  .float-sm-right {\n    float: right !important;\n  }\n  .float-sm-none {\n    float: none !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .float-md-left {\n    float: left !important;\n  }\n  .float-md-right {\n    float: right !important;\n  }\n  .float-md-none {\n    float: none !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .float-lg-left {\n    float: left !important;\n  }\n  .float-lg-right {\n    float: right !important;\n  }\n  .float-lg-none {\n    float: none !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .float-xl-left {\n    float: left !important;\n  }\n  .float-xl-right {\n    float: right !important;\n  }\n  .float-xl-none {\n    float: none !important;\n  }\n}\n\n.overflow-auto {\n  overflow: auto !important;\n}\n\n.overflow-hidden {\n  overflow: hidden !important;\n}\n\n.position-static {\n  position: static !important;\n}\n\n.position-relative {\n  position: relative !important;\n}\n\n.position-absolute {\n  position: absolute !important;\n}\n\n.position-fixed {\n  position: fixed !important;\n}\n\n.position-sticky {\n  position: sticky !important;\n}\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n@supports (position: sticky) {\n  .sticky-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n}\n\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n.sr-only-focusable:active,\n.sr-only-focusable:focus {\n  position: static;\n  width: auto;\n  height: auto;\n  overflow: visible;\n  clip: auto;\n  white-space: normal;\n}\n\n.shadow-sm {\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;\n}\n\n.shadow {\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;\n}\n\n.shadow-lg {\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;\n}\n\n.shadow-none {\n  box-shadow: none !important;\n}\n\n.w-25 {\n  width: 25% !important;\n}\n\n.w-50 {\n  width: 50% !important;\n}\n\n.w-75 {\n  width: 75% !important;\n}\n\n.w-100 {\n  width: 100% !important;\n}\n\n.w-auto {\n  width: auto !important;\n}\n\n.h-25 {\n  height: 25% !important;\n}\n\n.h-50 {\n  height: 50% !important;\n}\n\n.h-75 {\n  height: 75% !important;\n}\n\n.h-100 {\n  height: 100% !important;\n}\n\n.h-auto {\n  height: auto !important;\n}\n\n.mw-100 {\n  max-width: 100% !important;\n}\n\n.mh-100 {\n  max-height: 100% !important;\n}\n\n.min-vw-100 {\n  min-width: 100vw !important;\n}\n\n.min-vh-100 {\n  min-height: 100vh !important;\n}\n\n.vw-100 {\n  width: 100vw !important;\n}\n\n.vh-100 {\n  height: 100vh !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.mt-0,\n.my-0 {\n  margin-top: 0 !important;\n}\n\n.mr-0,\n.mx-0 {\n  margin-right: 0 !important;\n}\n\n.mb-0,\n.my-0 {\n  margin-bottom: 0 !important;\n}\n\n.ml-0,\n.mx-0 {\n  margin-left: 0 !important;\n}\n\n.m-1 {\n  margin: 0.25rem !important;\n}\n\n.mt-1,\n.my-1 {\n  margin-top: 0.25rem !important;\n}\n\n.mr-1,\n.mx-1 {\n  margin-right: 0.25rem !important;\n}\n\n.mb-1,\n.my-1 {\n  margin-bottom: 0.25rem !important;\n}\n\n.ml-1,\n.mx-1 {\n  margin-left: 0.25rem !important;\n}\n\n.m-2 {\n  margin: 0.5rem !important;\n}\n\n.mt-2,\n.my-2 {\n  margin-top: 0.5rem !important;\n}\n\n.mr-2,\n.mx-2 {\n  margin-right: 0.5rem !important;\n}\n\n.mb-2,\n.my-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.ml-2,\n.mx-2 {\n  margin-left: 0.5rem !important;\n}\n\n.m-3 {\n  margin: 1rem !important;\n}\n\n.mt-3,\n.my-3 {\n  margin-top: 1rem !important;\n}\n\n.mr-3,\n.mx-3 {\n  margin-right: 1rem !important;\n}\n\n.mb-3,\n.my-3 {\n  margin-bottom: 1rem !important;\n}\n\n.ml-3,\n.mx-3 {\n  margin-left: 1rem !important;\n}\n\n.m-4 {\n  margin: 1.5rem !important;\n}\n\n.mt-4,\n.my-4 {\n  margin-top: 1.5rem !important;\n}\n\n.mr-4,\n.mx-4 {\n  margin-right: 1.5rem !important;\n}\n\n.mb-4,\n.my-4 {\n  margin-bottom: 1.5rem !important;\n}\n\n.ml-4,\n.mx-4 {\n  margin-left: 1.5rem !important;\n}\n\n.m-5 {\n  margin: 3rem !important;\n}\n\n.mt-5,\n.my-5 {\n  margin-top: 3rem !important;\n}\n\n.mr-5,\n.mx-5 {\n  margin-right: 3rem !important;\n}\n\n.mb-5,\n.my-5 {\n  margin-bottom: 3rem !important;\n}\n\n.ml-5,\n.mx-5 {\n  margin-left: 3rem !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.pt-0,\n.py-0 {\n  padding-top: 0 !important;\n}\n\n.pr-0,\n.px-0 {\n  padding-right: 0 !important;\n}\n\n.pb-0,\n.py-0 {\n  padding-bottom: 0 !important;\n}\n\n.pl-0,\n.px-0 {\n  padding-left: 0 !important;\n}\n\n.p-1 {\n  padding: 0.25rem !important;\n}\n\n.pt-1,\n.py-1 {\n  padding-top: 0.25rem !important;\n}\n\n.pr-1,\n.px-1 {\n  padding-right: 0.25rem !important;\n}\n\n.pb-1,\n.py-1 {\n  padding-bottom: 0.25rem !important;\n}\n\n.pl-1,\n.px-1 {\n  padding-left: 0.25rem !important;\n}\n\n.p-2 {\n  padding: 0.5rem !important;\n}\n\n.pt-2,\n.py-2 {\n  padding-top: 0.5rem !important;\n}\n\n.pr-2,\n.px-2 {\n  padding-right: 0.5rem !important;\n}\n\n.pb-2,\n.py-2 {\n  padding-bottom: 0.5rem !important;\n}\n\n.pl-2,\n.px-2 {\n  padding-left: 0.5rem !important;\n}\n\n.p-3 {\n  padding: 1rem !important;\n}\n\n.pt-3,\n.py-3 {\n  padding-top: 1rem !important;\n}\n\n.pr-3,\n.px-3 {\n  padding-right: 1rem !important;\n}\n\n.pb-3,\n.py-3 {\n  padding-bottom: 1rem !important;\n}\n\n.pl-3,\n.px-3 {\n  padding-left: 1rem !important;\n}\n\n.p-4 {\n  padding: 1.5rem !important;\n}\n\n.pt-4,\n.py-4 {\n  padding-top: 1.5rem !important;\n}\n\n.pr-4,\n.px-4 {\n  padding-right: 1.5rem !important;\n}\n\n.pb-4,\n.py-4 {\n  padding-bottom: 1.5rem !important;\n}\n\n.pl-4,\n.px-4 {\n  padding-left: 1.5rem !important;\n}\n\n.p-5 {\n  padding: 3rem !important;\n}\n\n.pt-5,\n.py-5 {\n  padding-top: 3rem !important;\n}\n\n.pr-5,\n.px-5 {\n  padding-right: 3rem !important;\n}\n\n.pb-5,\n.py-5 {\n  padding-bottom: 3rem !important;\n}\n\n.pl-5,\n.px-5 {\n  padding-left: 3rem !important;\n}\n\n.m-n1 {\n  margin: -0.25rem !important;\n}\n\n.mt-n1,\n.my-n1 {\n  margin-top: -0.25rem !important;\n}\n\n.mr-n1,\n.mx-n1 {\n  margin-right: -0.25rem !important;\n}\n\n.mb-n1,\n.my-n1 {\n  margin-bottom: -0.25rem !important;\n}\n\n.ml-n1,\n.mx-n1 {\n  margin-left: -0.25rem !important;\n}\n\n.m-n2 {\n  margin: -0.5rem !important;\n}\n\n.mt-n2,\n.my-n2 {\n  margin-top: -0.5rem !important;\n}\n\n.mr-n2,\n.mx-n2 {\n  margin-right: -0.5rem !important;\n}\n\n.mb-n2,\n.my-n2 {\n  margin-bottom: -0.5rem !important;\n}\n\n.ml-n2,\n.mx-n2 {\n  margin-left: -0.5rem !important;\n}\n\n.m-n3 {\n  margin: -1rem !important;\n}\n\n.mt-n3,\n.my-n3 {\n  margin-top: -1rem !important;\n}\n\n.mr-n3,\n.mx-n3 {\n  margin-right: -1rem !important;\n}\n\n.mb-n3,\n.my-n3 {\n  margin-bottom: -1rem !important;\n}\n\n.ml-n3,\n.mx-n3 {\n  margin-left: -1rem !important;\n}\n\n.m-n4 {\n  margin: -1.5rem !important;\n}\n\n.mt-n4,\n.my-n4 {\n  margin-top: -1.5rem !important;\n}\n\n.mr-n4,\n.mx-n4 {\n  margin-right: -1.5rem !important;\n}\n\n.mb-n4,\n.my-n4 {\n  margin-bottom: -1.5rem !important;\n}\n\n.ml-n4,\n.mx-n4 {\n  margin-left: -1.5rem !important;\n}\n\n.m-n5 {\n  margin: -3rem !important;\n}\n\n.mt-n5,\n.my-n5 {\n  margin-top: -3rem !important;\n}\n\n.mr-n5,\n.mx-n5 {\n  margin-right: -3rem !important;\n}\n\n.mb-n5,\n.my-n5 {\n  margin-bottom: -3rem !important;\n}\n\n.ml-n5,\n.mx-n5 {\n  margin-left: -3rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mt-auto,\n.my-auto {\n  margin-top: auto !important;\n}\n\n.mr-auto,\n.mx-auto {\n  margin-right: auto !important;\n}\n\n.mb-auto,\n.my-auto {\n  margin-bottom: auto !important;\n}\n\n.ml-auto,\n.mx-auto {\n  margin-left: auto !important;\n}\n\n@media (min-width: 576px) {\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n  .mt-sm-0,\n  .my-sm-0 {\n    margin-top: 0 !important;\n  }\n  .mr-sm-0,\n  .mx-sm-0 {\n    margin-right: 0 !important;\n  }\n  .mb-sm-0,\n  .my-sm-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-sm-0,\n  .mx-sm-0 {\n    margin-left: 0 !important;\n  }\n  .m-sm-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-sm-1,\n  .my-sm-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-sm-1,\n  .mx-sm-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-sm-1,\n  .my-sm-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-sm-1,\n  .mx-sm-1 {\n    margin-left: 0.25rem !important;\n  }\n  .m-sm-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-sm-2,\n  .my-sm-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-sm-2,\n  .mx-sm-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-sm-2,\n  .my-sm-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-sm-2,\n  .mx-sm-2 {\n    margin-left: 0.5rem !important;\n  }\n  .m-sm-3 {\n    margin: 1rem !important;\n  }\n  .mt-sm-3,\n  .my-sm-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-sm-3,\n  .mx-sm-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-sm-3,\n  .my-sm-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-sm-3,\n  .mx-sm-3 {\n    margin-left: 1rem !important;\n  }\n  .m-sm-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-sm-4,\n  .my-sm-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-sm-4,\n  .mx-sm-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-sm-4,\n  .my-sm-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-sm-4,\n  .mx-sm-4 {\n    margin-left: 1.5rem !important;\n  }\n  .m-sm-5 {\n    margin: 3rem !important;\n  }\n  .mt-sm-5,\n  .my-sm-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-sm-5,\n  .mx-sm-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-sm-5,\n  .my-sm-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-sm-5,\n  .mx-sm-5 {\n    margin-left: 3rem !important;\n  }\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n  .pt-sm-0,\n  .py-sm-0 {\n    padding-top: 0 !important;\n  }\n  .pr-sm-0,\n  .px-sm-0 {\n    padding-right: 0 !important;\n  }\n  .pb-sm-0,\n  .py-sm-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-sm-0,\n  .px-sm-0 {\n    padding-left: 0 !important;\n  }\n  .p-sm-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-sm-1,\n  .py-sm-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-sm-1,\n  .px-sm-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-sm-1,\n  .py-sm-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-sm-1,\n  .px-sm-1 {\n    padding-left: 0.25rem !important;\n  }\n  .p-sm-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-sm-2,\n  .py-sm-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-sm-2,\n  .px-sm-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-sm-2,\n  .py-sm-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-sm-2,\n  .px-sm-2 {\n    padding-left: 0.5rem !important;\n  }\n  .p-sm-3 {\n    padding: 1rem !important;\n  }\n  .pt-sm-3,\n  .py-sm-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-sm-3,\n  .px-sm-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-sm-3,\n  .py-sm-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-sm-3,\n  .px-sm-3 {\n    padding-left: 1rem !important;\n  }\n  .p-sm-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-sm-4,\n  .py-sm-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-sm-4,\n  .px-sm-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-sm-4,\n  .py-sm-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-sm-4,\n  .px-sm-4 {\n    padding-left: 1.5rem !important;\n  }\n  .p-sm-5 {\n    padding: 3rem !important;\n  }\n  .pt-sm-5,\n  .py-sm-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-sm-5,\n  .px-sm-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-sm-5,\n  .py-sm-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-sm-5,\n  .px-sm-5 {\n    padding-left: 3rem !important;\n  }\n  .m-sm-n1 {\n    margin: -0.25rem !important;\n  }\n  .mt-sm-n1,\n  .my-sm-n1 {\n    margin-top: -0.25rem !important;\n  }\n  .mr-sm-n1,\n  .mx-sm-n1 {\n    margin-right: -0.25rem !important;\n  }\n  .mb-sm-n1,\n  .my-sm-n1 {\n    margin-bottom: -0.25rem !important;\n  }\n  .ml-sm-n1,\n  .mx-sm-n1 {\n    margin-left: -0.25rem !important;\n  }\n  .m-sm-n2 {\n    margin: -0.5rem !important;\n  }\n  .mt-sm-n2,\n  .my-sm-n2 {\n    margin-top: -0.5rem !important;\n  }\n  .mr-sm-n2,\n  .mx-sm-n2 {\n    margin-right: -0.5rem !important;\n  }\n  .mb-sm-n2,\n  .my-sm-n2 {\n    margin-bottom: -0.5rem !important;\n  }\n  .ml-sm-n2,\n  .mx-sm-n2 {\n    margin-left: -0.5rem !important;\n  }\n  .m-sm-n3 {\n    margin: -1rem !important;\n  }\n  .mt-sm-n3,\n  .my-sm-n3 {\n    margin-top: -1rem !important;\n  }\n  .mr-sm-n3,\n  .mx-sm-n3 {\n    margin-right: -1rem !important;\n  }\n  .mb-sm-n3,\n  .my-sm-n3 {\n    margin-bottom: -1rem !important;\n  }\n  .ml-sm-n3,\n  .mx-sm-n3 {\n    margin-left: -1rem !important;\n  }\n  .m-sm-n4 {\n    margin: -1.5rem !important;\n  }\n  .mt-sm-n4,\n  .my-sm-n4 {\n    margin-top: -1.5rem !important;\n  }\n  .mr-sm-n4,\n  .mx-sm-n4 {\n    margin-right: -1.5rem !important;\n  }\n  .mb-sm-n4,\n  .my-sm-n4 {\n    margin-bottom: -1.5rem !important;\n  }\n  .ml-sm-n4,\n  .mx-sm-n4 {\n    margin-left: -1.5rem !important;\n  }\n  .m-sm-n5 {\n    margin: -3rem !important;\n  }\n  .mt-sm-n5,\n  .my-sm-n5 {\n    margin-top: -3rem !important;\n  }\n  .mr-sm-n5,\n  .mx-sm-n5 {\n    margin-right: -3rem !important;\n  }\n  .mb-sm-n5,\n  .my-sm-n5 {\n    margin-bottom: -3rem !important;\n  }\n  .ml-sm-n5,\n  .mx-sm-n5 {\n    margin-left: -3rem !important;\n  }\n  .m-sm-auto {\n    margin: auto !important;\n  }\n  .mt-sm-auto,\n  .my-sm-auto {\n    margin-top: auto !important;\n  }\n  .mr-sm-auto,\n  .mx-sm-auto {\n    margin-right: auto !important;\n  }\n  .mb-sm-auto,\n  .my-sm-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-sm-auto,\n  .mx-sm-auto {\n    margin-left: auto !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .m-md-0 {\n    margin: 0 !important;\n  }\n  .mt-md-0,\n  .my-md-0 {\n    margin-top: 0 !important;\n  }\n  .mr-md-0,\n  .mx-md-0 {\n    margin-right: 0 !important;\n  }\n  .mb-md-0,\n  .my-md-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-md-0,\n  .mx-md-0 {\n    margin-left: 0 !important;\n  }\n  .m-md-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-md-1,\n  .my-md-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-md-1,\n  .mx-md-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-md-1,\n  .my-md-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-md-1,\n  .mx-md-1 {\n    margin-left: 0.25rem !important;\n  }\n  .m-md-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-md-2,\n  .my-md-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-md-2,\n  .mx-md-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-md-2,\n  .my-md-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-md-2,\n  .mx-md-2 {\n    margin-left: 0.5rem !important;\n  }\n  .m-md-3 {\n    margin: 1rem !important;\n  }\n  .mt-md-3,\n  .my-md-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-md-3,\n  .mx-md-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-md-3,\n  .my-md-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-md-3,\n  .mx-md-3 {\n    margin-left: 1rem !important;\n  }\n  .m-md-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-md-4,\n  .my-md-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-md-4,\n  .mx-md-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-md-4,\n  .my-md-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-md-4,\n  .mx-md-4 {\n    margin-left: 1.5rem !important;\n  }\n  .m-md-5 {\n    margin: 3rem !important;\n  }\n  .mt-md-5,\n  .my-md-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-md-5,\n  .mx-md-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-md-5,\n  .my-md-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-md-5,\n  .mx-md-5 {\n    margin-left: 3rem !important;\n  }\n  .p-md-0 {\n    padding: 0 !important;\n  }\n  .pt-md-0,\n  .py-md-0 {\n    padding-top: 0 !important;\n  }\n  .pr-md-0,\n  .px-md-0 {\n    padding-right: 0 !important;\n  }\n  .pb-md-0,\n  .py-md-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-md-0,\n  .px-md-0 {\n    padding-left: 0 !important;\n  }\n  .p-md-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-md-1,\n  .py-md-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-md-1,\n  .px-md-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-md-1,\n  .py-md-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-md-1,\n  .px-md-1 {\n    padding-left: 0.25rem !important;\n  }\n  .p-md-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-md-2,\n  .py-md-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-md-2,\n  .px-md-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-md-2,\n  .py-md-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-md-2,\n  .px-md-2 {\n    padding-left: 0.5rem !important;\n  }\n  .p-md-3 {\n    padding: 1rem !important;\n  }\n  .pt-md-3,\n  .py-md-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-md-3,\n  .px-md-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-md-3,\n  .py-md-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-md-3,\n  .px-md-3 {\n    padding-left: 1rem !important;\n  }\n  .p-md-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-md-4,\n  .py-md-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-md-4,\n  .px-md-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-md-4,\n  .py-md-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-md-4,\n  .px-md-4 {\n    padding-left: 1.5rem !important;\n  }\n  .p-md-5 {\n    padding: 3rem !important;\n  }\n  .pt-md-5,\n  .py-md-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-md-5,\n  .px-md-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-md-5,\n  .py-md-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-md-5,\n  .px-md-5 {\n    padding-left: 3rem !important;\n  }\n  .m-md-n1 {\n    margin: -0.25rem !important;\n  }\n  .mt-md-n1,\n  .my-md-n1 {\n    margin-top: -0.25rem !important;\n  }\n  .mr-md-n1,\n  .mx-md-n1 {\n    margin-right: -0.25rem !important;\n  }\n  .mb-md-n1,\n  .my-md-n1 {\n    margin-bottom: -0.25rem !important;\n  }\n  .ml-md-n1,\n  .mx-md-n1 {\n    margin-left: -0.25rem !important;\n  }\n  .m-md-n2 {\n    margin: -0.5rem !important;\n  }\n  .mt-md-n2,\n  .my-md-n2 {\n    margin-top: -0.5rem !important;\n  }\n  .mr-md-n2,\n  .mx-md-n2 {\n    margin-right: -0.5rem !important;\n  }\n  .mb-md-n2,\n  .my-md-n2 {\n    margin-bottom: -0.5rem !important;\n  }\n  .ml-md-n2,\n  .mx-md-n2 {\n    margin-left: -0.5rem !important;\n  }\n  .m-md-n3 {\n    margin: -1rem !important;\n  }\n  .mt-md-n3,\n  .my-md-n3 {\n    margin-top: -1rem !important;\n  }\n  .mr-md-n3,\n  .mx-md-n3 {\n    margin-right: -1rem !important;\n  }\n  .mb-md-n3,\n  .my-md-n3 {\n    margin-bottom: -1rem !important;\n  }\n  .ml-md-n3,\n  .mx-md-n3 {\n    margin-left: -1rem !important;\n  }\n  .m-md-n4 {\n    margin: -1.5rem !important;\n  }\n  .mt-md-n4,\n  .my-md-n4 {\n    margin-top: -1.5rem !important;\n  }\n  .mr-md-n4,\n  .mx-md-n4 {\n    margin-right: -1.5rem !important;\n  }\n  .mb-md-n4,\n  .my-md-n4 {\n    margin-bottom: -1.5rem !important;\n  }\n  .ml-md-n4,\n  .mx-md-n4 {\n    margin-left: -1.5rem !important;\n  }\n  .m-md-n5 {\n    margin: -3rem !important;\n  }\n  .mt-md-n5,\n  .my-md-n5 {\n    margin-top: -3rem !important;\n  }\n  .mr-md-n5,\n  .mx-md-n5 {\n    margin-right: -3rem !important;\n  }\n  .mb-md-n5,\n  .my-md-n5 {\n    margin-bottom: -3rem !important;\n  }\n  .ml-md-n5,\n  .mx-md-n5 {\n    margin-left: -3rem !important;\n  }\n  .m-md-auto {\n    margin: auto !important;\n  }\n  .mt-md-auto,\n  .my-md-auto {\n    margin-top: auto !important;\n  }\n  .mr-md-auto,\n  .mx-md-auto {\n    margin-right: auto !important;\n  }\n  .mb-md-auto,\n  .my-md-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-md-auto,\n  .mx-md-auto {\n    margin-left: auto !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n  .mt-lg-0,\n  .my-lg-0 {\n    margin-top: 0 !important;\n  }\n  .mr-lg-0,\n  .mx-lg-0 {\n    margin-right: 0 !important;\n  }\n  .mb-lg-0,\n  .my-lg-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-lg-0,\n  .mx-lg-0 {\n    margin-left: 0 !important;\n  }\n  .m-lg-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-lg-1,\n  .my-lg-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-lg-1,\n  .mx-lg-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-lg-1,\n  .my-lg-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-lg-1,\n  .mx-lg-1 {\n    margin-left: 0.25rem !important;\n  }\n  .m-lg-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-lg-2,\n  .my-lg-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-lg-2,\n  .mx-lg-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-lg-2,\n  .my-lg-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-lg-2,\n  .mx-lg-2 {\n    margin-left: 0.5rem !important;\n  }\n  .m-lg-3 {\n    margin: 1rem !important;\n  }\n  .mt-lg-3,\n  .my-lg-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-lg-3,\n  .mx-lg-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-lg-3,\n  .my-lg-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-lg-3,\n  .mx-lg-3 {\n    margin-left: 1rem !important;\n  }\n  .m-lg-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-lg-4,\n  .my-lg-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-lg-4,\n  .mx-lg-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-lg-4,\n  .my-lg-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-lg-4,\n  .mx-lg-4 {\n    margin-left: 1.5rem !important;\n  }\n  .m-lg-5 {\n    margin: 3rem !important;\n  }\n  .mt-lg-5,\n  .my-lg-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-lg-5,\n  .mx-lg-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-lg-5,\n  .my-lg-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-lg-5,\n  .mx-lg-5 {\n    margin-left: 3rem !important;\n  }\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n  .pt-lg-0,\n  .py-lg-0 {\n    padding-top: 0 !important;\n  }\n  .pr-lg-0,\n  .px-lg-0 {\n    padding-right: 0 !important;\n  }\n  .pb-lg-0,\n  .py-lg-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-lg-0,\n  .px-lg-0 {\n    padding-left: 0 !important;\n  }\n  .p-lg-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-lg-1,\n  .py-lg-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-lg-1,\n  .px-lg-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-lg-1,\n  .py-lg-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-lg-1,\n  .px-lg-1 {\n    padding-left: 0.25rem !important;\n  }\n  .p-lg-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-lg-2,\n  .py-lg-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-lg-2,\n  .px-lg-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-lg-2,\n  .py-lg-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-lg-2,\n  .px-lg-2 {\n    padding-left: 0.5rem !important;\n  }\n  .p-lg-3 {\n    padding: 1rem !important;\n  }\n  .pt-lg-3,\n  .py-lg-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-lg-3,\n  .px-lg-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-lg-3,\n  .py-lg-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-lg-3,\n  .px-lg-3 {\n    padding-left: 1rem !important;\n  }\n  .p-lg-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-lg-4,\n  .py-lg-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-lg-4,\n  .px-lg-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-lg-4,\n  .py-lg-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-lg-4,\n  .px-lg-4 {\n    padding-left: 1.5rem !important;\n  }\n  .p-lg-5 {\n    padding: 3rem !important;\n  }\n  .pt-lg-5,\n  .py-lg-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-lg-5,\n  .px-lg-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-lg-5,\n  .py-lg-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-lg-5,\n  .px-lg-5 {\n    padding-left: 3rem !important;\n  }\n  .m-lg-n1 {\n    margin: -0.25rem !important;\n  }\n  .mt-lg-n1,\n  .my-lg-n1 {\n    margin-top: -0.25rem !important;\n  }\n  .mr-lg-n1,\n  .mx-lg-n1 {\n    margin-right: -0.25rem !important;\n  }\n  .mb-lg-n1,\n  .my-lg-n1 {\n    margin-bottom: -0.25rem !important;\n  }\n  .ml-lg-n1,\n  .mx-lg-n1 {\n    margin-left: -0.25rem !important;\n  }\n  .m-lg-n2 {\n    margin: -0.5rem !important;\n  }\n  .mt-lg-n2,\n  .my-lg-n2 {\n    margin-top: -0.5rem !important;\n  }\n  .mr-lg-n2,\n  .mx-lg-n2 {\n    margin-right: -0.5rem !important;\n  }\n  .mb-lg-n2,\n  .my-lg-n2 {\n    margin-bottom: -0.5rem !important;\n  }\n  .ml-lg-n2,\n  .mx-lg-n2 {\n    margin-left: -0.5rem !important;\n  }\n  .m-lg-n3 {\n    margin: -1rem !important;\n  }\n  .mt-lg-n3,\n  .my-lg-n3 {\n    margin-top: -1rem !important;\n  }\n  .mr-lg-n3,\n  .mx-lg-n3 {\n    margin-right: -1rem !important;\n  }\n  .mb-lg-n3,\n  .my-lg-n3 {\n    margin-bottom: -1rem !important;\n  }\n  .ml-lg-n3,\n  .mx-lg-n3 {\n    margin-left: -1rem !important;\n  }\n  .m-lg-n4 {\n    margin: -1.5rem !important;\n  }\n  .mt-lg-n4,\n  .my-lg-n4 {\n    margin-top: -1.5rem !important;\n  }\n  .mr-lg-n4,\n  .mx-lg-n4 {\n    margin-right: -1.5rem !important;\n  }\n  .mb-lg-n4,\n  .my-lg-n4 {\n    margin-bottom: -1.5rem !important;\n  }\n  .ml-lg-n4,\n  .mx-lg-n4 {\n    margin-left: -1.5rem !important;\n  }\n  .m-lg-n5 {\n    margin: -3rem !important;\n  }\n  .mt-lg-n5,\n  .my-lg-n5 {\n    margin-top: -3rem !important;\n  }\n  .mr-lg-n5,\n  .mx-lg-n5 {\n    margin-right: -3rem !important;\n  }\n  .mb-lg-n5,\n  .my-lg-n5 {\n    margin-bottom: -3rem !important;\n  }\n  .ml-lg-n5,\n  .mx-lg-n5 {\n    margin-left: -3rem !important;\n  }\n  .m-lg-auto {\n    margin: auto !important;\n  }\n  .mt-lg-auto,\n  .my-lg-auto {\n    margin-top: auto !important;\n  }\n  .mr-lg-auto,\n  .mx-lg-auto {\n    margin-right: auto !important;\n  }\n  .mb-lg-auto,\n  .my-lg-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-lg-auto,\n  .mx-lg-auto {\n    margin-left: auto !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n  .mt-xl-0,\n  .my-xl-0 {\n    margin-top: 0 !important;\n  }\n  .mr-xl-0,\n  .mx-xl-0 {\n    margin-right: 0 !important;\n  }\n  .mb-xl-0,\n  .my-xl-0 {\n    margin-bottom: 0 !important;\n  }\n  .ml-xl-0,\n  .mx-xl-0 {\n    margin-left: 0 !important;\n  }\n  .m-xl-1 {\n    margin: 0.25rem !important;\n  }\n  .mt-xl-1,\n  .my-xl-1 {\n    margin-top: 0.25rem !important;\n  }\n  .mr-xl-1,\n  .mx-xl-1 {\n    margin-right: 0.25rem !important;\n  }\n  .mb-xl-1,\n  .my-xl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n  .ml-xl-1,\n  .mx-xl-1 {\n    margin-left: 0.25rem !important;\n  }\n  .m-xl-2 {\n    margin: 0.5rem !important;\n  }\n  .mt-xl-2,\n  .my-xl-2 {\n    margin-top: 0.5rem !important;\n  }\n  .mr-xl-2,\n  .mx-xl-2 {\n    margin-right: 0.5rem !important;\n  }\n  .mb-xl-2,\n  .my-xl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n  .ml-xl-2,\n  .mx-xl-2 {\n    margin-left: 0.5rem !important;\n  }\n  .m-xl-3 {\n    margin: 1rem !important;\n  }\n  .mt-xl-3,\n  .my-xl-3 {\n    margin-top: 1rem !important;\n  }\n  .mr-xl-3,\n  .mx-xl-3 {\n    margin-right: 1rem !important;\n  }\n  .mb-xl-3,\n  .my-xl-3 {\n    margin-bottom: 1rem !important;\n  }\n  .ml-xl-3,\n  .mx-xl-3 {\n    margin-left: 1rem !important;\n  }\n  .m-xl-4 {\n    margin: 1.5rem !important;\n  }\n  .mt-xl-4,\n  .my-xl-4 {\n    margin-top: 1.5rem !important;\n  }\n  .mr-xl-4,\n  .mx-xl-4 {\n    margin-right: 1.5rem !important;\n  }\n  .mb-xl-4,\n  .my-xl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n  .ml-xl-4,\n  .mx-xl-4 {\n    margin-left: 1.5rem !important;\n  }\n  .m-xl-5 {\n    margin: 3rem !important;\n  }\n  .mt-xl-5,\n  .my-xl-5 {\n    margin-top: 3rem !important;\n  }\n  .mr-xl-5,\n  .mx-xl-5 {\n    margin-right: 3rem !important;\n  }\n  .mb-xl-5,\n  .my-xl-5 {\n    margin-bottom: 3rem !important;\n  }\n  .ml-xl-5,\n  .mx-xl-5 {\n    margin-left: 3rem !important;\n  }\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n  .pt-xl-0,\n  .py-xl-0 {\n    padding-top: 0 !important;\n  }\n  .pr-xl-0,\n  .px-xl-0 {\n    padding-right: 0 !important;\n  }\n  .pb-xl-0,\n  .py-xl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pl-xl-0,\n  .px-xl-0 {\n    padding-left: 0 !important;\n  }\n  .p-xl-1 {\n    padding: 0.25rem !important;\n  }\n  .pt-xl-1,\n  .py-xl-1 {\n    padding-top: 0.25rem !important;\n  }\n  .pr-xl-1,\n  .px-xl-1 {\n    padding-right: 0.25rem !important;\n  }\n  .pb-xl-1,\n  .py-xl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n  .pl-xl-1,\n  .px-xl-1 {\n    padding-left: 0.25rem !important;\n  }\n  .p-xl-2 {\n    padding: 0.5rem !important;\n  }\n  .pt-xl-2,\n  .py-xl-2 {\n    padding-top: 0.5rem !important;\n  }\n  .pr-xl-2,\n  .px-xl-2 {\n    padding-right: 0.5rem !important;\n  }\n  .pb-xl-2,\n  .py-xl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n  .pl-xl-2,\n  .px-xl-2 {\n    padding-left: 0.5rem !important;\n  }\n  .p-xl-3 {\n    padding: 1rem !important;\n  }\n  .pt-xl-3,\n  .py-xl-3 {\n    padding-top: 1rem !important;\n  }\n  .pr-xl-3,\n  .px-xl-3 {\n    padding-right: 1rem !important;\n  }\n  .pb-xl-3,\n  .py-xl-3 {\n    padding-bottom: 1rem !important;\n  }\n  .pl-xl-3,\n  .px-xl-3 {\n    padding-left: 1rem !important;\n  }\n  .p-xl-4 {\n    padding: 1.5rem !important;\n  }\n  .pt-xl-4,\n  .py-xl-4 {\n    padding-top: 1.5rem !important;\n  }\n  .pr-xl-4,\n  .px-xl-4 {\n    padding-right: 1.5rem !important;\n  }\n  .pb-xl-4,\n  .py-xl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n  .pl-xl-4,\n  .px-xl-4 {\n    padding-left: 1.5rem !important;\n  }\n  .p-xl-5 {\n    padding: 3rem !important;\n  }\n  .pt-xl-5,\n  .py-xl-5 {\n    padding-top: 3rem !important;\n  }\n  .pr-xl-5,\n  .px-xl-5 {\n    padding-right: 3rem !important;\n  }\n  .pb-xl-5,\n  .py-xl-5 {\n    padding-bottom: 3rem !important;\n  }\n  .pl-xl-5,\n  .px-xl-5 {\n    padding-left: 3rem !important;\n  }\n  .m-xl-n1 {\n    margin: -0.25rem !important;\n  }\n  .mt-xl-n1,\n  .my-xl-n1 {\n    margin-top: -0.25rem !important;\n  }\n  .mr-xl-n1,\n  .mx-xl-n1 {\n    margin-right: -0.25rem !important;\n  }\n  .mb-xl-n1,\n  .my-xl-n1 {\n    margin-bottom: -0.25rem !important;\n  }\n  .ml-xl-n1,\n  .mx-xl-n1 {\n    margin-left: -0.25rem !important;\n  }\n  .m-xl-n2 {\n    margin: -0.5rem !important;\n  }\n  .mt-xl-n2,\n  .my-xl-n2 {\n    margin-top: -0.5rem !important;\n  }\n  .mr-xl-n2,\n  .mx-xl-n2 {\n    margin-right: -0.5rem !important;\n  }\n  .mb-xl-n2,\n  .my-xl-n2 {\n    margin-bottom: -0.5rem !important;\n  }\n  .ml-xl-n2,\n  .mx-xl-n2 {\n    margin-left: -0.5rem !important;\n  }\n  .m-xl-n3 {\n    margin: -1rem !important;\n  }\n  .mt-xl-n3,\n  .my-xl-n3 {\n    margin-top: -1rem !important;\n  }\n  .mr-xl-n3,\n  .mx-xl-n3 {\n    margin-right: -1rem !important;\n  }\n  .mb-xl-n3,\n  .my-xl-n3 {\n    margin-bottom: -1rem !important;\n  }\n  .ml-xl-n3,\n  .mx-xl-n3 {\n    margin-left: -1rem !important;\n  }\n  .m-xl-n4 {\n    margin: -1.5rem !important;\n  }\n  .mt-xl-n4,\n  .my-xl-n4 {\n    margin-top: -1.5rem !important;\n  }\n  .mr-xl-n4,\n  .mx-xl-n4 {\n    margin-right: -1.5rem !important;\n  }\n  .mb-xl-n4,\n  .my-xl-n4 {\n    margin-bottom: -1.5rem !important;\n  }\n  .ml-xl-n4,\n  .mx-xl-n4 {\n    margin-left: -1.5rem !important;\n  }\n  .m-xl-n5 {\n    margin: -3rem !important;\n  }\n  .mt-xl-n5,\n  .my-xl-n5 {\n    margin-top: -3rem !important;\n  }\n  .mr-xl-n5,\n  .mx-xl-n5 {\n    margin-right: -3rem !important;\n  }\n  .mb-xl-n5,\n  .my-xl-n5 {\n    margin-bottom: -3rem !important;\n  }\n  .ml-xl-n5,\n  .mx-xl-n5 {\n    margin-left: -3rem !important;\n  }\n  .m-xl-auto {\n    margin: auto !important;\n  }\n  .mt-xl-auto,\n  .my-xl-auto {\n    margin-top: auto !important;\n  }\n  .mr-xl-auto,\n  .mx-xl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xl-auto,\n  .my-xl-auto {\n    margin-bottom: auto !important;\n  }\n  .ml-xl-auto,\n  .mx-xl-auto {\n    margin-left: auto !important;\n  }\n}\n\n.text-monospace {\n  font-family: \"ff-magda-clean-mono-web-pro\", SFMono-Regular, Menlo, Monaco, Consolas,\n    \"Liberation Mono\", \"Courier New\", monospace;\n}\n\n.text-justify {\n  text-align: justify !important;\n}\n\n.text-wrap {\n  white-space: normal !important;\n}\n\n.text-nowrap {\n  white-space: nowrap !important;\n}\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.text-left {\n  text-align: left !important;\n}\n\n.text-right {\n  text-align: right !important;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n@media (min-width: 576px) {\n  .text-sm-left {\n    text-align: left !important;\n  }\n  .text-sm-right {\n    text-align: right !important;\n  }\n  .text-sm-center {\n    text-align: center !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .text-md-left {\n    text-align: left !important;\n  }\n  .text-md-right {\n    text-align: right !important;\n  }\n  .text-md-center {\n    text-align: center !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .text-lg-left {\n    text-align: left !important;\n  }\n  .text-lg-right {\n    text-align: right !important;\n  }\n  .text-lg-center {\n    text-align: center !important;\n  }\n}\n\n@media (min-width: 1200px) {\n  .text-xl-left {\n    text-align: left !important;\n  }\n  .text-xl-right {\n    text-align: right !important;\n  }\n  .text-xl-center {\n    text-align: center !important;\n  }\n}\n\n.text-lowercase {\n  text-transform: lowercase !important;\n}\n\n.text-uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-capitalize {\n  text-transform: capitalize !important;\n}\n\n.font-weight-light {\n  font-weight: 300 !important;\n}\n\n.font-weight-lighter {\n  font-weight: lighter !important;\n}\n\n.font-weight-normal {\n  font-weight: 400 !important;\n}\n\n.font-weight-bold {\n  font-weight: 700 !important;\n}\n\n.font-weight-bolder {\n  font-weight: bolder !important;\n}\n\n.font-italic {\n  font-style: italic !important;\n}\n\n.text-white {\n  color: #fff !important;\n}\n\n.text-primary {\n  color: #fc2a00 !important;\n}\n\na.text-primary:hover,\na.text-primary:focus {\n  color: #b01d00 !important;\n}\n\n.text-secondary {\n  color: #6c757d !important;\n}\n\na.text-secondary:hover,\na.text-secondary:focus {\n  color: #494f54 !important;\n}\n\n.text-success {\n  color: #28a745 !important;\n}\n\na.text-success:hover,\na.text-success:focus {\n  color: #19692c !important;\n}\n\n.text-info {\n  color: #17a2b8 !important;\n}\n\na.text-info:hover,\na.text-info:focus {\n  color: #0f6674 !important;\n}\n\n.text-warning {\n  color: #fcaf17 !important;\n}\n\na.text-warning:hover,\na.text-warning:focus {\n  color: #c48303 !important;\n}\n\n.text-danger {\n  color: #dc3545 !important;\n}\n\na.text-danger:hover,\na.text-danger:focus {\n  color: #a71d2a !important;\n}\n\n.text-light {\n  color: #e6e6e6 !important;\n}\n\na.text-light:hover,\na.text-light:focus {\n  color: silver !important;\n}\n\n.text-dark {\n  color: #343a40 !important;\n}\n\na.text-dark:hover,\na.text-dark:focus {\n  color: #121416 !important;\n}\n\n.text-body {\n  color: #212529 !important;\n}\n\n.text-muted {\n  color: #6c757d !important;\n}\n\n.text-black-50 {\n  color: rgba(0, 0, 0, 0.5) !important;\n}\n\n.text-white-50 {\n  color: rgba(255, 255, 255, 0.5) !important;\n}\n\n.text-hide {\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n}\n\n.text-decoration-none {\n  text-decoration: none !important;\n}\n\n.text-reset {\n  color: inherit !important;\n}\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n\n@media print {\n  *,\n  *::before,\n  *::after {\n    text-shadow: none !important;\n    box-shadow: none !important;\n  }\n  a:not(.btn) {\n    text-decoration: underline;\n  }\n  abbr[title]::after {\n    content: \" (\" attr(title) \")\";\n  }\n  pre {\n    white-space: pre-wrap !important;\n  }\n  pre,\n  blockquote {\n    border: 1px solid #adb5bd;\n    page-break-inside: avoid;\n  }\n  thead {\n    display: table-header-group;\n  }\n  tr,\n  img {\n    page-break-inside: avoid;\n  }\n  p,\n  h2,\n  h3 {\n    orphans: 3;\n    widows: 3;\n  }\n  h2,\n  h3 {\n    page-break-after: avoid;\n  }\n  @page {\n    size: a3;\n  }\n  body {\n    min-width: 992px !important;\n  }\n  .container {\n    min-width: 992px !important;\n  }\n  .navbar {\n    display: none;\n  }\n  .badge {\n    border: 1px solid #000;\n  }\n  .table {\n    border-collapse: collapse !important;\n  }\n  .table td,\n  .table th {\n    background-color: #fff !important;\n  }\n  .table-bordered th,\n  .table-bordered td {\n    border: 1px solid #dee2e6 !important;\n  }\n  .table-dark {\n    color: inherit;\n  }\n  .table-dark th,\n  .table-dark td,\n  .table-dark thead th,\n  .table-dark tbody + tbody {\n    border-color: #dee2e6;\n  }\n  .table .thead-dark th {\n    color: inherit;\n    border-color: #dee2e6;\n  }\n}\n", ".donation-buttons {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n/* .donation-buttons .btn-group-toggle {\n  display: flex;\n  flex: 1;\n} */\n\n.donation-buttons .btn-primary,\n.donation-buttons .btn-secondary {\n  margin-right: 15px;\n  margin-bottom: 20px;\n  flex: 1 0 auto;\n}\n\n#denomination-other {\n  display: flex !important;\n  flex: 1 0 100%;\n  align-items: center;\n  margin-right: 15px;\n  position: relative;\n}\n\n#denomination-other > input {\n  margin: 0 !important;\n}\n\n#denomination-other > label {\n  margin-right: 20px;\n  margin-left: 15px;\n  margin-bottom: 0;\n}\n\n#denomination-other input[type=\"number\"] {\n  color: #4d4d4d;\n  font-weight: 400;\n  text-align: center;\n  background-color: rgb(235, 235, 235);\n  padding: 13px 0;\n  /* flex: 1 1 80%; */\n  margin-right: 15px;\n  border-radius: 4px;\n  border: solid 1px rgb(128, 128, 128);\n  position: absolute;\n  top: 8px;\n  right: 16px;\n  width: 70%;\n  height: 38px;\n  z-index: 1;\n}\n\n@media only screen and (max-width: 600px) {\n  #denomination-other input[type=\"number\"] {\n    width: 40%;\n  }\n}\n\n#denomination-other input::placeholder {\n  color: #000 !important;\n  font-size: 18px;\n  /* font-family: \"ff-magda-clean-mono-web-pro\", courier, sans-serif; */\n}\n\n#denomination-other input:hover {\n  background-color: #d9d9d9;\n}\n\n.donation-buttons .btn.btn-secondary:last-of-type {\n  margin-right: 0;\n}\n", "#premiums-container .card {\n  margin-top: 15px;\n  margin-bottom: 15px;\n  height: calc(100% - 30px);\n}\n\n#premiums-container .card .card-img-top {\n  display: flex;\n  align-items: center;\n  flex-direction: column;\n  padding-top: 10px;\n  min-height: 150px;\n  justify-content: center;\n}\n\n#premiums-container .card .card-img-center {\n  max-height: 200px;\n  max-width: calc(100% - 20px);\n}\n\n#premiums-container .card-subtitle span {\n  display: block;\n}\n\n#premiums-container .card .card-text {\n  max-height: 4.5em;\n  overflow: hidden;\n  transition: max-height 0.5s ease;\n}\n\n#premiums-container .card .card-text.show {\n  max-height: 80em;\n}\n\n#premiums-container .card .btn.btn-link {\n  width: 100%;\n  text-align: center;\n  margin-top: -15px;\n  margin-bottom: -10px;\n  padding: 0;\n}\n\n/* PREMIUMS LIST */\n\n#premiums-container {\n  margin-top: 30px;\n  margin-bottom: 30px;\n}\n#premiumsTabContent {\n  padding-top: 20px;\n}\n", ".checkmark-container {\n  display: flex;\n  width: 100%;\n  align-items: center;\n  flex-direction: column;\n}\n\n.checkmark-container img {\n  width: 150px;\n  margin-bottom: 15px;\n}\n\n.fadeIn {\n  animation-name: fadeIn;\n  -webkit-animation-name: fadeIn;\n\n  animation-duration: 1.5s;\n  -webkit-animation-duration: 1.5s;\n\n  animation-timing-function: ease-in-out;\n  -webkit-animation-timing-function: ease-in-out;\n\n  visibility: visible !important;\n}\n\n@keyframes fadeIn {\n  0% {\n    transform: scale(0);\n    opacity: 0;\n  }\n  60% {\n    transform: scale(1.1);\n  }\n  80% {\n    transform: scale(0.9);\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n@-webkit-keyframes fadeIn {\n  0% {\n    -webkit-transform: scale(0);\n    opacity: 0;\n  }\n  60% {\n    -webkit-transform: scale(1.1);\n  }\n  80% {\n    -webkit-transform: scale(0.9);\n    opacity: 1;\n  }\n  100% {\n    -webkit-transform: scale(1);\n    opacity: 1;\n  }\n}\n\n:focus::-webkit-input-placeholder {\n  opacity: 0 !important;\n}\n\n:focus::-moz-placeholder {\n  opacity: 0 !important;\n}\n::-webkit-input-placeholder {\n  /* Chrome */\n  opacity: 0.5 !important;\n}\n:-ms-input-placeholder {\n  /* IE 10+ */\n  opacity: 0.5 !important;\n}\n::-moz-placeholder {\n  /* Firefox 19+ */\n  opacity: 0.5 !important;\n}\n"], "names": [], "sourceRoot": ""}