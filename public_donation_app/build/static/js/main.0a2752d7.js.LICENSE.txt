/*!
  Copyright (c) 2018 <PERSON>.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/

/*!
 * Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */

/*! ./Capture */

/*! ./LogRocket */

/*! ./TraceKit */

/*! ./_DataView */

/*! ./_Hash */

/*! ./_ListCache */

/*! ./_Map */

/*! ./_MapCache */

/*! ./_Promise */

/*! ./_Set */

/*! ./_SetCache */

/*! ./_Stack */

/*! ./_Symbol */

/*! ./_Uint8Array */

/*! ./_WeakMap */

/*! ./_arrayFilter */

/*! ./_arrayLikeKeys */

/*! ./_arrayMap */

/*! ./_arrayPush */

/*! ./_arraySome */

/*! ./_assocIndexOf */

/*! ./_baseGet */

/*! ./_baseGetAllKeys */

/*! ./_baseGetTag */

/*! ./_baseHasIn */

/*! ./_baseIsArguments */

/*! ./_baseIsEqual */

/*! ./_baseIsEqualDeep */

/*! ./_baseIsMatch */

/*! ./_baseIsNative */

/*! ./_baseIsTypedArray */

/*! ./_baseIteratee */

/*! ./_baseKeys */

/*! ./_baseMatches */

/*! ./_baseMatchesProperty */

/*! ./_baseProperty */

/*! ./_basePropertyDeep */

/*! ./_baseSortedIndexBy */

/*! ./_baseTimes */

/*! ./_baseToString */

/*! ./_baseUnary */

/*! ./_cacheHas */

/*! ./_castPath */

/*! ./_coreJsData */

/*! ./_equalArrays */

/*! ./_equalByTag */

/*! ./_equalObjects */

/*! ./_freeGlobal */

/*! ./_getAllKeys */

/*! ./_getMapData */

/*! ./_getMatchData */

/*! ./_getNative */

/*! ./_getRawTag */

/*! ./_getSymbols */

/*! ./_getTag */

/*! ./_getValue */

/*! ./_hasPath */

/*! ./_hashClear */

/*! ./_hashDelete */

/*! ./_hashGet */

/*! ./_hashHas */

/*! ./_hashSet */

/*! ./_isIndex */

/*! ./_isKey */

/*! ./_isKeyable */

/*! ./_isMasked */

/*! ./_isPrototype */

/*! ./_isStrictComparable */

/*! ./_listCacheClear */

/*! ./_listCacheDelete */

/*! ./_listCacheGet */

/*! ./_listCacheHas */

/*! ./_listCacheSet */

/*! ./_mapCacheClear */

/*! ./_mapCacheDelete */

/*! ./_mapCacheGet */

/*! ./_mapCacheHas */

/*! ./_mapCacheSet */

/*! ./_mapToArray */

/*! ./_matchesStrictComparable */

/*! ./_memoizeCapped */

/*! ./_nativeCreate */

/*! ./_nativeKeys */

/*! ./_nodeUtil */

/*! ./_objectToString */

/*! ./_overArg */

/*! ./_root */

/*! ./_setCacheAdd */

/*! ./_setCacheHas */

/*! ./_setToArray */

/*! ./_stackClear */

/*! ./_stackDelete */

/*! ./_stackGet */

/*! ./_stackHas */

/*! ./_stackSet */

/*! ./_stringToPath */

/*! ./_toKey */

/*! ./_toSource */

/*! ./addListener */

/*! ./adjustOsVersion */

/*! ./applyUrlSanitizer */

/*! ./arrayLikeToArray.js */

/*! ./arrayWithHoles.js */

/*! ./arrayWithoutHoles.js */

/*! ./browserUtils */

/*! ./constants/issues */

/*! ./constants/logTypes */

/*! ./constants/metrics */

/*! ./constants/mobile */

/*! ./constants/nps */

/*! ./constants/platformTypes */

/*! ./constants/replayTypes */

/*! ./constants/sdkTypes */

/*! ./constants/statusCodes */

/*! ./contains */

/*! ./createEnhancer */

/*! ./createMiddleware */

/*! ./deepArsonify */

/*! ./deepDearsonify */

/*! ./endsWith */

/*! ./enhanceFunc */

/*! ./eq */

/*! ./fetchIntercept */

/*! ./find */

/*! ./findIndex */

/*! ./findKeyFrames */

/*! ./flatten */

/*! ./get */

/*! ./getCssRules */

/*! ./getGraphQLOperation */

/*! ./getNodeSelector */

/*! ./hasIn */

/*! ./hashString */

/*! ./identity */

/*! ./identityStatus */

/*! ./interpolate */

/*! ./isActivityEvent */

/*! ./isArguments */

/*! ./isArray */

/*! ./isArrayLike */

/*! ./isBuffer */

/*! ./isFunction */

/*! ./isLength */

/*! ./isObject */

/*! ./isObjectLike */

/*! ./isRecordingSampled */

/*! ./isSessionEvent */

/*! ./isSymbol */

/*! ./isTypedArray */

/*! ./isValidAppID */

/*! ./iterableToArray.js */

/*! ./iterableToArrayLimit.js */

/*! ./keys */

/*! ./likeOperator */

/*! ./logError */

/*! ./makeLogRocket */

/*! ./makeRecordingID */

/*! ./maybeCleanSwiftUIClassName */

/*! ./memoize */

/*! ./nonIterableRest.js */

/*! ./nonIterableSpread.js */

/*! ./objectWithoutPropertiesLoose.js */

/*! ./parseIntFromHex */

/*! ./parseQueryString */

/*! ./parseSelectorForMatch */

/*! ./property */

/*! ./protectFunc */

/*! ./querySelectorWithShadowDom */

/*! ./randomInt */

/*! ./raven/raven */

/*! ./registerConsole */

/*! ./registerExceptions */

/*! ./registerFetch */

/*! ./registerIonic */

/*! ./registerNetworkInformation */

/*! ./registerXHR */

/*! ./removeOutdated */

/*! ./replayTypes */

/*! ./requireValue */

/*! ./sanitizeValue */

/*! ./scrollMapHistogramToPercent */

/*! ./scrubException */

/*! ./selectorFromNodePath */

/*! ./selectorMatches */

/*! ./sendTelemetryData */

/*! ./setFromArray */

/*! ./setToArray */

/*! ./setup */

/*! ./shallowArsonify */

/*! ./shallowDearsonify */

/*! ./stackTraceFromError */

/*! ./startsWith */

/*! ./stubArray */

/*! ./stubFalse */

/*! ./toPrimitive.js */

/*! ./toPropertyKey.js */

/*! ./toString */

/*! ./typeof.js */

/*! ./types */

/*! ./unsupportedIterableToArray.js */

/*! ./userTraitStrings */

/*! ./uuid */

/*! @babel/runtime/helpers/classCallCheck */

/*! @babel/runtime/helpers/createClass */

/*! @babel/runtime/helpers/defineProperty */

/*! @babel/runtime/helpers/interopRequireDefault */

/*! @babel/runtime/helpers/objectWithoutProperties */

/*! @babel/runtime/helpers/slicedToArray */

/*! @babel/runtime/helpers/toConsumableArray */

/*! @babel/runtime/helpers/typeof */

/*! @logrocket/arson */

/*! @logrocket/console */

/*! @logrocket/exceptions */

/*! @logrocket/network */

/*! @logrocket/now */

/*! @logrocket/redux */

/*! @logrocket/utils */

/*! @logrocket/utils/src/TraceKit */

/*! @logrocket/utils/src/constants/nps */

/*! @logrocket/utils/src/enhanceFunc */

/*! @logrocket/utils/src/mapValues */

/*! @logrocket/utils/src/protectFunc */

/*! @logrocket/utils/src/startsWith */

/*! lodash/sortedLastIndexBy */

/*!*****************************************!*\
        !*** ./packages/logrocket/src/setup.js ***!
        \*****************************************/

/*!*********************************************!*\
        !*** ./packages/logrocket/src/LogRocket.js ***!
        \*********************************************/

/*!**********************************************!*\
        !*** ./packages/@logrocket/now/src/index.js ***!
        \**********************************************/

/*!**********************************************!*\
        !*** ./packages/logrocket/src/module-npm.js ***!
        \**********************************************/

/*!***********************************************!*\
        !*** ./packages/@logrocket/utils/src/find.js ***!
        \***********************************************/

/*!***********************************************!*\
        !*** ./packages/@logrocket/utils/src/uuid.js ***!
        \***********************************************/

/*!************************************************!*\
        !*** ./packages/@logrocket/arson/src/index.js ***!
        \************************************************/

/*!************************************************!*\
        !*** ./packages/@logrocket/redux/src/index.js ***!
        \************************************************/

/*!************************************************!*\
        !*** ./packages/@logrocket/utils/src/index.ts ***!
        \************************************************/

/*!************************************************!*\
        !*** ./packages/@logrocket/utils/src/types.ts ***!
        \************************************************/

/*!*************************************************!*\
        !*** ./packages/logrocket/src/makeLogRocket.js ***!
        \*************************************************/

/*!**************************************************!*\
        !*** ./packages/@logrocket/console/src/index.js ***!
        \**************************************************/

/*!**************************************************!*\
        !*** ./packages/@logrocket/network/src/index.js ***!
        \**************************************************/

/*!**************************************************!*\
        !*** ./packages/@logrocket/utils/src/flatten.js ***!
        \**************************************************/

/*!***************************************************!*\
        !*** ./packages/@logrocket/utils/src/TraceKit.js ***!
        \***************************************************/

/*!***************************************************!*\
        !*** ./packages/@logrocket/utils/src/contains.js ***!
        \***************************************************/

/*!***************************************************!*\
        !*** ./packages/@logrocket/utils/src/endsWith.js ***!
        \***************************************************/

/*!***************************************************!*\
        !*** ./packages/@logrocket/utils/src/logError.js ***!
        \***************************************************/

/*!****************************************************!*\
        !*** ./packages/@logrocket/utils/src/findIndex.js ***!
        \****************************************************/

/*!****************************************************!*\
        !*** ./packages/@logrocket/utils/src/mapValues.js ***!
        \****************************************************/

/*!****************************************************!*\
        !*** ./packages/@logrocket/utils/src/randomInt.js ***!
        \****************************************************/

/*!*****************************************************!*\
        !*** ./packages/@logrocket/exceptions/src/index.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./packages/@logrocket/utils/src/hashString.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./packages/@logrocket/utils/src/setToArray.js ***!
        \*****************************************************/

/*!*****************************************************!*\
        !*** ./packages/@logrocket/utils/src/startsWith.js ***!
        \*****************************************************/

/*!******************************************************!*\
        !*** ./packages/@logrocket/utils/src/addListener.js ***!
        \******************************************************/

/*!******************************************************!*\
        !*** ./packages/@logrocket/utils/src/enhanceFunc.js ***!
        \******************************************************/

/*!******************************************************!*\
        !*** ./packages/@logrocket/utils/src/getCssRules.ts ***!
        \******************************************************/

/*!******************************************************!*\
        !*** ./packages/@logrocket/utils/src/interpolate.js ***!
        \******************************************************/

/*!******************************************************!*\
        !*** ./packages/@logrocket/utils/src/protectFunc.js ***!
        \******************************************************/

/*!*******************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/typeof.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./packages/@logrocket/exceptions/src/Capture.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./packages/@logrocket/utils/src/browserUtils.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./packages/@logrocket/utils/src/deepArsonify.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./packages/@logrocket/utils/src/isValidAppID.ts ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./packages/@logrocket/utils/src/likeOperator.js ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./packages/@logrocket/utils/src/requireValue.ts ***!
        \*******************************************************/

/*!*******************************************************!*\
        !*** ./packages/@logrocket/utils/src/setFromArray.js ***!
        \*******************************************************/

/*!********************************************************!*\
        !*** ./packages/@logrocket/network/src/registerXHR.js ***!
        \********************************************************/

/*!********************************************************!*\
        !*** ./packages/@logrocket/utils/src/constants/nps.js ***!
        \********************************************************/

/*!********************************************************!*\
        !*** ./packages/@logrocket/utils/src/findKeyFrames.js ***!
        \********************************************************/

/*!********************************************************!*\
        !*** ./packages/@logrocket/utils/src/sanitizeValue.js ***!
        \********************************************************/

/*!*********************************************************!*\
        !*** ./packages/@logrocket/redux/src/createEnhancer.js ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./packages/@logrocket/utils/src/deepDearsonify.js ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./packages/@logrocket/utils/src/identityStatus.ts ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./packages/@logrocket/utils/src/isSessionEvent.js ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./packages/@logrocket/utils/src/removeOutdated.js ***!
        \*********************************************************/

/*!*********************************************************!*\
        !*** ./packages/@logrocket/utils/src/scrubException.ts ***!
        \*********************************************************/

/*!**********************************************************!*\
        !*** ./packages/@logrocket/network/src/registerFetch.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./packages/@logrocket/network/src/registerIonic.ts ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./packages/@logrocket/utils/src/adjustOsVersion.ts ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./packages/@logrocket/utils/src/getNodeSelector.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./packages/@logrocket/utils/src/isActivityEvent.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./packages/@logrocket/utils/src/makeRecordingID.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./packages/@logrocket/utils/src/parseIntFromHex.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./packages/@logrocket/utils/src/selectorMatches.js ***!
        \**********************************************************/

/*!**********************************************************!*\
        !*** ./packages/@logrocket/utils/src/shallowArsonify.js ***!
        \**********************************************************/

/*!***********************************************************!*\
        !*** ./packages/@logrocket/exceptions/src/raven/raven.js ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./packages/@logrocket/network/src/fetchIntercept.js ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./packages/@logrocket/redux/src/createMiddleware.js ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./packages/@logrocket/utils/src/constants/issues.ts ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./packages/@logrocket/utils/src/constants/mobile.ts ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./packages/@logrocket/utils/src/parseQueryString.js ***!
        \***********************************************************/

/*!***********************************************************!*\
        !*** ./packages/@logrocket/utils/src/userTraitStrings.ts ***!
        \***********************************************************/

/*!************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/createClass.js ***!
        \************************************************************/

/*!************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/toPrimitive.js ***!
        \************************************************************/

/*!************************************************************!*\
        !*** ./packages/@logrocket/console/src/registerConsole.js ***!
        \************************************************************/

/*!************************************************************!*\
        !*** ./packages/@logrocket/utils/src/applyUrlSanitizer.js ***!
        \************************************************************/

/*!************************************************************!*\
        !*** ./packages/@logrocket/utils/src/constants/metrics.js ***!
        \************************************************************/

/*!************************************************************!*\
        !*** ./packages/@logrocket/utils/src/sendTelemetryData.js ***!
        \************************************************************/

/*!************************************************************!*\
        !*** ./packages/@logrocket/utils/src/shallowDearsonify.js ***!
        \************************************************************/

/*!*************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/eq.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./packages/@logrocket/utils/src/constants/logTypes.js ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./packages/@logrocket/utils/src/constants/sdkTypes.ts ***!
        \*************************************************************/

/*!*************************************************************!*\
        !*** ./packages/@logrocket/utils/src/isRecordingSampled.js ***!
        \*************************************************************/

/*!**************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/slicedToArray.js ***!
        \**************************************************************/

/*!**************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/toPropertyKey.js ***!
        \**************************************************************/

/*!**************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/get.js ***!
        \**************************************************************/

/*!**************************************************************!*\
        !*** ./packages/@logrocket/utils/src/getGraphQLOperation.js ***!
        \**************************************************************/

/*!***************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/arrayWithHoles.js ***!
        \***************************************************************/

/*!***************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/classCallCheck.js ***!
        \***************************************************************/

/*!***************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/defineProperty.js ***!
        \***************************************************************/

/*!***************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_Map.js ***!
        \***************************************************************/

/*!***************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_Set.js ***!
        \***************************************************************/

/*!***************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/keys.js ***!
        \***************************************************************/

/*!***************************************************************!*\
        !*** ./packages/@logrocket/utils/src/selectorFromNodePath.js ***!
        \***************************************************************/

/*!****************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/iterableToArray.js ***!
        \****************************************************************/

/*!****************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/nonIterableRest.js ***!
        \****************************************************************/

/*!****************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_Hash.js ***!
        \****************************************************************/

/*!****************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_root.js ***!
        \****************************************************************/

/*!****************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/hasIn.js ***!
        \****************************************************************/

/*!****************************************************************!*\
        !*** ./packages/@logrocket/utils/src/constants/replayTypes.ts ***!
        \****************************************************************/

/*!****************************************************************!*\
        !*** ./packages/@logrocket/utils/src/constants/statusCodes.ts ***!
        \****************************************************************/

/*!****************************************************************!*\
        !*** ./packages/@logrocket/utils/src/parseSelectorForMatch.js ***!
        \****************************************************************/

/*!*****************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/arrayLikeToArray.js ***!
        \*****************************************************************/

/*!*****************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_Stack.js ***!
        \*****************************************************************/

/*!*****************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_isKey.js ***!
        \*****************************************************************/

/*!*****************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_toKey.js ***!
        \*****************************************************************/

/*!******************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js ***!
        \******************************************************************/

/*!******************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/nonIterableSpread.js ***!
        \******************************************************************/

/*!******************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/toConsumableArray.js ***!
        \******************************************************************/

/*!******************************************************************!*\
        !*** ./packages/@logrocket/exceptions/src/registerExceptions.js ***!
        \******************************************************************/

/*!******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_Symbol.js ***!
        \******************************************************************/

/*!******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_getTag.js ***!
        \******************************************************************/

/*!******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isArray.js ***!
        \******************************************************************/

/*!******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/memoize.js ***!
        \******************************************************************/

/*!******************************************************************!*\
        !*** ./packages/@logrocket/utils/src/constants/platformTypes.ts ***!
        \******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/exceptions/src/stackTraceFromError.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_Promise.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_WeakMap.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseGet.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_hasPath.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_hashGet.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_hashHas.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_hashSet.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_isIndex.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_overArg.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/identity.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isBuffer.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isLength.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isObject.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isSymbol.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/property.js ***!
        \*******************************************************************/

/*!*******************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/toString.js ***!
        \*******************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_DataView.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_MapCache.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_SetCache.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_arrayMap.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseKeys.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_cacheHas.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_castPath.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_getValue.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_isMasked.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_nodeUtil.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_stackGet.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_stackHas.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_stackSet.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_toSource.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/stubArray.js ***!
        \********************************************************************/

/*!********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/stubFalse.js ***!
        \********************************************************************/

/*!*********************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/iterableToArrayLimit.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_ListCache.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_arrayPush.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_arraySome.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseHasIn.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseTimes.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseUnary.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_getNative.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_getRawTag.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_hashClear.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_isKeyable.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isFunction.js ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/src/maybeCleanSwiftUIClassName.ts ***!
        \*********************************************************************/

/*!*********************************************************************!*\
        !*** ./packages/@logrocket/utils/src/querySelectorWithShadowDom.js ***!
        \*********************************************************************/

/*!**********************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/interopRequireDefault.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_Uint8Array.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseGetTag.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_coreJsData.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_equalByTag.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_freeGlobal.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_getAllKeys.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_getMapData.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_getSymbols.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_hashDelete.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_mapToArray.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_nativeKeys.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_setToArray.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_stackClear.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isArguments.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isArrayLike.js ***!
        \**********************************************************************/

/*!**********************************************************************!*\
        !*** ./packages/@logrocket/utils/src/scrollMapHistogramToPercent.js ***!
        \**********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/network/src/registerNetworkInformation.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_arrayFilter.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseIsEqual.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseIsMatch.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseMatches.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_equalArrays.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_isPrototype.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_mapCacheGet.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_mapCacheHas.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_mapCacheSet.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_setCacheAdd.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_setCacheHas.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_stackDelete.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isObjectLike.js ***!
        \***********************************************************************/

/*!***********************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/isTypedArray.js ***!
        \***********************************************************************/

/*!************************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/objectWithoutProperties.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_assocIndexOf.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseIsNative.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseIteratee.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseProperty.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseToString.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_equalObjects.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_getMatchData.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_listCacheGet.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_listCacheHas.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_listCacheSet.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_nativeCreate.js ***!
        \************************************************************************/

/*!************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_stringToPath.js ***!
        \************************************************************************/

/*!*************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_arrayLikeKeys.js ***!
        \*************************************************************************/

/*!*************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_mapCacheClear.js ***!
        \*************************************************************************/

/*!*************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_memoizeCapped.js ***!
        \*************************************************************************/

/*!**************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseGetAllKeys.js ***!
        \**************************************************************************/

/*!**************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_listCacheClear.js ***!
        \**************************************************************************/

/*!**************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_mapCacheDelete.js ***!
        \**************************************************************************/

/*!**************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_objectToString.js ***!
        \**************************************************************************/

/*!***************************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js ***!
        \***************************************************************************/

/*!***************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseIsArguments.js ***!
        \***************************************************************************/

/*!***************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseIsEqualDeep.js ***!
        \***************************************************************************/

/*!***************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_listCacheDelete.js ***!
        \***************************************************************************/

/*!****************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseIsTypedArray.js ***!
        \****************************************************************************/

/*!****************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_basePropertyDeep.js ***!
        \****************************************************************************/

/*!****************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/sortedLastIndexBy.js ***!
        \****************************************************************************/

/*!*****************************************************************************!*\
        !*** ./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js ***!
        \*****************************************************************************/

/*!*****************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseSortedIndexBy.js ***!
        \*****************************************************************************/

/*!******************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_isStrictComparable.js ***!
        \******************************************************************************/

/*!*******************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_baseMatchesProperty.js ***!
        \*******************************************************************************/

/*!***********************************************************************************!*\
        !*** ./packages/@logrocket/utils/node_modules/lodash/_matchesStrictComparable.js ***!
        \***********************************************************************************/

/**
 * @license MIT
 * topbar 1.0.0, 2021-01-06
 * http://buunguyen.github.io/topbar
 * Copyright (c) 2021 Buu Nguyen
 */

/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
