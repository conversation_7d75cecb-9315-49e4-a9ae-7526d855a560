{"name": "kpfa_donation_app", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "kpfa_donation_app", "version": "0.1.0", "dependencies": {"@fortawesome/fontawesome-svg-core": "^1.2.34", "@fortawesome/free-brands-svg-icons": "^5.15.3", "@fortawesome/free-solid-svg-icons": "^5.15.2", "@fortawesome/react-fontawesome": "^0.1.16", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@types/jest": "^29.5.2", "@types/node": "^18.16.2", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "bootstrap": "4.6.1", "comma-number": "^2.0.1", "formik": "^2.4.2", "iso-3166-1-alpha-2": "^1.0.1", "logrocket": "^8.1.0", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "^5.0.1", "react-text-mask": "^5.5.0", "reactstrap": "8.10.1", "topbar": "^1.0.1", "typescript": "^4.9.5", "yup": "^0.32.11"}, "devDependencies": {"@playwright/test": "^1.54.1", "prettier": "^2.8.8"}}, "node_modules/@ampproject/remapping": {"version": "2.2.0", "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.1.0", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@ampproject/remapping/node_modules/@jridgewell/gen-mapping": {"version": "0.1.1", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.24.2", "license": "MIT", "dependencies": {"@babel/highlight": "^7.24.2", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.24.4", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.24.5", "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.24.2", "@babel/generator": "^7.24.5", "@babel/helper-compilation-targets": "^7.23.6", "@babel/helper-module-transforms": "^7.24.5", "@babel/helpers": "^7.24.5", "@babel/parser": "^7.24.5", "@babel/template": "^7.24.0", "@babel/traverse": "^7.24.5", "@babel/types": "^7.24.5", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/convert-source-map": {"version": "2.0.0", "license": "MIT"}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/eslint-parser": {"version": "7.19.1", "license": "MIT", "dependencies": {"@nicolo-ribaudo/eslint-scope-5-internals": "5.1.1-v1", "eslint-visitor-keys": "^2.1.0", "semver": "^6.3.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || >=14.0.0"}, "peerDependencies": {"@babel/core": ">=7.11.0", "eslint": "^7.5.0 || ^8.0.0"}}, "node_modules/@babel/eslint-parser/node_modules/eslint-visitor-keys": {"version": "2.1.0", "license": "Apache-2.0", "engines": {"node": ">=10"}}, "node_modules/@babel/generator": {"version": "7.24.5", "license": "MIT", "dependencies": {"@babel/types": "^7.24.5", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^2.5.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/generator/node_modules/jsesc": {"version": "2.5.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.22.5", "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-explode-assignable-expression": "^7.18.6", "@babel/types": "^7.18.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.23.6", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.23.5", "@babel/helper-validator-option": "^7.23.5", "browserslist": "^4.22.2", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache": {"version": "5.1.1", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-compilation-targets/node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.20.2", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.19.0", "@babel/helper-member-expression-to-functions": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-replace-supers": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/@babel/helper-member-expression-to-functions": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/types": "^7.18.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/@babel/helper-optimise-call-expression": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/@babel/helper-replace-supers": {"version": "7.19.1", "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-member-expression-to-functions": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/traverse": "^7.19.1", "@babel/types": "^7.19.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.14.5", "regexpu-core": "^4.7.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-environment-visitor": {"version": "7.22.20", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-explode-assignable-expression": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-function-name": {"version": "7.23.0", "license": "MIT", "dependencies": {"@babel/template": "^7.22.15", "@babel/types": "^7.23.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-hoist-variables": {"version": "7.22.5", "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.24.3", "license": "MIT", "dependencies": {"@babel/types": "^7.24.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.24.5", "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-module-imports": "^7.24.3", "@babel/helper-simple-access": "^7.24.5", "@babel/helper-split-export-declaration": "^7.24.5", "@babel/helper-validator-identifier": "^7.24.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.24.5", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-simple-access": {"version": "7.24.5", "license": "MIT", "dependencies": {"@babel/types": "^7.24.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-split-export-declaration": {"version": "7.24.5", "license": "MIT", "dependencies": {"@babel/types": "^7.24.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.24.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.24.5", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.23.5", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.24.5", "license": "MIT", "dependencies": {"@babel/template": "^7.24.0", "@babel/traverse": "^7.24.5", "@babel/types": "^7.24.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.24.5", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.24.5", "chalk": "^2.4.2", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/@babel/highlight/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/@babel/highlight/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@babel/highlight/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/parser": {"version": "7.24.5", "license": "MIT", "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.18.9", "@babel/plugin-proposal-optional-chaining": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.13.0"}}, "node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.20.0", "license": "MIT", "dependencies": {"@babel/types": "^7.20.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-proposal-async-generator-functions": {"version": "7.20.1", "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-remap-async-to-generator": "^7.18.9", "@babel/plugin-syntax-async-generators": "^7.8.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-async-generator-functions/node_modules/@babel/helper-remap-async-to-generator": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-wrap-function": "^7.18.9", "@babel/types": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-proposal-async-generator-functions/node_modules/@babel/helper-wrap-function": {"version": "7.19.0", "license": "MIT", "dependencies": {"@babel/helper-function-name": "^7.19.0", "@babel/template": "^7.18.10", "@babel/traverse": "^7.19.0", "@babel/types": "^7.19.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-proposal-class-properties": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-class-static-block": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.12.0"}}, "node_modules/@babel/plugin-proposal-decorators": {"version": "7.20.2", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.20.2", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/plugin-syntax-decorators": "^7.19.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-decorators/node_modules/@babel/helper-member-expression-to-functions": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/types": "^7.18.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-proposal-decorators/node_modules/@babel/helper-optimise-call-expression": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-proposal-decorators/node_modules/@babel/helper-replace-supers": {"version": "7.19.1", "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-member-expression-to-functions": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/traverse": "^7.19.1", "@babel/types": "^7.19.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-proposal-dynamic-import": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-export-namespace-from": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-json-strings": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-json-strings": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-logical-assignment-operators": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-nullish-coalescing-operator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-numeric-separator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-object-rest-spread": {"version": "7.20.2", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.20.1", "@babel/helper-compilation-targets": "^7.20.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-transform-parameters": "^7.20.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-object-rest-spread/node_modules/@babel/plugin-transform-parameters": {"version": "7.20.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-optional-catch-binding": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-optional-chaining": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.18.9", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-optional-chaining/node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.20.0", "license": "MIT", "dependencies": {"@babel/types": "^7.20.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-proposal-private-methods": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-private-property-in-object": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-unicode-property-regex": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-decorators": {"version": "7.19.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-export-namespace-from": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-flow": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-assertions": {"version": "7.20.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.24.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.20.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-remap-async-to-generator": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator/node_modules/@babel/helper-remap-async-to-generator": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-wrap-function": "^7.18.9", "@babel/types": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-async-to-generator/node_modules/@babel/helper-wrap-function": {"version": "7.19.0", "license": "MIT", "dependencies": {"@babel/helper-function-name": "^7.19.0", "@babel/template": "^7.18.10", "@babel/traverse": "^7.19.0", "@babel/types": "^7.19.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.20.2", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-classes": {"version": "7.20.2", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-compilation-targets": "^7.20.0", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.19.0", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-classes/node_modules/@babel/helper-member-expression-to-functions": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/types": "^7.18.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-classes/node_modules/@babel/helper-optimise-call-expression": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-classes/node_modules/@babel/helper-replace-supers": {"version": "7.19.1", "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-member-expression-to-functions": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/traverse": "^7.19.1", "@babel/types": "^7.19.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.20.2", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-flow-strip-types": {"version": "7.19.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/plugin-syntax-flow": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.18.8", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.18.9", "@babel/helper-function-name": "^7.18.9", "@babel/helper-plugin-utils": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-member-expression-literals": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-amd": {"version": "7.19.6", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.19.6", "@babel/helper-plugin-utils": "^7.19.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.19.6", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.19.6", "@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-simple-access": "^7.19.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.19.6", "license": "MIT", "dependencies": {"@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.19.6", "@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-validator-identifier": "^7.19.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-umd": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.19.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.19.0", "@babel/helper-plugin-utils": "^7.19.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex/node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.19.0", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "regexpu-core": "^5.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex/node_modules/regenerate-unicode-properties": {"version": "10.1.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex/node_modules/regexpu-core": {"version": "5.2.2", "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.1.0", "regjsgen": "^0.7.1", "regjsparser": "^0.9.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex/node_modules/regjsgen": {"version": "0.7.1", "license": "MIT"}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex/node_modules/regjsparser": {"version": "0.9.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~0.5.0"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex/node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex/node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex/node_modules/unicode-match-property-value-ecmascript": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex/node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-new-target": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-super": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-replace-supers": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-super/node_modules/@babel/helper-member-expression-to-functions": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/types": "^7.18.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-object-super/node_modules/@babel/helper-optimise-call-expression": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-object-super/node_modules/@babel/helper-replace-supers": {"version": "7.19.1", "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-member-expression-to-functions": "^7.18.9", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/traverse": "^7.19.1", "@babel/types": "^7.19.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-property-literals": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-display-name": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx": {"version": "7.23.4", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.23.3", "@babel/types": "^7.23.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-development": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/plugin-transform-react-jsx": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-pure-annotations": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "regenerator-transform": "^0.15.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-reserved-words": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime": {"version": "7.19.6", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.18.6", "@babel/helper-plugin-utils": "^7.19.0", "babel-plugin-polyfill-corejs2": "^0.3.3", "babel-plugin-polyfill-corejs3": "^0.6.0", "babel-plugin-polyfill-regenerator": "^0.4.1", "semver": "^6.3.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.19.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread/node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.20.0", "license": "MIT", "dependencies": {"@babel/types": "^7.20.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typescript": {"version": "7.20.2", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.20.2", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-typescript": "^7.20.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-escapes": {"version": "7.18.10", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex/node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.19.0", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "regexpu-core": "^5.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-unicode-regex/node_modules/regenerate-unicode-properties": {"version": "10.1.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-unicode-regex/node_modules/regexpu-core": {"version": "5.2.2", "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.1.0", "regjsgen": "^0.7.1", "regjsparser": "^0.9.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-unicode-regex/node_modules/regjsgen": {"version": "0.7.1", "license": "MIT"}, "node_modules/@babel/plugin-transform-unicode-regex/node_modules/regjsparser": {"version": "0.9.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~0.5.0"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/@babel/plugin-transform-unicode-regex/node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-unicode-regex/node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-unicode-regex/node_modules/unicode-match-property-value-ecmascript": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-transform-unicode-regex/node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/preset-env": {"version": "7.20.2", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.20.1", "@babel/helper-compilation-targets": "^7.20.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-option": "^7.18.6", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.18.6", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.18.9", "@babel/plugin-proposal-async-generator-functions": "^7.20.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-class-static-block": "^7.18.6", "@babel/plugin-proposal-dynamic-import": "^7.18.6", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-json-strings": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@babel/plugin-proposal-optional-catch-binding": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.18.6", "@babel/plugin-proposal-unicode-property-regex": "^7.18.6", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-namespace-from": "^7.8.3", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5", "@babel/plugin-transform-arrow-functions": "^7.18.6", "@babel/plugin-transform-async-to-generator": "^7.18.6", "@babel/plugin-transform-block-scoped-functions": "^7.18.6", "@babel/plugin-transform-block-scoping": "^7.20.2", "@babel/plugin-transform-classes": "^7.20.2", "@babel/plugin-transform-computed-properties": "^7.18.9", "@babel/plugin-transform-destructuring": "^7.20.2", "@babel/plugin-transform-dotall-regex": "^7.18.6", "@babel/plugin-transform-duplicate-keys": "^7.18.9", "@babel/plugin-transform-exponentiation-operator": "^7.18.6", "@babel/plugin-transform-for-of": "^7.18.8", "@babel/plugin-transform-function-name": "^7.18.9", "@babel/plugin-transform-literals": "^7.18.9", "@babel/plugin-transform-member-expression-literals": "^7.18.6", "@babel/plugin-transform-modules-amd": "^7.19.6", "@babel/plugin-transform-modules-commonjs": "^7.19.6", "@babel/plugin-transform-modules-systemjs": "^7.19.6", "@babel/plugin-transform-modules-umd": "^7.18.6", "@babel/plugin-transform-named-capturing-groups-regex": "^7.19.1", "@babel/plugin-transform-new-target": "^7.18.6", "@babel/plugin-transform-object-super": "^7.18.6", "@babel/plugin-transform-parameters": "^7.20.1", "@babel/plugin-transform-property-literals": "^7.18.6", "@babel/plugin-transform-regenerator": "^7.18.6", "@babel/plugin-transform-reserved-words": "^7.18.6", "@babel/plugin-transform-shorthand-properties": "^7.18.6", "@babel/plugin-transform-spread": "^7.19.0", "@babel/plugin-transform-sticky-regex": "^7.18.6", "@babel/plugin-transform-template-literals": "^7.18.9", "@babel/plugin-transform-typeof-symbol": "^7.18.9", "@babel/plugin-transform-unicode-escapes": "^7.18.10", "@babel/plugin-transform-unicode-regex": "^7.18.6", "@babel/preset-modules": "^0.1.5", "@babel/types": "^7.20.2", "babel-plugin-polyfill-corejs2": "^0.3.3", "babel-plugin-polyfill-corejs3": "^0.6.0", "babel-plugin-polyfill-regenerator": "^0.4.1", "core-js-compat": "^3.25.1", "semver": "^6.3.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-env/node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.19.0", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "regexpu-core": "^5.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/preset-env/node_modules/@babel/plugin-proposal-unicode-property-regex": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-env/node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-env/node_modules/@babel/plugin-transform-parameters": {"version": "7.20.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-env/node_modules/core-js-compat": {"version": "3.26.1", "license": "MIT", "dependencies": {"browserslist": "^4.21.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/@babel/preset-env/node_modules/regenerate-unicode-properties": {"version": "10.1.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/@babel/preset-env/node_modules/regexpu-core": {"version": "5.2.2", "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.1.0", "regjsgen": "^0.7.1", "regjsparser": "^0.9.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/preset-env/node_modules/regjsgen": {"version": "0.7.1", "license": "MIT"}, "node_modules/@babel/preset-env/node_modules/regjsparser": {"version": "0.9.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~0.5.0"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/@babel/preset-env/node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/preset-env/node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/preset-env/node_modules/unicode-match-property-value-ecmascript": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/preset-env/node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/preset-modules": {"version": "0.1.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-react": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-validator-option": "^7.18.6", "@babel/plugin-transform-react-display-name": "^7.18.6", "@babel/plugin-transform-react-jsx": "^7.18.6", "@babel/plugin-transform-react-jsx-development": "^7.18.6", "@babel/plugin-transform-react-pure-annotations": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-typescript": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-validator-option": "^7.18.6", "@babel/plugin-transform-typescript": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.14.5", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.13.4"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/runtime-corejs3": {"version": "7.14.5", "license": "MIT", "dependencies": {"core-js-pure": "^3.14.0", "regenerator-runtime": "^0.13.4"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.24.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.23.5", "@babel/parser": "^7.24.0", "@babel/types": "^7.24.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.24.5", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.24.2", "@babel/generator": "^7.24.5", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-function-name": "^7.23.0", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-split-export-declaration": "^7.24.5", "@babel/parser": "^7.24.5", "@babel/types": "^7.24.5", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.24.5", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.24.1", "@babel/helper-validator-identifier": "^7.24.5", "to-fast-properties": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bcoe/v8-coverage": {"version": "0.2.3", "license": "MIT"}, "node_modules/@csstools/normalize.css": {"version": "12.0.0", "license": "CC0-1.0"}, "node_modules/@csstools/postcss-cascade-layers": {"version": "1.1.1", "license": "CC0-1.0", "dependencies": {"@csstools/selector-specificity": "^2.0.2", "postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-color-function": {"version": "1.1.1", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-font-format-keywords": {"version": "1.0.1", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-hwb-function": {"version": "1.0.2", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-ic-unit": {"version": "1.0.1", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-is-pseudo-class": {"version": "2.0.7", "license": "CC0-1.0", "dependencies": {"@csstools/selector-specificity": "^2.0.0", "postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-nested-calc": {"version": "1.0.0", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-normalize-display-values": {"version": "1.0.1", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-oklab-function": {"version": "1.1.1", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-progressive-custom-properties": {"version": "1.3.0", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.3"}}, "node_modules/@csstools/postcss-stepped-value-functions": {"version": "1.0.1", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-text-decoration-shorthand": {"version": "1.0.0", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-trigonometric-functions": {"version": "1.0.2", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-unset-value": {"version": "1.0.2", "license": "CC0-1.0", "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/selector-specificity": {"version": "2.0.2", "license": "CC0-1.0", "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2", "postcss-selector-parser": "^6.0.10"}}, "node_modules/@eslint/eslintrc": {"version": "1.3.3", "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.4.0", "globals": "^13.15.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/@eslint/eslintrc/node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "13.18.0", "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/eslintrc/node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@fortawesome/fontawesome-common-types": {"version": "0.2.36", "hasInstallScript": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@fortawesome/fontawesome-svg-core": {"version": "1.2.36", "hasInstallScript": true, "license": "MIT", "dependencies": {"@fortawesome/fontawesome-common-types": "^0.2.36"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/free-brands-svg-icons": {"version": "5.15.4", "hasInstallScript": true, "license": "(CC-BY-4.0 AND MIT)", "dependencies": {"@fortawesome/fontawesome-common-types": "^0.2.36"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/free-solid-svg-icons": {"version": "5.15.4", "hasInstallScript": true, "license": "(CC-BY-4.0 AND MIT)", "dependencies": {"@fortawesome/fontawesome-common-types": "^0.2.36"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/react-fontawesome": {"version": "0.1.16", "license": "MIT", "dependencies": {"prop-types": "^15.7.2"}, "peerDependencies": {"@fortawesome/fontawesome-svg-core": "~1 || >=1.3.0-beta1", "react": ">=16.x"}}, "node_modules/@fortawesome/react-fontawesome/node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.11.7", "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^1.2.1", "debug": "^4.1.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hypnosphi/create-react-context": {"version": "0.3.1", "license": "MIT", "dependencies": {"gud": "^1.0.0", "warning": "^4.0.3"}, "peerDependencies": {"prop-types": "^15.0.0", "react": ">=0.14.0"}}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase": {"version": "5.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/console": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^27.5.1", "jest-util": "^27.5.1", "slash": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/core": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/console": "^27.5.1", "@jest/reporters": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/transform": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.8.1", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^27.5.1", "jest-config": "^27.5.1", "jest-haste-map": "^27.5.1", "jest-message-util": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-resolve": "^27.5.1", "jest-resolve-dependencies": "^27.5.1", "jest-runner": "^27.5.1", "jest-runtime": "^27.5.1", "jest-snapshot": "^27.5.1", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "jest-watcher": "^27.5.1", "micromatch": "^4.0.4", "rimraf": "^3.0.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/core/node_modules/jest-watcher": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/test-result": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "jest-util": "^27.5.1", "string-length": "^4.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/environment": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/fake-timers": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "jest-mock": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/expect-utils": {"version": "29.3.1", "license": "MIT", "dependencies": {"jest-get-type": "^29.2.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect-utils/node_modules/jest-get-type": {"version": "29.2.0", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/fake-timers": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@sinonjs/fake-timers": "^8.0.1", "@types/node": "*", "jest-message-util": "^27.5.1", "jest-mock": "^27.5.1", "jest-util": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/globals": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/environment": "^27.5.1", "@jest/types": "^27.5.1", "expect": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/reporters": {"version": "27.5.1", "license": "MIT", "dependencies": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/transform": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.2", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^5.1.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-haste-map": "^27.5.1", "jest-resolve": "^27.5.1", "jest-util": "^27.5.1", "jest-worker": "^27.5.1", "slash": "^3.0.0", "source-map": "^0.6.0", "string-length": "^4.0.1", "terminal-link": "^2.0.0", "v8-to-istanbul": "^8.1.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/schemas": {"version": "28.1.3", "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.24.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/@jest/source-map": {"version": "27.5.1", "license": "MIT", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "source-map": "^0.6.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/test-result": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/console": "^27.5.1", "@jest/types": "^27.5.1", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/test-sequencer": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/test-result": "^27.5.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.1", "jest-runtime": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/transform": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/core": "^7.1.0", "@jest/types": "^27.5.1", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^1.4.0", "fast-json-stable-stringify": "^2.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-util": "^27.5.1", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "source-map": "^0.6.1", "write-file-atomic": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/types": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "chalk": "^4.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/types/node_modules/@types/yargs": {"version": "16.0.4", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.5", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.2", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.14", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@leichtgewicht/ip-codec": {"version": "2.0.4", "license": "MIT"}, "node_modules/@nicolo-ribaudo/eslint-scope-5-internals": {"version": "5.1.1-v1", "license": "MIT", "dependencies": {"eslint-scope": "5.1.1"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@playwright/test": {"version": "1.54.1", "resolved": "https://registry.npmjs.org/@playwright/test/-/test-1.54.1.tgz", "integrity": "sha512-FS8hQ12acieG2dYSksmLOF7BNxnVf2afRJdCuM1eMSxj6QTSE6G4InGF7oApGgDb65MX7AwMVlIkpru0yZA4Xw==", "dev": true, "dependencies": {"playwright": "1.54.1"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=18"}}, "node_modules/@pmmmwh/react-refresh-webpack-plugin": {"version": "0.5.10", "license": "MIT", "dependencies": {"ansi-html-community": "^0.0.8", "common-path-prefix": "^3.0.0", "core-js-pure": "^3.23.3", "error-stack-parser": "^2.0.6", "find-up": "^5.0.0", "html-entities": "^2.1.0", "loader-utils": "^2.0.4", "schema-utils": "^3.0.0", "source-map": "^0.7.3"}, "engines": {"node": ">= 10.13"}, "peerDependencies": {"@types/webpack": "4.x || 5.x", "react-refresh": ">=0.10.0 <1.0.0", "sockjs-client": "^1.4.0", "type-fest": ">=0.17.0 <4.0.0", "webpack": ">=4.43.0 <6.0.0", "webpack-dev-server": "3.x || 4.x", "webpack-hot-middleware": "2.x", "webpack-plugin-serve": "0.x || 1.x"}, "peerDependenciesMeta": {"@types/webpack": {"optional": true}, "sockjs-client": {"optional": true}, "type-fest": {"optional": true}, "webpack-dev-server": {"optional": true}, "webpack-hot-middleware": {"optional": true}, "webpack-plugin-serve": {"optional": true}}}, "node_modules/@pmmmwh/react-refresh-webpack-plugin/node_modules/loader-utils": {"version": "2.0.4", "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/@pmmmwh/react-refresh-webpack-plugin/node_modules/schema-utils": {"version": "3.0.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.6", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/@pmmmwh/react-refresh-webpack-plugin/node_modules/source-map": {"version": "0.7.3", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/@rollup/plugin-babel": {"version": "5.3.1", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.10.4", "@rollup/pluginutils": "^3.1.0"}, "engines": {"node": ">= 10.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0", "@types/babel__core": "^7.1.9", "rollup": "^1.20.0||^2.0.0"}, "peerDependenciesMeta": {"@types/babel__core": {"optional": true}}}, "node_modules/@rollup/plugin-node-resolve": {"version": "11.2.1", "license": "MIT", "dependencies": {"@rollup/pluginutils": "^3.1.0", "@types/resolve": "1.17.1", "builtin-modules": "^3.1.0", "deepmerge": "^4.2.2", "is-module": "^1.0.0", "resolve": "^1.19.0"}, "engines": {"node": ">= 10.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0"}}, "node_modules/@rollup/plugin-node-resolve/node_modules/resolve": {"version": "1.22.1", "license": "MIT", "dependencies": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@rollup/plugin-replace": {"version": "2.4.2", "license": "MIT", "dependencies": {"@rollup/pluginutils": "^3.1.0", "magic-string": "^0.25.7"}, "peerDependencies": {"rollup": "^1.20.0 || ^2.0.0"}}, "node_modules/@rollup/pluginutils": {"version": "3.1.0", "license": "MIT", "dependencies": {"@types/estree": "0.0.39", "estree-walker": "^1.0.1", "picomatch": "^2.2.2"}, "engines": {"node": ">= 8.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0"}}, "node_modules/@rollup/pluginutils/node_modules/@types/estree": {"version": "0.0.39", "license": "MIT"}, "node_modules/@rushstack/eslint-patch": {"version": "1.2.0", "license": "MIT"}, "node_modules/@sinclair/typebox": {"version": "0.24.51", "license": "MIT"}, "node_modules/@sinonjs/commons": {"version": "1.8.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"type-detect": "4.0.8"}}, "node_modules/@sinonjs/fake-timers": {"version": "8.1.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^1.7.0"}}, "node_modules/@stripe/react-stripe-js": {"version": "3.7.0", "resolved": "https://registry.npmjs.org/@stripe/react-stripe-js/-/react-stripe-js-3.7.0.tgz", "integrity": "sha512-PYls/2S9l0FF+2n0wHaEJsEU8x7CmBagiH7zYOsxbBlLIHEsqUIQ4MlIAbV9Zg6xwT8jlYdlRIyBTHmO3yM7kQ==", "dependencies": {"prop-types": "^15.7.2"}, "peerDependencies": {"@stripe/stripe-js": ">=1.44.1 <8.0.0", "react": ">=16.8.0 <20.0.0", "react-dom": ">=16.8.0 <20.0.0"}}, "node_modules/@stripe/react-stripe-js/node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/@stripe/stripe-js": {"version": "7.4.0", "resolved": "https://registry.npmjs.org/@stripe/stripe-js/-/stripe-js-7.4.0.tgz", "integrity": "sha512-lQHQPfXPTBeh0XFjq6PqSBAyR7umwcJbvJhXV77uGCUDD6ymXJU/f2164ydLMLCCceNuPlbV9b+1smx98efwWQ==", "engines": {"node": ">=12.16"}}, "node_modules/@surma/rollup-plugin-off-main-thread": {"version": "2.2.3", "license": "Apache-2.0", "dependencies": {"ejs": "^3.1.6", "json5": "^2.2.0", "magic-string": "^0.25.0", "string.prototype.matchall": "^4.0.6"}}, "node_modules/@svgr/babel-plugin-add-jsx-attribute": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/babel-plugin-remove-jsx-attribute": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/babel-plugin-remove-jsx-empty-expression": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/babel-plugin-replace-jsx-attribute-value": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/babel-plugin-svg-dynamic-title": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/babel-plugin-svg-em-dimensions": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/babel-plugin-transform-react-native-svg": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/babel-plugin-transform-svg-component": {"version": "5.5.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/babel-preset": {"version": "5.5.0", "license": "MIT", "dependencies": {"@svgr/babel-plugin-add-jsx-attribute": "^5.4.0", "@svgr/babel-plugin-remove-jsx-attribute": "^5.4.0", "@svgr/babel-plugin-remove-jsx-empty-expression": "^5.0.1", "@svgr/babel-plugin-replace-jsx-attribute-value": "^5.0.1", "@svgr/babel-plugin-svg-dynamic-title": "^5.4.0", "@svgr/babel-plugin-svg-em-dimensions": "^5.4.0", "@svgr/babel-plugin-transform-react-native-svg": "^5.4.0", "@svgr/babel-plugin-transform-svg-component": "^5.5.0"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/core": {"version": "5.5.0", "license": "MIT", "dependencies": {"@svgr/plugin-jsx": "^5.5.0", "camelcase": "^6.2.0", "cosmiconfig": "^7.0.0"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/hast-util-to-babel-ast": {"version": "5.5.0", "license": "MIT", "dependencies": {"@babel/types": "^7.12.6"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/plugin-jsx": {"version": "5.5.0", "license": "MIT", "dependencies": {"@babel/core": "^7.12.3", "@svgr/babel-preset": "^5.5.0", "@svgr/hast-util-to-babel-ast": "^5.5.0", "svg-parser": "^2.0.2"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/plugin-svgo": {"version": "5.5.0", "license": "MIT", "dependencies": {"cosmiconfig": "^7.0.0", "deepmerge": "^4.2.2", "svgo": "^1.2.2"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/webpack": {"version": "5.5.0", "license": "MIT", "dependencies": {"@babel/core": "^7.12.3", "@babel/plugin-transform-react-constant-elements": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/preset-react": "^7.12.5", "@svgr/core": "^5.5.0", "@svgr/plugin-jsx": "^5.5.0", "@svgr/plugin-svgo": "^5.5.0", "loader-utils": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@svgr/webpack/node_modules/@babel/plugin-transform-react-constant-elements": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@tootallnate/once": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/@trysound/sax": {"version": "0.2.0", "license": "ISC", "engines": {"node": ">=10.13.0"}}, "node_modules/@types/babel__core": {"version": "7.1.20", "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.6.2", "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.0", "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.11.1", "license": "MIT", "dependencies": {"@babel/types": "^7.3.0"}}, "node_modules/@types/body-parser": {"version": "1.19.2", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/bonjour": {"version": "3.5.10", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.35", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect-history-api-fallback": {"version": "1.3.5", "license": "MIT", "dependencies": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "node_modules/@types/eslint": {"version": "8.4.10", "license": "MIT", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/@types/eslint-scope": {"version": "3.7.4", "license": "MIT", "dependencies": {"@types/eslint": "*", "@types/estree": "*"}}, "node_modules/@types/estree": {"version": "0.0.48", "license": "MIT"}, "node_modules/@types/express": {"version": "4.17.14", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.17.31", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "node_modules/@types/graceful-fs": {"version": "4.1.5", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/html-minifier-terser": {"version": "6.1.0", "license": "MIT"}, "node_modules/@types/http-proxy": {"version": "1.17.9", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.3", "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.0", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.1", "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/jest": {"version": "29.5.2", "license": "MIT", "dependencies": {"expect": "^29.0.0", "pretty-format": "^29.0.0"}}, "node_modules/@types/jest/node_modules/@jest/schemas": {"version": "29.0.0", "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.24.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/@jest/types": {"version": "29.3.1", "license": "MIT", "dependencies": {"@jest/schemas": "^29.0.0", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@types/jest/node_modules/diff-sequences": {"version": "29.3.1", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/expect": {"version": "29.3.1", "license": "MIT", "dependencies": {"@jest/expect-utils": "^29.3.1", "jest-get-type": "^29.2.0", "jest-matcher-utils": "^29.3.1", "jest-message-util": "^29.3.1", "jest-util": "^29.3.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-diff": {"version": "29.3.1", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.3.1", "jest-get-type": "^29.2.0", "pretty-format": "^29.3.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-get-type": {"version": "29.2.0", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-matcher-utils": {"version": "29.3.1", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.3.1", "jest-get-type": "^29.2.0", "pretty-format": "^29.3.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-message-util": {"version": "29.3.1", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.3.1", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.3.1", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/jest-util": {"version": "29.3.1", "license": "MIT", "dependencies": {"@jest/types": "^29.3.1", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/pretty-format": {"version": "29.3.1", "license": "MIT", "dependencies": {"@jest/schemas": "^29.0.0", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@types/jest/node_modules/react-is": {"version": "18.2.0", "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.11", "license": "MIT"}, "node_modules/@types/json5": {"version": "0.0.29", "license": "MIT"}, "node_modules/@types/lodash": {"version": "4.14.176", "license": "MIT"}, "node_modules/@types/mime": {"version": "3.0.1", "license": "MIT"}, "node_modules/@types/node": {"version": "18.16.2", "license": "MIT"}, "node_modules/@types/parse-json": {"version": "4.0.0", "license": "MIT"}, "node_modules/@types/prettier": {"version": "2.7.1", "license": "MIT"}, "node_modules/@types/prop-types": {"version": "15.7.5", "license": "MIT"}, "node_modules/@types/q": {"version": "1.5.4", "license": "MIT"}, "node_modules/@types/qs": {"version": "6.9.7", "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.4", "license": "MIT"}, "node_modules/@types/react": {"version": "18.2.14", "license": "MIT", "dependencies": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "18.2.6", "license": "MIT", "dependencies": {"@types/react": "*"}}, "node_modules/@types/resolve": {"version": "1.17.1", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/retry": {"version": "0.12.0", "license": "MIT"}, "node_modules/@types/scheduler": {"version": "0.16.2", "license": "MIT"}, "node_modules/@types/semver": {"version": "7.3.13", "license": "MIT"}, "node_modules/@types/serve-index": {"version": "1.9.1", "license": "MIT", "dependencies": {"@types/express": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.0", "license": "MIT", "dependencies": {"@types/mime": "*", "@types/node": "*"}}, "node_modules/@types/sockjs": {"version": "0.3.33", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/stack-utils": {"version": "2.0.0", "license": "MIT"}, "node_modules/@types/trusted-types": {"version": "2.0.2", "license": "MIT"}, "node_modules/@types/ws": {"version": "8.5.3", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/yargs": {"version": "17.0.14", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "20.2.0", "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "5.44.0", "license": "MIT", "dependencies": {"@typescript-eslint/scope-manager": "5.44.0", "@typescript-eslint/type-utils": "5.44.0", "@typescript-eslint/utils": "5.44.0", "debug": "^4.3.4", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "regexpp": "^3.2.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/semver": {"version": "7.3.8", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/experimental-utils": {"version": "5.44.0", "license": "MIT", "dependencies": {"@typescript-eslint/utils": "5.44.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@typescript-eslint/parser": {"version": "5.44.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "5.44.0", "@typescript-eslint/types": "5.44.0", "@typescript-eslint/typescript-estree": "5.44.0", "debug": "^4.3.4"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser/node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "5.44.0", "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.44.0", "@typescript-eslint/visitor-keys": "5.44.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "5.44.0", "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "5.44.0", "@typescript-eslint/utils": "5.44.0", "debug": "^4.3.4", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/type-utils/node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "5.44.0", "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "5.44.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.44.0", "@typescript-eslint/visitor-keys": "5.44.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/semver": {"version": "7.3.8", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/utils": {"version": "5.44.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.44.0", "@typescript-eslint/types": "5.44.0", "@typescript-eslint/typescript-estree": "5.44.0", "eslint-scope": "^5.1.1", "eslint-utils": "^3.0.0", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@typescript-eslint/utils/node_modules/semver": {"version": "7.3.8", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "5.44.0", "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.44.0", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@webassemblyjs/ast": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.11.1", "license": "MIT"}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.11.1", "license": "MIT"}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.11.1", "license": "MIT"}, "node_modules/@webassemblyjs/helper-numbers": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.11.1", "license": "MIT"}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.11.1", "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.11.1", "license": "Apache-2.0", "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.11.1", "license": "MIT"}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/helper-wasm-section": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-opt": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "@webassemblyjs/wast-printer": "1.11.1"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@xtuc/long": "4.2.2"}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@xtuc/long": {"version": "4.2.2", "license": "Apache-2.0"}, "node_modules/abab": {"version": "2.0.5", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/accepts": {"version": "1.3.7", "license": "MIT", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.8.1", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-globals": {"version": "6.0.0", "license": "MIT", "dependencies": {"acorn": "^7.1.1", "acorn-walk": "^7.1.1"}}, "node_modules/acorn-globals/node_modules/acorn": {"version": "7.4.1", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-import-assertions": {"version": "1.8.0", "license": "MIT", "peerDependencies": {"acorn": "^8"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-node": {"version": "1.8.2", "license": "Apache-2.0", "dependencies": {"acorn": "^7.0.0", "acorn-walk": "^7.0.0", "xtend": "^4.0.2"}}, "node_modules/acorn-node/node_modules/acorn": {"version": "7.4.1", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-walk": {"version": "7.2.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/address": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/adjust-sourcemap-loader": {"version": "4.0.0", "license": "MIT", "dependencies": {"loader-utils": "^2.0.0", "regex-parser": "^2.2.11"}, "engines": {"node": ">=8.9"}}, "node_modules/agent-base": {"version": "6.0.2", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-keywords": {"version": "3.5.2", "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-escapes/node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-html-community": {"version": "0.0.8", "engines": ["node >= 0.8.0"], "license": "Apache-2.0", "bin": {"ansi-html": "bin/ansi-html"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arg": {"version": "5.0.2", "license": "MIT"}, "node_modules/argparse": {"version": "1.0.10", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/aria-query": {"version": "4.2.2", "license": "Apache-2.0", "dependencies": {"@babel/runtime": "^7.10.2", "@babel/runtime-corejs3": "^7.10.2"}, "engines": {"node": ">=6.0"}}, "node_modules/array-flatten": {"version": "1.1.1", "license": "MIT"}, "node_modules/array-includes": {"version": "3.1.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "get-intrinsic": "^1.1.3", "is-string": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes/node_modules/get-intrinsic": {"version": "1.1.3", "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes/node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes/node_modules/is-string": {"version": "1.0.7", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-union": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/array.prototype.flat": {"version": "1.3.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.tosorted": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0", "get-intrinsic": "^1.1.3"}}, "node_modules/array.prototype.tosorted/node_modules/get-intrinsic": {"version": "1.1.3", "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.tosorted/node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/asap": {"version": "2.0.6", "license": "MIT"}, "node_modules/ast-types-flow": {"version": "0.0.7", "license": "ISC"}, "node_modules/async": {"version": "3.2.4", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/autoprefixer": {"version": "10.4.13", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}], "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "caniuse-lite": "^1.0.30001426", "fraction.js": "^4.2.0", "normalize-range": "^0.1.2", "picocolors": "^1.0.0", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/axe-core": {"version": "4.5.2", "license": "MPL-2.0", "engines": {"node": ">=4"}}, "node_modules/axobject-query": {"version": "2.2.0", "license": "Apache-2.0"}, "node_modules/babel-jest": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/transform": "^27.5.1", "@jest/types": "^27.5.1", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^27.5.1", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "node_modules/babel-loader": {"version": "8.3.0", "license": "MIT", "dependencies": {"find-cache-dir": "^3.3.1", "loader-utils": "^2.0.0", "make-dir": "^3.1.0", "schema-utils": "^2.6.5"}, "engines": {"node": ">= 8.9"}, "peerDependencies": {"@babel/core": "^7.0.0", "webpack": ">=2"}}, "node_modules/babel-loader/node_modules/schema-utils": {"version": "2.7.1", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-jest-hoist": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/babel-plugin-macros": {"version": "3.1.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}, "engines": {"node": ">=10", "npm": ">=6"}}, "node_modules/babel-plugin-macros/node_modules/resolve": {"version": "1.22.1", "license": "MIT", "dependencies": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/babel-plugin-named-asset-import": {"version": "0.3.8", "license": "MIT", "peerDependencies": {"@babel/core": "^7.1.0"}}, "node_modules/babel-plugin-polyfill-corejs2": {"version": "0.3.3", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.3.3", "semver": "^6.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/babel-plugin-polyfill-corejs2/node_modules/@babel/helper-define-polyfill-provider": {"version": "0.3.3", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.17.7", "@babel/helper-plugin-utils": "^7.16.7", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2", "semver": "^6.1.2"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}}, "node_modules/babel-plugin-polyfill-corejs3": {"version": "0.6.0", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.3.3", "core-js-compat": "^3.25.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/babel-plugin-polyfill-corejs3/node_modules/@babel/helper-define-polyfill-provider": {"version": "0.3.3", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.17.7", "@babel/helper-plugin-utils": "^7.16.7", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2", "semver": "^6.1.2"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}}, "node_modules/babel-plugin-polyfill-corejs3/node_modules/core-js-compat": {"version": "3.26.1", "license": "MIT", "dependencies": {"browserslist": "^4.21.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/babel-plugin-polyfill-regenerator": {"version": "0.4.1", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.3.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/babel-plugin-polyfill-regenerator/node_modules/@babel/helper-define-polyfill-provider": {"version": "0.3.3", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.17.7", "@babel/helper-plugin-utils": "^7.16.7", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2", "semver": "^6.1.2"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}}, "node_modules/babel-plugin-transform-react-remove-prop-types": {"version": "0.4.24", "license": "MIT"}, "node_modules/babel-preset-current-node-syntax": {"version": "1.0.1", "license": "MIT", "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-top-level-await": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/babel-preset-jest": {"version": "27.5.1", "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^27.5.1", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/babel-preset-react-app": {"version": "10.0.1", "license": "MIT", "dependencies": {"@babel/core": "^7.16.0", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-proposal-decorators": "^7.16.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-numeric-separator": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/plugin-proposal-private-methods": "^7.16.0", "@babel/plugin-transform-flow-strip-types": "^7.16.0", "@babel/plugin-transform-react-display-name": "^7.16.0", "@babel/plugin-transform-runtime": "^7.16.4", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.0", "@babel/runtime": "^7.16.3", "babel-plugin-macros": "^3.1.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24"}}, "node_modules/babel-preset-react-app/node_modules/@babel/runtime": {"version": "7.20.1", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.13.10"}, "engines": {"node": ">=6.9.0"}}, "node_modules/babel-preset-react-app/node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT"}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/batch": {"version": "0.6.1", "license": "MIT"}, "node_modules/bfj": {"version": "7.0.2", "license": "MIT", "dependencies": {"bluebird": "^3.5.5", "check-types": "^11.1.1", "hoopy": "^0.1.4", "tryer": "^1.0.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/big.js": {"version": "5.2.2", "license": "MIT", "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/bluebird": {"version": "3.7.2", "license": "MIT"}, "node_modules/body-parser": {"version": "1.20.1", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.11.0", "raw-body": "2.5.1", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/bonjour-service": {"version": "1.0.14", "license": "MIT", "dependencies": {"array-flatten": "^2.1.2", "dns-equal": "^1.0.0", "fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.5"}}, "node_modules/bonjour-service/node_modules/array-flatten": {"version": "2.1.2", "license": "MIT"}, "node_modules/boolbase": {"version": "1.0.0", "license": "ISC"}, "node_modules/bootstrap": {"version": "4.6.1", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}, "peerDependencies": {"jquery": "1.9.1 - 3", "popper.js": "^1.16.1"}}, "node_modules/brace-expansion": {"version": "1.1.11", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/browser-process-hrtime": {"version": "1.0.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/browserslist": {"version": "4.23.0", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001587", "electron-to-chromium": "^1.4.668", "node-releases": "^2.0.14", "update-browserslist-db": "^1.0.13"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bser": {"version": "2.1.1", "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/buffer-from": {"version": "1.1.1", "license": "MIT"}, "node_modules/builtin-modules": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind": {"version": "1.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camel-case": {"version": "4.1.2", "license": "MIT", "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/camelcase": {"version": "6.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/camelcase-css": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/caniuse-api": {"version": "3.0.0", "license": "MIT", "dependencies": {"browserslist": "^4.0.0", "caniuse-lite": "^1.0.0", "lodash.memoize": "^4.1.2", "lodash.uniq": "^4.5.0"}}, "node_modules/caniuse-lite": {"version": "1.0.30001620", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/case-sensitive-paths-webpack-plugin": {"version": "2.4.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/chalk": {"version": "4.1.1", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/char-regex": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/check-types": {"version": "11.1.2", "license": "MIT"}, "node_modules/chokidar": {"version": "3.5.3", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/chrome-trace-event": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">=6.0"}}, "node_modules/ci-info": {"version": "3.7.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cjs-module-lexer": {"version": "1.2.2", "license": "MIT"}, "node_modules/classnames": {"version": "2.3.1", "license": "MIT"}, "node_modules/clean-css": {"version": "5.3.1", "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 10.0"}}, "node_modules/cliui": {"version": "7.0.4", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/co": {"version": "4.6.0", "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/coa": {"version": "2.0.2", "license": "MIT", "dependencies": {"@types/q": "^1.5.1", "chalk": "^2.4.1", "q": "^1.1.2"}, "engines": {"node": ">= 4.0"}}, "node_modules/coa/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/coa/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/coa/node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/coa/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/coa/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/coa/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/coa/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/collect-v8-coverage": {"version": "1.0.1", "license": "MIT"}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/colord": {"version": "2.9.3", "license": "MIT"}, "node_modules/colorette": {"version": "2.0.19", "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/comma-number": {"version": "2.1.0", "license": "MIT"}, "node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "node_modules/common-path-prefix": {"version": "3.0.0", "license": "ISC"}, "node_modules/common-tags": {"version": "1.8.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/commondir": {"version": "1.0.1", "license": "MIT"}, "node_modules/compressible": {"version": "2.0.18", "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.7.4", "license": "MIT", "dependencies": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.16", "debug": "2.6.9", "on-headers": "~1.0.2", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/bytes": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/compression/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/confusing-browser-globals": {"version": "1.0.11", "license": "MIT"}, "node_modules/connect-history-api-fallback": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/content-disposition": {"version": "0.5.4", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "1.7.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.1"}}, "node_modules/convert-source-map/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/cookie": {"version": "0.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "license": "MIT"}, "node_modules/core-js": {"version": "3.26.1", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-js-pure": {"version": "3.26.1", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.2", "license": "MIT"}, "node_modules/cosmiconfig": {"version": "7.0.0", "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "node_modules/cross-spawn": {"version": "7.0.3", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypto-random-string": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/css-blank-pseudo": {"version": "3.0.3", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "bin": {"css-blank-pseudo": "dist/cli.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/css-declaration-sorter": {"version": "6.3.1", "license": "ISC", "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.0.9"}}, "node_modules/css-has-pseudo": {"version": "3.0.4", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "bin": {"css-has-pseudo": "dist/cli.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/css-loader": {"version": "6.7.2", "license": "MIT", "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.4.18", "postcss-modules-extract-imports": "^3.0.0", "postcss-modules-local-by-default": "^4.0.0", "postcss-modules-scope": "^3.0.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.2.0", "semver": "^7.3.8"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/css-loader/node_modules/semver": {"version": "7.3.8", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/css-minimizer-webpack-plugin": {"version": "3.4.1", "license": "MIT", "dependencies": {"cssnano": "^5.0.6", "jest-worker": "^27.0.2", "postcss": "^8.3.5", "schema-utils": "^4.0.0", "serialize-javascript": "^6.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}, "peerDependenciesMeta": {"@parcel/css": {"optional": true}, "clean-css": {"optional": true}, "csso": {"optional": true}, "esbuild": {"optional": true}}}, "node_modules/css-prefers-color-scheme": {"version": "6.0.3", "license": "CC0-1.0", "bin": {"css-prefers-color-scheme": "dist/cli.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/css-select": {"version": "4.1.3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.0.0", "domhandler": "^4.2.0", "domutils": "^2.6.0", "nth-check": "^2.0.0"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-select-base-adapter": {"version": "0.1.1", "license": "MIT"}, "node_modules/css-tree": {"version": "1.1.3", "license": "MIT", "dependencies": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/css-what": {"version": "5.0.1", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/cssdb": {"version": "7.1.0", "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}}, "node_modules/cssesc": {"version": "3.0.0", "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/cssnano": {"version": "5.1.14", "license": "MIT", "dependencies": {"cssnano-preset-default": "^5.2.13", "lilconfig": "^2.0.3", "yaml": "^1.10.2"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/cssnano"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/cssnano-preset-default": {"version": "5.2.13", "license": "MIT", "dependencies": {"css-declaration-sorter": "^6.3.1", "cssnano-utils": "^3.1.0", "postcss-calc": "^8.2.3", "postcss-colormin": "^5.3.0", "postcss-convert-values": "^5.1.3", "postcss-discard-comments": "^5.1.2", "postcss-discard-duplicates": "^5.1.0", "postcss-discard-empty": "^5.1.1", "postcss-discard-overridden": "^5.1.0", "postcss-merge-longhand": "^5.1.7", "postcss-merge-rules": "^5.1.3", "postcss-minify-font-values": "^5.1.0", "postcss-minify-gradients": "^5.1.1", "postcss-minify-params": "^5.1.4", "postcss-minify-selectors": "^5.2.1", "postcss-normalize-charset": "^5.1.0", "postcss-normalize-display-values": "^5.1.0", "postcss-normalize-positions": "^5.1.1", "postcss-normalize-repeat-style": "^5.1.1", "postcss-normalize-string": "^5.1.0", "postcss-normalize-timing-functions": "^5.1.0", "postcss-normalize-unicode": "^5.1.1", "postcss-normalize-url": "^5.1.0", "postcss-normalize-whitespace": "^5.1.1", "postcss-ordered-values": "^5.1.3", "postcss-reduce-initial": "^5.1.1", "postcss-reduce-transforms": "^5.1.0", "postcss-svgo": "^5.1.0", "postcss-unique-selectors": "^5.1.1"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/cssnano-utils": {"version": "3.1.0", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/csso": {"version": "4.2.0", "license": "MIT", "dependencies": {"css-tree": "^1.1.2"}, "engines": {"node": ">=8.0.0"}}, "node_modules/cssom": {"version": "0.4.4", "license": "MIT"}, "node_modules/cssstyle": {"version": "2.3.0", "license": "MIT", "dependencies": {"cssom": "~0.3.6"}, "engines": {"node": ">=8"}}, "node_modules/cssstyle/node_modules/cssom": {"version": "0.3.8", "license": "MIT"}, "node_modules/csstype": {"version": "3.1.1", "license": "MIT"}, "node_modules/damerau-levenshtein": {"version": "1.0.8", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/data-urls": {"version": "2.0.0", "license": "MIT", "dependencies": {"abab": "^2.0.3", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.0.0"}, "engines": {"node": ">=10"}}, "node_modules/debug": {"version": "4.3.1", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js": {"version": "10.2.1", "license": "MIT"}, "node_modules/dedent": {"version": "0.7.0", "license": "MIT"}, "node_modules/deep-equal": {"version": "1.1.1", "license": "MIT", "dependencies": {"is-arguments": "^1.0.4", "is-date-object": "^1.0.1", "is-regex": "^1.0.4", "object-is": "^1.0.1", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.2.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/deep-is": {"version": "0.1.3", "license": "MIT"}, "node_modules/deepmerge": {"version": "4.2.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/default-gateway": {"version": "6.0.3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/define-properties": {"version": "1.1.4", "license": "MIT", "dependencies": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/defined": {"version": "1.0.1", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-newline": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/detect-node": {"version": "2.1.0", "license": "MIT"}, "node_modules/detect-port-alt": {"version": "1.1.6", "license": "MIT", "dependencies": {"address": "^1.0.1", "debug": "^2.6.0"}, "bin": {"detect": "bin/detect-port", "detect-port": "bin/detect-port"}, "engines": {"node": ">= 4.2.1"}}, "node_modules/detect-port-alt/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/detect-port-alt/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/detective": {"version": "5.2.1", "license": "MIT", "dependencies": {"acorn-node": "^1.8.2", "defined": "^1.0.0", "minimist": "^1.2.6"}, "bin": {"detective": "bin/detective.js"}, "engines": {"node": ">=0.8.0"}}, "node_modules/detective/node_modules/minimist": {"version": "1.2.7", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/didyoumean": {"version": "1.2.2", "license": "Apache-2.0"}, "node_modules/diff-sequences": {"version": "27.5.1", "license": "MIT", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/dir-glob": {"version": "3.0.1", "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dlv": {"version": "1.1.3", "license": "MIT"}, "node_modules/dns-equal": {"version": "1.0.0", "license": "MIT"}, "node_modules/dns-packet": {"version": "5.4.0", "license": "MIT", "dependencies": {"@leichtgewicht/ip-codec": "^2.0.1"}, "engines": {"node": ">=6"}}, "node_modules/doctrine": {"version": "2.1.0", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/dom-converter": {"version": "0.2.0", "license": "MIT", "dependencies": {"utila": "~0.4"}}, "node_modules/dom-helpers": {"version": "3.4.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.1.2"}}, "node_modules/dom-serializer": {"version": "1.3.2", "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/domelementtype": {"version": "2.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domexception": {"version": "2.0.1", "license": "MIT", "dependencies": {"webidl-conversions": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/domexception/node_modules/webidl-conversions": {"version": "5.0.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/domhandler": {"version": "4.2.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "2.7.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dot-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/dotenv": {"version": "10.0.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/dotenv-expand": {"version": "5.1.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/duplexer": {"version": "0.1.2", "license": "MIT"}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/ejs": {"version": "3.1.8", "license": "Apache-2.0", "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/electron-to-chromium": {"version": "1.4.777", "license": "ISC"}, "node_modules/emittery": {"version": "0.8.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/emojis-list": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/enhanced-resolve": {"version": "5.12.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/entities": {"version": "2.2.0", "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/error-ex": {"version": "1.3.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/error-stack-parser": {"version": "2.0.6", "license": "MIT", "dependencies": {"stackframe": "^1.1.1"}}, "node_modules/es-abstract": {"version": "1.20.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.3", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.7", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "safe-regex-test": "^1.0.0", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-abstract/node_modules/get-intrinsic": {"version": "1.1.3", "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-abstract/node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-abstract/node_modules/is-callable": {"version": "1.2.7", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-abstract/node_modules/is-regex": {"version": "1.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-abstract/node_modules/is-string": {"version": "1.0.7", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-abstract/node_modules/object-inspect": {"version": "1.12.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-abstract/node_modules/object.assign": {"version": "4.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-module-lexer": {"version": "0.9.3", "license": "MIT"}, "node_modules/es-shim-unscopables": {"version": "1.0.0", "license": "MIT", "dependencies": {"has": "^1.0.3"}}, "node_modules/es-to-primitive": {"version": "1.2.1", "license": "MIT", "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/escalade": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/escodegen": {"version": "2.0.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/escodegen/node_modules/levn": {"version": "0.3.0", "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/escodegen/node_modules/optionator": {"version": "0.8.3", "license": "MIT", "dependencies": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/eslint": {"version": "8.28.0", "license": "MIT", "dependencies": {"@eslint/eslintrc": "^1.3.3", "@humanwhocodes/config-array": "^0.11.6", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.1.1", "eslint-utils": "^3.0.0", "eslint-visitor-keys": "^3.3.0", "espree": "^9.4.0", "esquery": "^1.4.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.15.0", "grapheme-splitter": "^1.0.4", "ignore": "^5.2.0", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-sdsl": "^4.1.4", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "regexpp": "^3.2.0", "strip-ansi": "^6.0.1", "strip-json-comments": "^3.1.0", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-react-app": {"version": "7.0.1", "license": "MIT", "dependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@rushstack/eslint-patch": "^1.1.0", "@typescript-eslint/eslint-plugin": "^5.5.0", "@typescript-eslint/parser": "^5.5.0", "babel-preset-react-app": "^10.0.1", "confusing-browser-globals": "^1.0.11", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jest": "^25.3.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.27.1", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-testing-library": "^5.0.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"eslint": "^8.0.0"}}, "node_modules/eslint-import-resolver-node": {"version": "0.3.6", "license": "MIT", "dependencies": {"debug": "^3.2.7", "resolve": "^1.20.0"}}, "node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-import-resolver-node/node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/eslint-module-utils": {"version": "2.7.4", "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-module-utils/node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/eslint-plugin-flowtype": {"version": "8.0.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"lodash": "^4.17.21", "string-natural-compare": "^3.0.1"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"@babel/plugin-syntax-flow": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.14.9", "eslint": "^8.1.0"}}, "node_modules/eslint-plugin-import": {"version": "2.26.0", "license": "MIT", "dependencies": {"array-includes": "^3.1.4", "array.prototype.flat": "^1.2.5", "debug": "^2.6.9", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.6", "eslint-module-utils": "^2.7.3", "has": "^1.0.3", "is-core-module": "^2.8.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.values": "^1.1.5", "resolve": "^1.22.0", "tsconfig-paths": "^3.14.1"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8"}}, "node_modules/eslint-plugin-import/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/eslint-plugin-import/node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint-plugin-import/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/eslint-plugin-import/node_modules/resolve": {"version": "1.22.1", "license": "MIT", "dependencies": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/eslint-plugin-jest": {"version": "25.7.0", "license": "MIT", "dependencies": {"@typescript-eslint/experimental-utils": "^5.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "peerDependencies": {"@typescript-eslint/eslint-plugin": "^4.0.0 || ^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"@typescript-eslint/eslint-plugin": {"optional": true}, "jest": {"optional": true}}}, "node_modules/eslint-plugin-jsx-a11y": {"version": "6.6.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.9", "aria-query": "^4.2.2", "array-includes": "^3.1.5", "ast-types-flow": "^0.0.7", "axe-core": "^4.4.3", "axobject-query": "^2.2.0", "damerau-levenshtein": "^1.0.8", "emoji-regex": "^9.2.2", "has": "^1.0.3", "jsx-ast-utils": "^3.3.2", "language-tags": "^1.0.5", "minimatch": "^3.1.2", "semver": "^6.3.0"}, "engines": {"node": ">=4.0"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8"}}, "node_modules/eslint-plugin-jsx-a11y/node_modules/@babel/runtime": {"version": "7.20.1", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.13.10"}, "engines": {"node": ">=6.9.0"}}, "node_modules/eslint-plugin-jsx-a11y/node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/eslint-plugin-jsx-a11y/node_modules/jsx-ast-utils": {"version": "3.3.3", "license": "MIT", "dependencies": {"array-includes": "^3.1.5", "object.assign": "^4.1.3"}, "engines": {"node": ">=4.0"}}, "node_modules/eslint-plugin-jsx-a11y/node_modules/object.assign": {"version": "4.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/eslint-plugin-jsx-a11y/node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT"}, "node_modules/eslint-plugin-react": {"version": "7.31.11", "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flatmap": "^1.3.1", "array.prototype.tosorted": "^1.1.1", "doctrine": "^2.1.0", "estraverse": "^5.3.0", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "minimatch": "^3.1.2", "object.entries": "^1.1.6", "object.fromentries": "^2.0.6", "object.hasown": "^1.1.2", "object.values": "^1.1.6", "prop-types": "^15.8.1", "resolve": "^2.0.0-next.3", "semver": "^6.3.0", "string.prototype.matchall": "^4.0.8"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8"}}, "node_modules/eslint-plugin-react-hooks": {"version": "4.6.0", "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0"}}, "node_modules/eslint-plugin-react/node_modules/estraverse": {"version": "5.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint-plugin-react/node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/eslint-plugin-react/node_modules/resolve": {"version": "2.0.0-next.3", "license": "MIT", "dependencies": {"is-core-module": "^2.2.0", "path-parse": "^1.0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/eslint-plugin-testing-library": {"version": "5.9.1", "license": "MIT", "dependencies": {"@typescript-eslint/utils": "^5.13.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0", "npm": ">=6"}, "peerDependencies": {"eslint": "^7.5.0 || ^8.0.0"}}, "node_modules/eslint-scope": {"version": "5.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/eslint-scope/node_modules/estraverse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint-utils": {"version": "3.0.0", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^2.0.0"}, "engines": {"node": "^10.0.0 || ^12.0.0 || >= 14.0.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": ">=5"}}, "node_modules/eslint-utils/node_modules/eslint-visitor-keys": {"version": "2.1.0", "license": "Apache-2.0", "engines": {"node": ">=10"}}, "node_modules/eslint-visitor-keys": {"version": "3.3.0", "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/eslint-webpack-plugin": {"version": "3.2.0", "license": "MIT", "dependencies": {"@types/eslint": "^7.29.0 || ^8.4.1", "jest-worker": "^28.0.2", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0", "webpack": "^5.0.0"}}, "node_modules/eslint-webpack-plugin/node_modules/jest-worker": {"version": "28.1.3", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/eslint-webpack-plugin/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/eslint/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/eslint/node_modules/debug": {"version": "4.3.4", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/eslint/node_modules/doctrine": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/eslint/node_modules/eslint-scope": {"version": "7.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/eslint/node_modules/glob-parent": {"version": "6.0.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/eslint/node_modules/glob-parent/node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint/node_modules/globals": {"version": "13.18.0", "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/eslint/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/espree": {"version": "9.4.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.4.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.2.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "1.0.1", "license": "MIT"}, "node_modules/esutils": {"version": "2.0.3", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter3": {"version": "4.0.7", "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/execa": {"version": "5.1.1", "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/execa/node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/exit": {"version": "0.1.2", "engines": {"node": ">= 0.8.0"}}, "node_modules/expect": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "jest-get-type": "^27.5.1", "jest-matcher-utils": "^27.5.1", "jest-message-util": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/express": {"version": "4.18.2", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.1", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.5.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.11.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express/node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/express/node_modules/negotiator": {"version": "0.6.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-glob": {"version": "3.2.12", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "license": "MIT"}, "node_modules/fastq": {"version": "1.11.0", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/faye-websocket": {"version": "0.11.4", "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/fb-watchman": {"version": "2.0.1", "license": "Apache-2.0", "dependencies": {"bser": "2.1.1"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/file-loader": {"version": "6.2.0", "license": "MIT", "dependencies": {"loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/file-loader/node_modules/schema-utils": {"version": "3.0.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.6", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/filelist": {"version": "1.0.4", "license": "Apache-2.0", "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/filelist/node_modules/brace-expansion": {"version": "2.0.1", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/filelist/node_modules/minimatch": {"version": "5.1.0", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/filesize": {"version": "8.0.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 0.4.0"}}, "node_modules/fill-range": {"version": "7.0.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.2.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/find-cache-dir": {"version": "3.3.1", "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/avajs/find-cache-dir?sponsor=1"}}, "node_modules/find-up": {"version": "5.0.0", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.0.4", "license": "MIT", "dependencies": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.1.1", "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.14.1", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/fork-ts-checker-webpack-plugin": {"version": "6.5.2", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.8.3", "@types/json-schema": "^7.0.5", "chalk": "^4.1.0", "chokidar": "^3.4.2", "cosmiconfig": "^6.0.0", "deepmerge": "^4.2.2", "fs-extra": "^9.0.0", "glob": "^7.1.6", "memfs": "^3.1.2", "minimatch": "^3.0.4", "schema-utils": "2.7.0", "semver": "^7.3.2", "tapable": "^1.0.0"}, "engines": {"node": ">=10", "yarn": ">=1.0.0"}, "peerDependencies": {"eslint": ">= 6", "typescript": ">= 2.7", "vue-template-compiler": "*", "webpack": ">= 4"}, "peerDependenciesMeta": {"eslint": {"optional": true}, "vue-template-compiler": {"optional": true}}}, "node_modules/fork-ts-checker-webpack-plugin/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/fork-ts-checker-webpack-plugin/node_modules/cosmiconfig": {"version": "6.0.0", "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.7.2"}, "engines": {"node": ">=8"}}, "node_modules/fork-ts-checker-webpack-plugin/node_modules/schema-utils": {"version": "2.7.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.4", "ajv": "^6.12.2", "ajv-keywords": "^3.4.1"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/fork-ts-checker-webpack-plugin/node_modules/semver": {"version": "7.3.5", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/fork-ts-checker-webpack-plugin/node_modules/tapable": {"version": "1.1.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/form-data": {"version": "3.0.1", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/formik": {"version": "2.4.2", "funding": [{"type": "individual", "url": "https://opencollective.com/formik"}], "license": "Apache-2.0", "dependencies": {"deepmerge": "^2.1.1", "hoist-non-react-statics": "^3.3.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "react-fast-compare": "^2.0.1", "tiny-warning": "^1.0.2", "tslib": "^2.0.0"}, "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/formik/node_modules/deepmerge": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/formik/node_modules/tslib": {"version": "2.6.0", "license": "0BSD"}, "node_modules/forwarded": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fraction.js": {"version": "4.2.0", "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://www.patreon.com/infusion"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-extra": {"version": "9.1.0", "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/fs-monkey": {"version": "1.0.3", "license": "Unlicense"}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.2", "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.1", "license": "MIT"}, "node_modules/function.prototype.name": {"version": "1.1.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.0", "functions-have-names": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.1.1", "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-own-enumerable-property-symbols": {"version": "3.0.2", "license": "ISC"}, "node_modules/get-package-type": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/get-stream": {"version": "6.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-symbol-description": {"version": "1.0.0", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/glob": {"version": "7.1.7", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/global-modules": {"version": "2.0.0", "license": "MIT", "dependencies": {"global-prefix": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/global-prefix": {"version": "3.0.0", "license": "MIT", "dependencies": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}, "engines": {"node": ">=6"}}, "node_modules/global-prefix/node_modules/which": {"version": "1.3.1", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/globals": {"version": "11.12.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/globby": {"version": "11.1.0", "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/graceful-fs": {"version": "4.2.10", "license": "ISC"}, "node_modules/grapheme-splitter": {"version": "1.0.4", "license": "MIT"}, "node_modules/gud": {"version": "1.0.0", "license": "MIT"}, "node_modules/gzip-size": {"version": "6.0.0", "license": "MIT", "dependencies": {"duplexer": "^0.1.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/handle-thing": {"version": "2.0.1", "license": "MIT"}, "node_modules/harmony-reflect": {"version": "1.6.2", "license": "(Apache-2.0 OR MPL-1.1)"}, "node_modules/has": {"version": "1.0.3", "license": "MIT", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-bigints": {"version": "1.0.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.0", "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.0", "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/he": {"version": "1.2.0", "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/hoopy": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">= 6.0.0"}}, "node_modules/hpack.js": {"version": "2.1.6", "license": "MIT", "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "node_modules/hpack.js/node_modules/readable-stream": {"version": "2.3.7", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/hpack.js/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/hpack.js/node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/html-encoding-sniffer": {"version": "2.0.1", "license": "MIT", "dependencies": {"whatwg-encoding": "^1.0.5"}, "engines": {"node": ">=10"}}, "node_modules/html-entities": {"version": "2.3.3", "license": "MIT"}, "node_modules/html-escaper": {"version": "2.0.2", "license": "MIT"}, "node_modules/html-minifier-terser": {"version": "6.1.0", "license": "MIT", "dependencies": {"camel-case": "^4.1.2", "clean-css": "^5.2.2", "commander": "^8.3.0", "he": "^1.2.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.10.0"}, "bin": {"html-minifier-terser": "cli.js"}, "engines": {"node": ">=12"}}, "node_modules/html-minifier-terser/node_modules/commander": {"version": "8.3.0", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/html-webpack-plugin": {"version": "5.5.0", "license": "MIT", "dependencies": {"@types/html-minifier-terser": "^6.0.0", "html-minifier-terser": "^6.0.2", "lodash": "^4.17.21", "pretty-error": "^4.0.0", "tapable": "^2.0.0"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/html-webpack-plugin"}, "peerDependencies": {"webpack": "^5.20.0"}}, "node_modules/htmlparser2": {"version": "6.1.0", "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "node_modules/http-deceiver": {"version": "1.2.7", "license": "MIT"}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-parser-js": {"version": "0.5.3", "license": "MIT"}, "node_modules/http-proxy": {"version": "1.18.1", "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-proxy-agent": {"version": "4.0.1", "license": "MIT", "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/http-proxy-middleware": {"version": "2.0.6", "license": "MIT", "dependencies": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"@types/express": "^4.17.13"}, "peerDependenciesMeta": {"@types/express": {"optional": true}}}, "node_modules/https-proxy-agent": {"version": "5.0.0", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/human-signals": {"version": "2.1.0", "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/icss-utils": {"version": "5.1.0", "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/idb": {"version": "7.1.1", "license": "ISC"}, "node_modules/identity-obj-proxy": {"version": "3.0.0", "license": "MIT", "dependencies": {"harmony-reflect": "^1.4.6"}, "engines": {"node": ">=4"}}, "node_modules/ignore": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/immer": {"version": "9.0.16", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}}, "node_modules/import-fresh": {"version": "3.3.0", "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/import-local": {"version": "3.0.2", "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}}, "node_modules/imurmurhash": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "license": "ISC"}, "node_modules/internal-slot": {"version": "1.0.3", "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.0", "has": "^1.0.3", "side-channel": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/ipaddr.js": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/is-arguments": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bind": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-bigint": {"version": "1.0.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-binary-path": {"version": "2.1.0", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-boolean-object": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-callable": {"version": "1.2.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.11.0", "license": "MIT", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "2.2.1", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/is-glob": {"version": "4.0.1", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-module": {"version": "1.0.0", "license": "MIT"}, "node_modules/is-negative-zero": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-obj": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-obj": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-potential-custom-element-name": {"version": "1.0.1", "license": "MIT"}, "node_modules/is-regex": {"version": "1.1.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-regexp": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-root": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-string": {"version": "1.0.6", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.0.4", "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typedarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/is-weakref": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-wsl": {"version": "2.2.0", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/iso-3166-1-alpha-2": {"version": "1.0.1", "license": "MIT", "dependencies": {"mout": "^1.2.3"}}, "node_modules/istanbul-lib-coverage": {"version": "3.0.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "5.2.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument/node_modules/istanbul-lib-coverage": {"version": "3.2.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report": {"version": "3.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^3.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-reports": {"version": "3.1.5", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jake": {"version": "10.8.5", "license": "Apache-2.0", "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.1", "minimatch": "^3.0.4"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/jake/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/core": "^27.5.1", "import-local": "^3.0.2", "jest-cli": "^27.5.1"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-changed-files": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "execa": "^5.0.0", "throat": "^6.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-circus": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/environment": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^0.7.0", "expect": "^27.5.1", "is-generator-fn": "^2.0.0", "jest-each": "^27.5.1", "jest-matcher-utils": "^27.5.1", "jest-message-util": "^27.5.1", "jest-runtime": "^27.5.1", "jest-snapshot": "^27.5.1", "jest-util": "^27.5.1", "pretty-format": "^27.5.1", "slash": "^3.0.0", "stack-utils": "^2.0.3", "throat": "^6.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-cli": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/core": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/types": "^27.5.1", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "import-local": "^3.0.2", "jest-config": "^27.5.1", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "prompts": "^2.0.1", "yargs": "^16.2.0"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-config": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/core": "^7.8.0", "@jest/test-sequencer": "^27.5.1", "@jest/types": "^27.5.1", "babel-jest": "^27.5.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.1", "graceful-fs": "^4.2.9", "jest-circus": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-environment-node": "^27.5.1", "jest-get-type": "^27.5.1", "jest-jasmine2": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-resolve": "^27.5.1", "jest-runner": "^27.5.1", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^27.5.1", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"ts-node": {"optional": true}}}, "node_modules/jest-diff": {"version": "27.5.1", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^27.5.1", "jest-get-type": "^27.5.1", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-docblock": {"version": "27.5.1", "license": "MIT", "dependencies": {"detect-newline": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-each": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "chalk": "^4.0.0", "jest-get-type": "^27.5.1", "jest-util": "^27.5.1", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-environment-jsdom": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/environment": "^27.5.1", "@jest/fake-timers": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "jest-mock": "^27.5.1", "jest-util": "^27.5.1", "jsdom": "^16.6.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-environment-node": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/environment": "^27.5.1", "@jest/fake-timers": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "jest-mock": "^27.5.1", "jest-util": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-get-type": {"version": "27.5.1", "license": "MIT", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-haste-map": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/graceful-fs": "^4.1.2", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^27.5.1", "jest-serializer": "^27.5.1", "jest-util": "^27.5.1", "jest-worker": "^27.5.1", "micromatch": "^4.0.4", "walker": "^1.0.7"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/jest-jasmine2": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/environment": "^27.5.1", "@jest/source-map": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "expect": "^27.5.1", "is-generator-fn": "^2.0.0", "jest-each": "^27.5.1", "jest-matcher-utils": "^27.5.1", "jest-message-util": "^27.5.1", "jest-runtime": "^27.5.1", "jest-snapshot": "^27.5.1", "jest-util": "^27.5.1", "pretty-format": "^27.5.1", "throat": "^6.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-leak-detector": {"version": "27.5.1", "license": "MIT", "dependencies": {"jest-get-type": "^27.5.1", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-matcher-utils": {"version": "27.5.1", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "jest-diff": "^27.5.1", "jest-get-type": "^27.5.1", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-message-util": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^27.5.1", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^27.5.1", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-mock": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/node": "*"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-pnp-resolver": {"version": "1.2.2", "license": "MIT", "engines": {"node": ">=6"}, "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}}, "node_modules/jest-regex-util": {"version": "27.5.1", "license": "MIT", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-resolve": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.1", "jest-pnp-resolver": "^1.2.2", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "resolve": "^1.20.0", "resolve.exports": "^1.1.0", "slash": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-resolve-dependencies": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-snapshot": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-runner": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/console": "^27.5.1", "@jest/environment": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/transform": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.8.1", "graceful-fs": "^4.2.9", "jest-docblock": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-environment-node": "^27.5.1", "jest-haste-map": "^27.5.1", "jest-leak-detector": "^27.5.1", "jest-message-util": "^27.5.1", "jest-resolve": "^27.5.1", "jest-runtime": "^27.5.1", "jest-util": "^27.5.1", "jest-worker": "^27.5.1", "source-map-support": "^0.5.6", "throat": "^6.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-runtime": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/environment": "^27.5.1", "@jest/fake-timers": "^27.5.1", "@jest/globals": "^27.5.1", "@jest/source-map": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/transform": "^27.5.1", "@jest/types": "^27.5.1", "chalk": "^4.0.0", "cjs-module-lexer": "^1.0.0", "collect-v8-coverage": "^1.0.0", "execa": "^5.0.0", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.1", "jest-message-util": "^27.5.1", "jest-mock": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-resolve": "^27.5.1", "jest-snapshot": "^27.5.1", "jest-util": "^27.5.1", "slash": "^3.0.0", "strip-bom": "^4.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-runtime/node_modules/strip-bom": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-serializer": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "graceful-fs": "^4.2.9"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-snapshot": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/core": "^7.7.2", "@babel/generator": "^7.7.2", "@babel/plugin-syntax-typescript": "^7.7.2", "@babel/traverse": "^7.7.2", "@babel/types": "^7.0.0", "@jest/transform": "^27.5.1", "@jest/types": "^27.5.1", "@types/babel__traverse": "^7.0.4", "@types/prettier": "^2.1.5", "babel-preset-current-node-syntax": "^1.0.0", "chalk": "^4.0.0", "expect": "^27.5.1", "graceful-fs": "^4.2.9", "jest-diff": "^27.5.1", "jest-get-type": "^27.5.1", "jest-haste-map": "^27.5.1", "jest-matcher-utils": "^27.5.1", "jest-message-util": "^27.5.1", "jest-util": "^27.5.1", "natural-compare": "^1.4.0", "pretty-format": "^27.5.1", "semver": "^7.3.2"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-snapshot/node_modules/semver": {"version": "7.3.5", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/jest-util": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-validate": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^27.5.1", "leven": "^3.1.0", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-watch-typeahead": {"version": "1.1.0", "license": "MIT", "dependencies": {"ansi-escapes": "^4.3.1", "chalk": "^4.0.0", "jest-regex-util": "^28.0.0", "jest-watcher": "^28.0.0", "slash": "^4.0.0", "string-length": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"jest": "^27.0.0 || ^28.0.0"}}, "node_modules/jest-watch-typeahead/node_modules/ansi-regex": {"version": "6.0.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/jest-watch-typeahead/node_modules/char-regex": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=12.20"}}, "node_modules/jest-watch-typeahead/node_modules/jest-regex-util": {"version": "28.0.2", "license": "MIT", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/jest-watch-typeahead/node_modules/slash": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watch-typeahead/node_modules/string-length": {"version": "5.0.1", "license": "MIT", "dependencies": {"char-regex": "^2.0.0", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watch-typeahead/node_modules/strip-ansi": {"version": "7.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/jest-watcher": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/test-result": "^28.1.3", "@jest/types": "^28.1.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^28.1.3", "string-length": "^4.0.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/jest-watcher/node_modules/@jest/console": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/types": "^28.1.3", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^28.1.3", "jest-util": "^28.1.3", "slash": "^3.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/jest-watcher/node_modules/@jest/test-result": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/console": "^28.1.3", "@jest/types": "^28.1.3", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/jest-watcher/node_modules/@jest/types": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/schemas": "^28.1.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/jest-watcher/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-watcher/node_modules/emittery": {"version": "0.10.2", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/jest-watcher/node_modules/jest-message-util": {"version": "28.1.3", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^28.1.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^28.1.3", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/jest-watcher/node_modules/jest-util": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/types": "^28.1.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/jest-watcher/node_modules/pretty-format": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/schemas": "^28.1.3", "ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/jest-watcher/node_modules/react-is": {"version": "18.2.0", "license": "MIT"}, "node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/jquery": {"version": "3.7.1", "license": "MIT", "peer": true}, "node_modules/js-sdsl": {"version": "4.2.0", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/js-sdsl"}}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsdom": {"version": "16.7.0", "license": "MIT", "dependencies": {"abab": "^2.0.5", "acorn": "^8.2.4", "acorn-globals": "^6.0.0", "cssom": "^0.4.4", "cssstyle": "^2.3.0", "data-urls": "^2.0.0", "decimal.js": "^10.2.1", "domexception": "^2.0.1", "escodegen": "^2.0.0", "form-data": "^3.0.0", "html-encoding-sniffer": "^2.0.1", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.0", "parse5": "6.0.1", "saxes": "^5.0.1", "symbol-tree": "^3.2.4", "tough-cookie": "^4.0.0", "w3c-hr-time": "^1.0.2", "w3c-xmlserializer": "^2.0.0", "webidl-conversions": "^6.1.0", "whatwg-encoding": "^1.0.5", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.5.0", "ws": "^7.4.6", "xml-name-validator": "^3.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"canvas": "^2.5.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "node_modules/jsdom/node_modules/ws": {"version": "7.5.9", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/jsesc": {"version": "0.5.0", "bin": {"jsesc": "bin/jsesc"}}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "license": "MIT"}, "node_modules/json-schema": {"version": "0.4.0", "license": "(AFL-2.1 OR BSD-3-Clause)"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonpointer": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/jsx-ast-utils": {"version": "3.2.0", "license": "MIT", "dependencies": {"array-includes": "^3.1.2", "object.assign": "^4.1.2"}, "engines": {"node": ">=4.0"}}, "node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/kleur": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/klona": {"version": "2.0.4", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/language-subtag-registry": {"version": "0.3.21", "license": "ODC-By-1.0"}, "node_modules/language-tags": {"version": "1.0.5", "license": "MIT", "dependencies": {"language-subtag-registry": "~0.3.2"}}, "node_modules/leven": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/levn": {"version": "0.4.1", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/levn/node_modules/prelude-ls": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/levn/node_modules/type-check": {"version": "0.4.0", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lilconfig": {"version": "2.0.6", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/lines-and-columns": {"version": "1.1.6", "license": "MIT"}, "node_modules/loader-runner": {"version": "4.3.0", "license": "MIT", "engines": {"node": ">=6.11.5"}}, "node_modules/loader-utils": {"version": "2.0.0", "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/locate-path": {"version": "6.0.0", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "license": "MIT"}, "node_modules/lodash.memoize": {"version": "4.1.2", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "license": "MIT"}, "node_modules/lodash.sortby": {"version": "4.7.0", "license": "MIT"}, "node_modules/lodash.uniq": {"version": "4.5.0", "license": "MIT"}, "node_modules/logrocket": {"version": "8.1.0", "license": "MIT"}, "node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lower-case": {"version": "2.0.2", "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/magic-string": {"version": "0.25.7", "license": "MIT", "dependencies": {"sourcemap-codec": "^1.4.4"}}, "node_modules/make-dir": {"version": "3.1.0", "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/makeerror": {"version": "1.0.11", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.x"}}, "node_modules/mdn-data": {"version": "2.0.14", "license": "CC0-1.0"}, "node_modules/media-typer": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memfs": {"version": "3.4.12", "license": "Unlicense", "dependencies": {"fs-monkey": "^1.0.3"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "license": "MIT"}, "node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.5", "license": "MIT", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/micromatch/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.48.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.31", "license": "MIT", "dependencies": {"mime-db": "1.48.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mini-css-extract-plugin": {"version": "2.7.0", "license": "MIT", "dependencies": {"schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "license": "ISC"}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.5", "license": "MIT"}, "node_modules/mkdirp": {"version": "0.5.5", "license": "MIT", "dependencies": {"minimist": "^1.2.5"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/moment": {"version": "2.29.4", "license": "MIT", "engines": {"node": "*"}}, "node_modules/mout": {"version": "1.2.4", "license": "MIT"}, "node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/multicast-dns": {"version": "7.2.5", "license": "MIT", "dependencies": {"dns-packet": "^5.2.2", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/nanoclone": {"version": "0.2.1", "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.4", "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "license": "MIT"}, "node_modules/natural-compare-lite": {"version": "1.4.0", "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "license": "MIT"}, "node_modules/no-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/node-forge": {"version": "1.3.1", "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "node_modules/node-int64": {"version": "0.4.0", "license": "MIT"}, "node_modules/node-releases": {"version": "2.0.14", "license": "MIT"}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm-run-path": {"version": "4.0.1", "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/nth-check": {"version": "2.0.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/nwsapi": {"version": "2.2.0", "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/object-inspect": {"version": "1.10.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-is": {"version": "1.1.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "has-symbols": "^1.0.1", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.entries": {"version": "1.1.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.fromentries": {"version": "2.0.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.getownpropertydescriptors": {"version": "2.1.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.2"}, "engines": {"node": ">= 0.8"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.hasown": {"version": "1.1.2", "license": "MIT", "dependencies": {"define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.values": {"version": "1.1.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/obuf": {"version": "1.1.2", "license": "MIT"}, "node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "8.4.0", "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optionator": {"version": "0.9.1", "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/optionator/node_modules/prelude-ls": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/optionator/node_modules/type-check": {"version": "0.4.0", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate/node_modules/p-limit": {"version": "3.1.0", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-retry": {"version": "4.6.2", "license": "MIT", "dependencies": {"@types/retry": "0.12.0", "retry": "^0.13.1"}, "engines": {"node": ">=8"}}, "node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/param-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/parent-module": {"version": "1.0.1", "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse5": {"version": "6.0.1", "license": "MIT"}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascal-case": {"version": "3.1.2", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-to-regexp": {"version": "0.1.7", "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/performance-now": {"version": "2.1.0", "license": "MIT"}, "node_modules/picocolors": {"version": "1.0.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pirates": {"version": "4.0.5", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pkg-dir": {"version": "4.2.0", "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-up": {"version": "3.1.0", "license": "MIT", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-up/node_modules/find-up": {"version": "3.0.0", "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/locate-path": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/p-locate": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/pkg-up/node_modules/path-exists": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/playwright": {"version": "1.54.1", "resolved": "https://registry.npmjs.org/playwright/-/playwright-1.54.1.tgz", "integrity": "sha512-peWpSwIBmSLi6aW2auvrUtf2DqY16YYcCMO8rTVx486jKmDTJg7UAhyrraP98GB8BoPURZP8+nxO7TSd4cPr5g==", "dev": true, "dependencies": {"playwright-core": "1.54.1"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=18"}, "optionalDependencies": {"fsevents": "2.3.2"}}, "node_modules/playwright-core": {"version": "1.54.1", "resolved": "https://registry.npmjs.org/playwright-core/-/playwright-core-1.54.1.tgz", "integrity": "sha512-Nbjs2zjj0htNhzgiy5wu+3w09YetDx5pkrpI/kZotDlDUaYk0HVA5xrBVPdow4SAUIlhgKcJeJg4GRKW6xHusA==", "dev": true, "bin": {"playwright-core": "cli.js"}, "engines": {"node": ">=18"}}, "node_modules/popper.js": {"version": "1.16.1", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/postcss": {"version": "8.4.19", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-attribute-case-insensitive": {"version": "5.0.2", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-browser-comments": {"version": "4.0.0", "license": "CC0-1.0", "engines": {"node": ">=8"}, "peerDependencies": {"browserslist": ">=4", "postcss": ">=8"}}, "node_modules/postcss-calc": {"version": "8.2.4", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.9", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.2.2"}}, "node_modules/postcss-clamp": {"version": "4.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": ">=7.6.0"}, "peerDependencies": {"postcss": "^8.4.6"}}, "node_modules/postcss-color-functional-notation": {"version": "4.2.4", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-color-hex-alpha": {"version": "8.0.4", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-color-rebeccapurple": {"version": "7.1.1", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-colormin": {"version": "5.3.0", "license": "MIT", "dependencies": {"browserslist": "^4.16.6", "caniuse-api": "^3.0.0", "colord": "^2.9.1", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-convert-values": {"version": "5.1.3", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-custom-media": {"version": "8.0.2", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.3"}}, "node_modules/postcss-custom-properties": {"version": "12.1.10", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-custom-selectors": {"version": "6.0.3", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.3"}}, "node_modules/postcss-dir-pseudo-class": {"version": "6.0.5", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-discard-comments": {"version": "5.1.2", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-discard-duplicates": {"version": "5.1.0", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-discard-empty": {"version": "5.1.1", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-discard-overridden": {"version": "5.1.0", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-double-position-gradients": {"version": "3.1.2", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-env-function": {"version": "4.0.6", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-flexbugs-fixes": {"version": "5.0.2", "license": "MIT", "peerDependencies": {"postcss": "^8.1.4"}}, "node_modules/postcss-focus-visible": {"version": "6.0.4", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-focus-within": {"version": "5.0.4", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-font-variant": {"version": "5.0.0", "license": "MIT", "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-gap-properties": {"version": "3.0.5", "license": "CC0-1.0", "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-image-set-function": {"version": "4.0.7", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-import": {"version": "14.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/postcss-import/node_modules/resolve": {"version": "1.22.1", "license": "MIT", "dependencies": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/postcss-initial": {"version": "4.0.1", "license": "MIT", "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/postcss-js": {"version": "4.0.0", "license": "MIT", "dependencies": {"camelcase-css": "^2.0.1"}, "engines": {"node": "^12 || ^14 || >= 16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.3.3"}}, "node_modules/postcss-lab-function": {"version": "4.2.1", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-load-config": {"version": "3.1.4", "license": "MIT", "dependencies": {"lilconfig": "^2.0.5", "yaml": "^1.10.2"}, "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/postcss-loader": {"version": "6.2.1", "license": "MIT", "dependencies": {"cosmiconfig": "^7.0.0", "klona": "^2.0.5", "semver": "^7.3.5"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"postcss": "^7.0.0 || ^8.0.1", "webpack": "^5.0.0"}}, "node_modules/postcss-loader/node_modules/klona": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/postcss-loader/node_modules/semver": {"version": "7.3.5", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/postcss-logical": {"version": "5.0.4", "license": "CC0-1.0", "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-media-minmax": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-merge-longhand": {"version": "5.1.7", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0", "stylehacks": "^5.1.1"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-merge-rules": {"version": "5.1.3", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "caniuse-api": "^3.0.0", "cssnano-utils": "^3.1.0", "postcss-selector-parser": "^6.0.5"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-minify-font-values": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-minify-gradients": {"version": "5.1.1", "license": "MIT", "dependencies": {"colord": "^2.9.1", "cssnano-utils": "^3.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-minify-params": {"version": "5.1.4", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "cssnano-utils": "^3.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-minify-selectors": {"version": "5.2.1", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.5"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-modules-extract-imports": {"version": "3.0.0", "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-local-by-default": {"version": "4.0.0", "license": "MIT", "dependencies": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.1.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-scope": {"version": "3.0.0", "license": "ISC", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-values": {"version": "4.0.0", "license": "ISC", "dependencies": {"icss-utils": "^5.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-nested": {"version": "6.0.0", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": ">=12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.2.14"}}, "node_modules/postcss-nesting": {"version": "10.2.0", "license": "CC0-1.0", "dependencies": {"@csstools/selector-specificity": "^2.0.0", "postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-normalize": {"version": "10.0.1", "license": "CC0-1.0", "dependencies": {"@csstools/normalize.css": "*", "postcss-browser-comments": "^4", "sanitize.css": "*"}, "engines": {"node": ">= 12"}, "peerDependencies": {"browserslist": ">= 4", "postcss": ">= 8"}}, "node_modules/postcss-normalize-charset": {"version": "5.1.0", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-display-values": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-positions": {"version": "5.1.1", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-repeat-style": {"version": "5.1.1", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-string": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-timing-functions": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-unicode": {"version": "5.1.1", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-url": {"version": "5.1.0", "license": "MIT", "dependencies": {"normalize-url": "^6.0.1", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-whitespace": {"version": "5.1.1", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-opacity-percentage": {"version": "1.1.2", "funding": [{"type": "kofi", "url": "https://ko-fi.com/mrcgrtz"}, {"type": "liberapay", "url": "https://liberapay.com/mrcgrtz"}], "license": "MIT", "engines": {"node": "^12 || ^14 || >=16"}}, "node_modules/postcss-ordered-values": {"version": "5.1.3", "license": "MIT", "dependencies": {"cssnano-utils": "^3.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-overflow-shorthand": {"version": "3.0.4", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-page-break": {"version": "3.0.4", "license": "MIT", "peerDependencies": {"postcss": "^8"}}, "node_modules/postcss-place": {"version": "7.0.5", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-preset-env": {"version": "7.8.3", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-cascade-layers": "^1.1.1", "@csstools/postcss-color-function": "^1.1.1", "@csstools/postcss-font-format-keywords": "^1.0.1", "@csstools/postcss-hwb-function": "^1.0.2", "@csstools/postcss-ic-unit": "^1.0.1", "@csstools/postcss-is-pseudo-class": "^2.0.7", "@csstools/postcss-nested-calc": "^1.0.0", "@csstools/postcss-normalize-display-values": "^1.0.1", "@csstools/postcss-oklab-function": "^1.1.1", "@csstools/postcss-progressive-custom-properties": "^1.3.0", "@csstools/postcss-stepped-value-functions": "^1.0.1", "@csstools/postcss-text-decoration-shorthand": "^1.0.0", "@csstools/postcss-trigonometric-functions": "^1.0.2", "@csstools/postcss-unset-value": "^1.0.2", "autoprefixer": "^10.4.13", "browserslist": "^4.21.4", "css-blank-pseudo": "^3.0.3", "css-has-pseudo": "^3.0.4", "css-prefers-color-scheme": "^6.0.3", "cssdb": "^7.1.0", "postcss-attribute-case-insensitive": "^5.0.2", "postcss-clamp": "^4.1.0", "postcss-color-functional-notation": "^4.2.4", "postcss-color-hex-alpha": "^8.0.4", "postcss-color-rebeccapurple": "^7.1.1", "postcss-custom-media": "^8.0.2", "postcss-custom-properties": "^12.1.10", "postcss-custom-selectors": "^6.0.3", "postcss-dir-pseudo-class": "^6.0.5", "postcss-double-position-gradients": "^3.1.2", "postcss-env-function": "^4.0.6", "postcss-focus-visible": "^6.0.4", "postcss-focus-within": "^5.0.4", "postcss-font-variant": "^5.0.0", "postcss-gap-properties": "^3.0.5", "postcss-image-set-function": "^4.0.7", "postcss-initial": "^4.0.1", "postcss-lab-function": "^4.2.1", "postcss-logical": "^5.0.4", "postcss-media-minmax": "^5.0.0", "postcss-nesting": "^10.2.0", "postcss-opacity-percentage": "^1.1.2", "postcss-overflow-shorthand": "^3.0.4", "postcss-page-break": "^3.0.4", "postcss-place": "^7.0.5", "postcss-pseudo-class-any-link": "^7.1.6", "postcss-replace-overflow-wrap": "^4.0.0", "postcss-selector-not": "^6.0.1", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-pseudo-class-any-link": {"version": "7.1.6", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-reduce-initial": {"version": "5.1.1", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "caniuse-api": "^3.0.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-reduce-transforms": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-replace-overflow-wrap": {"version": "4.0.0", "license": "MIT", "peerDependencies": {"postcss": "^8.0.3"}}, "node_modules/postcss-selector-not": {"version": "6.0.1", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-selector-parser": {"version": "6.0.11", "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-svgo": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0", "svgo": "^2.7.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-svgo/node_modules/commander": {"version": "7.2.0", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/postcss-svgo/node_modules/svgo": {"version": "2.8.0", "license": "MIT", "dependencies": {"@trysound/sax": "0.2.0", "commander": "^7.2.0", "css-select": "^4.1.3", "css-tree": "^1.1.3", "csso": "^4.2.0", "picocolors": "^1.0.0", "stable": "^0.1.8"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=10.13.0"}}, "node_modules/postcss-unique-selectors": {"version": "5.1.1", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.5"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.1.2", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "2.8.8", "dev": true, "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/pretty-bytes": {"version": "5.6.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pretty-error": {"version": "4.0.0", "license": "MIT", "dependencies": {"lodash": "^4.17.20", "renderkid": "^3.0.0"}}, "node_modules/pretty-format": {"version": "27.5.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^17.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/pretty-format/node_modules/react-is": {"version": "17.0.2", "license": "MIT"}, "node_modules/process-nextick-args": {"version": "2.0.1", "license": "MIT"}, "node_modules/promise": {"version": "8.1.0", "license": "MIT", "dependencies": {"asap": "~2.0.6"}}, "node_modules/prompts": {"version": "2.4.2", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/prop-types": {"version": "15.7.2", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.8.1"}}, "node_modules/property-expr": {"version": "2.0.4", "license": "MIT"}, "node_modules/proxy-addr": {"version": "2.0.7", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-addr/node_modules/ipaddr.js": {"version": "1.9.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/psl": {"version": "1.8.0", "license": "MIT"}, "node_modules/punycode": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/q": {"version": "1.5.1", "license": "MIT", "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/qs": {"version": "6.11.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/quick-lru": {"version": "5.1.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/raf": {"version": "3.4.1", "license": "MIT", "dependencies": {"performance-now": "^2.1.0"}}, "node_modules/randombytes": {"version": "2.1.0", "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.1", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/react": {"version": "18.2.0", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-app-polyfill": {"version": "3.0.0", "license": "MIT", "dependencies": {"core-js": "^3.19.2", "object-assign": "^4.1.1", "promise": "^8.1.0", "raf": "^3.4.1", "regenerator-runtime": "^0.13.9", "whatwg-fetch": "^3.6.2"}, "engines": {"node": ">=14"}}, "node_modules/react-app-polyfill/node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT"}, "node_modules/react-dev-utils": {"version": "12.0.1", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.16.0", "address": "^1.1.2", "browserslist": "^4.18.1", "chalk": "^4.1.2", "cross-spawn": "^7.0.3", "detect-port-alt": "^1.1.6", "escape-string-regexp": "^4.0.0", "filesize": "^8.0.6", "find-up": "^5.0.0", "fork-ts-checker-webpack-plugin": "^6.5.0", "global-modules": "^2.0.0", "globby": "^11.0.4", "gzip-size": "^6.0.0", "immer": "^9.0.7", "is-root": "^2.1.0", "loader-utils": "^3.2.0", "open": "^8.4.0", "pkg-up": "^3.1.0", "prompts": "^2.4.2", "react-error-overlay": "^6.0.11", "recursive-readdir": "^2.2.2", "shell-quote": "^1.7.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "engines": {"node": ">=14"}}, "node_modules/react-dev-utils/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/react-dev-utils/node_modules/loader-utils": {"version": "3.2.1", "license": "MIT", "engines": {"node": ">= 12.13.0"}}, "node_modules/react-dev-utils/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/react-dom": {"version": "18.2.0", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.0"}, "peerDependencies": {"react": "^18.2.0"}}, "node_modules/react-error-overlay": {"version": "6.0.11", "license": "MIT"}, "node_modules/react-fast-compare": {"version": "2.0.4", "license": "MIT"}, "node_modules/react-is": {"version": "16.13.1", "license": "MIT"}, "node_modules/react-lifecycles-compat": {"version": "3.0.4", "license": "MIT"}, "node_modules/react-popper": {"version": "1.3.11", "license": "MIT", "dependencies": {"@babel/runtime": "^7.1.2", "@hypnosphi/create-react-context": "^0.3.1", "deep-equal": "^1.1.1", "popper.js": "^1.14.4", "prop-types": "^15.6.1", "typed-styles": "^0.0.7", "warning": "^4.0.2"}, "peerDependencies": {"react": "0.14.x || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/react-refresh": {"version": "0.11.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-scripts": {"version": "5.0.1", "license": "MIT", "dependencies": {"@babel/core": "^7.16.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^5.5.0", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "mini-css-extract-plugin": "^2.4.5", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-refresh": "^0.11.0", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass-loader": "^12.3.0", "semver": "^7.3.5", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1"}, "bin": {"react-scripts": "bin/react-scripts.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}, "peerDependencies": {"react": ">= 16", "typescript": "^3.2.1 || ^4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/react-scripts/node_modules/camelcase": {"version": "6.3.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/react-scripts/node_modules/fs-extra": {"version": "10.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/react-scripts/node_modules/semver": {"version": "7.3.5", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/react-text-mask": {"version": "5.5.0", "license": "Unlicense", "dependencies": {"prop-types": "^15.5.6"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-transition-group": {"version": "3.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"dom-helpers": "^3.4.0", "loose-envify": "^1.4.0", "prop-types": "^15.6.2", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/reactstrap": {"version": "8.10.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "classnames": "^2.2.3", "prop-types": "^15.5.8", "react-popper": "^1.3.6", "react-transition-group": "^3.0.0"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}}, "node_modules/read-cache": {"version": "1.0.0", "license": "MIT", "dependencies": {"pify": "^2.3.0"}}, "node_modules/readable-stream": {"version": "3.6.0", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdirp": {"version": "3.6.0", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/recursive-readdir": {"version": "2.2.3", "license": "MIT", "dependencies": {"minimatch": "^3.0.5"}, "engines": {"node": ">=6.0.0"}}, "node_modules/regenerate": {"version": "1.4.2", "license": "MIT"}, "node_modules/regenerate-unicode-properties": {"version": "8.2.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.0"}, "engines": {"node": ">=4"}}, "node_modules/regenerator-runtime": {"version": "0.13.7", "license": "MIT"}, "node_modules/regenerator-transform": {"version": "0.15.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.4"}}, "node_modules/regex-parser": {"version": "2.2.11", "license": "MIT"}, "node_modules/regexp.prototype.flags": {"version": "1.4.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "functions-have-names": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regexpp": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}}, "node_modules/regexpu-core": {"version": "4.7.1", "license": "MIT", "dependencies": {"regenerate": "^1.4.0", "regenerate-unicode-properties": "^8.2.0", "regjsgen": "^0.5.1", "regjsparser": "^0.6.4", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.2.0"}, "engines": {"node": ">=4"}}, "node_modules/regjsgen": {"version": "0.5.2", "license": "MIT"}, "node_modules/regjsparser": {"version": "0.6.9", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~0.5.0"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/relateurl": {"version": "0.2.7", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/renderkid": {"version": "3.0.0", "license": "MIT", "dependencies": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^6.0.1"}}, "node_modules/renderkid/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "license": "MIT"}, "node_modules/resolve": {"version": "1.20.0", "license": "MIT", "dependencies": {"is-core-module": "^2.2.0", "path-parse": "^1.0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "3.0.0", "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve-url-loader": {"version": "4.0.0", "license": "MIT", "dependencies": {"adjust-sourcemap-loader": "^4.0.0", "convert-source-map": "^1.7.0", "loader-utils": "^2.0.0", "postcss": "^7.0.35", "source-map": "0.6.1"}, "engines": {"node": ">=8.9"}, "peerDependencies": {"rework": "1.0.1", "rework-visit": "1.0.0"}, "peerDependenciesMeta": {"rework": {"optional": true}, "rework-visit": {"optional": true}}}, "node_modules/resolve-url-loader/node_modules/picocolors": {"version": "0.2.1", "license": "ISC"}, "node_modules/resolve-url-loader/node_modules/postcss": {"version": "7.0.39", "license": "MIT", "dependencies": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}, "engines": {"node": ">=6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}}, "node_modules/resolve.exports": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/retry": {"version": "0.13.1", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.0.4", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rollup": {"version": "2.79.1", "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=10.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/rollup-plugin-terser": {"version": "7.0.2", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.10.4", "jest-worker": "^26.2.1", "serialize-javascript": "^4.0.0", "terser": "^5.0.0"}, "peerDependencies": {"rollup": "^2.0.0"}}, "node_modules/rollup-plugin-terser/node_modules/jest-worker": {"version": "26.6.2", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/rollup-plugin-terser/node_modules/serialize-javascript": {"version": "4.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.0.0", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-regex": "^1.1.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex-test/node_modules/get-intrinsic": {"version": "1.1.3", "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex-test/node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex-test/node_modules/is-regex": {"version": "1.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/sanitize.css": {"version": "13.0.0", "license": "CC0-1.0"}, "node_modules/sass-loader": {"version": "12.6.0", "license": "MIT", "dependencies": {"klona": "^2.0.4", "neo-async": "^2.6.2"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"fibers": ">= 3.1.0", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0", "sass": "^1.3.0", "sass-embedded": "*", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"fibers": {"optional": true}, "node-sass": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}}}, "node_modules/sax": {"version": "1.2.4", "license": "ISC"}, "node_modules/saxes": {"version": "5.0.1", "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=10"}}, "node_modules/scheduler": {"version": "0.23.0", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/schema-utils": {"version": "4.0.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.8.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/schema-utils/node_modules/ajv": {"version": "8.11.2", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/schema-utils/node_modules/ajv-formats": {"version": "2.1.1", "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/schema-utils/node_modules/ajv-keywords": {"version": "5.1.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/schema-utils/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/select-hose": {"version": "2.0.0", "license": "MIT"}, "node_modules/selfsigned": {"version": "2.1.1", "license": "MIT", "dependencies": {"node-forge": "^1"}, "engines": {"node": ">=10"}}, "node_modules/semver": {"version": "6.3.0", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/send": {"version": "0.18.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/send/node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/serialize-javascript": {"version": "6.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-index": {"version": "1.9.1", "license": "MIT", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-index/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/serve-index/node_modules/depd": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/http-errors": {"version": "1.6.3", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/inherits": {"version": "2.0.3", "license": "ISC"}, "node_modules/serve-index/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/serve-index/node_modules/setprototypeof": {"version": "1.1.0", "license": "ISC"}, "node_modules/serve-index/node_modules/statuses": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/serve-static": {"version": "1.15.0", "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.18.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.7.4", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.3", "license": "ISC"}, "node_modules/sisteransi": {"version": "1.0.5", "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/sockjs": {"version": "0.3.24", "license": "MIT", "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}}, "node_modules/source-list-map": {"version": "2.0.1", "license": "MIT"}, "node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-loader": {"version": "3.0.2", "license": "MIT", "dependencies": {"abab": "^2.0.5", "iconv-lite": "^0.6.3", "source-map-js": "^1.0.1"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/source-map-loader/node_modules/iconv-lite": {"version": "0.6.3", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/sourcemap-codec": {"version": "1.4.8", "license": "MIT"}, "node_modules/spdy": {"version": "4.0.2", "license": "MIT", "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/spdy-transport": {"version": "3.0.0", "license": "MIT", "dependencies": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "node_modules/sprintf-js": {"version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/stable": {"version": "0.1.8", "license": "MIT"}, "node_modules/stack-utils": {"version": "2.0.6", "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/stackframe": {"version": "1.2.0", "license": "MIT"}, "node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-length": {"version": "4.0.2", "license": "MIT", "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/string-natural-compare": {"version": "3.0.1", "license": "MIT"}, "node_modules/string-width": {"version": "4.2.2", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/string-width/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/string.prototype.matchall": {"version": "4.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "get-intrinsic": "^1.1.3", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "regexp.prototype.flags": "^1.4.3", "side-channel": "^1.0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.matchall/node_modules/get-intrinsic": {"version": "1.1.3", "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.matchall/node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/stringify-object": {"version": "3.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"get-own-enumerable-property-symbols": "^3.0.0", "is-obj": "^1.0.1", "is-regexp": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/strip-ansi": {"version": "6.0.0", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-comments": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/style-loader": {"version": "3.3.1", "license": "MIT", "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/stylehacks": {"version": "5.1.1", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-hyperlinks": {"version": "2.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svg-parser": {"version": "2.0.4", "license": "MIT"}, "node_modules/svgo": {"version": "1.3.2", "license": "MIT", "dependencies": {"chalk": "^2.4.1", "coa": "^2.0.2", "css-select": "^2.0.0", "css-select-base-adapter": "^0.1.1", "css-tree": "1.0.0-alpha.37", "csso": "^4.0.2", "js-yaml": "^3.13.1", "mkdirp": "~0.5.1", "object.values": "^1.1.0", "sax": "~1.2.4", "stable": "^0.1.8", "unquote": "~1.1.1", "util.promisify": "~1.0.0"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=4.0.0"}}, "node_modules/svgo/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/svgo/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/svgo/node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/svgo/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/svgo/node_modules/css-select": {"version": "2.1.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.2.1", "domutils": "^1.7.0", "nth-check": "^1.0.2"}}, "node_modules/svgo/node_modules/css-tree": {"version": "1.0.0-alpha.37", "license": "MIT", "dependencies": {"mdn-data": "2.0.4", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/svgo/node_modules/css-what": {"version": "3.4.2", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/svgo/node_modules/dom-serializer": {"version": "0.2.2", "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}}, "node_modules/svgo/node_modules/dom-serializer/node_modules/domelementtype": {"version": "2.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/svgo/node_modules/domelementtype": {"version": "1.3.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/svgo/node_modules/domutils": {"version": "1.7.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "0", "domelementtype": "1"}}, "node_modules/svgo/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/svgo/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/svgo/node_modules/mdn-data": {"version": "2.0.4", "license": "CC0-1.0"}, "node_modules/svgo/node_modules/nth-check": {"version": "1.0.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "~1.0.0"}}, "node_modules/svgo/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/symbol-tree": {"version": "3.2.4", "license": "MIT"}, "node_modules/tailwindcss": {"version": "3.2.4", "license": "MIT", "dependencies": {"arg": "^5.0.2", "chokidar": "^3.5.3", "color-name": "^1.1.4", "detective": "^5.2.1", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.2.12", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "lilconfig": "^2.0.6", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.18", "postcss-import": "^14.1.0", "postcss-js": "^4.0.0", "postcss-load-config": "^3.1.4", "postcss-nested": "6.0.0", "postcss-selector-parser": "^6.0.10", "postcss-value-parser": "^4.2.0", "quick-lru": "^5.1.1", "resolve": "^1.22.1"}, "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "engines": {"node": ">=12.13.0"}, "peerDependencies": {"postcss": "^8.0.9"}}, "node_modules/tailwindcss/node_modules/glob-parent": {"version": "6.0.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/tailwindcss/node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/tailwindcss/node_modules/resolve": {"version": "1.22.1", "license": "MIT", "dependencies": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tapable": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/temp-dir": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/tempy": {"version": "0.6.0", "license": "MIT", "dependencies": {"is-stream": "^2.0.0", "temp-dir": "^2.0.0", "type-fest": "^0.16.0", "unique-string": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/tempy/node_modules/type-fest": {"version": "0.16.0", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/terminal-link": {"version": "2.1.1", "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/terser": {"version": "5.16.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.2", "acorn": "^8.5.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser-webpack-plugin": {"version": "5.3.6", "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.14", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0", "terser": "^5.14.1"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "node_modules/terser-webpack-plugin/node_modules/schema-utils": {"version": "3.1.1", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/test-exclude": {"version": "6.0.0", "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/text-table": {"version": "0.2.0", "license": "MIT"}, "node_modules/throat": {"version": "6.0.1", "license": "MIT"}, "node_modules/thunky": {"version": "1.1.0", "license": "MIT"}, "node_modules/tiny-warning": {"version": "1.0.3", "license": "MIT"}, "node_modules/tmpl": {"version": "1.0.4", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/to-fast-properties": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/topbar": {"version": "1.0.1", "license": "MIT"}, "node_modules/toposort": {"version": "2.0.2", "license": "MIT"}, "node_modules/tough-cookie": {"version": "4.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.1.2"}, "engines": {"node": ">=6"}}, "node_modules/tough-cookie/node_modules/universalify": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/tr46": {"version": "2.1.0", "license": "MIT", "dependencies": {"punycode": "^2.1.1"}, "engines": {"node": ">=8"}}, "node_modules/tryer": {"version": "1.0.1", "license": "MIT"}, "node_modules/tsconfig-paths": {"version": "3.14.1", "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.1", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tsconfig-paths/node_modules/json5": {"version": "1.0.1", "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/tsconfig-paths/node_modules/minimist": {"version": "1.2.7", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tslib": {"version": "2.2.0", "license": "0BSD"}, "node_modules/tsutils": {"version": "3.21.0", "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "node_modules/tsutils/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/type-check": {"version": "0.3.2", "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.0.8", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.20.2", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-styles": {"version": "0.0.7", "license": "MIT"}, "node_modules/typedarray-to-buffer": {"version": "3.1.5", "license": "MIT", "dependencies": {"is-typedarray": "^1.0.0"}}, "node_modules/typescript": {"version": "4.9.5", "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/unbox-primitive": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/unbox-primitive/node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-ecmascript": {"version": "1.0.4", "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^1.0.4", "unicode-property-aliases-ecmascript": "^1.0.4"}, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unique-string": {"version": "2.0.0", "license": "MIT", "dependencies": {"crypto-random-string": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/universalify": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unquote": {"version": "1.1.1", "license": "MIT"}, "node_modules/upath": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4", "yarn": "*"}}, "node_modules/update-browserslist-db": {"version": "1.0.16", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.1.2", "picocolors": "^1.0.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/util.promisify": {"version": "1.0.1", "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.2", "has-symbols": "^1.0.1", "object.getownpropertydescriptors": "^2.1.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/utila": {"version": "0.4.0", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-to-istanbul": {"version": "8.1.1", "license": "ISC", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^1.6.0", "source-map": "^0.7.3"}, "engines": {"node": ">=10.12.0"}}, "node_modules/v8-to-istanbul/node_modules/source-map": {"version": "0.7.3", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/w3c-hr-time": {"version": "1.0.2", "license": "MIT", "dependencies": {"browser-process-hrtime": "^1.0.0"}}, "node_modules/w3c-xmlserializer": {"version": "2.0.0", "license": "MIT", "dependencies": {"xml-name-validator": "^3.0.0"}, "engines": {"node": ">=10"}}, "node_modules/walker": {"version": "1.0.7", "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.x"}}, "node_modules/warning": {"version": "4.0.3", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/watchpack": {"version": "2.4.0", "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/wbuf": {"version": "1.7.3", "license": "MIT", "dependencies": {"minimalistic-assert": "^1.0.0"}}, "node_modules/webidl-conversions": {"version": "6.1.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10.4"}}, "node_modules/webpack": {"version": "5.75.0", "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.3", "@types/estree": "^0.0.51", "@webassemblyjs/ast": "1.11.1", "@webassemblyjs/wasm-edit": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "acorn": "^8.7.1", "acorn-import-assertions": "^1.7.6", "browserslist": "^4.14.5", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.10.0", "es-module-lexer": "^0.9.0", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.9", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.1.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.1.3", "watchpack": "^2.4.0", "webpack-sources": "^3.2.3"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-dev-middleware": {"version": "5.3.3", "license": "MIT", "dependencies": {"colorette": "^2.0.10", "memfs": "^3.4.3", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/webpack-dev-middleware/node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/webpack-dev-middleware/node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/webpack-dev-server": {"version": "4.11.1", "license": "MIT", "dependencies": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/express": "^4.17.13", "@types/serve-index": "^1.9.1", "@types/serve-static": "^1.13.10", "@types/sockjs": "^0.3.33", "@types/ws": "^8.5.1", "ansi-html-community": "^0.0.8", "bonjour-service": "^1.0.11", "chokidar": "^3.5.3", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "default-gateway": "^6.0.3", "express": "^4.17.3", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.3", "ipaddr.js": "^2.0.1", "open": "^8.0.9", "p-retry": "^4.5.0", "rimraf": "^3.0.2", "schema-utils": "^4.0.0", "selfsigned": "^2.1.1", "serve-index": "^1.9.1", "sockjs": "^0.3.24", "spdy": "^4.0.2", "webpack-dev-middleware": "^5.3.1", "ws": "^8.4.2"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.37.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-manifest-plugin": {"version": "4.1.1", "license": "MIT", "dependencies": {"tapable": "^2.0.0", "webpack-sources": "^2.2.0"}, "engines": {"node": ">=12.22.0"}, "peerDependencies": {"webpack": "^4.44.2 || ^5.47.0"}}, "node_modules/webpack-manifest-plugin/node_modules/webpack-sources": {"version": "2.3.1", "license": "MIT", "dependencies": {"source-list-map": "^2.0.1", "source-map": "^0.6.1"}, "engines": {"node": ">=10.13.0"}}, "node_modules/webpack-sources": {"version": "1.4.3", "license": "MIT", "dependencies": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}}, "node_modules/webpack/node_modules/@types/estree": {"version": "0.0.51", "license": "MIT"}, "node_modules/webpack/node_modules/schema-utils": {"version": "3.1.1", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/webpack/node_modules/webpack-sources": {"version": "3.2.3", "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/websocket-driver": {"version": "0.7.4", "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/whatwg-encoding": {"version": "1.0.5", "license": "MIT", "dependencies": {"iconv-lite": "0.4.24"}}, "node_modules/whatwg-fetch": {"version": "3.6.2", "license": "MIT"}, "node_modules/whatwg-mimetype": {"version": "2.3.0", "license": "MIT"}, "node_modules/whatwg-url": {"version": "8.6.0", "license": "MIT", "dependencies": {"lodash": "^4.7.0", "tr46": "^2.1.0", "webidl-conversions": "^6.1.0"}, "engines": {"node": ">=10"}}, "node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/word-wrap": {"version": "1.2.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/workbox-background-sync": {"version": "6.5.4", "license": "MIT", "dependencies": {"idb": "^7.0.1", "workbox-core": "6.5.4"}}, "node_modules/workbox-broadcast-update": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-core": "6.5.4"}}, "node_modules/workbox-build": {"version": "6.5.4", "license": "MIT", "dependencies": {"@apideck/better-ajv-errors": "^0.3.1", "@babel/core": "^7.11.1", "@babel/preset-env": "^7.11.0", "@babel/runtime": "^7.11.2", "@rollup/plugin-babel": "^5.2.0", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-replace": "^2.4.1", "@surma/rollup-plugin-off-main-thread": "^2.2.3", "ajv": "^8.6.0", "common-tags": "^1.8.0", "fast-json-stable-stringify": "^2.1.0", "fs-extra": "^9.0.1", "glob": "^7.1.6", "lodash": "^4.17.20", "pretty-bytes": "^5.3.0", "rollup": "^2.43.1", "rollup-plugin-terser": "^7.0.0", "source-map": "^0.8.0-beta.0", "stringify-object": "^3.3.0", "strip-comments": "^2.0.1", "tempy": "^0.6.0", "upath": "^1.2.0", "workbox-background-sync": "6.5.4", "workbox-broadcast-update": "6.5.4", "workbox-cacheable-response": "6.5.4", "workbox-core": "6.5.4", "workbox-expiration": "6.5.4", "workbox-google-analytics": "6.5.4", "workbox-navigation-preload": "6.5.4", "workbox-precaching": "6.5.4", "workbox-range-requests": "6.5.4", "workbox-recipes": "6.5.4", "workbox-routing": "6.5.4", "workbox-strategies": "6.5.4", "workbox-streams": "6.5.4", "workbox-sw": "6.5.4", "workbox-window": "6.5.4"}, "engines": {"node": ">=10.0.0"}}, "node_modules/workbox-build/node_modules/@apideck/better-ajv-errors": {"version": "0.3.6", "license": "MIT", "dependencies": {"json-schema": "^0.4.0", "jsonpointer": "^5.0.0", "leven": "^3.1.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"ajv": ">=8"}}, "node_modules/workbox-build/node_modules/ajv": {"version": "8.11.2", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/workbox-build/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/workbox-build/node_modules/source-map": {"version": "0.8.0-beta.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"whatwg-url": "^7.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/workbox-build/node_modules/tr46": {"version": "1.0.1", "license": "MIT", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/workbox-build/node_modules/webidl-conversions": {"version": "4.0.2", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/workbox-build/node_modules/whatwg-url": {"version": "7.1.0", "license": "MIT", "dependencies": {"lodash.sortby": "^4.7.0", "tr46": "^1.0.1", "webidl-conversions": "^4.0.2"}}, "node_modules/workbox-cacheable-response": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-core": "6.5.4"}}, "node_modules/workbox-core": {"version": "6.5.4", "license": "MIT"}, "node_modules/workbox-expiration": {"version": "6.5.4", "license": "MIT", "dependencies": {"idb": "^7.0.1", "workbox-core": "6.5.4"}}, "node_modules/workbox-google-analytics": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-background-sync": "6.5.4", "workbox-core": "6.5.4", "workbox-routing": "6.5.4", "workbox-strategies": "6.5.4"}}, "node_modules/workbox-navigation-preload": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-core": "6.5.4"}}, "node_modules/workbox-precaching": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-core": "6.5.4", "workbox-routing": "6.5.4", "workbox-strategies": "6.5.4"}}, "node_modules/workbox-range-requests": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-core": "6.5.4"}}, "node_modules/workbox-recipes": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-cacheable-response": "6.5.4", "workbox-core": "6.5.4", "workbox-expiration": "6.5.4", "workbox-precaching": "6.5.4", "workbox-routing": "6.5.4", "workbox-strategies": "6.5.4"}}, "node_modules/workbox-routing": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-core": "6.5.4"}}, "node_modules/workbox-strategies": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-core": "6.5.4"}}, "node_modules/workbox-streams": {"version": "6.5.4", "license": "MIT", "dependencies": {"workbox-core": "6.5.4", "workbox-routing": "6.5.4"}}, "node_modules/workbox-sw": {"version": "6.5.4", "license": "MIT"}, "node_modules/workbox-webpack-plugin": {"version": "6.5.4", "license": "MIT", "dependencies": {"fast-json-stable-stringify": "^2.1.0", "pretty-bytes": "^5.4.1", "upath": "^1.2.0", "webpack-sources": "^1.4.3", "workbox-build": "6.5.4"}, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"webpack": "^4.4.0 || ^5.9.0"}}, "node_modules/workbox-window": {"version": "6.5.4", "license": "MIT", "dependencies": {"@types/trusted-types": "^2.0.2", "workbox-core": "6.5.4"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/write-file-atomic": {"version": "3.0.3", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "node_modules/ws": {"version": "8.11.0", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xml-name-validator": {"version": "3.0.0", "license": "Apache-2.0"}, "node_modules/xmlchars": {"version": "2.2.0", "license": "MIT"}, "node_modules/xtend": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/yaml": {"version": "1.10.2", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/yargs": {"version": "16.2.0", "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/yargs-parser": {"version": "20.2.9", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yocto-queue": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yup": {"version": "0.32.11", "license": "MIT", "dependencies": {"@babel/runtime": "^7.15.4", "@types/lodash": "^4.14.175", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "nanoclone": "^0.2.1", "property-expr": "^2.0.4", "toposort": "^2.0.2"}, "engines": {"node": ">=10"}}, "node_modules/yup/node_modules/@babel/runtime": {"version": "7.16.3", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.13.4"}, "engines": {"node": ">=6.9.0"}}}}