{"name": "kpfa_donation_app", "version": "0.1.0", "private": true, "repository": {"type": "git", "url": "https://git.newday.host/kpfa/KPFA_Public_Donation_App.git"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^1.2.34", "@fortawesome/free-brands-svg-icons": "^5.15.3", "@fortawesome/free-solid-svg-icons": "^5.15.2", "@fortawesome/react-fontawesome": "^0.1.16", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@types/jest": "^29.5.2", "@types/node": "^18.16.2", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "bootstrap": "4.6.1", "comma-number": "^2.0.1", "formik": "^2.4.2", "iso-3166-1-alpha-2": "^1.0.1", "logrocket": "^8.1.0", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "^5.0.1", "react-text-mask": "^5.5.0", "reactstrap": "8.10.1", "topbar": "^1.0.1", "typescript": "^4.9.5", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "test:auto": "playwright test --grep \"PDA Donation Flows|Form Discovery|Donation App Smoke Tests\"", "test:auto:ui": "playwright test --grep \"PDA Donation Flows|Form Discovery|Donation App Smoke Tests\" --ui", "test:manual": "MANUAL_TESTS=1 playwright test --grep 'MANUAL' --headed --project=chromium --workers=1", "test:manual:ui": "MANUAL_TESTS=1 playwright test --grep 'MANUAL' --ui --workers=1", "test:manual:cc": "playwright test --grep 'MANUAL: One-Time Credit Card Payment' --headed --project=chromium", "test:manual:monthly": "playwright test --grep 'MANUAL: Monthly Subscription Credit Card' --headed --project=chromium", "test:manual:ach": "playwright test --grep 'MANUAL: ACH Bank Payment' --headed --project=chromium", "test:manual:ach-monthly": "playwright test --grep 'MANUAL: Monthly ACH Subscription' --headed --project=chromium", "test:manual:ach-verification": "playwright test --grep 'MANUAL: ACH Manual Verification' --headed --project=chromium", "test:manual:ach-complex": "playwright test --grep 'MANUAL: Monthly ACH Manual Verification' --headed --project=chromium"}, "eslintConfig": {"extends": "react-app"}, "homepage": "./", "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "devDependencies": {"@playwright/test": "^1.54.1", "prettier": "^2.8.8"}}