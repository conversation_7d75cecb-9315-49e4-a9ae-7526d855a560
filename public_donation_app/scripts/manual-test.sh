#!/bin/bash

# Manual Testing Helper Script for PDA Donation Flows
# This script helps you run specific manual tests easily

echo "🧪 PDA Manual Testing Helper"
echo "=========================="
echo ""
echo "Available manual tests:"
echo "1. One-Time Credit Card Payment"
echo "2. Monthly Subscription Credit Card"
echo "3. ACH Bank Payment"
echo "4. Google Pay & Apple Pay"
echo "5. Monthly ACH Subscription"
echo "6. Error Handling - Declined Card"
echo "7. High Amount Donation"
echo "8. Run all manual tests"
echo "9. Run core automated tests only"
echo ""

read -p "Select test to run (1-9): " choice

case $choice in
  1)
    echo "🚀 Running One-Time Credit Card Payment test..."
    npx playwright test --project=chromium --grep "MANUAL: One-Time Credit Card Payment" --headed
    ;;
  2)
    echo "🚀 Running Monthly Subscription Credit Card test..."
    npx playwright test --project=chromium --grep "MANUAL: Monthly Subscription Credit Card" --headed
    ;;
  3)
    echo "🚀 Running ACH Bank Payment test..."
    npx playwright test --project=chromium --grep "MANUAL: ACH Bank Payment" --headed
    ;;
  4)
    echo "🚀 Running Google Pay & Apple Pay test..."
    npx playwright test --project=chromium --grep "MANUAL: Google Pay & Apple Pay" --headed
    ;;
  5)
    echo "🚀 Running Monthly ACH Subscription test..."
    npx playwright test --project=chromium --grep "MANUAL: Monthly ACH Subscription" --headed
    ;;
  6)
    echo "🚀 Running Error Handling - Declined Card test..."
    npx playwright test --project=chromium --grep "MANUAL: Error Handling - Declined Card" --headed
    ;;
  7)
    echo "🚀 Running High Amount Donation test..."
    npx playwright test --project=chromium --grep "MANUAL: High Amount Donation" --headed
    ;;
  8)
    echo "🚀 Running all manual tests..."
    npx playwright test --project=chromium --grep "Manual Testing Helpers" --headed
    ;;
  9)
    echo "🚀 Running core automated tests only..."
    npx playwright test --project=chromium --grep "PDA Donation Flows"
    ;;
  *)
    echo "❌ Invalid choice. Please run the script again and select 1-9."
    exit 1
    ;;
esac

echo ""
echo "✅ Test completed!"
echo ""
echo "📋 Remember to verify:"
echo "   - Success messages appear correctly"
echo "   - Payment records are created in StationAdmin backend"
echo "   - Stripe dashboard shows the transactions"
echo "   - No console errors in browser dev tools"
