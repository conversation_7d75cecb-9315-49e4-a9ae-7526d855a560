import { useEffect } from "react";
import { CardElement } from "@stripe/react-stripe-js";

const CardCheckoutForm = (props) => {
  // eslint-disable-next-line
  const cardOptions = {
    style: {
      base: {
        backgroundColor: "white",
        color: "black",
        fontFamily:
          '"ff-super-grotesk-web-pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
        fontSmoothing: "antialiased",
        fontSize: "17px",
        fontWeight: 400,
        "::placeholder": {
          color: "#6c757d",
        },
      },
      invalid: {
        color: "#fa755a",
        iconColor: "#fa755a",
      },
    },
    hidePostalCode: true,
  };
  // eslint-disable-next-line
  const handleChange = async (event) => {
    // Listen for changes in the CardElement
    // and display any errors as the customer types their card details
    props.setDisabled(event.empty);
    props.setError(
      event.error
        ? event.error.message
        : !event.complete
        ? "Card information incomplete. Fill out all fields"
        : "",
    );
    if (event.error || !event.complete) {
      props.setDisabled(true);
    }
  };

  //need to make a sure handleSubmit is run only once each time the API sends a successful response including a customer ID
  //running this when clientSecret is changed
  useEffect(() => {
    if (props.clientSecret && !props.disabled && props.stripe) {
      const handleSubmit = async () => {
        const payload = await props.stripe.confirmCardPayment(props.clientSecret, {
          payment_method: {
            card: props.elements.getElement(CardElement),
          },
        });

        if (payload.error) {
          props.setError(`Payment failed: ${payload.error.message}`);
          props.setProcessing(false);

          if (props.retryPayment > 0) {
            //if we are trying for a second+ time, we need to handle some of the modal flow from this function, as we're not relying on the createDonation function as usual
            props.setFailureFromCheckoutForm("400", payload.error.message);
          }
        } else {
          props.setError(null);
          props.setProcessing(false);
          props.setSucceeded(true);
        }
        //props.hideModal();
      };
      props.progressToProcessingModal();
      handleSubmit();
    }

    // spent some time researching if the deps needed to be listed in here, but trying to list them (and an attempt to refactor the handleSubmit function to be wrapped in useCallback) both broke the functionality and sent the app into an infinite loop on attempted payment. So, this is the currently chosen implementation.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.clientSecret, props.retryPayment]);

  return (
    <div id='payment-form' className='mb-5'>
      {/*
      <CardElement
        className="p-2 border rounded bg-white"
        id="card-element"
        options={cardOptions}
        onChange={handleChange}
      />
      */}
      {/* Show any error that happens when processing the payment */}
      {props.error && (
        <div className='card-error my-3 text-danger' role='alert'>
          {props.error}
        </div>
      )}
      {/* Show a success message upon completion */}
      <p className={props.succeeded ? "result-message" : "result-message d-none"}>
        Payment succeeded. Refresh the page to pay again.
      </p>
    </div>
  );
};

export default CardCheckoutForm;
