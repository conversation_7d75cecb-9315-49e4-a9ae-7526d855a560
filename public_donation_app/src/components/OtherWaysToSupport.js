import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "reactstrap";

const OtherWaysToSupport = () => {
  const [isOpen, setIsOpen] = useState(false);

  const toggle = () => setIsOpen(!isOpen);

  return (
    <>
      <div className='d-flex'>
        <Button color='primary' onClick={toggle} style={{ marginBottom: "1rem" }}>
          <h5> Other Ways To Donate</h5>
        </Button>
      </div>
      <div>
        <Collapse isOpen={isOpen} className='mb-4'>
          <Card>
            <div className='row p-3'>
              <div className='col-sm-12'>
                <ul>
                  <h5>
                    <li>
                      <a href='https://kpfa.org/donate-your-vehicle/'>Donate Your Vehicle</a>
                    </li>
                    <li>
                      <a href='https://kpfa.org/qcds-dafs/'>
                        Qualified Charitable Distributions (QCD) and Donor-Advised Funds (DAF)
                      </a>
                    </li>
                    <li>
                      <a href='https://kpfa.org/employer-matching/'>Employer Matching</a>
                    </li>
                    <li>
                      <a href='https://kpfa.org/donate-stocks/'>Stock Donations</a>
                    </li>
                    <li>
                      <a href='https://kpfa.org/create-your-own-legacy/'>Wills and Living Trusts</a>
                    </li>
                  </h5>
                </ul>
                <p>
                  If you would like to send us a physical check, please use our station address:
                  <address className='mt-'>
                    <strong>
                      1929 Martin Luther King Jr. Way
                      <br /> Berkeley, CA 94704
                    </strong>
                  </address>
                </p>
                <p>
                  For additional help, please contact our Development department at{" "}
                  <a href='mailto:<EMAIL>?subject=Other ways to donate'>
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          </Card>
        </Collapse>
      </div>
    </>
  );
};

export default OtherWaysToSupport;
