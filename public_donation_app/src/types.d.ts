export interface Donation {
  id: number;
  account_id: number | null;
  donor_id: number;
  transaction_id: string;
  timestamp: string;
  firstname: string;
  lastname: string;
  address1: string;
  address2: string | null;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  shipping_firstname: string | null;
  shipping_lastname: string | null;
  shipping_address1: string | null;
  shipping_address2: string | null;
  shipping_city: string | null;
  shipping_state: string | null;
  shipping_country: string | null;
  shipping_postal_code: string | null;
  phone: string;
  email: string;
  type: "Pledge";
  amount: number;
  installment: DonationInstallment;
  comments: string | null;
  add_me: boolean | null;
  read_onair: number;
  donation_match: number;
  show_name: string;
  campaign_id: number | null;
  campaign: string | null;
  updated: string;
}

export type DonationInstallment = "One-Time" | "Monthly";

export interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}
