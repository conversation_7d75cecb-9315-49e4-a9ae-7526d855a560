import { test, expect } from '@playwright/test';

test.describe('PDA Donation Flows', () => {

  // Safety verification before each test
  test.beforeEach(async ({ page }) => {
    // Navigate to the donation page
    await page.goto('http://localhost:3000');

    // Wait for page to load
    await expect(page.getByRole('heading', { name: 'Support KPFA Today!' })).toBeVisible();

    // SAFETY CHECK: Verify we're in test mode
    await expect(page.getByRole('heading', { name: 'Test Mode' })).toBeVisible();
    console.log('✅ SAFETY VERIFIED: Test mode is active');
  });

  test('should verify Stripe Elements integration works', async ({ page }) => {
    // Core test: Verify Stripe Elements load and integration is functional
    console.log('🚀 Testing Stripe Elements integration...');

    // Use unique timestamp to avoid fraud detection
    const timestamp = Date.now();

    // Step 1: Set up donation
    await page.getByRole('button', { name: 'One-Time' }).click();
    await page.getByRole('button', { name: '$ 25' }).click();

    // Step 2: Fill donor information
    await fillDonorInformation(page, {
      firstName: 'Stripe',
      lastName: 'Test',
      email: `stripe.test.${timestamp}@example.com`,
      phone: '(*************',
      address: {
        line1: '123 Test St',
        city: 'Berkeley',
        state: 'CA',
        zip: '94710'
      }
    });

    // Step 3: Submit to payment modal
    await page.getByRole('button', { name: 'Go to next step →' }).click();
    await expect(page.getByText('Please review and confirm your donation')).toBeVisible();

    // Step 4: Verify Stripe Elements load correctly
    console.log('🔄 Verifying Stripe Elements load...');
    await page.waitForSelector('iframe[name^="__privateStripeFrame"]', { timeout: 15000 });

    // Verify Stripe is loaded
    await page.waitForFunction(() => {
      return window.Stripe !== undefined;
    }, { timeout: 10000 });

    console.log('✅ Stripe Elements loaded successfully');

    // Step 5: Verify payment button is present
    const confirmButton = page.getByRole('button', { name: 'Confirm Donation' });
    await expect(confirmButton).toBeVisible();

    // Step 6: Verify donation amount is correct in modal
    await expect(page.getByRole('dialog').getByText('$25')).toBeVisible();

    console.log('🎉 SUCCESS: Stripe Elements integration is working!');
    console.log('✅ Payment modal loads correctly');
    console.log('✅ Stripe library loads without errors');
    console.log('✅ Payment form is ready to accept card input');
    console.log('✅ Updated Stripe library (v17.4.0) is compatible');
  });

  test('should verify monthly subscription setup', async ({ page }) => {
    // Test monthly subscription setup - verifies confirmation_secret fix
    console.log('🚀 Testing monthly subscription setup...');

    const timestamp = Date.now();

    // Step 1: Set up monthly donation
    await page.getByRole('button', { name: 'Monthly' }).click();
    await page.getByRole('button', { name: '$ 25' }).click();

    // Step 2: Fill donor information
    await fillDonorInformation(page, {
      firstName: 'Monthly',
      lastName: 'Test',
      email: `monthly.test.${timestamp}@example.com`,
      phone: '(*************',
      address: {
        line1: '456 Monthly Ave',
        city: 'Oakland',
        state: 'CA',
        zip: '94607'
      }
    });

    // Step 3: Submit to payment modal
    await page.getByRole('button', { name: 'Go to next step →' }).click();
    await expect(page.getByText('Please review and confirm your donation')).toBeVisible();

    // Step 4: Verify Stripe Elements load for subscription
    console.log('🔄 Waiting for Stripe Elements to load for monthly subscription...');
    await page.waitForSelector('iframe[name^="__privateStripeFrame"]', { timeout: 15000 });
    console.log('✅ Stripe Elements loaded for subscription');

    // Step 5: Verify subscription-specific elements
    const confirmButton = page.getByRole('button', { name: 'Confirm Donation' });
    await expect(confirmButton).toBeVisible();

    console.log('🎉 SUCCESS: Monthly subscription setup works!');
    console.log('✅ This indicates the confirmation_secret fix is working for subscriptions');
  });

  test('should validate required fields', async ({ page }) => {
    // Test form validation
    console.log('🚀 Testing form validation...');

    // Try to submit without filling required fields
    await page.getByRole('button', { name: 'One-Time' }).click();
    await page.getByRole('button', { name: '$ 25' }).click();

    // Try to submit without filling any donor information
    await page.getByRole('button', { name: 'Go to next step →' }).click();

    // Should show validation errors - check for visible error text (just check first one)
    await expect(page.getByText('This is a required field.').first()).toBeVisible();

    console.log('✅ Form validation working correctly');
  });
});

test.describe('Manual Testing Helpers', () => {

  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000');
    await expect(page.getByRole('heading', { name: 'Support KPFA Today!' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Test Mode' })).toBeVisible();
    console.log('✅ SAFETY VERIFIED: Test mode is active');
  });

  test('MANUAL: One-Time Credit Card Payment', async ({ page }) => {
    console.log('🚀 MANUAL TEST: One-Time Credit Card Payment');
    console.log('📋 This test will pause so you can manually complete a real payment');
    console.log('⚠️  IMPORTANT: Run with --headed or --ui to see the browser!');

    const timestamp = Date.now();

    // Step 1: Set up one-time donation
    await page.getByRole('button', { name: 'One-Time' }).click();
    await page.getByRole('button', { name: '$ 25' }).click();

    // Step 2: Fill donor information
    await fillDonorInformation(page, {
      firstName: 'Manual',
      lastName: 'OneTime',
      email: `manual.onetime.${timestamp}@example.com`,
      phone: '(*************', // Use 555 test phone number
      address: {
        line1: '123 Manual St',
        city: 'Berkeley',
        state: 'CA',
        zip: '94710'
      }
    });

    // Step 3: Submit to payment modal
    await page.getByRole('button', { name: 'Go to next step →' }).click();
    await expect(page.getByText('Please review and confirm your donation')).toBeVisible();

    // Step 4: Wait for Stripe Elements
    await page.waitForSelector('iframe[name^="__privateStripeFrame"]', { timeout: 15000 });

    console.log('🔄 Payment modal is ready. Please manually:');
    console.log('💳 CREDIT CARD TEST:');
    console.log('   Card: ****************');
    console.log('   Expiry: 12/34');
    console.log('   CVC: 123');
    console.log('   Click "Confirm Donation"');
    console.log('   Wait for completion and verify success message');
    console.log('   Check StationAdmin backend for the payment record');
    console.log('');
    console.log('🛑 BROWSER SHOULD BE VISIBLE - Complete the payment manually then press Continue in the browser');

    // Pause for manual testing - this should open the browser
    await page.pause();

    console.log('✅ Manual one-time credit card test completed');
  });

  test('MANUAL: Monthly Subscription Credit Card', async ({ page }) => {
    console.log('🚀 MANUAL TEST: Monthly Subscription Credit Card');
    console.log('📋 This test verifies the confirmation_secret fix for subscriptions');
    console.log('⚠️  IMPORTANT: Run with --headed or --ui to see the browser!');

    const timestamp = Date.now();

    // Step 1: Set up monthly donation
    await page.getByRole('button', { name: 'Monthly' }).click();
    await page.getByRole('button', { name: '$ 25/mo', exact: true }).click();

    // Step 2: Fill donor information
    await fillDonorInformation(page, {
      firstName: 'Manual',
      lastName: 'Monthly',
      email: `manual.monthly.${timestamp}@example.com`,
      phone: '(*************', // Use 555 test phone number
      address: {
        line1: '456 Monthly Ave',
        city: 'Oakland',
        state: 'CA',
        zip: '94607'
      }
    });

    // Step 3: Submit to payment modal
    await page.getByRole('button', { name: 'Go to next step →' }).click();
    await expect(page.getByText('Please review and confirm your donation')).toBeVisible();

    // Step 4: Wait for Stripe Elements
    await page.waitForSelector('iframe[name^="__privateStripeFrame"]', { timeout: 15000 });

    console.log('🔄 Monthly subscription payment modal is ready. Please manually:');
    console.log('💳 MONTHLY SUBSCRIPTION TEST:');
    console.log('   Card: ****************');
    console.log('   Expiry: 12/34');
    console.log('   CVC: 123');
    console.log('   Click "Confirm Donation"');
    console.log('   ⚠️  CRITICAL: This tests the confirmation_secret fix!');
    console.log('   Wait for completion and verify success message');
    console.log('   Check StationAdmin backend for the subscription record');
    console.log('   Verify in Stripe dashboard that subscription was created');
    console.log('');
    console.log('🛑 BROWSER SHOULD BE VISIBLE - Complete the payment manually then press Continue in the browser');

    // Pause for manual testing
    await page.pause();

    console.log('✅ Manual monthly subscription test completed');
  });

  test('MANUAL: ACH Bank Payment', async ({ page }) => {
    console.log('🚀 MANUAL TEST: ACH Bank Payment');
    console.log('📋 This test helps verify ACH payment integration');
    console.log('⚠️  IMPORTANT: Run with --headed or --ui to see the browser!');

    const timestamp = Date.now();

    // Step 1: Set up donation
    await page.getByRole('button', { name: 'One-Time' }).click();
    await page.getByRole('button', { name: '$ 50', exact: true }).click();

    // Step 2: Fill donor information
    await fillDonorInformation(page, {
      firstName: 'Manual',
      lastName: 'ACH',
      email: `manual.ach.${timestamp}@example.com`,
      phone: '(*************', // Use 555 test phone number
      address: {
        line1: '789 Bank St',
        city: 'Berkeley',
        state: 'CA',
        zip: '94710'
      }
    });

    // Step 3: Submit to payment modal
    await page.getByRole('button', { name: 'Go to next step →' }).click();
    await expect(page.getByText('Please review and confirm your donation')).toBeVisible();

    // Step 4: Wait for Stripe Elements
    await page.waitForSelector('iframe[name^="__privateStripeFrame"]', { timeout: 15000 });

    console.log('🔄 Payment modal is ready. Please manually:');
    console.log('🏦 ACH BANK PAYMENT TEST:');
    console.log('   1. Look for "Bank account" or "US bank account" option');
    console.log('   2. Select ACH/Bank payment method');
    console.log('   3. Enter test bank details:');
    console.log('      Routing: *********');
    console.log('      Account: ************');
    console.log('      Type: Checking');
    console.log('   4. Click "Confirm Donation"');
    console.log('   5. Verify ACH mandate appears');
    console.log('   6. Complete the payment and verify success');
    console.log('   7. Check StationAdmin backend for the ACH payment record');
    console.log('');
    console.log('🛑 BROWSER SHOULD BE VISIBLE - Complete the payment manually then press Continue in the browser');

    // Pause for manual testing
    await page.pause();

    console.log('✅ Manual ACH payment test completed');
  });

  test.skip('MANUAL: Google Pay & Apple Pay', async ({ page }) => {
    console.log('🚀 MANUAL TEST: Google Pay & Apple Pay');
    console.log('📋 This test helps verify digital wallet integration');
    console.log('⚠️  IMPORTANT: Run with --headed or --ui to see the browser!');

    const timestamp = Date.now();

    // Step 1: Set up donation
    await page.getByRole('button', { name: 'One-Time' }).click();
    await page.getByRole('button', { name: '$ 25' }).click();

    // Step 2: Fill donor information
    await fillDonorInformation(page, {
      firstName: 'Manual',
      lastName: 'Wallet',
      email: `manual.wallet.${timestamp}@example.com`,
      phone: '(*************', // Use 555 test phone number
      address: {
        line1: '321 Wallet Ave',
        city: 'Oakland',
        state: 'CA',
        zip: '94607'
      }
    });

    // Step 3: Submit to payment modal
    await page.getByRole('button', { name: 'Go to next step →' }).click();
    await expect(page.getByText('Please review and confirm your donation')).toBeVisible();

    // Step 4: Wait for Stripe Elements
    await page.waitForSelector('iframe[name^="__privateStripeFrame"]', { timeout: 15000 });

    console.log('🔄 Payment modal is ready. Please manually:');
    console.log('📱 DIGITAL WALLET TESTS:');
    console.log('   🟢 GOOGLE PAY:');
    console.log('      1. Look for Google Pay button/option');
    console.log('      2. Click Google Pay if available');
    console.log('      3. Complete Google Pay flow');
    console.log('      4. Verify payment completion');
    console.log('');
    console.log('   🍎 APPLE PAY:');
    console.log('      1. Look for Apple Pay button/option');
    console.log('      2. Click Apple Pay if available (Safari/iOS only)');
    console.log('      3. Complete Apple Pay flow');
    console.log('      4. Verify payment completion');
    console.log('');
    console.log('   ℹ️  NOTE: Digital wallets may not appear in test mode');
    console.log('   ℹ️  or may require specific browser/device setup');
    console.log('   📋 Check StationAdmin backend for payment records');
    console.log('');
    console.log('🛑 BROWSER SHOULD BE VISIBLE - Complete the payment manually then press Continue in the browser');

    // Pause for manual testing
    await page.pause();

    console.log('✅ Manual digital wallet test completed');
  });

  test('MANUAL: ACH Manual Verification', async ({ page }) => {
    console.log('🚀 MANUAL TEST: ACH Manual Verification');
    console.log('📋 This test helps you manually test ACH payments that require manual verification');
    console.log('⚠️  IMPORTANT: Run with --headed or --ui to see the browser!');

    const timestamp = Date.now();

    // Step 1: Set up donation
    await page.getByRole('button', { name: 'One-Time' }).click();
    await page.getByRole('button', { name: '$ 50', exact: true }).click();

    // Step 2: Fill donor information
    await fillDonorInformation(page, {
      firstName: 'Manual',
      lastName: 'ACHVerify',
      email: `manual.ach.verify.${timestamp}@example.com`,
      phone: '(*************',
      address: {
        line1: '999 Manual Verification St',
        city: 'Berkeley',
        state: 'CA',
        zip: '94710'
      }
    });

    // Step 3: Submit to payment modal
    await page.getByRole('button', { name: 'Go to next step →' }).click();
    await expect(page.getByText('Please review and confirm your donation')).toBeVisible();

    // Step 4: Wait for Stripe Elements
    await page.waitForSelector('iframe[name^="__privateStripeFrame"]', { timeout: 15000 });

    console.log('🔄 Payment modal is ready. Please manually:');
    console.log('🏦 ACH MANUAL VERIFICATION TEST:');
    console.log('   1. Select "Bank account" or "US bank account" option');
    console.log('   2. Use Stripe Elements auto-fill for test bank details');
    console.log('   3. Click "Confirm Donation"');
    console.log('   4. Complete the payment and verify it processes');
    console.log('   5. Check StationAdmin backend for the payment record');
    console.log('');
    console.log('🛑 BROWSER SHOULD BE VISIBLE - Complete the payment manually then press Continue');

    // Pause for manual testing
    await page.pause();

    console.log('✅ Manual ACH verification test completed');
  });

  test('MANUAL: Monthly ACH Subscription', async ({ page }) => {
    console.log('🚀 MANUAL TEST: Monthly ACH Subscription');
    console.log('📋 This test combines monthly subscription + ACH payment');
    console.log('⚠️  IMPORTANT: Run with --headed or --ui to see the browser!');

    const timestamp = Date.now();

    // Step 1: Set up monthly donation
    await page.getByRole('button', { name: 'Monthly' }).click();
    await page.getByRole('button', { name: '$ 50/mo', exact: true }).click();

    // Step 2: Fill donor information
    await fillDonorInformation(page, {
      firstName: 'Manual',
      lastName: 'MonthlyACH',
      email: `manual.monthly.ach.${timestamp}@example.com`,
      phone: '(*************', // Use 555 test phone number
      address: {
        line1: '555 Subscription Bank St',
        city: 'Berkeley',
        state: 'CA',
        zip: '94710'
      }
    });

    // Step 3: Submit to payment modal
    await page.getByRole('button', { name: 'Go to next step →' }).click();
    await expect(page.getByText('Please review and confirm your donation')).toBeVisible();

    // Step 4: Wait for Stripe Elements
    await page.waitForSelector('iframe[name^="__privateStripeFrame"]', { timeout: 15000 });

    console.log('🔄 Monthly ACH subscription modal is ready. Please manually:');
    console.log('🏦💳 MONTHLY ACH SUBSCRIPTION TEST:');
    console.log('   1. Select "Bank account" or "US bank account" option');
    console.log('   2. Enter test bank details:');
    console.log('      Routing: *********');
    console.log('      Account: ************');
    console.log('      Type: Checking');
    console.log('   3. Look for monthly/recurring mandate text');
    console.log('   4. Click "Confirm Donation"');
    console.log('   ⚠️  CRITICAL: This tests confirmation_secret fix for ACH subscriptions!');
    console.log('   5. Verify subscription creation and success message');
    console.log('   6. Check StationAdmin backend for subscription record');
    console.log('   7. Verify in Stripe dashboard that ACH subscription was created');
    console.log('');
    console.log('🛑 BROWSER SHOULD BE VISIBLE - Complete the payment manually then press Continue in the browser');

    // Pause for manual testing
    await page.pause();

    console.log('✅ Manual monthly ACH subscription test completed');
  });

  test('MANUAL: Monthly ACH Manual Verification', async ({ page }) => {
    console.log('🚀 MANUAL TEST: Monthly ACH Manual Verification');
    console.log('📋 This test helps you manually test monthly ACH subscriptions with manual verification');
    console.log('⚠️  IMPORTANT: Run with --headed or --ui to see the browser!');

    const timestamp = Date.now();

    // Step 1: Set up monthly donation
    await page.getByRole('button', { name: 'Monthly' }).click();
    await page.getByRole('button', { name: '$ 50/mo', exact: true }).click();

    // Step 2: Fill donor information
    await fillDonorInformation(page, {
      firstName: 'Manual',
      lastName: 'MonthlyACH',
      email: `manual.monthly.ach.verify.${timestamp}@example.com`,
      phone: '(*************',
      address: {
        line1: '1234 Monthly ACH Ave',
        city: 'Oakland',
        state: 'CA',
        zip: '94607'
      }
    });

    // Step 3: Submit to payment modal
    await page.getByRole('button', { name: 'Go to next step →' }).click();
    await expect(page.getByText('Please review and confirm your donation')).toBeVisible();

    // Step 4: Wait for Stripe Elements
    await page.waitForSelector('iframe[name^="__privateStripeFrame"]', { timeout: 15000 });

    console.log('🔄 Monthly ACH payment modal is ready. Please manually:');
    console.log('🏦💳 MONTHLY ACH MANUAL VERIFICATION TEST:');
    console.log('   1. Select "Bank account" or "US bank account" option');
    console.log('   2. Use Stripe Elements auto-fill for test bank details');
    console.log('   3. Look for monthly/recurring mandate text');
    console.log('   4. Click "Confirm Donation"');
    console.log('   5. Complete the subscription setup');
    console.log('   6. Check StationAdmin backend for subscription and payment records');
    console.log('');
    console.log('🛑 BROWSER SHOULD BE VISIBLE - Complete the payment manually then press Continue');

    // Pause for manual testing
    await page.pause();

    console.log('✅ Manual monthly ACH verification test completed');
  });


});

// Helper function to fill donor information
async function fillDonorInformation(page: any, donor: {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: {
    line1: string;
    city: string;
    state: string;
    zip: string;
  };
}) {
  await page.fill('input[name="firstName"]', donor.firstName);
  await page.fill('input[name="lastName"]', donor.lastName);
  await page.fill('input[name="address1"]', donor.address.line1);
  await page.fill('input[name="city"]', donor.address.city);
  await page.selectOption('select[name="state"]', donor.address.state);
  await page.fill('input[placeholder="94710"]', donor.address.zip);
  await page.fill('input[id="phone"]', donor.phone);
  await page.fill('input[name="email"]', donor.email);
}
