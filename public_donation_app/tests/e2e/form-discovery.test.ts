import { test, expect } from '@playwright/test';

test.describe('Form Discovery', () => {
  
  test('discover donation form structure', async ({ page }) => {
    await page.goto('/');
    
    // Wait for page to load
    await expect(page.getByRole('heading', { name: 'Support KPFA Today!' })).toBeVisible();
    
    console.log('=== DISCOVERING FORM STRUCTURE ===');
    
    // Find all input elements
    const inputs = await page.locator('input').all();
    console.log(`Found ${inputs.length} input elements:`);
    
    for (let i = 0; i < inputs.length; i++) {
      const input = inputs[i];
      const type = await input.getAttribute('type');
      const name = await input.getAttribute('name');
      const id = await input.getAttribute('id');
      const placeholder = await input.getAttribute('placeholder');
      const className = await input.getAttribute('class');
      
      console.log(`Input ${i + 1}:`, {
        type,
        name,
        id,
        placeholder,
        className: className?.substring(0, 50) + (className && className.length > 50 ? '...' : '')
      });
    }
    
    // Find all select elements
    const selects = await page.locator('select').all();
    console.log(`\nFound ${selects.length} select elements:`);
    
    for (let i = 0; i < selects.length; i++) {
      const select = selects[i];
      const name = await select.getAttribute('name');
      const id = await select.getAttribute('id');
      const className = await select.getAttribute('class');
      
      console.log(`Select ${i + 1}:`, { name, id, className });
    }
    
    // Find all buttons
    const buttons = await page.locator('button').all();
    console.log(`\nFound ${buttons.length} button elements:`);
    
    for (let i = 0; i < buttons.length; i++) {
      const button = buttons[i];
      const type = await button.getAttribute('type');
      const className = await button.getAttribute('class');
      const text = await button.textContent();
      
      console.log(`Button ${i + 1}:`, {
        type,
        text: text?.trim().substring(0, 30),
        className: className?.substring(0, 50) + (className && className.length > 50 ? '...' : '')
      });
    }
    
    // Find radio buttons (for donation type)
    const radios = await page.locator('input[type="radio"]').all();
    console.log(`\nFound ${radios.length} radio button elements:`);
    
    for (let i = 0; i < radios.length; i++) {
      const radio = radios[i];
      const name = await radio.getAttribute('name');
      const value = await radio.getAttribute('value');
      const id = await radio.getAttribute('id');
      
      console.log(`Radio ${i + 1}:`, { name, value, id });
    }
    
    // Look for amount buttons/options
    const amountElements = await page.locator('[class*="amount"], [data-testid*="amount"], [id*="amount"]').all();
    console.log(`\nFound ${amountElements.length} amount-related elements:`);
    
    for (let i = 0; i < amountElements.length; i++) {
      const element = amountElements[i];
      const tagName = await element.evaluate(el => el.tagName);
      const className = await element.getAttribute('class');
      const text = await element.textContent();
      
      console.log(`Amount element ${i + 1}:`, {
        tagName,
        text: text?.trim().substring(0, 30),
        className: className?.substring(0, 50)
      });
    }
    
    // This test always passes - it's just for discovery
    expect(true).toBe(true);
  });
  
  test('discover Stripe elements', async ({ page }) => {
    await page.goto('/');
    
    // Wait for page to load
    await expect(page.getByRole('heading', { name: 'Support KPFA Today!' })).toBeVisible();
    
    // Wait a bit for Stripe to potentially load
    await page.waitForTimeout(3000);
    
    console.log('\n=== DISCOVERING STRIPE ELEMENTS ===');
    
    // Look for Stripe iframes
    const frames = await page.frames();
    console.log(`Found ${frames.length} frames total`);
    
    for (let i = 0; i < frames.length; i++) {
      const frame = frames[i];
      const url = frame.url();
      const name = frame.name();
      
      if (url.includes('stripe') || name.includes('stripe')) {
        console.log(`Stripe frame ${i + 1}:`, { url, name });
      }
    }
    
    // Look for Stripe-related elements
    const stripeElements = await page.locator('[class*="stripe"], [id*="stripe"], [data-testid*="stripe"]').all();
    console.log(`\nFound ${stripeElements.length} Stripe-related elements:`);
    
    for (let i = 0; i < stripeElements.length; i++) {
      const element = stripeElements[i];
      const tagName = await element.evaluate(el => el.tagName);
      const className = await element.getAttribute('class');
      const id = await element.getAttribute('id');
      
      console.log(`Stripe element ${i + 1}:`, { tagName, className, id });
    }
    
    expect(true).toBe(true);
  });
});
