import { test as base, expect } from '@playwright/test';

// Test data and configuration
export const TEST_CONFIG = {
  // Test donor information
  testDonor: {
    firstName: 'Test',
    lastName: 'Donor',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Test Street',
    city: 'Test City',
    state: 'CA',
    zip: '12345',
  },
  
  // Test payment information (using Stripe test card)
  testCard: {
    number: '****************',
    expiry: '12/34',
    cvc: '123',
  },
  
  // Test amounts
  amounts: {
    oneTime: 25,
    monthly: 50,
  },
  
  // API endpoints
  api: {
    baseUrl: process.env.API_BASE_URL || 'http://localhost:8000',
  },
};

// Custom test fixtures
type TestFixtures = {
  donationPage: any; // We'll define this more specifically later
};

export const test = base.extend<TestFixtures>({
  donationPage: async ({ page }, use) => {
    // Navigate to donation page before each test
    await page.goto('/');
    await use(page);
  },
});

export { expect };

// Helper functions for common test operations
export class DonationPageHelpers {
  constructor(private page: any) {}

  async fillDonorInfo(donor = TEST_CONFIG.testDonor) {
    await this.page.fill('[data-testid="firstName"]', donor.firstName);
    await this.page.fill('[data-testid="lastName"]', donor.lastName);
    await this.page.fill('[data-testid="email"]', donor.email);
    await this.page.fill('[data-testid="phone"]', donor.phone);
    await this.page.fill('[data-testid="address"]', donor.address);
    await this.page.fill('[data-testid="city"]', donor.city);
    await this.page.selectOption('[data-testid="state"]', donor.state);
    await this.page.fill('[data-testid="zip"]', donor.zip);
  }

  async selectAmount(amount: number) {
    // Try to click predefined amount button first
    const amountButton = this.page.locator(`[data-testid="amount-${amount}"]`);
    if (await amountButton.isVisible()) {
      await amountButton.click();
    } else {
      // Fall back to custom amount input
      await this.page.fill('[data-testid="custom-amount"]', amount.toString());
    }
  }

  async selectDonationType(type: 'one-time' | 'monthly') {
    await this.page.click(`[data-testid="donation-type-${type}"]`);
  }

  async fillPaymentInfo(card = TEST_CONFIG.testCard) {
    // Wait for Stripe Elements to load
    await this.page.waitForSelector('[data-testid="stripe-card-element"]');
    
    // Fill card information (this will need to be adapted based on actual Stripe Elements implementation)
    const cardFrame = this.page.frameLocator('[data-testid="stripe-card-element"] iframe');
    await cardFrame.fill('[name="cardnumber"]', card.number);
    await cardFrame.fill('[name="exp-date"]', card.expiry);
    await cardFrame.fill('[name="cvc"]', card.cvc);
  }

  async submitDonation() {
    await this.page.click('[data-testid="submit-donation"]');
  }

  async waitForSuccessMessage() {
    await this.page.waitForSelector('[data-testid="success-message"]', { timeout: 30000 });
  }

  async waitForErrorMessage() {
    await this.page.waitForSelector('[data-testid="error-message"]', { timeout: 10000 });
  }

  async getFormValidationErrors() {
    return await this.page.locator('.error, .invalid, [class*="error"], [class*="invalid"]').allTextContents();
  }

  async isFormValid() {
    const errors = await this.getFormValidationErrors();
    return errors.length === 0;
  }

  async takeScreenshot(name: string) {
    await this.page.screenshot({ path: `test-results/screenshots/${name}.png`, fullPage: true });
  }

  async waitForStripeElements() {
    // Wait for Stripe Elements to be fully loaded
    await this.page.waitForFunction(() => {
      return window.Stripe !== undefined;
    }, { timeout: 10000 });
  }

  async selectPremium(premiumName: string) {
    const premium = this.page.locator(`[data-testid="premium-${premiumName}"], .premium:has-text("${premiumName}")`);
    if (await premium.isVisible()) {
      await premium.click();
    }
  }

  async verifyDonationSummary(expectedAmount: number, expectedType: string) {
    const summarySection = this.page.locator('[data-testid="donation-summary"], .summary, .review');
    await expect(summarySection).toBeVisible();

    await expect(summarySection).toContainText(expectedAmount.toString());
    await expect(summarySection).toContainText(expectedType);
  }
}
