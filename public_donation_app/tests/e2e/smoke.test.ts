import { test, expect } from './setup';

test.describe('Donation App Smoke Tests', () => {
  test('should load the donation page', async ({ page }) => {
    await page.goto('/');

    // Check that the page loads and has expected elements
    await expect(page).toHaveTitle(/KPFA|Donation/i);

    // Check for the main heading
    await expect(page.getByRole('heading', { name: 'Support KPFA Today!' })).toBeVisible();

    // Check for test mode indicator
    await expect(page.getByRole('heading', { name: 'Test Mode' })).toBeVisible();
  });

  test('should display donation amount options', async ({ page }) => {
    await page.goto('/');

    // Check for donation amount section
    await expect(page.getByRole('heading', { name: 'Donation Amount' })).toBeVisible();

    // Look for the $25 amount option that we saw in the error
    await expect(page.getByRole('heading', { name: '$25' })).toBeVisible();
  });

  test('should display donation type options (one-time vs monthly)', async ({ page }) => {
    await page.goto('/');

    // Check for donation type section
    await expect(page.getByRole('heading', { name: 'Donation Type' })).toBeVisible();
  });

  test('should display contact form fields', async ({ page }) => {
    await page.goto('/');
    
    // Check for common form fields
    await expect(page.locator('input[type="email"], input[name*="email"]')).toBeVisible();
    await expect(page.locator('input[name*="name"], input[name*="first"]')).toBeVisible();
  });

  test('should display billing address form', async ({ page }) => {
    await page.goto('/');

    // Check for billing & shipping address section
    await expect(page.getByRole('heading', { name: 'Billing & Shipping Address' })).toBeVisible();
  });

  test('should have responsive design', async ({ page }) => {
    await page.goto('/');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('body')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1200, height: 800 });
    await expect(page.locator('body')).toBeVisible();
  });

  test('should not have critical console errors on load', async ({ page }) => {
    const consoleErrors: string[] = [];

    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    await page.goto('/');

    // Wait a bit for any async errors
    await page.waitForTimeout(2000);

    // Filter out known acceptable errors and warnings
    const criticalErrors = consoleErrors.filter(error =>
      !error.includes('net::ERR_') &&
      !error.includes('Failed to fetch') &&
      !error.includes('stripe.com') && // Stripe might have network issues in test
      !error.includes('validateDOMNesting') && // React DOM nesting warnings are not critical
      !error.includes('Warning:') // Filter out React warnings
    );

    expect(criticalErrors).toHaveLength(0);
  });
});
