import React from 'react';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';

// Import your actual slices/reducers
import jwtSlice from './slices/jwtSlice';
import userSlice from './slices/userSlice';
import sidebarSlice from './slices/sidebarSlice';
import campaignOptionsSlice from './slices/campaignOptionsSlice';
import testModeSlice from './slices/testModeSlice';

// Create a mock store for testing
export function createMockStore(initialState = {}) {
  return configureStore({
    reducer: {
      jwt: jwtSlice,
      user: userSlice,
      sidebar: sidebarSlice,
      campaignOptions: campaignOptionsSlice,
      testMode: testModeSlice,
    },
    preloadedState: initialState,
  });
}

// Custom render function that includes providers
export function renderWithProviders(
  ui,
  {
    preloadedState = {},
    store = createMockStore(preloadedState),
    initialEntries = ['/'],
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return (
      <Provider store={store}>
        <MemoryRouter initialEntries={initialEntries}>
          <Routes>
            <Route path="*" element={children} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
}

// Simple wrapper for components that only need Redux
export function renderWithRedux(
  ui,
  {
    preloadedState = {},
    store = createMockStore(preloadedState),
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return <Provider store={store}>{children}</Provider>;
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
}

// Simple wrapper for components that only need Router
export function renderWithRouter(
  ui,
  {
    initialEntries = ['/'],
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return (
      <MemoryRouter initialEntries={initialEntries}>
        <Routes>
          <Route path="*" element={children} />
        </Routes>
      </MemoryRouter>
    );
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock data for common test scenarios
export const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  role: 'admin',
};

export const mockJwt = {
  token: 'mock-jwt-token',
  isAuthenticated: true,
};

export const mockCampaignOptions = [
  { value: 1, label: 'Test Campaign 1' },
  { value: 2, label: 'Test Campaign 2' },
];

// Default mock state
export const defaultMockState = {
  user: mockUser,
  jwt: mockJwt,
  campaignOptions: mockCampaignOptions,
  sidebar: { show: false },
  testMode: { enabled: false },
};
