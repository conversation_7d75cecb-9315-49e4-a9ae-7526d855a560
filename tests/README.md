# PDA Donation Testing Guide

This guide covers the automated and manual testing setup for the Public Donation App (PDA).

## 🚀 Quick Start

### Run Core Automated Tests
```bash
npm run test:core
```

### Run Manual Testing Helper
```bash
npm run test:manual
```

## 🧪 Test Categories

### 1. Core Automated Tests (`PDA Donation Flows`)
These tests verify the basic functionality without requiring manual payment completion:

- **Stripe Elements Integration**: Verifies Stripe library loads and payment modal works
- **Monthly Subscription Setup**: Tests the confirmation_secret fix for subscriptions  
- **Form Validation**: Ensures required field validation works

**Run with:**
```bash
npm run test:core
# or
npx playwright test --grep "PDA Donation Flows"
```

### 2. Manual Testing Helpers (`Manual Testing Helpers`)
These tests pause at the payment step so you can manually complete real payments:

#### Available Manual Tests:

1. **One-Time Credit Card Payment**
   ```bash
   npm run test:manual:cc
   ```
   - Tests basic credit card payment flow
   - Use test card: `****************`

2. **Monthly Subscription Credit Card**
   ```bash
   npm run test:manual:monthly
   ```
   - Tests monthly subscription creation
   - Verifies confirmation_secret fix
   - Use test card: `****************`

3. **ACH Bank Payment**
   ```bash
   npm run test:manual:ach
   ```
   - Tests ACH/bank account payments with instant verification
   - Use test bank: Routing `*********`, Account `************`

4. **ACH Manual Verification**
   ```bash
   npm run test:manual:ach-verification
   ```
   - Tests ACH payments requiring manual verification
   - Use Stripe Elements auto-fill for test bank details

5. **Monthly ACH Subscription**
   ```bash
   npm run test:manual:ach-monthly
   ```
   - Tests combination of monthly + ACH
   - Critical for confirmation_secret fix verification

6. **Monthly ACH Manual Verification**
   ```bash
   npm run test:manual:ach-complex
   ```
   - Tests monthly ACH subscriptions with manual verification
   - Use Stripe Elements auto-fill for test bank details

7. **Google Pay & Apple Pay**
   ```bash
   npm run test:manual:wallets
   ```
   - Tests digital wallet integration
   - May require specific browser/device setup

6. **Error Handling - Declined Card**
   - Tests error handling with declined test card
   - Use declined card: `****************`

7. **High Amount Donation**
   - Tests $1,000 donation handling
   - Verifies no fraud detection issues

## 🛠️ Test Commands

### NPM Scripts
```bash
# Core automated tests
npm run test:core

# Manual testing helper script
npm run test:manual

# Specific manual tests
npm run test:manual:cc              # Credit card
npm run test:manual:monthly         # Monthly subscription
npm run test:manual:ach             # ACH payment (instant verification)
npm run test:manual:ach-verification # ACH manual verification (webhook-dependent)
npm run test:manual:ach-monthly     # Monthly ACH subscription
npm run test:manual:ach-complex     # Monthly ACH manual verification (complex)
npm run test:manual:wallets         # Google/Apple Pay

# All Playwright tests
npm run test:e2e

# Playwright UI mode
npm run test:e2e:ui

# Headed mode (see browser)
npm run test:e2e:headed

# Debug mode
npm run test:e2e:debug
```

### Direct Playwright Commands
```bash
# Run specific test by name
npx playwright test --grep "MANUAL: One-Time Credit Card Payment" --headed

# Run all manual tests
npx playwright test --grep "Manual Testing Helpers" --headed

# List all available tests
npx playwright test --list
```

## 🔒 Safety Features

All tests include safety verification:
- ✅ Confirms test mode is active
- ✅ Verifies localhost environment
- ✅ Uses unique timestamps to avoid fraud detection
- ✅ Uses test payment methods only

## 📋 Manual Testing Checklist

When running manual tests, verify:

### ✅ Payment Completion
- [ ] Success message appears
- [ ] Correct amount displayed
- [ ] No console errors in browser dev tools

### ✅ Backend Verification
- [ ] Payment record created in StationAdmin backend
- [ ] Correct donor information saved
- [ ] Proper payment status

### ✅ Stripe Dashboard
- [ ] Transaction appears in Stripe dashboard
- [ ] Correct amount and payment method
- [ ] For subscriptions: recurring payment schedule created

### ✅ Microdeposit Verification (for ACH manual verification tests)
- [ ] Payment initially shows as "requires_action" status
- [ ] Check for microdeposit verification email from Stripe
- [ ] Use test microdeposit amounts: `32` and `45`
- [ ] Verify payment status updates to "succeeded" after verification
- [ ] Check StationAdmin backend for final payment record

### ✅ Webhook Testing (for ACH tests)
- [ ] Check Stripe webhook logs for payment processing
- [ ] Verify payment status updates in StationAdmin
- [ ] No webhook processing errors in server logs

## 🧩 Test Card Numbers

### Successful Payments
- **Basic card**: `****************`
- **Expiry**: `12/34`
- **CVC**: `123`

### Error Testing
- **Declined card**: `****************`
- **Insufficient funds**: `****************`
- **Expired card**: `****************`

### ACH Testing
- **Use Stripe Elements auto-fill**: Click the auto-fill option for test bank details
- **Test data**: Stripe Elements provides test routing and account numbers automatically
- **Verification**: Some tests may require microdeposit verification flow

## 🔧 Troubleshooting

### Tests Not Running
1. Ensure development server is running on `localhost:3000`
2. Check that test mode is active (should see "Test Mode" heading)
3. Verify Playwright is installed: `npx playwright install`

### Payment Modal Issues
1. Check browser console for JavaScript errors
2. Verify Stripe Elements are loading (look for iframes)
3. Ensure test environment has proper Stripe keys

### Manual Test Pausing
- Tests use `await page.pause()` to stop for manual input
- Click the "Resume" button in Playwright inspector to continue
- Or press `F8` in the browser to resume

## 📁 File Structure

```
tests/
├── e2e/
│   └── donation-flows.test.ts    # Main test file
├── README.md                     # This guide
└── ...

scripts/
└── manual-test.sh               # Interactive manual test runner
```

## 🎯 Testing Strategy

1. **Run core tests first** to verify basic functionality
2. **Use manual tests** to verify actual payment processing
3. **Test different payment methods** (CC, ACH, wallets)
4. **Test both one-time and subscription flows**
5. **Verify error handling** with declined cards
6. **Check backend integration** after each test

This testing setup ensures the Stripe library upgrade and confirmation_secret fix work correctly across all payment scenarios.
