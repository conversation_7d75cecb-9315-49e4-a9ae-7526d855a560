import { test as setup, expect } from '@playwright/test';

const authFile = 'playwright/.auth/user.json';

setup('authenticate', async ({ page }) => {
  console.log('🔧 Setting up authentication...');
  
  const testEmail = process.env.TEST_ADMIN_EMAIL;
  const testPassword = process.env.TEST_ADMIN_PASSWORD;
  
  if (!testEmail || !testPassword) {
    throw new Error('TEST_ADMIN_EMAIL and TEST_ADMIN_PASSWORD must be set in .env file');
  }
  
  console.log(`🔑 Authenticating with: ${testEmail}`);
  
  // Go to StationAdmin homepage
  await page.goto('/');
  await page.waitForTimeout(2000);
  
  // Take screenshot of initial state
  await page.screenshot({ path: 'playwright/.auth/01-initial-state.png', fullPage: true });
  
  // Check current state
  const hasSignOut = await page.locator('a:has-text("Sign Out")').isVisible().catch(() => false);
  const hasCreateButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
  
  console.log(`🔍 Initial state - Sign Out: ${hasSignOut}, Create Button: ${hasCreateButton}`);
  
  if (hasCreateButton) {
    console.log('✅ Already authenticated! Saving current state...');
    await page.context().storageState({ path: authFile });
    return;
  }
  
  // If we have a session but no create button, sign out first
  if (hasSignOut) {
    console.log('🔄 Existing session found, signing out to start fresh...');
    await page.click('a:has-text("Sign Out")');
    await page.waitForTimeout(3000);
    await page.screenshot({ path: 'playwright/.auth/02-after-signout.png', fullPage: true });
  }
  
  // Look for Google Sign In button
  const hasGoogleSignIn = await page.locator('button:has-text("Sign in with Google")').isVisible().catch(() => false);
  
  if (!hasGoogleSignIn) {
    console.log('❌ No Google Sign In button found');
    console.log('🔧 MANUAL SETUP REQUIRED:');
    console.log('1. A browser window should be open');
    console.log('2. Navigate to the Google OAuth page manually');
    console.log('3. Complete the authentication flow');
    console.log(`4. Login with: ${testEmail}`);
    console.log('5. Ensure you can access the Create New Donation button');
    console.log('6. The setup will continue automatically...');
    
    // Wait for manual authentication
    console.log('⏳ Waiting up to 2 minutes for manual authentication...');
    
    try {
      // Wait for either Create New Donation button or donors page
      await page.waitForFunction(() => {
        const createButton = document.querySelector('button:has-text("Create New Donation")');
        const donorsUrl = window.location.href.includes('/#/donors');
        return createButton || donorsUrl;
      }, { timeout: 120000 }); // 2 minutes
      
      console.log('✅ Manual authentication detected!');
      
    } catch (error) {
      throw new Error('Manual authentication timeout - please complete OAuth flow within 2 minutes');
    }
  } else {
    console.log('✅ Found Google Sign In button');
    
    // For automatic OAuth, we'd need to handle Google's bot detection
    // For now, let's use manual approach
    console.log('🔧 MANUAL OAUTH REQUIRED:');
    console.log('1. Click "Sign in with Google" in the browser window');
    console.log(`2. Login with: ${testEmail}`);
    console.log('3. Complete the OAuth flow');
    console.log('4. The setup will continue automatically...');
    
    // Wait for authentication to complete
    console.log('⏳ Waiting for OAuth completion...');
    
    try {
      await page.waitForFunction(() => {
        const createButton = document.querySelector('button:has-text("Create New Donation")');
        const donorsUrl = window.location.href.includes('/#/donors');
        return createButton || donorsUrl;
      }, { timeout: 120000 }); // 2 minutes
      
      console.log('✅ OAuth authentication completed!');
      
    } catch (error) {
      throw new Error('OAuth timeout - please complete authentication within 2 minutes');
    }
  }
  
  // Navigate to donors page to verify access
  console.log('🎯 Verifying access to donors page...');
  await page.goto('/#/donors');
  await page.waitForTimeout(3000);
  
  // Take screenshot of authenticated state
  await page.screenshot({ path: 'playwright/.auth/03-authenticated-state.png', fullPage: true });
  
  // Verify we can access Create New Donation
  const finalCreateButton = await page.locator('button:has-text("Create New Donation")').isVisible().catch(() => false);
  
  if (!finalCreateButton) {
    throw new Error('Authentication failed - Create New Donation button not accessible');
  }
  
  console.log('✅ Authentication successful! Create New Donation button accessible');
  
  // Save the authenticated state
  console.log('💾 Saving authentication state...');
  await page.context().storageState({ path: authFile });
  
  console.log(`✅ Authentication state saved to: ${authFile}`);
  console.log('🎉 Setup complete! Tests can now use this authenticated state');
});
