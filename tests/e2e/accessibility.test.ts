import { test, expect } from './setup';

test.describe('Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should have proper heading hierarchy', async ({ page }) => {
    // Check that headings follow proper hierarchy (h1 -> h2 -> h3, etc.)
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    
    expect(headings.length).toBeGreaterThan(0);
    
    // Should have at least one h1
    const h1Count = await page.locator('h1').count();
    expect(h1Count).toBeGreaterThanOrEqual(1);
  });

  test('should have proper form labels', async ({ page }) => {
    // All form inputs should have associated labels
    const inputs = page.locator('input[type="text"], input[type="email"], input[type="tel"], select, textarea');
    const inputCount = await inputs.count();
    
    if (inputCount > 0) {
      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        const inputId = await input.getAttribute('id');
        const inputName = await input.getAttribute('name');
        
        if (inputId) {
          // Check for label with for attribute
          const label = page.locator(`label[for="${inputId}"]`);
          const hasLabel = await label.count() > 0;
          
          // Check for aria-label or aria-labelledby
          const ariaLabel = await input.getAttribute('aria-label');
          const ariaLabelledBy = await input.getAttribute('aria-labelledby');
          
          expect(hasLabel || ariaLabel || ariaLabelledBy).toBeTruthy();
        }
      }
    }
  });

  test('should have proper button accessibility', async ({ page }) => {
    const buttons = page.locator('button, input[type="submit"], input[type="button"]');
    const buttonCount = await buttons.count();
    
    if (buttonCount > 0) {
      for (let i = 0; i < buttonCount; i++) {
        const button = buttons.nth(i);
        
        // Button should have accessible text
        const text = await button.textContent();
        const ariaLabel = await button.getAttribute('aria-label');
        const title = await button.getAttribute('title');
        
        expect(text || ariaLabel || title).toBeTruthy();
      }
    }
  });

  test('should have proper color contrast', async ({ page }) => {
    // This is a basic check - in a real implementation you'd use axe-core
    // Check that text is visible (basic contrast check)
    const textElements = page.locator('p, span, div, h1, h2, h3, h4, h5, h6, label');
    const count = await textElements.count();
    
    if (count > 0) {
      // Sample a few elements to check they're visible
      for (let i = 0; i < Math.min(5, count); i++) {
        const element = textElements.nth(i);
        const text = await element.textContent();
        
        if (text && text.trim()) {
          await expect(element).toBeVisible();
        }
      }
    }
  });

  test('should be keyboard navigable', async ({ page }) => {
    // Test tab navigation through interactive elements
    const interactiveElements = page.locator('button, input, select, textarea, a[href]');
    const count = await interactiveElements.count();
    
    if (count > 0) {
      // Focus first element
      await page.keyboard.press('Tab');
      
      // Check that focus is visible
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Tab through a few more elements
      for (let i = 0; i < Math.min(3, count - 1); i++) {
        await page.keyboard.press('Tab');
        const newFocusedElement = page.locator(':focus');
        await expect(newFocusedElement).toBeVisible();
      }
    }
  });

  test('should have proper ARIA attributes for form validation', async ({ page }) => {
    // Fill out form with invalid data to trigger validation
    const emailInput = page.locator('input[type="email"]');
    
    if (await emailInput.isVisible()) {
      await emailInput.fill('invalid-email');
      await emailInput.blur();
      
      // Check for aria-invalid attribute
      const ariaInvalid = await emailInput.getAttribute('aria-invalid');
      const ariaDescribedBy = await emailInput.getAttribute('aria-describedby');
      
      // If validation is shown, aria attributes should be present
      const errorMessage = page.locator('.error, .invalid, [role="alert"]');
      const hasError = await errorMessage.count() > 0;
      
      if (hasError) {
        expect(ariaInvalid === 'true' || ariaDescribedBy).toBeTruthy();
      }
    }
  });

  test('should have proper landmark roles', async ({ page }) => {
    // Check for main content area
    const main = page.locator('main, [role="main"]');
    const mainCount = await main.count();
    expect(mainCount).toBeGreaterThanOrEqual(1);
    
    // Check for navigation if present
    const nav = page.locator('nav, [role="navigation"]');
    const navCount = await nav.count();
    // Navigation is optional, so we just check it exists if present
    
    // Check for form landmark
    const form = page.locator('form, [role="form"]');
    const formCount = await form.count();
    expect(formCount).toBeGreaterThanOrEqual(1);
  });

  test('should handle screen reader announcements', async ({ page }) => {
    // Check for live regions for dynamic content
    const liveRegions = page.locator('[aria-live], [role="status"], [role="alert"]');
    
    // Live regions are optional but good to have for form feedback
    const liveRegionCount = await liveRegions.count();
    
    if (liveRegionCount > 0) {
      // Verify live regions are properly configured
      for (let i = 0; i < liveRegionCount; i++) {
        const region = liveRegions.nth(i);
        const ariaLive = await region.getAttribute('aria-live');
        const role = await region.getAttribute('role');
        
        expect(ariaLive || role).toBeTruthy();
      }
    }
  });

  test('should have proper focus management', async ({ page }) => {
    // Test that focus is managed properly during interactions
    const firstButton = page.locator('button').first();
    
    if (await firstButton.isVisible()) {
      await firstButton.click();
      
      // After interaction, focus should be on a logical element
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    }
  });

  test('should provide clear error messages', async ({ page }) => {
    // Try to submit form with missing required fields
    const submitButton = page.locator('button[type="submit"], .submit-button, [data-testid="submit-donation"]');
    
    if (await submitButton.isVisible()) {
      await submitButton.click();
      
      // Check that error messages are descriptive
      const errorMessages = page.locator('.error, .invalid, [role="alert"]');
      const errorCount = await errorMessages.count();
      
      if (errorCount > 0) {
        for (let i = 0; i < errorCount; i++) {
          const error = errorMessages.nth(i);
          const errorText = await error.textContent();
          
          // Error messages should be meaningful (not just "Error" or "Invalid")
          expect(errorText).toBeTruthy();
          expect(errorText!.length).toBeGreaterThan(5);
        }
      }
    }
  });
});
