import { test, expect } from './setup';

test.describe('Performance Tests', () => {
  test('should load page within acceptable time', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    
    // Wait for page to be fully loaded
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Page should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
  });

  test('should have reasonable bundle size', async ({ page }) => {
    const responses: any[] = [];
    
    page.on('response', (response) => {
      if (response.url().includes('.js') || response.url().includes('.css')) {
        responses.push({
          url: response.url(),
          size: response.headers()['content-length'],
          type: response.url().includes('.js') ? 'js' : 'css'
        });
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Calculate total bundle size
    let totalJSSize = 0;
    let totalCSSSize = 0;
    
    responses.forEach(response => {
      const size = parseInt(response.size || '0');
      if (response.type === 'js') {
        totalJSSize += size;
      } else {
        totalCSSSize += size;
      }
    });
    
    // JS bundle should be reasonable (under 2MB)
    expect(totalJSSize).toBeLessThan(2 * 1024 * 1024);
    
    // CSS should be reasonable (under 500KB)
    expect(totalCSSSize).toBeLessThan(500 * 1024);
  });

  test('should handle form interactions smoothly', async ({ page }) => {
    await page.goto('/');
    
    // Measure time for form interactions
    const startTime = Date.now();
    
    // Fill out form fields
    const emailInput = page.locator('input[type="email"]');
    if (await emailInput.isVisible()) {
      await emailInput.fill('<EMAIL>');
    }
    
    const nameInput = page.locator('input[name*="name"], input[name*="first"]');
    if (await nameInput.isVisible()) {
      await nameInput.fill('Test User');
    }
    
    // Select amount button
    const amountButton = page.locator('button:has-text("$25"), [data-amount="25"]').first();
    if (await amountButton.isVisible()) {
      await amountButton.click();
    }
    
    const interactionTime = Date.now() - startTime;
    
    // Form interactions should be responsive (under 1 second)
    expect(interactionTime).toBeLessThan(1000);
  });

  test('should not have memory leaks', async ({ page }) => {
    await page.goto('/');
    
    // Get initial memory usage
    const initialMetrics = await page.evaluate(() => {
      return {
        usedJSHeapSize: (performance as any).memory?.usedJSHeapSize || 0,
        totalJSHeapSize: (performance as any).memory?.totalJSHeapSize || 0
      };
    });
    
    // Perform some interactions
    for (let i = 0; i < 5; i++) {
      const amountButton = page.locator('button:has-text("$25"), [data-amount="25"]').first();
      if (await amountButton.isVisible()) {
        await amountButton.click();
        await page.waitForTimeout(100);
      }
    }
    
    // Force garbage collection if available
    await page.evaluate(() => {
      if ((window as any).gc) {
        (window as any).gc();
      }
    });
    
    const finalMetrics = await page.evaluate(() => {
      return {
        usedJSHeapSize: (performance as any).memory?.usedJSHeapSize || 0,
        totalJSHeapSize: (performance as any).memory?.totalJSHeapSize || 0
      };
    });
    
    // Memory usage shouldn't increase dramatically
    if (initialMetrics.usedJSHeapSize > 0 && finalMetrics.usedJSHeapSize > 0) {
      const memoryIncrease = finalMetrics.usedJSHeapSize - initialMetrics.usedJSHeapSize;
      const increasePercentage = (memoryIncrease / initialMetrics.usedJSHeapSize) * 100;
      
      // Memory shouldn't increase by more than 50%
      expect(increasePercentage).toBeLessThan(50);
    }
  });

  test('should have good Core Web Vitals', async ({ page }) => {
    await page.goto('/');
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Measure Core Web Vitals
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals: any = {};
        
        // Largest Contentful Paint
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          vitals.lcp = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // First Input Delay (simulated)
        vitals.fid = 0; // Would need real user interaction to measure
        
        // Cumulative Layout Shift
        let clsValue = 0;
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          vitals.cls = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });
        
        // Give some time for measurements
        setTimeout(() => resolve(vitals), 2000);
      });
    });
    
    const metrics = vitals as any;
    
    // LCP should be under 2.5 seconds
    if (metrics.lcp) {
      expect(metrics.lcp).toBeLessThan(2500);
    }
    
    // CLS should be under 0.1
    if (metrics.cls !== undefined) {
      expect(metrics.cls).toBeLessThan(0.1);
    }
  });

  test('should handle multiple rapid interactions', async ({ page }) => {
    await page.goto('/');
    
    const startTime = Date.now();
    
    // Rapidly click different amount buttons
    const amountButtons = page.locator('button:has-text("$"), [data-amount]');
    const buttonCount = await amountButtons.count();
    
    if (buttonCount > 0) {
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        await amountButtons.nth(i).click();
        await page.waitForTimeout(50); // Small delay between clicks
      }
    }
    
    const totalTime = Date.now() - startTime;
    
    // Should handle rapid interactions smoothly
    expect(totalTime).toBeLessThan(2000);
  });

  test('should not block main thread excessively', async ({ page }) => {
    await page.goto('/');
    
    // Measure long tasks
    const longTasks = await page.evaluate(() => {
      return new Promise((resolve) => {
        const tasks: any[] = [];
        
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            tasks.push({
              duration: entry.duration,
              startTime: entry.startTime
            });
          }
        }).observe({ entryTypes: ['longtask'] });
        
        // Monitor for 3 seconds
        setTimeout(() => resolve(tasks), 3000);
      });
    });
    
    const tasks = longTasks as any[];
    
    // Should not have many long tasks (over 50ms)
    const longTaskCount = tasks.filter(task => task.duration > 50).length;
    expect(longTaskCount).toBeLessThan(5);
  });

  test('should load images efficiently', async ({ page }) => {
    const imageRequests: any[] = [];
    
    page.on('response', (response) => {
      if (response.url().match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
        imageRequests.push({
          url: response.url(),
          size: response.headers()['content-length'],
          status: response.status()
        });
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check that images loaded successfully
    const failedImages = imageRequests.filter(req => req.status >= 400);
    expect(failedImages.length).toBe(0);
    
    // Check total image size is reasonable
    const totalImageSize = imageRequests.reduce((total, req) => {
      return total + parseInt(req.size || '0');
    }, 0);
    
    // Total images should be under 5MB
    expect(totalImageSize).toBeLessThan(5 * 1024 * 1024);
  });
});
