#!/bin/bash

# KPFA Donation App E2E Test Runner
# This script helps run different test suites with proper setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed or not in PATH"
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    print_warning "node_modules not found. Installing dependencies..."
    npm install
fi

# Check if Playwright browsers are installed
if [ ! -d "node_modules/@playwright/test" ]; then
    print_error "Playwright not installed. Run: npm install"
    exit 1
fi

# Install Playwright browsers if needed
print_status "Checking Playwright browsers..."
npx playwright install --with-deps

# Function to run specific test suite
run_test_suite() {
    local suite=$1
    local description=$2
    
    print_status "Running $description..."
    
    case $suite in
        "smoke")
            npx playwright test smoke.test.ts --reporter=line
            ;;
        "donation")
            npx playwright test donation-flow.test.ts --reporter=line
            ;;
        "accessibility")
            npx playwright test accessibility.test.ts --reporter=line
            ;;
        "performance")
            npx playwright test performance.test.ts --reporter=line
            ;;
        "all")
            npx playwright test --reporter=html
            ;;
        *)
            print_error "Unknown test suite: $suite"
            exit 1
            ;;
    esac
}

# Main script logic
case ${1:-"help"} in
    "smoke")
        run_test_suite "smoke" "Smoke Tests"
        ;;
    "donation")
        run_test_suite "donation" "Donation Flow Tests"
        ;;
    "accessibility")
        run_test_suite "accessibility" "Accessibility Tests"
        ;;
    "performance")
        run_test_suite "performance" "Performance Tests"
        ;;
    "all")
        run_test_suite "all" "All Test Suites"
        ;;
    "ui")
        print_status "Starting Playwright UI mode..."
        npx playwright test --ui
        ;;
    "debug")
        print_status "Starting debug mode..."
        npx playwright test --debug
        ;;
    "help"|*)
        echo "KPFA Donation App E2E Test Runner"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  smoke         Run smoke tests only"
        echo "  donation      Run donation flow tests"
        echo "  accessibility Run accessibility tests"
        echo "  performance   Run performance tests"
        echo "  all           Run all test suites"
        echo "  ui            Open Playwright UI mode"
        echo "  debug         Run tests in debug mode"
        echo "  help          Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 smoke                    # Quick smoke test"
        echo "  $0 all                      # Full test suite"
        echo "  $0 ui                       # Interactive testing"
        ;;
esac

print_status "Test execution completed!"
